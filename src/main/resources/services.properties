pod.status[0].status=Pending
pod.status[0].statusName=\u672A\u90E8\u7F72
pod.status[1].status=ContainerCreating
pod.status[1].statusName=\u521B\u5EFA\u4E2D
pod.status[2].status=PodInitializing
pod.status[2].statusName=\u542F\u52A8\u4E2D
pod.status[3].status=Running
pod.status[3].statusName=\u8FD0\u884C\u4E2D
pod.status[4].status=CrashLoopBackOff
pod.status[4].statusName=\u5F02\u5E38\u91CD\u542F
pod.status[5].status=Evicted
pod.status[5].statusName=\u88AB\u9A71\u9010
pod.status[6].status=ImagePullBackOff
pod.status[6].statusName=\u62C9\u53D6\u955C\u50CF\u5931\u8D25
pod.status[7].status=ErrImagePull
pod.status[7].statusName=\u62C9\u53D6\u955C\u50CF\u5931\u8D25
pod.status[8].status=InitTimeOut
pod.status[8].statusName=\u542F\u52A8\u8D85\u65F6

#  \u914D\u7F6E\u8BF4\u660E\uFF1A
#  displayName : \u670D\u52A1\u540D\u79F0
#  server      : pod name
#  xmxKey      : all-xmx \u4E2D\u5185\u5B58\u53C2\u6570 key
#  xmxMin      : \u6700\u5C0F\u5185\u5B58, \u9ED8\u8BA4512M
#  readOnly    : \u662F\u5426\u4E0D\u53EF\u7F16\u8F91(manage \u670D\u52A1\u5185\u5B58\u9875\u9762\u5C55\u793A)
#  desc        : \u670D\u52A1\u63CF\u8FF0, \u9ED8\u8BA4\u548C\u670D\u52A1\u540D\u79F0\u4FDD\u6301\u4E00\u81F4
#
#
#---------------- main -------------------#
all.services[0].displayName=\u7BA1\u7406\u5E73\u53F0
all.services[0].server=private-buffet
all.services[0].xmxKey=BUFFET_XMX
all.services[0].description=\u4F01\u4E1A\u7BA1\u7406\u5E73\u53F0\u767B\u5F55\u548C\u76F8\u5173\u4FE1\u606F\u7684\u83B7\u53D6

all.services[1].displayName=\u63A5\u5165\u670D\u52A1
all.services[1].server=private-access
all.services[1].xmxKey=ACCESS_XMX
all.services[1].description=\u7EC8\u7AEF\u63A5\u5165\u670D\u52A1\u5668

all.services[2].displayName=\u8BA1\u8D39\u670D\u52A1
all.services[2].server=private-charge
all.services[2].xmxKey=CHARGE_XMX
all.services[2].description=\u6D88\u8D39MQ,\u5904\u7406\u5165\u4F1A\u6D88\u606F,\u8C03\u7528callpermission\u8FDB\u884C\u4E8C\u6B21\u68C0\u67E5

all.services[3].displayName=\u8BA4\u8BC1\u670D\u52A1
all.services[3].server=private-iauth
all.services[3].xmxKey=IAUTH_XMX
all.services[3].description=\u7EC8\u7AEF\u767B\u5F55\u8BA4\u8BC1

all.services[4].displayName=\u901A\u8BAF\u5F55\u670D\u52A1
all.services[4].server=private-contact
all.services[4].xmxKey=CONTACT_XMX
all.services[4].description=\u901A\u8BAF\u5F55\u76F8\u5173\u6570\u636E\u7684\u67E5\u8BE2\u548C\u5199\u5165

all.services[5].displayName=\u4E91\u4F1A\u8BAE\u5BA4\u670D\u52A1
all.services[5].server=private-cloudmeetingroom
all.services[5].xmxKey=CLOUDMEETINGROOM_XMX
all.services[5].description=\u7BA1\u7406\u548C\u7EF4\u62A4\u4E91\u4F1A\u8BAE\u5BA4

all.services[6].displayName=\u7EA6\u4F1A\u670D\u52A1
all.services[6].server=private-dating
all.services[6].xmxKey=DATING_XMX
all.services[6].description=\u9884\u7EA6\u4F1A\u8BAE,\u4F1A\u8BAE\u6A21\u677F,\u4F1A\u8BAE\u8BB0\u5F55

all.services[7].displayName=\u4F1A\u63A7\u670D\u52A1
all.services[7].server=private-mms
all.services[7].xmxKey=MEETINGCONTROL_XMX

all.services[8].displayName=\u4F1A\u8BAE\u8BB0\u5F55\u670D\u52A1
all.services[8].server=private-meeting-recorder
all.services[8].xmxKey=RECORDER_XMX
all.services[8].description=\u65E7\u7248\u6570\u636E\u4E2D\u5FC3\u67E5\u8BE2,\u5386\u53F2\u76F4\u64AD\u7EDF\u8BA1

all.services[9].displayName=\u4FE1\u4EE4\u670D\u52A1
all.services[9].server=private-sigserver
all.services[9].xmxKey=SIGSERVER_XMX
all.services[9].description=\u547C\u53EB\u76F8\u5173

all.services[10].displayName=uss\u670D\u52A1
all.services[10].server=private-uss
all.services[10].xmxKey=USS_XMX
all.services[10].description=\u547C\u53EB\u67E5\u8BE2,\u67E5\u8BE2\u53F7\u7801,\u547C\u53EB\u4E2D\u5411\u4FE1\u4EE4\u63D0\u4F9B\u4E3B\u88AB\u547C\u53EB\u4FE1\u606F\u548C\u4F1A\u8BAE\u5C5E\u6027\u7684\u67E5\u8BE2

all.services[11].displayName=call permission
all.services[11].server=private-callpermission
all.services[11].xmxKey=CALL_XMX
all.services[11].description=\u547C\u53EB\u6743\u9650\u68C0\u67E5,\u7533\u8BF7\u4E34\u65F6\u4E91\u4F1A\u8BAE\u5BA4\u53F7

all.services[12].displayName=\u7528\u6237\u89D2\u8272\u6743\u9650\u670D\u52A1
all.services[12].server=private-userpermission
all.services[12].xmxKey=USERPERMISSION_XMX
all.services[12].description=\u4F01\u4E1A\u7BA1\u7406\u5E73\u53F0\u89D2\u8272\u7BA1\u7406

all.services[13].displayName=\u7EC8\u7AEF\u914D\u7F6E\u4E2D\u5FC3
all.services[13].server=private-clientconfig
all.services[13].xmxKey=CLIENT_CONFIG_XMX
all.services[13].description=\u4E0B\u53D1\u7EC8\u7AEF\u4E0E\u7528\u6237\u914D\u7F6E\u3001\u5BF9\u914D\u7F6E\u65BD\u52A0\u4F18\u5148\u7EA7\u7EA6\u675F

all.services[14].displayName=pivotor\u670D\u52A1
all.services[14].server=private-pivotor
all.services[14].xmxKey=PIVOTOR_XMX
all.services[14].description=\u7528\u6237\u548C\u8BBE\u5907\u7684\u7BA1\u7406,\u4E3B\u8981\u662F\u6570\u636E\u5E93\u76F8\u5173\u64CD\u4F5C

all.services[15].displayName=\u8BA1\u8D39\u5206\u53D1\u670D\u52A1
all.services[15].server=private-charge-dispatcher
all.services[15].xmxKey=CHARGE_DISPATCHER_XMX
all.services[15].description=\u7EC8\u7AEF\u5165\u4F1A,\u9000\u4F1A\u6D88\u606F\u8F6C\u53D1,\u5C06kafka\u6D88\u606F\u8F6C\u6210MQ\u6D88\u606F,\u5206\u53D1\u7ED9charge

all.services[16].displayName=IM
all.services[16].server=private-im
all.services[16].xmxKey=IM_XMX
all.services[16].description=\u4F1A\u4E2D\u804A\u5929,\u76EE\u524D\u652F\u6301\u8F6F\u7EC8\u7AEF\u804A\u5929

all.services[17].displayName=\u6D88\u606F\u670D\u52A1
all.services[17].server=private-msgserver
all.services[17].xmxKey=MSGSERVER_XMX
all.services[17].description=\u4F20\u9012message\u6D88\u606F,\u5F71\u54CD\u4F1A\u63A7(\u4E3E\u624B\u3001\u7B54\u9898\u3001\u5DE1\u68C0\u7B49)\u3001\u9884\u7EA6\u4F1A\u8BAE\u3001\u65E5\u7A0B\u3001\u7BA1\u7406\u5E73\u53F0(\u8BBE\u5907\u64CD\u4F5C\u7B49)

all.services[18].displayName=\u767D\u677F\u670D\u52A1
all.services[18].server=private-sharing
all.services[18].xmxKey=SHARING_XMX

all.services[19].displayName=Client Support Service
all.services[19].server=private-css
all.services[19].xmxKey=CSS_XMX
all.services[19].description=\u6570\u636E\u805A\u5408,\u5F71\u54CD\u8F6F\u7EC8\u7AEF\u6CE8\u518C\u3001\u9884\u7EA6\u4F1A\u8BAE

all.services[20].displayName=\u7248\u672C\u7BA1\u7406\u670D\u52A1
all.services[20].server=private-vcs
all.services[20].xmxKey=VCS_XMX
all.services[20].description=\u7248\u672C\u63A7\u5236,\u5F71\u54CD\u7070\u5EA6\u3001\u786C\u7EC8\u7AEF\u7684\u5361\u7247\u754C\u9762\u914D\u7F6E,\u5BA2\u6237\u7AEF\u83B7\u53D6\u7248\u672C\u4FE1\u606F\u3001\u68C0\u6D4B\u5347\u7EA7\u7B49

all.services[21].displayName=DMCU\u8C03\u5EA6\u670D\u52A1
all.services[21].server=private-mcserver
all.services[21].xmxKey=MCSERVER_XMX
all.services[21].description=\u5206\u914Dmcu\u7ED9\u7EC8\u7AEF,\u547C\u53EB502\u3001503\u62A5\u9519\u53EF\u8003\u8651\u8DDFmc\u5206\u914D\u4E0D\u5230mcu\u8D44\u6E90\u6709\u5173

all.services[22].displayName=DMCU\u670D\u52A1
all.services[22].server=private-dmcu
all.services[22].xmxKey=MAX
all.services[22].readOnly=true
all.services[22].description=\u5A92\u4F53\u670D\u52A1

all.services[23].displayName=\u5DE1\u68C0\u670D\u52A1
all.services[23].server=private-inspection
all.services[23].xmxKey=INSPECTION_XMX
all.services[23].description=\u4F9D\u8D56pivotor,\u67E5\u8BE2\u8BBE\u5907\u5C5E\u6027,\u5DE1\u68C0\u4F1A\u63A7\u670D\u52A1,\u7EC8\u7AEF\u81EA\u68C0

all.services[24].displayName=page\u670D\u52A1
all.services[24].server=private-page
all.services[24].xmxKey=PAGE_XMX
all.services[24].description=\u67E5\u8BE2\u7EC8\u7AEF\u901A\u8BDD\u8BB0\u5F55,\u751F\u6210\u4F1A\u8BAE\u9080\u8BF7\u94FE\u63A5,\u8BFB\u53D6\u786C\u7EC8\u7AEF\u6D88\u606F\u63A8\u9001\u56FE\u7247

all.services[25].displayName=\u6570\u636E\u6C47\u805A\u670D\u52A1
all.services[25].server=private-ocean
all.services[25].xmxKey=OCEAN_XMX
all.services[25].description=\u4E1A\u52A1\u670D\u52A1\u6570\u636E\u67E5\u8BE2\u548C\u5199\u5165mongodb,\u5F71\u54CD\u7528\u6237\u7BA1\u7406\u3001\u7EC8\u7AEF\u7BA1\u7406\u3001\u7EC4\u7EC7\u67B6\u6784\u3001\u4E91\u4F1A\u8BAE\u5BA4\u3001\u5F55\u5236\u6587\u4EF6

all.services[26].displayName=\u72B6\u6001\u670D\u52A1
all.services[26].server=private-presence
all.services[26].xmxKey=PRESENCE_XMX
all.services[26].description=\u67E5\u8BE2\u7EC8\u7AEF\u72B6\u6001

all.services[27].displayName=SDK\u670D\u52A1
all.services[27].server=private-sdkcallback
all.services[27].xmxKey=SDKCALLBACK_XMX
all.services[27].description=\u5C0F\u9C7C\u7CFB\u7EDF\u5728\u4E8B\u4EF6\u53D1\u751F\u65F6,\u901A\u8FC7websocket\u901A\u77E5\u5408\u4F5C\u4F19\u4F34

all.services[28].displayName=\u65E5\u5FD7\u670D\u52A1
all.services[28].server=private-logserver
all.services[28].xmxKey=LOGSERVER_XMX
all.services[28].description=\u7EC8\u7AEF\u65E5\u5FD7\u4E0A\u4F20\u3001\u5B58\u50A8

all.services[29].displayName=sitecode\u670D\u52A1
all.services[29].server=private-sitecode
all.services[29].xmxKey=SITECODE_XMX
all.services[29].description=\u63D0\u4F9Bsitecode\u67E5\u8BE2\u7ED3\u679C,\u7EC8\u7AEF\u5206\u914D\u5BF9\u5E94sitecode\u7684mcu

all.services[30].displayName=externalweb\u670D\u52A1
all.services[30].server=private-externalweb
all.services[30].xmxKey=EXTERNALWEB_XMX
all.services[30].description=\u63D0\u4F9Bapi\u63A5\u53E3,sdk\u5BA2\u6237\u7AEF\u5BF9\u63A5\u7684\u540E\u53F0sdk\u670D\u52A1

all.services[31].displayName=\u901A\u8BAF\u5F55\u5B9A\u65F6\u4EFB\u52A1
all.services[31].server=private-contact-schedule
all.services[31].xmxKey=CONTACT_SCHEDULE_XMX
all.services[31].description=\u901A\u8BAF\u5F55\u540C\u6B65\u8C03\u5EA6,\u91C7\u96C6MySQL\u6570\u636E\uFF0C\u4F9Bcontact\u8C03\u5EA6

all.services[32].displayName=basicinfo\u670D\u52A1
all.services[32].server=private-basicinfo
all.services[32].xmxKey=BASICINFO_XMX
all.services[32].description=\u7EC8\u7AEF\u5165\u4F1A\u67E5\u8BE2\u8BE5\u7EC8\u7AEF\u80FD\u529B,\u7EC4\u5408\u5230\u7EC8\u7AEF\u4FE1\u606F\u4E2D\u63A8\u7ED9h5

all.services[33].displayName=\u4F1A\u8BAE\u65E5\u5FD7
all.services[33].server=private-meetingmonitor
all.services[33].xmxKey=MEETINGMONITOR_XMX
all.services[33].description=\u4F1A\u8BAE\u65E5\u5FD7\u670D\u52A1

all.services[34].displayName=\u5185\u5916\u7F51\u63A2\u6D4B\u670D\u52A1
all.services[34].server=private-locator
all.services[34].xmxKey=LOCATOR_XMX
all.services[34].description=\u5185\u5916\u7F51\u63A2\u6D4B,\u5BF9mcu\u5185\u5916\u7F51\u5206\u914D\u6709\u5F71\u54CD

all.services[35].displayName=TSA\u670D\u52A1
all.services[35].server=private-tsa
all.services[35].xmxKey=TSA_XMX
all.services[35].description=web\u7F51\u5173access

all.services[36].displayName=\u7B7E\u5230\u7B54\u9898\u7EDF\u8BA1\u670D\u52A1
all.services[36].server=private-vote-statistics
all.services[36].xmxKey=VOTE_STATISTICS_XMX
all.services[36].description=\u7B7E\u5230\u7B54\u9898\u7EDF\u8BA1\u7ED3\u679C\u7EDF\u8BA1\u3001\u5BFC\u51FA

all.services[37].displayName=Nginx\u670D\u52A1
all.services[37].server=private-openresty-main
all.services[37].description=\u57FA\u7840\u670D\u52A1nginx

all.services[38].displayName=nettool\u670D\u52A1
all.services[38].server=private-nettool
all.services[38].description=main\u4E0A\u67091\u4E2Anettool,\u9700\u8981\u5F00\u653E5008\u7AEF\u53E3;\u6BCF\u4E2Amcu\u90FD\u914D\u67091\u4E2Anettool;main\u7684nettool\u4F9B\u7EC8\u7AEF\u7F51\u7EDC\u6D4B\u8BD5\u529F\u80FD\u4F7F\u7528;mcu\u4E0Anettool\u4F9B\u7EC8\u7AEF\u667A\u80FD\u5DE1\u68C0\u65F6\u7684\u7F51\u7EDC\u6D4B\u8BD5,nettool\u4E0D\u652F\u6301\u5185\u5916\u7F51

all.services[39].displayName=\u9759\u6001\u8D44\u6E90\u670D\u52A1
all.services[39].server=private-frontend
all.services[39].description=\u7BA1\u7406\u5E73\u53F0\u524D\u7AEF\u663E\u793A\u3001\u8F6F\u7EC8\u7AEFH5\u76F8\u5173\u529F\u80FD(\u901A\u8BAF\u5F55\u3001\u6587\u4EF6\u5939\u3001\u9884\u7EA6\u4F1A\u8BAE\u3001\u6211\u7684\u4F1A\u8BAE\u7B49)\u3001\u786C\u7EC8\u7AEF(\u6587\u4EF6\u5939)

all.services[40].displayName=AMQ
all.services[40].server=private-amq
all.services[40].xmxKey=AMQ_XMX
all.services[40].description=\u6D88\u606F\u4E2D\u95F4\u4EF6

all.services[41].displayName=Redis
all.services[41].server=private-redis
all.services[41].xmxKey=REDIS_MEM
all.services[41].description=\u975E\u5173\u7CFB\u578B\u6570\u636E\u5E93

all.services[42].displayName=mkdoc\u670D\u52A1
all.services[42].server=private-mkdoc
all.services[42].description=\u4F01\u4E1A\u7BA1\u7406\u5E73\u53F0\u5F00\u53D1\u8005\u6587\u6863

all.services[43].displayName=\u6295\u7968\u670D\u52A1
all.services[43].server=private-vote
all.services[43].xmxKey=VOTE_XMX
all.services[43].description=\u7B7E\u5230\u3001\u7B54\u9898\u3001\u6295\u7968\u3001\u8BC4\u4EF7\u3001\u516C\u5E03/\u505C\u6B62\u516C\u5E03\u7B54\u9898\u548C\u6295\u7968\u7ED3\u679C

#---------------- db -------------------#
all.services[44].displayName=Kafka
all.services[44].server=private-kafka
all.services[44].xmxMin=2048
all.services[44].readOnly=true
all.services[44].description=\u6D88\u606F\u901A\u9053\u670D\u52A1

all.services[45].displayName=Zookeeper
all.services[45].server=private-zookeeper
all.services[45].xmxMin=512
all.services[45].readOnly=true
all.services[45].description=\u670D\u52A1\u6CE8\u518C\u4E0E\u53D1\u73B0

all.services[46].displayName=\u4E3B\u6570\u636E\u5E93
all.services[46].server=private-mysql
all.services[46].xmxKey=MAX
all.services[46].xmxMin=1024
all.services[46].readOnly=true
all.services[46].description=\u5B58\u50A8\u57FA\u7840\u4E1A\u52A1\u6240\u9700\u7684\u6570\u636E

all.services[47].displayName=Mongodb
all.services[47].server=private-mongodb
all.services[47].xmxKey=MAX
all.services[47].xmxMin=1024
all.services[47].readOnly=true
all.services[47].description=\u4F01\u4E1A\u7BA1\u7406\u5E73\u53F0\u6570\u636E\u6C47\u96C6\u5B58\u50A8\u670D\u52A1


#---------------- vod -------------------#
all.services[48].displayName=Nginx(vod)\u670D\u52A1
all.services[48].server=private-openresty-vod

all.services[49].displayName=\u5F55\u5236\u6587\u4EF6(\u5939)\u7BA1\u7406\u670D\u52A1
all.services[49].server=private-vodmanager
all.services[49].xmxKey=VODMANAGER_XMX
all.services[49].description=\u5F55\u5236\u6587\u4EF6\u4E0A\u4F20\u3001\u64AD\u653E\u3001\u5206\u4EAB\u3001\u4E0B\u8F7D\u3001\u5220\u9664\u3001\u526A\u8F91\u3001\u5408\u5E76

all.services[50].displayName=\u5F55\u5236\u8C03\u5EA6\u670D\u52A1
all.services[50].server=private-rmserver
all.services[50].xmxKey=RMSERVER_XMX
all.services[50].description=\u5F55\u5236\u7BA1\u7406,\u63A5\u53D7rs\u6CE8\u518C,\u5206\u914D\u5F55\u5236\u8D44\u6E90

all.services[51].displayName=\u5F55\u5236\u670D\u52A1
all.services[51].server=private-recordingserver
all.services[51].xmxKey=MAX
all.services[51].readOnly=true
all.services[51].description=\u4F1A\u8BAE\u5F55\u5236

all.services[52].displayName=\u5F55\u5236\u70B9\u64AD\u670D\u52A1
all.services[52].server=private-vod
all.services[52].xmxKey=MAX
all.services[52].readOnly=true
all.services[52].description=\u5F55\u5236\u6587\u4EF6\u70B9\u64AD

all.services[53].displayName=\u5F55\u5236\u6587\u4EF6\u7BA1\u7406\u670D\u52A1
all.services[53].server=private-vodfilemanager
all.services[53].xmxKey=VODFILEMANAGER_XMX
all.services[53].description=\u5F55\u5236\u6587\u4EF6\u7F29\u7565\u56FE\u663E\u793A\u3001\u6587\u4EF6\u4E0B\u8F7D\u3001ES700\u672C\u5730\u5F55\u5236\u4E0A\u4F20

all.services[54].displayName=\u5F55\u5236\u5206\u4EAB\u670D\u52A1
all.services[54].server=private-vodshare
all.services[54].xmxKey=MAX
all.services[54].readOnly=true
all.services[54].description=\u5F55\u5236\u6587\u4EF6\u5206\u4EAB

all.services[55].displayName=\u76F4\u64AD\u670D\u52A1
all.services[55].server=private-live
all.services[55].xmxKey=LIVE_XMX
all.services[55].description=\u9884\u7EA6\u76F4\u64AD\u3001\u5F00\u542F/\u505C\u6B62\u76F4\u64AD\u3001\u89C2\u770B\u76F4\u64AD

all.services[56].displayName=\u89C6\u9891\u526A\u8F91\u670D\u52A1
all.services[56].server=private-vodedit
all.services[56].xmxKey=MAX
all.services[56].readOnly=true
all.services[56].description=\u5F55\u5236\u6587\u4EF6\u526A\u8F91

all.services[57].displayName=\u672C\u5730\u5F55\u5236\u6587\u4EF6\u4E0A\u4F20\u670D\u52A1
all.services[57].server=private-vodbroker
all.services[57].xmxKey=VODBROKER_XMX
all.services[57].description=NE60\u672C\u5730\u5F55\u5236\u4E0A\u4F20

all.services[58].displayName=nodelive\u670D\u52A1
all.services[58].server=private-nodelive
all.services[58].description=\u76F4\u64AD\u670D\u52A1\u7684api\u5C01\u88C5,\u76F4\u64AD\u524D\u53F0\u9875\u9762\u901A\u8FC7nodelive\u8BBF\u95EE\u76F4\u64AD\u670D\u52A1

all.services[59].displayName=\u63A8\u6D41\u670D\u52A1
all.services[59].server=private-srs
all.services[59].description=vod\u5185\u90E8nginx,\u8D1F\u8D23\u76F4\u64AD\u89C2\u770B\u5206\u53D1,\u76F8\u5F53\u4E8E\u76F4\u64ADcdn


#---------------- statis -------------------#
all.services[60].displayName=dcs\u670D\u52A1
all.services[60].server=private-dcs
all.services[60].xmxKey=DCS_XMX
all.services[60].description=\u7EDF\u8BA1\u670D\u52A1\u6570\u636E\u5E93\u7684api\u5C01\u88C5,\u5176\u4ED6\u4E1A\u52A1\u670D\u52A1\u901A\u8FC7dcs\u8BBF\u95EE\u7EDF\u8BA1\u670D\u52A1\u5668\u6570\u636E\u5E93

all.services[61].displayName=datafact\u670D\u52A1
all.services[61].server=private-datafact
all.services[61].xmxKey=DATAFACT_XMX
all.services[61].description=\u6570\u636E\u4E2D\u53F0\u7684\u6570\u636E\u4E0A\u62A5\u67E5\u8BE2,\u8D1F\u8D23\u4F1A\u8BAE\u65E5\u5FD7\u4E0A\u62A5\u548C\u67E5\u8BE2

all.services[62].displayName=\u4F1A\u8BAE\u8D28\u91CF\u7EDF\u8BA1\u670D\u52A1
all.services[62].server=private-statis-quality
all.services[62].xmxKey=MAX
all.services[62].readOnly=true
all.services[62].description=\u5F53\u524D\u4F1A\u8BAE\u8D28\u91CF\u3001\u5386\u53F2\u4F1A\u8BAE\u8D28\u91CF\uFF0C\u8D28\u91CF\u5BFC\u51FA

all.services[63].displayName=\u7EDF\u8BA1\u62A5\u544A\u670D\u52A1
all.services[63].server=private-statis-report
all.services[63].xmxKey=MAX
all.services[63].readOnly=true

all.services[64].displayName=\u4F1A\u8BAE\u7EDF\u8BA1\u670D\u52A1
all.services[64].server=private-statis-meeting
all.services[64].xmxKey=MAX
all.services[64].readOnly=true
all.services[64].description=\u5F53\u524D\u4F1A\u8BAE\u3001\u5386\u53F2\u4F1A\u8BAE\u3001\u4F1A\u8BAE\u65E5\u5FD7\u7EDF\u8BA1

all.services[65].displayName=\u7EDF\u8BA1\u76D1\u63A7\u670D\u52A1
all.services[65].server=private-statis-monitor
all.services[65].xmxKey=MAX
all.services[65].readOnly=true
all.services[65].description=\u4E91\u670D\u52A1\u76D1\u63A7,\u8BBE\u5907\u5728\u7EBF\u548C\u5CF0\u503C\u7EDF\u8BA1

all.services[66].displayName=\u7EDF\u8BA1\u6570\u636E\u5E93
all.services[66].server=private-statis-mysql
all.services[66].xmxKey=MAX
all.services[66].xmxMin=1024
all.services[66].readOnly=true

all.services[67].displayName=\u7EDF\u8BA1Hbase
all.services[67].server=private-hbase
all.services[67].xmxKey=MAX
all.services[67].xmxMin=1024
all.services[67].readOnly=true
all.services[67].description=\u5EFA\u7ACB\u5728Hadoop\u6587\u4EF6\u7CFB\u7EDF\u4E4B\u4E0A\u7684\u5206\u5E03\u5F0F\u9762\u5411\u5217\u7684\u6570\u636E\u5E93

all.services[68].displayName=dcs\u6587\u4EF6\u670D\u52A1
all.services[68].server=private-dcs-fileserver
all.services[68].xmxKey=MAX
all.services[68].readOnly=true
all.services[68].description=\u5386\u53F2\u4F1A\u8BAE\u548C\u65E5\u5FD7\u6587\u4EF6\u5BFC\u51FA


#---------------- ippbx -------------------#
all.services[69].displayName=IPPBX\u5A92\u4F53\u7F51\u5173
all.services[69].server=private-ippbx-mediagw
all.services[69].xmxKey=MAX
all.services[69].readOnly=true
all.services[69].description=\u5F71\u54CDippbx\u97F3\u89C6\u9891\u4E1A\u52A1

all.services[70].displayName=IPPBX\u4FE1\u4EE4\u7F51\u5173
all.services[70].server=private-ippbx-siggateway
all.services[70].xmxKey=SIGGATEWAY_XMX
all.services[70].description=\u5F71\u54CDippbx\u547C\u53EB\u4E1A\u52A1


#---------------- surv -------------------#
all.services[71].displayName=Nginx(\u76D1\u63A7\u878D\u5408)\u670D\u52A1
all.services[71].server=private-openresty-surv

all.services[72].displayName=\u76D1\u63A7\u878D\u5408(\u5A92\u4F53)
all.services[72].server=private-ma
all.services[72].xmxKey=MAX
all.services[72].readOnly=true

all.services[73].displayName=\u76D1\u63A7\u878D\u5408(\u4FE1\u4EE4)
all.services[73].server=private-shuttle
all.services[73].xmxKey=SHUTTLE_XMX
all.services[73].description=\u5BF9\u63A5\u4E09\u65B9\u76D1\u63A7\u5E73\u53F0\u6216ipc

all.services[74].displayName=\u76D1\u63A7\u63A5\u5165\u670D\u52A1
all.services[74].server=private-surv-access
all.services[74].xmxKey=SURV_ACCESSSERVER_XMX
all.services[74].description=\u7C7B\u4F3Caccess,\u548Cshuttle\u8FDB\u884Cws\u8FDE\u63A5\u7684

all.services[75].displayName=\u76D1\u63A7\u4E1A\u52A1\u670D\u52A1
all.services[75].server=private-survbiz
all.services[75].xmxKey=SURVBIZ_XMX
all.services[75].description=\u4E1A\u52A1\u670D\u52A1\u5B58\u50A8IPC\u76EE\u5F55\u5DF2\u7ECFshuttle\u7684sk\u76F8\u5173\u6743\u9650\u4FE1\u606F

all.services[76].displayName=\u76D1\u63A7\u4FE1\u4EE4\u670D\u52A1
all.services[76].server=private-survsig
all.services[76].xmxKey=SURVSIG_XMX
all.services[76].description=\u7C7B\u4F3Csigserver,\u5728\u76D1\u63A7\u4E2D\u626E\u6F14\u7684\u662F\u547C\u53EB\u7684\u89D2\u8272

all.services[77].displayName=\u76D1\u63A7\u6570\u636E\u5E93
all.services[77].server=private-surv-mysql
all.services[77].xmxKey=MAX
all.services[77].xmxMin=1024
all.services[77].readOnly=true


#---------------- matrix -------------------#
all.services[78].displayName=AI\u7B97\u6CD5\u670D\u52A1
all.services[78].server=private-matrix-alg
all.services[78].xmxKey=MAX
all.services[78].xmxMin=1024
all.services[78].readOnly=true
all.services[78].description=\u4EBA\u8138\u68C0\u6D4B\u3001\u8BA1\u7B97,\u53EF\u90E8\u7F72\u591A\u53F0,matrix\u5E94\u7528\u670D\u52A1\u5668\u901A\u8FC7\u6CE8\u518C\u53D1\u73B0\u52A8\u6001\u8FDE\u63A5\u591A\u4E2Amatrix\u7B97\u6CD5\u670D\u52A1\u5668,\u5747\u8861\u8C03\u7528matrix\u7B97\u6CD5\u670D\u52A1\u5668\u6765\u5B8C\u6210\u6700\u7EC8\u7684\u4EBA\u8138\u6CE8\u518C\u548C\u68C0\u6D4B

all.services[79].displayName=AI\u5E94\u7528\u670D\u52A1
all.services[79].server=private-matrix-app
all.services[79].xmxKey=MATRIXAPP_XMX
all.services[79].description=\u7BA1\u7406\u5E73\u53F0\u4EBA\u8138\u6CE8\u518C\u548C\u7BA1\u7406\u529F\u80FD\u3001\u7EC8\u7AEF\u4EBA\u8138\u8BC6\u522B\u529F\u80FD

all.services[80].displayName=AI\u6587\u4EF6\u670D\u52A1
all.services[80].server=private-matrix-fileserver
all.services[80].xmxKey=MAX
all.services[80].readOnly=true
all.services[80].description=\u7BA1\u7406\u5E73\u53F0\u6279\u91CF\u5BFC\u5165\u4EBA\u8138\u56FE\u7247

all.services[81].displayName=AI\u4E0A\u4F20\u670D\u52A1
all.services[81].server=private-matrix-upload
all.services[81].xmxKey=MATRIXUPLOAD_XMX
all.services[81].description=\u5BF9\u7BA1\u7406\u5E73\u53F0\u6279\u91CF\u4E0A\u4F20\u7684\u56FE\u7247\u8FDB\u884C\u89E3\u538B,\u5E76\u4E0A\u4F20\u5230\u670D\u52A1\u5668,\u5C06\u6D88\u606F\u8F6C\u53D1\u7ED9matrix\u5E94\u7528\u670D\u52A1\u5668

all.services[82].displayName=AI\u6570\u636E\u5E93
all.services[82].server=private-matrix-mysql
all.services[82].xmxKey=MAX
all.services[82].xmxMin=1024
all.services[82].readOnly=true


#---------------- edu -------------------#
all.services[83].displayName=\u6559\u80B2\u6570\u636E\u5E93
all.services[83].server=private-edu-mysql
all.services[83].xmxKey=MAX
all.services[83].xmxMin=1024
all.services[83].readOnly=true

all.services[84].displayName=\u6559\u80B2\u63A5\u5165\u670D\u52A1
all.services[84].server=private-edu-access
all.services[84].xmxKey=EDU_ACCESS_XMX
all.services[84].description=\u6559\u80B2h5\u573A\u666F\u548C\u52A9\u624B\u7EC8\u7AEF\u573A\u666F\u8131\u79BB\u4E1A\u52A1\u7684\u6D88\u606F\u63A8\u9001\u7CFB\u7EDF

all.services[85].displayName=\u6559\u80B2\u52A9\u624B\u540E\u53F0\u4E1A\u52A1
all.services[85].server=private-education
all.services[85].xmxKey=EDUCATION_XMX
all.services[85].description=\u8BFE\u7A0B\u8868\u83B7\u53D6,\u4F1A\u4E2D\u4E3E\u624B\u53D1\u8A00,\u767B\u5F55\u6559\u80B2\u52A9\u624B

all.services[86].displayName=\u6559\u80B2\u7BA1\u7406\u540E\u53F0
all.services[86].server=private-edu-manage
all.services[86].xmxKey=EDU_MANAGE_XMX
all.services[86].description=\u5907\u8BFE\u8D44\u6E90\u9884\u89C8\u3001\u6587\u6863\u4E2D\u5FC3\u8D44\u6E90\u9884\u89C8

all.services[87].displayName=\u6559\u80B2\u7EA6\u4F1A
all.services[87].server=private-edu-dating
all.services[87].xmxKey=EDU_DATING_XMX
all.services[87].description=\u9884\u7EA6\u8BFE\u7A0B\u3001\u8BFE\u7A0B\u7BA1\u7406

all.services[88].displayName=\u6559\u80B2\u8D44\u6E90
all.services[88].server=private-edu-resource
all.services[88].xmxKey=EDU_RESOURCE_XMX
all.services[88].description=\u6559\u80B2\u8D44\u6E90\u524D\u53F0\u89C2\u770B\u70B9\u64AD/\u5F55\u64AD\u89C6\u9891\u3001\u70B9\u64AD\u7BA1\u7406/\u5F55\u64AD\u8BFE\u7A0B

all.services[89].displayName=\u6559\u80B2\u6D88\u606F\u670D\u52A1
all.services[89].server=private-edu-message
all.services[89].xmxKey=EDU_MSGSERVER_XMX
all.services[89].description=\u8D1F\u8D23\u4F20\u9012message\u6D88\u606F\uFF0C\u5F71\u54CD\u4F1A\u63A7(\u4E3E\u624B\u3001\u7B54\u9898\u3001\u5DE1\u68C0\u7B49)\u3001\u9884\u7EA6\u4F1A\u8BAE\u3001\u65E5\u7A0B\u3001\u7BA1\u7406\u5E73\u53F0(\u8BBE\u5907\u64CD\u4F5C\u7B49)

all.services[90].displayName=\u6559\u80B2sdk/\u4E09\u65B9\u5BF9\u63A5
all.services[90].server=private-edu-adapter
all.services[90].xmxKey=EDU_ADAPTER_XMX
all.services[90].description=\u4E09\u65B9\u8BFE\u8868\u540C\u6B65

all.services[91].displayName=\u6559\u80B2\u6587\u4EF6\u9884\u89C8
all.services[91].server=private-edu-preview
all.services[91].xmxKey=EDU_PREVIEW_XMX
all.services[91].description=\u5907\u8BFE\u8D44\u6E90\u9884\u89C8\u3001\u6587\u6863\u4E2D\u5FC3\u8D44\u6E90\u9884\u89C8

all.services[92].displayName=\u6559\u80B2\u5F55\u5236\u5206\u4EAB
all.services[92].server=private-edu-vodshare
all.services[92].xmxKey=MAX
all.services[92].readOnly=true
all.services[92].description=\u540Cvodshare,\u6559\u80B2\u573A\u666F\u5355\u72EC\u90E8\u7F72

all.services[93].displayName=\u6559\u80B2\u6587\u4EF6\u670D\u52A1(\u7BA1\u7406)
all.services[93].server=private-file-manage
all.services[93].xmxKey=EDU_FILE_MANAGE_XMX
all.services[93].description=\u5404\u7C7B\u6587\u4EF6\u4E0A\u4F20

all.services[94].displayName=\u6559\u80B2\u6587\u4EF6\u670D\u52A1(\u64CD\u4F5C)
all.services[94].server=private-file-manage-ud
all.services[94].xmxKey=EDU_FILE_MANAGE_UD_XMX
all.services[94].description=\u6559\u80B2\u6587\u4EF6\u4E0A\u4F20\u9274\u6743

all.services[95].displayName=Nginx(\u6559\u80B2)\u670D\u52A1
all.services[95].server=private-openresty-edu

all.services[96].displayName=\u6559\u80B2\u6D88\u606F\u63A8\u9001
all.services[96].server=private-message-push
all.services[96].xmxKey=EDU_MESSAGE_PUSH_XMX
all.services[96].description=\u8D1F\u8D23\u63A8\u9001\u540E\u53F0\u6D88\u606F\u7ED9APP,\u5982\u94C3\u94DB\u6D88\u606F\u7B49

all.services[97].displayName=\u6559\u80B2Redis
all.services[97].server=private-edu-redis
all.services[97].xmxKey=MAX
all.services[97].readOnly=true

#---------------- webrtc -------------------#
all.services[98].displayName=WEBRTC API\u7F51\u5173
all.services[98].server=private-uaa-api
all.services[98].xmxKey=UAA_API_XMX
all.services[98].xmxMin=1024
all.services[98].description=API\u7F51\u5173,\u63A5\u53E3\u8DEF\u7531\u8F6C\u53D1,AE350\u7B49\u90E8\u5206\u786C\u4EF6\u63A5,webrtc\u547C\u53EB

all.services[99].displayName=WEBRTC \u5E73\u53F0\u8BA4\u8BC1\u670D\u52A1
all.services[99].server=private-uaa-admin
all.services[99].xmxKey=UAA_ADMIN_XMX
all.services[99].xmxMin=1024
all.services[99].description=API\u7F51\u5173,\u63A5\u53E3\u8DEF\u7531\u8F6C\u53D1,AE350\u7B49\u90E8\u5206\u786C\u4EF6\u63A5,webrtc\u547C\u53EB

all.services[100].displayName=WEBRTC \u7BA1\u7406\u540E\u53F0
all.services[100].server=private-uaa-base
all.services[100].xmxKey=UAA_BASE_XMX
all.services[100].xmxMin=1024
all.services[100].description=API\u7F51\u5173,\u63A5\u53E3\u8DEF\u7531\u8F6C\u53D1,AE350\u7B49\u90E8\u5206\u786C\u4EF6\u63A5,webrtc\u547C\u53EB

#---------------- main add -------------------#
all.services[101].displayName=\u4F1A\u63A7\u63A5\u5165
all.services[101].server=private-mms-edge
all.services[101].xmxKey=MMS_EDEG_XMX
all.services[101].description=\u5904\u7406\u524D\u7AEF\u8BF7\u6C42

all.services[102].displayName=\u4F1A\u63A7\u4E1A\u52A1
all.services[102].server=private-mms-logic
all.services[102].xmxKey=MMS_LOGIC_XMX
all.services[102].xmxMin=2048
all.services[102].description=\u4F1A\u63A7\u4E1A\u52A1\u5904\u7406

all.services[103].displayName=\u4F1A\u63A7\u4EE3\u7406
all.services[103].server=private-mmsproxy
all.services[103].xmxKey=MMS_PROXY_XMX
all.services[103].description=\u4F1A\u63A7\u8DE8\u4E91\u6D88\u606F\u3001\u4E3E\u624B\u6D88\u606F

all.services[104].displayName=BINLOG\u91C7\u96C6\u670D\u52A1
all.services[104].server=private-ocean-collector
all.services[104].xmxKey=OCEAN_COLLECTOR_XMX
all.services[104].description=mongodb\u7684\u6570\u636E\u5199\u5165(\u6765\u6E90\u4E8Ebinlog\u7684\u6570\u636E\u540C\u6B65),\u6D89\u53CA\u7528\u6237\u7BA1\u7406\u3001\u7EC8\u7AEF\u7BA1\u7406\u3001\u7EC4\u7EC7\u67B6\u6784\u3001\u4E91\u4F1A\u8BAE\u5BA4\u3001\u5F55\u5236\u6587\u4EF6

all.services[105].displayName=\u7B7E\u5230\u7B54\u9898\u901A\u77E5\u670D\u52A1
all.services[105].server=private-roster-msg
all.services[105].xmxKey=ROSTER_MSG_XMX
all.services[105].description=\u7B7E\u5230\u3001\u7B54\u9898\u3001\u8BC4\u4EF7\u3001\u6295\u7968,\u7ED9\u7EC8\u7AEF\u53D1\u6D88\u606F

all.services[106].displayName=\u9A8C\u8BC1\u7801\u670D\u52A1
all.services[106].server=private-captcha
all.services[106].xmxKey=CAPTCHA_XMX
all.services[106].description=\u4F01\u4E1A\u7BA1\u7406\u5E73\u53F0\u9A8C\u8BC1\u7801

all.services[107].displayName=\u76F4\u64AD\u89C2\u4F17\u670D\u52A1
all.services[107].server=private-live-audience
all.services[107].xmxKey=LIVE_AUDIENCE_XMX
all.services[107].description=\u76F4\u64AD\u89C2\u4F17\u670D\u52A1

all.services[108].displayName=\u76F4\u64AD\u4E0B\u8F7D\u670D\u52A1
all.services[108].server=private-business-download-service
all.services[108].xmxKey=BUS_DOWN_SER_XMX
all.services[108].description=live\u670D\u52A1excel\u5BFC\u51FA,\u7BA1\u7406\u5E73\u53F0\u4E2D\u5386\u53F2\u76F4\u64AD\u7684\u5BFC\u51FA\u3001\u6570\u636E\u4E2D\u5FC3-\u76F4\u64AD\u6570\u636E\u7684\u5BFC\u51FA\u62A5\u8868\u3001\u7EC8\u7AEF\u7684\u76F4\u64AD\u4E0B\u8F7D

all.services[109].displayName=\u5185\u7F51\u76F4\u64AD\u670D\u52A1
all.services[109].server=private-mcaccess
all.services[109].xmxKey=MCACCESS_XMX
all.services[109].description=\u652F\u6301\u5927\u89C4\u6A21\u5185\u7F51\u76F4\u64AD\u529F\u80FD

all.services[110].displayName=\u5185\u7F51\u76F4\u64AD\u670D\u52A1
all.services[110].server=private-avcloudapi
all.services[110].xmxKey=AVCLOUDAPI_XMX
all.services[110].description=\u652F\u6301\u5927\u89C4\u6A21\u5185\u7F51\u76F4\u64AD\u529F\u80FD

all.services[111].displayName=\u5185\u7F51\u70B9\u64AD\u670D\u52A1
all.services[111].server=private-vodclustermgr
all.services[111].xmxKey=VODCLUSTERMGR_XMX
all.services[111].description=\u652F\u6301\u5927\u89C4\u6A21\u5185\u7F51\u70B9\u64AD\u529F\u80FD

all.services[112].displayName=\u5185\u7F51\u76F4\u64AD\u70B9\u64ADsip\u670D\u52A1
all.services[112].server=private-sip-server
all.services[112].xmxKey=SIPSERVER_XMX
all.services[112].description=\u652F\u6301\u5927\u89C4\u6A21\u5185\u7F51\u70B9\u64AD,\u76F4\u64AD\u529F\u80FD

all.services[113].displayName=\u4F1A\u63A7\u8C03\u5EA6\u670D\u52A1
all.services[113].server=private-mms-dispatcher
all.services[113].xmxKey=MMS_DISPATCHER_XMX
all.services[113].description=\u4F1A\u63A7\u8C03\u5EA6

all.services[114].displayName=\u4F1A\u63A7\u5BC6\u7801\u6821\u9A8C
all.services[114].server=private-mms-permission
all.services[114].xmxKey=MMS_PERMISSION_XMX
all.services[114].description=\u4F1A\u8BAE\u4E3B\u6301\u5BC6\u7801\u6821\u9A8C

all.services[115].displayName=\u5FAE\u4FE1\u5C0F\u7A0B\u5E8F\u4FE1\u4EE4\u7F51\u5173
all.services[115].server=private-txlive
all.services[115].xmxKey=TX_LIVE_XMX
all.services[115].description=\u8D1F\u8D23\u548C\u4FE1\u4EE4\u670D\u52A1\u4EA4\u4E92

all.services[116].displayName=\u5FAE\u4FE1\u5C0F\u7A0B\u5E8FAPI\u7F51\u5173
all.services[116].server=private-txrest
all.services[116].xmxKey=TX_REST_XMX
all.services[116].description=\u8BBF\u95EE\u5FAE\u4FE1\u540E\u53F0\u670D\u52A1\u83B7\u53D6\u7528\u6237\u57FA\u7840\u4FE1\u606F,\u8BBF\u95EE\u5C0F\u9C7C\u540E\u53F0\u670D\u52A1\u83B7\u53D6\u8BBE\u5907\u53CA\u8D26\u53F7\u76F8\u5173\u4FE1\u606F

all.services[117].displayName=\u5FAE\u4FE1\u5C0F\u7A0B\u5E8FTSA\u670D\u52A1
all.services[117].server=private-tsa-mp
all.services[117].xmxKey=TSA_MP_XMX
all.services[117].description=\u5C0F\u7A0B\u5E8F\u7F51\u5173access

all.services[118].displayName=\u8DE8\u4E91\u7EA7\u8054roster\u670D\u52A1
all.services[118].server=private-roster
all.services[118].xmxKey=ROSTER_XMX
all.services[118].description=\u7EA7\u8054\u4F1A\u8BAE\u63D0\u4F9B\u53C2\u4F1A\u8005\u4FE1\u606F

all.services[119].displayName=\u8DE8\u4E91\u7EA7\u8054\u63A7\u5236\u5668
all.services[119].server=private-cascademgr
all.services[119].xmxKey=CASCADEMGR_SERVER_XMX
all.services[119].description=\u4F1A\u8BAE\u7EA7\u8054\u4E1A\u52A1\u7684\u8C03\u5EA6\u548C\u7BA1\u7406

all.services[120].displayName=\u8DE8\u4E91\u7EA7\u8054\u89C6\u8BAF\u72B6\u6001\u670D\u52A1
all.services[120].server=private-avstatusserver
all.services[120].xmxKey=AVSTATUS_SERVER_XMX
all.services[120].description=\u4FE1\u4EE4\u547C\u53EB\u548C\u4F1A\u8BAE\u72B6\u6001\u7684\u67E5\u8BE2

all.services[121].displayName=RTMP/RTSP\u4FE1\u4EE4\u7F51\u5173\u670D\u52A1
all.services[121].server=private-converged-siggw
all.services[121].xmxKey=CONVERGED_SIGGW_XMX
all.services[121].description=RTMP/RTSP\u6D41\u5A92\u4F53\u7EC8\u7AEF\u4FE1\u4EE4\u63A5\u5165\u670D\u52A1

all.services[122].displayName=\u878D\u5408\u4F1A\u7BA1mms\u670D\u52A1
all.services[122].server=private-fusion-mms
all.services[122].xmxKey=FUSION_MMS_XMX
all.services[122].description=\u878D\u5408\u4F1A\u7BA1mms\u670D\u52A1

all.services[123].displayName=\u878D\u5408\u4F1A\u7BA1uaa-admin\u670D\u52A1
all.services[123].server=private-fusion-uaa-admin
all.services[123].xmxKey=FUSION_UAA_ADMIN_XMX
all.services[123].description=\u878D\u5408\u4F1A\u7BA1API\u7F51\u5173,\u63A5\u53E3\u8DEF\u7531\u8F6C\u53D1

all.services[124].displayName=\u878D\u5408\u4F1A\u7BA1uaa-api\u670D\u52A1
all.services[124].server=private-fusion-uaa-api
all.services[124].xmxKey=FUSION_UAA_API_XMX
all.services[124].description=\u878D\u5408\u4F1A\u7BA1API\u7F51\u5173,\u63A5\u53E3\u8DEF\u7531\u8F6C\u53D1

all.services[125].displayName=\u878D\u5408\u4F1A\u7BA1uaa-base\u670D\u52A1
all.services[125].server=private-fusion-uaa-base
all.services[125].xmxKey=FUSION_UAA_BASE_XMX
all.services[125].description=\u878D\u5408\u4F1A\u7BA1API\u7F51\u5173,\u63A5\u53E3\u8DEF\u7531\u8F6C\u53D1