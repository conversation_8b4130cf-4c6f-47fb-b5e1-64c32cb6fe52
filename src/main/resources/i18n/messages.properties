global.title=\u670D\u52A1\u5668\u7BA1\u7406\u7CFB\u7EDF
global.name=\u5317\u4EAC\u8D5B\u8FDE\u79D1\u6280\u6709\u9650\u516C\u53F8
global.exit=\u9000\u51FA
global.changepwd=\u4FEE\u6539\u5BC6\u7801
global.input.require=\u5FC5\u586B
global.backhome=\u8FD4\u56DE\u9996\u9875
global.error=\u63D0\u793A
global.office.web=\u5B98\u65B9\u7F51\u7AD9
global.call.center=\u5BA2\u670D\u4E2D\u5FC3
global.manager.platform=\u670D\u52A1\u5668\u7BA1\u7406\u7CFB\u7EDF
global.welcome=\u6B22\u8FCE\u767B\u5F55\u670D\u52A1\u5668\u7BA1\u7406\u7CFB\u7EDF
global.reset.succ=\u4FEE\u6539\u6210\u529F
global.reset.fail=\u4FEE\u6539\u5931\u8D25,\u8BF7\u7A0D\u540E\u91CD\u8BD5
global.search=\u67E5\u8BE2
global.view=\u67E5\u770B
global.back=\u8FD4\u56DE
global.view.detail=\u67E5\u770B\u8BE6\u60C5
global.item.name=\u540D\u79F0
global.item.add=\u6DFB\u52A0
global.item.save=\u4FDD\u5B58
global.item.op=\u64CD\u4F5C
global.op.succ=\u64CD\u4F5C\u6210\u529F
global.invalidIp=\u65E0\u6548IP\uFF0C\u8BF7\u66F4\u65B0

global.item.required=\u5FC5\u586B\u9879
global.item.content.too.long=\u5185\u5BB9\u8FC7\u957F
global.item.content.number=\u5185\u5BB9\u5FC5\u987B\u4E3A\u6570\u5B57
prompt.user.mailbox.email=\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u90AE\u7BB1\u5730\u5740

#prompt
prompt.delete.confirm=\u786E\u5B9A\u8981\u5220\u9664\u5417\uFF1F
prompt.delete.error=\u5220\u9664\u5931\u8D25

login.login=\u767B\u5F55
login.username=\u5E10\u53F7
login.password=\u5BC6\u7801

module.service.title=\u670D\u52A1\u7BA1\u7406
module.server.upgrade.title=\u670D\u52A1\u5347\u7EA7
module.server.upgrade.history.title=\u5347\u7EA7\u5386\u53F2
module.sql.upgrade.title=\u6570\u636E\u5E93\u5347\u7EA7
module.server.list.title=\u670D\u52A1\u5668\u5217\u8868
module.server.repos.title=\u955C\u50CF\u5217\u8868
module.server.status.title=\u670D\u52A1\u72B6\u6001
module.server.network.title=\u670D\u52A1\u7F51\u7EDC\u914D\u7F6E
module.system.network.title=\u7CFB\u7EDF\u7F51\u7EDC\u914D\u7F6E
module.server.storage.title=\u5A92\u4F53\u5B58\u50A8\u914D\u7F6E
module.license.title=\u6CE8\u518C\u7801\u8BBE\u7F6E
module.server.package.download.title=\u57FA\u7840\u5305\u4E0B\u8F7D

module.monitor.title=\u670D\u52A1\u76D1\u63A7
module.monitor.base.title=\u57FA\u7840\u670D\u52A1
module.monitor.core.title=\u6838\u5FC3\u670D\u52A1
module.monitor.business.title=\u4E1A\u52A1\u670D\u52A1
module.monitor.vod.title=VOD\u670D\u52A1
module.monitor.other.title=\u5176\u4ED6\u670D\u52A1
module.monitor.node.title=\u670D\u52A1\u5668\u76D1\u63A7
module.monitor.pod.title=Pod\u76D1\u63A7

module.network.title=\u7F51\u7EDC\u914D\u7F6E
module.network.service.title=\u670D\u52A1\u7F51\u7EDC\u914D\u7F6E
module.network.ntp.title=NTP\u914D\u7F6E

module.endpoint.title=\u7EC8\u7AEF\u66F4\u65B0
module.endpoint.upgrade=\u5347\u7EA7\u7BA1\u7406

module.base.service.title=\u57FA\u7840\u4E1A\u52A1
module.base.service.log.download.title=\u65E5\u5FD7\u4E0B\u8F7D
module.base.service.database.title=\u6570\u636E\u5E93\u5907\u4EFD\u4E0E\u5347\u7EA7
module.base.service.production.registration.title=\u4EA7\u54C1\u6CE8\u518C
#module.server.mail.configuration.title=\u90AE\u7BB1\u914D\u7F6E
module.client.upload.title=\u5BA2\u6237\u7AEF\u4E0A\u4F20
module.server.status.deployment.title=Deployments
module.server.status.daemon.title=Daemon Sets

#mailConfig
mail.config.title=\u914D\u7F6E\u90AE\u4EF6\u670D\u52A1\u5668
mail.config.protocol=\u670D\u52A1\u5668\u7C7B\u578B
mail.config.host=\u670D\u52A1\u5668\u5730\u5740
mail.config.port=\u670D\u52A1\u5668\u7AEF\u53E3
mail.config.userName=\u7528\u6237\u540D
mail.config.password=\u5BC6\u7801
mail.config.encoding=\u7F16\u7801
mail.config.testMail=\u6D4B\u8BD5\u90AE\u7BB1
mail.config.apply=\u4FDD\u5B58
mail.config.test=\u6D4B\u8BD5
mail.config.send=\u53D1\u9001
mail.config.apply.success=\u4FDD\u5B58\u6210\u529F
mail.config.apply.failure=\u4FDD\u5B58\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5
mail.config.host.empty=\u670D\u52A1\u5668\u5730\u5740\u4E0D\u80FD\u4E3A\u7A7A
mail.config.userName.empty=\u7528\u6237\u540D\u4E0D\u80FD\u4E3A\u7A7A
mail.config.password.empty=\u5BC6\u7801\u4E0D\u80FD\u4E3A\u7A7A
mail.config.testMail.empty=\u6D4B\u8BD5\u90AE\u7BB1\u4E0D\u80FD\u4E3A\u7A7A
mail.config.test.success=\u90AE\u4EF6\u53D1\u9001\u6210\u529F
mail.config.test.failure=\u90AE\u4EF6\u53D1\u9001\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u90AE\u4EF6\u670D\u52A1\u5668\u914D\u7F6E

#alertConfig
alert.config.title=\u544A\u8B66\u914D\u7F6E
alert.config.cpu.threshold=CPU\u544A\u8B66\u9600\u503C
alert.config.memory.threshold=\u5185\u5B58\u544A\u8B66\u9600\u503C
alert.config.disk.threshold=\u78C1\u76D8\u544A\u8B66\u9600\u503C
alert.config.mail=\u544A\u8B66\u90AE\u7BB1
alert.config.threshold.title=\u544A\u8B66\u9600\u503C
alert.config.apply=\u4FDD\u5B58
alert.config.mail.name=\u59D3\u540D
alert.config.mail.content=\u90AE\u7BB1


error.lost.page=\u627E\u4E0D\u5230\u9875\u9762\u4E86<br>\u8BF7\u6838\u5BF9\u60A8\u8F93\u5165\u7684\u9875\u9762\u5730\u5740\u662F\u5426\u6B63\u786E

module.server_upgrade.title=\u670D\u52A1\u5347\u7EA7

# system information
module.system.version.information=\u670D\u52A1\u7248\u672C\u4FE1\u606F
module.system.version.no=\u7248\u672C\u53F7

module.db.select.file=\u9009\u62E9\u4E0A\u4F20\u6587\u4EF6
module.db.title=\u6570\u636E\u5E93\u5907\u4EFD\u4E0E\u5347\u7EA7
RESTORE=\u6062\u590D
BACKUP=\u5907\u4EFD

log.download=\u65E5\u5FD7\u4E0B\u8F7D
log.client.download=\u5BA2\u6237\u7AEF\u65E5\u5FD7\u4E0B\u8F7D
log.server.download=\u4E3B\u670D\u52A1\u5668\u7AEF\u65E5\u5FD7\u4E0B\u8F7D
log.server.other.download=\u5176\u5B83\u670D\u52A1\u5668\u7AEF\u65E5\u5FD7\u4E0B\u8F7D
log.client.name=\u5BA2\u6237\u7AEF\u65E5\u5FD7\u540D\u79F0
log.lastModify.time=\u6700\u540E\u4FEE\u6539\u65F6\u95F4
log.operation=\u64CD\u4F5C
log.all.download=\u4E00\u952E\u4E0B\u8F7D\u5168\u90E8
log.download.download=\u4E0B\u8F7D
log.server.name=\u670D\u52A1\u5668\u65E5\u5FD7\u540D\u79F0
log.client.name.directory=\u5BA2\u6237\u7AEF\u65E5\u5FD7\u76EE\u5F55
log.enter=\u8FDB\u5165\u8BE5\u76EE\u5F55

#license
license.title=\u6CE8\u518C\u7801\u8BBE\u7F6E
license.name=\u6CE8\u518C\u7801
license.import=\u91CD\u65B0\u5BFC\u5165
license.empty=\u6CE8\u518C\u7801\u4E0D\u80FD\u4E3A\u7A7A
license.import.success=\u5BFC\u5165\u6210\u529F
license.import.failure=\u5BFC\u5165\u5931\u8D25
license.session=\u5E76\u53D1\u6570\u91CF
license.issueTime=\u7B7E\u53D1\u65E5\u671F
license.expiredTime=\u8FC7\u671F\u65E5\u671F
license.invalidate=\u6CE8\u518C\u7801\u5DF2\u635F\u574F\u6216\u5DF2\u8FC7\u671F\uFF0C\u8BF7\u91CD\u65B0\u5BFC\u5165
license.sig=\u4FE1\u4EE4\u6A21\u5757
license.vod=\u5F55\u5236\u6A21\u5757
license.h323=H323\u6A21\u5757
license.imported=\u5DF2\u5BFC\u5165
license.fingerprint=\u6307\u7EB9\u7801
license.error=\u9519\u8BEF\u4FE1\u606F

prompt.save.error=\u4FDD\u5B58\u5931\u8D25
global.op.close=\u5173\u95ED
global.op.save=\u4FDD\u5B58
global.op.cancel=\u53D6\u6D88


# hybrid
module.hybrid.title=\u670D\u52A1\u72B6\u6001\u76D1\u63A7
module.hybrid.node.display=\u6839\u636ENode\u5C55\u793A
module.hybrid.pod.display=\u6839\u636EPod\u5C55\u793A
module.hybrid.node.select=\u9009\u62E9\u670D\u52A1\u5668
module.hybrid.pod.select=\u9009\u62E9Pod
module.hybrid.statis.memory.title=\u5185\u5B58
module.hybrid.statis.memory.unit=GB
module.hybrid.statis.cpu.title=CPU
#module.hybrid.statis.cpu.unit=Millicores
module.hybrid.statis.percent.unit=%
module.hybrid.statis.cpu.core=\u6838
module.hybrid.statis.filesystem.title=\u78C1\u76D8
module.hybrid.statis.filesystem.unit=GB
module.hybrid.statis.network.title=\u7F51\u7EDC\u5E26\u5BBD
module.hybrid.statis.network.unit=MB/s

docker.server.node.list=\u670D\u52A1\u5668\u5217\u8868
docker.server.node.status=\u72B6\u6001
docker.server.node.name=\u670D\u52A1\u5668\u540D\u79F0
docker.server.node.type=\u7C7B\u578B
docker.server.node.internal.ip=\u5185\u7F51IP
docker.server.node.external.ip=\u5916\u7F51IP
docker.server.node.report.ip=\u670D\u52A1\u5668\u4E0A\u62A5IP
docker.server.node.domain=\u57DF\u540D

docker.server.repos.list=\u955C\u50CF\u5217\u8868
docker.server.repos.name=\u955C\u50CF\u540D\u79F0
docker.server.repos.version=\u7248\u672C\u5217\u8868
docker.server.repos.version.now=\u5F53\u524D\u7248\u672C

package.base=\u57FA\u7840\u5305
package.dmcu=DMCU\u57FA\u7840\u5305
package.download=\u4E0B\u8F7D

gateway.item.list=\u7F51\u5173\u914D\u7F6E
gateway.item.sn=\u7F51\u5173ID
gateway.item.maxinout=\u6700\u5927\u65B9\u6570



