package com.xylink.util.sign;

import java.security.InvalidKeyException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.SignatureException;

public interface Sign {

    /**
     * 签名
     * @return
     */
    String sign(byte[] data, PrivateKey privateKey) throws InvalidKeyException, SignatureException;

    /**
     * 解签
     * @return
     */
    boolean verify(byte[] data, byte[] sign, PublicKey publicKey) throws InvalidKeyException, SignatureException;
}
