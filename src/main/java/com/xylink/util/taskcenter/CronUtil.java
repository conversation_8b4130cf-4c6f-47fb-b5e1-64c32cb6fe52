package com.xylink.util.taskcenter;

import com.xylink.config.exception.basic.ServerException;
import com.xylink.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.support.CronExpression;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Objects;

/**
 * <AUTHOR> create on 2023/12/12
 */
@Slf4j
public class CronUtil {


    /**
     * 创建类型为每天的 cron
     *
     * @param executeTime 开始时间 HH:mm 例如 17:00
     * @return cron 例如 0 00 17 * * ?
     */
    public static String buildDailyCron(String executeTime) {
        String[] split = executeTime.split(":");
        String cron = String.format("0 %s %s * * ?", split[1], split[0]);
        if (CronExpression.isValidExpression(cron)) {
            return cron;
        }
        throw new ServerException("Expression is not valid[" + cron + "]!");
    }

    /**
     * 创建类型以每周周几开始的 cron
     *
     * @param week      周几 例如 5 (周五)
     * @param startTime 开始时间 HH:mm 例如: 17:00
     * @return cron 例如: 0 00 17 ? * 5
     */
    public static String buildWeekCron(int week, String startTime) {
        String[] split = startTime.split(":");
        String cron = String.format("0 %s %s ? * %s", split[1], split[0], week);
        if (CronExpression.isValidExpression(cron)) {
            return cron;
        }
        throw new ServerException("Expression is not valid[" + cron + "]!");
    }


    /**
     * 创建类型为每月某号开始的 cron
     *
     * @param day       某号 例如 24 (24号)
     * @param startTime 开始时间 HH:mm 例如 17:00
     * @return cron 例如 0 00 17 24 * ?
     */
    public static String buildMonthlyCron(int day, String startTime) {
        String[] split = startTime.split(":");
        String cron = String.format("0 %s %s %s * ?", split[1], split[0], day);
        if (CronExpression.isValidExpression(cron)) {
            return cron;
        }
        throw new ServerException("Expression is not valid[" + cron + "]!");
    }

    /**
     * 根据 cron 获取下一次运行的时间戳
     *
     * @param cron .
     * @return 下一次运行的时间戳
     */
    public static long nextStartTime(String cron) {
        if (!CronExpression.isValidExpression(cron)) {
            throw new ServerException("Expression is not valid[" + cron + "]!");
        }
        CronExpression cronExpression = CronExpression.parse(cron);
        LocalDateTime nextTime = cronExpression.next(LocalDateTime.now());
        // LocalDateTime 转换为时间戳
        return Objects.requireNonNull(nextTime).toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
    }

    /**
     * 根据 cron 获取下一次运行的时间戳
     *
     * @param cron .
     * @return 下一次运行的时间戳
     */
    public static String nextStartTime(String cron, String format) {
        if (!CronExpression.isValidExpression(cron)) {
            throw new ServerException("Expression is not valid[" + cron + "]!");
        }
        CronExpression cronExpression = CronExpression.parse(cron);
        LocalDateTime nextTime = cronExpression.next(LocalDateTime.now());
        // LocalDateTime 转换为时间戳
        long time = Objects.requireNonNull(nextTime).toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
        return DateTimeUtil.dateToStr(DateTimeUtil.millsToDate(time), format);
    }


}
