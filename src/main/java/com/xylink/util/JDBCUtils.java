package com.xylink.util;

import com.google.common.collect.Lists;
import com.xylink.config.Constants;
import com.xylink.config.EduConstants;
import com.xylink.config.MysqlConstants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.config.exception.basic.ServiceErrorException;
import com.xylink.manager.controller.dto.*;
import com.xylink.manager.controller.dto.dmcu.DmcuIpAllocationRuleDto;
import com.xylink.manager.inspection.common.OpsManagerException;
import com.xylink.manager.model.*;
import com.xylink.manager.model.develop.DmcuDto;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.ServerListService;
import com.xylink.manager.service.ServerNetworkService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.dto.license.AccessTerminalTypeLimitDto;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.sql.*;
import java.util.Date;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 数据库相关的简单业务处理
 */
@Service
public class JDBCUtils {

    private final static Logger logger = LoggerFactory.getLogger(JDBCUtils.class);

    @Autowired
    private DbConfigUtil dbConfigUtil;
    @Autowired
    private ServerListService serverListService;
    @Autowired
    private RestTemplate restTemplate;

    private Connection con;

    @Resource
    private K8sService k8sService;
    @Resource
    private ServerNetworkService serverNetworkService ;

    ExecutorService pool = Executors.newSingleThreadExecutor();

    /**
     * 专有云配置连接mysql数据库
     *
     * @return
     */
    public synchronized Connection getProprietaryConnection() {
        try {
            Map<String, String> allIp = dbConfigUtil.getDbConfigMap();
            String dbIP = allIp.get(NetworkConstants.DATABASE_IP);
            String dbPort = allIp.get(NetworkConstants.DATABASE_PORT);
            String db = "proprietary";

            if (StringUtils.isNotBlank(dbIP) && StringUtils.isNotBlank(dbPort)) {
                String dbType = allIp.get(NetworkConstants.DATABASE_TYPE);
                String userName = allIp.get(NetworkConstants.MAIN_DB_SUPER_USERNAME);
                String pwd = serverListService.getUsernameOrPwdFromCM(allIp,NetworkConstants.MAIN_DB_SUPER_PASSWORD);
                DbConfigUtil.DbConfig dbConfig = new DbConfigUtil.DbConfig(getAddress(dbIP, dbIP, dbType), dbPort, dbType, db, allIp);
                con = DriverManager.getConnection(dbConfig.jdbcUrl(), userName, pwd);
            }
        } catch (SQLException e) {
            logger.error("fail to create db connection !!! msg:", e);
            throw new ServiceErrorException(ErrorStatus.DB_CONNECTION_ERROR);
        }
        return con;
    }

    /**
     * 信创环境：DB_USERNAME_PERMISSION_SECURITY_SWITCH=false
     */
    public synchronized Connection getProprietaryConnectionV2() {
        Map<String, String> allIp;
        try{
            allIp = dbConfigUtil.getDbConfigMap();
            String isUserNameSecurity = allIp.get("DB_USERNAME_PERMISSION_SECURITY_SWITCH");
            if(!Boolean.parseBoolean(isUserNameSecurity)) {
                return getProprietaryConnection();
            }
        }catch (Exception e) {
            logger.warn("DB_USERNAME_PERMISSION_SECURITY_SWITCH get error", e);
            return getProprietaryConnection();
        }

        try {
            if(CollectionUtils.isEmpty(allIp)) {
                allIp = dbConfigUtil.getDbConfigMap();
            }
            String dbIP = allIp.get(NetworkConstants.DATABASE_IP);
            String dbPort = allIp.get(NetworkConstants.DATABASE_PORT);
            String db = "proprietary";

            if (StringUtils.isNotBlank(dbIP) && StringUtils.isNotBlank(dbPort)) {
                String dbType = allIp.get(NetworkConstants.DATABASE_TYPE);
                String pwd = serverListService.getUsernameOrPwdFromCM(allIp,NetworkConstants.MAIN_DB_SUPER_PASSWORD);
                DbConfigUtil.DbConfig dbConfig = new DbConfigUtil.DbConfig(getAddress(dbIP, dbIP, dbType), dbPort, dbType, db, allIp);
                String url = dbConfig.jdbcUrl();
                DriverManager.setLoginTimeout(3);
                con = DriverManager.getConnection(url, db, pwd);
            }
        } catch (SQLException e) {
            logger.error("fail to create db connection !!! msg:", e);
            throw new ServiceErrorException(ErrorStatus.DB_CONNECTION_ERROR);
        }
        return con;
    }





    /**
     * 这里获取数据库连接可能失败（mysql pod未启动）
     */
    private synchronized Connection getConnection(String type) {
        try {
            Map<String, String> allIp = dbConfigUtil.getDbConfigMap();
            if (Labels.dcm.label().equals(type) || MysqlConstants.manager.equals(type)) {
                return getStatisConnectionByType(allIp, false, type);
            }
            String mainDbIP = allIp.get(NetworkConstants.DATABASE_IP);
            String dbIP = allIp.get(NetworkConstants.DATABASE_IP);
            String dbPort = allIp.get(NetworkConstants.DATABASE_PORT);
            String db = "ainemo";
            String pwd = serverListService.getUsernameOrPwdFromCM(allIp,NetworkConstants.MAIN_DB_SUPER_PASSWORD);

            if (Labels.webrtc.label().equals(type) || Labels.uaa.label().equals(type)) {
                dbIP = allIp.get(NetworkConstants.UAA_DATABASE_IP);
                db = "uaa";
                pwd = serverListService.getUsernameOrPwdFromCM(allIp,NetworkConstants.UAA_DB_PASSWORD);
            }
            if (Labels.edu.label().equals(type)) {
                dbIP = serverNetworkService.getEduDatabaseIpNetworkConfiguration();
                db = "education";
                pwd = serverListService.getUsernameOrPwdFromCM(allIp,NetworkConstants.EDU_DB_PASSWORD);
            }
            if (Labels.surv.label().equals(type)) {
                dbIP = allIp.get(NetworkConstants.DB_SURVEILLANCE);
                // 兼容老项目升级场景
                if(StringUtils.isBlank(dbIP)) {
                    dbIP = allIp.get(NetworkConstants.SURV_INTERNAL_IP);
                }
                db = "surveillance";
                if ("MYSQL".equals(allIp.get(NetworkConstants.DATABASE_TYPE))) {
                    pwd = serverListService.getUsernameOrPwdFromCM(allIp,NetworkConstants.SURV_DB_PASSWORD);
                }
            }

            if (StringUtils.isNotBlank(dbIP) && StringUtils.isNotBlank(dbPort)) {
                String dbType = allIp.get(NetworkConstants.DATABASE_TYPE);
                String userName = allIp.get(NetworkConstants.MAIN_DB_SUPER_USERNAME);

                DbConfigUtil.DbConfig dbConfig = new DbConfigUtil.DbConfig(getAddress(mainDbIP, dbIP, dbType), dbPort, dbType, db, allIp);
                String url = dbConfig.jdbcUrl();
                DriverManager.setLoginTimeout(3);
                con = DriverManager.getConnection(url, userName, pwd);
            }
        } catch (SQLException e) {
            logger.error("fail to create db connection !!! msg:", e);
            throw new ServiceErrorException(ErrorStatus.DB_CONNECTION_ERROR);
        }
        return con;
    }

    /**
     * 这里获取数据库连接可能失败（mysql pod未启动）
     * 信创环境：DB_USERNAME_PERMISSION_SECURITY_SWITCH=false
     */
    public synchronized Connection getConnectionV2(String type, String schema) {
        Map<String, String> allIp;
        try{
            allIp = dbConfigUtil.getDbConfigMap();
            String isUserNameSecurity = allIp.get("DB_USERNAME_PERMISSION_SECURITY_SWITCH");
            if(!Boolean.parseBoolean(isUserNameSecurity)) {
                return getConnection(type);
            }
        }catch (Exception e) {
            logger.warn("DB_USERNAME_PERMISSION_SECURITY_SWITCH get error", e);
            return getConnection(type);
        }

        String jdbcUrl = null;
        try{
            if(CollectionUtils.isEmpty(allIp)) {
                allIp = dbConfigUtil.getDbConfigMap();
            }
            if (Labels.dcm.label().equals(type) || MysqlConstants.manager.equals(type)) {
                return getStatisConnectionByType(allIp, true, type);
            }
            String dbIP = allIp.get(NetworkConstants.DATABASE_IP);
            String dbPort = allIp.get(NetworkConstants.DATABASE_PORT);
            String dbType = allIp.get(NetworkConstants.DATABASE_TYPE);

            if (StringUtils.isNotBlank(dbIP) && StringUtils.isNotBlank(dbPort)) {
                String pwd = serverListService.getUsernameOrPwdFromCM(allIp,NetworkConstants.MAIN_DB_SUPER_PASSWORD);

                DbConfigUtil.DbConfig dbConfig = new DbConfigUtil.DbConfig(dbIP, dbPort, dbType, schema, allIp);
                jdbcUrl = dbConfig.jdbcUrl();
                con = DriverManager.getConnection(jdbcUrl, schema, pwd);
            }
        } catch (SQLException e) {
            logger.error("fail to create db connection !!! jdbcUrl = {}, msg:", jdbcUrl, e);
            throw new ServiceErrorException(ErrorStatus.DB_CONNECTION_ERROR);
        }
        return con;
    }

    private String getAddress(String mainDbIp, String finalDbIp, String dbType) {
        return "MYSQL".equals(dbType) ? finalDbIp : mainDbIp;
    }


    public void close(Connection con, Statement stat, ResultSet rs) {
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException ex) {
                logger.error("close ResultSet error! ", ex);
            }
        }

        if (stat != null) {
            try {
                stat.close();
            } catch (SQLException ex) {
                logger.error("close Statement error! ", ex);
            }
        }

        if (con != null) {
            try {
                con.close();
            } catch (SQLException ex) {
                logger.error("close Connection error! ", ex);
            }
        }

    }


    /**
     * 异步添加内网外探测配置，应在mysql启动后触发
     */
    public void asyncConfigureNetworkDetection(String innerIp, String outerIp, String port) {
        pool.execute(() -> configureNetworkDetection(innerIp, outerIp, port));
    }

    public void configureNetworkDetection(String innerIp, String outerIp, String port) {

        logger.info("start to configure network detection...");
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");

        Optional<String> selfTestLinkSetting = this.getSelfTestLinkSetting();


        if ((StringUtils.isBlank(innerIp) && !selfTestLinkSetting.isPresent() )|| StringUtils.isBlank(outerIp)) return;

        if (connection == null) {
            logger.error("No mysql connection!!!");
            return;
        }

        String idSql = "SELECT id FROM ainemo.t_enterprise_node_config  WHERE " +
                " node ='connectionTest' and server_name='connectionTest' and enterprise_id ='default_enterprise' and provider ='PXYLINK' ";

        String updateSql = "UPDATE ainemo.t_enterprise_node_config SET inner_ip=? , outer_ip =? WHERE id =?";

        String insertSql = " INSERT INTO ainemo.t_enterprise_node_config (id, enterprise_id, node, server_name, inner_ip, outer_ip, provider) " +
                " VALUES (?, ?, 'connectionTest', 'connectionTest', " +
                " ? , ? , 'PXYLINK')";

        PreparedStatement stat = null;
        try {
            Statement statement = connection.createStatement();
            ResultSet resultSet = statement.executeQuery(idSql);
            String id = null;
            while (resultSet.next()) {
                id = resultSet.getString("id");
            }
            String inner = selfTestLinkSetting.orElse("http://" + innerIp + (StringUtils.isBlank(port) || "80".equals(port) ? "" : (":" + port)) + "/testlink.html");
            String outer = "http://" + outerIp + (StringUtils.isBlank(port) || "80".equals(port) ? "" : (":" + port)) + "/testlink.html";

            if (StringUtils.isBlank(id)) {
                String enterpriseId = getEnterpriseId();
                stat = connection.prepareStatement(insertSql);
                stat.setString(1, UUID.randomUUID().toString().replace("-", "").toLowerCase());
                stat.setString(2, enterpriseId);
                stat.setString(3, inner);
                stat.setString(4, outer);

                logger.info(stat.toString());
                stat.executeUpdate();
            } else {
                stat = connection.prepareStatement(updateSql);
                stat.setString(1, inner);
                stat.setString(2, outer);
                stat.setString(3, id);

                logger.info(stat.toString());
                stat.executeUpdate();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            close(connection, stat, null);
        }

    }

    private String getEnterpriseId() throws SQLException {
        Connection buffetConnection = getConnectionV2(Labels.mysql.label(), "buffet");
        PreparedStatement buffetStat = null;
        ResultSet buffetResultSet = null;
        String enterpriseId;
        try {
            buffetStat = buffetConnection.prepareStatement("select id from buffet.t_en_enterprise");
            buffetResultSet = buffetStat.executeQuery();
            enterpriseId = null;
            while (buffetResultSet.next()) {
                enterpriseId = buffetResultSet.getString("id");
            }
        } finally {
            close(buffetConnection, buffetStat, buffetResultSet);
        }
        return enterpriseId;
    }

    /**
     * 异步添加dmcu监控配置，应在mysql启动后触发
     */
    public void asyncConfigureDmcuOrRecordMonitor(NodeDto nodeDto, String comments, String capacity, String serverType, boolean addLabel,int instanceNum,String mcuSiteCode) {
        pool.execute(() -> configureDmcuOrRecordMonitor(nodeDto, comments, capacity, serverType, addLabel, instanceNum,mcuSiteCode));
    }


    private void configureDmcuOrRecordMonitor(NodeDto nodeDto, String comments, String capacity, String serverType, boolean addLabel,int instanceNum,String mcuSiteCode) {
        String nodeName = nodeDto.getName();
        String internalIp = Ipv6Util.getIpv6Addr(nodeDto.getInternalIp());
        String externalIp = nodeDto.getExternalIp();
        String stationName = comments;

        logger.info("start to configure dmcu monitor,instance number is:{}...", instanceNum);
        if (StringUtils.isBlank(nodeName) || StringUtils.isBlank(capacity)) {
            logger.error("config dmcu monitor error , param is null");
            return;
        }

        Connection connection = getConnectionV2(Labels.mysql.label(), "buffet");

        String queryEnterpriseName = "SELECT name FROM buffet.t_en_enterprise WHERE id ='default_enterprise'";
        String addDmcu = "INSERT INTO buffet.t_en_server_config (" +
                "id, " +
                "enterprise_id, " +
                "enterprise_name, " +
                "server_type, " +
                "state, " +
                "capacity, " +
                "host_name, " +
                "server_internal_ip, " +
                "server_external_ip, " +
                "server_name, " +
                "station_name, " +
                "create_time, " +
                "update_time, " +
                "mcu_site_code) " +
                "VALUES ( ?, 'default_enterprise', ?, " + serverType + " , 1, ?, ?, ?, ?, ?, ?, NOW(), NOW(),? )";
        String updateDmcu = "UPDATE buffet.t_en_server_config SET  " +
                "enterprise_name = ?, " +
                "server_type = " + serverType + ", " +
                "state = " + (addLabel ? 1 : 0) + ", " +
                "capacity = ?, " +
                "host_name = ?, " +
                "server_internal_ip = ?, " +
                "server_external_ip = ? , " +
                "server_name = ?, " +
                "station_name = ?, " +
                "update_time = NOW()  " +
                "WHERE " +
                " id =?";

        String updateDmcuByNodeName = "UPDATE buffet.t_en_server_config SET  " +
                "enterprise_name = ?, " +
                "server_type = " + serverType + ", " +
                "state = " + (addLabel ? 1 : 0) + ", " +
                "capacity = ?, " +
                "server_internal_ip = ?, " +
                "server_external_ip = ? , " +
                "station_name = ?, " +
                "update_time = NOW()  " +
                "WHERE " +
                " host_name =?";

        String updateDmcuNoStationNameByNodeName = "UPDATE buffet.t_en_server_config SET  " +
                "enterprise_name = ?, " +
                "server_type = " + serverType + ", " +
                "state = " + (addLabel ? 1 : 0) + ", " +
                "capacity = ?, " +
                "server_internal_ip = ?, " +
                "server_external_ip = ? , " +
                "update_time = NOW()  " +
                "WHERE " +
                " host_name =?";
        String queryDmcu = "SELECT id FROM buffet.t_en_server_config WHERE server_type=" + serverType + "  AND host_name =? ";

        String queryNodeDmcus = "SELECT host_name FROM buffet.t_en_server_config WHERE server_type=" + serverType + "  AND host_name like ? ";
        String queryNodeDmcusInDM = "SELECT host_name FROM buffet.t_en_server_config WHERE server_type=" + serverType + "  AND host_name like ? ESCAPE '\\'";

        String delete = "delete from buffet.t_en_server_config where server_type=" + serverType + " AND host_name =?";

        String deleteMultiInstance = "delete from buffet.t_en_server_config where server_type=" + serverType + " AND host_name like ?";
        String deleteMultiInstanceInDM = "delete from buffet.t_en_server_config where server_type=" + serverType + " AND host_name like ? ESCAPE '\\'";

        PreparedStatement stat = null;
        ResultSet resultSet;
        try {
            //查询企业名称
            Statement statement = connection.createStatement();
            resultSet = statement.executeQuery(queryEnterpriseName);
            String enName = null;
            while (resultSet.next()) {
                enName = resultSet.getString("name");
            }
            if (instanceNum>1){
                // 删除之前单实例的记录
                stat = connection.prepareStatement(delete);
                stat.setString(1,nodeName);
                stat.executeUpdate();

                // 多实例处理逻辑
                if ("dm.jdbc.driver.DmDriver".equalsIgnoreCase(connection.getMetaData().getDriverName())){
                    stat = connection.prepareStatement(queryNodeDmcusInDM);
                }else {
                    stat = connection.prepareStatement(queryNodeDmcus);
                }
                stat.setString(1, nodeName+"\\_%");
                resultSet = stat.executeQuery();
                List<String> dmcuRecords = new ArrayList<>();
                while (resultSet.next()) {
                    dmcuRecords.add(resultSet.getString("host_name"));
                }

                if (CollectionUtils.isEmpty(dmcuRecords)) {
                    if (addLabel) {
                        String joinNodeName = nodeName;
                        String joinStationName = comments;
                        for (int i = 0; i < instanceNum; i++) {
                            joinNodeName = nodeName + "_" + (i + 1);
                            if (StringUtils.isBlank(comments)) {
                                joinStationName = joinNodeName;
                            }
                            stat = connection.prepareStatement(addDmcu);
                            stat.setString(1, UUID.randomUUID().toString().replace("-", "").toLowerCase());
                            stat.setString(2, enName);
                            stat.setInt(3, Integer.parseInt(capacity));
                            stat.setString(4, joinNodeName);
                            stat.setString(5, internalIp);
                            stat.setString(6, externalIp);
                            stat.setString(7, joinNodeName);
                            stat.setString(8, joinStationName);
                            stat.setString(9, mcuSiteCode);
                            logger.info(stat.toString());
                            stat.executeUpdate();
                        }
                    }
                }else{
                    // 更新已有数据
                    // instanceNum > dmcuRecords.size() 新增记录
                    // instanceNum < dmcuRecords.size() 不处理
                    if (instanceNum > dmcuRecords.size()) {
                        Optional<Integer> number = dmcuRecords.stream().map(item -> Integer.parseInt(item.replace(nodeDto.getName() + "_", ""))).max(Integer::compareTo);
                        int currentIndex = number.get();
                        if(instanceNum > currentIndex){
                            String joinNodeName = nodeName;
                            String joinStationName = comments;
                            for (int i = currentIndex; i < instanceNum; i++) {
                                joinNodeName = nodeName + "_" + (i + 1);
                                if (StringUtils.isBlank(comments)) {
                                    joinStationName = joinNodeName;
                                }
                                stat = connection.prepareStatement(addDmcu);
                                stat.setString(1, UUID.randomUUID().toString().replace("-", "").toLowerCase());
                                stat.setString(2, enName);
                                stat.setInt(3, Integer.parseInt(capacity));
                                stat.setString(4, joinNodeName);
                                stat.setString(5, internalIp);
                                stat.setString(6, externalIp);
                                stat.setString(7, joinNodeName);
                                stat.setString(8, joinStationName);
                                stat.setString(9, mcuSiteCode);
                                logger.info(stat.toString());
                                stat.executeUpdate();
                            }
                        }
                    }

                    for(String updateItem : dmcuRecords){
                        if (StringUtils.isBlank(comments)){
                            stat = connection.prepareStatement(updateDmcuNoStationNameByNodeName);
                            stat.setString(1, enName);
                            stat.setInt(2, Integer.parseInt(capacity));
                            stat.setString(3, internalIp);
                            stat.setString(4, externalIp);
                            stat.setString(5, updateItem);
                            logger.info(stat.toString());
                            stat.executeUpdate();
                        }else{
                            stat = connection.prepareStatement(updateDmcuByNodeName);
                            stat.setString(1, enName);
                            stat.setInt(2, Integer.parseInt(capacity));
                            stat.setString(3, internalIp);
                            stat.setString(4, externalIp);
                            stat.setString(5, comments);
                            stat.setString(6, updateItem);
                            logger.info(stat.toString());
                            stat.executeUpdate();
                        }
                    }

                }
            }else{
                // 删除多实例的记录
                if ("dm.jdbc.driver.DmDriver".equalsIgnoreCase(connection.getMetaData().getDriverName())){
                    stat = connection.prepareStatement(deleteMultiInstanceInDM);
                }else {
                    stat = connection.prepareStatement(deleteMultiInstance);
                }

                stat.setString(1, nodeName+"\\_%");
                stat.executeUpdate();

                if (StringUtils.isBlank(stationName)) {
                    stationName = nodeName;
                }
                //校验配置是否存在
                stat = connection.prepareStatement(queryDmcu);
                stat.setString(1, nodeName);
                resultSet = stat.executeQuery();
                String id = null;
                while (resultSet.next()) {
                    id = resultSet.getString("id");
                }

                if (StringUtils.isBlank(id)) {
                    if (addLabel) {
                        stat = connection.prepareStatement(addDmcu);
                        stat.setString(1, UUID.randomUUID().toString().replace("-", "").toLowerCase());
                        stat.setString(2, enName);
                        stat.setInt(3, Integer.parseInt(capacity));
                        stat.setString(4, nodeName);
                        stat.setString(5, internalIp);
                        stat.setString(6, externalIp);
                        stat.setString(7, nodeName);
                        stat.setString(8, stationName);
                        stat.setString(9, mcuSiteCode);
                        logger.info(stat.toString());
                        stat.executeUpdate();
                    }
                } else {
                    stat = connection.prepareStatement(updateDmcu);
                    stat.setString(1, enName);
                    stat.setInt(2, Integer.parseInt(capacity));
                    stat.setString(3, nodeName);
                    stat.setString(4, internalIp);
                    stat.setString(5, externalIp);
                    stat.setString(6, nodeName);
                    stat.setString(7, stationName);
                    stat.setString(8, id);
                    logger.info(stat.toString());
                    stat.executeUpdate();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            close(connection, stat, null);
        }
    }


    public void deleteDmcuOrRecordMonitorConfig(String nodeName) {
        if (StringUtils.isBlank(nodeName)) return;

        String delete = "delete from buffet.t_en_server_config where host_name =? or host_name like ? ";
        String deleteInDM = "delete from buffet.t_en_server_config where host_name =? or host_name like ? ESCAPE '\\'";
        PreparedStatement stat;
        Connection connection = getConnectionV2(Labels.mysql.label(), "buffet");
        try {
            if ("dm.jdbc.driver.DmDriver".equalsIgnoreCase(connection.getMetaData().getDriverName())) {
                stat = connection.prepareStatement(deleteInDM);
            } else {
                stat = connection.prepareStatement(delete);
            }
            stat.setString(1, nodeName);
            stat.setString(2, nodeName+"\\_%");

            logger.info(stat.toString());
            stat.executeUpdate();
        } catch (Exception e) {
            logger.error("delete config of dmcu or record monitor error!", e);
        } finally {
            close(connection, null, null);
        }

    }
    public void asynUpdateDmcuSitecode(String serverType, String nodeName, String siteCode) {
        pool.execute(() -> updateDmcuSitecode(serverType, nodeName, siteCode));
    }
    private void updateDmcuSitecode(String serverType, String nodeName, String siteCode) {
        if (StringUtils.isBlank(nodeName)) {
            logger.error("updateDmcuSitecode error , param is null");
            return;
        }
        Connection connection = getConnectionV2(Labels.mysql.label(), "buffet");
        if (connection == null) {
            logger.error("No mysql connection!!!");
            return;
        }
        // 更新逻辑
        String updateSiteCode = "update buffet.t_en_server_config set mcu_site_code =? where server_type=? AND (host_name =? or host_name like ?)";
        String updateSiteCodeInDM = "update buffet.t_en_server_config set mcu_site_code =? where server_type=? AND (host_name =? or host_name like ? ESCAPE '\\')";
        PreparedStatement stat = null;
        //校验配置是否存在
        try {
            if ("dm.jdbc.driver.DmDriver".equalsIgnoreCase(connection.getMetaData().getDriverName())){
                stat = connection.prepareStatement(updateSiteCodeInDM);
            }else{
                stat = connection.prepareStatement(updateSiteCode);
            }
            stat.setString(1, siteCode);
            stat.setString(2, serverType);
            stat.setString(3, nodeName);
            stat.setString(4, nodeName+"\\_%");
            stat.executeUpdate();
        } catch (SQLException e) {
            logger.error("updateDmcuSitecode error,serverType = {}, nodeName = {}, stationName = {}, siteCode = {}",
                    serverType, nodeName, siteCode, e);
        } finally {
            close(connection, stat, null);
        }

    }

    /**
     * 异步添加终端网络测试配置，应在mysql启动后触发
     */
    public void asyncConfigureNetworkTest(String outerIp, String outerIpV6, String mainNodeNetToolPort) {
        pool.execute(() -> configureNetworkTest(outerIp, outerIpV6, mainNodeNetToolPort));
    }

    private void configureNetworkTest(String outerIp,String outerIpV6, String mainNodeNetToolPort) {

        logger.info("start to configure network test...");
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");


        if (StringUtils.isBlank(outerIp)) {
            logger.error("main public ip is null!");
            return;
        }

        if (connection == null) {
            logger.error("No mysql connection!!!");
            return;
        }

        String idSql = "SELECT id FROM ainemo.libra_enterprise_site_path  WHERE " +
                " location_part_of_sitecode ='86' and enterprise_part_of_sitecode='EDEF' and enterprise_id ='default' and sublocation_part_of_sitecode ='SLDEF' and provider_part_of_sitecode='PDEF' and network_type = 1";

        String insertSql = "INSERT INTO ainemo.libra_enterprise_site_path ( id, location_part_of_sitecode, enterprise_part_of_sitecode, net_tool_server, disaplay_name, enterprise_id, detail, time_enable, time_disable, enabled, sublocation_part_of_sitecode, provider_part_of_sitecode, network_type, ipv6_net_tool_server)" +
                "VALUES " +
                " ( ?," +
                " '86', " +
                " 'EDEF', " +
                " ?, " +
                " 'CHN', " +
                " 'default', " +
                " '私有云', " +
                " 1534008219075, " +
                " 0, " +
                " 1, " +
                " 'SLDEF', " +
                " 'PDEF', " +
                " 1 ,"+
                " ?)";

        String updateSql = "UPDATE ainemo.libra_enterprise_site_path SET " +
                "location_part_of_sitecode = '86', " +
                "enterprise_part_of_sitecode = 'EDEF', " +
                "net_tool_server = ? , " +
                "disaplay_name = 'CHN', " +
                "enterprise_id = 'default', " +
                "detail = '私有云', " +
                "time_enable = 1534008219075, " +
                "time_disable = 0, " +
                "enabled = 1, " +
                "sublocation_part_of_sitecode = 'SLDEF', " +
                "provider_part_of_sitecode = 'PDEF', " +
                "ipv6_net_tool_server = ? , " +
                "network_type = 1 WHERE id = ? ";

        PreparedStatement stat = null;
        String netToolServer = outerIp + ":" + mainNodeNetToolPort;
        String netToolServerIpv6 = "";
        if (StringUtils.isNotBlank(outerIpV6)) {
            netToolServerIpv6 = outerIpV6 + ":" + mainNodeNetToolPort;
        }
        try {
            Statement statement = connection.createStatement();
            ResultSet resultSet = statement.executeQuery(idSql);
            String id = null;
            while (resultSet.next()) {
                id = resultSet.getString("id");
            }


            if (StringUtils.isBlank(id)) {
                stat = connection.prepareStatement(insertSql);
                stat.setString(1, UUID.randomUUID().toString().replace("-", "").toLowerCase());
                stat.setString(2, netToolServer);
                stat.setString(3, netToolServerIpv6);

                logger.info(stat.toString());
                stat.executeUpdate();
            } else {
                stat = connection.prepareStatement(updateSql);
                stat.setString(1, netToolServer);
                stat.setString(2, netToolServerIpv6);
                stat.setString(3, id);

                logger.info(stat.toString());
                stat.executeUpdate();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            close(connection, stat, null);
        }

    }


    public Devices queryDevices(String type, String snOrNumber) {

        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        Devices devices = new Devices();

        String queryBySn = "SELECT " +
                "  a.id, " +
                "  a.device_display_name, " +
                "  a.device_sn, " +
                "  a.device_sk, " +
                "  a.device_category, " +
                "  a.sub_type, " +
                "  b.number  " +
                "FROM " +
                "  ainemo.libra_user_device a " +
                "  LEFT JOIN ainemo.libra_nemo_number b ON a.id = b.device_id  " +
                "WHERE " +
                "  a.device_sn = ?";

        String queryByNumber = "SELECT " +
                "  a.id, " +
                "  a.device_display_name, " +
                "  a.device_sn, " +
                "  a.device_sk, " +
                "  a.device_category, " +
                "  a.sub_type, " +
                "  b.number  " +
                "FROM " +
                "  ainemo.libra_user_device a " +
                "  LEFT JOIN ainemo.libra_nemo_number b ON a.id = b.device_id  " +
                "WHERE " +
                "  b.number = ?";


        PreparedStatement stat = null;
        try {
            String sql;
            if ("sn".equals(type)) {
                sql = queryBySn;
            } else if ("number".equals(type)) {
                sql = queryByNumber;
            } else {
                return null;
            }


            stat = connection.prepareStatement(sql);
            stat.setString(1, snOrNumber);
            ResultSet resultSet = stat.executeQuery();
            String id = null;
            while (resultSet.next()) {
                devices.setId(resultSet.getString("id"));
                devices.setName(resultSet.getString("device_display_name"));
                devices.setSn(resultSet.getString("device_sn"));
                devices.setSk(resultSet.getString("device_sk"));
                devices.setModel(resultSet.getString("device_category"));
                devices.setSubType(resultSet.getString("sub_type"));
                devices.setNumber(resultSet.getString("number"));
            }


        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            close(connection, stat, null);
        }

        return devices;

    }

    public List<Devices> queryHotStandbyDevices() {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");

        String sql = "select sub_type,category_display from ainemo.libra_device_subtype_model where hot_standby = 1";

        PreparedStatement stat = null;
        try {
            stat = connection.prepareStatement(sql);
            ResultSet resultSet = stat.executeQuery();
            List<Devices> devicesList = new ArrayList<>();
            while (resultSet.next()) {
                Devices devices = new Devices();
                devices.setName(resultSet.getString("category_display"));
                devices.setSubType(resultSet.getString("sub_type"));
                devicesList.add(devices);
            }
            return devicesList;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            close(connection, stat, null);
        }
        return new ArrayList<>();
    }

    public String queryDevicesModel(String subType, String display) {

        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        Devices devices = new Devices();

        String query = "SELECT model FROM ainemo.libra_device_subtype WHERE sub_type = ? AND display_model = ?  AND model IS NOT NULL";
        PreparedStatement stat = null;
        String model = null;
        try {
            stat = connection.prepareStatement(query);
            stat.setInt(1, Integer.parseInt(subType));
            stat.setString(2, display);
            ResultSet resultSet = stat.executeQuery();
            while (resultSet.next()) {
                model = resultSet.getString("model");
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            close(connection, stat, null);
        }

        return model;

    }


    public List<Config> listBuffetConfigDict() {
        Connection connection = getConnectionV2(Labels.mysql.label(), "external");
        String query = "SELECT name, value FROM external.nconsole_config_name ";
        PreparedStatement stat = null;
        List<Config> list = new ArrayList<>();
        try {
            Statement statement = connection.createStatement();
            ResultSet resultSet = statement.executeQuery(query);
            while (resultSet.next()) {
                Config config = new Config();
                config.setName(resultSet.getString("name"));
                config.setConfigName(resultSet.getString("value"));
                config.setConfigValue("false");
                //隐藏1080P权限配置
                if(!"SHOW_HR_RECORD".equalsIgnoreCase(config.getConfigName()) && !"REGISTER_INITROLE".equals(config.getConfigName())){
                    list.add(config);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            close(connection, stat, null);
        }
        return list;

    }

    public List<Config> listBuffetConfig() {
        Connection connection = getConnectionV2(Labels.mysql.label(), "buffet");
        String query = "SELECT id, config_name,config_value FROM buffet.t_en_enterprise_config where enterprise_id='default_enterprise' ";
        PreparedStatement stat = null;
        List<Config> list = new ArrayList<>();
        try {
            Statement statement = connection.createStatement();
            ResultSet resultSet = statement.executeQuery(query);
            while (resultSet.next()) {
                Config config = new Config();
                config.setId(resultSet.getString("id"));
                config.setConfigName(resultSet.getString("config_name"));
                config.setConfigValue(resultSet.getString("config_value"));
                //隐藏1080P权限配置
                if(!config.getConfigName().equalsIgnoreCase("SHOW_HR_RECORD")){
                    list.add(config);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            close(connection, stat, null);
        }
        return list;
    }

    /**
     * 获取config_name为APP_SETTING的config_value
     * @return
     */
    public String getConfigValueByAppSetting() {

        String query = "SELECT config_value FROM buffet.t_en_enterprise_config where enterprise_id='default_enterprise' and config_name = 'APP_SETTING'";
        Connection connection = null;
        try {
            connection = getConnection(Labels.mysql.label());
            Statement statement = connection.createStatement();
            ResultSet resultSet = statement.executeQuery(query);
            while (resultSet.next()) {
                return resultSet.getString("config_value");
            }
        } catch (Exception e) {
            logger.error("get config value failed from buffet where value_name = APP_SETTING");
        } finally {
            close(connection, null, null);
        }
        return "";
    }

    public void saveBuffetConfig(String configName, String configValue) {
        if (StringUtils.isBlank(configName) || StringUtils.isBlank(configValue)) return;

        Connection connection = getConnectionV2(Labels.mysql.label(), "buffet");
        String query = "SELECT id FROM buffet.t_en_enterprise_config where enterprise_id='default_enterprise' and config_name=?";

        String add = "INSERT INTO buffet.t_en_enterprise_config" +
                " (id, enterprise_id, config_name, config_value, config_expire_time, create_time, update_time) " +
                " VALUES " +
                " ( ?, 'default_enterprise', ?, ?, 9223372036854775807, now(), now() )";


        String update = "UPDATE buffet.t_en_enterprise_config  " +
                "SET config_value = ? , " +
                "update_time = NOW( )  " +
                "WHERE " +
                " enterprise_id = 'default_enterprise'  " +
                " AND config_name = ? ";
        PreparedStatement stat = null;
        try {
            stat = connection.prepareStatement(query);
            stat.setString(1, configName);
            ResultSet resultSet = stat.executeQuery();
            String id = null;
            while (resultSet.next()) {
                id = resultSet.getString("id");
            }

            if (StringUtils.isBlank(id)) {
                stat = connection.prepareStatement(add);
                stat.setString(1, UUID.randomUUID().toString().replace("-", "").toLowerCase());
                stat.setString(2, configName);
                stat.setString(3, configValue);
                logger.info(stat.toString());
                stat.executeUpdate();
            } else {
                stat = connection.prepareStatement(update);
                stat.setString(1, configValue);
                stat.setString(2, configName);
                logger.info(stat.toString());
                stat.executeUpdate();

            }
        } catch (Exception e) {
            logger.error("failed save buffet config",e);
        } finally {
            close(connection, stat, null);
        }


    }

    public boolean saveBuffetConfigDict(String configName, String name) {
        if (StringUtils.isBlank(configName) || StringUtils.isBlank(name)) return false;

        Connection connection = getConnectionV2(Labels.mysql.label(), "external");
        String query = "SELECT value FROM external.nconsole_config_name where value=?";

        String add = "INSERT INTO external.nconsole_config_name  ( name, value ) VALUES ( ? , ? )";

        String update = "UPDATE external.nconsole_config_name SET name = ?  WHERE  value = ? ";
        PreparedStatement stat = null;
        try {
            stat = connection.prepareStatement(query);
            stat.setString(1, configName);
            ResultSet resultSet = stat.executeQuery();
            String id = null;
            while (resultSet.next()) {
                id = resultSet.getString("value");
            }

            if (StringUtils.isBlank(id)) {
                stat = connection.prepareStatement(add);
                stat.setString(1, name);
                stat.setString(2, configName);
            } else {
                stat = connection.prepareStatement(update);
                stat.setString(1, name);
                stat.setString(2, configName);
            }
            logger.info(stat.toString());
            stat.executeUpdate();
            return StringUtils.isNotBlank(id);
        } catch (Exception e) {
            logger.error("save buffet configDict failed", e);
        } finally {
            close(connection, stat, null);
        }
        return false;
    }

    public void updateBuffetAndSynchronizePivot(String configName, String configValue){
        if (StringUtils.isAnyBlank(configName,configValue)) {
            return;
        }

        Connection connection = getConnectionV2(Labels.mysql.label(), "buffet");
        String query = "SELECT id FROM buffet.t_en_enterprise_config where enterprise_id='default_enterprise' and config_name=?";

        String add = "INSERT INTO buffet.t_en_enterprise_config" +
                " (id, enterprise_id, config_name, config_value, config_expire_time, create_time, update_time) " +
                " VALUES " +
                " ( ?, 'default_enterprise', ?, ?, 9223372036854775807, now(), now() )";


        String update = "UPDATE buffet.t_en_enterprise_config  " +
                "SET config_value = ? , " +
                "update_time = NOW( )  " +
                "WHERE " +
                " enterprise_id = 'default_enterprise'  " +
                " AND config_name = ? ";
        PreparedStatement stat = null;
        ResultSet resultSet = null;
        try {
            connection.setAutoCommit(false);
            stat = connection.prepareStatement(query);
            stat.setString(1, configName);
            resultSet = stat.executeQuery();
            String id = null;
            while (resultSet.next()) {
                id = resultSet.getString("id");
            }

            if (StringUtils.isBlank(id)) {
                stat = connection.prepareStatement(add);
                stat.setString(1, UUID.randomUUID().toString().replace("-", "").toLowerCase());
                stat.setString(2, configName);
                stat.setString(3, configValue);
                logger.info(stat.toString());
                stat.executeUpdate();
            } else {
                stat = connection.prepareStatement(update);
                stat.setString(1, configValue);
                stat.setString(2, configName);
                logger.info(stat.toString());
                stat.executeUpdate();
            }
            synchronizePivot();
            connection.commit();
        } catch (Exception e) {
            try {
                connection.rollback();
            } catch (SQLException e1) {
                logger.error("rollback error:", e);
            }
            logger.error("error occurred,Transaction rolled back:", e);
        } finally {
            close(connection, stat, resultSet);
        }

    }

    public void synchronizePivot(){
        String url = "http://" + serverListService.getMainNodeInternalIP() + ":11111/api/rest/internal/v1/buffet/enterprise/exconfig/saveOrUpdate?configName=%s&configValue=false&enterpriseId=default_enterprise";
        ResponseEntity<String> recordingResponse = restTemplate.exchange(String.format(url, "enableLocalRecording"), HttpMethod.POST, null, String.class);
        logger.info("localRecord value is false, response = {}", recordingResponse.getBody());
        //本地录制主持人审批
        ResponseEntity<String> approvalResponse = restTemplate.exchange(String.format(url, "enableLocalRecordingApproval"), HttpMethod.POST, null, String.class);
        logger.info("localRecordApproval value is false, response = {}", approvalResponse.getBody());
    }

    public void deleteBuffetConfigAndDict(String configName) {
        if (StringUtils.isBlank(configName)) {
            return;
        }

        Connection connectionExt = getConnectionV2(Labels.mysql.label(), "external");

        String deleteDictg = "DELETE FROM external.nconsole_config_name WHERE value = ?";

        PreparedStatement stat = null;
        try {
            stat = connectionExt.prepareStatement(deleteDictg);
            stat.setString(1, configName);
            logger.info(stat.toString());
            stat.executeUpdate();

            logger.info(stat.toString());
            stat.executeUpdate();

        } catch (Exception e) {
            logger.error("error:", e);
        } finally {
            close(connectionExt, stat, null);
        }
    }


    /**
     * 查询所有终端类型 subtype 及接入配置
     */
    public List<ClientAccess> clientAccessConfig() {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");

        List<ClientAccess> list = new ArrayList<>();
        String queryAllSubTypes = "select sub_type, category from ainemo.libra_device_subtype_model";
        String queryAccessConfig = "select device_sub_type from ainemo.libra_enterprise_device_auth_config where enterprise_id='default_enterprise'";
        PreparedStatement stat = null;
        Map<String, ClientAccess> map = new HashMap<>();
        try {
            Statement statement = connection.createStatement();
            ResultSet resultSet = statement.executeQuery(queryAllSubTypes);
            while (resultSet.next()) {
                if ("70".equals(resultSet.getString("sub_type"))) continue;
                ClientAccess clientAccess = new ClientAccess(
                        resultSet.getString("category"),
                        resultSet.getString("sub_type"),
                        true,
                        resultSet.getString("sub_type")
                );
                map.put(resultSet.getString("sub_type"), clientAccess);
            }


            resultSet = statement.executeQuery(queryAccessConfig);
            while (resultSet.next()) {
                ClientAccess clientAccess = map.get(resultSet.getString("device_sub_type"));
                if (clientAccess != null) clientAccess.setAllow(false);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            close(connection, stat, null);
        }
        list.addAll(map.values());
        return list;

    }

    /**
     * 禁止终端接入类型
     */
    public void addForbidAccessClient(List<String> subTypes) {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        String insertSql = "INSERT INTO ainemo.libra_enterprise_device_auth_config ( id, enterprise_id, join_enterprise, device_sub_type, device_type )" +
                "VALUES " +
                " ( ?," +
                " 'default_enterprise', " +
                " 0, " +
                " ?, " +
                " 0 )";
        PreparedStatement stat = null;
        try {
            connection.setAutoCommit(false);
            for (String subType : subTypes) {
                stat = connection.prepareStatement(insertSql);
                stat.setString(1, UUID.randomUUID().toString().replace("-", "").toLowerCase());
                stat.setString(2, subType);
                stat.executeUpdate();
                logger.info(stat.toString());
            }
            connection.commit();
        } catch (Exception e) {
            try {
                connection.rollback();
            } catch (SQLException e1) {
                logger.error("error:", e);
            }
            logger.error("error:", e);
        } finally {
            close(connection, stat, null);
        }
    }


    public void setTerminalAccessLimit(List<String> seriesWhiteList, AccessTerminalTypeLimitDto accessTerminalTypeLimitDto) {
        logger.info("start set terminal access limit into database");
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        if (ObjectUtils.isEmpty(connection)) {
            throw new ServiceErrorException(ErrorStatus.TERMINAL_ACCESS_LIMIT_FAILED);
        }

        try {
            connection.setAutoCommit(false);
            setTerminalSeriesWhitelist(connection, seriesWhiteList);
            setTerminalTypeAccessBlackList(connection, accessTerminalTypeLimitDto);
            connection.commit();
        } catch (Exception e) {
            logger.error("set access series whitelist and type blacklist failed,", e);
            try {
                connection.rollback();
            } catch (SQLException e1) {
                logger.error("roll back failed", e);
            }
            throw new ServiceErrorException(ErrorStatus.TERMINAL_ACCESS_LIMIT_FAILED);
        } finally {
            close(connection, null, null);
        }
        logger.info("set terminal access limit into database successfully");
    }

    private void setTerminalSeriesWhitelist(Connection connection, List<String> seriesWhiteList) throws SQLException {
        if (CollectionUtils.isEmpty(seriesWhiteList)) {
            throw new ServiceErrorException(ErrorStatus.TERMINAL_ACCESS_LIMIT_NOT_EXIST);
        }
        String deleteOldSeriesWhiteList = "DELETE FROM ainemo.libra_ent_series_white_config";
        String insertNewSeriesWhiteList = "INSERT INTO ainemo.libra_ent_series_white_config(id,enterprise_id,series_name) VALUES (?,'default_enterprise',?)";
        PreparedStatement stat = connection.prepareStatement(deleteOldSeriesWhiteList);
        stat.executeUpdate();
        stat = connection.prepareStatement(insertNewSeriesWhiteList);
        for (int i = 0; i < seriesWhiteList.size(); i++) {
            stat.setLong(1, i + 1);
            stat.setString(2, seriesWhiteList.get(i));
            stat.addBatch();
        }
        logger.info(stat.toString());
        stat.executeBatch();
    }

    private void setTerminalTypeAccessBlackList(Connection connection, AccessTerminalTypeLimitDto accessTerminalTypeLimitDto) throws SQLException {
        if (ObjectUtils.isEmpty(accessTerminalTypeLimitDto) || CollectionUtils.isEmpty(accessTerminalTypeLimitDto.getNeedUpdatedSubtypeBlacklist())) {
            return;
        }
        logger.info("start set terminal type access blacklist");
        String existedListSql = "SELECT join_enterprise,device_sub_type FROM ainemo.libra_enterprise_device_auth_config";

        Set<Integer> existedBlacklist = new HashSet<>();
        Set<Integer> existedWhitelist = new HashSet<>();
        PreparedStatement ps = connection.prepareStatement(existedListSql);
        logger.info(ps.toString());
        ResultSet resultSet = ps.executeQuery();
        while (resultSet.next()) {
            int joinEnterprise = resultSet.getInt("join_enterprise");
            if (joinEnterprise == 1) {
                //白名单
                existedWhitelist.add(resultSet.getInt("device_sub_type"));
            } else {
                //黑名单
                existedBlacklist.add(resultSet.getInt("device_sub_type"));
            }
        }

        List<Integer> nonDeletedSubtypeList = accessTerminalTypeLimitDto.getNonDeletedSubtypeList();
        List<Integer> needUpdatedSubtypeBlacklist = accessTerminalTypeLimitDto.getNeedUpdatedSubtypeBlacklist();
        List<Integer> needUpdateList = new ArrayList<>();
        List<Integer> needInsertList = new ArrayList<>();
        needUpdatedSubtypeBlacklist.forEach(x -> {
            //之前的白名单,现在要变黑,需要update
            if (existedWhitelist.contains(x)) {
                needUpdateList.add(x);
            }else if (!existedBlacklist.contains(x)) {
                //之前黑名单未包含,需要insert
                needInsertList.add(x);
            }
        });
        List<Integer> needDeleteList = existedBlacklist.stream().filter(x -> !needUpdatedSubtypeBlacklist.contains(x) && !nonDeletedSubtypeList.contains(x)).collect(Collectors.toList());
        insertAndUpdateTerminalBlackList(connection, needUpdateList, needInsertList, needDeleteList);
    }

    private void insertAndUpdateTerminalBlackList(Connection connection, List<Integer> needUpdateList, List<Integer> needInsertList,List<Integer> needDeleteList) throws SQLException {
        logger.info("terminal type limit updateList:{},insertList:{},deleteList:{}", needUpdateList, needInsertList, needDeleteList);
        if (!CollectionUtils.isEmpty(needUpdateList)) {
            String updateSql = "UPDATE ainemo.libra_enterprise_device_auth_config SET join_enterprise=0 WHERE device_sub_type IN";
            updateSql = SqlUtil.getWhereInSql(updateSql, needUpdateList.size());
            PreparedStatement updatePS = connection.prepareStatement(updateSql);
            for (int i = 0; i < needUpdateList.size(); i++) {
                Integer blackList = needUpdateList.get(i);
                updatePS.setInt(i + 1, blackList);
            }
            logger.info("update terminal type blacklist sql:{}", updatePS.toString());
            updatePS.executeUpdate();
        }

        if (!CollectionUtils.isEmpty(needInsertList)) {
            String insertSql = "INSERT INTO ainemo.libra_enterprise_device_auth_config(id, enterprise_id, join_enterprise, device_sub_type, device_type) " +
                    "VALUES(? ,'default_enterprise' ,0 ,? ,0)";
            PreparedStatement insertPS = connection.prepareStatement(insertSql);
            for (Integer subType : needInsertList) {
                insertPS.setString(1, UUID.randomUUID().toString().replace("-", "").toLowerCase());
                insertPS.setInt(2, subType);
                insertPS.addBatch();
            }
            logger.info("insert terminal type blacklist sql:{}", insertPS.toString());
            insertPS.executeBatch();
        }

        if (!CollectionUtils.isEmpty(needDeleteList)) {
            String deleteSql = "DELETE FROM ainemo.libra_enterprise_device_auth_config WHERE device_sub_type IN";
            deleteSql = SqlUtil.getWhereInSql(deleteSql, needDeleteList.size());
            PreparedStatement stat = connection.prepareStatement(deleteSql);
            for (int i = 0; i < needDeleteList.size(); i++) {
                stat.setInt(i+1,needDeleteList.get(i));
            }
            logger.info("delete terminal type blacklist sql:{}", stat.toString());
            stat.executeUpdate();
        }
    }

    /**
     * 清空非默认系列下的黑名单型号
     * @param nonDeletedSubtype 默认系列下的所有型号
     */
    public void clearTerminalBlackList(List<Integer> nonDeletedSubtype) {
        logger.info("clear terminal blackList");
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        String deleteSql = "DELETE FROM ainemo.libra_enterprise_device_auth_config WHERE device_sub_type NOT IN";
        PreparedStatement stat = null;
        try {
            deleteSql = SqlUtil.getWhereInSql(deleteSql, nonDeletedSubtype.size());
            stat = connection.prepareStatement(deleteSql);
            for (int i = 0; i < nonDeletedSubtype.size(); i++) {
                stat.setInt(i+1,nonDeletedSubtype.get(i));
            }
            logger.info("delete terminal type blacklist sql:{}", stat.toString());
            stat.executeUpdate();
        } catch (Exception e) {
            try {
                connection.rollback();
            } catch (SQLException e1) {
                logger.error("clear terminal terminal blacklist error rollback failed :", e);
            }
            logger.error("clear terminal terminal blacklist error:", e);
            throw new ServiceErrorException(ErrorStatus.TERMINAL_ACCESS_LIMIT_FAILED);
        } finally {
            close(connection, stat, null);
        }
    }

    /**
     * 取消终端限制接入类型
     */
    public void deleteForbidAccessClient(List<String> subTypes) {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        StringBuffer deleteConfig = new StringBuffer("DELETE FROM ainemo.libra_enterprise_device_auth_config  WHERE " +
                "enterprise_id = 'default_enterprise' AND " +
                "join_enterprise = 0 AND " +
                "device_sub_type in (");

        Statement stat = null;
        try {

            for (String subType : subTypes) {
                deleteConfig.append("'" + subType + "',");
            }
            String sql = deleteConfig.toString();
            if (sql.endsWith(",")) {
                sql = sql.substring(0, sql.length() - 1);
            }
            sql = sql + ")";
            stat = connection.createStatement();
            logger.info(sql);
            stat.executeUpdate(sql);

        } catch (Exception e) {
            try {
                connection.rollback();
            } catch (SQLException e1) {
                logger.error("error:", e);
            }
            logger.error("error:", e);
        } finally {
            close(connection, stat, null);
        }
    }

    public List<ClientFeatureConfig> clientFeatureConfig(ClientFeatureConfig config) {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");

        List<ClientFeatureConfig> configs = new ArrayList<>();

        String clientConfigNameQueryForCommon = "( client_config_name='' or client_config_name is null or client_config_name='common' )";

        String sql = null;
        Statement statement = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet;
        try {
            if (config == null || StringUtils.isBlank(config.getModelName())) {
                sql = "SELECT client_config_name FROM ainemo.libra_default_config GROUP BY client_config_name";
                statement = connection.createStatement();
                resultSet = statement.executeQuery(sql);
                logger.info(statement.toString());
                Set<String> models = new HashSet<>();
                while (resultSet.next()) {
                    models.add(StringUtils.isBlank(resultSet.getString("client_config_name")) ? "common" : resultSet.getString("client_config_name"));
                }
                configs = models.stream().map(model -> new ClientFeatureConfig(model, null, null, null, null, null, null)).collect(Collectors.toList());


            } else if (StringUtils.isNotBlank(config.getModelName()) && StringUtils.isNotBlank(config.getConfigName()) && StringUtils.isNotBlank(config.getClientType())) {
                sql = "SELECT config_value FROM ainemo.libra_default_config WHERE  config_name=? AND config_type =? AND  client_config_name=? ";
                if ("common".equals(config.getModelName()))
                    sql = sql.replace("client_config_name=?", clientConfigNameQueryForCommon);
                preparedStatement = connection.prepareStatement(sql);
                preparedStatement.setString(1, config.getConfigName());
                preparedStatement.setString(2, config.getClientType());
                if (!"common".equals(config.getModelName())) preparedStatement.setString(3, config.getModelName());
                logger.info(preparedStatement.toString());
                resultSet = preparedStatement.executeQuery();
                ClientFeatureConfig clientFeatureConfig = null;
                while (resultSet.next()) {
                    clientFeatureConfig = new ClientFeatureConfig(
                            config.getModelName(),
                            config.getConfigName(),
                            config.getClientType(),
                            null,
                            resultSet.getString("config_value"),
                            null,
                            null
                    );
                }
                if (clientFeatureConfig != null) configs.add(clientFeatureConfig);

            } else if (StringUtils.isNotBlank(config.getModelName()) && StringUtils.isNotBlank(config.getConfigName())) {
                sql = "SELECT config_type FROM ainemo.libra_default_config WHERE  config_name=? AND  client_config_name=? ";
                if ("common".equals(config.getModelName()))
                    sql = sql.replace("client_config_name=?", clientConfigNameQueryForCommon);
                preparedStatement = connection.prepareStatement(sql);
                preparedStatement.setString(1, config.getConfigName());
                if (!"common".equals(config.getModelName())) preparedStatement.setString(2, config.getModelName());
                logger.info(preparedStatement.toString());
                resultSet = preparedStatement.executeQuery();
                Set<String> types = new HashSet<>();
                while (resultSet.next()) {
                    types.add(resultSet.getString("config_type"));
                }
                configs = types.stream().map(type -> new ClientFeatureConfig(config.getModelName(), config.getConfigName(), type, null, null, null, null)).collect(Collectors.toList());


            } else if (StringUtils.isNotBlank(config.getModelName())) {

                sql = "SELECT config_name FROM ainemo.libra_default_config WHERE  client_config_name = ? ";
                if ("common".equals(config.getModelName()))
                    sql = sql.replace("client_config_name = ?", clientConfigNameQueryForCommon);
                preparedStatement = connection.prepareStatement(sql);
                if (!"common".equals(config.getModelName())) preparedStatement.setString(1, config.getModelName());
                logger.info(preparedStatement.toString());
                resultSet = preparedStatement.executeQuery();
                Set<String> configNames = new HashSet<>();
                while (resultSet.next()) {
                    configNames.add(resultSet.getString("config_name"));
                }
                configs = configNames.stream().map(name -> new ClientFeatureConfig(config.getModelName(), name, null, null, null, null, null)).collect(Collectors.toList());

            }
        } catch (Exception e) {
            logger.error("error:", e);
        } finally {
            close(connection, statement == null ? preparedStatement : statement, null);
        }

        return configs;
    }

    private List<CloudMeetingRoomConfig> listMeetingFeatureType() {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            String sql = "SELECT distinct config_profile_type FROM ainemo.libra_conference_config_display GROUP BY config_profile_type";
            statement = connection.createStatement();
            resultSet = statement.executeQuery(sql);
            logger.info(statement.toString());
            Set<String> configTypeSet = new HashSet<>();
            while (resultSet.next()) {
                configTypeSet.add(resultSet.getString("config_profile_type"));
            }
            return configTypeSet.stream().map(configType -> new CloudMeetingRoomConfig(configType, null, null, null)).collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("listMeetingFeatureType error:", e);
            return Collections.emptyList();
        } finally {
            close(connection, statement, resultSet);
        }
    }

    private List<CloudMeetingRoomConfig> listMeetingFeatureValue(CloudMeetingRoomConfig config) {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        try {
            String sql = "SELECT config_display FROM ainemo.libra_conference_config_display WHERE config_profile_type=? AND config_profile_value =? AND  config_name=? ";
            preparedStatement = connection.prepareStatement(sql);
            preparedStatement.setString(1, config.getConfigType());
            preparedStatement.setString(2, config.getConfigLevel());
            preparedStatement.setString(3, config.getConfigName());
            logger.info(preparedStatement.toString());
            resultSet = preparedStatement.executeQuery();

            List<CloudMeetingRoomConfig> result = Lists.newArrayList();
            CloudMeetingRoomConfig meetingRoomConfig;
            while (resultSet.next()) {
                meetingRoomConfig = new CloudMeetingRoomConfig(
                        config.getConfigType(),
                        config.getConfigLevel(),
                        config.getConfigName(),
                        resultSet.getInt("config_display")+""
                );
                result.add(meetingRoomConfig);
            }
            return result;
        } catch (Exception e) {
            logger.error("listMeetingFeatureValue error:", e);
            return Collections.emptyList();
        } finally {
            close(connection, preparedStatement, resultSet);
        }
    }

    private List<CloudMeetingRoomConfig> listMeetingFeatureName(CloudMeetingRoomConfig config) {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        try {
            String sql = "SELECT distinct config_name FROM ainemo.libra_conference_config_display WHERE  config_profile_type=? AND config_profile_value =? order by config_name";
            preparedStatement = connection.prepareStatement(sql);
            preparedStatement.setString(1, config.getConfigType());
            preparedStatement.setString(2, config.getConfigLevel());
            logger.info(preparedStatement.toString());
            resultSet = preparedStatement.executeQuery();

            List<CloudMeetingRoomConfig> result = Lists.newArrayList();
            CloudMeetingRoomConfig meetingRoomConfig;
            while (resultSet.next()) {
                meetingRoomConfig = new CloudMeetingRoomConfig(
                        config.getConfigType(),
                        config.getConfigLevel(),
                        resultSet.getString("config_name"),
                        null
                );
                //隐藏1080P权限配置
                if(!meetingRoomConfig.getConfigName().equalsIgnoreCase("showHrRecord")){
                    result.add(meetingRoomConfig);
                }
            }
            return result;
        } catch (Exception e) {
            logger.error("listMeetingFeatureName error:", e);
            return Collections.emptyList();
        } finally {
            close(connection, preparedStatement, resultSet);
        }
    }

    private List<CloudMeetingRoomConfig> listMeetingFeatureLevel(CloudMeetingRoomConfig config) {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        try {
            String sql = "SELECT distinct config_profile_value FROM ainemo.libra_conference_config_display WHERE config_profile_type=?";
            preparedStatement = connection.prepareStatement(sql);
            preparedStatement.setString(1, config.getConfigType());
            logger.info(preparedStatement.toString());
            resultSet = preparedStatement.executeQuery();

            List<CloudMeetingRoomConfig> result = Lists.newArrayList();
            CloudMeetingRoomConfig meetingRoomConfig;
            while (resultSet.next()) {
                meetingRoomConfig = new CloudMeetingRoomConfig(
                        config.getConfigType(),
                        resultSet.getString("config_profile_value"),
                        null, null
                );
                result.add(meetingRoomConfig);
            }
            return result;
        } catch (Exception e) {
            logger.error("listMeetingFeatureLevel error:", e);
            return Collections.emptyList();
        } finally {
            close(connection, preparedStatement, resultSet);
        }
    }

    public List<CloudMeetingRoomConfig> listMeetingConfigName() {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        Statement statement = null;
        ResultSet resultSet = null;
        List<CloudMeetingRoomConfig> res = new ArrayList<>();
        try {
            String sql = "select distinct config_name from ainemo.libra_conference_enterprise_config where config_name in ('allowMergeConf','allowMaxContentNum','enableDynamicGroup','dynamicGroupCount','dynamicGroupMode','professionalMC','watermark','allowCallScope','transcription','subtitle','userAvatarModeUserType','shareChatContentPermission') group by config_name order by config_name";
            statement = connection.createStatement();
            resultSet = statement.executeQuery(sql);
            logger.info(statement.toString());
            while (resultSet.next()) {
                String configName = resultSet.getString("config_name");
                res.add(new CloudMeetingRoomConfig(null, null, configName, null));
            }
            return res;
        } catch (Exception e) {
            logger.error("listMeetingConfigName error:", e);
            return Collections.emptyList();
        } finally {
            close(connection, statement, resultSet);
        }
    }

    public List<CloudMeetingRoomConfig> listMeetingConfigValue(CloudMeetingRoomConfig config) {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        List<CloudMeetingRoomConfig> result = Lists.newArrayList();
        try {
            String sql = "SELECT config_value FROM ainemo.libra_conference_enterprise_config WHERE config_name= ?";
            preparedStatement = connection.prepareStatement(sql);
            preparedStatement.setString(1, config.getConfigName());
            logger.info(preparedStatement.toString());
            resultSet = preparedStatement.executeQuery();

            CloudMeetingRoomConfig meetingRoomConfig;
            while (resultSet.next()) {
                meetingRoomConfig = new CloudMeetingRoomConfig(
                        config.getConfigType(),
                        config.getConfigLevel(),
                        config.getConfigName(),
                        resultSet.getString("config_value")
                );
                result.add(meetingRoomConfig);
                return result;
            }
        } catch (Exception e) {
            logger.error("listMeetingConfigValue error:", e);
            return Collections.emptyList();
        } finally {
            close(connection, preparedStatement, resultSet);
        }
        return result;
    }

    public List<CloudMeetingRoomConfig> listMeetingFeature(CloudMeetingRoomConfig config) {
        if (Objects.isNull(config) || StringUtils.isBlank(config.getConfigType())) {
            return listMeetingFeatureType();
        } else if (StringUtils.isNotBlank(config.getConfigType()) && StringUtils.isNotBlank(config.getConfigLevel()) && StringUtils.isNotBlank(config.getConfigName())) {
            return listMeetingFeatureValue(config);
        } else if (StringUtils.isNotBlank(config.getConfigType()) && StringUtils.isNotBlank(config.getConfigLevel())) {
            List<CloudMeetingRoomConfig> configResult = listMeetingFeatureName(config);
            return configResult.stream().filter(c -> !(StringUtils.equals("transcription",c.getConfigName()) && StringUtils.equals("conference",c.getConfigType()))).collect(Collectors.toList());
        } else {
            return listMeetingFeatureLevel(config);
        }
    }

    public void editMeetingConfig(CloudMeetingRoomConfig config) {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        String update = "UPDATE ainemo.libra_conference_enterprise_config SET config_value = ?  WHERE config_name = ?";

        PreparedStatement stat = null;
        try {
            stat = connection.prepareStatement(update);
            stat.setString(1, config.getValue());
            stat.setString(2, config.getConfigName());
            logger.info(stat.toString());
            stat.executeUpdate();
        } catch (Exception e) {
            logger.error("editMeetingConfig error", e);
        } finally {
            close(connection, stat, null);
        }
    }

    public void editMeetingFeature(CloudMeetingRoomConfig config) {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        String update = "UPDATE ainemo.libra_conference_config_display SET config_display = ?  WHERE config_name = ? AND config_profile_type = ? and config_profile_value = ?";

        PreparedStatement stat = null;
        try {
            stat = connection.prepareStatement(update);
            stat.setInt(1, Integer.parseInt(config.getValue()));
            stat.setString(2, config.getConfigName());
            stat.setString(3, config.getConfigType());
            stat.setString(4, config.getConfigLevel());
            logger.info(stat.toString());
            stat.executeUpdate();
        } catch (Exception e) {
            logger.error("editMeetingFeature error", e);
            throw new ServiceErrorException(ErrorStatus.DB_OPERATION_ERROR);
        } finally {
            close(connection, stat, null);
        }
    }

    public void editClientFeatureConfig(ClientFeatureConfig config) {

        if (StringUtils.isBlank(config.getConfigName()) ||
                StringUtils.isBlank(config.getClientType())) {
            return;
        }
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        String update = "UPDATE ainemo.libra_default_config SET config_value = ?  WHERE config_name=? AND config_type =? AND  client_config_name=?  ";
        PreparedStatement stat = null;
        try {
            if ("common".equals(config.getModelName()))
                update = update.replace("client_config_name=?", "(client_config_name='common' or client_config_name='' or client_config_name is null)");
            stat = connection.prepareStatement(update);
            stat.setString(1, config.getValue());
            stat.setString(2, config.getConfigName());
            stat.setString(3, config.getClientType());
            if (!"common".equals(config.getModelName())) stat.setString(4, config.getModelName());

            logger.info(stat.toString());
            stat.executeUpdate();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            close(connection, stat, null);
        }
    }

    public void addOrEditClientFeatureConfig(ClientFeatureConfig config) {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        String select = "SELECT id FROM  ainemo.libra_default_config  WHERE config_name=? AND config_type =? AND  client_config_name=?";
        PreparedStatement stat = null;
        ResultSet resultSet;
        try {
            if ("common".equals(config.getModelName()))
                select = select.replace("client_config_name=?", "(client_config_name='common' or client_config_name='' or client_config_name is null)");
            stat = connection.prepareStatement(select);
            stat.setString(1, config.getConfigName());
            stat.setString(2, config.getClientType());
            if (!"common".equals(config.getModelName())) stat.setString(3, config.getModelName());

            logger.info(stat.toString());
            resultSet = stat.executeQuery();
            Set<String> configNames = new HashSet<>();
            String id = null;
            while (resultSet.next()) {
                id = resultSet.getString("id");
            }
            if (StringUtils.isNotBlank(id)) {
                editClientFeatureConfig(config);
            } else {
                String insertSql = "INSERT INTO ainemo.libra_default_config ( config_name, config_value, config_type, product_family, client_config_name, base_config_type)" +
                        "VALUES " +
                        " ( ?," +
                        " ?, " +
                        " ?, " +
                        " null, " +
                        " ?, " +
                        " ? )";

                stat = connection.prepareStatement(insertSql);
                stat.setString(1, config.getConfigName());
                stat.setString(2, config.getValue());
                stat.setString(3, config.getClientType());
                stat.setString(4, "common".equals(config.getModelName()) ? null : config.getModelName());
                stat.setString(5, StringUtils.isEmpty(config.getBaseConfigType()) ? "0" : config.getBaseConfigType());
                logger.info(stat.toString());
                stat.executeUpdate();
            }
        } catch (Exception e) {
            logger.error("", e);
        } finally {
            close(connection, stat, null);
        }
    }

    /**
     * 查询所有终端类型 subtype
     */
    public Map<String, String> clientSubType() {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");

        String queryAllSubTypes = "select sub_type, category from ainemo.libra_device_subtype_model";
        PreparedStatement stat = null;
        Map<String, String> map = new HashMap<>();
        try {
            Statement statement = connection.createStatement();

            ResultSet resultSet = statement.executeQuery(queryAllSubTypes);
            while (resultSet.next()) {
                map.put(resultSet.getString("sub_type"), resultSet.getString("category"));
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            close(connection, stat, null);
        }
        return map;

    }


    public void configureIppbxSigGatewaySN(String sn) {
        if (StringUtils.isBlank(sn)) return;
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        PreparedStatement stat = null;
        ResultSet resultSet;
        try {
            String query = "SELECT id FROM ainemo.libra_gw_manager WHERE sn=?  AND   gw_type='PSTNGWM'";

            String insert = "INSERT INTO ainemo.libra_gw_manager (id, sn, number, sk, created_timestamp, gw_type, max_in_count, expired_timestamp, pwd, enterprise_id) " +
                    "VALUES " +
                    " ( ?,  ?, ?, NULL, ?, 'PSTNGWM', 10, 1901203200000,  '', 'default_enterprise'  )";
            //校验配置是否存在
            stat = connection.prepareStatement(query);
            stat.setString(1, sn);
            resultSet = stat.executeQuery();
            String id = null;
            while (resultSet.next()) {
                id = resultSet.getString("id");
            }

            if (StringUtils.isBlank(id)) {
                stat = connection.prepareStatement(insert);
                stat.setString(1, UUID.randomUUID().toString().replace("-", "").toLowerCase());
                stat.setString(2, sn);
                stat.setString(3, getGwNumber(connection));
                stat.setLong(4, new Date().getTime());
                logger.info(stat.toString());
                stat.executeUpdate();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            close(connection, stat, null);
        }

    }

    public void configureSipplusGatewaySN(String sn) {
        if (StringUtils.isBlank(sn)) return;
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        PreparedStatement stat = null;
        ResultSet resultSet = null;
        try {
            String query = "SELECT id FROM ainemo.libra_gw_device WHERE sn=?  AND   gw_type='H323'";

            String insert = " INSERT INTO ainemo.libra_gw_device (id, sn, number, sk, created_timestamp, gw_type, max_in_count, expired_timestamp, pwd, enterprise_id, is_native) " +
                    "VALUES " +
                    " ( ?, ?, ?, null, ?, 'H323', 8, 1901203200000, '', 'default_enterprise', 0 )";
            //校验配置是否存在
            stat = connection.prepareStatement(query);
            stat.setString(1, sn);
            resultSet = stat.executeQuery();
            String id = null;
            while (resultSet.next()) {
                id = resultSet.getString("id");
            }

            if (StringUtils.isBlank(id)) {
                stat = connection.prepareStatement(insert);
                stat.setString(1, sn.replace("-", "").toLowerCase());
                stat.setString(2, sn);
                stat.setString(3, getGwNumber(connection));
                stat.setLong(4, new Date().getTime());
                logger.info(stat.toString());
                stat.executeUpdate();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            close(connection, stat, null);
        }
    }


    private String getGwNumber(Connection connection) {
        String query = "SELECT MAX( a.number ) AS max FROM (" +
                " SELECT number AS number FROM ainemo.libra_gw_device WHERE number LIKE '62%0' " +
                " UNION " +
                " SELECT number FROM ainemo.libra_gw_manager WHERE number LIKE '62%0' ) AS a";

        Statement statement;
        ResultSet resultSet;
        int nu = 62000000;
        try {
            statement = connection.createStatement();
            resultSet = statement.executeQuery(query);

            String number = null;

            while (resultSet.next()) {
                number = resultSet.getString("max");
            }
            if (StringUtils.isNotBlank(number)) {
                nu = Integer.parseInt(number) + 10;
            }
        } catch (SQLException e) {
            logger.error("fail to get gw number!", e);
        }
        return nu + "";
    }


    public String getSdkUserNum() {

        String query = "SELECT count(*) as num from ainemo.libra_user_profile where user_flag='sdk'";

        Statement statement = null;
        ResultSet resultSet = null;
        String number = null;
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        try {
            statement = connection.createStatement();
            resultSet = statement.executeQuery(query);
            while (resultSet.next()) {
                number = resultSet.getString("num");
            }
        } catch (SQLException e) {
            logger.error("fail to get sdk number!", e);
        } finally {
            close(connection, statement, resultSet);
        }
        return number;
    }

    public void configureDefaultServerConfig(List<String> datas) {

        if (datas == null || datas.size() == 0) return;

        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        String deleteConfig = "DELETE  FROM ainemo.default_server_config ";
        String insert = "INSERT INTO ainemo.default_server_config (config_name, config_value, value_type) VALUES (?, ?, ?) ";

        PreparedStatement stat = null;
        try {
            connection.setAutoCommit(false);
            stat = connection.prepareStatement(deleteConfig);
            stat.executeUpdate();

            stat = connection.prepareStatement(insert);
            String[] params;
            for (int i = 0; i < datas.size(); i++) {
                if (i == 0) continue; //header

                params = datas.get(i).split("\\|");
                stat.setString(1, params[1]);
                stat.setString(2, params[2]);
                stat.setInt(3, Integer.parseInt(params[3]));
                stat.addBatch();

            }
            stat.executeBatch();
            connection.commit();
        } catch (Exception e) {
            try {
                connection.rollback();
            } catch (SQLException e1) {
                logger.error("error:", e);
            }
            logger.error("error:", e);
        } finally {
            close(connection, stat, null);
        }
    }

    public void updateErrorCodeInfo(String configValue){
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        String update = "UPDATE ainemo.default_server_config set config_value = ? WHERE config_name = 'errorcode'";

        PreparedStatement stat = null;
        try {
            stat = connection.prepareStatement(update);
            stat.setString(1, configValue);
            stat.execute();
        } catch (Exception e) {
            try {
                connection.rollback();
            } catch (SQLException sqlException) {
                logger.error("mysql rollback error", sqlException);
            }
            throw new ServerException("update errorcode info failed" + e.getMessage());
        } finally {
            close(connection, stat, null);
        }
    }

    public void updateI18nInfo(String configValue){
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        String update = "UPDATE ainemo.default_server_config set config_value = ? WHERE config_name = 'i18n'";

        PreparedStatement stat = null;
        try {
            stat = connection.prepareStatement(update);
            stat.setString(1, configValue);
            stat.execute();
        } catch (Exception e) {
            try {
                connection.rollback();
            } catch (SQLException sqlException) {
                logger.error("mysql rollback error", sqlException);
            }
            throw new ServerException("update errorcode info failed" + e.getMessage());
        } finally {
            close(connection, stat, null);
        }
    }

    public String queryErrorCodeInfo(){
        Connection connection  = getConnectionV2(Labels.mysql.label(), "ainemo");
        String selectResult = "SELECT config_value as data FROM ainemo.default_server_config WHERE config_name = 'errorcode'";
        PreparedStatement ps = null;
        String result = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(selectResult);
            rs = ps.executeQuery();
            while (rs.next()) {
                result = rs.getString("data");
            }
        } catch (Exception e) {
            logger.error("select errorcode info failed，error",e);
            throw new ServiceErrorException(ErrorStatus.UNEXPECTED_ERROR);
        } finally {
            close(connection, ps, rs);
        }
        return result;
    }

    public String queryI18nInfo(){
        Connection connection  = getConnectionV2(Labels.mysql.label(), "ainemo");
        String selectResult = "SELECT config_value as data FROM ainemo.default_server_config WHERE config_name = 'i18n'";
        PreparedStatement ps = null;
        String result = null;
        ResultSet rs = null;
        try {
            ps = connection.prepareStatement(selectResult);
            rs = ps.executeQuery();
            while (rs.next()) {
                result = rs.getString("data");
            }
        } catch (Exception e) {
            logger.error("select errorcode info failed，error",e);
            throw new ServiceErrorException(ErrorStatus.UNEXPECTED_ERROR);
        } finally {
            close(connection, ps, rs);
        }
        return result;
    }

    //监控融合服务器实时呼叫并发
    public int getShuttleConcurrency(){
        Connection connection = getConnectionV2(Labels.mysql.label(), "charge");
        String select = "select count(*) as num from charge.libra_enterprise_ipc_meeting where enterprise_id='default_enterprise'";
        PreparedStatement ps = null;
        ResultSet rs = null;
        int num = 0;
        try {
            ps = connection.prepareStatement(select);
            rs = ps.executeQuery();
            while (rs.next()){
                num = rs.getInt("num");
            }
        } catch (SQLException e) {
            logger.error("select shuttle concurrency failed",e);
        } finally {
            close(connection,ps,rs);
        }
        return num;
    }

    public String getExtId() {
        String query = "select ext_id from ainemo.sdk_enterprise_token where enterprise_id='default_enterprise' limit 1 ";
        Statement statement = null;
        ResultSet resultSet = null;
        String extId = null;
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        try {
            statement = connection.createStatement();
            resultSet = statement.executeQuery(query);
            while (resultSet.next()) {
                extId = resultSet.getString("ext_id");
            }
        } catch (SQLException e) {
            logger.error("fail to get sdk number!", e);
        } finally {
            close(connection, statement, resultSet);
        }
        return extId;
    }

    public void editThreeCourse(short threeCourses) {
        String updateSQL = "update education.edu_manage_enterprise set three_courses = ?";

        PreparedStatement stat = null;
        Connection connection = getConnection(Labels.edu.label());
        try {
            connection.setAutoCommit(false);

            stat = connection.prepareStatement(updateSQL);
            stat.setShort(1, threeCourses);
            logger.info(stat.toString());
            stat.executeUpdate();

            connection.commit();
        } catch (SQLException e) {
            try {
                connection.rollback();
            } catch (SQLException e1) {
                logger.error("editThreeCourse error:", e);
            }
            logger.error("editThreeCourse error", e);
        } finally {
            close(connection, stat, null);
        }
    }

    public void editEduModelId(long modelId) {
        String updateSQL1 = "update education.edu_manage_enterprise set model_id = ?";
        String updateSQL2 = "update education.edu_resource_organization set platform_direction = ?";

        PreparedStatement stat = null;
        Connection connection = getConnection(Labels.edu.label());
        try {
            connection.setAutoCommit(false);

            stat = connection.prepareStatement(updateSQL1);
            stat.setLong(1, modelId);
            logger.info(stat.toString());
            stat.executeUpdate();

            stat = connection.prepareStatement(updateSQL2);
            stat.setShort(1, modelId == EduConstants.EDU_JY_MODEL_ID ? (short) 0 : (short) 1);
            logger.info(stat.toString());
            stat.executeUpdate();

            connection.commit();
        } catch (SQLException e) {
            try {
                connection.rollback();
            } catch (SQLException e1) {
                logger.error("error:", e);
            }
            logger.error("editEduModelId error", e);
        } finally {
            close(connection, stat, null);
        }
    }

    /**
     * 编辑保存edu节点配置时，触发域名更新&文件系统域名配置
     */
    public void asyncConfigureEduDomain(String mainInternalIp, String edu, String eduNgPort, String fm, String fmNgport, String protocol) {
        pool.execute(() -> configureEduDomain(mainInternalIp, edu, eduNgPort, fm, fmNgport, protocol));
    }

    private void configureEduDomain(String mainInternalIp, String edu, String eduNgPort, String fm, String fmNgport, String protocol) {
        String updateSql1 = "update education.edu_manage_enterprise set host = ?";
        String updateSql2 = "update education.edu_resource_organization_host set host = ?";

        String updateSql3 = "update education.edu_resource_organization set host = ?, resource_host = ?";
        String updateSql4 = "update filemanage.file_manage_upload_account set view_host = ?";
        String updateSql5 = "update filemanage.repository set url_host = ?, internal_endpoint = ?";

        PreparedStatement stat = null;
        Connection connection;
        try {
            connection = getConnection(Labels.edu.label());
        } catch (ServiceErrorException se) {
            logger.error("获取edu数据库连接失败，数据库可能未部署!");
            return;
        }
        try {
            connection.setAutoCommit(false);

            stat = connection.prepareStatement(updateSql1);
            stat.setString(1, protocol + "://" + edu + (("443".equals(eduNgPort) || "80".equals(eduNgPort)) ? "" : (":" + eduNgPort)));

            logger.info(stat.toString());
            stat.executeUpdate();

            stat = connection.prepareStatement(updateSql2);
            stat.setString(1, protocol + "://" + edu + (("443".equals(eduNgPort) || "80".equals(eduNgPort)) ? "" : (":" + eduNgPort)));
            logger.info(stat.toString());
            stat.executeUpdate();

            stat = connection.prepareStatement(updateSql3);
            stat.setString(1, edu + (("443".equals(eduNgPort) || "80".equals(eduNgPort)) ? "" : (":" + eduNgPort)));
            stat.setString(2, protocol + "://" + fm + (("443".equals(fmNgport) || "80".equals(fmNgport)) ? "" : (":" + fmNgport)));
            logger.info(stat.toString());
            stat.executeUpdate();

            stat = connection.prepareStatement(updateSql4);
            stat.setString(1, protocol + "://" + fm + (("443".equals(fmNgport) || "80".equals(fmNgport)) ? "" : (":" + fmNgport)));
            logger.info(stat.toString());
            stat.executeUpdate();

            stat = connection.prepareStatement(updateSql5);
            stat.setString(1, protocol + "://" + fm + (("443".equals(fmNgport) || "80".equals(fmNgport)) ? "" : (":" + fmNgport)));
            stat.setString(2, "http://" + mainInternalIp + ":11111");
            logger.info(stat.toString());
            stat.executeUpdate();

            connection.commit();
        } catch (Exception e) {
            try {
                connection.rollback();
            } catch (SQLException e1) {
                logger.error("error:", e);
            }
            logger.error("configureEduDomain error", e);
        } finally {
            close(connection, stat, null);
        }
    }

    /**
     * client_edu_entry：客户端培训平台开关入口 0-关闭 1-开启
     */
    public EduManageEnterprise getEduManageEnterprise() {
        String sql = "select * from education.edu_manage_enterprise limit 1;";
        Statement statement = null;
        ResultSet resultSet = null;
        Connection connection = null;
        EduManageEnterprise result = null;
        try {
            connection = getConnection(Labels.edu.label());
            statement = connection.createStatement();
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                result = new EduManageEnterprise();
                result.setId(resultSet.getLong("id"));
                result.setModelId(resultSet.getLong("model_id"));
                result.setClientEduEntry(resultSet.getShort("client_edu_entry"));
                result.setEnterpriseId(resultSet.getString("enterprise_id"));
                result.setOpenResource(resultSet.getShort("open_resource"));
                result.setPlatformId(resultSet.getObject("platform_id") == null ? null : resultSet.getInt("platform_id"));
                result.setHost(resultSet.getString("host"));
                result.setThreeCourses(resultSet.getShort("three_courses"));
            }
        } catch (SQLException e) {
            logger.error("getTrainEntryStatus error", e);
        } finally {
            close(connection, statement, resultSet);
        }
        return result;
    }

    /**
     * 查询所有的课堂配置项
     */
    public List<CurriculumConfigInfo> getEduCurriculumConfigs(){
        String query = "select distinct config_key from education.edu_manage_config";

        PreparedStatement ps = null;
        Connection connection = getConnection(Labels.edu.label());
        ResultSet rs = null;
        List<CurriculumConfigInfo> result = new ArrayList<>();
        try{
            ps = connection.prepareStatement(query);
            rs = ps.executeQuery();
            while(rs.next()){
                CurriculumConfigInfo curriculumConfigInfo = new CurriculumConfigInfo();
                curriculumConfigInfo.setConfigKey(rs.getString("config_key"));
                result.add(curriculumConfigInfo);
            }
        } catch (SQLException e) {
            logger.error("getEduCurriculumConfigs error",e);
        } finally {
            close(connection, ps, rs);
        }
        return result;
    }

    /**
     *查询对应课堂配置项的信息
     */
    public List<CurriculumConfigInfo> getEduCurriculumConfig(String param){
        String query = "select distinct config_value from education.edu_manage_config where config_key = ?";

        PreparedStatement ps = null;
        Connection connection = getConnection(Labels.edu.label());
        ResultSet rs = null;
        List<CurriculumConfigInfo> result = new ArrayList<>();
        try{
            ps = connection.prepareStatement(query);
            ps.setString(1,param);
            rs = ps.executeQuery();
            while(rs.next()){
                CurriculumConfigInfo curriculumConfigInfo = new CurriculumConfigInfo();
                curriculumConfigInfo.setConfigKey(param);
                curriculumConfigInfo.setConfigValue(rs.getString("config_value"));
                result.add(curriculumConfigInfo);
            }
        } catch (SQLException e) {
            logger.error("getEduCurriculumConfig error",e);
        } finally {
            close(connection, ps, rs);
        }
        return result;
    }

    public void updateEduCurriculumConfig(CurriculumConfigInfo param){
        String update = "update education.edu_manage_config set config_value = ?,update_time = now() where config_key = ?";

        PreparedStatement ps = null;
        Connection connection = getConnection(Labels.edu.label());
        try{
            ps = connection.prepareStatement(update);
            ps.setString(1,param.getConfigValue());
            ps.setString(2,param.getConfigKey());
            ps.executeUpdate();
        } catch (SQLException e) {
            logger.error("updateEduCurriculumConfig failed",e);
        } finally {
            close(connection, ps, null);
        }
    }

    public List<Map<String, Object>> getUHD4List() {
        String query = "select lrc.id, lrc.device_sn,lud.device_category ,lud.device_display_name, lrc.create_time  " +
                "from ainemo.libra_recharge_config lrc left join ainemo.libra_user_device lud on lrc.device_sn=lud.device_sn " +
                "where lrc.config_name ='show4kResolution' order by lrc.create_time desc ";
        Statement statement;
        ResultSet resultSet;
        Connection connection = getConnectionV2("", "ainemo");
        List<Map<String, Object>> list = new ArrayList<>();
        try {
            statement = connection.createStatement();
            resultSet = statement.executeQuery(query);
            while (resultSet.next()) {
                Map<String, Object> map = new HashMap<>();
                map.put("deviceSn", resultSet.getString("device_sn"));
                map.put("deviceCategory", resultSet.getString("device_category"));
                map.put("displayName", resultSet.getString("device_display_name"));
                map.put("createTime", resultSet.getString("create_time"));
                list.add(map);
            }
            return list;
        } catch (SQLException e) {
            logger.error("fail to get uhd4 list", e);
        } finally {
            close(connection, null, null);
        }
        return null;
    }

    /**
     * 查询4K终端添加数量
     */
    public String getUHD4Number() {
        String query = "select count(DISTINCT device_sn) as num from ainemo.libra_recharge_config ";

        Statement statement;
        ResultSet resultSet;
        String number = null;
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        try {
            statement = connection.createStatement();
            resultSet = statement.executeQuery(query);
            while (resultSet.next()) {
                number = resultSet.getString("num");
            }
        } catch (SQLException e) {
            logger.error("fail to get uhd4 number", e);
        }
        return number;
    }

    public void addUHD4(String[] deviceSnAry, Long expireTime) {
        if (ArrayUtils.isEmpty(deviceSnAry)) {
            return;
        }
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");

        String deleteConfig = "DELETE FROM ainemo.libra_recharge_config where device_sn = ? ";
        String insert1 = "INSERT INTO ainemo.libra_recharge_config (device_sn, config_name, config_value,config_type,client_config_name,config_expire_time,create_time,update_time) " +
                "VALUES (?, 'show4kResolution', 'true', 8,'UIDisplayCustomization',?,now(),now()) ";
        String insert2 = "INSERT INTO ainemo.libra_recharge_config (device_sn, config_name, config_value,config_type,client_config_name,config_expire_time,create_time,update_time) " +
                "VALUES (?, 'enable4kResolution', 'true', 8,'common',?,now(),now()) ";

        PreparedStatement stat = null;
        try {
            connection.setAutoCommit(false);
            for (int i = 0; i < deviceSnAry.length; i++) {
                stat = connection.prepareStatement(deleteConfig);
                stat.setString(1, deviceSnAry[i]);
                stat.executeUpdate();

                stat = connection.prepareStatement(insert1);
                stat.setString(1, deviceSnAry[i]);
                stat.setLong(2, expireTime);
                stat.executeUpdate();

                stat = connection.prepareStatement(insert2);
                stat.setString(1, deviceSnAry[i]);
                stat.setLong(2, expireTime);
                stat.executeUpdate();
            }
            connection.commit();
        } catch (Exception e) {
            try {
                connection.rollback();
            } catch (SQLException e1) {
                logger.error("addUHD4 error:", e);
            }
            logger.error("addUHD4 error:", e);
        } finally {
            close(connection, stat, null);
        }
    }

    public List<DmcuDto> listDmcuConfig() {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");

        String query = "SELECT ip, site_code FROM ainemo.libra_ip_sitecodes";
        PreparedStatement stat = null;
        List<DmcuDto> list = new ArrayList<>();
        try {
            Statement statement = connection.createStatement();
            ResultSet resultSet = statement.executeQuery(query);
            while (resultSet.next()) {
                DmcuDto dmcuDto = new DmcuDto();
                dmcuDto.setIp(resultSet.getString("ip"));
                dmcuDto.setSiteCode(resultSet.getString("site_code"));
                list.add(dmcuDto);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            close(connection, stat, null);
        }
        return list;
    }

    public void deleteDmcuConfig(DmcuDto dmcuDto) {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        String delete = "delete from ainemo.libra_ip_sitecodes WHERE ip=? AND site_code =?";
        PreparedStatement stat = null;
        try {
            stat = connection.prepareStatement(delete);
            stat.setString(1, dmcuDto.getIp());
            stat.setString(2, dmcuDto.getSiteCode());
            logger.info(stat.toString());
            stat.executeUpdate();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            close(connection, stat, null);
        }
    }

    public void addDmcuConfig(DmcuDto dmcuDto) {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        PreparedStatement stat = null;
        try {
            String insert = " INSERT INTO ainemo.libra_ip_sitecodes (ip, site_code) VALUES ( ?, ?) ";

            stat = connection.prepareStatement(insert);
            stat.setString(1, dmcuDto.getIp());
            stat.setString(2, dmcuDto.getSiteCode());
            logger.info(stat.toString());
            stat.executeUpdate();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            close(connection, stat, null);
        }
    }

    public void updateDmcuConfig(DmcuDto dmcuDto) {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        PreparedStatement stat = null;
        try {
            String insert = " update ainemo.libra_ip_sitecodes set ip = ?, site_code = ? where ip = ? and site_code = ? ";

            stat = connection.prepareStatement(insert);
            stat.setString(1, dmcuDto.getIp());
            stat.setString(2, dmcuDto.getSiteCode());
            stat.setString(3, dmcuDto.getOldIp());
            stat.setString(4, dmcuDto.getOldSiteCode());
            logger.info(stat.toString());
            stat.executeUpdate();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            close(connection, stat, null);
        }
    }

    public List<DmcuDto> pageDmcuConfig(String key, long start, long limit) {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        String query = "SELECT ip, site_code FROM ainemo.libra_ip_sitecodes WHERE 1=1";

        if (StringUtils.isNotBlank(key)){
            query += " AND (ip like ? or site_code like ?)";
        }
        query += " limit " + SqlUtil.getLimitSql(start, limit);
        PreparedStatement stat = null;
        List<DmcuDto> list = new ArrayList<>();
        try {
            stat = connection.prepareStatement(query);
            if (StringUtils.isNotBlank(key)) {
                String likeKey = "%" + key + "%";
                stat.setString(1, likeKey);
                stat.setString(2, likeKey);
            }
            ResultSet resultSet = stat.executeQuery();
            while (resultSet.next()) {
                DmcuDto dmcuDto = new DmcuDto();
                dmcuDto.setIp(resultSet.getString("ip"));
                dmcuDto.setSiteCode(resultSet.getString("site_code"));
                list.add(dmcuDto);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceErrorException(ErrorStatus.DB_OPERATION_ERROR);
        } finally {
            close(connection, stat, null);
        }
        return list;
    }

    public long countDmcuConfig(String key) {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        String query = "SELECT count(*) FROM ainemo.libra_ip_sitecodes WHERE 1=1";
        if (StringUtils.isNotBlank(key)) {
            query += " AND (ip like ? or site_code like ?)";
        }
        PreparedStatement stat = null;
        try {
            stat = connection.prepareStatement(query);
            if (StringUtils.isNotBlank(key)) {
                String likeKey = "%" + key + "%";
                stat.setString(1, likeKey);
                stat.setString(2, likeKey);
            }

            ResultSet resultSet = stat.executeQuery();
            if (resultSet.next()) {
                return resultSet.getLong(1);
            }

        } catch (SQLException e) {
            throw new RuntimeException(e);
        } finally {
            close(connection, stat, null);
        }
        return 0L;
    }

    /**
     * 添加dmcu ip绑定规则
     * @param dto
     */
    public void addDmcuAllocationRule(DmcuIpAllocationRuleDto dto) {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        String insertSql = "INSERT INTO ainemo.libra_sitecode_ip_rule (id, ip, rule, create_time, update_time) VALUES (?, ?, ?, ?, ?) ";
        PreparedStatement stat = null;
        try {
            stat = connection.prepareStatement(insertSql);
            String uuid = UUID.randomUUID().toString().replace("-", "").toLowerCase();

            stat.setString(1, uuid);
            stat.setString(2, dto.getSourceIp());
            stat.setString(3, dto.getAllocationRule());
            stat.setLong(4, System.currentTimeMillis());
            stat.setLong(5, System.currentTimeMillis());
            logger.info("addDmcuAllocationRule sql:{}", stat);
            stat.executeUpdate();
        } catch (Exception e) {
            logger.error("failed addDmcuAllocationRule", e);
            throw new ServiceErrorException(ErrorStatus.DB_OPERATION_ERROR);
        } finally {
            close(connection, stat, null);
        }
    }

    /**
     * 更新dmcu ip绑定规则
     * @param dto
     */
    public void updateDmcuAllocationRuleByIp(DmcuIpAllocationRuleDto dto) {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        String updateSql = "update ainemo.libra_sitecode_ip_rule set rule = ?, update_time = ? where ip = ? ";
        PreparedStatement stat = null;
        try {
            stat = connection.prepareStatement(updateSql);
            long now = System.currentTimeMillis() / 1000;
            stat.setString(1, dto.getAllocationRule());
            stat.setLong(2, now);
            stat.setString(3, dto.getSourceIp());
            logger.info("updateDmcuAllocationRuleByIp sql:{}",stat);
            stat.executeUpdate();
        } catch (Exception e) {
            logger.error("failed updateDmcuAllocationRuleByIp", e);
            throw new ServiceErrorException(ErrorStatus.DB_OPERATION_ERROR);
        } finally {
            close(connection, stat, null);
        }
    }

    public void deleteDmcuAllocationRuleByIp(String sourceIp) {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        String deleteSql = "delete from ainemo.libra_sitecode_ip_rule where ip = ? ";
        PreparedStatement stat = null;
        try {
            stat = connection.prepareStatement(deleteSql);
            stat.setString(1, sourceIp);
            logger.info("deleteDmcuAllocationRuleByIp sql:{}",stat);
            stat.executeUpdate();
        } catch (Exception e) {
            logger.error("failed updateDmcuAllocationRuleByIp", e);
            throw new ServiceErrorException(ErrorStatus.DB_OPERATION_ERROR);
        } finally {
            close(connection, stat, null);
        }
    }

    public List<DmcuIpAllocationRuleDto> getDmcuAllocationRuleList() {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        String querySql = "select ip,rule from ainemo.libra_sitecode_ip_rule";
        List<DmcuIpAllocationRuleDto> res = new ArrayList<>();
        Statement statement = null;
        try {
            statement = connection.createStatement();
            ResultSet resultSet = statement.executeQuery(querySql);
            while (resultSet.next()) {
                DmcuIpAllocationRuleDto temp = new DmcuIpAllocationRuleDto();
                temp.setSourceIp(resultSet.getString("ip"));
                temp.setAllocationRule(resultSet.getString("rule"));
                res.add(temp);
            }
        } catch (Exception e) {
            logger.error("failed getDmcuAllocationRuleList", e);
            throw new ServiceErrorException(ErrorStatus.DB_OPERATION_ERROR);
        } finally {
            close(connection, statement, null);
        }
        return res;
    }

    /**
     * 根据硬终端型号，获取硬终端推送白名单列表
     * @param hardTerminalType
     * @return
     */
    public List<PushWhitelistDto> getWhitelistList(HardTerminalTypeDto hardTerminalType) {
        List<PushWhitelistDto> res = new ArrayList<>();
        Connection connection = getConnectionV2(Labels.mysql.label(),"buffet");
        PreparedStatement ps = null;
        ResultSet resultSet = null;
        String query = "select * from buffet.t_en_app_config where device_subtype = ?";
        try {
            ps = connection.prepareStatement(query);
            ps.setInt(1, hardTerminalType.getSubType());
            resultSet = ps.executeQuery();
            while (resultSet.next()) {
                PushWhitelistDto dto = new PushWhitelistDto();
                dto.setId(resultSet.getInt("id"));
                dto.setSubType(resultSet.getInt("device_subtype"));
                dto.setCategoryDisplay(hardTerminalType.getCategoryDisplay());
                dto.setApplicationName(resultSet.getString("app_name"));
                dto.setApplicationType(resultSet.getInt("app_type"));
                dto.setParameterType(resultSet.getString("app_key"));
                dto.setParameter(resultSet.getString("app_value"));
                res.add(dto);
            }
        } catch (Exception e) {
            logger.error("get hard terminal whitelist failed",e);
            throw new ServiceErrorException(ErrorStatus.UNEXPECTED_ERROR);
        }finally {
            close(connection, ps, resultSet);
        }
        return res;
    }

    public void addWhitelist(PushWhitelistDto whitelistDto) {
        Connection connection = getConnectionV2(Labels.mysql.label(),"buffet");
        PreparedStatement stat = null;
        String insert = "insert into buffet.t_en_app_config(device_subtype,app_name,app_type,app_key,app_value,enterprise_id,update_time) values(?,?,?,?,?,'default_enterprise',NOW())";
        try {
            stat = connection.prepareStatement(insert);
            stat.setInt(1, whitelistDto.getSubType());
            stat.setString(2, whitelistDto.getApplicationName());
            stat.setInt(3, whitelistDto.getApplicationType());
            stat.setString(4,whitelistDto.getParameterType());
            stat.setString(5,whitelistDto.getParameter());
            logger.info(stat.toString());
            stat.executeUpdate();
        } catch (Exception e) {
            logger.error("add hard terminal whitelist failed",e);
            throw new ServiceErrorException(ErrorStatus.UNEXPECTED_ERROR);
        }finally {
            close(connection, stat, null);
        }
    }

    public void deleteWhitelist(String id) {
        Connection connection = getConnectionV2(Labels.mysql.label(),"buffet");
        PreparedStatement stat = null;
        String delete = "delete from buffet.t_en_app_config where id = ?";
        try {
            stat = connection.prepareStatement(delete);
            stat.setInt(1, Integer.parseInt(id));
            logger.info(stat.toString());
            stat.executeUpdate();
        } catch (Exception e) {
            logger.error("delete hard terminal whitelist failed",e);
            throw new ServiceErrorException(ErrorStatus.UNEXPECTED_ERROR);
        }finally {
            close(connection, stat, null);
        }
    }

    public List<Integer> getHardTerminalList() {
        Connection connection = getConnectionV2(Labels.mysql.label(),"buffet");
        PreparedStatement ps = null;
        ResultSet resultSet = null;
        List<Integer> res = new ArrayList<>();
        String query = "select distinct subtype from buffet.t_en_device_custype where handle_type = 'THIRD_APP'";
        try {
            ps = connection.prepareStatement(query);
            resultSet = ps.executeQuery();
            while (resultSet.next()) {
                res.add(resultSet.getInt("subtype"));
            }
        } catch (Exception e) {
            logger.error("get hard terminal whitelist failed",e);
            throw new ServiceErrorException(ErrorStatus.UNEXPECTED_ERROR);
        }finally {
            close(connection, ps, resultSet);
        }
        return res;
    }

    public void updateRecordStorageSite(String uploadSite){
        uploadSite = RecordLoctionDto.getConvertedUploadSite(uploadSite);
        Connection connection = getConnectionV2(Labels.mysql.label(),"charge");
        PreparedStatement stat = null;
        String delete = "delete from charge.libra_self_storage_enterprise where enterprise_id = 'default_enterprise'";
        try {
            connection.setAutoCommit(false);
            stat = connection.prepareStatement(delete);
            stat.executeUpdate();
            if ("LOCAL".equals(uploadSite)) {
                connection.commit();
                return;
            }
            String insert = "INSERT INTO charge.libra_self_storage_enterprise (id,enterprise_id,type) " +
                    "VALUES " +
                    " ('1','default_enterprise' ,?)";
            stat = connection.prepareStatement(insert);
            stat.setString(1, uploadSite);
            logger.info("update record storage site,sql:{}",stat);
            stat.executeUpdate();
            connection.commit();
        } catch (Exception e) {
            try {
                connection.rollback();
            } catch (SQLException ex) {
                logger.error("rollback error,",ex);
            }
            logger.error("update record storage site failed in charge database",e);
            throw new ServiceErrorException(ErrorStatus.DB_OPERATION_ERROR);
        } finally {
            close(con,stat,null);
        }
    }

    private Optional<String> getSelfTestLinkSetting(){
        Map<String, String> openrestyMain = k8sService.getConfigmap(Constants.CONFIGMAP_OPENRESTY_MAIN);
        if (openrestyMain != null) {
            String innerTestLinkAddr = openrestyMain.get("INNER_TEST_LINK_ADDR");
            if (StringUtils.isNotBlank(innerTestLinkAddr)){
                return Optional.of(innerTestLinkAddr);
            }
        }
        return Optional.empty();
    }

    public Connection getBackupAccountConnection(String dbType, String dbIp, String dbPort, String dbName) {
        Map<String, String> allIp = dbConfigUtil.getDbConfigMap();
        Connection connection = null;
        try {
            String username = allIp.get(NetworkConstants.MAIN_DB_BACKUP_USERNAME);
            String password = serverListService.getUsernameOrPwdFromCM(allIp,NetworkConstants.MAIN_DB_BACKUP_PASSWORD);
            DbConfigUtil.DbConfig dbConfig = new DbConfigUtil.DbConfig(dbIp, dbPort, dbType, dbName, allIp);
            String url = dbConfig.jdbcUrl();
            logger.info("url={}", url);
            DriverManager.setLoginTimeout(3);
            connection = DriverManager.getConnection(url, username, password);
        } catch (Exception e) {
            logger.error("Fail to create {} connection !!! msg:{}", dbType, e.getMessage());
        }
        return connection;
    }

    public void updateOceanBasePwd(String ip, String newPassword) {
        Map<String, String> allIp = dbConfigUtil.getDbConfigMap();
        String dbPort = allIp.get(NetworkConstants.DATABASE_PORT);
        Connection connection = null;
        PreparedStatement stat = null;
        try {
            String username = allIp.get(NetworkConstants.MAIN_DB_BACKUP_USERNAME);
            String password = serverListService.getUsernameOrPwdFromCM(allIp,NetworkConstants.MAIN_DB_BACKUP_PASSWORD);
            DbConfigUtil.DbConfig dbConfig = new DbConfigUtil.DbConfig(ip, dbPort, "OB", "", allIp);
            String url = dbConfig.jdbcUrl();
            DriverManager.setLoginTimeout(3);
            connection = DriverManager.getConnection(url, username, password);
            stat = connection.prepareStatement("alter user private_cloud identified by ?");
            stat.setString(1, newPassword);
            stat.execute();
        } catch (Exception e) {
            logger.error("updateOceanBasePwd error",e);
        } finally {
            close(connection, stat,null);
        }
    }


    /**
     * 修改mysql密码
     * @param mysqlIp
     * @param newPassword
     */
    public void updateMysqlPwd(String mysqlIp, String newPassword) {
        Map<String, String> allIp = dbConfigUtil.getDbConfigMap();
        String dbPort = allIp.get(NetworkConstants.DATABASE_PORT);
        Connection connection = null;
        PreparedStatement pstat = null;
        ResultSet resultSet = null;
        try {
            connection = getBackupAccountConnection("MYSQL", mysqlIp, dbPort, "mysql");
            connection.setAutoCommit(false);

            boolean readOnly = false;   //数据库是否是只读模式
            pstat = connection.prepareStatement("show global variables like 'read_only'");
            resultSet = pstat.executeQuery();
            while (resultSet.next()) {
                logger.info("The Mysql read_only = {}", resultSet.getString("Value"));
                if ("ON".equalsIgnoreCase(resultSet.getString("Value"))) {
                    readOnly = true;
                    break;
                }
            }

            //如果 mysql 是只读模式，则先关闭
            if (readOnly) {
                pstat = connection.prepareStatement("set global read_only = 0");
                pstat.execute();
            }

            pstat = connection.prepareStatement("use mysql");
            pstat.execute();

            pstat = connection.prepareStatement("update user set authentication_string=password(?) where user in " +
                    "('private_cloud', 'ainemo', 'buffet', 'charge', 'contact', 'dating', 'dating_job', 'external', 'health'," +
                    "'inspection', 'logserver', 'monitor', 'proprietary', 'task', 'vcs', 'vote', " +
                    "'azkaban', 'datafact', 'datareal', 'dubhe', 'fact', 'hive', 'statis'," +
                    "'education', 'examination', 'filemanage', 'message_push','dcm','manager','n9e_v5')");
            pstat.setString(1, newPassword);
//            pstat.setString(2, targetAccount);
            pstat.executeUpdate();

            pstat = connection.prepareStatement("flush privileges");
            pstat.execute();

            //数据库密码修改完成后，恢复原有只读模式
            if (readOnly) {
                pstat = connection.prepareStatement("set global read_only = 1");
                pstat.execute();
            }

            connection.commit();

        } catch (Exception e) {
            try {
                connection.rollback();
            } catch (SQLException ex) {
                logger.error("rollback error,",ex);
            }
            logger.error("update record storage site failed in charge database",e);
        } finally {
            close(con, pstat,resultSet);
        }
    }

    /**
     * 修改mysql-root密码
     * @param mysqlIp
     * @param newPassword
     */
    public void updateMysqlRootPwd(String mysqlIp, String newPassword) {
        Map<String, String> allIp = dbConfigUtil.getDbConfigMap();
        String dbPort = allIp.get(NetworkConstants.DATABASE_PORT);
        Connection connection = null;
        PreparedStatement pstat = null;
        ResultSet resultSet = null;
        try {
            connection = getBackupAccountConnection("MYSQL", mysqlIp, dbPort, "mysql");
            connection.setAutoCommit(false);

            boolean readOnly = false;   //数据库是否是只读模式
            pstat = connection.prepareStatement("show global variables like 'read_only'");
            resultSet = pstat.executeQuery();
            while (resultSet.next()) {
                logger.info("The Mysql read_only = {}", resultSet.getString("Value"));
                if ("ON".equalsIgnoreCase(resultSet.getString("Value"))) {
                    readOnly = true;
                    break;
                }
            }

            //如果 mysql 是只读模式，则先关闭
            if (readOnly) {
                pstat = connection.prepareStatement("set global read_only = 0");
                pstat.execute();
            }

            pstat = connection.prepareStatement("use mysql");
            pstat.execute();

            pstat = connection.prepareStatement("ALTER USER 'root' IDENTIFIED BY ? ");
            pstat.setString(1, newPassword);
            pstat.executeUpdate();

            String checkUserQuery = "SELECT HOST ,User FROM mysql.user  where HOST ='localhost' and User ='root'";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(checkUserQuery);
            if(rs.next()){
                pstat = connection.prepareStatement("ALTER USER 'root'@'localhost' IDENTIFIED BY ? ");
                pstat.setString(1, newPassword);
                pstat.executeUpdate();
            }

            pstat = connection.prepareStatement("flush privileges");
            pstat.execute();

            //数据库密码修改完成后，恢复原有只读模式
            if (readOnly) {
                pstat = connection.prepareStatement("set global read_only = 1");
                pstat.execute();
            }

            connection.commit();

        } catch (Exception e) {
            try {
                connection.rollback();
            } catch (SQLException ex) {
                logger.error("rollback error,",ex);
            }
            logger.error("update record storage site failed in charge database",e);
        } finally {
            close(con, pstat,resultSet);
        }
    }


    /**
     * 查询数据库配置
     *
     * @param sql
     * @param dbIP
     * @param dbPort
     * @param username
     * @param password
     * @return
     */
    public Map<String, String> getMysqlSlaveStatus(String sql, String dbIP, String dbPort, String username, String password) {
        Connection connection = getConnectionNoDBName(dbIP, dbPort, username, password);
        PreparedStatement ps = null;
        ResultSet resultSet = null;
        Map<String, String> res = new HashMap<>();
        try {
            ps = connection.prepareStatement(sql);
            resultSet = ps.executeQuery();
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            while (resultSet.next()) {
                for (int i = 1; i <= columnCount; i++) {
                    res.put(metaData.getColumnLabel(i), resultSet.getString(i));
                }
            }
        } catch (Exception e) {
            logger.error("Query error.", e);
            throw new ServiceErrorException(ErrorStatus.UNEXPECTED_ERROR);
        } finally {
            close(connection, ps, resultSet);
        }
        return res;
    }

    /**
     * @param sql
     * @param dbIP
     * @param dbPort
     * @param username
     * @param password
     * @return
     */
    public Map<String, String> getMysqlProperties(String sql, String dbIP, String dbPort, String username, String password) {
        Connection connection = getConnectionNoDBName(dbIP, dbPort, username, password);
        PreparedStatement ps = null;
        ResultSet resultSet = null;
        Map<String, String> res = new HashMap<>();
        try {
            ps = connection.prepareStatement(sql);
            resultSet = ps.executeQuery();
            while (resultSet.next()) {
                res.put(resultSet.getString(1), resultSet.getString(2));
            }
        } catch (Exception e) {
            logger.error("Query error.", e);
            throw new ServiceErrorException(ErrorStatus.UNEXPECTED_ERROR);
        } finally {
            close(connection, ps, resultSet);
        }
        return res;
    }

    /**
     * 数据库配置
     *
     * @param sql
     * @param dbIP
     * @param dbPort
     * @param username
     * @param password
     * @return
     */
    public void setMysqlProperties(String sql, String dbIP, String dbPort, String username, String password) {
        Connection connection = getConnectionNoDBName(dbIP, dbPort, username, password);
        PreparedStatement ps = null;
        try {
            String[] sqlArr = sql.split(";");
            for (String executeSql : sqlArr) {
                ps = connection.prepareStatement(executeSql);
                ps.executeUpdate();
            }
        } catch (Exception e) {
            logger.error("Set error.", e);
            throw new ServiceErrorException(ErrorStatus.UNEXPECTED_ERROR);
        } finally {
            close(connection, ps, null);
        }
    }
    private synchronized Connection getConnectionNoDBName(String dbIP, String dbPort, String userName, String pwd) {
        try {
            Map<String, String> allIp = dbConfigUtil.getDbConfigMap();
            if (StringUtils.isNotBlank(dbIP) && StringUtils.isNotBlank(dbPort)) {
                String dbType = allIp.get(NetworkConstants.DATABASE_TYPE);
                DbConfigUtil.DbConfig dbConfig = new DbConfigUtil.DbConfig(getAddress(dbIP, dbIP, dbType), dbPort, dbType, null, allIp);
                String url = dbConfig.jdbcUrl();
                DriverManager.setLoginTimeout(3);
                con = DriverManager.getConnection(url, userName, pwd);
            }
        } catch (SQLException e) {
            logger.error("fail to create mysql connection !!! msg:{}", e.getMessage());
            con = null;
        }
        return con;
    }
    /**
     * @Description 5.2检查sdktoken是否更改
    **/
    public boolean sdkTokenCheck(){
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        String sql = "SELECT * FROM ainemo.sdk_enterprise_token WHERE token='426739735a32d27eb7d5e38cac4b808f3817c2fbc106a9bde695395553510e6a' and enabled =1 and allow_use =1 and enterprise_id = 'default_enterprise'";
        Statement statement = null;
        ResultSet resultSet = null ;
        try{
            statement = connection.createStatement();
            resultSet = statement.executeQuery(sql);
            if(resultSet.next()){
                return false ;
            }
        }catch (Exception e){
            logger.error("fail to get sdk token!");
        }finally {
            close(connection,statement,resultSet);
        }
        return true;
    }

    public ShuttleSnDto getLargestShuttleSn() {
        Connection connection = getConnectionV2(Labels.surv.label(), "surveillance");
        String querySql = "SELECT server_id, sn FROM surveillance.t_surveillance_shuttle ORDER BY id DESC LIMIT 1";
        Statement state =null;
        ResultSet resultSet = null;
        try {
            state= connection.createStatement();
            resultSet = state.executeQuery(querySql);

            if(resultSet.next()){
                ShuttleSnDto shuttleSnDto = new ShuttleSnDto();
                shuttleSnDto.setServerId(resultSet.getString(1));
                shuttleSnDto.setSn(resultSet.getString(2));
                return shuttleSnDto;
            }
        } catch (SQLException e) {
            logger.error("查询 shuttle sn error, msg:{}", e.getMessage());
            throw new ServiceErrorException(ErrorStatus.UNEXPECTED_ERROR);
        } finally {
            close(connection,state,resultSet);
        }
        return new ShuttleSnDto();
    }

    public void insertShuttleSn(String newShuttleSn, String newServerId, String newShuttleName) {
        Connection connection = getConnectionV2(Labels.surv.label(), "surveillance");
        String insertSql = "INSERT INTO surveillance.t_surveillance_shuttle (id, sn, shuttle_name, server_id, create_on, update_on, enterprise_id)" +
                "VALUES (?, ?, ?, ?, ?, ?, 'default_enterprise')";
        PreparedStatement preState = null;
        try {
            preState = connection.prepareStatement(insertSql);
            preState.setString(1, newShuttleSn);
            preState.setString(2, newShuttleSn);
            preState.setString(3, newShuttleName);
            preState.setString(4, newServerId);
            preState.setLong(5, System.currentTimeMillis());
            preState.setLong(6, System.currentTimeMillis());
            preState.executeUpdate();
        } catch (SQLException e) {
            logger.error("创建 shuttle sn error, msg:{}", e.getMessage());
            throw new ServiceErrorException(ErrorStatus.UNEXPECTED_ERROR);
        } finally {
            close(connection,preState, null);
        }
    }

    public void deleteShuttleSn(String shuttleSn) {
        Connection connection = getConnectionV2(Labels.surv.label(), "surveillance");
        String deleteSql = "DELETE FROM surveillance.t_surveillance_shuttle WHERE id = ?";
        PreparedStatement preState = null;
        try {
            preState = connection.prepareStatement(deleteSql);
            preState.setString(1, shuttleSn);
            preState.executeUpdate();
        } catch (SQLException e) {
            logger.error("删除 shuttle sn error, msg:{}", e.getMessage());
            throw new ServiceErrorException(ErrorStatus.UNEXPECTED_ERROR);
        } finally {
            close(connection,preState, null);
        }
    }
    public List<RegionCodeConfig> listRegionCodeList() {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        Statement statement = null;
        ResultSet resultSet = null;
        List<RegionCodeConfig> res = new ArrayList<>();
        try {
            String sql = "SELECT id,country_code,area_code,p_code,area_name,share_resource FROM ainemo.sitecode ";
            statement = connection.createStatement();
            resultSet = statement.executeQuery(sql);
            logger.info(statement.toString());
            while (resultSet.next()) {
                RegionCodeConfig regionCodeConfig = new RegionCodeConfig(resultSet.getInt("id"),
                        resultSet.getString("country_code"),
                        resultSet.getString("area_code"),
                        resultSet.getString("p_code"),
                        resultSet.getString("area_name"),
                        resultSet.getString("share_resource"));
                res.add(regionCodeConfig);
            }
            return res;
        } catch (Exception e) {
            logger.error("listRegionCodeList error:", e);
            throw new ServiceErrorException(ErrorStatus.DB_OPERATION_ERROR);
        } finally {
            close(connection, statement, resultSet);
        }
    }

    public void updateRegionCodeConfig(RegionCodeConfig regionCodeConfig) {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        PreparedStatement statement = null;
        String updateSql = "update ainemo.sitecode SET area_code = ? ,p_code = ?,area_name = ?,share_resource = ? WHERE id = ?";
        try {
            statement = connection.prepareStatement(updateSql);
            statement.setString(1, regionCodeConfig.getAreaCode());
            statement.setString(2, regionCodeConfig.getPcCode());
            statement.setString(3, regionCodeConfig.getAreaName());
            statement.setString(4, regionCodeConfig.getSharedResources());
            statement.setInt(5, regionCodeConfig.getId());
            logger.info(statement.toString());
            statement.executeUpdate();
        } catch (Exception e) {
            logger.error("updateRegionCodeConfig error:", e);
            throw new ServiceErrorException(ErrorStatus.DB_OPERATION_ERROR);
        } finally {
            close(connection, statement, null);
        }
    }

    public void addRegionCodeConfig(RegionCodeConfig regionCodeConfig) {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        PreparedStatement statement = null;
        String insertSql = "INSERT INTO ainemo.sitecode (country_code, area_code, p_code, area_name, share_resource, enterprise_id) VALUES (?,?,?,?,?,'default_enterprise')";
        try {
            statement = connection.prepareStatement(insertSql);
            statement.setString(1, regionCodeConfig.getCountryCode());
            statement.setString(2, regionCodeConfig.getAreaCode());
            statement.setString(3, regionCodeConfig.getPcCode());
            statement.setString(4, regionCodeConfig.getAreaName());
            statement.setString(5, regionCodeConfig.getSharedResources());
            logger.info(statement.toString());
            statement.executeUpdate();
        } catch (Exception e) {
            logger.error("addRegionCodeConfig error:", e);
            throw new ServiceErrorException(ErrorStatus.DB_OPERATION_ERROR);
        } finally {
            close(connection, statement, null);
        }
    }

    public void deleteRegionCodeConfig(String countryCode) {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        PreparedStatement statement = null;
        String deleteSql = "DELETE FROM ainemo.sitecode WHERE country_code = ?  ";
        try {
            statement = connection.prepareStatement(deleteSql);
            statement.setString(1,countryCode);
            statement.executeUpdate();
        } catch (Exception e) {
            logger.error("deleteRegionCodeConfig error:", e);
            throw new ServiceErrorException(ErrorStatus.DB_OPERATION_ERROR);
        } finally {
            close(connection, statement, null);
        }
    }

    public int countRegionCodeList() {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        Statement statement = null;
        ResultSet resultSet = null;
        String countSql = "SELECT COUNT(*) FROM ainemo.sitecode ";
        try {
            statement = connection.createStatement();
            resultSet = statement.executeQuery(countSql);
            if (resultSet.next()) {
                return resultSet.getInt(1);
            }
        } catch (Exception e) {
            logger.error("countRegionCodeList error:", e);
            throw new ServiceErrorException(ErrorStatus.DB_OPERATION_ERROR);
        } finally {
            close(connection, statement, resultSet);
        }
        return 0;
    }

    public RegionCodeConfig getRegionCodeConfigById(Integer id) {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        String sql = "SELECT * FROM ainemo.sitecode  WHERE id = ?";
        RegionCodeConfig regionCodeConfig = null;
        try {
            statement = connection.prepareStatement(sql);
            statement.setInt(1, id);
            resultSet = statement.executeQuery();
            while (resultSet.next()) {
                regionCodeConfig = new RegionCodeConfig(resultSet.getInt("id"),
                        resultSet.getString("country_code"),
                        resultSet.getString("area_code"),
                        resultSet.getString("p_code"),
                        resultSet.getString("area_name"),
                        resultSet.getString("share_resource"));
                return regionCodeConfig;
            }
        } catch (Exception e) {
            logger.error("getRegionCodeConfigById error", e);
            throw new ServiceErrorException(ErrorStatus.DB_OPERATION_ERROR);
        } finally {
            close(connection, statement, resultSet);
        }
        return null;
    }

    public ArrayList<String> getAllAreaCode() {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        String sql = "SELECT country_code FROM ainemo.sitecode";
        ArrayList<String> res = new ArrayList<>();
        try {
            statement = connection.prepareStatement(sql);
            resultSet = statement.executeQuery();
            while (resultSet.next()) {
                res.add(resultSet.getString("country_code"));
            }
        } catch (Exception e) {
            logger.error("getAllAreaCode error", e);
            throw new ServiceErrorException(ErrorStatus.DB_OPERATION_ERROR);
        } finally {
            close(connection, statement, resultSet);
        }
        return res;
    }

    /**
     * 获取dcm数据库连接
     */
    private Connection getStatisConnectionByType(Map<String, String> allIp, boolean isUserNameSecurity, String type) throws SQLException {
        String db = Labels.dcm.label();
        String userName = Labels.dcm.label();
        if (MysqlConstants.manager.equals(type)) {
            db = MysqlConstants.manager;
            userName = MysqlConstants.manager;
        }
        String dbIP = allIp.get(NetworkConstants.STATIS_DATABASE_IP);
        String dbPort = allIp.get(NetworkConstants.STATIS_DATABASE_PORT);
        String pwd = serverListService.getUsernameOrPwdFromCM(allIp,NetworkConstants.STATIS_DB_PASSWORD);
        if (StringUtils.isBlank(dbIP) || StringUtils.isBlank(dbPort)) {
            logger.error("getDcmConnection error, dbIP || dbPort is null");
            return null;
        }
        String dbType = allIp.get(NetworkConstants.DATABASE_TYPE);
        if (("MYSQL".equalsIgnoreCase(dbType) || "OB".equalsIgnoreCase(dbType)) && !isUserNameSecurity) {
            userName = allIp.get(NetworkConstants.MAIN_DB_SUPER_USERNAME);
        }
        DbConfigUtil.DbConfig dbConfig = new DbConfigUtil.DbConfig(dbIP, dbPort, dbType, db, allIp);
        String url = dbConfig.jdbcUrl();
        DriverManager.setLoginTimeout(3);
        return DriverManager.getConnection(url, userName, pwd);
    }

    /**
     * 获取dcm数据库用户名
     */
    public String getStatisUserName(Map<String, String> allIp, boolean isUserNameSecurity, String type) throws SQLException {
        String userName = Labels.dcm.label();
        if (MysqlConstants.manager.equals(type)) {
            userName = MysqlConstants.manager;
        }
        String dbType = allIp.get(NetworkConstants.DATABASE_TYPE);
        if (("MYSQL".equalsIgnoreCase(dbType) || "OB".equalsIgnoreCase(dbType)) && !isUserNameSecurity) {
            userName = allIp.get(NetworkConstants.MAIN_DB_SUPER_USERNAME);
        }
        return userName;
    }

    /**
     * 获取dcm数据库用户名
     */
    public String getStatisPass(Map<String, String> allIp) {
        return serverListService.getUsernameOrPwdFromCM(allIp,NetworkConstants.STATIS_DB_PASSWORD);
    }

    /**
     * 获取dcm数据库连接
     */
    public String getStatisConnectionUrl(Map<String, String> allIp, String type) throws SQLException {
        String db = Labels.dcm.label();
        if (MysqlConstants.manager.equals(type)) {
            db = MysqlConstants.manager;
        }
        String dbIP = allIp.get(NetworkConstants.STATIS_DATABASE_IP);
        String dbPort = allIp.get(NetworkConstants.STATIS_DATABASE_PORT);
        if (StringUtils.isBlank(dbIP) || StringUtils.isBlank(dbPort)) {
            logger.error("getDcmConnection error, dbIP || dbPort is null");
            return null;
        }
        String dbType = allIp.get(NetworkConstants.DATABASE_TYPE);
        DbConfigUtil.DbConfig dbConfig = new DbConfigUtil.DbConfig(dbIP, dbPort, dbType, db, allIp);
        return dbConfig.jdbcUrlWithDbName();
    }

    /**
     * 保存sdk so库上传文件下载地址
     * @param versionId
     * @param downloadUrl
     * @param libraryMd5
     */
    public void saveSDKFileUrl(String versionId, String downloadUrl, String libraryMd5) {

        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");

        String query = "SELECT id FROM ainemo.sdk_client_library_detail where version_id = ?";

        String insert = "INSERT INTO ainemo.sdk_client_library_detail (version_id,download_url,library_md5) VALUES (?,?,?)";

        String update = "UPDATE ainemo.sdk_client_library_detail " +
                "SET download_url = ? , " +
                "library_md5 = ?  " +
                "WHERE id = ?";
        PreparedStatement stat = null;
        try {
            stat = connection.prepareStatement(query);
            stat.setString(1, versionId);
            ResultSet resultSet = stat.executeQuery();
            int id = 0;
            while (resultSet.next()) {
                id = resultSet.getInt("id");
            }

            if (id < 1) {
                stat = connection.prepareStatement(insert);
                stat.setString(1, versionId);
                stat.setString(2, downloadUrl);
                stat.setString(3, libraryMd5);
                logger.info(stat.toString());
                stat.executeUpdate();
            } else {
                stat = connection.prepareStatement(update);
                stat.setString(1, downloadUrl);
                stat.setString(2, libraryMd5);
                stat.setInt(3, id);
                logger.info(stat.toString());
                stat.executeUpdate();
            }
        } catch (Exception e) {
            logger.error("saveSDKFileUrl failed!", e);
            throw new ServiceErrorException(ErrorStatus.DB_OPERATION_ERROR);
        } finally {
            close(connection, stat, null);
        }
    }

    public void deleteSDKFileUrl(String versionId) {
        Connection connection = getConnectionV2(Labels.mysql.label(), "ainemo");

        String delete = "DELETE FROM ainemo.sdk_client_library_detail WHERE version_id = ?";
        PreparedStatement stat = null;
        try {
            stat = connection.prepareStatement(delete);
            stat.setString(1, versionId);
            logger.info(stat.toString());
            stat.executeUpdate();
        } catch (Exception e) {
            logger.error("saveSDKFileUrl failed!", e);
            throw new ServiceErrorException(ErrorStatus.DB_OPERATION_ERROR);
        } finally {
            close(connection, stat, null);
        }
    }

    /**
     * 获取cp服务所在节点的p2p号码池
     * @param cpPodName cp服务所在节点podname
     * @return
     */
    public int getP2PPoolNumber(String cpPodName) {
        Connection connection = getConnectionV2(Labels.mysql.label(), "charge");
        String select = "select count(*) as num from charge.p2p_number_pool  where pod_name = ?;";
        PreparedStatement ps = null;
        ResultSet rs = null;
        int num = 0;
        try {
            ps = connection.prepareStatement(select);
            ps.setString(1, cpPodName);
            logger.info(ps.toString());
            rs = ps.executeQuery();
            while (rs.next()){
                num = rs.getInt("num");
            }
        } catch (SQLException e) {
            logger.error("getP2PPoolNumber failed",e);
        } finally {
            close(connection,ps,rs);
        }
        return num;
    }

    /**
     * 勾选cp时，设置对应节点的p2p号码池
     * @param cpPodName cp服务所在节点podname
     */
    public void updateP2PPoolNumber(String cpPodName) {
        Connection connection = getConnectionV2(Labels.mysql.label(), "charge");
        String update = "UPDATE charge.p2p_number_pool set pod_name = ? where id in (select t.id from (select id from charge.p2p_number_pool where pod_name='' limit 250) as t )";
        PreparedStatement stat = null;
        try {
            stat = connection.prepareStatement(update);
            stat.setString(1, cpPodName);
            logger.info(stat.toString());
            stat.executeUpdate();
        } catch (Exception e) {
            logger.error("updateP2PPoolNumber error", e);
        } finally {
            close(connection, stat, null);
        }
    }

    /**
     * 取消勾选cp时，清空对应节点的p2p号码池
     * @param cpPodName cp服务所在节点podname
     */
    public void clearP2PPoolNumber(String cpPodName) {
        Connection connection = getConnectionV2(Labels.mysql.label(), "charge");
        String update = "UPDATE charge.p2p_number_pool set pod_name='' where pod_name= ?";
        PreparedStatement stat = null;
        try {
            stat = connection.prepareStatement(update);
            stat.setString(1, cpPodName);
            logger.info(stat.toString());
            stat.executeUpdate();
        } catch (Exception e) {
            logger.error("clearP2PPoolNumber error", e);
        } finally {
            close(connection, stat, null);
        }
    }

    public Map<String, String> getDefaultServerConfig(String configNames) {
        Connection connection;
        try {
            connection = getConnection(Labels.mysql.label());
        } catch (Exception ex) {
            logger.error("getDefaultServerConfig error:{}", ex);
            throw new OpsManagerException("获取数据失败");
        }
        if (connection == null) {
            return Collections.emptyMap();
        }
        String selectSql = "select config_name, config_value from ainemo.default_server_config where config_value != ''";

        if (StringUtils.isNotBlank(configNames)) {
            selectSql += " and config_name in (" + configNames + ")";
        }
        Statement statement = null;
        ResultSet resultSet = null;
        Map<String, String> result = new HashMap<>();
        try {
            statement = connection.createStatement();
            resultSet = statement.executeQuery(selectSql);
            while (resultSet.next()) {
                result.put(resultSet.getString(1), resultSet.getString(2));
            }
        } catch (Exception e) {
            logger.error("getDefaultServerConfig error:", e);
            return Collections.emptyMap();
        } finally {
            close(connection, statement, resultSet);
        }
        return result;
    }

    public boolean updateDefaultServerConfig(Map<String, String> configs) {
        if (CollectionUtils.isEmpty(configs)) {
            return true;
        }

        Connection connection;
        try {
            connection = getConnection(Labels.mysql.label());
        } catch (Exception ex) {
            logger.error("configureDefaultServerConfig error:{}", ex.getMessage(), ex);
            throw new OpsManagerException("同步到数据库失败");
        }
        if (connection == null) {
            return false;
        }
        String update = "UPDATE ainemo.default_server_config SET config_value = ? where config_name = ?";

        PreparedStatement stat = null;
        try {
            connection.setAutoCommit(false);

            stat = connection.prepareStatement(update);
            for (Map.Entry<String, String> next : configs.entrySet()) {
                stat.setString(1, next.getValue());
                stat.setString(2, next.getKey());
                stat.addBatch();
            }
            stat.executeBatch();
            connection.commit();
            return true;
        } catch (Exception e) {
            try {
                connection.rollback();
            } catch (SQLException e1) {
                logger.error("error:", e);
            }
            logger.error("error:", e);
            return false;
        } finally {
            close(connection, stat, null);
        }
    }
}