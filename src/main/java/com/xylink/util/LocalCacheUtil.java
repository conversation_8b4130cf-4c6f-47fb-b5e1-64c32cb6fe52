package com.xylink.util;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> create on 2025/6/11
 */
@Slf4j
public class LocalCacheUtil {

    /**
     * 最大缓存1000条（LRU清理）
     * 30s过期
     */
    private static final Cache<String, Object> CACHE = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(10, TimeUnit.SECONDS)
            .build();

    public static void put(String key, Object val) {
        log.info("LocalCache put key:{}",key);
        CACHE.put(key, val);
    }

    /**
     * 删除缓存
     * @param key 缓存键
     */
    public static void remove(String key) {
        log.info("LocalCache remove key:{}",key);
        CACHE.invalidate(key);
    }

    public static Object get(String key) {
        return CACHE.getIfPresent(key);
    }

    /**
     * 清空缓存
     */
    public static void clearAll() {
        CACHE.invalidateAll();
    }

}
