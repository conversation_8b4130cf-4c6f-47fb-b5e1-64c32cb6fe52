package com.xylink.util.activestandby;

import org.yaml.snakeyaml.DumperOptions;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.nodes.Tag;
import org.yaml.snakeyaml.representer.Representer;

import java.io.BufferedWriter;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025-03-12 10:42
 */
public abstract class ScoutsConfigYamlUtils {

    private ScoutsConfigYamlUtils() {

    }

    public static String updateContent(String yamlPath, List<String> members, int heartbeatInterval, int heartbeatTimeout, int heartbeatToTheDeadCount) {
        try {
            InputStream inputStream = Files.newInputStream(Paths.get(yamlPath));
            Yaml yaml = new Yaml();
            Map<String, Object> yamlData = yaml.load(inputStream);
            Map<String, Object> nodeConfig = (Map<String, Object>) yamlData.get("node");
            nodeConfig.put("Members", members);
            if (heartbeatInterval >= 0) {
                nodeConfig.put("HeartbeatInterval", heartbeatInterval);
            }
            if (heartbeatTimeout >= 0) {
                nodeConfig.put("HeartbeatTimeout", heartbeatTimeout);
            }

            if (heartbeatToTheDeadCount >= 0) {
                nodeConfig.put("HeartbeatToTheDeadCount", heartbeatToTheDeadCount);
            }
            DumperOptions options = new DumperOptions();

            options.setDefaultFlowStyle(DumperOptions.FlowStyle.BLOCK);
            Representer representer = new Representer(options);
            representer.addClassTag(yamlData.getClass(), Tag.MAP);
            Yaml outputYaml = new Yaml(representer);
            return outputYaml.dump(yamlData);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static String updateContent(String yamlPath, List<String> members) {
        return updateContent(yamlPath, members, -1, -1, -1);
    }

    public static String currentContent(String yamlPath) {
        try {
            InputStream inputStream = Files.newInputStream(Paths.get(yamlPath));
            DumperOptions options = new DumperOptions();
            options.setDefaultFlowStyle(DumperOptions.FlowStyle.BLOCK);
            Representer representer = new Representer(options);
            Yaml yaml = new Yaml(representer);
            Map<String, Object> yamlData = yaml.load(inputStream);
            representer.addClassTag(yamlData.getClass(), Tag.MAP);
            return yaml.dump(yamlData);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static void updateContentInLocal(String yamlPath, List<String> members, int heartbeatInterval, int heartbeatTimeout, int heartbeatToTheDeadCount) {
        try {
            Map<String, Object> yamlData;
            try (InputStream inputStream = Files.newInputStream(Paths.get(yamlPath))) {
                Yaml yaml = new Yaml();
                yamlData = yaml.load(inputStream);
            }
            Map<String, Object> nodeConfig = (Map<String, Object>) yamlData.get("node");
            nodeConfig.put("Members", members);
            if (heartbeatInterval >= 0) {
                nodeConfig.put("HeartbeatInterval", heartbeatInterval);
            }
            if (heartbeatTimeout >= 0) {
                nodeConfig.put("HeartbeatTimeout", heartbeatTimeout);
            }

            if (heartbeatToTheDeadCount >= 0) {
                nodeConfig.put("HeartbeatToTheDeadCount", heartbeatToTheDeadCount);
            }
            DumperOptions options = new DumperOptions();

            options.setDefaultFlowStyle(DumperOptions.FlowStyle.BLOCK);
            Representer representer = new Representer(options);
            representer.addClassTag(yamlData.getClass(), Tag.MAP);
            Yaml outputYaml = new Yaml(representer);
            try (BufferedWriter output = Files.newBufferedWriter(Paths.get(yamlPath), StandardCharsets.UTF_8)) {
                outputYaml.dump(yamlData, output);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static void updateContentInLocal(String yamlPath, int heartbeatInterval, int heartbeatTimeout, int heartbeatToTheDeadCount) {
        try {
            Map<String, Object> yamlData;
            try (InputStream inputStream = Files.newInputStream(Paths.get(yamlPath))) {
                Yaml yaml = new Yaml();
                yamlData = yaml.load(inputStream);
            }
            Map<String, Object> nodeConfig = (Map<String, Object>) yamlData.get("node");
            if (heartbeatInterval >= 0) {
                nodeConfig.put("HeartbeatInterval", heartbeatInterval);
            }
            if (heartbeatTimeout >= 0) {
                nodeConfig.put("HeartbeatTimeout", heartbeatTimeout);
            }

            if (heartbeatToTheDeadCount >= 0) {
                nodeConfig.put("HeartbeatToTheDeadCount", heartbeatToTheDeadCount);
            }
            DumperOptions options = new DumperOptions();

            options.setDefaultFlowStyle(DumperOptions.FlowStyle.BLOCK);
            Representer representer = new Representer(options);
            representer.addClassTag(yamlData.getClass(), Tag.MAP);
            Yaml outputYaml = new Yaml(representer);
            try (BufferedWriter output = Files.newBufferedWriter(Paths.get(yamlPath), StandardCharsets.UTF_8)) {
                outputYaml.dump(yamlData, output);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

}
