package com.xylink.manager.inspection.dao.impl;

import com.xylink.manager.inspection.dao.InspectionTemplateItemDao;
import com.xylink.manager.inspection.entity.condition.InspectionItemCondition;
import com.xylink.manager.inspection.entity.db.InspectionTemplateItemDb;
import com.xylink.manager.inspection.entity.enums.InspectionItemTypeEnum;
import com.xylink.manager.inspection.mapper.InspectionTemplateItemMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/21 16:37
 */
@Repository("templateItemDao")
@Slf4j

public class InspectionTemplateItemDaoImpl implements InspectionTemplateItemDao {
    @Autowired
    private InspectionTemplateItemMapper templateItemMapper;

    @Override
    public void insert(List<InspectionTemplateItemDb> mapDbs) {
        templateItemMapper.insert(mapDbs);
    }

    @Override
    public InspectionItemCondition getItems(String templateId) {
        List<InspectionTemplateItemDb> mapDbs = templateItemMapper.selectByTemplateId(templateId);
        Map<Integer, List<InspectionTemplateItemDb>> collect = mapDbs.stream().collect(Collectors.groupingBy(InspectionTemplateItemDb::getItemType));
        InspectionItemCondition itemCondition = new InspectionItemCondition();
        List<InspectionTemplateItemDb> system = collect.get(InspectionItemTypeEnum.SYSTEM.getId());
        List<InspectionTemplateItemDb> middleware = collect.get(InspectionItemTypeEnum.MIDDLEWARE.getId());
        List<InspectionTemplateItemDb> server = collect.get(InspectionItemTypeEnum.SERVER.getId());
        if (CollectionUtils.isNotEmpty(system)) {
            itemCondition.setSystem(system.stream().map(InspectionTemplateItemDb::getItemId).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(middleware)) {
            itemCondition.setMiddleware(middleware.stream().map(InspectionTemplateItemDb::getItemId).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(server)) {
            itemCondition.setServer(server.stream().map(InspectionTemplateItemDb::getItemId).collect(Collectors.toList()));
        }
        return itemCondition;
    }

    @Override
    public void deleteByTemplateId(String templateId) {
        templateItemMapper.deleteByTemplateId(templateId);
    }
}
