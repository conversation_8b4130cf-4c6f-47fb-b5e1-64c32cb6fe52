package com.xylink.manager.inspection.dao.impl;

import com.xylink.manager.inspection.dao.InspectionTaskDao;
import com.xylink.manager.inspection.entity.db.InspectionSubTaskDb;
import com.xylink.manager.inspection.entity.db.InspectionTaskDb;
import com.xylink.manager.inspection.mapper.InspectionTaskMapper;
import com.xylink.util.UUIDGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/1 12:02
 */
@Repository("taskDao")
@Slf4j
public class InspectionTaskDaoImpl implements InspectionTaskDao {
    @Autowired
    private InspectionTaskMapper taskMapper;

    @Override
    public InspectionTaskDb createTask(String jobId, int type) {
        InspectionTaskDb taskDb = new InspectionTaskDb();
        taskDb.setInstanceId(jobId);
        taskDb.setId(UUIDGenerator.generate());
        taskDb.setCreateTime(System.currentTimeMillis());
        taskDb.setTaskType(type);
        taskMapper.insertTask(taskDb);
        return taskDb;
    }


    @Override
    public void taskExecFailed(InspectionTaskDb task, long endTime, String exceptionMessage) {
        taskMapper.updateTaskExceptionDesc(task.getId(), endTime, exceptionMessage);
        task.setFinishedTime(endTime);
        task.setExceptionDesc(exceptionMessage);
    }

    @Override
    public void updateTask(InspectionTaskDb task, long endTime, List<InspectionSubTaskDb> subTaskDbs) {
        int highRisk = 0;
        int middleRisk = 0;
        int lowRisk = 0;
        if (CollectionUtils.isNotEmpty(subTaskDbs)) {
            highRisk = subTaskDbs.stream().mapToInt(InspectionSubTaskDb::getHighRisk).sum();
            middleRisk = subTaskDbs.stream().mapToInt(InspectionSubTaskDb::getMiddleRisk).sum();
            lowRisk = subTaskDbs.stream().mapToInt(InspectionSubTaskDb::getLowRisk).sum();
        }
        taskMapper.updateTask(task.getId(), endTime, highRisk, middleRisk, lowRisk);
        task.setHighRisk(highRisk);
        task.setMiddleRisk(middleRisk);
        task.setLowRisk(lowRisk);
        task.setFinishedTime(endTime);
    }

    @Override
    public List<InspectionTaskDb> getTaskList(String jobId) {
        return taskMapper.getTaskByInstanceId(jobId);
    }

    @Override
    public InspectionTaskDb getTaskById(String taskId) {
        return taskMapper.getTaskById(taskId);
    }
}