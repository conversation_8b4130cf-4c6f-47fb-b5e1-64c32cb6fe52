package com.xylink.manager.inspection.dao.impl;

import com.xylink.manager.inspection.dao.InspectionTemplateDao;
import com.xylink.manager.inspection.entity.db.InspectionTemplateDb;
import com.xylink.manager.inspection.entity.vo.InspectionTemplateVo;
import com.xylink.manager.inspection.mapper.InspectionTemplateMapper;
import com.xylink.manager.inspection.utils.PageUtil;
import com.xylink.manager.model.common.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/28 14:18
 */
@Repository("templateDao")
@Slf4j
public class InspectionTemplateDaoImpl implements InspectionTemplateDao {
    @Autowired
    public InspectionTemplateMapper templateMapper;

    @Override
    public Page<InspectionTemplateVo> getTemplateList(int size, int page, Long startTime, Long endTime, Boolean asc) {
        page = page - 1;
        int startRow = PageUtil.getStart(page, size);
        Long totalRow = templateMapper.count(startTime, endTime);
        if (totalRow == null || totalRow == 0) {
            return new Page<>(0, 0, 0L, Collections.emptyList());
        }
        List<InspectionTemplateDb> list = templateMapper.list(size, startRow, startTime, endTime, asc);
        List<InspectionTemplateVo> collect = list.stream().map(this::buildTemplateVo).collect(Collectors.toList());
        return new Page<>(startRow, size, totalRow, collect);
    }

    @Override
    public InspectionTemplateDb selectById(String id) {
        return templateMapper.selectById(id);
    }

    @Override
    public InspectionTemplateDb getTemplateById(String id) {
        return templateMapper.selectById(id);
    }

    private InspectionTemplateVo buildTemplateVo(InspectionTemplateDb template) {
        return InspectionTemplateVo.builder()
                .id(template.getId())
                .createTime(template.getCreateTime())
                .createUser(template.getCreateUser())
                .name(template.getInspectionName())
                .type(template.getInspectType())
                .status(template.getJobStatus())
                .enable(template.getTemplateEnable())
                .build();
    }

    @Override
    public void updateStatus(String templateId, int status) {
        templateMapper.updateStatus(templateId, status);
    }

    @Override
    public String getTemplateIdByName(String name) {
        return templateMapper.selectIdByName(name);
    }

    @Override
    public void update(InspectionTemplateDb template) {
        templateMapper.update(template);
    }

    @Override
    public Long selectStatusById(String id) {
        return templateMapper.selectStatusById(id);
    }

    @Override
    public void updateEnable(String id) {
        templateMapper.updateEnable(id);
    }

    @Override
    public void insert(InspectionTemplateDb template) {
        templateMapper.insert(template);
    }

}
