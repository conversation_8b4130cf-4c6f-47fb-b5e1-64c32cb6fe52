package com.xylink.manager.inspection.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

@Configuration("inspectionRestClientConfiguration")
public class RestClientConfiguration {

    @Value("${inspection.rest.timeout:60000}")
    private int inspectionTimeout;


    /**
     * 专门巡检用的rest客户端
     *
     * @return .
     */
    @Bean("inspectionRestTemplate")
    public RestTemplate inspectionRestTemplate() {
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        factory.setReadTimeout(inspectionTimeout);
        factory.setConnectTimeout(inspectionTimeout);
        return new RestTemplate(factory);
    }
}