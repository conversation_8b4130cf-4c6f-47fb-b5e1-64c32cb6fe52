package com.xylink.manager.inspection.config;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.xylink.config.Constants;
import com.xylink.config.MysqlConstants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.watch.processor.impl.AllIpProcessor;
import com.xylink.util.JDBCUtils;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.mapping.Environment;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.transaction.TransactionFactory;
import org.apache.ibatis.transaction.jdbc.JdbcTransactionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/4
 */
@Service
@Slf4j
public class DataSourceManager {

    @Autowired
    private JDBCUtils jdbcUtils;

    @Autowired
    private K8sService k8sService;

    private SqlSessionFactory sessionFactory;

    private HikariDataSource dataSource;

    private String password;

    public SqlSessionFactory getSqlSessionFactory() {
        if(sessionFactory != null) {
            return sessionFactory;
        }

        try {
            dataSource = getDataSource();
            MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
            bean.setDataSource(dataSource);
            bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/*.xml"));
            MybatisConfiguration configuration = getMybatisConfiguration();
            bean.setConfiguration(configuration);
            sessionFactory = bean.getObject();
            return sessionFactory;
        } catch (Exception e) {
            log.error("get sql session factory error", e);
            throw new RuntimeException(e);
        }
    }

    @Nonnull
    private MybatisConfiguration getMybatisConfiguration() {
        TransactionFactory transactionFactory = new JdbcTransactionFactory();
        Environment environment = new Environment("development", transactionFactory, dataSource);
        MybatisConfiguration configuration = new MybatisConfiguration(environment);
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.addMappers("com.xylink.manager.inspection.mapper");
        configuration.addMappers("com.xylink.manager.mapper");
        configuration.addMappers("com.xylink.manager.iptables.mapper");
        return configuration;
    }

    public HikariDataSource getDataSource() throws SQLException {
        Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        String url;
        url = jdbcUtils.getStatisConnectionUrl(allIp, MysqlConstants.manager);
        //url = url.replace("**********:2883", "************:2881");
        boolean isUserNameSecurity = Boolean.parseBoolean(allIp.get("DB_USERNAME_PERMISSION_SECURITY_SWITCH"));
        String username = jdbcUtils.getStatisUserName(allIp, isUserNameSecurity, MysqlConstants.manager);
        password = jdbcUtils.getStatisPass(allIp);
        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setJdbcUrl(url);
        hikariConfig.setUsername(username);
        hikariConfig.setPassword(password);
        hikariConfig.addDataSourceProperty("cachePrepStmts", "true");
        hikariConfig.addDataSourceProperty("prepStmtCacheSize", "250");
        hikariConfig.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        // 创建HikariCP数据源
        return new HikariDataSource(hikariConfig);
    }

    public void refreshStatisPassword() {
        Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        String newPassword = jdbcUtils.getStatisPass(allIp);
        if(StringUtils.equals(newPassword, password)) {
            return;
        }
        log.info("the statis db password is reset");
        password = newPassword;
        dataSource.setPassword(password);
        dataSource.getHikariPoolMXBean().softEvictConnections();
    }

    private void refreshStatisPassword(Set<String> set) {
        if (!set.contains(NetworkConstants.STATIS_DB_PASSWORD)) {
            return;
        }
        refreshStatisPassword();
    }

    public void refreshHikariPool(List<AllIpProcessor.ChangeData> changeData) {
        Set<String> set = changeData.stream()
                .map(AllIpProcessor.ChangeData::getKey)
                .collect(Collectors.toSet());
        refreshStatisPassword(set);
        refreshIp(set);
    }

    private void refreshIp(Set<String> set) {
        if (!set.contains(NetworkConstants.STATIS_DATABASE_IP)) {
            return;
        }
        try {
            // 替换为新数据源
            this.dataSource = getDataSource();
            dataSource.getHikariPoolMXBean().softEvictConnections();
        } catch (SQLException e) {
            log.error("refresh statis db ip error", e);
        }

    }
}
