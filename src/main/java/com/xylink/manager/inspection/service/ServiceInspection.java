package com.xylink.manager.inspection.service;

import com.xylink.manager.inspection.dao.InspectionServiceResultDao;
import com.xylink.manager.inspection.entity.bo.InspectPodAvailableBo;
import com.xylink.manager.inspection.entity.bo.service.KafkaInspectionAckMsgBO;
import com.xylink.manager.inspection.entity.bo.service.RestInspectionParamBO;
import com.xylink.manager.inspection.entity.bo.service.RestInspectionResultBO;
import com.xylink.manager.inspection.entity.db.InspectionServiceResultDB;
import com.xylink.manager.inspection.entity.enums.InspectionServiceLadder;
import com.xylink.manager.inspection.entity.enums.InspectionServiceType;
import com.xylink.manager.inspection.kafka.KafkaConfig;
import com.xylink.manager.inspection.kafka.KafkaSender;
import com.xylink.manager.inspection.kafka.consumer.KafkaInspectionConsumer;
import com.xylink.manager.inspection.utils.JsonUtils;
import com.xylink.manager.inspection.utils.UUIDUtil;
import com.xylink.util.SpringBeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/12 14:45
 */
@Service
@Slf4j
public class ServiceInspection {

    /**
     * 专门用于发送rest巡检使用
     */
    private ExecutorService restInspectionExecutor;
    /**
     * kafka巡检的ack缓存信息
     */
    private final Map<String, Map<String, KafkaInspectionAckMsgBO>> ackMsgMap = new ConcurrentHashMap<>();
    @Value("${inspection.service.rest.thread.num.min:150}")
    private int restMinThreadNum;
    @Value("${inspection.service.rest.thread.num.max:200}")
    private int restMaxThreadNum;
    @Value("${inspection.service.rest.thread.queue:500}")
    private int restMThreadQueue;
    @Value("${inspection.service.kafka.timeout:60000}")
    private int inspectionKafkaTimeout;

    private volatile boolean isFirstInspection = true;

    private KafkaSender kafkaSender;

    private KafkaConfig kafkaConfig;
    @Autowired
    private InspectionServiceResultDao inspectionServiceResultDao;


    public void init() throws InterruptedException {
        // 获取 kafkaConfig
        kafkaConfig = SpringBeanUtil.getBean("kafkaConfig");
        // 获取 kafka sender
        kafkaSender = KafkaSender.getInstance();

        // 创建 ack 消费者, 等待消费者创建完成
        CountDownLatch countDownLatch = new CountDownLatch(1);
        KafkaInspectionConsumer.createInstance(countDownLatch);
        countDownLatch.await(30, TimeUnit.SECONDS);

        // 初始化 rest 线程池
        restInspectionExecutor = new ThreadPoolExecutor(restMinThreadNum, restMaxThreadNum,
                1L, TimeUnit.HOURS,
                new ArrayBlockingQueue<>(restMThreadQueue),
                r -> {
                    Thread thread = new Thread(r);
                    thread.setName("rest-inspection-thread");
                    return thread;
                });
        isFirstInspection = false;
    }


    public void inspection(String inspectionId, List<InspectPodAvailableBo> params) throws Exception {
        try {
            // 检查是否是第一次运行并初始化服务巡检相关bean
            checkFirstInspection();
            Long inspectStartTs = System.currentTimeMillis();
            // 1.过滤出发kafka的pod，如果有，发送kafka
            List<InspectPodAvailableBo> kafkaPods = params.stream().filter(x -> InspectionServiceType.KAFKA.getValue() == x.getInspectionType()).collect(Collectors.toList());
            log.info("kafka pods:{}", kafkaPods);
            if (CollectionUtils.isNotEmpty(kafkaPods)) {
                //发送kafka的巡检方式
                sendKafkaInspection(kafkaPods, inspectionId, inspectStartTs);
            }
            // 2.过滤出rest模式的pod
            List<InspectPodAvailableBo> restPods = params.stream().filter(x -> InspectionServiceType.REST.getValue() == x.getInspectionType()).collect(Collectors.toList());
            log.info("rest pods:{}", restPods);
            if (CollectionUtils.isNotEmpty(restPods)) {
                handleRestInspection(restPods, inspectionId);
            }
            // 3.处理kafka巡检的响应
            if (CollectionUtils.isNotEmpty(kafkaPods)) {
                // 说明本地没有rest巡检的服务，只有kafka巡检的服务，在这里等待30秒
                handleKafkaInspectionAck(kafkaPods, inspectionId);
            }
        } finally {
            ackMsgMap.remove(inspectionId);
        }
    }

    private synchronized void checkFirstInspection() throws InterruptedException {
        try {
            if (isFirstInspection) {
                log.info("[inspection] init inspectService");
                init();
            }
        } catch (Exception e) {
            log.error("[inspection] init failed", e);
        }
    }

    /**
     * 发送kafka巡检命令
     *
     * @param kafkaPods .
     */
    private void sendKafkaInspection(List<InspectPodAvailableBo> kafkaPods, String inspectionId, Long inspectStartTs) {
        // 检测kafka是否正常，如果不正常，直接不用巡检kafka方式的服务了
        Map<String, Object> msg = new HashMap<>();
        msg.put("inspectionId", inspectionId);
        msg.put("podName", kafkaPods.stream().map(InspectPodAvailableBo::getPodName).collect(Collectors.toList()));
        msg.put("time", inspectStartTs);
        String msgJson = JsonUtils.objectToJson(msg);
        try {
            kafkaSender.sendMessage(kafkaConfig.getInspectionTopic(), null, msgJson);
            Map<String, KafkaInspectionAckMsgBO> map = new ConcurrentHashMap<>(kafkaPods.size());
            KafkaInspectionAckMsgBO emptyBo = new KafkaInspectionAckMsgBO();
            kafkaPods.forEach(pods -> map.put(pods.getPodName(), emptyBo));
            ackMsgMap.put(inspectionId, map);
        } catch (Exception e) {
            log.error("sendKafkaInspection error, pods:{}", kafkaPods, e);
        }
    }

    /**
     * 处理rest巡检请求
     *
     * @param restPods .
     */
    private void handleRestInspection(List<InspectPodAvailableBo> restPods, String inspectionId) {
        // 因为要发送rest请求，并行去做,这里要尽可能的创建线程，因为尽可能的在主线程总共只等60秒
        ExecutorCompletionService<RestInspectionResultBO> completionService = new ExecutorCompletionService<>(restInspectionExecutor);
        restPods.forEach(param -> completionService.submit(new RestInspectionTask(
                new RestInspectionParamBO(inspectionId, param.getServiceName(), param.getPodName(),
                        param.getInspectionUrl(), param.getPreError()))));
        List<RestInspectionResultBO> inspectionResultList = new ArrayList<>(restPods.size());

        for (InspectPodAvailableBo restPod : restPods) {
            try {
                RestInspectionResultBO restInspectionResultBO = completionService.take().get();
                inspectionResultList.add(restInspectionResultBO);
            } catch (InterruptedException | ExecutionException e) {
                log.error("{}, get rest inspection future error", restPod.getServiceName(), e);
            }
        }
        saveRestInspectionResult(inspectionResultList);
    }

    /**
     * 保存rest巡检方式的结果
     *
     * @param inspectionResultList .
     */
    private void saveRestInspectionResult(List<RestInspectionResultBO> inspectionResultList) {
        List<InspectionServiceResultDB> allPO = inspectionResultList.stream().flatMap(bo -> bo.getItem().stream().map(item -> {
            InspectionServiceResultDB po = new InspectionServiceResultDB();
            po.setId(UUIDUtil.generate());
            po.setInspectionId(bo.getInspectionId());
            po.setServiceName(bo.getServiceName());
            po.setPodName(bo.getPodName());
            po.setInspectionItem(item.getInspectionItem());
            po.setInspectionValue(item.getInspectionValue());
            po.setDescription(subDesc(item.getDesc()));
            return po;
        })).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(allPO)) {
            inspectionServiceResultDao.save(allPO);
            log.info("save rest inspection po .size:{}", allPO.size());
        }
    }

    /**
     * kafka ack消息处理
     *
     * @param kafkaPods .
     */
    private void handleKafkaInspectionAck(List<InspectPodAvailableBo> kafkaPods, String inspectionId) {
        long startTs = System.currentTimeMillis();
        List<String> hasAckPods = new ArrayList<>(kafkaPods.size());
        List<KafkaInspectionAckMsgBO> allAckMsg = new ArrayList<>(kafkaPods.size());
        try {
            Map<String, KafkaInspectionAckMsgBO> kafkaInspectionAckMsgMap = ackMsgMap.get(inspectionId);
            ExecutorCompletionService<KafkaInspectionAckMsgBO> completionService = new ExecutorCompletionService<>(restInspectionExecutor);
            kafkaPods.forEach(podInfo -> completionService.submit(new KafkaInspectionTask(startTs, inspectionKafkaTimeout, podInfo.getPodName(), kafkaInspectionAckMsgMap)));
            for (InspectPodAvailableBo kafkaPod : kafkaPods) {
                try {
                    KafkaInspectionAckMsgBO kafkaInspectionAckMsgBO = completionService.take().get();
                    allAckMsg.add(kafkaInspectionAckMsgBO);
                } catch (InterruptedException | ExecutionException e) {
                    log.error("{}, get rest inspection future error", kafkaPod.getServiceName(), e);
                }
            }
        } catch (Exception e) {
            log.error("wait kafka ack error", e);
        }

        Map<String, String> podName2ServerName = new HashMap<>();
        kafkaPods.forEach(x -> podName2ServerName.put(x.getPodName(), x.getServiceName()));
        List<InspectionServiceResultDB> collect = allAckMsg.stream().filter(Objects::nonNull).flatMap(ack -> {
            hasAckPods.add(ack.getPodName());
            if (CollectionUtils.isNotEmpty(ack.getResult())) {//有上报指标项

                // 将消息转成po准备持久化
                return ack.getResult().stream().map(item -> {
                    InspectionServiceResultDB po = new InspectionServiceResultDB();
                    po.setId(UUIDUtil.generate());
                    po.setInspectionId(ack.getInspectionId());
                    po.setServiceName(podName2ServerName.getOrDefault(ack.getPodName(), ""));
                    po.setPodName(ack.getPodName());
                    po.setInspectionItem(item.getInspectionItem());
                    po.setInspectionValue(item.getInspectionValue());
                    po.setDescription(subDesc(item.getDesc()));
                    return po;
                });
            } else { //没有具体指标
                InspectionServiceResultDB po = new InspectionServiceResultDB();
                po.setId(UUIDUtil.generate());
                po.setInspectionId(ack.getInspectionId());
                po.setServiceName(podName2ServerName.getOrDefault(ack.getPodName(), ""));
                po.setPodName(ack.getPodName());
                po.setInspectionValue(InspectionServiceLadder.NORMAL.getValue());
                po.setDescription(subDesc("无指标监控"));
                return Stream.of(po);
            }
        }).collect(Collectors.toList());
        log.info("kafka ack pods:{}", collect);
        if (CollectionUtils.isNotEmpty(collect)) {
            inspectionServiceResultDao.save(collect);
        }

        // 处理没有收到kafka ack的pod
        List<InspectionServiceResultDB> timeoutPods = kafkaPods.stream().filter(x -> !hasAckPods.contains(x.getPodName()))
                .map(x -> {
                    InspectionServiceResultDB po = new InspectionServiceResultDB();
                    po.setId(UUIDUtil.generate());
                    po.setInspectionId(inspectionId);
                    po.setServiceName(x.getServiceName());
                    po.setPodName(x.getPodName());
                    po.setDescription("巡检ack超时");
                    po.setInspectionValue(InspectionServiceLadder.RISK.getValue());
                    return po;
                }).collect(Collectors.toList());
        log.info("kafka ack timeout pods:{}", timeoutPods);
        if (CollectionUtils.isNotEmpty(timeoutPods)) {
            inspectionServiceResultDao.save(timeoutPods);
        }
    }

    /**
     * 截取desc长度
     *
     * @param inspectionDesc .
     * @return .
     */
    private String subDesc(String inspectionDesc) {
        if (StringUtils.isNotBlank(inspectionDesc) && inspectionDesc.length() > 2000) {
            return inspectionDesc.substring(0, 2000);
        }
        return inspectionDesc;
    }

    public Map<String, Map<String, KafkaInspectionAckMsgBO>> getAckMsgMap() {
        return ackMsgMap;
    }
}
