package com.xylink.manager.inspection.service;

import com.xylink.manager.inspection.entity.bo.service.KafkaInspectionAckMsgBO;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

/**
 * rest方式的巡检任务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/9 17:47
 */
public class KafkaInspectionTask implements Callable<KafkaInspectionAckMsgBO> {

    private final long taskStartTime;
    private final int timeout;
    private final String podName;

    private final Map<String, KafkaInspectionAckMsgBO> ackMsgBoMap;

    public KafkaInspectionTask(long taskStartTime, int timeout, String podName, Map<String, KafkaInspectionAckMsgBO> ackMsgBoMap) {
        this.taskStartTime = taskStartTime;
        this.timeout = timeout;
        this.podName = podName;
        this.ackMsgBoMap = ackMsgBoMap;
    }

    @Override
    public KafkaInspectionAckMsgBO call() throws Exception {
        KafkaInspectionAckMsgBO kafkaInspectionAckMsgBO;
        long passTime;
        do {
            kafkaInspectionAckMsgBO = ackMsgBoMap.get(podName);
            // ack 存在，并且 ack 有效
            if (kafkaInspectionAckMsgBO != null && StringUtils.isNotBlank(kafkaInspectionAckMsgBO.getPodName())) {
                // 返回结果
                return kafkaInspectionAckMsgBO;
            }
            // 等待一秒
            TimeUnit.SECONDS.sleep(1);
            passTime = System.currentTimeMillis() - taskStartTime;
        } while (passTime < timeout && !Thread.currentThread().isInterrupted());
        return null;
    }
}
