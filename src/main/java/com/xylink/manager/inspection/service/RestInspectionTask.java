package com.xylink.manager.inspection.service;

import com.xylink.manager.inspection.entity.bo.service.RestInspectionParamBO;
import com.xylink.manager.inspection.entity.bo.service.RestInspectionResultBO;
import com.xylink.manager.inspection.entity.bo.service.ServiceInspectionItemBO;
import com.xylink.manager.inspection.entity.enums.InspectionMetricKeyEnum;
import com.xylink.manager.inspection.entity.enums.InspectionServiceLadder;
import com.xylink.manager.inspection.entity.model.RestMessage;
import com.xylink.config.util.JsonUtil;
import com.xylink.manager.inspection.utils.SignUtilV2;
import com.xylink.util.SpringBeanUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;

/**
 * rest方式的巡检任务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/9 17:47
 */
public class RestInspectionTask implements Callable<RestInspectionResultBO> {
    private static final Logger LOGGER = LoggerFactory.getLogger(RestInspectionTask.class);

    private RestInspectionParamBO paramBO;
    private RestTemplate restTemplate;

    public RestInspectionTask(RestInspectionParamBO paramBO) {
        this.paramBO = paramBO;
        this.restTemplate = SpringBeanUtil.getBean("inspectionRestTemplate");
    }

    @Override
    public RestInspectionResultBO call() throws Exception {
        RestInspectionResultBO bo = new RestInspectionResultBO();
        bo.setInspectionId(paramBO.getInspectionId());
        bo.setPodName(paramBO.getPodName());
        bo.setServiceName(paramBO.getServiceName());
        List<ServiceInspectionItemBO> itemList = new ArrayList<>();
        bo.setItem(itemList);
        if (StringUtils.isNotBlank(paramBO.getPreError())) {
            ServiceInspectionItemBO item = new ServiceInspectionItemBO();
            item.setInspectionValue(InspectionServiceLadder.EXCEPTION.getValue());
            item.setInspectionItem(InspectionMetricKeyEnum.SERVER_INSPECTION.getMetricKey());
            item.setDesc("rest请求异常，" + paramBO.getPreError());
            itemList.add(item);
            return bo;
        }
        String url = paramBO.getUrl() + "?inspectionId=" + paramBO.getInspectionId();
        try {
            LOGGER.info("rest inspection serviceName:{} url:{}", paramBO.getServiceName(), url);
            ResponseEntity<String> entity;
            if ("uaa-api-spring-server".equals(paramBO.getServiceName())) {
                String subUrl = url.substring(url.indexOf("/api/rest"));
                Map<String, String> signHeaders = SignUtilV2.getGatewatSignHeaders("GET", subUrl, "");
                HttpHeaders headers = new HttpHeaders();
                signHeaders.forEach(headers::add);
                HttpEntity<String> request = new HttpEntity<>("", headers);
                entity = restTemplate.exchange(url, HttpMethod.GET, request, String.class);
            } else {
                entity = restTemplate.getForEntity(url, String.class);
            }
            LOGGER.info("rest inspection serviceName:{} result:{}", paramBO.getServiceName(), entity);
            if (paramBO.getServiceName().contains("private-vodnetwork")) {
                LOGGER.info("rest inspection serviceName:{} result:{}", paramBO.getServiceName(), entity);
            }
            List<ServiceInspectionItemBO> serviceInspectionResult = JsonUtil.parseToList(entity.getBody(), ServiceInspectionItemBO.class);
            if (CollectionUtils.isNotEmpty(serviceInspectionResult)) {
                serviceInspectionResult.forEach(x -> {
                    ServiceInspectionItemBO item = new ServiceInspectionItemBO();
                    item.setDesc(x.getDesc());
                    item.setInspectionItem(x.getInspectionItem());
                    item.setInspectionValue(x.getInspectionValue());
                    itemList.add(item);
                });
            } else {
                // 上报的空数组
                ServiceInspectionItemBO item = new ServiceInspectionItemBO();
                item.setDesc("上报空指标");
                item.setInspectionItem(InspectionMetricKeyEnum.SERVER_INSPECTION.getMetricKey());
                item.setInspectionValue(InspectionServiceLadder.NORMAL.getValue());
                itemList.add(item);
            }
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            LOGGER.warn("rest inspection error, url:{} serviceName:{}, http no 200. parse body:{} and code:{}", url,
                    paramBO.getServiceName(), e.getResponseBodyAsString(), e.getStatusCode());
            ServiceInspectionItemBO item = new ServiceInspectionItemBO();
            try {
                RestMessage restMessage = JsonUtil.parseJson(e.getResponseBodyAsString(), RestMessage.class);
                if (restMessage.getErrorCode() == 0) {
                    item.setInspectionItem(InspectionMetricKeyEnum.SERVER_INSPECTION.getMetricKey());
                    item.setDesc("httpCode:" + e.getStatusCode().value() + ";body:" + e.getResponseBodyAsString());
                } else {
                    item.setInspectionItem(InspectionMetricKeyEnum.SERVER_INSPECTION.getMetricKey());
                    item.setDesc("httpCode:" + e.getStatusCode().value() + "; errorCode:" + restMessage.getErrorCode()
                            + "; msg:" + restMessage.getUserMessage());
                }
            } catch (Exception e2) { //解析出错就直接给一个错误的状态码就行
                LOGGER.error("rest inspection error, url:{} serviceName:{}, parse error body:{}", url,
                        paramBO.getServiceName(), e.getResponseBodyAsString(), e);
                item.setInspectionItem(InspectionMetricKeyEnum.SERVER_INSPECTION.getMetricKey());
                item.setDesc("httpCode:" + e.getStatusCode().value() + ";body:" + e.getResponseBodyAsString());
            }
            item.setInspectionValue(InspectionServiceLadder.EXCEPTION.getValue());
            itemList.add(item);

        } catch (Exception e) {
            ServiceInspectionItemBO item = new ServiceInspectionItemBO();
            item.setInspectionValue(InspectionServiceLadder.EXCEPTION.getValue());
            item.setInspectionItem(InspectionMetricKeyEnum.SERVER_INSPECTION.getMetricKey());
            item.setDesc("rest请求异常" + e.getMessage());
            itemList.add(item);
            LOGGER.error("rest inspection error, url:{}, params:{}", url, paramBO, e);
        }
        itemList.forEach(x -> {
            if (StringUtils.isNotBlank(x.getDesc()) && x.getDesc().length() > 2000) {
                x.setDesc(x.getDesc().substring(0, 2000));
            }
        });
        return bo;
    }
}
