package com.xylink.manager.inspection.service.report;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.xylink.manager.inspection.entity.bo.InspectionMetricConfigBo;
import com.xylink.manager.inspection.entity.db.*;
import com.xylink.manager.inspection.entity.enums.InspectionItemTypeEnum;
import com.xylink.manager.inspection.entity.enums.InspectionMetricKeyEnum;
import com.xylink.manager.inspection.entity.enums.InspectionThresholdLadderEnum;
import com.xylink.manager.inspection.entity.excel.HostResultData;
import com.xylink.manager.inspection.entity.excel.MiddleResultData;
import com.xylink.manager.inspection.entity.excel.ServiceResultData;
import com.xylink.manager.inspection.entity.vo.InspectionSystemRecordVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/9 15:51
 */
@Service
@Slf4j
public class InspectionExcelReportService extends InspectionBaseReportService {

    /**
     * excel 单元格中的换行符
     */
    private static final String EXCEL_LINE_BREAK = String.valueOf((char) 10);


    public void export(InspectInstanceDb instance, File dir) {
        List<InspectionTaskDb> taskDbList = inspectionTaskDao.getTaskList(instance.getId());

        InspectionTaskDb systemTask = null;
        InspectionTaskDb middleTask = null;
        InspectionTaskDb serviceTask = null;

        for (InspectionTaskDb inspectionTaskDb : taskDbList) {
            if (InspectionItemTypeEnum.SYSTEM.getId() == inspectionTaskDb.getTaskType()) {
                systemTask = inspectionTaskDb;
            }
            if (InspectionItemTypeEnum.MIDDLEWARE.getId() == inspectionTaskDb.getTaskType()) {
                middleTask = inspectionTaskDb;
            }
            if (InspectionItemTypeEnum.SERVER.getId() == inspectionTaskDb.getTaskType()) {
                serviceTask = inspectionTaskDb;
            }
        }

        List<HostResultData> hostResultData = buildHostResultDataList(systemTask);
        List<MiddleResultData> middleResultData = buildMiddleResultDataList(middleTask);
        List<ServiceResultData> serviceResultData = buildServiceResultDataList(serviceTask);

        String inspectionName = instance.getInspectionName();
        if (StringUtils.isNotBlank(inspectionName) && inspectionName.length() > 32) {
            inspectionName = inspectionName.substring(0, 32);
        }
        String savePath = dir.getAbsolutePath() + FILE_SEPARATOR + inspectionName + "-" + instance.getCreateTime().toString() + ".xls";
        try (ExcelWriter excelWriter = EasyExcel.write(savePath).build()) {
            if (CollectionUtils.isNotEmpty(hostResultData)) {
                WriteSheet hostSheet = EasyExcel.writerSheet("主机巡检").head(HostResultData.class).build();
                excelWriter.write(hostResultData, hostSheet);
            }
            if (CollectionUtils.isNotEmpty(middleResultData)) {
                WriteSheet middleSheet = EasyExcel.writerSheet("中间件巡检").head(MiddleResultData.class).build();
                excelWriter.write(middleResultData, middleSheet);
            }
            if (CollectionUtils.isNotEmpty(serviceResultData)) {
                WriteSheet serviceSheet = EasyExcel.writerSheet("服务巡检").head(ServiceResultData.class).build();
                excelWriter.write(serviceResultData, serviceSheet);
            }
        }
        log.info("[inspection] exportPdf path: {}", savePath);
    }

    private List<ServiceResultData> buildServiceResultDataList(InspectionTaskDb serviceTask) {
        List<ServiceResultData> resultData = new ArrayList<>();
        if (serviceTask == null) {
            return resultData;
        }
        List<InspectionSubTaskDb> subTaskList = subTaskDao.getSubTaskList(serviceTask.getId());
        for (InspectionSubTaskDb subTask : subTaskList) {
            String itemName = subTask.getItemName();
            List<InspectionMetricTaskDb> metricTasks = inspectionMetricTaskDao.getMetricTasksBySubTaskId(subTask.getId());
            for (InspectionMetricTaskDb metricTask : metricTasks) {
                ServiceResultData serviceResultData = buildServiceResultData(itemName, metricTask);
                resultData.add(serviceResultData);
            }
        }
        return resultData;
    }

    private ServiceResultData buildServiceResultData(String itemName, InspectionMetricTaskDb metricTask) {
        // itemName = serverName|podName|ident
        String[] split = itemName.split("\\|");
        String serverName = split[0];
        String podName = split[1];
        String ident = split[2];
        String metricKey = metricTask.getMetricKey();
        // 将 metricValue 中所有换行符替换为 excel 的换行符

        String metricValue = metricTask.getMetricResult();
        if (StringUtils.isNotBlank(metricValue)) {
            metricValue = metricValue.replaceAll("\n", EXCEL_LINE_BREAK);
        } else {
            metricValue = InspectionThresholdLadderEnum.NORMAL.getDescription();
        }
        InspectionMetricConfigBo metric = inspectionMetricDao.getInspectionMetricByKey(metricKey);

        String metricDesc = "";
        if (metric != null) {
            metricDesc = metric.getMetricDesc().replaceAll("<br />", EXCEL_LINE_BREAK);
        }

        ServiceResultData serviceResultData = new ServiceResultData();
        serviceResultData.setServiceName(serverName);
        serviceResultData.setPodName(podName);
        serviceResultData.setMetricName(metricKey);
        serviceResultData.setMetricValue(metricValue);
        serviceResultData.setMetricDesc(metricDesc);
        serviceResultData.setResult(buildResultCellData(metricTask.getLadder()));
        return serviceResultData;
    }

    private List<MiddleResultData> buildMiddleResultDataList(InspectionTaskDb middleTask) {
        List<MiddleResultData> resultData = new ArrayList<>();
        if (middleTask == null) {
            return resultData;
        }
        List<InspectionSubTaskDb> subTaskList = subTaskDao.getSubTaskList(middleTask.getId());
        for (InspectionSubTaskDb subTask : subTaskList) {
            String itemName = subTask.getItemName();
            List<InspectionMetricTaskDb> metricTasks = inspectionMetricTaskDao.getMetricTasksBySubTaskId(subTask.getId());
            for (InspectionMetricTaskDb metricTask : metricTasks) {
                MiddleResultData middleResultData = buildMiddleResultData(itemName, metricTask);
                resultData.add(middleResultData);
            }
        }
        return resultData;
    }

    private MiddleResultData buildMiddleResultData(String itemName, InspectionMetricTaskDb metricTask) {
        String metricKey = metricTask.getMetricKey();
        String metricValue = metricTask.getMetricResult();
        // 将 metricValue 中所有换行符替换为 excel 的换行符
        if (StringUtils.isNotBlank(metricValue)) {
            metricValue = metricValue.replaceAll("\n", EXCEL_LINE_BREAK);
        }
        InspectionMetricConfigBo metric = inspectionMetricDao.getInspectionMetricByKey(metricKey);

        String metricDesc = "";
        if (metric != null) {
            metricDesc = metric.getMetricDesc().replaceAll("<br />", EXCEL_LINE_BREAK);
        }

        MiddleResultData middleResultData = new MiddleResultData();
        middleResultData.setItem(itemName);
        middleResultData.setMetricName(metricKey);
        middleResultData.setResult(buildResultCellData(metricTask.getLadder()));
        middleResultData.setMetricValue(metricValue);
        middleResultData.setMetricDesc(metricDesc);
        return middleResultData;
    }


    private List<HostResultData> buildHostResultDataList(InspectionTaskDb systemTask) {
        List<HostResultData> resultDataList = new ArrayList<>();
        if (systemTask == null) {
            return resultDataList;
        }

        // 根据 task 获取到 subTask 列表
        List<InspectionSubTaskDb> subTaskList = subTaskDao.getSubTaskList(systemTask.getId());
        // 获取 systemMetric 对应的所有指标 key，获取指标 key 对应的阈值
        List<String> systemMetricKeys = Arrays.stream(InspectionMetricKeyEnum.values())
                .map(InspectionMetricKeyEnum::getMetricKey)
                .collect(Collectors.toList());
        Map<String, List<InspectionMetricThresholdDb>> metricIdsAndThresholds = metricThresholdDao.getMetricIdsAndThresholds(systemMetricKeys);

        // 遍历 subTask，创建 excel 主机巡检 sheet bean
        for (InspectionSubTaskDb subTask : subTaskList) {
            HostResultData hostResultData = new HostResultData();
            String itemName = subTask.getItemName();
            String hostName = itemName.substring(0, itemName.lastIndexOf("("));
            String hostIp = itemName.substring(itemName.lastIndexOf("(") + 1, itemName.lastIndexOf(")"));
            // 设置 "主机名" 列
            hostResultData.setHostName(hostName);
            hostResultData.setHostIp(hostIp);
            // 填充 "结果" 列
            setHostResultCellData(subTask, hostResultData);
            // 填充 各项指标结果 列
            setHostValueCellData(subTask, hostResultData, metricIdsAndThresholds);
            resultDataList.add(hostResultData);
        }
        return resultDataList;
    }

    /**
     * 填充 Host 各巡检项的值
     *
     * @param hostResultData         excel 导出 bean
     * @param metricIdsAndThresholds 风险判断map，cpu 和 mem 指标拆分后需要重新计算阈值
     */
    private void setHostValueCellData(InspectionSubTaskDb subTask,
                                      HostResultData hostResultData,
                                      Map<String, List<InspectionMetricThresholdDb>> metricIdsAndThresholds) {
        // 根据 subTask 获取 metricTask
        List<InspectionMetricTaskDb> metricTasksBySubTaskId = inspectionMetricTaskDao.getMetricTasksBySubTaskId(subTask.getId());
        // 转换成 InspectionSystemRecordVo 结构
        InspectionSystemRecordVo systemRecord = subTaskDao.buildSystemRecordVo(subTask, metricTasksBySubTaskId);

        // 获取各相指标结果
        InspectionSystemRecordVo.Metric cpu = systemRecord.getCpu();
        InspectionSystemRecordVo.Metric mem = systemRecord.getMemory();
        InspectionSystemRecordVo.Metric disk = systemRecord.getDisk();

        StringBuilder descBuilder = new StringBuilder();
        if (disk != null) {
            // 巡检结果中有 disk 设置 disk 内容
            setHostDiskResultData(hostResultData, disk, descBuilder);
        } else {
            // 没有 disk 设置空单元格
            hostResultData.setDisk(buildValueBlankCellData());
        }

        if (cpu != null) {
            // 巡检结果中有 cpu 设置 cpu 内容
            setHostCpuResultData(hostResultData, metricIdsAndThresholds, cpu.getValue(), descBuilder);
        } else {
            // 没有 cpu 设置空单元格
            hostResultData.setCpu(buildValueBlankCellData());
        }

        if (mem != null) {
            // 巡检结果中有 mem 设置 mem 内容
            setHostMemResultData(hostResultData, metricIdsAndThresholds, mem.getValue(), descBuilder);
        } else {
            // 没有 mem 设置空单元格
            hostResultData.setMem(buildValueBlankCellData());
        }

        if (descBuilder.length() > 0) {
            // 删掉最后一个换行符
            descBuilder.deleteCharAt(descBuilder.length() - 1);
        }
        // 将描述文本中的换行符替换为excel单元格中的换行符
        String desc = descBuilder.toString().replaceAll("\n", EXCEL_LINE_BREAK);
        hostResultData.setDesc(desc);
    }

    /**
     * 设置 disk 巡检项结果
     *
     * @param hostResultData cellBean
     * @param disk           巡检指标结果
     * @param descBuilder    描述
     */
    private void setHostDiskResultData(HostResultData hostResultData, InspectionSystemRecordVo.Metric disk, StringBuilder descBuilder) {
        // 设置结果
        hostResultData.setDisk(buildValueCellData(disk.getValue(), disk.getLadder()));

        // 如果结果存在风险，需要拼接描述信息
        if (!UNKNOWN_METRIC_VALUE.equals(disk.getValue())) {
            appendHostDesc(disk.getLadder(), InspectionMetricKeyEnum.HOST_DISK_USAGE.getMetricKey(), descBuilder);
        } else {
            descBuilder.append("磁盘指标获取失败").append("\n");
        }
    }

    private void appendHostDesc(int ladder, String metricKey, StringBuilder descBuilder) {
        if (!InspectionThresholdLadderEnum.NORMAL.getLadder().equals(ladder)) {
            InspectionMetricConfigBo metricConfig = inspectionMetricDao.getInspectionMetricByKey(metricKey);
            String desc = "";
            if (metricConfig != null) {
                desc = metricConfig.getMetricDesc();
                desc = desc.replace("<br />", "，");

            }
            descBuilder.append(desc).append("\n");
        }
    }

    private void setHostMemResultData(HostResultData hostResultData,
                                      Map<String, List<InspectionMetricThresholdDb>> metricIdsAndThresholds,
                                      String value,
                                      StringBuilder descBuilder) {
        if (value.startsWith(UNKNOWN_METRIC_VALUE)) {
            hostResultData.setMem(buildValueCellData(UNKNOWN_METRIC_VALUE, InspectionThresholdLadderEnum.HIGH_RISK.getLadder()));
            descBuilder.append("内存指标获取失败").append("\n");
        } else {
            String usageRateString = value.substring(0, value.lastIndexOf("%"));
            int usageRateLadder = systemMetricRiskJudge(metricIdsAndThresholds, usageRateString, InspectionMetricKeyEnum.HOST_MEM_USAGE.getMetricKey());
            hostResultData.setMem(buildValueCellData(usageRateString + "%", usageRateLadder));
            appendHostDesc(usageRateLadder, InspectionMetricKeyEnum.HOST_MEM_USAGE.getMetricKey(), descBuilder);
        }
    }

    private void setHostCpuResultData(HostResultData hostResultData,
                                      Map<String, List<InspectionMetricThresholdDb>> metricIdsAndThresholds,
                                      String value,
                                      StringBuilder descBuilder) {
        if (value.startsWith(UNKNOWN_METRIC_VALUE)) {
            hostResultData.setCpu(buildValueCellData(UNKNOWN_METRIC_VALUE, InspectionThresholdLadderEnum.HIGH_RISK.getLadder()));
            descBuilder.append("CPU指标获取失败").append("\n");
        } else {
            String usageRateString = value.substring(0, value.lastIndexOf("%"));
            int usageRateLadder = systemMetricRiskJudge(metricIdsAndThresholds, usageRateString, InspectionMetricKeyEnum.HOST_CPU_USAGE.getMetricKey());
            hostResultData.setCpu(buildValueCellData(usageRateString + "%", usageRateLadder));
            appendHostDesc(usageRateLadder, InspectionMetricKeyEnum.HOST_CPU_USAGE.getMetricKey(), descBuilder);
        }
    }

    private WriteCellData<String> buildValueBlankCellData() {
        WriteCellData<String> writeCellData = new WriteCellData<>("-");
        writeCellData.setType(CellDataTypeEnum.STRING);
        return writeCellData;
    }

    private WriteCellData<String> buildValueCellData(String value, int ladder) {
        WriteCellData<String> writeCellData = new WriteCellData<>(value);
        writeCellData.setType(CellDataTypeEnum.STRING);
        if (ladder == InspectionThresholdLadderEnum.HIGH_RISK.getLadder()) {
            WriteCellStyle writeCellStyleData = new WriteCellStyle();
            writeCellStyleData.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
            writeCellStyleData.setFillForegroundColor(IndexedColors.RED.getIndex());
            writeCellData.setWriteCellStyle(writeCellStyleData);
            return writeCellData;
        }
        if (ladder == InspectionThresholdLadderEnum.MIDDLE_RISK.getLadder()) {
            WriteCellStyle writeCellStyleData = new WriteCellStyle();
            writeCellStyleData.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
            writeCellStyleData.setFillForegroundColor(IndexedColors.ORANGE.getIndex());
            writeCellData.setWriteCellStyle(writeCellStyleData);
            return writeCellData;
        }
        if (ladder == InspectionThresholdLadderEnum.LOW_RISK.getLadder()) {
            WriteCellStyle writeCellStyleData = new WriteCellStyle();
            writeCellStyleData.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
            writeCellStyleData.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
            writeCellData.setWriteCellStyle(writeCellStyleData);
            return writeCellData;
        }
        return writeCellData;
    }

    private WriteCellData<String> buildResultCellData(int ladder) {
        // 高风险结果设置单元格颜色为红色
        if (ladder == InspectionThresholdLadderEnum.HIGH_RISK.getLadder()) {
            WriteCellData<String> writeCellData = new WriteCellData<>(InspectionThresholdLadderEnum.HIGH_RISK.getDescription());
            writeCellData.setType(CellDataTypeEnum.STRING);
            WriteCellStyle writeCellStyleData = new WriteCellStyle();
            writeCellStyleData.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
            writeCellStyleData.setFillForegroundColor(IndexedColors.RED.getIndex());
            writeCellData.setWriteCellStyle(writeCellStyleData);
            return writeCellData;
        }
        // 中风险结果设置单元格颜色为橘色
        if (ladder == InspectionThresholdLadderEnum.MIDDLE_RISK.getLadder()) {
            WriteCellData<String> writeCellData = new WriteCellData<>(InspectionThresholdLadderEnum.MIDDLE_RISK.getDescription());
            writeCellData.setType(CellDataTypeEnum.STRING);
            WriteCellStyle writeCellStyleData = new WriteCellStyle();
            writeCellStyleData.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
            writeCellStyleData.setFillForegroundColor(IndexedColors.ORANGE.getIndex());
            writeCellData.setWriteCellStyle(writeCellStyleData);
            return writeCellData;
        }
        // 低风险结果设置单元格颜色为灰色
        if (ladder == InspectionThresholdLadderEnum.LOW_RISK.getLadder()) {
            WriteCellData<String> writeCellData = new WriteCellData<>(InspectionThresholdLadderEnum.LOW_RISK.getDescription());
            writeCellData.setType(CellDataTypeEnum.STRING);
            WriteCellStyle writeCellStyleData = new WriteCellStyle();
            writeCellStyleData.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
            writeCellStyleData.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
            writeCellData.setWriteCellStyle(writeCellStyleData);
            return writeCellData;
        }
        WriteCellData<String> writeCellData = new WriteCellData<>(InspectionThresholdLadderEnum.NORMAL.getDescription());
        writeCellData.setType(CellDataTypeEnum.STRING);
        return writeCellData;
    }

    private void setHostResultCellData(InspectionSubTaskDb subTask, HostResultData hostResultData) {
        if (subTask.getHighRisk() > 0) {
            WriteCellData<String> writeCellData = new WriteCellData<>(InspectionThresholdLadderEnum.HIGH_RISK.getDescription());
            writeCellData.setType(CellDataTypeEnum.STRING);
            WriteCellStyle writeCellStyleData = new WriteCellStyle();
            writeCellStyleData.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
            writeCellStyleData.setFillForegroundColor(IndexedColors.RED.getIndex());
            writeCellData.setWriteCellStyle(writeCellStyleData);
            hostResultData.setResult(writeCellData);
            return;
        }
        if (subTask.getMiddleRisk() > 0) {
            WriteCellData<String> writeCellData = new WriteCellData<>(InspectionThresholdLadderEnum.MIDDLE_RISK.getDescription());
            writeCellData.setType(CellDataTypeEnum.STRING);
            WriteCellStyle writeCellStyleData = new WriteCellStyle();
            writeCellStyleData.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
            writeCellStyleData.setFillForegroundColor(IndexedColors.ORANGE.getIndex());
            writeCellData.setWriteCellStyle(writeCellStyleData);
            hostResultData.setResult(writeCellData);
            return;
        }
        if (subTask.getLowRisk() > 0) {
            WriteCellData<String> writeCellData = new WriteCellData<>(InspectionThresholdLadderEnum.LOW_RISK.getDescription());
            writeCellData.setType(CellDataTypeEnum.STRING);
            WriteCellStyle writeCellStyleData = new WriteCellStyle();
            writeCellStyleData.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
            writeCellStyleData.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
            writeCellData.setWriteCellStyle(writeCellStyleData);
            hostResultData.setResult(writeCellData);
            return;
        }
        WriteCellData<String> writeCellData = new WriteCellData<>(InspectionThresholdLadderEnum.NORMAL.getDescription());
        writeCellData.setType(CellDataTypeEnum.STRING);
        hostResultData.setResult(writeCellData);
    }

}
