package com.xylink.manager.inspection.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.xylink.config.Constants;
import com.xylink.config.StatisItem;
import com.xylink.manager.controller.dto.HybridStaticDto;
import com.xylink.manager.inspection.common.OpsManagerException;
import com.xylink.manager.inspection.common.monitor.MonitorIndicatorDto;
import com.xylink.manager.inspection.dao.*;
import com.xylink.manager.inspection.entity.condition.DefaultMonitor;
import com.xylink.manager.inspection.entity.db.*;
import com.xylink.manager.inspection.entity.enums.InspectionItemTypeEnum;
import com.xylink.manager.inspection.entity.enums.InspectionMetricKeyEnum;
import com.xylink.manager.inspection.entity.enums.InspectionThresholdLadderEnum;
import com.xylink.manager.inspection.entity.model.HostMonitorIndicatorInfo;
import com.xylink.manager.inspection.entity.vo.HostPageVO;
import com.xylink.manager.inspection.entity.vo.MonitorIndicatorVo;
import com.xylink.manager.inspection.utils.JsonUtils;
import com.xylink.manager.inspection.utils.MetricRiskJudgeUtil;
import com.xylink.manager.model.KuberDiskInfo;
import com.xylink.manager.model.deploy.Node;
import com.xylink.manager.service.InfluxDBService;
import com.xylink.manager.service.KuberNodeInfoService;
import com.xylink.manager.service.PrivateDataService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.nightingale.MonitorN9eService;
import com.xylink.manager.service.nightingale.monitor.MonitorService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.xylink.manager.inspection.entity.enums.InspectionMetricKeyEnum.HOST_CPU_USAGE;
import static com.xylink.manager.inspection.entity.enums.InspectionMetricKeyEnum.HOST_MEM_USAGE;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/5 18:52
 */
@Service
@Slf4j
public class InspectSystemService {
    @Autowired
    private K8sService k8sService;
    @Autowired
    private InspectionMetricDao metricDao;
    @Autowired
    private InspectionItemConfigDao itemConfigDao;
    @Autowired
    private MonitorService monitorService;
    @Autowired
    private InspectionMetricTaskDao metricTaskDao;
    @Autowired
    private InspectionSubTaskDao subTaskDao;
    @Autowired
    private InspectionMetricThresholdDao metricThresholdDao;
    @Autowired
    private InfluxDBService influxDBService;
    @Autowired
    private MonitorN9eService monitorN9eService;
    @Autowired
    private PrivateDataService privateDataService;
    @Autowired
    private KuberNodeInfoService kuberNodeInfoService;

    private static final String UNKNOWN_METRIC_VALUE = "状态未知";

    public List<InspectionSubTaskDb> execInspection(String taskId, List<String> itemConfigIds) {
        Map<String, String> metricNameToQuery = getMetricQuery();
        log.info("itemConfigIds: {}", itemConfigIds);
        List<InspectionSubTaskDb> subTasks = new ArrayList<>();
        // 调用 HostService.getPage() 接口获取主机列表
        List<HostPageVO> hosts = toHostPageVOList(k8sService.listNode());
        if (CollectionUtils.isEmpty(hosts)) {
            return new ArrayList<>();
        }
        for (HostPageVO host : hosts) {
            log.info("host: {}", host);
            InspectionSubTaskDb subTask = subTaskDao.createSubTask(taskId, null, InspectionItemTypeEnum.SYSTEM.getId(), host.getName() + "(" + host.getPrivateIp() + ")");
            List<InspectionMetricTaskDb> metricTasks = execNodeInspection(subTask.getId(), host, metricNameToQuery, itemConfigIds);
            // system 类型的统计风险数量与其他不同
            subTaskDao.updateSystemSubTask(subTask, metricTasks);
            subTasks.add(subTask);
        }
        return subTasks;
    }

    private List<HostPageVO> toHostPageVOList(List<Node> nodes) {
        return nodes.stream().map(this::toHostPageVO).collect(Collectors.toList());
    }

    private HostPageVO toHostPageVO(Node node) {
        HostPageVO pageVO = new HostPageVO();
        pageVO.setPrivateIp(node.getIp());
        pageVO.setName(node.getHostName());
        pageVO.setCpu(node.getCpu().getAmount());
        pageVO.setMemory(String.format("%.0fG", Double.parseDouble(node.getMemory().getAmount()) / 1024 / 1024));
        pageVO.setDisk(node.getDisk().getAmount());
        completeNodeInfoForInspect(pageVO, node, node.getIp());
        return pageVO;
    }

    /**
     * 数据来源：夜莺 || influxdb
     *
     * @param pageVO
     */
    private void completeNodeInfoForInspect(HostPageVO pageVO, Node node, String ip) {
        String name = node.getName();
        long time = new Date().getTime();
        if (!monitorN9eService.nightingaleSwitch()) {
            queryFromInfluxdb(pageVO, name, ip, time);
            JsonNode diskConfig = privateDataService.getDiskConfigCache();
            log.info("{}, Node status: {}", ip, node.getReadyStatus());
            pageVO.setKuberDiskInfos(node.isReady() ?
                    kuberNodeInfoService.getNodeFilesystem(ip, diskConfig) : new ArrayList<>());
        }
    }

    private void queryFromInfluxdb(HostPageVO pageVO, String name, String ip, long time) {
        try {
            //内存
            HybridStaticDto memoryDto = influxDBService.getStaticsByTypeAndNode(StatisItem.NODE_MEMORY, name, Constants.LAST30MIN);
            if (memoryDto != null && null != memoryDto.getSeries() && !memoryDto.getSeries().isEmpty()) {
                Float memoryUsage = (Float) memoryDto.getSeries().get(memoryDto.getSeries().size() - 1);
                pageVO.setMemoryUsage(memoryUsage);
            }

            //cpu
            HybridStaticDto cpuDto = influxDBService.getStaticsByTypeAndNode(StatisItem.NODE_CPU, name, Constants.LAST30MIN);
            if (cpuDto != null && null != cpuDto.getSeries() && !cpuDto.getSeries().isEmpty()) {
                Float cpuUsage = (Float) cpuDto.getSeries().get(cpuDto.getSeries().size() - 1);
                pageVO.setCpuUsage(cpuUsage);
            }
            log.info("{} query in influxdb take: {}", ip, new Date().getTime() - time);
        } catch (Exception e) {
            log.error("error\n", e);
        }
    }

    /**
     * 调用 OPS 的 {@link MonitorService#getHostDefaulltScreen()} 方法获取到主机指标的查询表达式，该表达式用于查询夜莺中保存的指标
     *
     * @return Key: MetricName, Value: 查询条件。例如: CPU使用率 -> 100-cpu_usage_idle{ident="$ident",cpu="cpu-total"}
     */
    private Map<String, String> getMetricQuery() {
        String hostDefaultScreen = monitorService.getHostDefaulltScreen();
        List<DefaultMonitor> defaultMonitors = JsonUtils.jsonToList(hostDefaultScreen, DefaultMonitor.class);
        Map<String, String> metricNameToQuery = new HashMap<>();
        if (CollectionUtils.isEmpty(defaultMonitors)) {
            log.error("system inspection error");
            throw new OpsManagerException("系统巡检失败，指标获取失败");
        }
        DefaultMonitor defaultMonitor = defaultMonitors.get(0);
        DefaultMonitor.Tags tag = defaultMonitor.getTags().get(0);
        for (DefaultMonitor.Charts chart : tag.getCharts()) {
            // 指标名称
            String metricName = chart.getConfigs().getTitle();
            // 查询表达式，用于查询夜莺中保存的指标值
            String metricQueryExpression = chart.getConfigs().getMetrics().get(0).getQuery();
            metricNameToQuery.put(metricName, metricQueryExpression);
        }
        return metricNameToQuery;
    }

    private List<InspectionMetricTaskDb> execNodeInspection(String subTaskId, HostPageVO host, Map<String, String> metricNameToQuery, List<String> itemConfigIds) {
        List<InspectionMetricTaskDb> result = new ArrayList<>();
        for (String itemConfigId : itemConfigIds) {
            InspectionItemConfigDb itemConfig = itemConfigDao.getItemById(itemConfigId);
            result.add(execMetricInspection(subTaskId, host, metricNameToQuery, itemConfig));
        }
        return result;
    }


    private InspectionMetricTaskDb execMetricInspection(String subTaskId, HostPageVO host, Map<String, String> metricNameToQuery, InspectionItemConfigDb itemConfig) {
        // 查询出所有 Metric 信息
        List<InspectionMetricConfigDb> metrics = metricDao.getInspectionMetricByItemId(itemConfig.getId());
        // 获取指标的所有阈值信息
        Map<String, List<InspectionMetricThresholdDb>> metricIdToThresholds = metricThresholdDao.getMetricIdsAndThresholds(metrics.stream().map(InspectionMetricConfigDb::getMetricKey).collect(Collectors.toList()));
        InspectionMetricConfigDb metric = metrics.get(0);
        long startTime = System.currentTimeMillis();
        String metricKey = metric.getMetricKey();
        InspectionMetricTaskDb metricTask = metricTaskDao.buildMetricTaskDb(subTaskId, startTime, "", metric.getMetricKey());
        try {
            Double maxValue = getMetricValue(host, metricNameToQuery, metricKey);
            List<InspectionMetricThresholdDb> thresholds = metricIdToThresholds.get(metric.getMetricKey());
            systemMetricRiskJudge(metricTask, maxValue, thresholds);
            judgeDeployInfo(host, metricTask);
        } catch (Exception e) {
            log.warn("An exception occurs when obtaining node metric, node: " + host.getPrivateIp() + "; metric: " + metricKey + ";", e);
            metricTask.setMetricResult(UNKNOWN_METRIC_VALUE);
            metricTask.setLadder(InspectionThresholdLadderEnum.HIGH_RISK.getLadder());
            metricTask.setExceptionDesc("获取指标时出现异常");
        }
        metricTask.setFinishedTime(System.currentTimeMillis());
        metricTaskDao.saveMetricTask(metricTask);
        return metricTask;
    }

    private void judgeDeployInfo(HostPageVO hostDetailDto, InspectionMetricTaskDb metricTask) {
        String metricKey = metricTask.getMetricKey();
        String result = metricTask.getMetricResult();
        if (HOST_CPU_USAGE.getMetricKey().equals(metricKey)) {
            Integer cpu = Integer.parseInt(hostDetailDto.getCpu());
            result = result + "(" + cpu + "核)";
            metricTask.setMetricResult(result);
        } else if (HOST_MEM_USAGE.getMetricKey().equals(metricKey)) {
            String memory = hostDetailDto.getMemory().toLowerCase();
            result = result + "(" + memory + ")";
            metricTask.setMetricResult(result);
        }
    }

    private Double getMetricValue(HostPageVO host, Map<String, String> metricNameToQuery, String metricKey) {
        if (monitorN9eService.nightingaleSwitch()) {
            String metricQueryExpression = metricNameToQuery.get(metricKey);
            if (InspectionMetricKeyEnum.HOST_DISK_USAGE.getMetricKey().equals(metricKey)) {
                metricQueryExpression = metricNameToQuery.get("硬盘使用率");
            }
            return getMetricValue(host.getName(), metricQueryExpression);
        }
        if (metricKey.equals("CPU使用率") && host.getCpuUsage() != null) {
            return Double.valueOf(host.getCpuUsage());
        }
        if (metricKey.equals("内存使用率") && host.getMemoryUsage() != null) {
            return Double.valueOf(host.getMemoryUsage());
        }
        if (metricKey.equals(InspectionMetricKeyEnum.HOST_DISK_USAGE.getMetricKey())) {
            return getMaxDiskUsage(host);
        }
        return null;
    }

    private double getMaxDiskUsage(HostPageVO host) {
        List<KuberDiskInfo> diskInfos = host.getKuberDiskInfos();
        if (CollectionUtils.isEmpty(diskInfos)) {
            return 0.0d;
        }
        double maxValue = 0.0d;
        for (KuberDiskInfo diskInfo : diskInfos) {
            double usage = (double) diskInfo.getUsage() * 100 / diskInfo.getCapacity();
            if (usage > maxValue) {
                maxValue = usage;
            }
        }
        return maxValue;
    }

    /**
     * 根据 host 的 privateIp 查询到 n9e 保存的指标信息
     *
     * @param metricQueryExpression 指标表查询表达式
     * @return .
     */
    private Double getMetricValue(String hostname, String metricQueryExpression) {
        if (StringUtils.isBlank(metricQueryExpression)) {
            throw new OpsManagerException("获取指标失败");
        }
        MonitorIndicatorDto monitorIndicatorDto = buildMonitorIndicatorDto(hostname + "-.*", metricQueryExpression);
        // 调用 ops-resource 模块查询 host 的指标值
        MonitorIndicatorVo monitorIndicatorVo = monitorService.getIndicatorList(monitorIndicatorDto);
        String json = JsonUtils.objectToJson(monitorIndicatorVo.getUi());
        List<HostMonitorIndicatorInfo> hostMonitorIndicatorInfos = JsonUtils.jsonToList(json, HostMonitorIndicatorInfo.class);
        if (CollectionUtils.isEmpty(hostMonitorIndicatorInfos)) {
            throw new OpsManagerException("获取指标失败");
        }
        Double maxValue = null;
        // 取得 1 小时内的指标的最高值
        HostMonitorIndicatorInfo info = hostMonitorIndicatorInfos.get(0);
        List<HostMonitorIndicatorInfo.Values> values = info.getValues();

        Optional<HostMonitorIndicatorInfo.Values> optional = values.stream()
                .filter(o -> Objects.nonNull(o) && Objects.nonNull(o.getTimestamp()) && Objects.nonNull(o.getValue()))
                .max(Comparator.comparing(HostMonitorIndicatorInfo.Values::getValue));
        if (optional.isPresent()) {
            HostMonitorIndicatorInfo.Values max = optional.get();
            maxValue = max.getValue();
        }
        return maxValue;
    }

    /**
     * 判断指标值是否到达某项风险
     *
     * @param metricTask 结果
     * @param maxValue   host指标最大值
     * @param thresholds 阈值
     */
    public void systemMetricRiskJudge(InspectionMetricTaskDb metricTask, Double maxValue, List<InspectionMetricThresholdDb> thresholds) {
        if (maxValue == null) {
            metricTask.setMetricResult(UNKNOWN_METRIC_VALUE);
            metricTask.setLadder(InspectionThresholdLadderEnum.HIGH_RISK.getLadder());
            return;
        }
        if (CollectionUtils.isEmpty(thresholds)) {
            metricTask.setMetricResult(String.format("%.2f", maxValue) + "%");
            metricTask.setLadder(InspectionThresholdLadderEnum.NORMAL.getLadder());
            return;
        }
        boolean flag = true;
        for (InspectionMetricThresholdDb threshold : thresholds) {
            String riskJudgment = threshold.getRiskJudgment();
            String[] split = riskJudgment.split("\\|");
            Double riskValue = Double.valueOf(split[0]);
            String operator = split[1];

            if (MetricRiskJudgeUtil.compare(maxValue, operator, riskValue)) {
                metricTask.setMetricResult(String.format("%.2f", maxValue) + "%");
                metricTask.setLadder(threshold.getLadder());
                flag = false;
                break;
            }
        }
        if (flag) {
            metricTask.setMetricResult(String.format("%.2f", maxValue) + "%");
            metricTask.setLadder(InspectionThresholdLadderEnum.NORMAL.getLadder());
        }
    }

    /**
     * 构建查询指标值的参数，获取1小时的值
     *
     * @param endpoint .
     * @param query    .
     * @return .
     */
    private MonitorIndicatorDto buildMonitorIndicatorDto(String endpoint, String query) {
        MonitorIndicatorDto monitorIndicatorDto = new MonitorIndicatorDto();
        ArrayList<String> endpoints = new ArrayList<>();
        endpoints.add(endpoint);
        monitorIndicatorDto.setEndpoints(endpoints);
        monitorIndicatorDto.setQuery(query);
        long time = System.currentTimeMillis() / 1000;
        monitorIndicatorDto.setStart(time - 60 * 60);
        monitorIndicatorDto.setEnd(time);
        return monitorIndicatorDto;
    }
}
