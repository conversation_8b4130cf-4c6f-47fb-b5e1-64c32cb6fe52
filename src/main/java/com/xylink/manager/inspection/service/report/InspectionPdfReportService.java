package com.xylink.manager.inspection.service.report;

import com.xylink.manager.inspection.entity.bo.InspectionMetricConfigBo;
import com.xylink.manager.inspection.entity.condition.InspectionItemCondition;
import com.xylink.manager.inspection.entity.db.*;
import com.xylink.manager.inspection.entity.enums.BigDataServiceEnum;
import com.xylink.manager.inspection.entity.enums.InspectionInspectTypeEnum;
import com.xylink.manager.inspection.entity.enums.InspectionItemTypeEnum;
import com.xylink.manager.inspection.entity.enums.InspectionMetricKeyEnum;
import com.xylink.manager.inspection.entity.jasperreport.*;
import com.xylink.manager.inspection.utils.jasperreport.JasperPdfUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/9 15:48
 */
@Service
@Slf4j
public class InspectionPdfReportService extends InspectionBaseReportService {
    @Value("${inspection.no.prefix:XYLINK}")
    private String noPrefix;
    private static final String TEMPLATE_PATH = "jasperReport/inspection.jasper";

    public void export(InspectInstanceDb instance, File dir) throws Exception {
        String templateId = instance.getConfigId();
        InspectionItemCondition items = templateItemDao.getItems(templateId);

        List<InspectionTaskDb> taskDbList = inspectionTaskDao.getTaskList(instance.getId());

        InspectionTaskDb systemTask = null;
        InspectionTaskDb middleTask = null;
        InspectionTaskDb serviceTask = null;

        for (InspectionTaskDb inspectionTaskDb : taskDbList) {
            if (InspectionItemTypeEnum.SYSTEM.getId() == inspectionTaskDb.getTaskType()) {
                systemTask = inspectionTaskDb;
            }
            if (InspectionItemTypeEnum.MIDDLEWARE.getId() == inspectionTaskDb.getTaskType()) {
                middleTask = inspectionTaskDb;
            }
            if (InspectionItemTypeEnum.SERVER.getId() == inspectionTaskDb.getTaskType()) {
                serviceTask = inspectionTaskDb;
            }
        }

        Map<String, Object> paramsMap = buildPdfParamsMap(instance, systemTask, middleTask, serviceTask);

        InspectionPdfFieldBean pdfFieldBean = new InspectionPdfFieldBean();
        pdfFieldBean.setHostItemTable(new JRBeanCollectionDataSource(buildHostItem(items.getSystem())));
        pdfFieldBean.setHostResultTable(new JRBeanCollectionDataSource(buildHostResult(systemTask)));
        pdfFieldBean.setMiddleItemTable(new JRBeanCollectionDataSource(buildMiddleItem(items.getMiddleware())));
        pdfFieldBean.setMiddleResultTable(new JRBeanCollectionDataSource(buildMiddleResult(middleTask)));
        pdfFieldBean.setServiceItemTable(new JRBeanCollectionDataSource(buildServiceItem(items.getServer())));
        pdfFieldBean.setServiceResultTable(new JRBeanCollectionDataSource(buildServiceResult(serviceTask)));

        String inspectionName = instance.getInspectionName();
        if (StringUtils.isNotBlank(inspectionName) && inspectionName.length() > 32) {
            inspectionName = inspectionName.substring(0, 32);
        }
        String savePath = dir.getAbsolutePath() + FILE_SEPARATOR + inspectionName + "-" + instance.getCreateTime().toString() + ".pdf";

        JasperPdfUtil.exportPdf(TEMPLATE_PATH, savePath, paramsMap, Collections.singletonList(pdfFieldBean));
        log.info("[inspection] exportPdf path: {}", savePath);
    }

    private List<ItemDataset> buildHostItem(List<String> itemIds) {
        if (CollectionUtils.isEmpty(itemIds)) {
            return null;
        }
        String itemType = "主机巡检";

        List<ItemList> itemLists = new ArrayList<>();

        InspectionSystemItemEnum[] systemItemEnums = InspectionSystemItemEnum.values();
        for (InspectionSystemItemEnum systemItemEnum : systemItemEnums) {
            String itemId = systemItemEnum.getItemId();
            boolean inspected;
            inspected = itemIds.contains(itemId);
            // 获取指标说明
            InspectionMetricConfigBo metricConfig = inspectionMetricDao.getInspectionMetricByKey(systemItemEnum.getShowName());
            String desc = "";
            if (metricConfig != null) {
                desc = metricConfig.getMetricDesc();
                desc = desc.replace("<br />", "\n");
            }
            itemLists.add(ItemList.builder()
                    .itemName(systemItemEnum.getShowName())
                    .itemDesc(desc)
                    .inspected(inspected)
                    .build());
        }
        ItemDataset itemDataset = new ItemDataset();
        itemDataset.setItemType(itemType);
        itemDataset.setItems(new JRBeanCollectionDataSource(itemLists));
        return Collections.singletonList(itemDataset);
    }

    private List<ResultList> buildHostResult(InspectionTaskDb systemTask) {
        if (systemTask == null) {
            return null;
        }
        List<ResultList> resultLists = new ArrayList<>();

        List<String> systemMetricKeys = Arrays.stream(InspectionMetricKeyEnum.values())
                .map(InspectionMetricKeyEnum::getMetricKey)
                .collect(Collectors.toList());
        Map<String, List<InspectionMetricThresholdDb>> metricIdsAndThresholds = metricThresholdDao.getMetricIdsAndThresholds(systemMetricKeys);

        String taskId = systemTask.getId();
        List<InspectionSubTaskDb> subTaskList = subTaskDao.getSubTaskList(taskId)
                .stream()
                // 过滤出有风险的项
                .filter(subTask -> 0 < (subTask.getHighRisk() + subTask.getMiddleRisk() + subTask.getLowRisk()))
                .collect(Collectors.toList());

        for (InspectionSubTaskDb subTask : subTaskList) {
            List<ResultItems> resultItems = new ArrayList<>();
            // itemName = hostName(hostIp)
            String itemName = subTask.getItemName();
            String hostName = itemName.substring(0, itemName.lastIndexOf("("));
            String hostIp = itemName.substring(itemName.lastIndexOf("(") + 1, itemName.lastIndexOf(")"));
            List<InspectionMetricTaskDb> metricTasks = inspectionMetricTaskDao.getRiskMetricTasksBySubTaskId(subTask.getId());

            // 获取每项指标的值
            for (InspectionMetricTaskDb task : metricTasks) {
                String metricKey = task.getMetricKey();
                String suggest = inspectionMetricDao.getSuggestByMetricKey(metricKey);
                // 硬盘没有元数据检查不需要特殊处理
                if (InspectionMetricKeyEnum.HOST_DISK_USAGE.getMetricKey().equals(metricKey)) {
                    String result = task.getMetricResult();
                    if (UNKNOWN_METRIC_VALUE.equals(result)) {
                        ResultItems resultItem = new ResultItems();
                        resultItem.setResult(result);
                        resultItem.setMetricName(metricKey);
                        resultItem.setItemName(hostName);
                        resultItem.setHostIp(hostIp);
                        resultItem.setLadder(task.getLadder());
                        resultItem.setDesc(suggest);
                        resultItems.add(resultItem);
                    } else {
                        ResultItems resultItem = new ResultItems();
                        resultItem.setResult("最大使用率" + result);
                        resultItem.setMetricName(metricKey);
                        resultItem.setItemName(hostName);
                        resultItem.setHostIp(hostIp);
                        resultItem.setLadder(task.getLadder());
                        resultItem.setDesc(suggest);
                        resultItems.add(resultItem);
                    }
                    continue;
                }

                // 需要从 CPU使用率 和 内存使用率 中取出元数据检查信息
                if (InspectionMetricKeyEnum.HOST_CPU_USAGE.getMetricKey().equals(metricKey)
                        || InspectionMetricKeyEnum.HOST_MEM_USAGE.getMetricKey().equals(metricKey)) {
                    String result = task.getMetricResult();
                    if (result.startsWith(UNKNOWN_METRIC_VALUE)) {
                        buildUnknownValueItem(resultItems, hostName, hostIp, task);
                    } else {
                        String usageRateString = result.substring(0, result.lastIndexOf("%"));
                        String requestMatchingValue = "";
                        if (result.contains("(")) {
                            requestMatchingValue = result.substring(result.lastIndexOf("(") + 1, result.lastIndexOf(")"));
                        }
                        if (InspectionMetricKeyEnum.HOST_CPU_USAGE.getMetricKey().equals(metricKey)) {
                            buildHostCpuResultItem(metricIdsAndThresholds,resultItems, hostName, hostIp, metricKey, usageRateString, requestMatchingValue);
                        }
                        if (InspectionMetricKeyEnum.HOST_MEM_USAGE.getMetricKey().equals(metricKey)) {
                            buildHostMemResultItem(metricIdsAndThresholds, resultItems, hostName, hostIp, metricKey, usageRateString, requestMatchingValue);
                        }
                    }
                }
            }

            if (!CollectionUtils.isEmpty(resultItems)) {
                resultItems.removeIf(item -> item.getLadder() == 0);
                resultItems.sort(Comparator.comparing(ResultItems::getLadder).reversed());
                ResultList resultList = new ResultList();
                resultList.setResultItems(new JRBeanCollectionDataSource(resultItems));
                resultLists.add(resultList);
            }
        }

        return resultLists;
    }

    private List<ItemDataset> buildMiddleItem(List<String> middleware) {
        if (CollectionUtils.isEmpty(middleware)) {
            return null;
        }

        InspectionMiddleItemEnum[] values = InspectionMiddleItemEnum.values();
        List<ItemDataset> itemDatasets = new ArrayList<>();

        for (InspectionMiddleItemEnum value : values) {
            ItemDataset itemDataset = new ItemDataset();
            List<ItemList> itemLists = new ArrayList<>();
            boolean inspected = middleware.contains(value.getItemId());
            if(!inspected) {
                continue;
            }
            // 特殊处理大数据巡检项展示，只展示巡检组件的内容
            if (value.equals(InspectionMiddleItemEnum.BIGDATA)) {
                List<String> bigDataDeployedServices = Arrays.asList("hdfs", "kafka", "hbase", "zookeeper", "phoenix", "yarn");
                for (String bigdata : bigDataDeployedServices) {
                    BigDataServiceEnum bigDataServiceEnum = BigDataServiceEnum.valueOfModule(bigdata);
                    if (bigDataServiceEnum != null) {
                        itemLists.add(ItemList.builder()
                                .itemName(bigdata)
                                .inspected(inspected)
                                .itemDesc(bigDataServiceEnum.getMessage())
                                .build());
                    }
                }
            } else {
                List<InspectionMetricConfigDb> inspectionMetricByItemId = inspectionMetricDao.getInspectionMetricByItemId(value.getItemId());
                for (InspectionMetricConfigDb metricConfigDb : inspectionMetricByItemId) {
                    itemLists.add(ItemList.builder()
                            .itemName(metricConfigDb.getMetricKey())
                            .inspected(inspected)
                            .itemDesc(metricConfigDb.getMetricDesc().replace("<br />", "\n"))
                            .build());
                }
            }
            itemDataset.setItemType(value.getShowName());
            itemDataset.setItems(new JRBeanCollectionDataSource(itemLists));
            itemDatasets.add(itemDataset);
        }

        return itemDatasets;
    }

    private List<ResultList> buildMiddleResult(InspectionTaskDb middleTask) {
        if (middleTask == null) {
            return null;
        }
        List<ResultList> resultLists = new ArrayList<>();

        String taskId = middleTask.getId();
        List<InspectionSubTaskDb> subTasks = subTaskDao.getSubTaskList(taskId)
                .stream()
                // 过滤出有风险的项
                .filter(subTask -> 0 < (subTask.getHighRisk() + subTask.getMiddleRisk() + subTask.getLowRisk()))
                .collect(Collectors.toList());

        for (InspectionSubTaskDb subTask : subTasks) {
            List<ResultItems> resultItems = new ArrayList<>();
            String itemName = subTask.getItemName();
            List<InspectionMetricTaskDb> metricTasks = inspectionMetricTaskDao.getRiskMetricTasksBySubTaskId(subTask.getId());
            for (InspectionMetricTaskDb metricTask : metricTasks) {
                String metricKey = metricTask.getMetricKey();
                String suggest = inspectionMetricDao.getSuggestByMetricKey(metricKey);
                ResultItems resultItem = new ResultItems();
                resultItem.setResult(metricTask.getMetricResult());
                resultItem.setMetricName(metricKey);
                resultItem.setItemName(itemName);
                resultItem.setLadder(metricTask.getLadder());
                resultItem.setDesc(suggest);
                resultItems.add(resultItem);
            }
            if (!CollectionUtils.isEmpty(resultItems)) {
                resultItems.removeIf(item -> item.getLadder() == 0);
                resultItems.sort(Comparator.comparingInt(ResultItems::getLadder).reversed());
                ResultList resultList = new ResultList();
                resultList.setResultItems(new JRBeanCollectionDataSource(resultItems));
                resultLists.add(resultList);
            }
        }
        return resultLists;
    }

    private List<ItemDataset> buildServiceItem(List<String> server) {
        if (CollectionUtils.isEmpty(server)) {
            return null;
        }
        InspectionServiceItemEnum[] values = InspectionServiceItemEnum.values();
        List<ItemDataset> itemDatasets = new ArrayList<>();
        ItemDataset itemDataset = new ItemDataset();
        List<ItemList> itemLists = new ArrayList<>();
        for (InspectionServiceItemEnum value : values) {
            itemLists.add(ItemList.builder()
                    .itemName(value.getShowName())
                    .inspected(server.contains(value.getItemId()))
                    .itemDesc(value.getDesc())
                    .build());
        }
        itemDataset.setItemType("服务巡检");
        itemDataset.setItems(new JRBeanCollectionDataSource(itemLists));
        itemDatasets.add(itemDataset);
        return itemDatasets;
    }

    private List<ResultList> buildServiceResult(InspectionTaskDb serviceTask) {
        if (serviceTask == null) {
            return null;
        }
        List<ResultList> resultLists = new ArrayList<>();

        String taskId = serviceTask.getId();
        List<InspectionSubTaskDb> subTasks = subTaskDao.getSubTaskList(taskId)
                .stream()
                // 过滤出有风险的项
                .filter(subTask -> 0 < (subTask.getHighRisk() + subTask.getMiddleRisk() + subTask.getLowRisk()))
                .collect(Collectors.toList());

        for (InspectionSubTaskDb subTask : subTasks) {
            List<ResultItems> resultItems = new ArrayList<>();
            // itemName = serverName|podName|ident
            String itemName = subTask.getItemName();
            String[] split = itemName.split("\\|");
            String serverName = split[0];
            String podName = split[1];
            String ident = split[2];
            List<InspectionMetricTaskDb> metricTasks = inspectionMetricTaskDao.getRiskMetricTasksBySubTaskId(subTask.getId());
            for (InspectionMetricTaskDb metricTask : metricTasks) {
                String metricKey = metricTask.getMetricKey();
                String suggest = inspectionMetricDao.getSuggestByMetricKey(metricKey);
                ResultItems resultItem = new ResultItems();
                resultItem.setResult(metricTask.getMetricResult());
                resultItem.setMetricName(metricKey);
                resultItem.setItemName(serverName);
                resultItem.setPodName(podName);
                resultItem.setServiceIdent(ident);
                resultItem.setLadder(metricTask.getLadder());
                resultItem.setDesc(suggest);
                resultItems.add(resultItem);
            }
            if (!CollectionUtils.isEmpty(resultItems)) {
                resultItems.removeIf(item -> item.getLadder() == 0);
                resultItems.sort(Comparator.comparingInt(ResultItems::getLadder).reversed());
                ResultList resultList = new ResultList();
                resultList.setResultItems(new JRBeanCollectionDataSource(resultItems));
                resultLists.add(resultList);
            }
        }
        return resultLists;
    }

    private void buildUnknownValueItem(List<ResultItems> resultItems,
                                              String hostName,
                                              String hostIp,
                                              InspectionMetricTaskDb task) {
        ResultItems resultItem1 = new ResultItems();
        resultItem1.setResult(task.getMetricResult());
        resultItem1.setMetricName(task.getMetricKey());
        resultItem1.setItemName(hostName);
        resultItem1.setHostIp(hostIp);
        resultItem1.setLadder(task.getLadder());
        resultItem1.setDesc(inspectionMetricDao.getSuggestByMetricKey(task.getMetricKey()));
        resultItems.add(resultItem1);
    }

    private void buildHostMemResultItem(Map<String, List<InspectionMetricThresholdDb>> metricIdsAndThresholds,
                                        List<ResultItems> resultItems,
                                        String hostName,
                                        String hostIp,
                                        String metricKey,
                                        String usageRateString,
                                        String requestMatchingValue) {
        int usageRateLadder = systemMetricRiskJudge(metricIdsAndThresholds, usageRateString, InspectionMetricKeyEnum.HOST_MEM_USAGE.getMetricKey());
        ResultItems resultItem1 = new ResultItems();
        resultItem1.setResult("最大使用率" + usageRateString + "%");
        resultItem1.setMetricName(metricKey);
        resultItem1.setItemName(hostName);
        resultItem1.setHostIp(hostIp);
        resultItem1.setLadder(usageRateLadder);
        resultItem1.setDesc(inspectionMetricDao.getSuggestByMetricKey(metricKey));
        resultItems.add(resultItem1);
    }

    private void buildHostCpuResultItem(Map<String, List<InspectionMetricThresholdDb>> metricIdsAndThresholds,
                                        List<ResultItems> resultItems,
                                        String hostName, String hostIp,
                                        String metricKey,
                                        String usageRateString,
                                        String requestMatchingValue) {
        int usageRateLadder = systemMetricRiskJudge(metricIdsAndThresholds, usageRateString, InspectionMetricKeyEnum.HOST_CPU_USAGE.getMetricKey());
        ResultItems resultItem1 = new ResultItems();
        resultItem1.setResult("最大使用率" + usageRateString + "%");
        resultItem1.setMetricName(metricKey);
        resultItem1.setItemName(hostName);
        resultItem1.setHostIp(hostIp);
        resultItem1.setLadder(usageRateLadder);
        resultItem1.setDesc(inspectionMetricDao.getSuggestByMetricKey(metricKey));
        resultItems.add(resultItem1);
    }

    private Map<String, Object> buildPdfParamsMap(InspectInstanceDb instance,
                                                  InspectionTaskDb systemTask,
                                                  InspectionTaskDb middleTask,
                                                  InspectionTaskDb serviceTask) {
        InspectionPdfParametersBean parametersBean = new InspectionPdfParametersBean();
        parametersBean.setName(instance.getInspectionName());
        parametersBean.setCreateTime(formatDateString(instance.getCreateTime()));
        parametersBean.setType(Objects.requireNonNull(InspectionInspectTypeEnum.valueOf(instance.getJobType())).getName());
        parametersBean.setCreateUser(instance.getCreateUser());
        parametersBean.setConsumedTime(formatConsumedString(instance.getConsumedTime()));
        parametersBean.setNo(generateInspectionNo(instance));
        if (systemTask != null) {
            parametersBean.setHostHighRisk(systemTask.getHighRisk());
            parametersBean.setHostMiddleRisk(systemTask.getMiddleRisk());
            parametersBean.setHostLowRisk(systemTask.getLowRisk());
            parametersBean.setShowHost(true);
        }
        if (middleTask != null) {
            parametersBean.setMiddleHighRisk(middleTask.getHighRisk());
            parametersBean.setMiddleMiddleRisk(middleTask.getMiddleRisk());
            parametersBean.setMiddleLowRisk(middleTask.getLowRisk());
            parametersBean.setShowMiddle(true);
        }
        if (serviceTask != null) {
            parametersBean.setServiceHighRisk(serviceTask.getHighRisk());
            parametersBean.setServiceMiddleRisk(serviceTask.getMiddleRisk());
            parametersBean.setServiceLowRisk(serviceTask.getLowRisk());
            parametersBean.setShowService(true);
        }

        return parametersBean.beanTransferParamMap();
    }

    private String generateInspectionNo(InspectInstanceDb instance) {
        Long createTime = instance.getCreateTime();
        String time = DateFormatUtils.format(createTime, "yyyyMMddHHmmss");
        Integer jobType = instance.getJobType();
        String type = String.format("%02d", jobType);
        return "XJ-" + noPrefix + "-" + time + type;
    }
}