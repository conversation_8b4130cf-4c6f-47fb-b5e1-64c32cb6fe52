package com.xylink.manager.inspection.service.report;

import com.xylink.manager.inspection.common.OpsManagerException;
import com.xylink.manager.inspection.dao.InspectionExportRecordDao;
import com.xylink.manager.inspection.dao.InspectionInstanceDao;
import com.xylink.manager.inspection.entity.db.InspectInstanceDb;
import com.xylink.manager.inspection.entity.db.InspectionExportRecordDb;
import com.xylink.manager.inspection.entity.enums.InspectionJobStatusEnum;
import com.xylink.manager.inspection.entity.enums.InspectionRecordStateEnum;
import com.xylink.util.ZipUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DefaultDataBuffer;
import org.springframework.core.io.buffer.DefaultDataBufferFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;
import reactor.core.publisher.Mono;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.Map;
import java.util.Vector;
import java.util.zip.ZipOutputStream;

import static com.xylink.manager.inspection.utils.ExecutorServiceUtil.INSPECION_EXPROT_EXECUOR;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/3 11:04
 */
@Service
@Slf4j
public class InspectionReportService {
    @Value("${base.dir:/mnt/xylink}")
    protected String basePath;

    protected static final String FILE_SEPARATOR = File.separator;

    @Autowired
    private InspectionInstanceDao inspectionInstanceDao;
    @Autowired
    private InspectionPdfReportService pdfReportService;
    @Autowired
    private InspectionExcelReportService excelReportService;
    @Autowired
    private InspectionExportRecordDao exportRecordDao;

    public static final String INSPECTION_REPORT_KEY = "inspection_export_key";

    public static final int DEFAULT_INSPECTION_NAME_SIZE = 32;

    public Map<String, Object> exportState(String instanceId, boolean export) {
        InspectInstanceDb instance = inspectionInstanceDao.getInstanceById(instanceId);

        if (instance == null) {
            throw new OpsManagerException(HttpStatus.BAD_REQUEST, "巡检不存在");
        }

        if (InspectionJobStatusEnum.COMPLETED.getStatus() != instance.getJobStatus()) {
            throw new OpsManagerException(HttpStatus.BAD_REQUEST, "巡检未结束或巡检任务异常");
        }

        HashMap<String, Object> result = new HashMap<>(1);
        InspectionExportRecordDb record = exportRecordDao.getRecordByInstanceId(instanceId);

        // 当导出记录为空、文件已删除、导出失败，都返回文件状态 0 文件不存在
        if (record == null
                || InspectionRecordStateEnum.DELETED.getState() == record.getState()
                || InspectionRecordStateEnum.FAIL.getState() == record.getState()) {
            result.put("state", 0);
            // 当需要导出时，返回文件状态为导出中
            if (export) {
                result.put("state", 1);
                exportReport(instance);
            }
            return result;
        }

        int state = record.getState();
        if (InspectionRecordStateEnum.EXPORTED.getState() == state) {
            if (export) {
                throw new OpsManagerException(HttpStatus.BAD_REQUEST, "该导出任务已存在");
            }
            result.put("state", 2);
            return result;
        }

        if (InspectionRecordStateEnum.EXPORTING.getState() == state) {
            if (export) {
                throw new OpsManagerException(HttpStatus.BAD_REQUEST, "该导出任务已存在");
            }
            result.put("state", 1);
            return result;
        }

        return result;
    }

    public void exportReport(InspectInstanceDb instance) {
        try {
            INSPECION_EXPROT_EXECUOR.submit(() -> {
                log.info("[inspection] export, inspectionId: {}", instance.getId());
                exportInspectionReport(instance);
            });
        } catch (Exception e) {
            log.error("[inspection] export error, instanceId: {}", instance.getId(), e);
            throw new OpsManagerException(HttpStatus.INTERNAL_SERVER_ERROR, "服务异常");
        }
    }

    public void exportInspectionReport(InspectInstanceDb instance) {
        String instanceId = instance.getId();
        String inspectionName = instance.getInspectionName();
        if (StringUtils.isNotBlank(inspectionName) && inspectionName.length() > DEFAULT_INSPECTION_NAME_SIZE) {
            inspectionName = inspectionName.substring(0, DEFAULT_INSPECTION_NAME_SIZE);
        }
        String saveDir = basePath + FILE_SEPARATOR + "inspection" + FILE_SEPARATOR + inspectionName + "-" + instance.getCreateTime().toString();
        InspectionExportRecordDb record = exportRecordDao.getRecordByInstanceId(instanceId);
        if (record == null) {
            exportRecordDao.insert(instanceId, InspectionRecordStateEnum.EXPORTING.getState());
        } else {
            exportRecordDao.updateRecord(instanceId, InspectionRecordStateEnum.EXPORTING.getState(), "");
        }
        try {
            File dir = new File(saveDir);
            if (!dir.exists()) {
                boolean mkdirs = dir.mkdirs();
                log.info("create new dir {}, {}", saveDir, mkdirs);
            }
            pdfReportService.export(instance, dir);
            excelReportService.export(instance, dir);

            File zipFile = dirToZip(saveDir);
            if (zipFile != null && zipFile.exists()) {
                boolean delete = dir.delete();
                log.info("[inspection] delete dir {}, {}", dir.getPath(), delete);
                log.info("[inspection] export file success. file path: {}", zipFile.getPath());
                exportRecordDao.updateRecord(instanceId, InspectionRecordStateEnum.EXPORTED.getState(), zipFile.getAbsolutePath());
            } else {
                log.error("[inspection] export file fail, zip file is empty. instanceId: {}", instanceId);
                exportRecordDao.updateRecord(instanceId, InspectionRecordStateEnum.FAIL.getState(), "");
            }
        } catch (Exception e) {
            log.error("[inspection] export file error, instanceId: {}", instanceId, e);
            exportRecordDao.updateFailRecord(instanceId, InspectionRecordStateEnum.FAIL.getState(), e.getMessage());
        }

    }

    public static File dirToZip(String dirPath) {
        File dir = new File(dirPath);
        if (!dir.exists()) {
            throw new OpsManagerException("压缩的目标目录不存在!");
        }
        Vector<File> fileVector = getAllDirFiles(dir);
        String filePath = dirPath + ".zip";
        //用于压缩文件
        ZipOutputStream zos = null;
        try {
            File zipFile = new File(filePath);
            if (!zipFile.exists()) {
                zipFile.createNewFile();
            }
            zos = new ZipOutputStream(Files.newOutputStream(zipFile.toPath()));
            //循环文件，一个一个按顺序压缩
            for (File file : fileVector) {
                ZipUtil.zipFileFun(file, dir.getPath(), zos);
            }
            for (File file : fileVector) {
                boolean delete = file.delete();
                log.info("[inspection] delete export file {}, {}", file.getPath(), delete);
            }
            return zipFile;
        } catch (Exception e) {
            log.info("dirToZip error:{}", e.getMessage(), e);
        } finally {
            try {
                if (zos != null) {
                    zos.closeEntry();
                    zos.close();
                }
            } catch (Exception ex) {
                log.info("dirToZip close zos error:{}", ex.getMessage(), ex);
            }
        }
        return null;
    }

    private static Vector<File> getAllDirFiles(File dir) {
        Vector<File> fileVector = new Vector<>();
        File[] subFile = dir.listFiles();
        if (subFile == null || subFile.length == 0) {
            return new Vector<>();
        }
        for (File item : subFile) {
            if (!item.isDirectory()) {
                //不是文件夹并且不为空，装载文件
                fileVector.add(item);
            } else {
                //是文件夹，递归遍历文件装载
                fileVector.addAll(ZipUtil.getPathAllFiles(item, new Vector<>()));
            }
        }
        return fileVector;
    }


    public void export(String instanceId, HttpServletResponse response) {
        InspectionExportRecordDb record = exportRecordDao.getRecordByInstanceId(instanceId);
        if (record == null) {
            throw new OpsManagerException(HttpStatus.INTERNAL_SERVER_ERROR, "报告文件不存在");
        }
        if (record.getState() != InspectionRecordStateEnum.EXPORTED.getState()) {
            log.error("[inspection] export state {}", record.getState());
            throw new OpsManagerException(HttpStatus.INTERNAL_SERVER_ERROR, "报告文件不存在或报告正在导出");
        }
        String filePath = record.getFilePath();
        File file = new File(filePath);
        if (!file.exists()) {
            throw new OpsManagerException(HttpStatus.INTERNAL_SERVER_ERROR, "报告文件不存在");
        }
        String fileName = file.getName();
        try {
            response.addHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + UriUtils.encode(fileName, "UTF-8"));
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM.getType());
            InputStream in = new FileInputStream(file); // 文件输入流
            int len;
            byte[] buffer = new byte[1024];
            OutputStream out = response.getOutputStream();
            while((len = in.read(buffer))>0){
                out.write(buffer,0,len);    // 将缓冲区的数据输出到客户端浏览器
            }
            in.close();
        } catch (IOException ex) {
            log.error("download file error!", ex);
            throw new OpsManagerException(HttpStatus.INTERNAL_SERVER_ERROR, "download file error!");
        }
    }
}
