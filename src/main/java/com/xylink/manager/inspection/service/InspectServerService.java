package com.xylink.manager.inspection.service;

import com.xylink.config.Constants;
import com.xylink.config.VodnetworkConstants;
import com.xylink.manager.inspection.common.OpsManagerException;
import com.xylink.manager.inspection.dao.*;
import com.xylink.manager.inspection.entity.Constant;
import com.xylink.manager.inspection.entity.bo.InspectPodAvailableBo;
import com.xylink.manager.inspection.entity.bo.InspectPodInfoBo;
import com.xylink.manager.inspection.entity.bo.InspectionMetricConfigBo;
import com.xylink.manager.inspection.entity.bo.ServerInspectResultBo;
import com.xylink.manager.inspection.entity.db.*;
import com.xylink.manager.inspection.entity.enums.InspectionItemTypeEnum;
import com.xylink.manager.inspection.entity.enums.InspectionMetricKeyEnum;
import com.xylink.manager.inspection.entity.enums.InspectionServiceLadder;
import com.xylink.manager.inspection.entity.enums.InspectionThresholdLadderEnum;
import com.xylink.manager.inspection.entity.vo.ServerInstanceVO;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.cache.bean.*;
import com.xylink.manager.service.cache.service.ICacheService;
import com.xylink.util.UUIDGenerator;
import io.fabric8.kubernetes.api.model.ContainerState;
import io.fabric8.kubernetes.api.model.ContainerStateTerminated;
import io.fabric8.kubernetes.api.model.ContainerStateWaiting;
import io.fabric8.kubernetes.api.model.IntOrString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/13 16:33
 */
@Service
@Slf4j
public class InspectServerService {
    @Value("#{'${inspection.service.deploy-status-ignore:tiangong,front-tiangong,tiangong-mid,noah,nettool}'.split(',')}")
    private Set<String> alwaysDeployApp;
    private static final String SERVICE_VODNETWORK_VOD = "private-vodnetwork-vod";
    private static final String SERVICE_VODNETWORK_VODEDIT = "private-vodnetwork-vodedit";
    @Autowired
    private K8sService k8sService;
    @Autowired
    private InspectionServiceResultDao inspectionServiceResultDao;
    @Autowired
    private InspectionServiceListDao serviceListDao;
    @Autowired
    private InspectionMetricTaskDao metricTaskDao;
    @Autowired
    private ServiceInspection serviceInspection;
    @Autowired
    private InspectionSubTaskDao subTaskDao;
    @Autowired
    private InspectionMetricDao metricDao;

    public static final Integer INSPECT_TYPE_REST = 0;

    public static final String POD_RUNNING_STATUS = "Running";
    @Autowired
    private ICacheService cacheService;

    public List<InspectionSubTaskDb> execInspection(String taskId, List<String> itemConfigIds) {
        long startTime = System.currentTimeMillis();
        // 目前只有一项，直接取出该项
        String itemId = itemConfigIds.get(0);

        List<InspectPodInfoBo> inspectPodInfos = getInspectPodInfoList();

        List<ServerInspectResultBo> serverInspectResultBos = Collections.synchronizedList(new ArrayList<>());

        // pod 状态检查
        agentLive(inspectPodInfos, serverInspectResultBos);

        // 服务可用性检查
        execServiceInspection(taskId, inspectPodInfos, serverInspectResultBos);

        // POD 重启次数
        restartCount(inspectPodInfos, serverInspectResultBos);

        List<InspectionSubTaskDb> subTaskDbs = transferSubTaskList(taskId, startTime, serverInspectResultBos);
        subTaskDao.saveSubTasks(subTaskDbs);
        return subTaskDbs;
    }

    /**
     * POD 重启次数
     */
    private void restartCount(List<InspectPodInfoBo> inspectPodInfos, List<ServerInspectResultBo> serverInspectResultBos) {
        long currentTimeMillis = System.currentTimeMillis();
        inspectPodInfos.parallelStream().forEach(inspectPodInfo -> {
            ServerInstanceVO serverInstanceVO = inspectPodInfo.getServerInstanceVO();
            if (serverInstanceVO == null) {
                return;
            }
            Integer restarts = serverInstanceVO.getRestarts();
            // TODO: 考虑重启次数超过多少次再进行设置
            if (restarts == null || restarts <= 0) {
                return;
            }
            serverInspectResultBos.add(ServerInspectResultBo.builder()
                    .appId(inspectPodInfo.getAppId())
                    .serverName(inspectPodInfo.getAppName())
                    .podName(inspectPodInfo.getPodName())
                    .ident(inspectPodInfo.getIdent())
                    .createTime(currentTimeMillis)
                    .fishTime(currentTimeMillis)
                    .metricKey(InspectionMetricKeyEnum.SERVER_RESTART_COUNT.getMetricKey())
                    .ladder(InspectionThresholdLadderEnum.LOW_RISK.getLadder())
                    .result("POD重启" + restarts + "次")
                    .build());
        });
    }

    /**
     * serverInspectResultBos 转换为 subTaskList
     *
     * @param taskId                 .
     * @param startTime              .
     * @param serverInspectResultBos .
     * @return .
     */
    private List<InspectionSubTaskDb> transferSubTaskList(String taskId,
                                                          long startTime,
                                                          List<ServerInspectResultBo> serverInspectResultBos) {
        // 根据 appName podName ident 分组
        Map<String, List<ServerInspectResultBo>> itemNameToResultList = serverInspectResultBos.stream()
                .collect(Collectors.groupingBy(serverInspectResultBo ->
                        buildItemName(serverInspectResultBo.getServerName(),
                                serverInspectResultBo.getPodName(),
                                serverInspectResultBo.getIdent()))
                );
        List<InspectionSubTaskDb> subTaskList = new ArrayList<>();
        itemNameToResultList.forEach((itemName, resultList) -> subTaskList.add(transferSubTask(taskId, startTime, itemName, resultList)));
        return subTaskList;
    }

    /**
     * 构建 subTaskDb 并保存 result 结果
     *
     * @param taskId     .
     * @param startTime  .
     * @param itemName   .
     * @param resultList .
     * @return .
     */
    private InspectionSubTaskDb transferSubTask(String taskId,
                                                long startTime,
                                                String itemName,
                                                List<ServerInspectResultBo> resultList) {
        InspectionSubTaskDb subTask = new InspectionSubTaskDb();
        String subTaskId = UUIDGenerator.generate();
        subTask.setId(subTaskId);
        subTask.setTaskId(taskId);
        subTask.setItemName(itemName);
        subTask.setCreateTime(startTime);
        subTask.setTaskType(InspectionItemTypeEnum.SERVER.getId());

        int highRisk = 0;
        int middleRisk = 0;
        int lowRisk = 0;
        long finishTime = 0;
        String exceptionDesc = null;

        List<InspectionMetricTaskDb> metricTaskDbList = new ArrayList<>();
        for (ServerInspectResultBo result : resultList) {
            int ladder = result.getLadder();
            long fishTime = result.getFishTime();
            InspectionMetricTaskDb metricTaskDb = new InspectionMetricTaskDb();
            metricTaskDb.setSubTaskId(subTaskId);
            metricTaskDb.setId(UUIDGenerator.generate());
            metricTaskDb.setLadder(ladder);
            metricTaskDb.setMetricResult(result.getResult());
            metricTaskDb.setMetricKey(result.getMetricKey());
            metricTaskDb.setFinishedTime(fishTime);
            metricTaskDb.setCreateTime(result.getCreateTime());

            if (ladder == InspectionThresholdLadderEnum.HIGH_RISK.getLadder()) {
                highRisk++;
            }
            if (ladder == InspectionThresholdLadderEnum.MIDDLE_RISK.getLadder()) {
                middleRisk++;
            }
            if (ladder == InspectionThresholdLadderEnum.LOW_RISK.getLadder()) {
                lowRisk++;
            }

            if (finishTime < fishTime) {
                finishTime = fishTime;
            }
            // 服务探活指标需要特殊处理
            if (InspectionMetricKeyEnum.SERVER_AGENT_LIVE.getMetricKey().equals(result.getMetricKey())) {
                // 服务探活指标需要置顶
                metricTaskDb.setIsTop(0);
                // 服务探活指标结果为高风险，保存 metric 的异常描述为 result 结果
                exceptionDesc = InspectionThresholdLadderEnum.HIGH_RISK.getLadder().equals(result.getLadder()) ? result.getResult() : null;
                metricTaskDb.setExceptionDesc(exceptionDesc);
            }
            metricTaskDbList.add(metricTaskDb);
        }
        // result 保存到 metric task 表中
        metricTaskDao.saveMetricTasks(metricTaskDbList);

        subTask.setHighRisk(highRisk);
        subTask.setMiddleRisk(middleRisk);
        subTask.setLowRisk(lowRisk);
        subTask.setFinishedTime(finishTime);
        // 如果存在服务探活异常结果则保存
        subTask.setExceptionDesc(exceptionDesc);
        return subTask;
    }

    private void execServiceInspection(String taskId,
                                       List<InspectPodInfoBo> inspectPodInfos,
                                       List<ServerInspectResultBo> serverInspectResultBos) {
        long startTime = System.currentTimeMillis();
        // 过滤出所有未运行的服务，直接设置结果为失败状态
        List<InspectPodInfoBo> notRunningList = inspectPodInfos.stream()
                .filter(InspectPodInfoBo::isSupportInspection)
                .filter(inspectPodInfo -> Objects.isNull(inspectPodInfo.getServerInstanceVO()))
                .collect(Collectors.toList());
        buildFailedInspectResult(startTime, notRunningList, serverInspectResultBos);

        // 过滤出支持巡检，且运行中的 pod
        List<InspectPodInfoBo> supportInspectPodList = inspectPodInfos.stream()
                .filter(InspectPodInfoBo::isSupportInspection)
                .filter(inspectPodInfoBo -> inspectPodInfoBo.getServerInstanceVO() != null)
                .collect(Collectors.toList());
        List<InspectPodAvailableBo> availableBos = Collections.synchronizedList(new ArrayList<>());
        supportInspectPodList.parallelStream().forEach(podInfo -> availableBos.add(buildPodAvailableBo(podInfo)));
        try {
            // 调用 服务可用性检查接口 等待接口完成
            serviceInspection.inspection(taskId, availableBos);
            // 读取 inspection_task 表
            List<InspectionServiceResultDB> serviceResultDbs = inspectionServiceResultDao.getResultListByInspectionId(taskId);
            if (CollectionUtils.isEmpty(serviceResultDbs)) {
                throw new OpsManagerException("serviceResultDbs is empty");
            }
            transferInspectResult(startTime, serviceResultDbs, supportInspectPodList, serverInspectResultBos);
        } catch (Exception e) {
            log.error("[inspection] inspect service availability error", e);
            buildFailedInspectResult(startTime, supportInspectPodList, serverInspectResultBos);
        }
    }

    private void transferInspectResult(long startTime,
                                       List<InspectionServiceResultDB> serviceResultDbs,
                                       List<InspectPodInfoBo> supportInspectPodList,
                                       List<ServerInspectResultBo> serverInspectResultBos) {
        long endTime = System.currentTimeMillis();

        Map<String, InspectPodInfoBo> serverPodToInfo = new HashMap<>();
        for (InspectPodInfoBo podInfo : supportInspectPodList) {
            serverPodToInfo.put(podInfo.getIdent() + podInfo.getPodName(), podInfo);
        }

        serviceResultDbs.parallelStream().forEach(result -> {
            String serviceName = result.getServiceName();
            String podName = result.getPodName();
            InspectPodInfoBo podInfoBo = serverPodToInfo.get(serviceName + podName);

            // 可用性检查结果转换为 巡检风险等级
            int ladder = InspectionThresholdLadderEnum.EXCEPTION.getLadder();
            int inspectionValue = result.getInspectionValue();

            if (inspectionValue == InspectionServiceLadder.NORMAL.getValue()) {
                ladder = InspectionThresholdLadderEnum.NORMAL.getLadder();
            } else if (inspectionValue == InspectionServiceLadder.EXCEPTION.getValue()) {
                ladder = InspectionThresholdLadderEnum.HIGH_RISK.getLadder();
            } else if (inspectionValue == InspectionServiceLadder.RISK.getValue()) {
                ladder = InspectionThresholdLadderEnum.MIDDLE_RISK.getLadder();
            }

            // Item 存的是编码，将它转换为 metricKey
            String metricKey = result.getInspectionItem();
            if (StringUtils.isBlank(metricKey)) {
                // 为空展示为服务探活
                metricKey = InspectionMetricKeyEnum.SERVER_INSPECTION.getMetricKey();
            } else {
                InspectionMetricConfigBo metricConfig = metricDao.getInspectionMetricByCode(metricKey);
                // 找到 metricConfig 替换名称
                if (metricConfig != null && StringUtils.isNotBlank(metricConfig.getMetricKey())) {
                    metricKey = metricConfig.getMetricKey();
                }
            }
            serverInspectResultBos.add(ServerInspectResultBo.builder()
                    .appId(podInfoBo.getAppId())
                    .serverName(podInfoBo.getAppName())
                    .podName(podInfoBo.getPodName())
                    .ident(podInfoBo.getIdent())
                    .metricKey(metricKey)
                    .ladder(ladder)
                    .result(result.getDescription())
                    .createTime(startTime)
                    .fishTime(endTime)
                    .build());

        });
    }


    private void buildFailedInspectResult(long startTime,
                                          List<InspectPodInfoBo> supportInspectPodList,
                                          List<ServerInspectResultBo> serverInspectResultBos) {
        long endTime = System.currentTimeMillis();
        supportInspectPodList.parallelStream().forEach(podInfoBo ->
                serverInspectResultBos.add(ServerInspectResultBo.builder()
                        .appId(podInfoBo.getAppId())
                        .serverName(podInfoBo.getAppName())
                        .podName(podInfoBo.getPodName())
                        .ident(podInfoBo.getIdent())
                        .metricKey(InspectionMetricKeyEnum.SERVER_INSPECTION.getMetricKey())
                        .result("服务可用性检查失败")
                        .ladder(InspectionThresholdLadderEnum.HIGH_RISK.getLadder())
                        .createTime(startTime)
                        .fishTime(endTime)
                        .build()));
    }


    private void agentLive(List<InspectPodInfoBo> inspectPodInfos, List<ServerInspectResultBo> serverInspectResultBos) {
        long currentTimeMillis = System.currentTimeMillis();
        inspectPodInfos.parallelStream().forEach(inspectPodInfo -> {
            ServerInstanceVO serverInstanceVO = inspectPodInfo.getServerInstanceVO();
            if (!inspectPodInfo.isDeployed()) {
                // 不再部署列表中，且启动的任务保存为低风险，未启动的忽略
                if (serverInstanceVO != null) {
                    String status = serverInstanceVO.getStatus();
                    int ladder;
                    String result;
                    if (POD_RUNNING_STATUS.equals(serverInstanceVO.getStatus())) {
                        ladder = InspectionThresholdLadderEnum.LOW_RISK.getLadder();
                        result = "服务正在运行，但该服务不在部署列表中";
                    } else {
                        ladder = InspectionThresholdLadderEnum.HIGH_RISK.getLadder();
                        result = "服务处于" + status + "状态，且该服务不在部署列表中";
                    }
                    serverInspectResultBos.add(ServerInspectResultBo.builder()
                            .appId(inspectPodInfo.getAppId())
                            .serverName(inspectPodInfo.getAppName())
                            .podName(inspectPodInfo.getPodName())
                            .ident(inspectPodInfo.getIdent())
                            .createTime(currentTimeMillis)
                            .fishTime(currentTimeMillis)
                            .metricKey(InspectionMetricKeyEnum.SERVER_AGENT_LIVE.getMetricKey())
                            .ladder(ladder)
                            .result(result)
                            .build());
                }
            } else {
                int ladder;
                String result;
                if (serverInstanceVO == null) {
                    ladder = InspectionThresholdLadderEnum.HIGH_RISK.getLadder();
                    result = "服务未启动";
                } else {
                    String status = serverInstanceVO.getStatus();
                    // POD 运行状态
                    if (POD_RUNNING_STATUS.equals(status)) {
                        ladder = InspectionThresholdLadderEnum.NORMAL.getLadder();
                    } else {
                        ladder = InspectionThresholdLadderEnum.HIGH_RISK.getLadder();
                    }
                    result = status;
                }
                serverInspectResultBos.add(ServerInspectResultBo.builder()
                        .appId(inspectPodInfo.getAppId())
                        .serverName(inspectPodInfo.getAppName())
                        .podName(inspectPodInfo.getPodName())
                        .ident(inspectPodInfo.getIdent())
                        .createTime(currentTimeMillis)
                        .fishTime(currentTimeMillis)
                        .metricKey(InspectionMetricKeyEnum.SERVER_AGENT_LIVE.getMetricKey())
                        .ladder(ladder)
                        .result(result)
                        .build());
            }
        });
    }

    private List<InspectPodInfoBo> getInspectPodInfoList() {
        Map<String, InspectionServiceListDb> serverNameToInspectionServerDb = getInspectionServerList();
        List<PodCache> podCacheList = cacheService.cachePodList();
        // 过滤掉 Job 类型 Pod
        podCacheList = podCacheList.stream()
                .filter(pod -> pod.getMetadata().getOwnerReferences().stream()
                        .noneMatch(ownerReference -> ownerReference != null && "Job".equalsIgnoreCase(ownerReference.getKind())))
                .collect(Collectors.toList());

        // 构建 InspectPodInfoBo 列表，并对 Labels 做空判断
        return podCacheList.parallelStream()
                .filter(podCache -> podCache.getMetadata() != null
                        && podCache.getMetadata().getLabels() != null
                        && StringUtils.isNotBlank(podCache.getMetadata().getLabels().get("app")))
                .map(pod -> buildPodInfoBo(pod, serverNameToInspectionServerDb))
                .collect(Collectors.toList());
    }

    private InspectPodInfoBo buildPodInfoBo(PodCache podCache, Map<String, InspectionServiceListDb> serverNameToInspectionServerDb) {
        InspectionServiceListDb serviceListDb = serverNameToInspectionServerDb.get(podCache.getMetadata().getLabels().get("app"));
        InspectPodInfoBo inspectPodInfoBo = new InspectPodInfoBo();
        inspectPodInfoBo.setPodName(podCache.getMetadata().getName());
        inspectPodInfoBo.setAppName(podCache.getMetadata().getLabels().get("app"));
        ServerInstanceVO instanceVO = buildServerInstanceVO(podCache);
        inspectPodInfoBo.setServerInstanceVO(instanceVO);
        inspectPodInfoBo.setIdent(podCache.getMetadata().getLabels().get("app"));
        inspectPodInfoBo.setDeployed(true);
        inspectPodInfoBo.setServiceListDb(serviceListDb);
        inspectPodInfoBo.setSupportInspection(Objects.nonNull(serviceListDb));
        return inspectPodInfoBo;
    }

    /**
     * 构建巡检 pod 信息类
     *
     * @param application    应用信息
     * @param deployed       是否部署
     * @param serviceListDb  巡检配置信息
     * @param serverInstance pod 信息
     */
    private InspectPodInfoBo buildPodInfoBo(InspectionApplicationDb application,
                                            boolean deployed,
                                            InspectionServiceListDb serviceListDb,
                                            ServerInstanceVO serverInstance) {
        InspectPodInfoBo podInfoBo = new InspectPodInfoBo();
        podInfoBo.setAppId(application.getId());
        podInfoBo.setAppName(application.getName());
        podInfoBo.setIdent(application.getIdent());
        podInfoBo.setDeployed(deployed);

        // pod 是否存在
        if (serverInstance == null) {
            podInfoBo.setPodName(null);
            podInfoBo.setServerInstanceVO(null);
        } else {
            podInfoBo.setPodName(serverInstance.getPodName());
            podInfoBo.setServerInstanceVO(serverInstance);
        }
        // 是否支持 inspection 巡检
        if (serviceListDb == null) {
            podInfoBo.setSupportInspection(false);
            podInfoBo.setServiceListDb(null);
        } else {
            podInfoBo.setSupportInspection(true);
            podInfoBo.setServiceListDb(serviceListDb);
        }

        return podInfoBo;
    }

    /**
     * 获取服务巡检配置的信息
     *
     * @return key: 服务名；value：巡检配置信息
     */
    private Map<String, InspectionServiceListDb> getInspectionServerList() {
        Map<String, InspectionServiceListDb> result = new HashMap<>();
        List<InspectionServiceListDb> all = serviceListDao.getAll();

        for (InspectionServiceListDb inspectionServiceListDb : all) {
            String serviceName = inspectionServiceListDb.getServiceName();
            result.put(serviceName, inspectionServiceListDb);
        }
        return result;
    }

    private InspectPodAvailableBo buildPodAvailableBo(InspectPodInfoBo podInfo) {
        InspectPodAvailableBo availableBo = new InspectPodAvailableBo();
        Integer inspectionType = podInfo.getServiceListDb().getInspectionType();
        ServerInstanceVO serverInstanceVO = podInfo.getServerInstanceVO();
        InspectionServiceListDb serviceListDb = podInfo.getServiceListDb();
        availableBo.setInspectionType(inspectionType);
        availableBo.setServiceName(podInfo.getIdent());
        availableBo.setPodName(podInfo.getPodName());
        if (INSPECT_TYPE_REST.equals(inspectionType)) {
            StringBuilder builder = new StringBuilder();
            builder.append("http://");
            String podIp = serverInstanceVO.getPodIp();
            Integer port = serverInstanceVO.getPodPort();
            if (port == null || port == 0) {
                log.warn("service port is error, podInfo:{}", podInfo);
                availableBo.setPreError("服务端口错误！port:" + port);
            }
            if (StringUtils.isNotBlank(podIp) && port != null) {
                builder.append(podIp)
                        .append(":")
                        .append(port)
                        .append(serviceListDb.getInspectionUrlPre());
                availableBo.setInspectionUrl(builder.toString());
            }
        }
        return availableBo;
    }

    /**
     * itemName = serverName + podName + ident
     *
     * @param serverName 服务名称
     * @param podName    pod名称
     * @param ident      服务id
     * @return .
     */
    private String buildItemName(String serverName, String podName, String ident) {
        return serverName +
                "|" +
                (StringUtils.isNotBlank(podName) ? podName : "未知") +
                "|" +
                ident;
    }

    public ServerInstanceVO buildServerInstanceVO(PodCache podCache) {
        String seviceName = podCache.getMetadata().getName();
        if (StringUtils.isNotBlank(podCache.getMetadata().getLabels().get("app"))) {
            seviceName = podCache.getMetadata().getLabels().get("app");
        }
        Integer port = 0;
        if (StringUtils.equals(SERVICE_VODNETWORK_VOD, seviceName) || StringUtils.equals(SERVICE_VODNETWORK_VODEDIT, seviceName)) {
            String nodeName = podCache.getSpec().getNodeName();
            ConfigMapCache configMapCache = cacheService.cacheConfigMapByName(Constants.CONFIGMAP_VODNETWORK);
            if (configMapCache == null) {
                log.warn("the configmap is not exist:{}", Constants.CONFIGMAP_VODNETWORK);
                return null;
            }
            Map<String, String> data = configMapCache.getData();
            if (StringUtils.equals(SERVICE_VODNETWORK_VOD, seviceName)) {
                port = data != null && data.containsKey(nodeName + VodnetworkConstants.VOD_BACK_HTTP_PORT) &&
                        StringUtils.isBlank(data.get(nodeName + VodnetworkConstants.VOD_BACK_HTTP_PORT)) ?
                        Integer.valueOf(VodnetworkConstants.DEFAULT_VOD_BACK_HTTP_PORT) :
                        Integer.valueOf(data.get(nodeName + VodnetworkConstants.VOD_BACK_HTTP_PORT));
            } else {
                port = data != null && data.containsKey(nodeName + VodnetworkConstants.VODEDIT_DOWNLOAD_HTTP_PORT) &&
                        StringUtils.isBlank(data.get(nodeName + VodnetworkConstants.VODEDIT_DOWNLOAD_HTTP_PORT)) ?
                        Integer.valueOf(VodnetworkConstants.DEFAULT_VODEDIT_DOWNLOAD_HTTP_PORT) :
                        Integer.valueOf(data.get(nodeName + VodnetworkConstants.VODEDIT_DOWNLOAD_HTTP_PORT));
            }
        } else {
            for (ContainerCache container : podCache.getSpec().getContainers()) {
                port = Optional.ofNullable(container.getLivenessProbe()).map(ProbeCache::getHttpGet)
                        .map(HTTPGetActionCache::getPort).map(IntOrString::getIntVal).orElse(0);
                break;
            }
        }
        ServerInstanceVO result = ServerInstanceVO.builder()
                .podName(podCache.getMetadata().getName())
                .hostIp(podCache.getStatus().getHostIP())
                .nodeIp(podCache.getSpec().getNodeName())
                .version(getVersion(podCache))
                .status(getStatus(podCache))
                .restarts(getRestarts(podCache))
                .age(getAge(podCache))
                .allowRestart(getAllowRestart(podCache))
                .namespace(podCache.getMetadata().getNamespace())
                .hostname(k8sService.getHostNameByNodeIp(podCache.getSpec().getNodeName()))
                .podIp(podCache.getStatus().getPodIP())
                .podPort(port)
                .build();
        if (Objects.nonNull(podCache.getSpec()) && org.apache.commons.collections.CollectionUtils.isNotEmpty(podCache.getSpec().getContainers())) {
            List<String> containers = new ArrayList<>();
            podCache.getSpec().getContainers().forEach(c -> containers.add(c.getName()));
            result.setContainers(containers);
        }
        return result;
    }


    private Boolean getAllowRestart(PodCache pod) {
        List<OwnerReferenceCache> ownerReferences = pod.getMetadata().getOwnerReferences();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(ownerReferences)) {
            return true;
        }
        for (OwnerReferenceCache item : ownerReferences) {
            if ("Job".equals(item.getKind())) {
                return false;
            }
        }
        return true;
    }

    private List<String> getVersion(PodCache pod) {

        List<ContainerCache> containers = new ArrayList<>(pod.getSpec().getContainers());
        String podName = pod.getMetadata().getName();
        // 特殊处理大数据的服务
        if (podName.startsWith("spark-") || podName.startsWith("azkaban-")) {
            containers.addAll(pod.getSpec().getInitContainers().stream().filter(container -> container.getName().matches("^init-.*-jar$")).collect(Collectors.toList()));
        }
        return getVersionByContainer(containers);
    }

    public List<String> getVersionByContainer(List<ContainerCache> containers) {
        List<String> resultList = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(containers)) {
            containers.forEach(x -> resultList.add(buildVersion(x)));
        }
        return resultList;
    }

    private String buildVersion(ContainerCache container) {
        return container.getName() + ": " + container.getImage();
    }

    private String getStatus(PodCache pod) {
        String podStatus = pod.getStatus().getPhase();
        if (!StringUtils.equals(Constant.RUNNING, podStatus)) {
            return podStatus;
        }
        List<ContainerStatusCache> containerStatuses = pod.getStatus().getContainerStatuses();
        for (ContainerStatusCache item : containerStatuses) {
            ContainerState state = item.getState();
            ContainerStateTerminated terminated = state.getTerminated();
            if (Objects.nonNull(terminated)) {
                return terminated.getReason();
            }
            ContainerStateWaiting waiting = state.getWaiting();
            if (Objects.nonNull(waiting)) {
                return waiting.getReason();
            }
        }
        return podStatus;
    }

    private int getRestarts(PodCache pod) {
        List<ContainerStatusCache> statusList = pod.getStatus().getContainerStatuses();
        if (statusList.isEmpty()) {
            return 0;
        }

        int restartCount = 0;
        for (ContainerStatusCache s : statusList) {
            if (Objects.nonNull(s.getRestartCount()) && restartCount < s.getRestartCount()) {
                restartCount = s.getRestartCount();
            }
        }
        return restartCount;
    }

    private String getAge(PodCache pod) {
        return pod.getMetadata().getCreationTimestamp();
    }
}
