package com.xylink.manager.inspection.service;

import com.xylink.manager.inspection.common.OpsManagerException;
import com.xylink.manager.inspection.dao.InspectionSchedulerTimeDao;
import com.xylink.manager.inspection.dao.InspectionTemplateDao;
import com.xylink.manager.inspection.dao.InspectionTemplateItemDao;
import com.xylink.manager.inspection.entity.common.InspectionConfig;
import com.xylink.manager.inspection.entity.condition.InspectionCondition;
import com.xylink.manager.inspection.entity.condition.InspectionItemCondition;
import com.xylink.manager.inspection.entity.condition.InspectionSchedulerJobCondition;
import com.xylink.manager.inspection.entity.db.InspectionSchedulerTimeDb;
import com.xylink.manager.inspection.entity.db.InspectionTemplateDb;
import com.xylink.manager.inspection.entity.db.InspectionTemplateItemDb;
import com.xylink.manager.inspection.entity.enums.InspectionInspectTypeEnum;
import com.xylink.manager.inspection.entity.enums.InspectionItemTypeEnum;
import com.xylink.manager.inspection.entity.enums.InspectionJobStatusEnum;
import com.xylink.manager.inspection.entity.enums.InspectionSchedulerTypeEnum;
import com.xylink.util.UUIDGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/28 14:14
 */
@Service("templateService")
@Slf4j
public class InspectTemplateService {
    @Autowired
    private InspectInstanceService instanceService;
    @Autowired
    private InspectionTemplateDao templateDao;
    @Autowired
    private InspectionTemplateItemDao templateItemDao;
    @Autowired
    private InspectionSchedulerTimeDao schedulerTimeDao;
    @Autowired
    private InspectionConfig inspectionConfig;

    private static final String DATA_FROM_HOUR_MIN = "yyyy-MM-dd HH:mm";
    private static final String DATA_FROM = "yyyy-MM-dd";

    /**
     * 构建巡检任务模版
     *
     * @param condition 巡检任务参数
     * @return 模板id
     */
    public String generateInspectTemplate(InspectionCondition condition) {
        String id = "";
        Integer type = condition.getType();
        // 判断巡检任务的名称是否已经重复
        if (isNameExist(condition.getName(), condition.getId())) {
            throw new OpsManagerException("创建失败，巡检名称重复");
        }
        if (type == InspectionInspectTypeEnum.temp.getType()) {
            // 构建临时巡检模板
            id = generateTempInspection(condition);
        } else if (type == InspectionInspectTypeEnum.time.getType()) {
            // 构建定时巡检模板
            id = generateTimeInspection(condition);
        } else if (type == InspectionInspectTypeEnum.scheduler.getType()) {
            // 构建周期巡检模板
            id = generateSchedulerInspection(condition);
        }
        return id;
    }

    /**
     * 判断巡检名称是否已经存在
     *
     * @param name 巡检名称
     * @param id   模版id
     * @return true: 存在; false: 不存在
     */
    public boolean isNameExist(String name, String id) {
        String templateId = templateDao.getTemplateIdByName(name);
        // 排除掉自身，避免修改时无法保存自身信息
        if (StringUtils.isNotBlank(id) && StringUtils.isNotBlank(templateId)) {
            return !(templateId.equals(id));
        }
        return StringUtils.isNotBlank(templateId);
    }

    /**
     * 构建临时巡检模板
     * @param condition 巡检信息
     * @return 模板 id
     */
    public String generateTempInspection(InspectionCondition condition) {
        String id;
        // 临时巡检构建时需要判断当前是否有正在执行的巡检任务
        if (instanceService.hasRunningInstance()) {
            throw new OpsManagerException("任务创建失败，已有一个巡检任务正在执行");
        }
        try {
            // 巡检信息保存到数据库中
            id = createTempTemplate(condition);
            // 创建一个新的巡检实例
            instanceService.generateInstance(condition);
        } catch (Exception e) {
            log.error("create inspection job error: ", e);
            throw new OpsManagerException("任务创建失败");
        }
        return id;
    }

    /**
     * 创建临时巡检模板并保存到数据库中
     * @param condition 巡检信息
     * @return 模板 id
     */
    public String createTempTemplate(InspectionCondition condition) {
        long createTime = System.currentTimeMillis();
        String id = UUIDGenerator.generate();
        InspectionTemplateDb template = new InspectionTemplateDb();
        template.setId(id);
        template.setCreateUser(condition.getCreateUser());
        template.setInspectType(condition.getType());
        template.setCreateTime(createTime);
        template.setInspectionName(condition.getName());
        // 将前端传入的巡检项列表，转换成存入数据库中的巡检项列表
        List<InspectionTemplateItemDb> mapDbs = buildInspectTemplateItemMapDbs(id, condition.getInspectionItems());
        // 保存到数据库中
        saveTempTemplate(template, mapDbs);
        condition.setId(id);
        return id;
    }

    /**
     * 将临时巡检模板信息保存到数据库中
     * @param template .
     * @param mapDbs .
     */
    public void saveTempTemplate(InspectionTemplateDb template, List<InspectionTemplateItemDb> mapDbs) {
        templateItemDao.insert(mapDbs);
        templateDao.insert(template);
    }

    /**
     * 将前端传入的巡检项列表，转换成存入数据库中的巡检项列表
     * @param templateId 巡检模板id
     * @param inspectionItems 前端传来的巡检项
     * @return 巡检项列表
     */
    private List<InspectionTemplateItemDb> buildInspectTemplateItemMapDbs(String templateId, InspectionItemCondition inspectionItems) {
        List<InspectionTemplateItemDb> mapDbs = new ArrayList<>();
        mapDbs.addAll(buildInspectTemplateItemMapDbs(templateId, inspectionItems.getSystem(), InspectionItemTypeEnum.SYSTEM.getId()));
        mapDbs.addAll(buildInspectTemplateItemMapDbs(templateId, inspectionItems.getMiddleware(), InspectionItemTypeEnum.MIDDLEWARE.getId()));
        mapDbs.addAll(buildInspectTemplateItemMapDbs(templateId, inspectionItems.getServer(), InspectionItemTypeEnum.SERVER.getId()));
        return mapDbs;
    }

    /**
     * 创建巡检项列表
     * @param templateId 模板id
     * @param itemIds 巡检项id 列表
     * @param type 巡检项类型
     * @return 巡检项列表
     */
    private List<InspectionTemplateItemDb> buildInspectTemplateItemMapDbs(String templateId, List<String> itemIds, int type) {
        List<InspectionTemplateItemDb> templateItemDbs = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(itemIds)) {
            for (String itemId : itemIds) {
                InspectionTemplateItemDb mapDb = new InspectionTemplateItemDb();
                mapDb.setConfigId(templateId);
                mapDb.setItemId(itemId);
                mapDb.setItemType(type);
                templateItemDbs.add(mapDb);
            }
        }
        return templateItemDbs;
    }

    /**
     * 创建定时巡检
     *
     * @param condition .
     * @return timeJobId
     */
    public String generateTimeInspection(InspectionCondition condition) {
        try {
            // 检查定时巡检参数
            checkTimeCondition(condition);
            return createTimeTemplate(condition);
        } catch (OpsManagerException e) {
            throw e;
        } catch (Exception e) {
            log.error("create inspection time job error: ", e);
            throw new OpsManagerException("任务创建失败");
        }
    }

    public String createTimeTemplate(InspectionCondition condition) {
        long createTime = System.currentTimeMillis();
        String templateId = UUIDGenerator.generate();
        InspectionTemplateDb template = new InspectionTemplateDb();
        template.setId(templateId);
        template.setCreateUser(condition.getCreateUser());
        template.setCreateTime(createTime);
        template.setInspectType(condition.getType());
        template.setInspectionName(condition.getName());
        // 将前端传入的巡检项列表，转换成存入数据库中的巡检项列表
        List<InspectionTemplateItemDb> mapDbs = buildInspectTemplateItemMapDbs(templateId, condition.getInspectionItems());
        // 将定时巡检当作特殊的周期巡检处理，创建调度时间db
        InspectionSchedulerTimeDb schedulerTimeDb = schedulerTimeDao.builderOnceSchedulerTimeDb(templateId, condition.getTimerJobTime());
        // 保存到数据库
        saveTimeTemplate(template, mapDbs, schedulerTimeDb);
        condition.setId(templateId);
        return templateId;
    }

    /**
     * 定时巡检信息保存到数据库中
     * @param template 巡检模板
     * @param mapDbs 巡检包含的巡检项
     * @param schedulerTimeDb 定时巡检执行时间的信息
     */
    public void saveTimeTemplate(InspectionTemplateDb template, List<InspectionTemplateItemDb> mapDbs, InspectionSchedulerTimeDb schedulerTimeDb) {
        templateItemDao.insert(mapDbs);
        templateDao.insert(template);
        schedulerTimeDao.insert(schedulerTimeDb);
    }

    /**
     * 创建周期巡检
     * @param condition 巡检任务条件
     * @return 模板id
     */
    public String generateSchedulerInspection(InspectionCondition condition) {
        try {
            String templateId = UUIDGenerator.generate();
            condition.setId(templateId);
            // 检查周期巡检参数
            checkSchedulerCondition(condition);
            // 构建周期巡检 开始时间list
            List<InspectionSchedulerTimeDb> schedulerTimeDbs = buildSchedulerTimeList(condition);
            // 检查 开始时间list 是否存在冲突
            checkSchedulerTimeDb(schedulerTimeDbs);
            // 创建模版并保存到数据库
            return createSchedulerTemplate(condition, schedulerTimeDbs);
        } catch (OpsManagerException e) {
            throw e;
        } catch (Exception e) {
            log.error("create inspection scheduler job error: ", e);
            throw new OpsManagerException(e.getMessage());
        }
    }

    private List<InspectionSchedulerTimeDb> buildSchedulerTimeList(InspectionCondition condition) {
        return schedulerTimeDao.buildSchedulerTimeDbs(condition);
    }

    /**
     * 创建周期巡检
     *
     * @param condition 周期巡检条件
     * @return 周期巡检 id
     */
    public String createSchedulerTemplate(InspectionCondition condition, List<InspectionSchedulerTimeDb> schedulerTimeDbs) {
        long createTime = System.currentTimeMillis();
        InspectionTemplateDb template = new InspectionTemplateDb();
        template.setId(condition.getId());
        template.setCreateUser(condition.getCreateUser());
        template.setInspectType(condition.getType());
        template.setCreateTime(createTime);
        template.setInspectionName(condition.getName());
        condition.setId(condition.getId());
        InspectionItemCondition inspectionItems = condition.getInspectionItems();
        List<InspectionTemplateItemDb> mapDbs = buildInspectTemplateItemMapDbs(condition.getId(), inspectionItems);
        saveSchedulerTemplate(template, mapDbs, schedulerTimeDbs);
        return condition.getId();
    }

    /**
     * 检查开始时间的间隔是否大于四小时
     * @param schedulerTimeDbs
     */
    private void checkSchedulerTimeDb(List<InspectionSchedulerTimeDb> schedulerTimeDbs) {
        log.info("[inspection] check scheduler time list:{}", schedulerTimeDbs);
        for (int i = 0; i < schedulerTimeDbs.size(); i++) {
            InspectionSchedulerTimeDb outerScheduler = schedulerTimeDbs.get(i);
            long outerNextExecTime = outerScheduler.getNextExecTime();
            for (int j = i + 1; j < schedulerTimeDbs.size(); j++) {
                InspectionSchedulerTimeDb innerScheduler = schedulerTimeDbs.get(j);
                long innerNextExecTime = innerScheduler.getNextExecTime();
                if (outerNextExecTime == innerNextExecTime) {
                    throw new OpsManagerException("存在相同的开始时间: " + getSchedulerTimeDesc(outerScheduler));
                }
                if (outerNextExecTime < innerNextExecTime && innerNextExecTime - DateUtils.MILLIS_PER_HOUR * 4 <= outerNextExecTime) {
                    throw new OpsManagerException("四小时内存在多个巡检: " + getSchedulerTimeDesc(innerScheduler) + ", " + getSchedulerTimeDesc(outerScheduler));
                }
                if (outerNextExecTime > innerNextExecTime && innerNextExecTime + DateUtils.MILLIS_PER_HOUR * 4 >= outerNextExecTime) {
                    throw new OpsManagerException("四小时内存在多个巡检: " + getSchedulerTimeDesc(innerScheduler) + ", " + getSchedulerTimeDesc(outerScheduler));
                }
            }
        }
    }

    private String getSchedulerTimeDesc(InspectionSchedulerTimeDb schedulerTimeDb) {
        if (InspectionSchedulerTypeEnum.WEEK.getType().equals(schedulerTimeDb.getSchedulerType())) {
            return schedulerTimeDao.getWeekStr(schedulerTimeDb.getSchedulerWeek()) + schedulerTimeDb.getStartTime();
        }
        if (InspectionSchedulerTypeEnum.MONTH.getType().equals(schedulerTimeDb.getSchedulerType())) {
            return schedulerTimeDao.getMonthStr(schedulerTimeDb.getSchedulerMonth()) + schedulerTimeDb.getStartTime();
        }
        return "";
    }

    public void saveSchedulerTemplate(InspectionTemplateDb template, List<InspectionTemplateItemDb> mapDbs, List<InspectionSchedulerTimeDb> schedulerTimes) {
        templateItemDao.insert(mapDbs);
        templateDao.insert(template);
        schedulerTimeDao.insertTimeList(schedulerTimes);
    }

    /**
     * 检查周期巡检参数是否合理
     *
     * @param condition 参数
     */
    private void checkSchedulerCondition(InspectionCondition condition) {
        InspectionSchedulerJobCondition schedulerJob = condition.getSchedulerJob();
        // 没有周期巡检参数
        if (schedulerJob == null) {
            throw new OpsManagerException("周期巡检创建失败，参数为空！");
        }
        if (StringUtils.isBlank(schedulerJob.getStartDate()) || StringUtils.isBlank(schedulerJob.getEndDate())) {
            throw new OpsManagerException("周期巡检创建失败，周期开始或结束时间为空！");
        }
        try {
            long startTime = DateUtils.parseDate(schedulerJob.getStartDate(), DATA_FROM).getTime();
            // 结束时间字符串 "yyyy-MM-dd"
            Date date = DateUtils.parseDate(schedulerJob.getEndDate(), DATA_FROM);
            // 加上 23时59分59秒999毫秒
            long endTime = date.getTime() + (DateUtils.MILLIS_PER_DAY - 1);
            schedulerJob.setStartTime(startTime);
            schedulerJob.setEndTime(endTime);
            // 结束时间早于开始时间
            if (endTime <= startTime) {
                throw new OpsManagerException("周期巡检创建失败，周期结束时间早于开始时间！");
            }
            // 结束时间早于当前时间
            if (endTime < System.currentTimeMillis()) {
                throw new OpsManagerException("周期巡检创建失败，周期结束时间早于当前时间！");
            }
        } catch (ParseException e) {
            throw new OpsManagerException("周期巡检创建失败，周期开始或结束时间格式错误！");
        }

        // 检查周期巡检开始时间是否为空
        checkSchedulerTimeCondition(condition.getSchedulerJob());
    }

    /**
     * 检查周期巡检开始时间是否为空
     *
     * @param schedulerCondition 周期巡检
     */
    private void checkSchedulerTimeCondition(InspectionSchedulerJobCondition schedulerCondition) {
        String schedulerType = schedulerCondition.getSchedulerType();
        if (InspectionSchedulerTypeEnum.WEEK.getType().equals(schedulerType)) {
            // 类型为 week
            List<InspectionSchedulerJobCondition.WeekScheduler> weekSchedulers = schedulerCondition.getWeekSchedulers();
            // 巡检开始时间为空
            if (CollectionUtils.isEmpty(weekSchedulers)) {
                throw new OpsManagerException("周期巡检创建失败，开始时间为空！");
            }
            if (weekSchedulers.size() > inspectionConfig.getScheduleItemLimit()) {
                throw new OpsManagerException("周期巡检创建失败，开始时间个数超过限制！");
            }
        } else if (InspectionSchedulerTypeEnum.MONTH.getType().equals(schedulerType)) {
            // 类型为 month
            List<InspectionSchedulerJobCondition.MonthScheduler> monthSchedulers = schedulerCondition.getMonthSchedulers();
            // 巡检开始时间为空
            if (CollectionUtils.isEmpty(monthSchedulers)) {
                throw new OpsManagerException("周期巡检创建失败，开始时间为空！");
            }
            if (monthSchedulers.size() > inspectionConfig.getScheduleItemLimit()) {
                throw new OpsManagerException("周期巡检创建失败，开始时间个数超过限制！");
            }
        }
    }

    /**
     * 检查定时巡检参数
     *
     * @param condition 参数
     */
    private void checkTimeCondition(InspectionCondition condition) {
        // 开始时间为空
        if (StringUtils.isBlank(condition.getTimerJob())) {
            throw new OpsManagerException("巡检开始时间为空，或开始时间早于当前时间");
        }
        try {
            // 将开始时间字符串转换为时间戳
            long time = DateUtils.parseDate(condition.getTimerJob(), DATA_FROM_HOUR_MIN).getTime();
            condition.setTimerJobTime(time);
            // 判断开始时间是否小于当前时间
            if (time < System.currentTimeMillis()) {
                throw new OpsManagerException("开始时间早于当前时间");
            }
        } catch (ParseException e) {
            log.error("create inspection time job error: ", e);
            throw new OpsManagerException("时间格式错误");
        }
    }

    public InspectionCondition templateDetail(String templateId, Integer type) {
        InspectionTemplateDb template = templateDao.selectById(templateId);
        if (template == null) {
            throw new OpsManagerException("没有找到该任务详情");
        }
        Integer inspectType = template.getInspectType();
        if (inspectType == null || !inspectType.equals(type)) {
            throw new OpsManagerException("请求巡检和保存巡检类型不一致");
        }
        if (type == InspectionInspectTypeEnum.temp.getType()) {
            return tempTemplateCondition(template);
        } else if (type == InspectionInspectTypeEnum.time.getType()) {
            return timeTemplateCondition(template);
        } else if (type == InspectionInspectTypeEnum.scheduler.getType()) {
            return schedulerTemplateCondition(template);
        } else {
            throw new OpsManagerException("不支持该巡检类型");
        }
    }

    private InspectionCondition tempTemplateCondition(InspectionTemplateDb template) {
        InspectionCondition condition = new InspectionCondition();
        InspectionItemCondition itemCondition = templateItemDao.getItems(template.getId());
        condition.setId(template.getId());
        condition.setInspectionItems(itemCondition);
        condition.setCreateUser(template.getCreateUser());
        condition.setType(template.getInspectType());
        condition.setName(template.getInspectionName());
        return condition;
    }


    private InspectionCondition timeTemplateCondition(InspectionTemplateDb template) {
        InspectionCondition condition = new InspectionCondition();
        String id = template.getId();
        InspectionItemCondition itemCondition = templateItemDao.getItems(id);
        condition.setId(template.getId());
        condition.setInspectionItems(itemCondition);
        condition.setCreateUser(template.getCreateUser());
        condition.setType(template.getInspectType());
        condition.setName(template.getInspectionName());
        InspectionSchedulerTimeDb schedulerTimeDb = schedulerTimeDao.getSchedulerTimeByTemplateId(id);
        Long nextExecTime = schedulerTimeDb.getNextExecTime();
        condition.setTimerJob(DateFormatUtils.format(nextExecTime, DATA_FROM_HOUR_MIN));
        return condition;
    }


    private InspectionCondition schedulerTemplateCondition(InspectionTemplateDb template) {
        InspectionCondition condition = new InspectionCondition();
        InspectionItemCondition itemCondition = templateItemDao.getItems(template.getId());
        condition.setId(template.getId());
        condition.setInspectionItems(itemCondition);
        condition.setCreateUser(template.getCreateUser());
        condition.setType(template.getInspectType());
        condition.setName(template.getInspectionName());
        InspectionSchedulerJobCondition schedulerJobCondition = schedulerTimeDao.getSchedulerJobCondition(template);
        condition.setSchedulerJob(schedulerJobCondition);
        return condition;
    }

    public void templateEdit(InspectionCondition condition) {
        Integer type = condition.getType();
        if (isNameExist(condition.getName(), condition.getId())) {
            throw new OpsManagerException("修改失败，巡检名称重复");
        }
        if (InspectionInspectTypeEnum.time.getType() == type) {
            editTimeTemplate(condition);
        } else if (InspectionInspectTypeEnum.scheduler.getType() == type) {
            editSchedulerTemplate(condition);
        } else {
            throw new OpsManagerException("不支持该巡检类型");
        }
    }

    private void editTimeTemplate(InspectionCondition condition) {
        InspectionTemplateDb template = templateDao.getTemplateById(condition.getId());
        if (template == null) {
            throw new OpsManagerException("没有找到该任务详情");
        }
        Integer jobStatus = template.getJobStatus();
        if (InspectionJobStatusEnum.NOT_STARTED.getStatus() != jobStatus) {
            throw new OpsManagerException("已结束的任务不允许修改");
        }
        if (InspectionJobStatusEnum.RUNNING.getStatus() == jobStatus) {
            throw new OpsManagerException("运行中的任务不允许修改");
        }
        checkTimeCondition(condition);
        template.setUpdateTime(System.currentTimeMillis());
        template.setCreateUser(condition.getCreateUser());
        template.setInspectionName(condition.getName());
        updateTimeTemplate(template, condition);
    }

    public void updateTimeTemplate(InspectionTemplateDb template, InspectionCondition condition) {
        InspectionItemCondition inspectionItems = condition.getInspectionItems();
        List<InspectionTemplateItemDb> mapDbs = buildInspectTemplateItemMapDbs(template.getId(), inspectionItems);
        InspectionSchedulerTimeDb schedulerTimeDb = schedulerTimeDao.builderOnceSchedulerTimeDb(template.getId(), condition.getTimerJobTime());
        updateTimeTemplate(template, mapDbs, schedulerTimeDb);
    }

    public void updateTimeTemplate(InspectionTemplateDb template, List<InspectionTemplateItemDb> mapDbs, InspectionSchedulerTimeDb schedulerTimeDb) {
        templateDao.update(template);
        templateItemDao.deleteByTemplateId(template.getId());
        templateItemDao.insert(mapDbs);
        schedulerTimeDao.deleteByTemplateId(template.getId());
        schedulerTimeDao.insert(schedulerTimeDb);
    }

    private void editSchedulerTemplate(InspectionCondition condition) {
        InspectionTemplateDb template = templateDao.getTemplateById(condition.getId());
        if (template == null) {
            throw new OpsManagerException("没有找到该任务详情");
        }
        Integer jobStatus = template.getJobStatus();
        if (InspectionJobStatusEnum.COMPLETED.getStatus() == jobStatus) {
            throw new OpsManagerException("已结束的任务不允许修改");
        }
        if (InspectionJobStatusEnum.RUNNING.getStatus() == jobStatus) {
            throw new OpsManagerException("运行中的任务不允许修改");
        }
        InspectionSchedulerJobCondition schedulerCondition = condition.getSchedulerJob();
        if (schedulerCondition == null) {
            throw new OpsManagerException("周期巡检id不存在");
        }
        checkSchedulerCondition(condition);
        checkSchedulerTimeCondition(schedulerCondition);
        // 构建周期巡检 开始时间list
        List<InspectionSchedulerTimeDb> schedulerTimeDbs = buildSchedulerTimeList(condition);
        // 检查 开始时间list 是否存在冲突
        checkSchedulerTimeDb(schedulerTimeDbs);
        template.setUpdateTime(System.currentTimeMillis());
        template.setCreateUser(condition.getCreateUser());
        template.setInspectionName(condition.getName());
        updateSchedulerTemplate(template, condition, schedulerTimeDbs);
    }

    public void updateSchedulerTemplate(InspectionTemplateDb template, InspectionCondition condition, List<InspectionSchedulerTimeDb> schedulerTimeDbs) {
        InspectionItemCondition inspectionItems = condition.getInspectionItems();
        List<InspectionTemplateItemDb> mapDbs = buildInspectTemplateItemMapDbs(template.getId(), inspectionItems);
        updateSchedulerTemplate(template, mapDbs, schedulerTimeDbs);
    }

    public void updateSchedulerTemplate(InspectionTemplateDb template, List<InspectionTemplateItemDb> mapDbs, List<InspectionSchedulerTimeDb> schedulerTimeDbs) {
        templateDao.update(template);
        templateItemDao.deleteByTemplateId(template.getId());
        templateItemDao.insert(mapDbs);
        schedulerTimeDao.deleteByTemplateId(template.getId());
        schedulerTimeDao.insertTimeList(schedulerTimeDbs);
    }

    public void disable(String id, int type) {
        Long status = templateDao.selectStatusById(id);
        if (status != null && InspectionJobStatusEnum.COMPLETED.getStatus() == status) {
            throw new OpsManagerException("该任务已经结束");
        }
        if (status != null && InspectionJobStatusEnum.RUNNING.getStatus() == status) {
            throw new OpsManagerException("该任务正在运行");
        }
        templateDao.updateEnable(id);
        if (type == InspectionInspectTypeEnum.scheduler.getType()) {
            schedulerTimeDao.disableByTemplateId(id);
        }
    }

    public List<InspectionSchedulerTimeDb> schedulerTimeList(int page, int size, long timestamp) {
        return schedulerTimeDao.getSchedulerTimeList(page, size, timestamp);
    }

    public void updateSchedulerNextTime(String schedulerTimeId, long nextStartTime) {
        schedulerTimeDao.updateSchedulerNextTime(schedulerTimeId, nextStartTime);
    }

    public void updateTemplateStatus(String templateId, int status) {
        templateDao.updateStatus(templateId, status);
    }

    public void updateSchedulerFinished(String id) {
        schedulerTimeDao.updateSchedulerFinished(id);
    }
}
