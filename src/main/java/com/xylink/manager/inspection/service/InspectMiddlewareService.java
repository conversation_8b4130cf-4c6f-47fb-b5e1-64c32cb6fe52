package com.xylink.manager.inspection.service;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.inspection.dao.InspectionItemConfigDao;
import com.xylink.manager.inspection.entity.db.InspectionItemConfigDb;
import com.xylink.manager.inspection.entity.db.InspectionSubTaskDb;
import com.xylink.manager.inspection.utils.inspect.*;
import com.xylink.manager.service.base.K8sService;
import com.xylink.util.SpringBeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <a href="https://nones.xylink.com/wiki/?from_wecom=1#/team/AQzvsooq/space/SLqJDsvD/page/XuJLe8hx">巡检-中间件巡检</a>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/16 10:46
 */
@Service
@Slf4j
public class InspectMiddlewareService {
    @Autowired
    private InspectionItemConfigDao itemConfigDao;
    @Autowired
    private KafkaInspect kafkaInspector;
    @Autowired
    private RedisInspect redisInspect;
    @Autowired
    private ZookeeperInspect zookeeperInspector;
    @Autowired
    private BigdataInspector bigdataInspector;
    @Autowired
    private K8sService k8sService;

    public List<InspectionSubTaskDb> execInspection(String taskId, List<String> itemConfigIds) {
        List<InspectionSubTaskDb> subTasks = new ArrayList<>();
        List<InspectionItemConfigDb> items = itemConfigDao.getItemByIds(itemConfigIds);
        for (InspectionItemConfigDb item : items) {
            log.info("item {}", item);
            subTasks.addAll(execItemInspection(taskId, item));
        }
        return subTasks;
    }

    private List<InspectionSubTaskDb> execItemInspection(String taskId, InspectionItemConfigDb itemConfig) {
        String name = itemConfig.getItemName().toLowerCase();
        List<InspectionSubTaskDb> subTaskDbs = new ArrayList<>();
        switch (name) {
            case "kafka":
                subTaskDbs = kafkaInspector.inspect(taskId, itemConfig);
                break;
            case "redis":
                subTaskDbs = redisInspect.inspect(taskId, itemConfig);
                break;
            case "db":
                Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
                String dbType = allIp.get(NetworkConstants.DATABASE_TYPE);
                DbInspect dbInspect = SpringBeanUtil.getBean(dbType + "_Inspect");
                subTaskDbs = dbInspect.inspect(taskId, itemConfig);
                break;
            case "zookeeper":
                subTaskDbs = zookeeperInspector.inspect(taskId, itemConfig);
                break;
            case "bigdata":
                subTaskDbs = bigdataInspector.inspect(taskId, itemConfig);
            default:
                break;
        }
        return subTaskDbs;
    }
}
