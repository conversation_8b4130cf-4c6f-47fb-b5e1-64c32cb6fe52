package com.xylink.manager.inspection.service;

import com.xylink.manager.inspection.common.OpsManagerException;
import com.xylink.manager.inspection.common.monitor.MonitorIndicatorDto;
import com.xylink.manager.inspection.dao.*;
import com.xylink.manager.inspection.entity.bo.InspectionMetricConfigBo;
import com.xylink.manager.inspection.entity.condition.DefaultMonitor;
import com.xylink.manager.inspection.entity.condition.InspectionTrendCondition;
import com.xylink.manager.inspection.entity.db.*;
import com.xylink.manager.inspection.entity.enums.InspectionItemTypeEnum;
import com.xylink.manager.inspection.entity.enums.InspectionMetricKeyEnum;
import com.xylink.manager.inspection.entity.enums.InspectionServerStatus;
import com.xylink.manager.inspection.entity.vo.*;
import com.xylink.manager.inspection.utils.JsonUtils;
import com.xylink.manager.inspection.utils.PageUtil;
import com.xylink.manager.model.common.Page;
import com.xylink.manager.service.nightingale.monitor.MonitorService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/4 11:17
 */
@Service
@Slf4j
public class InspectionService {
    @Autowired
    private InspectionTaskDao taskDao;
    @Autowired
    private InspectionMetricDao metricDao;
    @Autowired
    private InspectionItemConfigDao itemConfigDao;
    @Autowired
    private InspectionInstanceDao instanceDao;
    @Autowired
    private InspectionTemplateDao templateDao;
    @Autowired
    private MonitorService monitorService;
    @Autowired
    private InspectionMetricTaskDao metricTaskDao;
    @Autowired
    private InspectionSubTaskDao subTaskDao;

    private static final String BIGDATA_MODULE_IDENT = "daShuJuJiChuMOKuai";
    private static final String BIGDATA_INSPECTION_ITEM_ID = "e1a103afbe6b4044ae6324ecf4554cfb";

    /**
     * 获取巡检项
     *
     * @return .
     */
    public InspectionItemsVo items() {
        // 查询 所有巡检项
        List<InspectionItemConfigDb> itemConfigs = itemConfigDao.getItemConfigs();

        // 判断大数据模块是否部署，没有部署移除掉 大数据 巡检项
        if (!isBigdataModuleOpen()) {
            itemConfigs.removeIf(itemConfig -> BIGDATA_INSPECTION_ITEM_ID.equals(itemConfig.getId()));
        }

        // Key: 巡检类型， Value: 巡检项集合
        Map<Integer, List<InspectionItemsVo.InspectionItem>> typeToItemsMap = itemConfigs.stream()
                .map(item -> {
                    InspectionItemsVo.InspectionItem inspectionItem = new InspectionItemsVo.InspectionItem();
                    inspectionItem.setId(item.getId());
                    inspectionItem.setName(item.getShowName());
                    inspectionItem.setType(item.getItemType());
                    return inspectionItem;
                })
                // 根据 ItemType 归组
                .collect(Collectors.groupingBy(InspectionItemsVo.InspectionItem::getType));

        // build Vo
        InspectionItemsVo inspectionItemsVo = new InspectionItemsVo();
        typeToItemsMap.forEach((type, list) -> {
            InspectionItemTypeEnum typeEnum = InspectionItemTypeEnum.getEnum(type);
            if (null == typeEnum) {
                return;
            }
            switch (typeEnum) {
                case SYSTEM:
                    inspectionItemsVo.setSystem(list);
                    break;
                case MIDDLEWARE:
                    inspectionItemsVo.setMiddleware(list);
                    break;
                case SERVER:
                    inspectionItemsVo.setServer(list);
                    break;
                default:
                    break;
            }
        });
        return inspectionItemsVo;
    }

    /**
     * 巡检记录表
     *
     * @param size      .
     * @param page      .
     * @param startTime .
     * @param endTime   .
     * @param asc       是否按照时间排序
     * @return .
     */
    public Page<InspectionRecordVo> recordList(int size, int page, Long startTime, Long endTime, Boolean asc) {
        return instanceDao.getRecordList(size, page, startTime, endTime, asc);
    }

    /**
     * 巡检列表
     *
     * @param size      .
     * @param page      .
     * @param startTime .
     * @param endTime   .
     * @param asc       是否按照时间排序
     * @return .
     */
    public Page<InspectionTemplateVo> list(int size, int page, Long startTime, Long endTime, Boolean asc) {
        return templateDao.getTemplateList(size, page, startTime, endTime, asc);
    }


    /**
     * 巡检详情
     *
     * @param instanceId 巡检任务id
     * @return .
     */
    public InspectionDetailVo detail(String instanceId) {
        InspectInstanceDb instance = instanceDao.getInstanceById(instanceId);
        if (instance == null) {
            throw new OpsManagerException("未查询到该巡检详情");
        }
        // 获取具体三种巡检类型(系统巡检、中间件巡检、服务巡检)的结果
        List<InspectionTaskDb> taskDbs = taskDao.getTaskList(instanceId);
        // Key: 巡检类型， Value: 巡检类型结果集合
        Map<Integer, InspectionTaskRecordVo> typeToTaskRecord = buildTaskRecord(taskDbs);

        // build Vo
        InspectionDetailVo.Records records = InspectionDetailVo.Records.builder()
                .server(typeToTaskRecord.get(InspectionItemTypeEnum.SERVER.getId()))
                .middleware(typeToTaskRecord.get(InspectionItemTypeEnum.MIDDLEWARE.getId()))
                .system(typeToTaskRecord.get(InspectionItemTypeEnum.SYSTEM.getId()))
                .build();
        return InspectionDetailVo.builder()
                .createUser(instance.getCreateUser())
                .createTime(instance.getCreateTime())
                .name(instance.getInspectionName())
                .step(instance.getStep())
                .status(instance.getJobStatus())
                .records(records)
                .build();
    }

    /**
     * Key: 巡检类型， Value: 巡检类型结果集合
     *
     * @param taskDbs 类型巡检结果集合
     * @return Key: 巡检类型， Value: 类型巡检结果集合
     */
    private Map<Integer, InspectionTaskRecordVo> buildTaskRecord(List<InspectionTaskDb> taskDbs) {
        Map<Integer, InspectionTaskRecordVo> typeToTaskRecord = new HashMap<>();
        for (InspectionTaskDb taskDb : taskDbs) {
            Integer taskType = taskDb.getTaskType();
            InspectionTaskRecordVo taskRecordVo = InspectionTaskRecordVo.builder()
                    .taskId(taskDb.getId())
                    .finishedTime(taskDb.getFinishedTime())
                    .highRisk(taskDb.getHighRisk())
                    .middleRisk(taskDb.getMiddleRisk())
                    .lowRisk(taskDb.getLowRisk())
                    .build();
            // 中间件巡检结果比服务、系统巡检多巡检项的展示
            if (taskDb.getTaskType() == InspectionItemTypeEnum.MIDDLEWARE.getId()) {
                // 从数据库查询出结果
                List<InspectionSubTaskDb> subTaskDbs = subTaskDao.getSubTaskList(taskDb.getId());
                // build Vo
                List<InspectionItemRecordVo> itemRecordVos = subTaskDbs.stream().map(x -> InspectionItemRecordVo.builder()
                        .subTaskId(x.getId())
                        .itemId(x.getItemId())
                        .itemName(x.getItemName())
                        .finishedTime(x.getFinishedTime())
                        .highRisk(x.getHighRisk())
                        .middleRisk(x.getMiddleRisk())
                        .lowRisk(x.getLowRisk())
                        .build()).collect(Collectors.toList());
                taskRecordVo.setItems(itemRecordVos);
            }
            typeToTaskRecord.put(taskType, taskRecordVo);
        }
        return typeToTaskRecord;
    }

    /**
     * 中间件巡检详情
     *
     * @param subTaskId 中间件巡检项结果id
     * @param size      .
     * @param page      .
     * @param ladders   风险等级过滤条件
     * @return .
     */
    public Page<InspectionMetricRecordVo> middlewareDetail(String subTaskId, Integer size, Integer page, List<Integer> ladders) {
        return metricTaskDao.getMetricTaskPageBySubTaskId(subTaskId, size, page, ladders);
    }

    /**
     * 系统巡检详情
     *
     * @param taskId 系统巡检id
     * @param size   .
     * @param page   .
     * @return .
     */
    public SystemRecordPageVo systemDetail(String taskId, Integer size, Integer page, List<Integer> ladders) {
        page = page - 1;
        int startRow = PageUtil.getStart(page, size);
        // 查询主机巡检项结果
        Long totalRow = subTaskDao.countIdByTaskIdAndType(taskId, InspectionItemTypeEnum.SYSTEM.getId(), ladders);
        if (totalRow == null || totalRow == 0) {
            return new SystemRecordPageVo(0, 0, 0L, Collections.emptyList(), getSystemMetric());
        }
        List<InspectionSubTaskDb> subTaskDbs = subTaskDao.getSubTasksByTaskIdAndType(taskId, InspectionItemTypeEnum.SYSTEM.getId(), size, startRow, ladders);

        // 查询主机巡检各项指标结果
        List<InspectionMetricTaskDb> metricTaskDbs = metricTaskDao.getMetricTaskBySubTaskIds(subTaskDbs.stream().map(InspectionSubTaskDb::getId).collect(Collectors.toList()));

        if (CollectionUtils.isEmpty(metricTaskDbs)) {
            return new SystemRecordPageVo(0, 0, 0L, Collections.emptyList(), getSystemMetric());
        }

        // 根据 subTask 分组(各主机对应的指标巡检结果)
        Map<String, List<InspectionMetricTaskDb>> collect = metricTaskDbs.stream().collect(Collectors.groupingBy(InspectionMetricTaskDb::getSubTaskId));
        List<InspectionSystemRecordVo> result = new ArrayList<>();

        for (InspectionSubTaskDb subTaskDb : subTaskDbs) {
            // 构建 VO
            result.add(subTaskDao.buildSystemRecordVo(subTaskDb, collect.get(subTaskDb.getId())));
        }

        return new SystemRecordPageVo(startRow, size, totalRow, result, getSystemMetric());
    }

    private SystemRecordPageVo.ItemDesc getSystemMetric() {
        InspectionMetricConfigBo metricCpu = metricDao.getInspectionMetricByKey(InspectionMetricKeyEnum.HOST_CPU_USAGE.getMetricKey());
        InspectionMetricConfigBo metricMemory = metricDao.getInspectionMetricByKey(InspectionMetricKeyEnum.HOST_MEM_USAGE.getMetricKey());
        InspectionMetricConfigBo metricDisk = metricDao.getInspectionMetricByKey(InspectionMetricKeyEnum.HOST_DISK_USAGE.getMetricKey());
        return SystemRecordPageVo.ItemDesc.builder()
                .cpu(buildSystemMetricDesc(metricCpu))
                .memory(buildSystemMetricDesc(metricMemory))
                .disk(buildSystemMetricDesc(metricDisk))
                .build();
    }

    private String buildSystemMetricDesc(InspectionMetricConfigBo metricConfig) {
        return "<ul>" +
                "<li>" +
                "指标描述：" + metricConfig.getMetricDesc() +
                "</li>" +
                "<li>" +
                "处理意见：" + metricConfig.getMetricSuggest().replaceAll("\\n", "<br />") +
                "</li>" +
                "</ul>";
    }

    /**
     * 服务巡检展开详情列表
     *
     * @param taskId 服务巡检id
     * @param size   .
     * @param page   .
     * @return .
     */
    public InspectionServerExpandVo serverExpand(String taskId, Integer size, Integer page, String ident, List<Integer> ladders) {
        page = page - 1;
        int startRow = PageUtil.getStart(page, size);
        // 查询 task
        InspectionTaskDb taskDb = taskDao.getTaskById(taskId);
        if (taskDb == null) {
            return null;
        }
        // 查询 巡检实例
        String instanceId = taskDb.getInstanceId();
        InspectInstanceDb instanceDb = instanceDao.getInstanceById(instanceId);
        if (instanceDb == null) {
            return null;
        }
        // 查询 subTask
        Long totalRow = subTaskDao.countByTaskIdAndIdent(taskId, ident, ladders);
        if (totalRow == null || totalRow == 0) {
            return InspectionServerExpandVo.builder()
                    .createUser(instanceDb.getCreateUser())
                    .createTime(instanceDb.getCreateTime())
                    .name(instanceDb.getInspectionName())
                    .build();
        }
        List<InspectionSubTaskDb> list = subTaskDao.getSubTasksByTaskIdAndIdentPage(taskId, size, startRow, ident, ladders);
        if (CollectionUtils.isEmpty(list)) {
            return InspectionServerExpandVo.builder()
                    .createUser(instanceDb.getCreateUser())
                    .createTime(instanceDb.getCreateTime())
                    .name(instanceDb.getInspectionName())
                    .build();
        }
        // 构建服务巡检展开列表
        List<InspectionServerExpandVo.Record> records = list.stream().map(this::buildServerExpandRecord).collect(Collectors.toList());
        return InspectionServerExpandVo.builder()
                .createUser(instanceDb.getCreateUser())
                .createTime(instanceDb.getCreateTime())
                .name(instanceDb.getInspectionName())
                .pages(new Page<>(startRow, size, totalRow, records))
                .build();
    }

    /**
     * 构建 服务巡检展开详情 VO
     *
     * @param subTaskDb .
     * @return .
     */
    private InspectionServerExpandVo.Record buildServerExpandRecord(InspectionSubTaskDb subTaskDb) {
        // itemName = serverName + "|" + podName + "|" + ident
        String itemName = subTaskDb.getItemName();
        String[] split = itemName.split("\\|");
        String serverName;
        String podName = null;
        String ident = null;
        if (split.length == 3) {
            // 存在 pod 名
            serverName = split[0];
            podName = split[1];
            ident = split[2];
        } else if (split.length == 2) {
            serverName = split[0];
            podName = split[1];
        } else {
            serverName = itemName;
        }

        int serverStatus;
        if (StringUtils.isNotBlank(subTaskDb.getExceptionDesc())) {
            serverStatus = InspectionServerStatus.NOT_AVAILABLE.getValue();
        } else {
            // 高风险和中风险设置服务存在异常
            serverStatus = subTaskDb.getHighRisk() + subTaskDb.getMiddleRisk() + subTaskDb.getLowRisk() > 0
                    ? InspectionServerStatus.EXCEPTION.getValue()
                    : InspectionServerStatus.NORMAL.getValue();
        }
        return InspectionServerExpandVo.Record
                .builder()
                .subTaskId(subTaskDb.getId())
                .serverName(serverName)
                .podName(podName)
                .ident(ident)
                .finishedTime(subTaskDb.getFinishedTime())
                .serverStatus(serverStatus)
                .highRisk(subTaskDb.getHighRisk())
                .middleRisk(subTaskDb.getMiddleRisk())
                .lowRisk(subTaskDb.getLowRisk())
                .build();
    }

    /**
     * 服务巡检详情
     *
     * @param subTaskId 服务巡检项 id
     * @param size      .
     * @param page      .
     * @return .
     */
    public Page<InspectionMetricRecordVo> serverDetail(String subTaskId, Integer size, Integer page, List<Integer> ladders) {
        return metricTaskDao.getMetricTaskPageBySubTaskId(subTaskId, size, page, ladders);
    }

    public Object systemTrend(InspectionTrendCondition trendCondition) {
        String nodeIp = trendCondition.getNodeIp();
        String merticName = trendCondition.getMerticName();
        long endTime = Long.parseLong(trendCondition.getEndTime()) / 1000;
        String hostDefaultScreen = monitorService.getHostDefaulltScreen();
        List<DefaultMonitor> defaultMonitors = JsonUtils.jsonToList(hostDefaultScreen, DefaultMonitor.class);
        Map<String, String> metricNameToQuery = new HashMap<>();
        if (CollectionUtils.isEmpty(defaultMonitors)) {
            throw new OpsManagerException("服务巡检失败，指标获取失败");
        }
        DefaultMonitor defaultMonitor = defaultMonitors.get(0);
        DefaultMonitor.Tags tag = defaultMonitor.getTags().get(0);
        for (DefaultMonitor.Charts chart : tag.getCharts()) {
            String title = chart.getConfigs().getTitle();
            String query = chart.getConfigs().getMetrics().get(0).getQuery();
            metricNameToQuery.put(title, query);
        }
        String query = metricNameToQuery.get(merticName);
        if (InspectionMetricKeyEnum.HOST_DISK_USAGE.getMetricKey().equals(merticName)) {
            query = metricNameToQuery.get("硬盘使用率");
        }
        MonitorIndicatorVo monitorIndicatorVo = monitorService.getIndicatorList(buildMonitorIndicatorDto(nodeIp, query, endTime));
        String s = JsonUtils.objectToJson(monitorIndicatorVo.getUi());
        List<Object> objects = JsonUtils.jsonToList(s, Object.class);
        if (CollectionUtils.isEmpty(objects)) {
            return null;
        }
        return objects.get(0);
    }

    /**
     * 构建查询指标值的参数，获取1小时的值
     *
     * @param endpoint .
     * @param query    .
     * @return .
     */
    private MonitorIndicatorDto buildMonitorIndicatorDto(String endpoint, String query, Long time) {
        MonitorIndicatorDto monitorIndicatorDto = new MonitorIndicatorDto();
        ArrayList<String> endpoints = new ArrayList<>();
        endpoints.add(endpoint + "-.*");
        monitorIndicatorDto.setEndpoints(endpoints);
        monitorIndicatorDto.setQuery(query);
        monitorIndicatorDto.setStart(time - 60 * 60);
        monitorIndicatorDto.setEnd(time);
        return monitorIndicatorDto;
    }

    private Boolean isBigdataModuleOpen() {
        return true;
    }
}
