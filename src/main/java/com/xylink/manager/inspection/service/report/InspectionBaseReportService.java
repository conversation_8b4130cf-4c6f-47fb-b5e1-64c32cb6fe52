package com.xylink.manager.inspection.service.report;

import com.xylink.manager.inspection.dao.*;
import com.xylink.manager.inspection.entity.db.InspectionMetricThresholdDb;
import com.xylink.manager.inspection.utils.MetricRiskJudgeUtil;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/9 18:07
 */
public abstract class InspectionBaseReportService {
    protected static final String FILE_SEPARATOR = File.separator;
    @Autowired
    protected InspectionTaskDao inspectionTaskDao;
    @Autowired
    protected InspectionSubTaskDao subTaskDao;
    @Autowired
    protected InspectionMetricTaskDao inspectionMetricTaskDao;
    @Autowired
    protected InspectionMetricDao inspectionMetricDao;
    @Autowired
    protected InspectionMetricThresholdDao metricThresholdDao;
    @Autowired
    protected InspectionTemplateItemDao templateItemDao;
    protected static final String UNKNOWN_METRIC_VALUE = "状态未知";

    String formatDateString(Long ts) {
        return DateFormatUtils.format(ts, "yyyy年MM月dd日 HH时mm分ss秒");
    }

    String formatConsumedString(Integer ms) {
        long hour = ms / DateUtils.MILLIS_PER_HOUR;
        long minute = (ms - hour * DateUtils.MILLIS_PER_HOUR) / DateUtils.MILLIS_PER_MINUTE;
        long second = (ms - hour * DateUtils.MILLIS_PER_HOUR - minute * DateUtils.MILLIS_PER_MINUTE) / DateUtils.MILLIS_PER_SECOND;
        StringBuilder time = new StringBuilder();
        if (hour > 0) {
            time.append(hour).append("小时");
        }
        if (minute > 0) {
            time.append(minute).append("分");
        }
        if (second >= 0) {
            time.append(second).append("秒");
        }
        return time.toString();
    }

    int systemMetricRiskJudge(Map<String, List<InspectionMetricThresholdDb>> metricIdsAndThresholds,
                              String usageRateString,
                              String metricKey) {
        List<InspectionMetricThresholdDb> thresholds = metricIdsAndThresholds.get(metricKey);
        for (InspectionMetricThresholdDb threshold : thresholds) {
            String riskJudgment = threshold.getRiskJudgment();
            String[] split = riskJudgment.split("\\|");
            Double riskValue = Double.valueOf(split[0]);
            Double usageRate = Double.valueOf(usageRateString);
            String operator = split[1];

            if (MetricRiskJudgeUtil.compare(usageRate, operator, riskValue)) {
                return threshold.getLadder();
            }
        }
        return 0;
    }
}
