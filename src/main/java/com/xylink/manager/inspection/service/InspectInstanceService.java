package com.xylink.manager.inspection.service;

import com.xylink.manager.inspection.dao.*;
import com.xylink.manager.inspection.entity.common.InspectionConfig;
import com.xylink.manager.inspection.entity.condition.InspectionCondition;
import com.xylink.manager.inspection.entity.condition.InspectionItemCondition;
import com.xylink.manager.inspection.entity.db.InspectInstanceDb;
import com.xylink.manager.inspection.entity.db.InspectionSubTaskDb;
import com.xylink.manager.inspection.entity.db.InspectionTaskDb;
import com.xylink.manager.inspection.entity.enums.InspectionInspectTypeEnum;
import com.xylink.manager.inspection.entity.enums.InspectionItemTypeEnum;
import com.xylink.manager.inspection.entity.enums.InspectionJobStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;

import static com.xylink.manager.inspection.utils.ExecutorServiceUtil.INSPECTION_EXECUTOR;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/28 15:44
 */
@Service("instanceService")
@Slf4j
public class InspectInstanceService {
    @Autowired
    private InspectionInstanceDao instanceDao;
    @Autowired
    private InspectionTemplateDao templateDao;
    @Autowired
    private InspectServerService serverService;
    @Autowired
    private InspectionTaskDao taskDao;
    @Autowired
    private InspectSystemService systemService;
    @Autowired
    private InspectMiddlewareService middlewareService;
    @Autowired
    private InspectionSchedulerTimeDao schedulerTimeDao;

    @Autowired
    private InspectionConfig inspectionConfig;

    @Autowired
    private InspectionMetricDao metricDao;

    /**
     * 重启后将未完成的任务改为失败状态
     */
    @PostConstruct
    private void init() {
        // 巡检未启用
        if (!inspectionConfig.isEnable()) {
            return;
        }
        INSPECTION_EXECUTOR.submit(() -> {
            try {
                // 初始化指标描述
                metricDao.init();
                log.info("[inspection] Check for unfinished instance");
                InspectInstanceDb instance = instanceDao.getRunnerInstance();
                if (instance == null) {
                    log.info("[inspection] Check for unfinished instance done.");
                    return;
                }
                instanceDao.updateFailed(instance.getId());
                Integer jobType = instance.getJobType();
                String templateId = instance.getConfigId();
                if (jobType == InspectionInspectTypeEnum.scheduler.getType() && schedulerTimeDao.existNextExecInScheduler(templateId)) {
                    // 类型为 周期巡检 并且 周期巡检 未结束设置状态为 待执行
                    templateDao.updateStatus(templateId, InspectionJobStatusEnum.TO_BE_EXECUTED.getStatus());
                } else {
                    // 否则设置为已完成
                    templateDao.updateStatus(templateId, InspectionJobStatusEnum.COMPLETED.getStatus());
                }
                log.info("[inspection] Check for unfinished instance done.");
            } catch (Exception e) {
                log.error("[inspection] set instance failed error", e);
            }
        });
    }

    public void generateInstance(InspectionCondition condition) {
        instanceDao.generateInstance(condition);
        log.info("[inspection] Create inspection instance, templateId:{}, instanceId:{}", condition.getId(), condition.getInstanceId());
        // 执行巡检
        submitInspection(condition);
    }

    public void submitInspection(InspectionCondition condition) {
        // 提交到新线程执行巡检任务
        INSPECTION_EXECUTOR.submit(() -> {
            try {
                log.info("[inspection] Execute inspection instance, instanceId = {}", condition.getInstanceId());
                execInspection(condition);
                // 更新模版状态
                updateFinishStatus(condition.getId(), condition.getType());
            } catch (Exception e) {
                log.error("[inspection] Execute inspection instance", e);
            }
        });
    }

    /**
     * 更新 template 状态
     *
     * @param templateId .
     * @param type       {@link com.xylink.manager.inspection.entity.enums.InspectionSchedulerTypeEnum} 模版类型
     */
    public void updateFinishStatus(String templateId, Integer type) {
        if (type == InspectionInspectTypeEnum.scheduler.getType() && schedulerTimeDao.existNextExecInScheduler(templateId)) {
            // 类型为 周期巡检 并且 周期巡检 未结束设置状态为 待执行
            templateDao.updateStatus(templateId, InspectionJobStatusEnum.TO_BE_EXECUTED.getStatus());
        } else {
            // 否则设置为已完成
            templateDao.updateStatus(templateId, InspectionJobStatusEnum.COMPLETED.getStatus());
        }
    }

    /**
     * 执行巡检任务
     *
     * @param condition .
     */
    public void execInspection(InspectionCondition condition) {
        String instanceId = condition.getInstanceId();
        InspectionItemCondition items = condition.getInspectionItems();
        long startTime = System.currentTimeMillis();

        // 系统巡检
        if (CollectionUtils.isNotEmpty(items.getSystem())) {
            instanceDao.updateStep(instanceId, InspectionItemTypeEnum.SYSTEM.getId());
            log.info("begin system inspection, instanceId = {}", instanceId);
            InspectionTaskDb task = taskDao.createTask(instanceId, InspectionItemTypeEnum.SYSTEM.getId());
            log.info("create system task, taskId = {}", task.getId());
            List<InspectionSubTaskDb> subTasks = null;
            try {
                subTasks = systemService.execInspection(task.getId(), items.getSystem());
            } catch (Exception e) {
                taskDao.taskExecFailed(task, System.currentTimeMillis(), "系统巡检异常");
                log.warn("system inspect error: ", e);
            }
            taskDao.updateTask(task, System.currentTimeMillis(), subTasks);
            instanceDao.update(instanceId, InspectionItemTypeEnum.SYSTEM.getId(), task.getHighRisk(), task.getMiddleRisk(), task.getLowRisk());
            log.info("system inspection end, instanceId = {}", instanceId);
        }

        // 中间件巡检
        if (CollectionUtils.isNotEmpty(items.getMiddleware())) {
            instanceDao.updateStep(instanceId, InspectionItemTypeEnum.MIDDLEWARE.getId());
            log.info("begin middleware inspection, instanceId = {}", instanceId);
            InspectionTaskDb task = taskDao.createTask(instanceId, InspectionItemTypeEnum.MIDDLEWARE.getId());
            log.info("create middleware task, taskId = {}", task.getId());
            List<InspectionSubTaskDb> subTasks = null;
            try {
                subTasks = middlewareService.execInspection(task.getId(), items.getMiddleware());
            } catch (Exception e) {
                taskDao.taskExecFailed(task, System.currentTimeMillis(), "中间件巡检异常");
                log.warn("middle inspect error: ", e);
            }
            taskDao.updateTask(task, System.currentTimeMillis(), subTasks);
            instanceDao.update(instanceId, InspectionItemTypeEnum.MIDDLEWARE.getId(), task.getHighRisk(), task.getMiddleRisk(), task.getLowRisk());
            log.info("middleware inspection end, instanceId = {}", instanceId);
        }

        // 服务巡检
        if (CollectionUtils.isNotEmpty(items.getServer())) {
            instanceDao.updateStep(instanceId, InspectionItemTypeEnum.SERVER.getId());
            log.info("begin server inspection, instanceId = {}", instanceId);
            InspectionTaskDb task = taskDao.createTask(instanceId, InspectionItemTypeEnum.SERVER.getId());
            List<InspectionSubTaskDb> subTasks = null;
            try {
                subTasks = serverService.execInspection(task.getId(), items.getServer());
            } catch (Exception e) {
                taskDao.taskExecFailed(task, System.currentTimeMillis(), "服务巡检异常");
                log.warn("server inspect error: ", e);
            }
            taskDao.updateTask(task, System.currentTimeMillis(), subTasks);
            instanceDao.update(instanceId, InspectionItemTypeEnum.SERVER.getId(), task.getHighRisk(), task.getMiddleRisk(), task.getLowRisk());
            log.info("server inspection end, instanceId = {}", instanceId);
        }

        instanceDao.update(instanceId, startTime, InspectionJobStatusEnum.COMPLETED.getStatus());
    }

    /**
     * 判断当前是否有正在执行的巡检任务
     *
     * @return .
     */
    public boolean hasRunningInstance() {
        return instanceDao.hasRunningInstance();
    }
}
