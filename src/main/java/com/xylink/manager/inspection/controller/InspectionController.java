package com.xylink.manager.inspection.controller;


import com.xylink.manager.inspection.common.OpsManagerException;
import com.xylink.manager.inspection.entity.condition.InspectionCondition;
import com.xylink.manager.inspection.entity.condition.InspectionItemCondition;
import com.xylink.manager.inspection.entity.condition.InspectionTrendCondition;
import com.xylink.manager.inspection.entity.enums.InspectionInspectTypeEnum;
import com.xylink.manager.inspection.entity.vo.*;
import com.xylink.manager.inspection.service.InspectTemplateService;
import com.xylink.manager.inspection.service.InspectionService;
import com.xylink.manager.inspection.service.report.InspectionReportService;
import com.xylink.manager.inspection.utils.RegexpUtil;
import com.xylink.manager.model.common.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * [问题诊断]巡检 <a href="https://yapi.xylink.com/project/1479/interface/api/64472">接口</a>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/16 15:55
 */
@RestController
@RequestMapping("/external/inspection")
@Validated
@Slf4j
public class InspectionController {
    @Autowired
    private InspectionService inspectionService;
    @Autowired
    private InspectTemplateService templateService;
    @Autowired
    private InspectionReportService reportService;

    /**
     * 获取巡检项
     *
     * @return 巡检项
     */
    @GetMapping("/items/v1")
    public InspectionItemsVo items() {
        return inspectionService.items();
    }

    /**
     * 创建 临时/定时/周期 巡检
     *
     * @param condition .
     * @return 临时/定时/周期巡检 id
     */
    @PostMapping(value = "/submit/v1")
    public String submit(@RequestBody @Validated InspectionCondition condition) {
        InspectionItemCondition inspectionItems = condition.getInspectionItems();
        if (CollectionUtils.isEmpty(inspectionItems.getSystem())
                && CollectionUtils.isEmpty(inspectionItems.getMiddleware())
                && CollectionUtils.isEmpty(inspectionItems.getServer())) {
            throw new OpsManagerException("任务创建失败，必须选择一项巡检内容");
        }
        String name = condition.getName();
        if (RegexpUtil.containSpecialCharacter(name)) {
            throw new OpsManagerException("任务创建失败，名称中不允许出现非数字、字母、空格、汉字、_、-、—以外的字符");
        }
        return templateService.generateInspectTemplate(condition);
    }

    /**
     * 巡检结果
     *
     * @param size      页大小
     * @param page      当前页
     * @param startTime 查询时间范围 开始时间
     * @param endTime   结束时间
     * @param asc       是否按照时间排序
     * @return 巡检记录
     */
    @GetMapping("/record/list/v1")
    public Page<InspectionRecordVo> recordList(@RequestParam("size") int size,
                                               @RequestParam("page") int page,
                                               @RequestParam(name = "startTime", required = false) Long startTime,
                                               @RequestParam(name = "endTime", required = false) Long endTime,
                                               @RequestParam(name = "asc", required = false) Boolean asc) {
        return inspectionService.recordList(size, page, startTime, endTime, asc);
    }

    /**
     * 巡检列表
     *
     * @param size      .
     * @param page      .
     * @param startTime .
     * @param endTime   .
     * @param asc       是否按照时间排序
     * @return .
     */
    @GetMapping("/list/v1")
    public Page<InspectionTemplateVo> list(@RequestParam("size") int size,
                                           @RequestParam("page") int page,
                                           @RequestParam(name = "startTime", required = false) Long startTime,
                                           @RequestParam(name = "endTime", required = false) Long endTime,
                                           @RequestParam(name = "asc", required = false) Boolean asc) {
        return inspectionService.list(size, page, startTime, endTime, asc);
    }

    /**
     * 巡检任务参数
     *
     * @param id   临时/定时/周期巡检 id
     * @param type 巡检类型 {@link InspectionInspectTypeEnum}
     * @return 巡检任务参数信息
     */
    @GetMapping("/edit/detail/v1")
    public InspectionCondition templateDetail(@RequestParam("id") String id, @RequestParam("type") Integer type) {
        return templateService.templateDetail(id, type);
    }

    /**
     * 修改巡检任务
     *
     * @param condition 巡检任务参数
     */
    @PostMapping("/edit/v1")
    public void templateEdit(@RequestBody @Validated InspectionCondition condition) {
        InspectionItemCondition inspectionItems = condition.getInspectionItems();
        if (StringUtils.isBlank(condition.getId())) {
            throw new OpsManagerException("任务修改失败，必须指定id");
        }
        if (CollectionUtils.isEmpty(inspectionItems.getSystem())
                && CollectionUtils.isEmpty(inspectionItems.getMiddleware())
                && CollectionUtils.isEmpty(inspectionItems.getServer())) {
            throw new OpsManagerException("任务修改失败，必须选择一项巡检内容");
        }
        String name = condition.getName();
        if (RegexpUtil.containSpecialCharacter(name)) {
            throw new OpsManagerException("任务创建失败，名称中不允许出现非数字、字母、空格、汉字、_、-、—以外的字符");
        }
        Integer type = condition.getType();
        if (type == null || InspectionInspectTypeEnum.temp.getType() == type) {
            throw new OpsManagerException("任务修改失败，该巡检类型不允许修改");
        }
        templateService.templateEdit(condition);
    }

    /**
     * 禁用巡检任务，仅支持未开始的定时巡检和未结束的周期巡检
     *
     * @param id   定时/周期巡检 id
     * @param type 巡检类型 {@link InspectionInspectTypeEnum}
     */
    @PostMapping("/disable/v1")
    public void disable(@RequestParam("id") String id, @RequestParam("type") Integer type) {
        if (StringUtils.isBlank(id)) {
            throw new OpsManagerException("任务修改失败，必须指定id");
        }
        if (InspectionInspectTypeEnum.time.getType() != type
                && InspectionInspectTypeEnum.scheduler.getType() != type) {
            throw new OpsManagerException("任务修改失败，该巡检类型不允许修改");
        }
        templateService.disable(id, type);
    }

    /**
     * 巡检详情
     *
     * @param jobId 巡检任务id
     * @return 巡检详情
     */
    @GetMapping("/detail/v1")
    public InspectionDetailVo detail(@RequestParam("jobId") String jobId) {
        return inspectionService.detail(jobId);
    }

    @GetMapping("/detail/middleware/v1")
    public Page<InspectionMetricRecordVo> middlewareDetail(@RequestParam("subTaskId") String subTaskId,
                                                           @RequestParam("size") Integer size,
                                                           @RequestParam("page") Integer page,
                                                           @RequestParam(name = "ladder", required = false) String ladderStr) {
        return inspectionService.middlewareDetail(subTaskId, size, page, splitLadderString(ladderStr));
    }

    @GetMapping("/detail/system/v1")
    public SystemRecordPageVo systemDetail(@RequestParam("taskId") String taskId,
                                           @RequestParam("size") Integer size,
                                           @RequestParam("page") Integer page,
                                           @RequestParam(name = "ladder", required = false) String ladderStr) {
        SystemRecordPageVo systemRecordPageVo = inspectionService.systemDetail(taskId, size, page, splitLadderString(ladderStr));
        systemRecordPageVo.getRecords().forEach(inspectionSystemRecordVo -> {
                    String itemName = inspectionSystemRecordVo.getNodeName();
                    if (itemName.contains("(")) {
                        inspectionSystemRecordVo.setNodeIp(itemName.substring(0, itemName.lastIndexOf("(")));
                    }
                }
        );
        return systemRecordPageVo;
    }

    @GetMapping("/server/expand/v1")
    public InspectionServerExpandVo serverExpand(@RequestParam("taskId") String taskId,
                                                 @RequestParam("size") Integer size,
                                                 @RequestParam("page") Integer page,
                                                 @RequestParam(name = "ident", required = false) String ident,
                                                 @RequestParam(name = "ladder", required = false) String ladderStr) {
        // 如果筛选存在去除开头结尾空格
        if (StringUtils.isNotBlank(ident)) {
            ident = ident.trim();
        }
        return inspectionService.serverExpand(taskId, size, page, ident, splitLadderString(ladderStr));
    }

    @GetMapping("/detail/server/v1")
    public Page<InspectionMetricRecordVo> serverDetail(@RequestParam("subTaskId") String subTaskId,
                                                       @RequestParam("size") Integer size,
                                                       @RequestParam("page") Integer page,
                                                       @RequestParam(name = "ladder", required = false) String ladderStr) {
        return inspectionService.serverDetail(subTaskId, size, page, splitLadderString(ladderStr));
    }

    @PostMapping("/system/trend/v1")
    public Object systemTrend(@RequestBody @Validated InspectionTrendCondition trendCondition) {
        return inspectionService.systemTrend(trendCondition);
    }


    @GetMapping("/export/state/v1")
    public Object exportState(@RequestParam("jobId") String jobId,
                              @RequestParam(name = "state", required = false) String state) {
        // 当调用接口传 state 字段时表示除了需要返回状态，还要导出文件
        boolean export = StringUtils.isNotBlank(state);
        return reportService.exportState(jobId, export);
    }

    @GetMapping("/export/v1")
    public void export(@RequestParam("jobId") String jobId, HttpServletResponse response) {
        reportService.export(jobId, response);
    }

    private List<Integer> splitLadderString(String ladderStr) {
        if (StringUtils.isBlank(ladderStr)) {
            return Collections.emptyList();
        }
        List<Integer> ladders = new ArrayList<>();

        String[] split = ladderStr.split(",");
        for (String s : split) {
            ladders.add(Integer.parseInt(s));
        }
        return ladders;
    }
}
