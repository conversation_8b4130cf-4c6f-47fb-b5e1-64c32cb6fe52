package com.xylink.manager.inspection.job;

import com.xylink.manager.inspection.dao.InspectionExportRecordDao;
import com.xylink.manager.inspection.entity.common.InspectionConfig;
import com.xylink.manager.inspection.entity.db.InspectionExportRecordDb;
import com.xylink.manager.inspection.entity.enums.InspectionRecordStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/14 10:33
 */
@Component
@Slf4j
public class SchedulerDeleteReportJob {
    @Autowired
    private InspectionConfig inspectionConfig;

    @Autowired
    private InspectionExportRecordDao inspectionExportRecordDao;
    /**
     * 每日凌晨一点删除7天前的巡检报告
     */
    @Scheduled(cron = "0 0 1 * * ?")
    private void deleteInspectionReport() {
        try {
            if (!inspectionConfig.isEnable()) {
                return;
            }
            // 启用巡检
            long nowTime = System.currentTimeMillis();
            long sevenDayEarlier = nowTime - TimeUnit.DAYS.toMillis(7);
            String time = DateFormatUtils.format(sevenDayEarlier, "yyyy-MM-dd HH:mm:ss");
            List<InspectionExportRecordDb> exportRecordDbs = inspectionExportRecordDao.getRecordByTime(time);
            for (InspectionExportRecordDb exportRecordDb : exportRecordDbs) {
                String instanceId = exportRecordDb.getInstanceId();
                String filePath = exportRecordDb.getFilePath();
                log.info("[inspection] instanceId {}, file path {}", instanceId, filePath);
                try {
                    File file = new File(filePath);
                    if (file.exists()) {
                        boolean delete = file.delete();
                        log.error("[inspection] delete file, file path: {}, {}", filePath, delete);
                    }
                } catch (Exception e) {
                    log.error("[inspection] delete file error, file path: {}", filePath);
                }
                inspectionExportRecordDao.updateRecord(instanceId, InspectionRecordStateEnum.DELETED.getState(), "");
            }
        } catch (Exception e) {
            log.error("[inspection]删除巡检报告出现异常", e);
        }
    }
}
