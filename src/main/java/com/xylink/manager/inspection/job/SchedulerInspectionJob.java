package com.xylink.manager.inspection.job;

import com.xylink.manager.inspection.entity.common.InspectionConfig;
import com.xylink.manager.inspection.entity.condition.InspectionCondition;
import com.xylink.manager.inspection.entity.db.InspectionSchedulerTimeDb;
import com.xylink.manager.inspection.entity.enums.InspectionInspectTypeEnum;
import com.xylink.manager.inspection.entity.enums.InspectionJobStatusEnum;
import com.xylink.manager.inspection.entity.enums.InspectionSchedulerTypeEnum;
import com.xylink.manager.inspection.service.InspectInstanceService;
import com.xylink.manager.inspection.service.InspectTemplateService;
import com.xylink.manager.inspection.utils.CronUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/29 17:48
 */
@Component
@Slf4j
public class SchedulerInspectionJob {
    @Autowired
    private InspectTemplateService templateService;
    @Autowired
    private InspectInstanceService instanceService;
    @Autowired
    private InspectionConfig inspectionConfig;

    /**
     * 一分钟查找一次数据库，检查是否有需要执行的巡检任务
     */
    @Scheduled(cron = "0 * * * * ?")
    private void schedule() {
        try {
            // 启用巡检
            if (inspectionConfig.isEnable()) {
                checkSchedulerJob();
            }
        } catch (Exception e) {
            log.error("[inspection]检查周期巡检或创建巡检任务时出现异常", e);
        }
    }

    /**
     * 检查是否有需要执行的定时/周期巡检的任务
     */
    private void checkSchedulerJob() {
        log.info("[inspection] Check whether periodic inspection tasks need to be performed.");
        Long timestamp = System.currentTimeMillis();
        List<InspectionSchedulerTimeDb> schedulerTimeDbs = templateService.schedulerTimeList(1, 1, timestamp);
        // 没有启用或状态为未开始的定时任务
        if (CollectionUtils.isEmpty(schedulerTimeDbs)) {
            return;
        }
        InspectionSchedulerTimeDb schedulerTimeDb = schedulerTimeDbs.get(0);
        Long startTime = schedulerTimeDb.getNextExecTime();
        log.info("[inspection] templateId:{}, startTimestamp:{}", schedulerTimeDb.getConfigId(), schedulerTimeDb.getSchedulerStartTime());
        // 当前时间小于任务时间，即没有到达执行时间
        if (timestamp.compareTo(startTime) < 0) {
            return;
        }
        // 当前有正在运行的任务
        if (instanceService.hasRunningInstance()) {
            return;
        }

        String schedulerType = schedulerTimeDb.getSchedulerType();
        // 类型为 once，表示为定时巡检
        if (InspectionSchedulerTypeEnum.ONCE.getType().equals(schedulerType)) {
            templateService.updateSchedulerFinished(schedulerTimeDb.getId());
            InspectionCondition condition = templateService.templateDetail(schedulerTimeDb.getConfigId(), InspectionInspectTypeEnum.time.getType());
            log.info("[inspection] 定时巡检到达巡检时间, templateId:{}, startTime:{}", schedulerTimeDb.getConfigId(), startTime);
            instanceService.generateInstance(condition);
            templateService.updateTemplateStatus(schedulerTimeDb.getConfigId(), InspectionJobStatusEnum.RUNNING.getStatus());
            return;
        }

        String cronExpression = schedulerTimeDb.getCronExpression();
        long nextStartTime = CronUtil.nextStartTime(cronExpression);
        long schedulerEndTime = schedulerTimeDb.getSchedulerEndTime();

        if (nextStartTime > schedulerEndTime) {
            // 下次运行时间在周期外，设置触发时间为非法制
            templateService.updateSchedulerFinished(schedulerTimeDb.getId());
        } else {
            // 下次运行时间在周期内，更新下次触发时间
            templateService.updateSchedulerNextTime(schedulerTimeDb.getId(), nextStartTime);
        }
        // 执行巡检任务
        InspectionCondition condition = templateService.templateDetail(schedulerTimeDb.getConfigId(), InspectionInspectTypeEnum.scheduler.getType());
        log.info("[inspection] 周期巡检到达巡检时间, templateId:{}, startTime:{}", schedulerTimeDb.getConfigId(), startTime);
        instanceService.generateInstance(condition);
        templateService.updateTemplateStatus(schedulerTimeDb.getConfigId(), InspectionJobStatusEnum.RUNNING.getStatus());
    }
}
