package com.xylink.manager.inspection.constants;


import com.xylink.config.NetworkConstants;

public class MiddlewareConstants {
    public static final String MYSQL_AUXI_ENABLE = "MYSQL_MULTI";
    public static final String MYSQL_STATIS_ENABLE = "MYSQL_MULTI";
    public static final String MYSQL_MAIN_HOST = "DATABASE_MASTER_IP";
    public static final String DB_MAIN_NAME = "db-main";
    public static final String MYSQL_STATIS_HOST = "DATABASE_STATIS_IP";
    public static final String MYSQL_STATIS_NAME = "mysql-statis";
    public static final String MYSQL_AUXI_HOST = "DATABASE_AUXI_IP";
    public static final String MYSQL_AUXI_NAME = "mysql-auxi";
    public static final String MYSQL_PORT = NetworkConstants.DATABASE_PORT;

    public static final String KAFKA_STATIS = "kafka-statis";
    public static final String KAFKA_CLUSTER = "kafka-cluster";
    public static final String KAFKA_STATIS_ENABLE = "GLOBAL_KAFKA_STATIS_SWITCH";
    public static final String KAFKA_STATIS_HOST = "GLOBAL_KAFKA_STATIS_ADDR";
    public static final String KAFKA_SINGLE = "kafka-single";
    public static final String KAFKA_SINGLE_HOST = "GLOBAL_KAFKA_NO_STATIS_ADDR";

    /**
     * redis
     */
    public static final String REDIS_SINGLE = "craftmaster-redis";
    public static final String REDIS_SENTINEL = "craftmaster-sentinel";
    public static final String REDIS_PASSWORD = "MID_REDIS6_PASSWORD";
    public static final String REDIS_SENTINEL_PASSWORD = "MID_REDIS_PASSWORD";
    public static final String REDIS_USER = "REDIS6_USER";
    public static final String REDIS_MODE = "redis.redis_mode";
    public static final String REDIS_MODE_SINGLE = "single";
    public static final String REDIS_HOST = "redis.host";
    public static final String REDIS_PORT = "redis.port";
    public static final String REDIS_REDISMASTER_SENTINEL = "redis.redismaster_sentinel";
}
