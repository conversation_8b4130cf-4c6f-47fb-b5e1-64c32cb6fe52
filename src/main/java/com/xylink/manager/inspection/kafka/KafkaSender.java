package com.xylink.manager.inspection.kafka;

import com.xylink.util.SpringBeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

/**
 * <AUTHOR>
 * @version V1.0.0
 * @date 2023/06/09
 **/
@Slf4j
public class KafkaSender {
    private final KafkaTemplate<String, String> kafkaTemplate;

    public static KafkaSender getInstance() {
        return new KafkaSender();
    }

    private KafkaSender() {
        KafkaConfig kafkaConfig = SpringBeanUtil.getBean("kafkaConfig");
        kafkaTemplate = kafkaConfig.kafkaTemplate();
    }

    public void sendMessage(String kafkaTopic, final String key, final String text) {
        ListenableFuture<SendResult<String, String>> future = kafkaTemplate.send(kafkaTopic, key, text);
        future.addCallback(new ListenableFutureCallback<SendResult<String, String>>() {
            @Override
            public void onFailure(Throwable ex) {
                log.error("Failed to send: " + text, ex);
            }

            @Override
            public void onSuccess(SendResult result) {
                log.info("kafka send success: " + text + " and result is :" + result.toString());
            }
        });
    }

    public void sendMessage(String kafkaTopic, final String text) {
        sendMessage(kafkaTopic, null, text);
    }
}
