package com.xylink.manager.inspection.kafka.consumer;

import com.xylink.manager.inspection.entity.bo.service.KafkaInspectionAckMsgBO;
import com.xylink.manager.inspection.kafka.KafkaConfig;
import com.xylink.manager.inspection.service.ServiceInspection;
import com.xylink.config.util.JsonUtil;
import com.xylink.util.SpringBeanUtil;
import io.netty.util.concurrent.DefaultThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.springframework.kafka.core.ConsumerFactory;

import java.time.Duration;
import java.util.Collections;
import java.util.concurrent.*;

/**
 * 接受kafka巡检的ack消息
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2023-06-09
 */
@Slf4j
public class KafkaInspectionConsumer {

    private final KafkaConfig kafkaConfig;
    private final ServiceInspection serviceInspection;

    public static final ExecutorService EXECUTOR = new ThreadPoolExecutor(1, 1,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(),
            new DefaultThreadFactory("inspection-ack-request"));

    private KafkaInspectionConsumer() {
        kafkaConfig = SpringBeanUtil.getBean("kafkaConfig");
        serviceInspection = SpringBeanUtil.getBean("serviceInspection");
    }

    public static void createInstance(CountDownLatch countDownLatch) {
        KafkaInspectionConsumer instance = new KafkaInspectionConsumer();
        instance.init(countDownLatch);
    }


    public void init(CountDownLatch countDownLatch) {
        Runnable handleInspectionRequest = () -> handleInspectionRequest(countDownLatch);
        EXECUTOR.submit(handleInspectionRequest);
    }

    private void handleInspectionRequest(CountDownLatch countDownLatch) {
        log.info("[inspection] activate handleInspectionRequest, consumer inspection-ack topic");
        Consumer<String, String> consumer = null;
        try {
            ConsumerFactory<String, String> consumerFactory = kafkaConfig.consumerFactory(kafkaConfig.getInspectionKafkaBrokers());
            consumer = consumerFactory.createConsumer();
            consumer.subscribe(Collections.singletonList(kafkaConfig.getInspectionAskTopic()));
            testConsumer(consumer, countDownLatch);
            pullInspectionRequest(consumer);
        } finally {
            if (consumer != null) {
                consumer.close();
            }
        }
    }

    /**
     * 检查是否可以连接到 kafka
     */
    private static void testConsumer(Consumer<String, String> consumer, CountDownLatch countDownLatch) {
        log.info("[inspection] check consumer");
        // 校验连接
        consumer.poll(Duration.ofMillis(1000));
        countDownLatch.countDown();
    }

    @SuppressWarnings("InfiniteLoopStatement")
    private void pullInspectionRequest(Consumer<String, String> consumer) {
        while (true) {
            try {
                ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(1000));
                for (ConsumerRecord<String, String> record : records) {
                    receive(record);
                }
            } catch (Exception e) {
                log.error("[inspection] get kafka ack msg error.", e);
            }
        }
    }

    private void receive(ConsumerRecord<?, String> record) {
        String value = record.value();
        if (StringUtils.isBlank(value)) {
            return;
        }
        log.info("KafkaInspectionConsumer value:{}", value);
        try {
            KafkaInspectionAckMsgBO ackMsg = JsonUtil.parseJson(value, KafkaInspectionAckMsgBO.class);
            if (serviceInspection.getAckMsgMap().containsKey(ackMsg.getInspectionId())) {
                serviceInspection.getAckMsgMap().get(ackMsg.getInspectionId()).put(ackMsg.getPodName(), ackMsg);
            } else {
                log.error("KafkaInspectionConsumer inspectionId not exists, value:{}", value);
            }
        } catch (Exception e) {
            log.error("value:{} ", record, e);
        }
    }

}
