package com.xylink.manager.inspection.kafka;

import com.xylink.config.Constants;
import com.xylink.config.K8sSvcConstants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.base.K8sSvcService;
import com.xylink.manager.service.db.JasyptService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.*;

import java.util.HashMap;
import java.util.Map;

/**
 * kafka配置
 */
@Configuration
@Slf4j
public class KafkaConfig {
    @Value("${inspection.kafka.broker.key:kafka.inspection.brokers}")
    private String kafkaBrokersConfigKey;
    @Value("${inspection.consumer.group-id:inspection-server}")
    private String groupId;
    @Value("${inspection.kafka.topic.inspectionTopic:inspection-metric}")
    private String inspectionTopic;
    @Value("${inspection.kafka.topic.inspectionAckTopic:inspection-metric-ack}")
    private String inspectionAskTopic;
    @Autowired
    private K8sService k8sService;
    @Autowired
    private K8sSvcService k8sSvcService;
    @Autowired
    private JasyptService jasyptService;

    public String getInspectionKafkaBrokers() {
        Map<String, String> config = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        String kafkaBrokers = config.get(NetworkConstants.KAFKA_INTERNAL_IP);
        String kafkaSvcIp = k8sSvcService.getServiceIpByDefaultNs(K8sSvcConstants.KAFKA_NAME);
        if (StringUtils.isNotBlank(kafkaSvcIp)) {
            kafkaBrokers = kafkaSvcIp;
        }
        if (!kafkaBrokers.endsWith(":9093")) {
            kafkaBrokers += ":9093";
        }
        log.info("[inspection] inspection kafka brokers: {}, config: {}", kafkaBrokers, config);
        return kafkaBrokers;
    }

    public KafkaTemplate<String, String> kafkaTemplate() {
        return new KafkaTemplate<>(producerFactory(getInspectionKafkaBrokers()));
    }

    private ProducerFactory<String, String> producerFactory(String kafkaHosts) {
        return new DefaultKafkaProducerFactory<>(senderProps(kafkaHosts));
    }

    public ConsumerFactory<String, String> consumerFactory(String kafkaHosts) {
        return new DefaultKafkaConsumerFactory<>(consumerProps(kafkaHosts));
    }


    private Map<String, Object> senderProps(String kafkaHosts) {
        Map<String, String> config = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaHosts);
        props.put(ProducerConfig.RETRIES_CONFIG, 0);
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 16384);
        props.put(ProducerConfig.LINGER_MS_CONFIG, 1);
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 10240 * 1024);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG,
                "org.apache.kafka.common.serialization.StringSerializer");
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG,
                "org.apache.kafka.common.serialization.StringSerializer");
        return addAuthorizeProps(config, props);
    }


    private Map<String, Object> consumerProps(String kafkaHosts) {
        Map<String, String> config = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaHosts);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, groupId);
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG,
                "org.apache.kafka.common.serialization.StringDeserializer");
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG,
                "org.apache.kafka.common.serialization.StringDeserializer");
        return addAuthorizeProps(config, props);
    }

    private Map<String, Object> addAuthorizeProps(Map<String, String> allIp, Map<String, Object> props) {
        boolean kafkaAuthorize = allIp.containsKey("KAFKA_AUTHORIZE") && Boolean.parseBoolean(allIp.get("KAFKA_AUTHORIZE"));
        if (kafkaAuthorize) {
            props.put("security.protocol", allIp.get("KAFKA_AUTH_ENABLE_SECURITY_PROTOCOL"));
            props.put("sasl.mechanism", allIp.get("KAFKA_AUTH_ENABLE_SASL_MECHANISMS"));
            props.put("sasl.jaas.config", jasyptService.decrypt(allIp.get("KAFKA_AUTH_ENABLE_SASL_CONFIG")));
        }
        return props;
    }

    public String getGroupId() {
        return groupId;
    }

    public String getInspectionTopic() {
        return inspectionTopic;
    }

    public String getInspectionAskTopic() {
        return inspectionAskTopic;
    }
}
