package com.xylink.manager.inspection.entity.vo;

import com.xylink.manager.model.common.Page;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/20 16:45
 */
@Getter
@Setter
@Accessors(chain = true)
public class SystemRecordPageVo extends Page<InspectionSystemRecordVo> {
    private ItemDesc itemDesc;

    public SystemRecordPageVo(int pageIndex, int pageSize) {
        super(pageIndex, pageSize);
    }

    public SystemRecordPageVo(int pageIndex, int pageSize, Long total, List<InspectionSystemRecordVo> records) {
        super(pageIndex, pageSize, total, records);
    }

    public static SystemRecordPageVo emptyPage(int pageIndex, int pageSize) {
        return new SystemRecordPageVo(pageIndex, pageSize, 0L, new ArrayList<>());
    }

    public SystemRecordPageVo(int pageIndex, int pageSize, Long total, List<InspectionSystemRecordVo> records, ItemDesc itemDesc) {
        super(pageIndex, pageSize, total, records);
        this.itemDesc = itemDesc;
    }


    @Data
    @Builder
    public static class ItemDesc {
        String disk;
        String cpu;
        String memory;
    }
}
