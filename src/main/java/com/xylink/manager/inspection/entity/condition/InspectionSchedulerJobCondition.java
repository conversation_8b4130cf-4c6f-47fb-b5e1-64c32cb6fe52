package com.xylink.manager.inspection.entity.condition;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/10 15:09
 *
 * 创建周期巡检所需参数
 */
@Data
public class InspectionSchedulerJobCondition {
    /**
     * 周期巡检类型 week: 间隔周； month: 间隔月
     */
    @NotNull
    private String schedulerType;

    /**
     * 周期开始时间, eg "2023-01-06"
     */
    @NotNull
    private String startDate;

    private Long startTime;

    /**
     * 周期结束时间, eg "2023-01-08"
     */
    @NotNull
    private String endDate;

    private Long endTime;

    /**
     * 以周几为触发条件
     */
    private List<WeekScheduler> weekSchedulers;
    /**
     * 以每月第几日为触发条件
     */
    private List<MonthScheduler> monthSchedulers;

    @Data
    public static class WeekScheduler {
        /**
         * 周几, eg 1
         */
        private Integer week;
        /**
         * 当天开始时间, eg "07:00"
         */
        private String startTime;
    }

    @Data
    public static class MonthScheduler {
        /**
         * 每月第几日, eg 5
         */
        private Integer day;
        /**
         * 当天开始时间, eg "07:00"
         */
        private String startTime;
    }
}
