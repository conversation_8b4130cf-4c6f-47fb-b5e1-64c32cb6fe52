package com.xylink.manager.inspection.entity.vo;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/13 16:17
 */
@Data
@Builder
public class InspectionDetailVo {
    long createTime;
    String createUser;
    String name;
    int status;
    int step;
    Records records;

    @Data
    @Builder
    public static class Records {
        InspectionTaskRecordVo system;
        InspectionTaskRecordVo middleware;
        InspectionTaskRecordVo server;
    }
}
