package com.xylink.manager.inspection.entity.condition;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/2 23:12
 */
@Data
public class DefaultMonitor {
    String name;
    String node_path;
    List<Tags> tags;

    @Data
    public static class Tags {
        String name;
        String weight;
        List<Charts> charts;
    }

    @Data
    public static class Charts {
        Configs configs;
        String weight;
    }

    @Data
    public static class Configs {
        String title;
        String type;
        List<Metrics> metrics;
    }

    @Data
    public static class Metrics {
        String query;
    }
}
