package com.xylink.manager.inspection.entity.bo;

import com.xylink.manager.inspection.entity.db.InspectionServiceListDb;
import com.xylink.manager.inspection.entity.vo.ServerInstanceVO;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/10 20:03
 */
@Data
public class InspectPodInfoBo {
    /**
     * 应用在元数据的编号
     */
    String appId;
    /**
     * 应用名
     */
    String appName;
    /**
     * pod 名，如果没有启动，pod 为空
     */
    String podName;
    /**
     * ident (服务名)
     */
    String ident;
    /**
     * 是否部署
     */
    boolean deployed;
    /**
     * 是否支持巡检
     */
    boolean supportInspection;
    /**
     * 如果支持巡检，巡检方式等内容会放在改熟悉中
     */
    InspectionServiceListDb serviceListDb;
    /**
     * 如果 pod 存在，这里保存 pod 的基本信息
     */
    ServerInstanceVO serverInstanceVO;
}
