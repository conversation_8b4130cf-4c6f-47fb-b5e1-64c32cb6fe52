package com.xylink.manager.inspection.entity.jasperreport;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/21 15:44
 */

/**
 * 巡检报告参数枚举
 * <AUTHOR>
 */
public enum InspectionPdfParametersEnum  {
    /**
     * 编号，对应parameter名称
     */
    NO("inspection.no", "no", "巡检编号"),
    NAME("inspection.name", "name", "巡检名称"),
    CREATE_USER("inspection.createUser", "createUser", "巡检人"),
    CREATE_TIME("inspection.createTime", "createTime", "创建时间"),
    CONSUMED_TIME("inspection.consumedTime", "consumedTime", "巡检耗时"),
    TYPE("inspection.type", "type", "巡检类型"),
    HOST_HIGH_RISK("inspection.host.highRisk", "hostHighRisk", "主机巡检高风险数量"),
    HOST_MIDDLE_RISK("inspection.host.middleRisk", "hostMiddleRisk", "主机巡检中风险数量"),
    HOST_LOW_RISK("inspection.host.lowRisk", "hostLowRisk", "主机巡检低风险数量"),
    MIDDLE_HIGH_RISK("inspection.middle.highRisk", "middleHighRisk", "中间件巡检高风险数量"),
    MIDDLE_MIDDLE_RISK("inspection.middle.middleRisk", "middleMiddleRisk", "中间件巡检中风险数量"),
    MIDDLE_LOW_RISK("inspection.middle.lowRisk", "middleLowRisk", "中间件巡检低风险数量"),
    SERVICE_HIGH_RISK("inspection.service.highRisk", "serviceHighRisk", "服务巡检高风险数量"),
    SERVICE_MIDDLE_RISK("inspection.service.middleRisk", "serviceMiddleRisk", "服务巡检中风险数量"),
    SERVICE_LOW_RISK("inspection.service.lowRisk", "serviceLowRisk", "服务巡检低风险数量"),
    SHOW_HOST_DETAIL("inspection.show.host", "showHost", "主机Detail展示开关"),
    SHOW_MIDDLE_DETAIL("inspection.show.middle", "showMiddle", "中间件Detail展示开关"),
    SHOW_SERVICE_DETAIL("inspection.show.service", "showService", "服务Detail展示开关"),
    ;

    private final String paramName;
    private final String propertyName;

    InspectionPdfParametersEnum(String paramName, String propertyName, String desc) {
        this.paramName = paramName;
        this.propertyName = propertyName;
    }

    public String getParamName() {
        return paramName;
    }

    public String getPropertyName() {
        return propertyName;
    }
}
