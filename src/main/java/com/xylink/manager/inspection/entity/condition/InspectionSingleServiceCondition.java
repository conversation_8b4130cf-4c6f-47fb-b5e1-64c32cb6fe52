package com.xylink.manager.inspection.entity.condition;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/22 19:49
 */
@Data
public class InspectionSingleServiceCondition {
    @NotNull(message = "服务名称不能为空")
    private String controllerName;
    @NotNull(message = "应用id不能为空")
    private String appId;
    @NotNull(message = "应用名称不能为空")
    private String appName;
}
