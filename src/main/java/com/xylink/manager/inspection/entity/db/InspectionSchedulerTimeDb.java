package com.xylink.manager.inspection.entity.db;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/16 09:51
 */
@Data
public class InspectionSchedulerTimeDb {
    /**
     * 主键 id
     */
    private String id;
    /**
     * 周期巡检 id
     */
    private String configId;
    /**
     * 周期巡检类型 week: 间隔周；month:  间隔月
     */
    private String schedulerType;
    /**
     * week类型，每周第几天
     */
    private Integer schedulerWeek;
    /**
     * month类型，每月第多少天
     */
    private Integer schedulerMonth;
    /**
     * 开始时间，eg. 07:00
     */
    private String startTime;
    /**
     * 巡检周期开始时间
     */
    private Long schedulerStartTime;
    /**
     * 巡检周期结束时间
     */
    private Long schedulerEndTime;
    private Integer schedulerEnable;
    private Long nextExecTime;
    private String cronExpression;
    private Long createTime;
    private Integer isTop;
}
