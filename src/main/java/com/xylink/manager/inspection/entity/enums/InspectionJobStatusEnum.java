package com.xylink.manager.inspection.entity.enums;

/**
 * 任务状态
 * 0:未开始
 * 1:待执行
 * 2:进行中
 * 3:已结束
 * <br/>
 * 周期性巡检： 未开始、进行中（有真正巡检的任务的时候为进行中）、待执行（巡检周期未到期）、已结束 <br/>
 * 临时巡检：进行中、已结束<br/>
 * 定时巡检：未开始、进行中、已结束<br/>
 * <br/>
 * 可编辑状态为：未开始、进行中（只有周期性巡检）、待执行（周期性巡检）
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/12 18:06
 */
public enum InspectionJobStatusEnum {
    EXCEPTION(-1, "异常"),
    // 任务状态：0: 未开始；1: 待执行；2: 进行中；3: 已结束
    NOT_STARTED(0, "未开始"),
    TO_BE_EXECUTED(1, "待执行"),
    RUNNING(2, "进行中"),
    COMPLETED(3, "已结束");

    private final int status;

    InspectionJobStatusEnum(int status, String description) {
        this.status = status;
    }

    public int getStatus() {
        return status;
    }

    public static InspectionJobStatusEnum valueOf(int value) {
        for (InspectionJobStatusEnum x : values()) {
            if (x.getStatus() == value) {
                return x;
            }
        }
        return null;
    }
}
