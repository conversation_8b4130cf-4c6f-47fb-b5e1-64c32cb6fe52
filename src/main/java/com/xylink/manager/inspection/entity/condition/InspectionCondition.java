package com.xylink.manager.inspection.entity.condition;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 创建巡检人任务所需参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/16 16:18
 */
@Data
public class InspectionCondition {
    /**
     * template Id
     */
    private String id;

    private String instanceId;
    /**
     * 巡检人
     */
    @NotBlank(message = "巡检人不能为空")
    private String createUser;

    /**
     * 巡检类型
     * 1: 周期巡检
     * 2: 定时巡检
     * 3: 临时巡检
     */
    @NotNull(message = "巡检类型不能为空")
    private Integer type;

    @NotBlank(message = "巡检名称不能为空")
    @Size(max = 128)
    private String name;

    /**
     * 创建定时巡检，指定巡检时间
     */
    private String timerJob;

    private Long timerJobTime;

    /**
     * 创建周期巡检，周期巡检参数
     */
    private InspectionSchedulerJobCondition schedulerJob;

    /**
     * 三种巡检类型至少有一项不为空
     */
    @NotNull(message = "巡检项不能为空")
    private InspectionItemCondition inspectionItems;
}
