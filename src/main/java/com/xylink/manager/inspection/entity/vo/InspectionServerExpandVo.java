package com.xylink.manager.inspection.entity.vo;

import com.xylink.manager.model.common.Page;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/10 17:12
 */
@Data
@Builder
public class InspectionServerExpandVo {
    String createUser;
    Long createTime;
    String name;
    Page<Record> pages;

    @Data
    @Builder
    public static class Record {
        String podName;
        String ident;
        String serverName;
        Integer serverStatus;
        Long finishedTime;
        String subTaskId;
        Integer highRisk;
        Integer middleRisk;
        Integer lowRisk;
    }
}
