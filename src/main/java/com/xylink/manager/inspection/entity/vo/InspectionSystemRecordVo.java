package com.xylink.manager.inspection.entity.vo;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/2 23:42
 */
@Data
@Builder
public class InspectionSystemRecordVo {
    String nodeName;
    String nodeIp;
    long finishTime;
    Metric disk;
    Metric cpu;
    Metric memory;

    @Data
    @Builder
    public static class Metric {
        String value;
        Integer ladder;
        String metricName;
    }
}
