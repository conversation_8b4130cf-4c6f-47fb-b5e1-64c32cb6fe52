package com.xylink.manager.inspection.entity.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/22 19:08
 */
@Data
public class InspectionSingleServiceVo {
    private String serverName;
    private String podName;
    private String ident;
    private Integer serverStatus;
    private Integer highRisk;
    private Integer middleRisk;
    private Integer lowRisk;
    private List<Result> results;

    @Data
    public static class Result {
        private String metricName;
        private String metricDesc;
        private String risk;
        private Integer ladder;
        private String remark;
        private String suggest;
    }
}
