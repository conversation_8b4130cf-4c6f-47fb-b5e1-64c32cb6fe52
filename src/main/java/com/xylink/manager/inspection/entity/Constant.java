package com.xylink.manager.inspection.entity;

import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName: Constant
 * @Description:
 * <AUTHOR> @Date 2022/11/9
 * @Version 1.0
 */
public class Constant {
    public static final String DEFAULT = "DEFAULT";
    public static final String HOST = "HOST";
    public static final String MCSERVER = "mcserver";
    public static final String DOT = ".";
    public static final String DASH = "-";
    public static final String UNDERLINE = "_";
    public static final String PRIVATE = "private";
    public static final String PRIVATE_VERSION;
    public static final String CONFIGMAP_SUFFIX = "config";
    public static final String OPS_SEVER_NAME = "tiangong";
    public static final String OPS_SEVER_NAME_MID = "tiangong-mid";
    public static final String OPS_SEVER_NAME_FRONTEND = "frontend-tiangong";
    public static final String XY_CONFIG_CENTER = "noah";
    public static final String SIGAPP_CONTROLLER_MANAGER = "sigapp-controller-manager";
    public static final String XYLINKAPP_CONTROLLER_MANAGER = "xylinkapp-controller-manager";
    public static final String GO_HTTP_SERVER = "gohttpserver";
    public static final String RUNNING = "Running";
    public static final String LOG_TIMEOUT_DAY = "log_timeout_day";
    public static final String LOG_PERCENTAGE = "log_percentage";
    public static final String CLIENT_DOWNLOAD_CDN = "CLIENT_DOWNLOAD_CDN";
    public static final String CRD_API_VERSION = "app.ops.xylik.com/v1";
    public static final String CRD_KIND = "XylinkApp";
    public static final String SIG_KIND = "Sigapp";
    public static final String DEPLOYMENT_KIND = "Deployment";
    public static final String STATEFULSET_KIND = "StatefulSet";
    public static final String DAEMONSET_KIND = "DaemonSet";
    public static final String SERVICE_KIND = "Service";
    public static final String PV_KIND = "PersistentVolume";
    public static final String PVC_KIND = "PersistentVolumeClaim";
    public static final String SECRET_KIND = "Secret";
    public static final String SA_KIND = "ServiceAccount";
    public static final String CLUSTERROLE_KIND = "ClusterRole";
    public static final String CLUSTERROLEBINDING_KIND = "ClusterRoleBinding";
    public static final String ROLEBINDING_KIND = "RoleBinding";
    public static final String ROLE_KIND = "Role";
    public static final String SIG_SERVER = "sigserver";
    public static final String PROXY_SIG = "proxysig";
    public static final String ACCESS_SIG = "accesssig";
    public static final String HOST_NAME = "hostname";
    public static final String SECURITY_GROUP_CONFIG_MAP = "security-group";
    /**
     * 一些大数据服务初始化容器镜像名
     */
    public static final String INIT_STATIS_JAR_IMAGE_NAME = "xylink-spark:";
    public static final String INIT_DATACAL_JAR_IMAGE_NAME = "datacal:";
    public static final String INIT_ETL_SCRIPT_JAR_IMAGE_NAME = "etl-script:";
    public static final String INIT_KERNEL_IMAGE_NAME = "dmcu-ko:";

    public static final String INIT_STATIS_JAR_CONTAINER_NAME = "init-statis-jar";
    public static final String INIT_DATACAL_JAR_CONTAINER_NAME = "init-datacal-jar";
    public static final String INIT_ETL_SCRIPT_JAR_CONTAINER_NAME = "init-etlscript-jar";
    public static final String INIT_KERNEL_CONTAINER_NAME = "init-kernel";

    static {
        String private_version = System.getProperty("private_version");
        PRIVATE_VERSION = StringUtils.isEmpty(private_version) ? "private60" : private_version;
    }
}
