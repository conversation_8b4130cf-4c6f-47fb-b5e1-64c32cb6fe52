package com.xylink.manager.inspection.entity.vo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2022/11/17
 */
@Data
public class HostDetailVO {
    //主键ID
    private String id;
    //编号
    private String number;
    //主机名称
    private String name;
    //内网地址
    private String privateIp;
    //外网地址
    private String publicIp;
    //内存，G
    private String memory;
    //磁盘，G
    private String disk;
    //外网带宽，M
    private String bandwidth;
    //区域
    private String regionName;
    //区域
    private String regionId;
    //是否是核心区域
    private boolean coreRegion;

    private String regionIp;
    //状态，Running，
    private String status;
    //是否支持外网
    private Integer supportExtranet;
    //是否支持sdb
    private Integer supportSdb;
    //加入集群时间
    private Date joinClusterTime;
    //描述
    private String comment;

    //企业id
    private String enterpriseId;

    private String cpu;

    private String k8sRole;

    private String k8sStatus;

    private Long k8sHostStartTime;
    //节点绑定的服务
    private String bindingServers;
}
