package com.xylink.manager.inspection.entity.vo;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/10 15:52
 *
 * 巡检列表 VO
 */
@Data
@Builder
public class InspectionTemplateVo {
    /**
     * 临时巡检id/周期巡检id/定时巡检id
     */
    String id;
    /**
     * 巡检名称
     */
    String name;
    /**
     * 任务创建时间，时间戳
     */
    Long createTime;
    /**
     * 创建人
     */
    String createUser;
    /**
     * 巡检类型
     *  1: 周期巡检
     *  2: 定时巡检
     *  3: 临时巡检
     */
    Integer type;

    /**
     * 任务状态
     *  0: 未开始
     *  1: 待执行
     *  2: 进行中
     *  3: 已结束
     *
     *  周期性巡检： 未开始、进行中（有真正巡检的任务的时候为进行中）、待执行（巡检周期未到期）、已结束
     *  临时巡检：进行中、已结束
     *  定时巡检：未开始、进行中、已结束
     *
     *  可编辑状态为：未开始、进行中（只有周期性巡检）、待执行（周期性巡检）
     */
    Integer status;

    Integer enable;
}
