package com.xylink.manager.inspection.entity.jasperreport;

import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/23 11:41
 */
public class InspectionPdfFieldBean {
    private JRBeanCollectionDataSource hostItemTable;
    private JRBeanCollectionDataSource hostResultTable;
    private JRBeanCollectionDataSource middleItemTable;
    private JRBeanCollectionDataSource middleResultTable;
    private JRBeanCollectionDataSource serviceItemTable;
    private JRBeanCollectionDataSource serviceResultTable;

    public JRBeanCollectionDataSource getHostItemTable() {
        return hostItemTable;
    }

    public void setHostItemTable(JRBeanCollectionDataSource hostItemTable) {
        this.hostItemTable = hostItemTable;
    }

    public JRBeanCollectionDataSource getHostResultTable() {
        return hostResultTable;
    }

    public void setHostResultTable(JRBeanCollectionDataSource hostResultTable) {
        this.hostResultTable = hostResultTable;
    }

    public JRBeanCollectionDataSource getMiddleItemTable() {
        return middleItemTable;
    }

    public void setMiddleItemTable(JRBeanCollectionDataSource middleItemTable) {
        this.middleItemTable = middleItemTable;
    }

    public JRBeanCollectionDataSource getMiddleResultTable() {
        return middleResultTable;
    }

    public void setMiddleResultTable(JRBeanCollectionDataSource middleResultTable) {
        this.middleResultTable = middleResultTable;
    }

    public JRBeanCollectionDataSource getServiceItemTable() {
        return serviceItemTable;
    }

    public void setServiceItemTable(JRBeanCollectionDataSource serviceItemTable) {
        this.serviceItemTable = serviceItemTable;
    }

    public JRBeanCollectionDataSource getServiceResultTable() {
        return serviceResultTable;
    }

    public void setServiceResultTable(JRBeanCollectionDataSource serviceResultTable) {
        this.serviceResultTable = serviceResultTable;
    }
}
