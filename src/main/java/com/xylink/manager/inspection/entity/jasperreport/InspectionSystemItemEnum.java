package com.xylink.manager.inspection.entity.jasperreport;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/8 16:58
 */
public enum InspectionSystemItemEnum {
    HOST_CPU_USAGE("CPU使用率", "23cc7b23d6b44664a3f63bea5c1fb4a1", ""),
    HOST_DISK_USAGE("磁盘使用率", "80df19d244a24cbfa1a1d9202f5cc408", ""),
    HOST_MEM_USAGE("内存使用率", "38f7e480ad0b47e5bcc5554191934918", "")
    ;
    private final String showName;
    private final String itemId;
    private final String desc;

    InspectionSystemItemEnum(String showName, String itemId, String desc) {
        this.showName = showName;
        this.itemId = itemId;
        this.desc = desc;
    }

    public String getShowName() {
        return showName;
    }

    public String getItemId() {
        return itemId;
    }

    public String getDesc() {
        return desc;
    }
}
