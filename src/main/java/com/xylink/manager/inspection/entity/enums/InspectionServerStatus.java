package com.xylink.manager.inspection.entity.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/7 18:57
 */
public enum InspectionServerStatus {
    /**
     * 服务状态正常
     */
    NORMAL(0),
    /**
     * 不可用
     */
    NOT_AVAILABLE(1),
    /**
     * 存在异常
     */
    EXCEPTION(2),
    ;
    private final int value;

    InspectionServerStatus(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }
}
