package com.xylink.manager.inspection.entity.vo;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @ClassName: ServiceInstanceInfo
 * @Description: 服务实例信息
 * <AUTHOR> @Date 2022/10/25
 * @Version 1.0
 */
@Data
@Builder
public class ServerInstanceVO {

    private String podName;

    private String hostIp;

    private String nodeIp;

    private List<String> version;

    private Boolean ready;

    private String status;

    private Integer restarts;

    private String age;

    private Boolean allowRestart;

    private String namespace;

    /**
     * 容器列表
     */
    private List<String> containers;

    private String hostname;

    private String podIp;

    private Integer podPort;
}
