package com.xylink.manager.inspection.entity.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/2 16:28
 */
public enum BigDataServiceEnum {
    /**
     * zookeeper: 对应所有以 hadoop-zookeeper 开头的大数据组件
     */
    ZOOKEEPER("zookeeper", "hadoop-zookeeper", "检查大数据zookeeper是否正常"),
    HDFS("hdfs", "hdfs", "检查HDFS是否正常"),
    HBASE("hbase", "hbase", "检查HBASE是否正常"),
    // FIXME: 当前版本暂时不实现 hive 和 azkaban
//    HIVE("hive", "hive"),
//    AZKABAN("azkaban", "azkaban"),
    YARN("hdfs", "yarn", "检查HDFS是否正常"),
    HADOOP("hdfs", "hadoop", "检查HDFS是否正常");

    private final String moduleName;
    private final String servicePrefix;

    private final String message;

    BigDataServiceEnum(String moduleName, String servicePrefix, String message) {
        this.moduleName = moduleName;
        this.servicePrefix = servicePrefix;
        this.message = message;
    }

    public String getModuleName() {
        return moduleName;
    }

    public String getServicePrefix() {
        return servicePrefix;
    }

    public String getMessage() {
        return message;
    }

    public static BigDataServiceEnum valueOfModule(String moduleName){
        BigDataServiceEnum[] values = BigDataServiceEnum.values();
        for (BigDataServiceEnum value : values) {
            if (value.getModuleName().equals(moduleName)) {
                return value;
            }
        }
        return null;
    }
}
