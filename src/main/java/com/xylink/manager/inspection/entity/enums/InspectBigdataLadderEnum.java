package com.xylink.manager.inspection.entity.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/04/13/15:09
 */
public enum InspectBigdataLadderEnum {
    NORMAL("正常", 0),
    RISK("风险",1),
    EXCEPT("异常",2)
    ;
    private final String type;
    private final int value;

    InspectBigdataLadderEnum(String type, int value) {
        this.type = type;
        this.value = value;
    }

    /**
     * 获取巡检状态
     */
    public static InspectBigdataLadderEnum getInspectLadder(Integer value) {
        if (null == value) {
            return InspectBigdataLadderEnum.EXCEPT;
        }
        for (InspectBigdataLadderEnum inspectLadder : values()) {
            if (inspectLadder.value == value) {
                return inspectLadder;
            }
        }
        return InspectBigdataLadderEnum.EXCEPT;
    }

    /**
     * 获取巡检状态
     */
    public static InspectBigdataLadderEnum getInspectLadderByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return InspectBigdataLadderEnum.EXCEPT;
        }
        for (InspectBigdataLadderEnum inspectLadder : values()) {
            if (type.equals(inspectLadder.getType())) {
                return inspectLadder;
            }
        }
        return InspectBigdataLadderEnum.EXCEPT;
    }

    public String getType() {
        return type;
    }
    public int getValue() {
        return value;
    }

}
