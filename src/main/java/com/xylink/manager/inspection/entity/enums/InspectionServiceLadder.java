package com.xylink.manager.inspection.entity.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/9 18:58
 */
public enum InspectionServiceLadder {
    /**
     * 正常
     */
    NORMAL(1),
    /**
     * 风险
     */
    RISK(2),
    /**
     * 异常
     */
    EXCEPTION(0);

    private int value;

    InspectionServiceLadder(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }
}
