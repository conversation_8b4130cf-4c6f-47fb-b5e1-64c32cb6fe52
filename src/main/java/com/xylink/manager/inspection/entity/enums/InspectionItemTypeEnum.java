package com.xylink.manager.inspection.entity.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/16 17:27
 */
public enum InspectionItemTypeEnum {
    // 系统检查
    SYSTEM(1, "system", "系统检查"),
    // 中间件检查
    MIDDLEWARE(2, "middleware", "中间件检查"),
    // 服务检查
    SERVER(3, "server", "服务检查");

    private final int id;

    InspectionItemTypeEnum(int id, String type, String description) {
        this.id = id;
    }

    public int getId() {
        return id;
    }

    public static InspectionItemTypeEnum getEnum(Integer value) {
        for (InspectionItemTypeEnum x: values()) {
            if (x.getId() == value) {
                return x;
            }
        }
        return null;
    }
}
