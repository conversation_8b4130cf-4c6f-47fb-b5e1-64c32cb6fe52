package com.xylink.manager.inspection.entity.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/16 19:20
 */
public enum InspectionTaskStatusEnum {
    // 巡检正在运行
    RUNNING(0, "正在运行"),
    // 巡检执行完成
    COMPLETE(1, "执行完成"),
    EXCEPTED(2, "运行过程中出现异常");

    private final int status;

    InspectionTaskStatusEnum(int status, String description) {
        this.status = status;
    }

    public int getStatus() {
        return status;
    }
}
