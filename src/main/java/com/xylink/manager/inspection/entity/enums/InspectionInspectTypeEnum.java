package com.xylink.manager.inspection.entity.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/12 16:18
 */
public enum InspectionInspectTypeEnum {
    // 周期巡检
    scheduler(1, "周期巡检"),
    // 定时巡检
    time(2, "定时巡检"),
    // 临时巡检
    temp(3, "临时巡检");

    private final int type;

    private final String name;
    InspectionInspectTypeEnum(int id, String type) {
        this.type = id;
        this.name = type;
    }

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static InspectionInspectTypeEnum valueOf(int value) {
        for (InspectionInspectTypeEnum x : values()) {
            if (x.getType() == value) {
                return x;
            }
        }
        return null;
    }
}
