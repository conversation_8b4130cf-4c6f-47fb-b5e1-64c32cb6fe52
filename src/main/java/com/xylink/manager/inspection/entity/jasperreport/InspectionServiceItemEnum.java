package com.xylink.manager.inspection.entity.jasperreport;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/9 11:23
 */
public enum InspectionServiceItemEnum {

    SERVICE_LIVE("服务探活", "6a74db5f4f1e424e9786d96f977f55a0", "查看服务的状态是否存活，服务未部署或服务pod处于非运行状态即为不可用状态"),
    SERVICE_AVAILABILITY("服务可用性", "6a74db5f4f1e424e9786d96f977f55a0", "检查服务可用性"),
    ;
    private final String showName;
    private final String itemId;
    private final String desc;

    InspectionServiceItemEnum(String showName, String itemId, String desc) {
        this.showName = showName;
        this.itemId = itemId;
        this.desc = desc;
    }

    public String getShowName() {
        return showName;
    }

    public String getItemId() {
        return itemId;
    }

    public String getDesc() {
        return desc;
    }
}
