package com.xylink.manager.inspection.entity.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/18 11:05
 */
public enum InspectionSchedulerTypeEnum {
    /**
     * 周期巡检为 week 类型
     */
    WEEK(1, "week"),
    /**
     * 周期巡检为 month 类型
     */
    MONTH(2, "month"),
    /**
     * 执行一次的周期巡检，即为定时巡检
     */
    ONCE(3, "once");

    private final String type;

    InspectionSchedulerTypeEnum(int id, String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }
}
