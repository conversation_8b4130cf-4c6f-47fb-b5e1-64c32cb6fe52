package com.xylink.manager.inspection.entity.jasperreport;

import com.alibaba.excel.support.cglib.beans.BeanMap;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/21 17:02
 */
@Data
public class InspectionPdfParametersBean {
    private String no;
    private String name;
    private String createUser;
    private String createTime;
    private String consumedTime;
    private String type;
    private Integer hostHighRisk;
    private Integer hostMiddleRisk;
    private Integer hostLowRisk;
    private Integer middleHighRisk;
    private Integer middleMiddleRisk;
    private Integer middleLowRisk;
    private Integer serviceHighRisk;
    private Integer serviceMiddleRisk;
    private Integer serviceLowRisk;
    private Boolean showHost = false;
    private Boolean showMiddle = false;
    private Boolean showService = false;

    public Map<String, Object> beanTransferParamMap() {
        Map<String, Object> map = new HashMap<>();
        BeanMap beanMap = BeanMap.create(this);
        InspectionPdfParametersEnum[] values = InspectionPdfParametersEnum.values();
        for (InspectionPdfParametersEnum value : values) {
            map.put(value.getParamName(), beanMap.get(value.getPropertyName()));
        }
        return map;
    }
}
