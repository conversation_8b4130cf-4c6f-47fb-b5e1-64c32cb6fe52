package com.xylink.manager.inspection.entity.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.alibaba.excel.metadata.data.WriteCellData;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/10 14:28
 */
@Getter
@Setter
@ContentStyle(
        verticalAlignment = VerticalAlignmentEnum.CENTER,
        borderTop = BorderStyleEnum.THIN,
        borderBottom = BorderStyleEnum.THIN,
        borderLeft = BorderStyleEnum.THIN,
        borderRight = BorderStyleEnum.THIN,
        topBorderColor = 23,
        bottomBorderColor = 23,
        leftBorderColor = 23,
        rightBorderColor = 23,
        wrapped = BooleanEnum.TRUE)
public class MiddleResultData {
    @ExcelProperty(value = "巡检项", index = 0)
    @ColumnWidth(15)
    private String item;

    @ExcelProperty(value = "巡检指标", index = 1)
    @ColumnWidth(28)
    private String metricName;

    @ExcelProperty(value = "结果", index = 2)
    @ColumnWidth(7)
    private WriteCellData<String> result;

    @ExcelProperty(value = "指标值", index = 3)
    @ColumnWidth(40)
    private String metricValue;

    @ExcelProperty(value = "指标描述", index = 4)
    @ColumnWidth(65)
    private String metricDesc;
}
