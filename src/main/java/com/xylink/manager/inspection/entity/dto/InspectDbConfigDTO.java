package com.xylink.manager.inspection.entity.dto;

import com.xylink.manager.model.em.DBType;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/27
 */
@Data
@Builder
public class InspectDbConfigDTO {
    private String inspectName;
    private String nodeType;
    //数据库类型
    private String dbType;
    //业务类型
    private DBType type;
    private String schema;
    private String dbIPKey;
    private String dbPortKey;
    private String dbMainHost;
    private String username;
    private String password;

}
