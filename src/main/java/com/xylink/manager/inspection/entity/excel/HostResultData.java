package com.xylink.manager.inspection.entity.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.VerticalAlignmentEnum;
import com.alibaba.excel.metadata.data.WriteCellData;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/9 16:37
 */
@Getter
@Setter
@ContentStyle(
        verticalAlignment = VerticalAlignmentEnum.CENTER,
        borderTop = BorderStyleEnum.THIN,
        borderBottom = BorderStyleEnum.THIN,
        borderLeft = BorderStyleEnum.THIN,
        borderRight = BorderStyleEnum.THIN,
        topBorderColor = 23,
        bottomBorderColor = 23,
        leftBorderColor = 23,
        rightBorderColor = 23,
        wrapped = BooleanEnum.TRUE)
public class HostResultData {
    @ExcelProperty(value = "主机名", index = 0)
    @ColumnWidth(25)
    private String hostName;

    @ExcelProperty(value = "主机IP", index = 1)
    @ColumnWidth(15)
    private String hostIp;

    @ExcelProperty(value = "CPU使用率(%)", index = 2)
    @ColumnWidth(18)
    private WriteCellData<String> cpu;

    @ExcelProperty(value = "内存使用率(%)", index = 3)
    @ColumnWidth(18)
    private WriteCellData<String> mem;

    @ExcelProperty(value = "磁盘使用率(%)", index = 4)
    @ColumnWidth(18)
    private WriteCellData<String> disk;

    @ExcelProperty(value = "结果", index = 5)
    @ColumnWidth(7)
    private WriteCellData<String> result;

    @ExcelProperty(value = "备注", index = 6)
    @ColumnWidth(18)
    private String desc;
}
