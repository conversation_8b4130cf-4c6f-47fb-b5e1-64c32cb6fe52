package com.xylink.manager.inspection.entity.jasperreport;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/9 10:59
 */
public enum InspectionMiddleItemEnum {
    MYSQL("mysql", "036515dc701e4e1a91ac7ae866462c64", "mysql"),
    <PERSON><PERSON>("zookeeper", "0957eca7dc914d4a8c59d023ce6a2da4", "zookeeper"),
    REDIS("redis", "0ee7b1987f5e48ea87b6fba85ec3ba49", "redis"),
    KAFKA("kafka", "f7de2bd7c0264ba284d2295685ce6417", "kafka"),
    BIGDATA("bigdata", "e1a103afbe6b4044ae6324ecf4554cfb", "大数据"),
    ;

    private final String showName;
    private final String itemId;
    private final String desc;

    InspectionMiddleItemEnum(String showName, String itemId, String desc) {
        this.showName = showName;
        this.itemId = itemId;
        this.desc = desc;
    }

    public String getShowName() {
        return showName;
    }

    public String getItemId() {
        return itemId;
    }

    public String getDesc() {
        return desc;
    }
}
