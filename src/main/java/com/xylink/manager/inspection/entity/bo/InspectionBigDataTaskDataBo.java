package com.xylink.manager.inspection.entity.bo;

import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/8 11:29
 */
public class InspectionBigDataTaskDataBo {
    private String module;
    private Long startTime;
    private Long endTime;
    private List<InspectionTaskDataBo> result;

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public List<InspectionTaskDataBo> getResult() {
        return result;
    }

    public void setResult(List<InspectionTaskDataBo> result) {
        this.result = result;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", InspectionBigDataTaskDataBo.class.getSimpleName() + "[", "]")
                .add("module='" + module + "'")
                .add("startTime=" + startTime)
                .add("endTime=" + endTime)
                .add("result=" + result)
                .toString();
    }
}
