package com.xylink.manager.inspection.entity.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/24 15:12
 */
public enum InspectionMetricKeyEnum {
    /**
     * 主机默认指标
     */
    HOST_CPU_USAGE("CPU使用率", "MS_cpu_used"),
    HOST_DISK_USAGE("磁盘使用率", "MS_disk_used"),
    HOST_MEM_USAGE("内存使用率", "MS_mem_used"),
    HOST_RESOURCE_REQUEST_MATCHING("资源申请匹配", "MS_host_resource"),

    /**
     * 服务默认指标
     */
    SERVER_AGENT_LIVE("服务探活", "MS_service_agent_live"),
    SERVER_INSPECTION("服务可用性", "MS_service_availability"),
    SERVER_RESTART_COUNT("POD重启次数", "MS_service_pod_restart"),

    /**
     * kafka 默认指标
     */
    KAFKA_PRODUCED_AND_CONSUMED("Kafka消费生产数据", ""),

    /**
     * mysql 默认指标
     */
    DB_AGENT_LIVE("数据库探活", ""),
    MYSQL_CONNECTION_COUNT("连接数", ""),
    MYSQL_INNODB_BUFFER_POOL_HIT_RATE("InnoDB Buffer Pool命中率", ""),
    MYSQL_INNODB_BUFFER_POOL_REQUEST_COUNT("InnoDB Buffer Pool请求次数", ""),
    MYSQL_INNODB_ROW_OPERATIONS("InnoDB Row Operations", ""),
    MYSQL_TPS("Mysql TPS", ""),
    MYSQL_QPS("Mysql QPS", ""),

    /**
     * redis 默认指标
     */
    REDIS_AGENT_LIVE("redis探活", ""),
    REDIS_CONNECTION_COUNT("redis连接数", ""),
    REDIS_ACTUAL_MEMORY_USAGE("redis实际使用内存", ""),
    REDIS_AOF("redis aof", ""),
    REDIS_QPS("redis qps", ""),
    REDIS_KEYSPACE("各DB的key个数", ""),

    /**
     * zk 默认指标
     */
    ZK_AGENT_LIVE("zookeeper探活", ""),
    ZK_LATENCY("zookeeper请求延时", ""),
    ZK_RECEIVED("zookeeper收包数", ""),
    ZK_SEND("zookeeper发包数", ""),
    ZK_CONNECTED_CLIENTS("zookeeper连接数", ""),
    ZK_NODE("zookeeper节点数", ""),

    /**
     * 大数据默认指标
     */
    BIG_DATA_INSPECTION("组件指标获取", ""),
    ;

    private final String metricKey;
    private final String metricCode;

    InspectionMetricKeyEnum(String metricKey, String metricCode) {
        this.metricKey = metricKey;
        this.metricCode = metricCode;
    }

    public String getMetricKey() {
        return metricKey;
    }

    public String getMetricCode() {
        return metricCode;
    }
}
