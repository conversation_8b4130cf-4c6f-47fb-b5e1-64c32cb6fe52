package com.xylink.manager.inspection.entity.vo;


import com.xylink.manager.model.KuberDiskInfo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description host+external的书记
 * @date 2022/10/17
 */
@Data
public class HostPageVO {

    //主键ID
    private String id;
    //编号
    private String number;
    //主机名称
    private String name;
    //内网地址
    private String privateIp;
    //外网地址
    private String publicIp;
    //内存，G
    private String memory;
    //磁盘，G
    private String disk;
    //外网带宽，M
    private String bandwidth;
    //区域
    private String regionName;
    //状态，Running，
    private String status;
    //是否支持外网
    private Integer supportExtranet;
    //是否支持sdb
    private Integer supportSdb;
    //加入集群时间
    private Date joinClusterTime;
    //描述
    private String comment;

    //企业id
    private String enterpriseId;

    private String cpu;

    /**
     * 自定义标签
     */
    private String customTags;

    /**
     * 系统标签
     */
    private String systemTags;

    private String bindingServers;

    private List<String> systemTagValueIds;

    private List<String> customTagValueIds;

    /**
     * 安全组id
     */
    private String groupId;
    /**
     * 安全组名称
     */
    private String groupName;

    private List<KuberDiskInfo> kuberDiskInfos;

    private Float memoryUsage;

    private Float cpuUsage;

}
