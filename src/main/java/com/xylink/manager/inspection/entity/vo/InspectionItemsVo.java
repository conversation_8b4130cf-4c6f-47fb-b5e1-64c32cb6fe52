package com.xylink.manager.inspection.entity.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/4 15:28
 *
 * 巡检项列表 VO
 */
@Data
public class InspectionItemsVo {
    /**
     * 系统巡检项
     */
    private List<InspectionItem> system;
    /**
     * 中间件巡检项
     */
    private List<InspectionItem> middleware;
    /**
     * 服务巡检项
     *  服务巡检目前只有一个 "服务巡检"
     *  返回一个固定 id 的巡检项，名称 "服务巡检"。
     */
    private List<InspectionItem> server;

    @Data
    public static class InspectionItem {
        /**
         * 巡检项 id
         */
        private String id;
        private String name;
        /**
         * 巡检项类型
         *  1. 系统巡检
         *  2. 中间件巡检
         *  3. 服务巡检
         */
        private Integer type;
    }
}
