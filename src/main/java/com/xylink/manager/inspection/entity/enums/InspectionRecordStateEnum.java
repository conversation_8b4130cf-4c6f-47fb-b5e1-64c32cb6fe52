package com.xylink.manager.inspection.entity.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/11 15:41
 */
public enum InspectionRecordStateEnum {
    FAIL("导出失败", -1),
    EXPORTING("导出中", 0),
    EXPORTED("导出完成", 1),
    DELETED("已删除", 2),
    ;

    private final String desc;
    private final int state;

    InspectionRecordStateEnum(String desc, int state) {
        this.desc = desc;
        this.state = state;
    }

    public String getDesc() {
        return desc;
    }

    public int getState() {
        return state;
    }
}
