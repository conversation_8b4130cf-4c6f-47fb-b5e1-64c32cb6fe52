package com.xylink.manager.inspection.entity.model;

public class RestMessage {
    private static final char SEPERATOR = ';';
    private String developerMessage;
    private String userMessage;
    private int errorCode;
    private String moreInfo;

    /**
     * @return the developerMessage
     */
    public String getDeveloperMessage() {
        return developerMessage;
    }

    /**
     * @param developerMessage the developerMessage to set
     */
    public void setDeveloperMessage(String developerMessage) {
        this.developerMessage = developerMessage;
    }

    /**
     * @return the userMessage
     */
    public String getUserMessage() {
        return userMessage;
    }

    /**
     * @param userMessage the userMessage to set
     */
    public void setUserMessage(String userMessage) {
        this.userMessage = userMessage;
    }

    /**
     * @return the errorCode
     */
    public int getErrorCode() {
        return errorCode;
    }

    /**
     * @param errorCode the errorCode to set
     */
    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    /**
     * @return the moreInfo
     */
    public String getMoreInfo() {
        return moreInfo;
    }

    /**
     * @param moreInfo the moreInfo to set
     */
    public void setMoreInfo(String moreInfo) {
        this.moreInfo = moreInfo;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append('{');
        sb.append("errorCode: ").append(errorCode).append(SEPERATOR);
        sb.append("userMessage: ").append(userMessage).append(SEPERATOR);
        sb.append("developerMessage: ").append(developerMessage).append(SEPERATOR);
        sb.append("moreInfo: ").append(moreInfo);
        sb.append('}');
        return sb.toString();
    }
}