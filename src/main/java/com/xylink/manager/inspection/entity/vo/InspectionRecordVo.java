package com.xylink.manager.inspection.entity.vo;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/3 11:05
 * <p>
 * 巡检结果列表 VO
 */
@Data
@Builder
public class InspectionRecordVo {
    /**
     * 巡检名称
     */
    private String name;
    /**
     * 任务 Id
     */
    private String jobId;

    /**
     * 任务创建时间，时间戳
     */
    private String createTime;

    /**
     * 任务结束时间，时间戳
     */
    private String finishedTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 任务状态，0: 正在执行；1: 执行完成；2：巡检异常
     */
    private Integer jobStatus;

    /**
     * 任务耗时
     */
    private String consumedTime;

    /**
     * 高风险
     */
    private Integer highRisk;

    /**
     * 中风险
     */
    private Integer middleRisk;

    /**
     * 低风险
     */
    private Integer lowRisk;

    /**
     * 巡检类型
     * 1: 周期巡检
     * 2: 定时巡检
     * 3: 临时巡检
     */
    private Integer type;

    /**
     * 任务未完成状态时，表示任务运行到第几步 1: 系统巡检已完成；2: 中间件已完成；3: 服务检查已完成
     */
    private Integer step;
}
