package com.xylink.manager.inspection.entity.db;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/13 18:37
 */
@Data
public class InspectionSchedulerJobDb {
    /**
     * 定时巡检 id
     */
    private String schedulerJobId;
    /**
     * 巡检人
     */
    private String createUser;
    /**
     * 巡检创建时间
     */
    private Long createTime;
    /**
     * 巡检项列表
     */
    private String inspectionItems;
    /**
     * 周期开始时间
     */
    private Long starTime;
    /**
     * 周期结束时间
     */
    private Long endTime;
    /**
     * 周期巡检类型 week: 间隔周；month: 间隔月
     */
    private String schedulerType;
    /**
     * 巡检任务状态
     * @see com.xylink.inspection.entity.enums.InspectionJobStatusEnum
     */
    private Integer jobStatus;
    /**
     * 最近一次巡检任务 id
     */
    private String latestJobId;
    /**
     * 最近一次更新时间
     */
    private Long updateTime;
    /**
     * 是否启用
     */
    private Integer enable;
}
