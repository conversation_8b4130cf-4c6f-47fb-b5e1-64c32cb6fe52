package com.xylink.manager.inspection.entity.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/13 14:31
 */
public enum InspectionThresholdLadderEnum {
    // 正常
    NORMAL(0, "正常"),
    LOW_RISK(1, "低风险"),
    MIDDLE_RISK(2, "中风险"),
    HIGH_RISK(3, "高风险"),
    EXCEPTION(4, "异常");
    private final Integer ladder;
    private final String description;

    InspectionThresholdLadderEnum(int ladder, String description) {
        this.ladder = ladder;
        this.description = description;
    }

    public Integer getLadder() {
        return ladder;
    }

    public String getDescription() {
        return description;
    }
}
