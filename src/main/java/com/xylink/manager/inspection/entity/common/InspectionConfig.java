package com.xylink.manager.inspection.entity.common;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/12 16:38
 */
@Component
public class InspectionConfig {
    @Value("${inspection.enable:true}")
    private Boolean enable;

    @Value("${inspection.schedule.item.limit:10}")
    private Integer scheduleItemLimit;

    public boolean isEnable() {
        return enable;
    }

    public Integer getScheduleItemLimit() {
        return scheduleItemLimit;
    }
}
