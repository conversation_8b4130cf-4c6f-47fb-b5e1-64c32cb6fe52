package com.xylink.manager.inspection.utils.inspect;

import com.xylink.manager.inspection.dao.InspectionMetricTaskDao;
import com.xylink.manager.inspection.dao.InspectionSubTaskDao;
import com.xylink.manager.inspection.entity.db.InspectionMetricTaskDb;
import com.xylink.manager.inspection.entity.enums.InspectionMetricKeyEnum;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.db.JasyptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.xylink.manager.inspection.entity.enums.InspectionThresholdLadderEnum.*;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/9 16:35
 */
@Service(value = "MYSQL_Inspect")
@Slf4j
public class MysqlInspect extends DbInspect {

    private InspectionMetricTaskDao metricTaskDao;

    public MysqlInspect(InspectionSubTaskDao subTaskDao,
                        InspectionMetricTaskDao metricTaskDao, JasyptService jasyptService,
                        K8sService k8sService) {
        super(subTaskDao, metricTaskDao, jasyptService, k8sService);
        this.metricTaskDao = metricTaskDao;
    }

    @Override
    public void inspectionMetric(String subTaskId, Connection connection, List<InspectionMetricTaskDb> metricTasks) {
        // 连接数
        metricTasks.add(connectionCount(subTaskId, connection));
        // InnoDB Buffer Pool 命中率
        metricTasks.add(bufferPoolHitRate(subTaskId, connection));
        // InnoDB Buffer Pool 请求次数
        metricTasks.add(bufferPoolRequestCount(subTaskId, connection));
        // InnoDB Row Operations
        metricTasks.add(rowOperations(subTaskId, connection));
        // TPS
        metricTasks.add(tps(subTaskId, connection));
        // QPS
        metricTasks.add(qps(subTaskId, connection));
    }

    @Override
    public void saveFailedMetricTasks(String subTaskId, List<InspectionMetricTaskDb> metricTasks) {
        metricTasks.add(metricTaskDao.buildFailedMetricTaskDb(subTaskId, InspectionMetricKeyEnum.MYSQL_CONNECTION_COUNT.getMetricKey(), "指标获取失败"));
        metricTasks.add(metricTaskDao.buildFailedMetricTaskDb(subTaskId, InspectionMetricKeyEnum.MYSQL_INNODB_BUFFER_POOL_HIT_RATE.getMetricKey(), "指标获取失败"));
        metricTasks.add(metricTaskDao.buildFailedMetricTaskDb(subTaskId, InspectionMetricKeyEnum.MYSQL_INNODB_BUFFER_POOL_REQUEST_COUNT.getMetricKey(), "指标获取失败"));
        metricTasks.add(metricTaskDao.buildFailedMetricTaskDb(subTaskId, InspectionMetricKeyEnum.MYSQL_INNODB_ROW_OPERATIONS.getMetricKey(), "指标获取失败"));
        metricTasks.add(metricTaskDao.buildFailedMetricTaskDb(subTaskId, InspectionMetricKeyEnum.MYSQL_TPS.getMetricKey(), "指标获取失败"));
        metricTasks.add(metricTaskDao.buildFailedMetricTaskDb(subTaskId, InspectionMetricKeyEnum.MYSQL_QPS.getMetricKey(), "指标获取失败"));
    }

    public InspectionMetricTaskDb connectionCount(String subTaskId, Connection connection) {
        long startTime = System.currentTimeMillis();
        String metricKey = InspectionMetricKeyEnum.MYSQL_CONNECTION_COUNT.getMetricKey();
        String maxConnectionsSql = "show variables like 'max_connections';";
        String sessionTotalSql = "select count(*) as '会话总数' from information_schema.PROCESSLIST;";
        String runningSessionTotalSql = "select count(*) as '运行中会话总数' from information_schema.PROCESSLIST where COMMAND not in ('Sleep','Binlog Dump');";
        String maxRunningSessionTimeSql = "select max(TIME) as '运行中会话最长时间' from information_schema.PROCESSLIST where COMMAND not in ('Sleep','Binlog Dump');";
        try (PreparedStatement maxConnectionsPs = connection.prepareStatement(maxConnectionsSql);
             PreparedStatement sessionTotalPs = connection.prepareStatement(sessionTotalSql);
             PreparedStatement runningSessionTotalPs = connection.prepareStatement(runningSessionTotalSql);
             PreparedStatement maxRunningSessionTimePs = connection.prepareStatement(maxRunningSessionTimeSql);
             ResultSet maxConnectionsRs = maxConnectionsPs.executeQuery();
             ResultSet sessionTotalRs = sessionTotalPs.executeQuery();
             ResultSet runningSessionTotalRs = runningSessionTotalPs.executeQuery();
             ResultSet maxRunningSessionTimeRs = maxRunningSessionTimePs.executeQuery()
        ) {
            if (!maxConnectionsRs.next()
                    || !sessionTotalRs.next()
                    || !runningSessionTotalRs.next()
                    || !maxRunningSessionTimeRs.next()) {
                log.error("[inspection] Mysql inspection connection count failed, subTaskId={}", subTaskId);
                // 指标获取失败
                return metricTaskDao.buildFailedMetricTaskDb(subTaskId, startTime, "指标获取失败", "指标获取失败", metricKey);
            }
            long maxConnections = Long.parseLong(maxConnectionsRs.getString("Value"));
            long sessionTotal = sessionTotalRs.getLong(1);
            long runningSessionTotal = runningSessionTotalRs.getLong(1);
            long maxRunningSessionTime = maxRunningSessionTimeRs.getLong(1);

            String result = "最大连接数=" + maxConnections +
                    "\n会话总数=" + sessionTotal +
                    "\n运行中会话总数=" + runningSessionTotal +
                    "\n运行中会话最长时间" + maxRunningSessionTime;

            double value = sessionTotal * 1.0 / maxConnections;

            if (value >= 0.5) {
                // 高风险
                return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, result, metricKey, HIGH_RISK);
            }
            // 正常
            return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, result, metricKey, NORMAL);
        } catch (Exception e) {
            log.error("[inspection] mysql connection count info get error", e);
            return metricTaskDao.buildFailedMetricTaskDb(subTaskId, startTime, "指标获取失败", e.getMessage(), metricKey);
        }
    }

    public InspectionMetricTaskDb bufferPoolHitRate(String subTaskId, Connection connection) {
        long startTime = System.currentTimeMillis();
        String metricKey = InspectionMetricKeyEnum.MYSQL_INNODB_BUFFER_POOL_HIT_RATE.getMetricKey();
        String innodbBufferPoolStatusSQL = "select * from performance_schema.global_status where VARIABLE_NAME in ('Innodb_buffer_pool_pages_dirty', 'Innodb_buffer_pool_pages_data', 'Innodb_buffer_pool_read_requests', 'Innodb_buffer_pool_reads', 'Innodb_buffer_pool_pages_free');";

        try (PreparedStatement innodbBufferPoolStatusPs = connection.prepareStatement(innodbBufferPoolStatusSQL);
             ResultSet innodbBufferPoolStatusRs = innodbBufferPoolStatusPs.executeQuery()
        ) {
            Map<String, Long> map = new HashMap<>();
            while (innodbBufferPoolStatusRs.next()) {
                map.put(innodbBufferPoolStatusRs.getString(1), innodbBufferPoolStatusRs.getLong(2));
            }

            Long innodbBufferPoolPagesDirty = map.get("Innodb_buffer_pool_pages_dirty");
            Long innodbBufferPoolPagesData = map.get("Innodb_buffer_pool_pages_data");
            Long innodbBufferPoolReadRequests = map.get("Innodb_buffer_pool_read_requests");
            Long innodbBufferPoolReads = map.get("Innodb_buffer_pool_reads");
            Long innodbBufferPoolPagesFree = map.get("Innodb_buffer_pool_pages_free");

            if (innodbBufferPoolPagesDirty == null
                    || innodbBufferPoolPagesData == null
                    || innodbBufferPoolReadRequests == null
                    || innodbBufferPoolReads == null
                    || innodbBufferPoolPagesFree == null) {

                // 指标获取失败
                return metricTaskDao.buildFailedMetricTaskDb(subTaskId, startTime, "指标获取失败", "指标获取失败", metricKey);
            }

            double readCacheHitRatio = ((innodbBufferPoolReadRequests - innodbBufferPoolReads) * 1.0 / innodbBufferPoolReadRequests) * 100;
            double cacheUsage = (innodbBufferPoolPagesData * 1.0 / (innodbBufferPoolPagesData + innodbBufferPoolPagesFree)) * 100;
            String result = "Buffer Pool读缓存命中率=" + String.format("%.2f", readCacheHitRatio) + "%\nBuffer Pool使用率=" + String.format("%.2f", cacheUsage) + "%";
            if (readCacheHitRatio < 98) {
                // 低风险
                return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, result, metricKey, LOW_RISK);
            }
            // 正常
            return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, result, metricKey, NORMAL);
        } catch (Exception e) {
            log.error("[inspection] mysql buffer pool hit rate info get error", e);
            return metricTaskDao.buildFailedMetricTaskDb(subTaskId, startTime, "指标获取失败", e.getMessage(), metricKey);
        }
    }

    public InspectionMetricTaskDb bufferPoolRequestCount(String subTaskId, Connection connection) {
        long startTime = System.currentTimeMillis();
        String metricKey = InspectionMetricKeyEnum.MYSQL_INNODB_BUFFER_POOL_REQUEST_COUNT.getMetricKey();
        String innodbBufferPoolStatusSQL = "select * from performance_schema.global_status where VARIABLE_NAME in ('Innodb_buffer_pool_read_requests','Innodb_buffer_pool_write_requests');";
        try (PreparedStatement innodbBufferPoolStatusPs = connection.prepareStatement(innodbBufferPoolStatusSQL);
             ResultSet innodbBufferPoolStatusRs = innodbBufferPoolStatusPs.executeQuery()
        ) {
            Map<String, Long> map = new HashMap<>();
            while (innodbBufferPoolStatusRs.next()) {
                map.put(innodbBufferPoolStatusRs.getString(1), innodbBufferPoolStatusRs.getLong(2));
            }

            Long innodbBufferPoolReadRequests = map.get("Innodb_buffer_pool_read_requests");
            Long innodbBufferPoolWriteRequests = map.get("Innodb_buffer_pool_write_requests");

            if (innodbBufferPoolReadRequests == null || innodbBufferPoolWriteRequests == null) {
                // 指标获取失败
                return metricTaskDao.buildFailedMetricTaskDb(subTaskId, startTime, "指标获取失败", "指标获取失败", metricKey);
            }

            String result = "InnoDB平均每秒从Buffer Pool读页次数=" + innodbBufferPoolReadRequests + "\nInnoDB平均每秒从Buffer Pool写页次数=" + innodbBufferPoolWriteRequests;
            return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, result, metricKey, NORMAL);
        } catch (Exception e) {
            log.error("[inspection] mysql buffer pool request count info get error", e);
            return metricTaskDao.buildFailedMetricTaskDb(subTaskId, startTime, "指标获取失败", e.getMessage(), metricKey);
        }
    }

    public InspectionMetricTaskDb rowOperations(String subTaskId, Connection connection) {
        long startTime = System.currentTimeMillis();
        String metricKey = InspectionMetricKeyEnum.MYSQL_INNODB_ROW_OPERATIONS.getMetricKey();
        String rowOperationsSql = "select * from performance_schema.global_status where VARIABLE_NAME in ('Innodb_rows_deleted','Innodb_rows_read','Innodb_rows_inserted','Innodb_log_writes','Innodb_rows_updated');";

        try (PreparedStatement executionCountSqlPs = connection.prepareStatement(rowOperationsSql);
             ResultSet executionCountSqlRs = executionCountSqlPs.executeQuery()
        ) {
            Map<String, Long> map = new HashMap<>();
            while (executionCountSqlRs.next()) {
                map.put(executionCountSqlRs.getString(1), executionCountSqlRs.getLong(2));
            }

            Long innodbRowsDeleted = map.get("Innodb_rows_deleted");
            Long innodbRowsRead = map.get("Innodb_rows_read");
            Long innodbRowsInserted = map.get("Innodb_rows_inserted");
            Long innodbLogWrites = map.get("Innodb_log_writes");
            Long innodbRowsUpdated = map.get("Innodb_rows_updated");

            if (innodbRowsDeleted == null
                    || innodbRowsRead == null
                    || innodbRowsInserted == null
                    || innodbLogWrites == null
                    || innodbRowsUpdated == null) {
                // 指标获取失败
                return metricTaskDao.buildFailedMetricTaskDb(subTaskId, startTime, "指标获取失败", "指标获取失败", metricKey);
            }

            String result = "innodb_rows_deleted=" + innodbRowsDeleted
                    + "\ninnodb_rows_read=" + innodbRowsRead
                    + "\ninnodb_rows_inserted=" + innodbRowsInserted
                    + "\ninnodb_log_writes=" + innodbLogWrites
                    + "\ninnodb_rows_updated=" + innodbRowsUpdated;

            return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, result, metricKey, NORMAL);
        } catch (Exception e) {
            log.error("[inspection] mysql row operations info get error", e);
            return metricTaskDao.buildFailedMetricTaskDb(subTaskId, startTime, "指标获取失败", e.getMessage(), metricKey);
        }
    }

    public InspectionMetricTaskDb tps(String subTaskId, Connection connection) {
        long startTime = System.currentTimeMillis();
        String metricKey = InspectionMetricKeyEnum.MYSQL_TPS.getMetricKey();
        String rowOperationsSql = "show global status where VARIABLE_NAME in ('Com_commit','Com_rollback');";

        try (PreparedStatement executionCountSqlPs = connection.prepareStatement(rowOperationsSql);
             ResultSet executionCountSqlRs = executionCountSqlPs.executeQuery()
        ) {
            Map<String, Long> map = new HashMap<>();
            while (executionCountSqlRs.next()) {
                map.put(executionCountSqlRs.getString(1), executionCountSqlRs.getLong(2));
            }

            Long commit = map.get("Com_commit");
            Long rollback = map.get("Com_rollback");

            if (commit == null || rollback == null) {
                // 指标获取失败
                return metricTaskDao.buildFailedMetricTaskDb(subTaskId, startTime, "指标获取失败", "指标获取失败", metricKey);
            }
            long tps = commit + rollback;
            String result = "TPS=" + tps;

            return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, result, metricKey, NORMAL);
        } catch (Exception e) {
            log.error("[inspection] mysql tps info get error", e);
            return metricTaskDao.buildFailedMetricTaskDb(subTaskId, startTime, "指标获取失败", e.getMessage(), metricKey);
        }
    }

    public InspectionMetricTaskDb qps(String subTaskId, Connection connection) {
        long startTime = System.currentTimeMillis();
        String metricKey = InspectionMetricKeyEnum.MYSQL_QPS.getMetricKey();
        String rowOperationsSql = "select * from performance_schema.global_status where VARIABLE_NAME in ('Questions');";

        try (PreparedStatement executionCountSqlPs = connection.prepareStatement(rowOperationsSql);
             ResultSet executionCountSqlRs = executionCountSqlPs.executeQuery()
        ) {
            Map<String, Long> map = new HashMap<>();
            while (executionCountSqlRs.next()) {
                map.put(executionCountSqlRs.getString(1), executionCountSqlRs.getLong(2));
            }

            Long questions = map.get("Questions");
            if (questions == null) {
                // 指标获取失败
                return metricTaskDao.buildFailedMetricTaskDb(subTaskId, startTime, "指标获取失败", "指标获取失败", metricKey);
            }

            String result = "QPS=" + questions;

            return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, result, metricKey, NORMAL);
        } catch (Exception e) {
            log.error("[inspection] mysql qps info get error", e);
            return metricTaskDao.buildFailedMetricTaskDb(subTaskId, startTime, "指标获取失败", e.getMessage(), metricKey);
        }
    }
}
