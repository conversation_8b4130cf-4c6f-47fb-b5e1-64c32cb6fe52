package com.xylink.manager.inspection.utils;

import com.xylink.manager.inspection.common.OpsManagerException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.support.CronExpression;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Objects;

/**
 * Cron 工具类
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/6 14:27
 */
@Slf4j
public class CronUtil {
    /**
     * 创建类型以每周周几开始的 cron
     * @param week 周几 例如 5 (周五)
     * @param startTime 开始时间 HH:mm 例如: 17:00
     * @return cron 例如: 0 00 17 ? * 5
     */
    public static String buildWeekCron(int week, String startTime) {
        String[] split = startTime.split(":");
        String cron = "0" + ' ' + split[1] + ' ' + split[0] + ' ' + "? * " + week;
        if (!CronExpression.isValidExpression(cron)) {
            log.error("Expression is not valid! week={}, startTime={}", week, startTime);
            throw new OpsManagerException("Expression is not valid!");
        }
        return cron;
    }

    /**
     * 创建类型为每月某号开始的 cron
     * @param day 某号 例如 24 (24号)
     * @param startTime 开始时间 HH:mm 例如 17:00
     * @return cron 例如 0 00 17 24 * ?
     */
    public static String buildMonthCron(int day, String startTime) {
        String[] split = startTime.split(":");
        String cron = "0" + ' ' + split[1] + ' ' + split[0] + ' ' + day + " * ?";
        if (!CronExpression.isValidExpression(cron)) {
            log.error("Expression is not valid! day={}, startTime={}", day, startTime);
            throw new OpsManagerException("Expression is not valid!");
        }
        return cron;
    }

    /**
     * 根据 cron 获取下一次运行的时间戳
     * @param cron .
     * @return 下一次运行的时间戳
     */
    public static long nextStartTime(String cron) {
        if (!CronExpression.isValidExpression(cron)) {
            log.error("Expression is not valid! cron={}", cron);
            throw new OpsManagerException("Expression is not valid!");
        }
        CronExpression cronExpression = CronExpression.parse(cron);
        LocalDateTime nextTime = cronExpression.next(LocalDateTime.now());
        // LocalDateTime 转换为时间戳
        return Objects.requireNonNull(nextTime).toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
    }
}
