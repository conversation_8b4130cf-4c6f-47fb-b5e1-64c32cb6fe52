package com.xylink.manager.inspection.utils.jasperreport;

import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.manager.inspection.common.OpsManagerException;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ClassPathResource;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/3 11:06
 */
public class JasperPdfUtil {
    public static void exportPdf(String jasperParerFileClassPath, String exportPdfPath, Map<String, Object> params, List<?> entityList) throws IOException, JRException {
        if (StringUtils.isBlank(jasperParerFileClassPath) || StringUtils.isBlank(exportPdfPath)) {
            throw new OpsManagerException(ErrorStatus.FILE_PATH_ILLEGAL.getUserMessage());
        }

        JRDataSource dataSource = new JRBeanCollectionDataSource(entityList);

        ClassPathResource resource = new ClassPathResource(jasperParerFileClassPath);
        try (InputStream inputStream = resource.getInputStream()) {
            JasperPrint jasperPrint = JasperFillManager.fillReport(inputStream, params, dataSource);
            FileOutputStream outputStream = new FileOutputStream(exportPdfPath);
            JasperExportManager.exportReportToPdfStream(jasperPrint, outputStream);
        }
    }
}
