package com.xylink.manager.inspection.utils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.client.ClientHttpResponse;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2021/11/25 13:15
 */
public class StringFormatUtils extends StringUtils {

    /**
     * 将pattern中%s替换为具体值
     *
     * @param pattern
     * @param params
     * @return
     */
    public static String format(String pattern, Object... params) {
        return String.format(pattern, params);
    }

    public static String object2String(Object param) {
        if (param == null) {
            return "";
        }
        return String.valueOf(param);
    }

    public static String transferResponseAsString(ClientHttpResponse response) throws IOException {
        StringBuilder inputStringBuilder = new StringBuilder();
        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(response.getBody(), StandardCharsets.UTF_8));
        String line = bufferedReader.readLine();
        while (line != null) {
            inputStringBuilder.append(line);
            inputStringBuilder.append('\n');
            line = bufferedReader.readLine();
        }
        return inputStringBuilder.toString();
    }

    public static String subStr(String sString, int nLeng) {
        if (sString.length() <= nLeng) {
            return sString;
        }
        String sNewStr = sString.substring(0, nLeng);
        sNewStr = sNewStr + "...";
        return sNewStr;
    }

    public static String jvmStr(String param, int m) {
        if (param == null) {
            return "";
        }
        String regex = "-Xm[xs]\\d*?[mgMG]\\s*-Xm[xs]\\d*?[mgMG]";

        String replace = "-Xms" + m + "M -Xmx" + m + "M";

        Pattern p = Pattern.compile(regex);
        // get a matcher object
        Matcher matcher = p.matcher(param);
        param = matcher.replaceFirst(replace);


        return param;
    }
 /*   public static void main(String[] args) {
         String test = "ZOO_LOG_DIR=/var/log/zookeeper\n" +
                 "JVMFLAGS=\"-Xmx512M -Xms512M\"\n";
        test = jvmStr(test,1024);
        System.out.println(test);
    }*/

}
