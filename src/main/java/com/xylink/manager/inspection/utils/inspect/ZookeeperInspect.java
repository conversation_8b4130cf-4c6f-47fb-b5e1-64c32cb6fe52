package com.xylink.manager.inspection.utils.inspect;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.inspection.dao.InspectionMetricTaskDao;
import com.xylink.manager.inspection.dao.InspectionSubTaskDao;
import com.xylink.manager.inspection.entity.bo.ZookeeperConfigBo;
import com.xylink.manager.inspection.entity.db.InspectionItemConfigDb;
import com.xylink.manager.inspection.entity.db.InspectionMetricTaskDb;
import com.xylink.manager.inspection.entity.db.InspectionSubTaskDb;
import com.xylink.manager.inspection.entity.enums.InspectionItemTypeEnum;
import com.xylink.manager.inspection.entity.enums.InspectionMetricKeyEnum;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.manager.service.base.K8sService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.xylink.manager.inspection.entity.enums.InspectionThresholdLadderEnum.HIGH_RISK;
import static com.xylink.manager.inspection.entity.enums.InspectionThresholdLadderEnum.NORMAL;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/16 14:51
 */
@Component
@Slf4j
public class ZookeeperInspect {
    private static final int DEFAULT_PORT = 2181;
    private static final String DEFAULT_TASK_ITEM = "zookeeper";

    @Autowired
    private K8sService k8sService;
    @Autowired
    private InspectionSubTaskDao subTaskDao;
    @Autowired
    private InspectionMetricTaskDao metricTaskDao;
    @Autowired
    private IDeployService deployService;

    public List<InspectionSubTaskDb> inspect(String taskId, InspectionItemConfigDb itemConfig) {
        List<InspectionSubTaskDb> subTasks = new ArrayList<>();
        try {
            List<ZookeeperConfigBo> configs = getZookeeperConfig();
            log.info("[inspection] zookeeper configs size: {}", configs.size());
            for (ZookeeperConfigBo config : configs) {
                log.info("[inspection] zookeeper name {}, zookeeper host {}", config.getName(), config.getHost());
                InspectionSubTaskDb subTask = subTaskDao.createSubTask(taskId, itemConfig.getId(), InspectionItemTypeEnum.MIDDLEWARE.getId(), config.getName());
                List<InspectionMetricTaskDb> metricTasks = inspectZookeeper(subTask.getId(), config);
                subTaskDao.updateSubTask(subTask, metricTasks);
                subTasks.add(subTask);
            }
        } catch (Exception e) {
            log.error("[inspection] zookeeper inspect error", e);
        }
        return subTasks;
    }

    private List<InspectionMetricTaskDb> inspectZookeeper(String subTaskId, ZookeeperConfigBo config) {
        List<InspectionMetricTaskDb> metricTasks = new ArrayList<>();
        try {
            if (config == null || config.getPod() == null) {
                saveAgentLiveFileTasks(subTaskId, metricTasks);
                saveFailedMetricTasks(subTaskId, metricTasks);
            } else {
                metricTasks.add(agentLive(subTaskId, config));
                Map<String, String> infoMap = getZookeeperInfoMap(config);
                if (infoMap != null && !infoMap.isEmpty()) {
                    inspectionMetric(subTaskId, infoMap, metricTasks);
                } else {
                    saveAgentLiveFileTasks(subTaskId, metricTasks);
                    saveFailedMetricTasks(subTaskId, metricTasks);
                }
            }
        } catch (Exception e) {
            saveAgentLiveFileTasks(subTaskId, metricTasks);
            saveFailedMetricTasks(subTaskId, metricTasks);
            log.error("[inspection] zookeeper inspect error", e);
        }
        metricTaskDao.saveMetricTasks(metricTasks);
        return metricTasks;
    }

    private void inspectionMetric(String subTaskId, Map<String, String> infoMap, List<InspectionMetricTaskDb> metricTasks) {
        metricTasks.add(latency(subTaskId, infoMap));
        metricTasks.add(received(subTaskId, infoMap));
        metricTasks.add(sent(subTaskId, infoMap));
        metricTasks.add(connectedClients(subTaskId, infoMap));
        metricTasks.add(znodeCount(subTaskId, infoMap));
    }

    private void saveAgentLiveFileTasks(String subTaskId, List<InspectionMetricTaskDb> metricTasks) {
        metricTasks.add(metricTaskDao.buildFailedMetricTaskDb(subTaskId, InspectionMetricKeyEnum.ZK_AGENT_LIVE.getMetricKey(), "指标获取失败", 0));
    }

    private void saveFailedMetricTasks(String subTaskId, List<InspectionMetricTaskDb> metricTasks) {
        metricTasks.add(metricTaskDao.buildFailedMetricTaskDb(subTaskId, InspectionMetricKeyEnum.ZK_LATENCY.getMetricKey(), "指标获取失败"));
        metricTasks.add(metricTaskDao.buildFailedMetricTaskDb(subTaskId, InspectionMetricKeyEnum.ZK_RECEIVED.getMetricKey(), "指标获取失败"));
        metricTasks.add(metricTaskDao.buildFailedMetricTaskDb(subTaskId, InspectionMetricKeyEnum.ZK_SEND.getMetricKey(), "指标获取失败"));
        metricTasks.add(metricTaskDao.buildFailedMetricTaskDb(subTaskId, InspectionMetricKeyEnum.ZK_CONNECTED_CLIENTS.getMetricKey(), "指标获取失败"));
        metricTasks.add(metricTaskDao.buildFailedMetricTaskDb(subTaskId, InspectionMetricKeyEnum.ZK_NODE.getMetricKey(), "指标获取失败"));
    }

    /**
     * 获取zookeeper配置信息
     */
    private List<ZookeeperConfigBo> getZookeeperConfig() {
        List<ZookeeperConfigBo> list = new ArrayList<>();
        Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        String address = allIp.get(NetworkConstants.ZOOKEEPER_IP);
        Optional<Pod> zookeeperPod = k8sService.getPodWithLabelInApp("private-zookeeper-cluster");
        if (zookeeperPod.isPresent()) {
            address = allIp.get(NetworkConstants.MASTER_ZOOKEEPER_IP);
        } else {
            zookeeperPod = k8sService.getPodWithLabelInApp("private-zookeeper");
        }
        ZookeeperConfigBo config = ZookeeperConfigBo.builder()
                .host(address)
                .port(DEFAULT_PORT)
                .name(DEFAULT_TASK_ITEM)
                .pod(zookeeperPod.orElse(null))
                .build();
        list.add(config);
        return list;
    }

    public InspectionMetricTaskDb agentLive(String subTaskId, ZookeeperConfigBo config) {
        long startTime = System.currentTimeMillis();
        String metricKey = InspectionMetricKeyEnum.ZK_AGENT_LIVE.getMetricKey();

        String exec = "echo ruok | nc localhost 2181";
        String s = deployService.executeCommandForPod(config.getPod().getPodName(), config.getPod().getNamespace(), new String[]{
                "/bin/bash",
                "-c",
                exec
        });
        log.info("zookeeper ruok exec '{}' result: {}", exec, s);
        if (s.contains("imok")) {
            return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, "zookeeper 正常运行", metricKey, NORMAL);
        } else {
            return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, "zookeeper 未运行", metricKey, HIGH_RISK);
        }
    }

    /**
     * mntr 命令返回结果:
     * zk_server_state standalone
     * zk_ephemerals_count     24
     * zk_num_alive_connections        31
     * zk_avg_latency  0.128
     * zk_outstanding_requests 0
     * zk_znode_count  1511
     * zk_global_sessions      30
     * zk_non_mtls_remote_conn_count   0
     * zk_last_client_response_size    16
     * zk_packets_sent 1195693
     * zk_packets_received     1193063
     *
     * @return .
     */
    public Map<String, String> getZookeeperInfoMap(ZookeeperConfigBo config) {
        Map<String, String> infoMap = new HashMap<>();
        String exec = "echo mntr | nc localhost 2181";
        String s = deployService.executeCommandForPod(config.getPod().getPodName(), config.getPod().getNamespace(), new String[]{
                "/bin/bash",
                "-c",
                exec
        });
        log.info("zookeeper mntr exec '{}' result: {}", exec, s);
        if (StringUtils.isBlank(s)) {
            return infoMap;
        }
        // 按照行分割
        String[] lines = s.split("\n");
        for (String line : lines) {
            if (StringUtils.isBlank(line)) {
                continue;
            }
            // 按照 空格 分割
            String[] split = line.split("\\s");
            if (split.length == 2) {
                infoMap.put(split[0], split[1]);
            }
        }
        return infoMap;
    }

    public InspectionMetricTaskDb latency(String subTaskId, Map<String, String> infoMap) {
        long startTime = System.currentTimeMillis();
        String metricKey = InspectionMetricKeyEnum.ZK_LATENCY.getMetricKey();
        String zkMaxLatency = infoMap.get("zk_max_latency");
        String zkMinLatency = infoMap.get("zk_min_latency");
        String zkAvgLatency = infoMap.get("zk_avg_latency");
        if (StringUtils.isBlank(zkMaxLatency) || StringUtils.isBlank(zkMinLatency) || StringUtils.isBlank(zkAvgLatency)) {
            // 指标获取失败
            return metricTaskDao.buildFailedMetricTaskDb(subTaskId, startTime, "指标获取失败", "指标获取失败", metricKey);
        }
        String result = "zk_max_latency=" + zkMaxLatency + "\nzk_min_latency=" + zkMinLatency + "\nzk_avg_latency=" + zkAvgLatency;

        return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, result, metricKey, NORMAL);
    }

    public InspectionMetricTaskDb received(String subTaskId, Map<String, String> infoMap) {
        long startTime = System.currentTimeMillis();
        String metricKey = InspectionMetricKeyEnum.ZK_RECEIVED.getMetricKey();
        String zkPacketsReceived = infoMap.get("zk_packets_received");
        if (StringUtils.isBlank(zkPacketsReceived)) {
            // 指标获取失败
            return metricTaskDao.buildFailedMetricTaskDb(subTaskId, startTime, "指标获取失败", "指标获取失败", metricKey);
        }
        String result = "zk_packets_received=" + zkPacketsReceived;
        return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, result, metricKey, NORMAL);
    }

    public InspectionMetricTaskDb sent(String subTaskId, Map<String, String> infoMap) {
        long startTime = System.currentTimeMillis();
        String metricKey = InspectionMetricKeyEnum.ZK_SEND.getMetricKey();

        String zkPacketsSent = infoMap.get("zk_packets_sent");
        if (StringUtils.isBlank(zkPacketsSent)) {
            // 指标获取失败
            return metricTaskDao.buildFailedMetricTaskDb(subTaskId, startTime, "指标获取失败", "指标获取失败", metricKey);
        }
        String result = "zk_packets_sent=" + zkPacketsSent;
        return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, result, metricKey, NORMAL);
    }

    public InspectionMetricTaskDb connectedClients(String subTaskId, Map<String, String> infoMap) {
        long startTime = System.currentTimeMillis();
        String metricKey = InspectionMetricKeyEnum.ZK_CONNECTED_CLIENTS.getMetricKey();
        String zkNumAliveConnections = infoMap.get("zk_num_alive_connections");

        if (StringUtils.isBlank(zkNumAliveConnections)) {
            // 指标获取失败
            return metricTaskDao.buildFailedMetricTaskDb(subTaskId, startTime, "指标获取失败", "指标获取失败", metricKey);
        }

        long value = Long.parseLong(zkNumAliveConnections);

        String result = "连接活跃数=" + zkNumAliveConnections;
        // 活跃数大于两百，高风险
        if (value > 200) {
            return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, result, metricKey, HIGH_RISK);
        }
        return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, result, metricKey, NORMAL);
    }

    public InspectionMetricTaskDb znodeCount(String subTaskId, Map<String, String> infoMap) {
        long startTime = System.currentTimeMillis();
        String metricKey = InspectionMetricKeyEnum.ZK_NODE.getMetricKey();
        String zkZnodeCount = infoMap.get("zk_znode_count");
        if (StringUtils.isBlank(zkZnodeCount)) {
            // 指标获取失败
            return metricTaskDao.buildFailedMetricTaskDb(subTaskId, startTime, "指标获取失败", "指标获取失败", metricKey);
        }
        String result = "zk_znode_count=" + zkZnodeCount;
        return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, result, metricKey, NORMAL);
    }
}
