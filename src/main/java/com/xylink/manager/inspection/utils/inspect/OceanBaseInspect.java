package com.xylink.manager.inspection.utils.inspect;

import com.xylink.manager.inspection.dao.InspectionMetricTaskDao;
import com.xylink.manager.inspection.dao.InspectionSubTaskDao;
import com.xylink.manager.inspection.entity.db.InspectionMetricTaskDb;
import com.xylink.manager.inspection.entity.enums.InspectionMetricKeyEnum;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.db.JasyptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;

import static com.xylink.manager.inspection.entity.enums.InspectionThresholdLadderEnum.HIGH_RISK;
import static com.xylink.manager.inspection.entity.enums.InspectionThresholdLadderEnum.NORMAL;

/**
 * <AUTHOR>
 * @date 2024/11/19
 */
@Service("OB_Inspect")
@Slf4j
public class OceanBaseInspect extends DbInspect {

    private final InspectionMetricTaskDao metricTaskDao;

    public OceanBaseInspect(InspectionSubTaskDao subTaskDao,
                            InspectionMetricTaskDao metricTaskDao, JasyptService jasyptService,
                            K8sService k8sService) {
        super(subTaskDao, metricTaskDao, jasyptService, k8sService);
        this.metricTaskDao = metricTaskDao;
    }


    @Override
    public void inspectionMetric(String subTaskId, Connection connection, List<InspectionMetricTaskDb> metricTasks) {
        // 连接数
        metricTasks.add(connectionCount(subTaskId, connection));
    }

    @Override
    public void saveFailedMetricTasks(String subTaskId, List<InspectionMetricTaskDb> metricTasks) {
        metricTasks.add(metricTaskDao.buildFailedMetricTaskDb(subTaskId, InspectionMetricKeyEnum.MYSQL_CONNECTION_COUNT.getMetricKey(), "指标获取失败"));
    }

    public InspectionMetricTaskDb connectionCount(String subTaskId, Connection connection) {
        long startTime = System.currentTimeMillis();
        String metricKey = InspectionMetricKeyEnum.MYSQL_CONNECTION_COUNT.getMetricKey();
        String maxConnectionsSql = "show variables like 'max_connections';";
        String sessionTotalSql = "select count(*) as '会话总数' from information_schema.PROCESSLIST;";
        String runningSessionTotalSql = "select count(*) as '运行中会话总数' from information_schema.PROCESSLIST where COMMAND not in ('Sleep','Binlog Dump');";
        String maxRunningSessionTimeSql = "select max(TIME) as '运行中会话最长时间' from information_schema.PROCESSLIST where COMMAND not in ('Sleep','Binlog Dump');";
        try (PreparedStatement maxConnectionsPs = connection.prepareStatement(maxConnectionsSql);
             PreparedStatement sessionTotalPs = connection.prepareStatement(sessionTotalSql);
             PreparedStatement runningSessionTotalPs = connection.prepareStatement(runningSessionTotalSql);
             PreparedStatement maxRunningSessionTimePs = connection.prepareStatement(maxRunningSessionTimeSql);
             ResultSet maxConnectionsRs = maxConnectionsPs.executeQuery();
             ResultSet sessionTotalRs = sessionTotalPs.executeQuery();
             ResultSet runningSessionTotalRs = runningSessionTotalPs.executeQuery();
             ResultSet maxRunningSessionTimeRs = maxRunningSessionTimePs.executeQuery()
        ) {
            if (!maxConnectionsRs.next()
                    || !sessionTotalRs.next()
                    || !runningSessionTotalRs.next()
                    || !maxRunningSessionTimeRs.next()) {
                log.error("[inspection] Mysql inspection connection count failed, subTaskId={}", subTaskId);
                // 指标获取失败
                return metricTaskDao.buildFailedMetricTaskDb(subTaskId, startTime, "指标获取失败", "指标获取失败", metricKey);
            }
            long maxConnections = Long.parseLong(maxConnectionsRs.getString("Value"));
            long sessionTotal = sessionTotalRs.getLong(1);
            long runningSessionTotal = runningSessionTotalRs.getLong(1);
            long maxRunningSessionTime = maxRunningSessionTimeRs.getLong(1);

            String result = "最大连接数=" + maxConnections +
                    "\n会话总数=" + sessionTotal +
                    "\n运行中会话总数=" + runningSessionTotal +
                    "\n运行中会话最长时间" + maxRunningSessionTime;

            double value = sessionTotal * 1.0 / maxConnections;

            if (value >= 0.5) {
                // 高风险
                return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, result, metricKey, HIGH_RISK);
            }
            // 正常
            return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, result, metricKey, NORMAL);
        } catch (Exception e) {
            log.error("[inspection] mysql connection count info get error", e);
            return metricTaskDao.buildFailedMetricTaskDb(subTaskId, startTime, "指标获取失败", e.getMessage(), metricKey);
        }
    }
}
