package com.xylink.manager.inspection.utils;

import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 *<AUTHOR>
 */
public class PageUtil {

    private static int firstPageNo = 0;
    /**
     * 分页获取集合中的数据
     * @param all .
     * @param page 从0开始.
     * @param size .
     * @return .
     */
    public static <T> List<T> getPage(List<T> all, int page, int size) {
        if (CollectionUtils.isEmpty(all)) {
            return new ArrayList<>();
        }

        // 分页获取meetingId
        int startIndex = page * size;
        int endIndex = (page + 1) * size;
        List<T> pageData;

        if (startIndex >= all.size()) {
            // 开始索引超出了集合大小
            pageData = new ArrayList<>();
        } else if (endIndex <= all.size()){
            // 结束索引在集合中
            pageData = all.subList(startIndex, endIndex);
        } else {
            // 结束索引超出了集合，说明不够分一页的了
            pageData = all.subList(startIndex, all.size());
        }

        return pageData;

    }

    public static int getStart(int pageNo, int pageSize) {
        if (pageNo < firstPageNo) {
            pageNo = firstPageNo;
        }

        if (pageSize < 1) {
            pageSize = 0;
        }

        return (pageNo - firstPageNo) * pageSize;
    }

}
