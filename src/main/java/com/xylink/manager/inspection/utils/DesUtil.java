package com.xylink.manager.inspection.utils;

import javax.crypto.Cipher;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/19 16:06
 */
public class DesUtil {
    private static final String PASSWORD = "ops-inspection";
    private static final DesUtil INSTANCE = new DesUtil();

    public static DesUtil getInstance() {
        return INSTANCE;
    }

    private Key key;

    private DesUtil() {
        getKey(PASSWORD);
    }

    public void getKey(String strKey) {
        try {
            DESKeySpec desKeySpec = new DESKeySpec(strKey.getBytes());
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
            this.key = keyFactory.generateSecret(desKeySpec);
        } catch (InvalidKeyException | InvalidKeySpecException | NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 加密
     *
     * @param bytes 带加密的内容
     * @return 加密后的内容
     * @throws Exception .
     */
    public byte[] encrypt(byte[] bytes) throws Exception {
        Cipher cipher = Cipher.getInstance("DES");
        cipher.init(Cipher.ENCRYPT_MODE, this.key);

        return cipher.doFinal(bytes);
    }

    /**
     * 解密
     *
     * @param bytes 待解密的内容
     * @return 解密后的内容
     * @throws Exception .
     */
    public byte[] decrypt(byte[] bytes) throws Exception {
        Cipher cipher = Cipher.getInstance("DES");
        cipher.init(Cipher.DECRYPT_MODE, this.key);
        return cipher.doFinal(bytes);
    }
}
