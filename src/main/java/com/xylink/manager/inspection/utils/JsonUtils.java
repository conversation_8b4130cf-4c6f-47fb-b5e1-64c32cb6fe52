package com.xylink.manager.inspection.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonElement;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.xylink.manager.inspection.common.OpsManagerException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;

import javax.annotation.Nonnull;
import java.io.File;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.lang.reflect.Type;
import java.util.*;

/**
 * Created by niulong on 2022/3/8 6:15 PM
 */
public class JsonUtils {
    // 定义jackson对象
    private static final ObjectMapper MAPPER = new ObjectMapper();
    private static final ObjectMapper MAPPER_NO_NULL = new ObjectMapper();
    public static Logger log = LoggerFactory.getLogger(JsonUtils.class);

    static {
        //忽略不存在的字段
        MAPPER.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        MAPPER.registerModule(new JavaTimeModule());
        MAPPER_NO_NULL.disable(DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES);
        MAPPER_NO_NULL.registerModule(new JavaTimeModule());
        MAPPER_NO_NULL.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    /**
     * 把对象转换为json数据
     *
     * @param obj 要转换的java对象
     * @return json数据
     */
    public static String objectToJson(Object obj) {
        Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();

        try {
            return gson.toJson(obj);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    /**
     * 把对象转换为json数据
     *
     * @param obj 要转换的java对象
     * @return json数据
     */
    public static String objectToJsonNonNull(Object obj) {
        try {
            return MAPPER_NO_NULL.writeValueAsString(obj);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    /**
     * 把对象转换为json数据
     *
     * @param obj 要转换的java对象
     * @return json数据
     */
    public static String objectToJsonString(Object obj) {
        try {
            return MAPPER.writeValueAsString(obj);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    /**
     * 把对象转换为json数据
     *
     * @param obj 要转换的java对象
     * @return json数据
     */
    public static String objectToPrettyJsonString(Object obj) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(obj);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    /**
     * 将Object转换成Map
     *
     * @param obj java对象
     * @return map对象
     */
    public static Map<String, Object> objectToMap(Object obj) {

        try {
            Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
            String json = gson.toJson(obj);
            return jsonToMap(json);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    /**
     * 将Object类型的map转换成String类型
     *
     * @param map Map<String, Object>
     * @return Map<String, String>
     */
    public static Map<String, String> mapToMap(Map<String, Object> map) {
        Map<String, String> returnMap = new HashMap<>();
        for (String key : map.keySet()) {
            // String.valueOf(Object obj) : 将 obj 对象转换成 字符串, 等于 obj.toString()
            returnMap.put(key, String.valueOf(map.get(key)));
        }
        return returnMap;
    }

    /**
     * 任意类型转换成Map
     *
     * @param obj Java对象
     * @return map对象
     */
    public static Map<String, String> object2Map(Object obj) {
        Map<String, String> hashMap = new HashMap();
        try {
            Class c = obj.getClass();
            Method m[] = c.getDeclaredMethods();
            for (Method method : m) {
                if (method.getName().indexOf("get") == 0) {
                    // 得到Map的key
                    String suffixKey = method.getName().substring(4);
                    String prefixKey = method.getName().substring(3, 4).toLowerCase();
                    hashMap.put(prefixKey + suffixKey, String.valueOf(method.invoke(obj)));
                }
            }
        } catch (Throwable e) {
            log.error(e.getMessage());
        }
        return hashMap;
    }

    /**
     * 把json字符串转化为对象
     *
     * @param jsonString json字符串
     * @param clazz      java对象的类型
     * @return java对象
     */
    public static Object jsonToObject(String jsonString, Class<?> clazz) {
        Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
        Object obj = null;
        try {
            obj = gson.fromJson(jsonString, clazz);
        } catch (JsonSyntaxException e) {
            log.error(e.getMessage());
        }
        return obj;
    }

    /**
     * josn转arrayList
     *
     * @param jsonArray json字符串
     * @return List集合
     */
    public static ArrayList<?> jsonArrayToArrayList(String jsonArray) {
        Gson gson = new GsonBuilder()
                //以防止序列化Final，Transient或Static字段
                .excludeFieldsWithModifiers(Modifier.FINAL, Modifier.TRANSIENT, Modifier.STATIC)
                .setDateFormat("yyyy-MM-dd HH:mm:ss")
                // serializeNulls()，支持空字符串的序列化
                .serializeNulls()
                .create();
        ArrayList<?> list = null;
        try {
            Type listType = new TypeToken<ArrayList<?>>() {
            }.getType();

            list = gson.fromJson(jsonArray, listType);
        } catch (JsonSyntaxException e) {
            log.error(e.getMessage());
        }
        return list;
    }

    /**
     * JSON 转 ArrayList
     *
     * @param jsonArray JSON字符串
     * @param clazz     转换类型
     */
    public static ArrayList<?> jsonArrayToArrayList(String jsonArray, Class<?> clazz) {
        Gson gson = new GsonBuilder()
                .excludeFieldsWithModifiers(Modifier.FINAL, Modifier.TRANSIENT, Modifier.STATIC)
                .setDateFormat("yyyy-MM-dd HH:mm:ss")
                .serializeNulls()
                .create();
        ArrayList<?> list = null;
        try {
            Type listType = TypeToken.getParameterized(ArrayList.class, clazz).getType();
            list = gson.fromJson(jsonArray, listType);
        } catch (JsonSyntaxException e) {
            log.error(e.getMessage());
        }
        return list;
    }

    /**
     * 把json转换为map类型的数据
     *
     * @param json JSON字符串
     * @return map集合
     */
    public static Map<String, Object> jsonToMap(String json) {
        Gson gson = new GsonBuilder()
                .excludeFieldsWithModifiers(Modifier.FINAL, Modifier.TRANSIENT, Modifier.STATIC)
                .setDateFormat("yyyy-MM-dd HH:mm:ss")
                .serializeNulls()
                .create();
        Map<String, Object> map = null;
        try {
            Type type = new TypeToken<Map<String, Object>>() {
            }.getType();

            map = gson.fromJson(json, type);
        } catch (JsonSyntaxException e) {
            log.error(e.getMessage());
        }
        return map;
    }

    /**
     * 将Json转换成Map<String, ?>
     *
     * @param json  json字符串
     * @param clazz 转换类型
     * @return map集合
     */
    public static Map<String, ?> jsonToMap(String json, Class<?> clazz) {
        Gson gson = new GsonBuilder()
                .excludeFieldsWithModifiers(Modifier.FINAL, Modifier.TRANSIENT, Modifier.STATIC)
                .setDateFormat("yyyy-MM-dd HH:mm:ss")
                .serializeNulls()
                .create();
        Map<String, ?> map = null;
        try {
            Type type = new TypeToken<Map<String, ?>>() {
            }.getType();

            map = gson.fromJson(json, type);
        } catch (JsonSyntaxException e) {
            log.error(e.getMessage());
        }
        return map;
    }

    /**
     * 将map转换成pojo
     *
     * @param map      map集合
     * @param beanType 转换类型
     * @param <T>      泛型
     * @return pojo对象
     */
    public static <T> T mapToPojo(Map<String, Object> map, Class<T> beanType) {

        Gson gson = new GsonBuilder()
                .excludeFieldsWithModifiers(Modifier.FINAL, Modifier.TRANSIENT, Modifier.STATIC)
                .setDateFormat("yyyy-MM-dd HH:mm:ss")
                .serializeNulls()
                .create();

        JsonElement jsonElement = gson.toJsonTree(map);
        return gson.fromJson(jsonElement, beanType);
    }

    /**
     * 将json结果集转化为对象
     *
     * @param jsonData json字符串
     * @param beanType 转换类型
     * @param <T>      泛型
     * @return pojo对象
     */
    public static <T> T jsonToPojo(String jsonData, Class<T> beanType) {
        try {
            return MAPPER.readValue(jsonData, beanType);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    /**
     * 将json数据转换成list
     *
     * @param jsonData json字符串
     * @param beanType 转换类型
     * @return list集合
     */
    public static <T> List<T> jsonToList(String jsonData, Class<T> beanType) {
        JavaType javaType = MAPPER.getTypeFactory().constructParametricType(List.class, beanType);
        try {
            return MAPPER.readValue(jsonData, javaType);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }


    /**
     * 将任意pojo转化成map
     *
     * @param t pojo对象
     * @return map集合
     */
    public static <T> Map<String, Object> pojoToMap(T t) {
        Map<String, Object> result = new HashMap<String, Object>();
        Method[] methods = t.getClass().getMethods();
        try {
            for (Method method : methods) {
                Class<?>[] paramClass = method.getParameterTypes();
                // 如果方法带参数，则跳过
                if (paramClass.length > 0) {
                    continue;
                }
                String methodName = method.getName();
                if (methodName.startsWith("get")) {
                    Object value = method.invoke(t);
                    result.put(methodName, value);
                }
            }
        } catch (IllegalArgumentException | IllegalAccessException | InvocationTargetException | SecurityException e) {
            log.error(e.getMessage());
        }
        return result;
    }

    /**
     * 查json字符串中 嵌套的字段
     */
    public static String findKey(String json, String key) {
        try {
            JsonNode jsonNode = MAPPER.readTree(json);
            return findJsonNode(jsonNode, key);
        } catch (Exception e) {
            throw new OpsManagerException(HttpStatus.INTERNAL_SERVER_ERROR, "json parse error");
        }
    }

    public static String findJsonNode(JsonNode jsonNode, String key) {
        for (JsonNode node : jsonNode) {
            Iterator<String> child = node.fieldNames();
            while (child.hasNext()) {
                if (child.next().equals(key)) {
                    return node.get(key).asText();
                }
            }
            findJsonNode(node, key);
        }
        return "";
    }

    /**
     * 将Json转换成Map<String, ?>
     *
     * @param json  json字符串
     * @param clazz 转换类型
     * @return map集合
     */
    public static <T> Map<String, T> jsonToMap2(String json, Class<T> clazz) {
        Gson gson = new GsonBuilder()
                .excludeFieldsWithModifiers(Modifier.FINAL, Modifier.TRANSIENT, Modifier.STATIC)
                .setDateFormat("yyyy-MM-dd HH:mm:ss")
                .serializeNulls()
                .create();
        Map<String, T> map = null;
        try {
            Type type = new TypeToken<Map<String, T>>() {
            }.getType();

            map = gson.fromJson(json, type);
        } catch (JsonSyntaxException e) {
            log.error(e.getMessage());
        }
        return map;
    }

    /**
     * 将文件转化为对象
     *
     * @param file     文件
     * @param beanType 转换类型
     * @param <T>      泛型
     * @return pojo对象
     */
    public static <T> T fileToPojo(File file, TypeReference<T> beanType) {
        try {
            return MAPPER.readValue(file, beanType);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    public static <T> T jsonToPo(String json, TypeReference<T> type) {
        try {
            return MAPPER.readValue(json, type);
        } catch (JsonProcessingException e) {
            throw new OpsManagerException(HttpStatus.INTERNAL_SERVER_ERROR, "json parse exception");
        }
    }

    public static <T> List<T> jsonToList2(String json, @Nonnull Class<T> tClass) {
        List<T> resultList = new ArrayList<>();
        if (StringUtils.isBlank(json)) {
            return new ArrayList<>();
        }
        try {
            JsonNode jsonNode = MAPPER.readTree(json);
            if (jsonNode.isArray()) {
                for (JsonNode node : jsonNode) {
                    T t = MAPPER.treeToValue(node, tClass);
                    resultList.add(t);
                }
            }
            return resultList;
        } catch (JsonProcessingException e) {
            throw new OpsManagerException(HttpStatus.INTERNAL_SERVER_ERROR, "json parse exception");
        }
    }

    public static ObjectMapper getMapper() {
        return MAPPER;
    }

    /**
     * 将文件转化为对象
     *
     * @param inputStream 文件流
     * @param beanType 转换类型
     * @param <T>      泛型
     * @return pojo对象
     */
    public static <T> T toPojo(InputStream inputStream, TypeReference<T> beanType) {
        try {
            return MAPPER.readValue(inputStream, beanType);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }
}
