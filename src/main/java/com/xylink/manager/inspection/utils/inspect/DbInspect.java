package com.xylink.manager.inspection.utils.inspect;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.inspection.constants.MiddlewareConstants;
import com.xylink.manager.inspection.dao.InspectionMetricTaskDao;
import com.xylink.manager.inspection.dao.InspectionSubTaskDao;
import com.xylink.manager.inspection.entity.bo.DbConfigBo;
import com.xylink.manager.inspection.entity.db.InspectionItemConfigDb;
import com.xylink.manager.inspection.entity.db.InspectionMetricTaskDb;
import com.xylink.manager.inspection.entity.db.InspectionSubTaskDb;
import com.xylink.manager.inspection.entity.dto.InspectDbConfigDTO;
import com.xylink.manager.inspection.entity.enums.InspectionItemTypeEnum;
import com.xylink.manager.inspection.entity.enums.InspectionMetricKeyEnum;
import com.xylink.manager.model.deploy.Node;
import com.xylink.manager.model.em.DBType;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.base.SystemModeConfig;
import com.xylink.manager.service.db.JasyptService;
import com.xylink.util.DbConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.xylink.manager.inspection.entity.enums.InspectionThresholdLadderEnum.HIGH_RISK;
import static com.xylink.manager.inspection.entity.enums.InspectionThresholdLadderEnum.NORMAL;

/**
 * <AUTHOR>
 * @date 2024/11/19
 */
@Slf4j
public abstract class DbInspect {
    private InspectionSubTaskDao subTaskDao;
    private InspectionMetricTaskDao metricTaskDao;
    private JasyptService jasyptService;
    private K8sService k8sService;


    public DbInspect(InspectionSubTaskDao subTaskDao,
                     InspectionMetricTaskDao metricTaskDao, JasyptService jasyptService,
                     K8sService k8sService) {
        this.subTaskDao = subTaskDao;
        this.metricTaskDao = metricTaskDao;
        this.jasyptService = jasyptService;
        this.k8sService = k8sService;
    }

    protected Map<String, String> getDbConfigMap() {
        try {
            return k8sService.getConfigmap("all-ip");
        } catch (Exception e) {
            log.error("[inspection] get db config error.", e);
            return Collections.emptyMap();
        }
    }

    protected List<DbConfigBo> getDbConfig() {
        List<DbConfigBo> configs = new ArrayList<>();
        Map<String, String> dbConfigMap = getDbConfigMap();
        String dbType = dbConfigMap.get(NetworkConstants.DATABASE_TYPE);
        String username = dbConfigMap.get(NetworkConstants.MAIN_DB_BACKUP_USERNAME);
        String password = dbConfigMap.get(NetworkConstants.MAIN_DB_BACKUP_PASSWORD);
        password = jasyptService.decrypt(password);

        // 获取 mysql-main 数据库连接信息
        String dbMainHost = String.valueOf(dbConfigMap.get(NetworkConstants.DATABASE_IP));
        String dbMainPort = String.valueOf(dbConfigMap.get(NetworkConstants.DATABASE_PORT));
        configs.add(getDbConfigBo(MiddlewareConstants.DB_MAIN_NAME, dbMainHost, dbMainPort,
                username, password, "ainemo", dbType));
        if (SystemModeConfig.isCmsOrXms()) {
            return configs;
        }

        InspectDbConfigDTO inspectDbConfigDTOUaa = InspectDbConfigDTO.builder()
                .inspectName("db-uaa")
                .schema("uaa")
                .dbIPKey(NetworkConstants.UAA_DATABASE_IP)
                .dbPortKey(NetworkConstants.UAA_DATABASE_PORT)
                .dbType(dbType)
                .type(DBType.uaa)
                .username(username)
                .password(password)
                .build();
        // 如果 mysql-uaa 是启用状态，获取 mysql 连接信息
        addDbConfigBo(configs, dbConfigMap, inspectDbConfigDTOUaa);

        InspectDbConfigDTO inspectDbConfigDTOStatis = InspectDbConfigDTO.builder()
                .inspectName("db-statis")
                .schema("statis")
                .dbIPKey(NetworkConstants.STATIS_DATABASE_IP)
                .dbPortKey(NetworkConstants.STATIS_DATABASE_PORT)
                .dbType(dbType)
                .type(DBType.statis)
                .username(username)
                .password(password)
                .build();
        // 如果 mysql-statis 是启用状态，获取 mysql 连接信息
        addDbConfigBo(configs, dbConfigMap, inspectDbConfigDTOStatis);


        InspectDbConfigDTO inspectDbConfigDTOMatrix = InspectDbConfigDTO.builder()
                .inspectName("db-matrix")
                .schema("matrix")
                .dbIPKey(NetworkConstants.MATRIX_DATABASE_IP)
                .dbPortKey(NetworkConstants.MATRIX_DATABASE_PORT)
                .dbType(dbType)
                .type(DBType.matrix)
                .username(username)
                .password(password)
                .build();
        // 如果 mysql-matrix 是启用状态，获取 mysql 连接信息
        addDbConfigBo(configs, dbConfigMap, inspectDbConfigDTOMatrix);

        String survDbIpKey = dbConfigMap.containsKey(NetworkConstants.DB_SURVEILLANCE) &&
                StringUtils.isNotBlank(dbConfigMap.get(NetworkConstants.DB_SURVEILLANCE)) ?
                NetworkConstants.DB_SURVEILLANCE : NetworkConstants.SURV_INTERNAL_IP;
        InspectDbConfigDTO inspectDbConfigDTOSurv = InspectDbConfigDTO.builder()
                .inspectName("db-surv")
                .schema("surveillance")
                .dbIPKey(survDbIpKey)
                .dbPortKey(NetworkConstants.SURV_DATABASE_PORT)
                .dbType(dbType)
                .type(DBType.surv)
                .username(username)
                .password(password)
                .build();
        // 如果 mysql-surv 是启用状态，获取 mysql 连接信息
        addDbConfigBo(configs, dbConfigMap, inspectDbConfigDTOSurv);
        InspectDbConfigDTO inspectDbConfigDTODu = InspectDbConfigDTO.builder()
                .inspectName("db-edu")
                .schema("education")
                .dbIPKey(NetworkConstants.EDU_DATABASE_IP)
                .dbPortKey(NetworkConstants.EDU_DATABASE_PORT)
                .dbType(dbType)
                .type(DBType.edu)
                .username(username)
                .password(password)
                .build();
        // 如果 mysql-edu 是启用状态，获取 mysql 连接信息
        addDbConfigBo(configs, getDbConfigMap(), inspectDbConfigDTODu);

        return configs;
    }


    private void addDbConfigBo(List<DbConfigBo> configs, Map<String, String> dbConfigMap,
                               InspectDbConfigDTO configDTO) {
        // 如果 mysql-uaa 是启用状态，获取 mysql 连接信息
        if (!dbConfigMap.containsKey(configDTO.getDbIPKey())
                || dbConfigMap.get(configDTO.getDbIPKey()) == null
                || dbConfigMap.get(configDTO.getDbIPKey()).equals(configDTO.getDbMainHost())) {
            log.info("the db is not single, dbname:{}", configDTO.getInspectName());
            return;
        }
        String host = String.valueOf(dbConfigMap.get(configDTO.getDbIPKey()));
        String port = String.valueOf(dbConfigMap.get(configDTO.getDbPortKey()));
        List<Node> nodes = k8sService.getNodeByLabel("type", configDTO.getType().name());
        if (nodes == null || nodes.isEmpty()) {
            log.info("the db is not deploy, dbname:{}", configDTO.getInspectName());
            return;
        }
        configs.add(DbConfigBo.builder()
                .name(configDTO.getInspectName())
                .host(host)
                .port(port)
                .username(configDTO.getUsername())
                .password(configDTO.getPassword())
                .databaseName(configDTO.getSchema())
                .dbType(configDTO.getDbType())
                .build());
    }

    protected DbConfigBo getDbConfigBo(String name, String host, String port,
                                       String username, String password, String databaseName, String dbType) {
        return DbConfigBo.builder()
                .name(name)
                .host(host)
                .port(port)
                .username(username)
                .password(password)
                .databaseName(databaseName)
                .dbType(dbType)
                .build();
    }

    public List<InspectionSubTaskDb> inspect(String taskId, InspectionItemConfigDb itemConfig) {
        List<InspectionSubTaskDb> subTasks = new ArrayList<>();
        try {
            List<DbConfigBo> configs = getDbConfig();
            log.info("[inspection] db configs size: {}", configs.size());
            for (DbConfigBo config : configs) {
                log.info("[inspection] db name {}, db url {}", config.getName(), config.getHost());
                InspectionSubTaskDb subTask = subTaskDao.createSubTask(taskId, itemConfig.getId(), InspectionItemTypeEnum.MIDDLEWARE.getId(), config.getName());
                List<InspectionMetricTaskDb> metricTasks = inspectDb(subTask.getId(), config);
                subTaskDao.updateSubTask(subTask, metricTasks);
                subTasks.add(subTask);
            }
        } catch (Exception e) {
            log.error("[inspection] mysql inspect error", e);
        }
        return subTasks;
    }

    /**
     * 获取数据库指标信息
     *
     * @param subTaskId .
     * @param config    数据库信息
     * @return metricTask
     */
    protected List<InspectionMetricTaskDb> inspectDb(String subTaskId, DbConfigBo config) {
        List<InspectionMetricTaskDb> metricTasks = new ArrayList<>();
        Connection connection = null;
        try {
            connection = getConnection(config);
            if (!connection.isValid(60)) {
                // 数据库连接不可用
                saveFailedMetricTasksDefault(subTaskId, metricTasks);
                saveFailedMetricTasks(subTaskId, metricTasks);
            } else {
                inspectionMetricDefault(subTaskId, connection, metricTasks);
                inspectionMetric(subTaskId, connection, metricTasks);
            }
        } catch (Exception e) {
            log.error("[inspection] " + config.getUsername() + " connection error, mysql host " + config.getHost(), e);
            // 拼接所有指标错误信息
            saveFailedMetricTasks(subTaskId, metricTasks);
        } finally {
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    log.error("[inspection] Mysql collection close error.", e);
                }
            }
        }
        metricTaskDao.saveMetricTasks(metricTasks);
        return metricTasks;
    }

    void saveFailedMetricTasksDefault(String subTaskId, List<InspectionMetricTaskDb> metricTasks) {
        metricTasks.add(metricTaskDao.buildFailedMetricTaskDb(subTaskId, InspectionMetricKeyEnum.DB_AGENT_LIVE.getMetricKey(), "指标获取失败", 0));
    }

    void inspectionMetricDefault(String subTaskId, Connection connection, List<InspectionMetricTaskDb> metricTasks) {
        // 数据库可用
        metricTasks.add(agentLive(subTaskId, connection));
    }

    abstract void saveFailedMetricTasks(String subTaskId, List<InspectionMetricTaskDb> metricTasks);

    abstract void inspectionMetric(String subTaskId, Connection connection, List<InspectionMetricTaskDb> metricTasks);

    private synchronized Connection getConnection(DbConfigBo configBo) throws SQLException {
        Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        DbConfigUtil.DbConfig dbConfig = new DbConfigUtil.DbConfig(configBo.getHost(), configBo.getPort(),
                configBo.getDbType(), "", allIp);
        String jdbcUrl = dbConfig.jdbcUrl();
        String username = configBo.getUsername();
        String password = configBo.getPassword();
        return DriverManager.getConnection(jdbcUrl, username, password);
    }

    protected InspectionMetricTaskDb agentLive(String subTaskId, Connection connection) {
        long startTime = System.currentTimeMillis();
        String metricKey = InspectionMetricKeyEnum.DB_AGENT_LIVE.getMetricKey();
        try {
            boolean valid = connection.isValid(60);
            if (valid) {
                return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, "数据库连接成功", metricKey, NORMAL);
            }
            return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, "数据库不可用", metricKey, HIGH_RISK);
        } catch (Exception e) {
            log.error("[inspection] mysql agent live info get error", e);
            return metricTaskDao.buildFailedMetricTaskDb(subTaskId, startTime, "指标获取失败", e.getMessage(), metricKey);
        }
    }

}
