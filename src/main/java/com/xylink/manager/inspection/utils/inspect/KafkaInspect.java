package com.xylink.manager.inspection.utils.inspect;

import com.xylink.config.Constants;
import com.xylink.config.K8sSvcConstants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.inspection.common.OpsManagerException;
import com.xylink.manager.inspection.dao.InspectionMetricTaskDao;
import com.xylink.manager.inspection.dao.InspectionSubTaskDao;
import com.xylink.manager.inspection.entity.bo.KafkaConfigBo;
import com.xylink.manager.inspection.entity.db.InspectionItemConfigDb;
import com.xylink.manager.inspection.entity.db.InspectionMetricTaskDb;
import com.xylink.manager.inspection.entity.db.InspectionSubTaskDb;
import com.xylink.manager.inspection.entity.enums.InspectionItemTypeEnum;
import com.xylink.manager.inspection.entity.enums.InspectionMetricKeyEnum;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.base.K8sSvcService;
import com.xylink.manager.service.db.JasyptService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.xylink.manager.inspection.entity.enums.InspectionThresholdLadderEnum.NORMAL;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/17 10:47
 */
@Component
@Slf4j
public class KafkaInspect {
    @Autowired
    private K8sService k8sService;
    @Autowired
    private InspectionSubTaskDao subTaskDao;
    @Autowired
    private InspectionMetricTaskDao metricTaskDao;
    @Autowired
    private JasyptService jasyptService;

    private static final int DEFAULT_PORT = 9093;
    private static final String DEFAULT_TASK_ITEM = "kafka";

    private static final String GROUP_ID = "kafka-inspection";
    private static final String TEST_TOPIC = "manager-inspection-topic";

    @Autowired
    private K8sSvcService k8sSvcService;

    private List<KafkaConfigBo> getKafkaConfig() {
        List<KafkaConfigBo> configs = new ArrayList<>();
        Map<String, String> allIp = k8sService.getConfigmap("all-ip");
        String kafkaSingleHost = allIp.get(NetworkConstants.KAFKA_INTERNAL_IP);
        if(k8sService.isNewCms()) {
            kafkaSingleHost = k8sSvcService.getServiceIpByDefaultNs(K8sSvcConstants.KAFKA_NAME);
        }
        // kafka 单机模式
        configs.add(KafkaConfigBo.builder().name(DEFAULT_TASK_ITEM).host(kafkaSingleHost).build());
        return configs;
    }

    public List<InspectionSubTaskDb> inspect(String taskId, InspectionItemConfigDb itemConfig) {
        List<InspectionSubTaskDb> subTasks = new ArrayList<>();
        try {
            List<KafkaConfigBo> configs = getKafkaConfig();
            log.info("[inspection] kafka configs size: {}", configs.size());
            for (KafkaConfigBo config : configs) {
                log.info("[inspection] kafka name {}, kafka host {}", config.getName(), config.getHost());
                InspectionSubTaskDb subTask = subTaskDao.createSubTask(taskId, itemConfig.getId(), InspectionItemTypeEnum.MIDDLEWARE.getId(), config.getName());
                List<InspectionMetricTaskDb> metricTasks = inspectKafka(subTask.getId(), config);
                subTaskDao.updateSubTask(subTask, metricTasks);
                subTasks.add(subTask);
            }
        } catch (Exception e) {
            log.error("[inspection] kafka inspect error", e);
        }
        return subTasks;
    }

    public List<InspectionMetricTaskDb> inspectKafka(String subTaskId, KafkaConfigBo configBo) {
        List<InspectionMetricTaskDb> metricTasks = new ArrayList<>();
        metricTasks.add(inspectWriteAndRead(subTaskId, configBo.getHost()));
        metricTaskDao.saveMetricTasks(metricTasks);
        return metricTasks;
    }

    private InspectionMetricTaskDb inspectWriteAndRead(String subTaskId, String kafkaHost) {
        long startTime = System.currentTimeMillis();
        String metricKey = InspectionMetricKeyEnum.KAFKA_PRODUCED_AND_CONSUMED.getMetricKey();
        try {
            productionAndConsumption(kafkaHost);
        } catch (OpsManagerException e) {
            return metricTaskDao.buildFailedMetricTaskDb(subTaskId, startTime, e.getMessage(), e.getMessage(), metricKey);
        } catch (Exception e) {
            return metricTaskDao.buildFailedMetricTaskDb(subTaskId, startTime, "kafka 连接失败", e.getMessage(), metricKey);
        }
        return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, "正常生产消费数据", metricKey, NORMAL);
    }

    /**
     * 检查 业务kafka
     * Production and consumption data
     */
    public void productionAndConsumption(String kafkaHost) {
        Properties properties = constructProperties(kafkaHost);
        producedTestMessage(properties);
        consumeTestMessage(properties);
    }

    private Properties constructProperties(String kafkaHost) {
        Properties properties = new Properties();
        if(!kafkaHost.endsWith(":9093")) {
            kafkaHost += ":9093";
        }
        properties.put("bootstrap.servers", kafkaHost);
        properties.put("group.id", GROUP_ID);
        // 响应超时时间
        properties.put("request.timeout.ms", 3000);
        // 空闲超时时间
        properties.put("connections.max.idle.ms", 5000);
        // acks=all 消息在所有的 ISR 都被持久化成功后才告知生产者消息发送成功
        properties.put("acks", "all");
        // 重试次数
        properties.put("retries", 0);
        properties.put("auto.offset.reset", "earliest");
        properties.put("allow.auto.create.topics", false);
        properties.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        properties.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        properties.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        properties.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        boolean kafkaAuthorize = allIp.containsKey("KAFKA_AUTHORIZE") && Boolean.parseBoolean(allIp.get("KAFKA_AUTHORIZE"));
        if (kafkaAuthorize) {
            properties.put("security.protocol", allIp.get("KAFKA_AUTH_ENABLE_SECURITY_PROTOCOL"));
            properties.put("sasl.mechanism", allIp.get("KAFKA_AUTH_ENABLE_SASL_MECHANISMS"));
            properties.put("sasl.jaas.config", jasyptService.decrypt(allIp.get("KAFKA_AUTH_ENABLE_SASL_CONFIG")));
        }
        return properties;
    }

    /**
     * 向测试 topic 发送 kafka 消息
     *
     * @param properties .
     */
    private void producedTestMessage(Properties properties) {
        try (Producer<String, String> producer = new KafkaProducer<>(properties)) {
            RecordMetadata recordMetadata = producer.send(new ProducerRecord<>(TEST_TOPIC, "ops-inspection-test", "hello world!")).get(5000L, TimeUnit.MILLISECONDS);
            log.debug("inspect producer: partition=" + recordMetadata.partition());
        } catch (Exception e) {
            log.error("producedTestMessage error", e);
            throw new OpsManagerException("生产数据失败");
        }
    }

    /**
     * 消费测试 topic 中的消息
     *
     * @param properties .
     */
    private void consumeTestMessage(Properties properties) {
        try (KafkaConsumer<String, String> consumer = new KafkaConsumer<>(properties)) {
            consumer.subscribe(Collections.singletonList(TEST_TOPIC));
            ConsumerRecords<String, String> records = consumer.poll(Duration.ofSeconds(10));
            if (log.isDebugEnabled()) {
                for (ConsumerRecord<String, String> record : records) {
                    log.debug("inspect consumer: " + record.key() + "=" + record.value());
                }
            }
        } catch (Exception e) {
            log.error("consumeTestMessage error", e);
            throw new OpsManagerException("消费数据失败");
        }
    }
}
