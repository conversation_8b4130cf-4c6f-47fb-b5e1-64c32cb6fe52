package com.xylink.manager.inspection.utils;

import io.netty.util.concurrent.DefaultThreadFactory;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/2 18:02
 */
public class ExecutorServiceUtil {
    /**
     * 创建一个用于执行巡检的线程池
     */
    public static final ExecutorService INSPECTION_EXECUTOR = new ThreadPoolExecutor(1, 1,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(),
            new DefaultThreadFactory("InspectionThreadPool"));

    public static final ExecutorService INSPECION_EXPROT_EXECUOR = new ThreadPoolExecutor(1,
            1, 0L,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(),
            new DefaultThreadFactory("InspectionExportThreadPool"));
}
