package com.xylink.manager.inspection.utils.inspect;

import com.xylink.config.Constants;
import com.xylink.config.K8sSvcConstants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.RedisConstants;
import com.xylink.manager.controller.dto.inspect.RedisConfigDTO;
import com.xylink.manager.inspection.constants.MiddlewareConstants;
import com.xylink.manager.inspection.dao.InspectionMetricTaskDao;
import com.xylink.manager.inspection.dao.InspectionSubTaskDao;
import com.xylink.manager.inspection.entity.db.InspectionItemConfigDb;
import com.xylink.manager.inspection.entity.db.InspectionMetricTaskDb;
import com.xylink.manager.inspection.entity.db.InspectionSubTaskDb;
import com.xylink.manager.inspection.entity.enums.InspectionItemTypeEnum;
import com.xylink.manager.inspection.entity.enums.InspectionMetricKeyEnum;
import com.xylink.manager.model.em.InspectLadderEnum;
import com.xylink.manager.model.em.RedisMode;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.base.K8sSvcService;
import com.xylink.manager.service.base.SystemModeConfig;
import com.xylink.manager.service.db.JasyptService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import redis.clients.jedis.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.*;

import static com.xylink.config.RedisConstants.REDIS_MODE_SINGLE;
import static com.xylink.manager.inspection.entity.enums.InspectionThresholdLadderEnum.HIGH_RISK;
import static com.xylink.manager.inspection.entity.enums.InspectionThresholdLadderEnum.NORMAL;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/8 16:15
 */
@Component
@Slf4j
public class RedisInspect {

    @Value("${inspect.redis.single.username:otierdsor}")
    private String redisUser;
    private String redisPass="ENC(m5QXyWI2gT624TJdXfHNN/Sso8/PAa15zSPW2tKNfbLQM4f5maTibc/iZV9tvPcumwnv8/w20FAkPp6A+Zl8cg==)";
    @Value("${inspect.redis.single.port:6379}")
    private Integer port;
    @Value("${inspect.redis.sentinel.name:redismaster}")
    private String redisSentinelName;
    private String redisSentinelPasswd = "ENC(dO5NrdToiDg6aJKqzm0DsT3Z5VFcTRWhhVItOoWqdIDhZ9CBB65SQ6eeWJwLoMYcgO8RIkrXjNlTxd7BMVSBEg==)";

    @Autowired
    private K8sService k8sService;

    @Autowired
    private K8sSvcService k8sSvcService;

    @Autowired
    JasyptService jasyptService;

    @Autowired
    private InspectionSubTaskDao subTaskDao;
    @Autowired
    private InspectionMetricTaskDao metricTaskDao;

    private RedisConfigDTO getRedisConfig() {
        Map<String, String> allRedis = k8sService.getConfigmap(Constants.CONFIGMAP_REDIS);
        String redisMode = allRedis.get(RedisConstants.REDIS_MODE_KEY);
        Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        String redisIp = allIp.get(NetworkConstants.MAIN_REDIS_IP);
        if(k8sService.isNewCms()) {
            redisIp = k8sSvcService.getServiceIpByDefaultNs(K8sSvcConstants.REDIS_NAME);
        }

        String passwd = jasyptService.decrypt(redisPass);
        String sentinelPasswd = jasyptService.decrypt(redisSentinelPasswd);
        String portStr = allIp.get(NetworkConstants.MAIN_REDIS_PORT);
        //单机模式
        if (StringUtils.isBlank(redisMode)) {
            redisMode = allIp.get(RedisConstants.REDIS_MODE_KEY);
            if (StringUtils.isBlank(redisMode)) {
                redisMode = REDIS_MODE_SINGLE;
            }
        }
        if (StringUtils.isNotBlank(portStr)) {
            port = Integer.parseInt(portStr);
        }

        String name = "main-redis";
        return RedisConfigDTO.builder().host(redisIp).name(name).user(redisUser).pwd(passwd).port(port)
                .redisSentinelPasswd(sentinelPasswd).redisSentinelName(redisSentinelName).mode(redisMode).build();
    }

    public List<InspectionSubTaskDb> inspect(String taskId, InspectionItemConfigDb itemConfig) {
        List<InspectionSubTaskDb> subTasks = new ArrayList<>();
        try {
            RedisConfigDTO configBo = getRedisConfig();
            log.info("[inspection] redis name {}, redis host {}", configBo.getName(), configBo.getHost());
            InspectionSubTaskDb subTask = subTaskDao.createSubTask(taskId, itemConfig.getId(), InspectionItemTypeEnum.MIDDLEWARE.getId(), configBo.getName());
            List<InspectionMetricTaskDb> metricTasks = inspectRedis(subTask.getId(), configBo);
            subTaskDao.updateSubTask(subTask, metricTasks);
            subTasks.add(subTask);
        } catch (Exception e) {
            log.error("[inspection] redis inspect error", e);
        }
        return subTasks;
    }

    private List<InspectionMetricTaskDb> inspectRedis(String subTaskId, RedisConfigDTO configDTO) {
        List<InspectionMetricTaskDb> metricTasks = new ArrayList<>();
        try {
            //inspectionMetric(subTaskId, redisInfo, metricTasks);
            //集群模式
            if (StringUtils.equals(RedisMode.CLUSTER.getMode(), configDTO.getMode())) {
                inspectRedisCluster(subTaskId, configDTO, metricTasks);
            } else {
                inspectRedisNoCluster(subTaskId, configDTO, metricTasks);
            }
        } catch (Exception e) {
            log.error("[inspection] " + configDTO.getName() + " connection error, redis host " + configDTO.getHost(), e);
            saveFailedMetricTasks(subTaskId, metricTasks);
        }
        metricTaskDao.saveMetricTasks(metricTasks);
        return metricTasks;
    }

    private boolean inspectRedisNoCluster(String subTaskId, RedisConfigDTO configDTO,
                                          List<InspectionMetricTaskDb> metricTasks) {
        if (StringUtils.equals(RedisMode.CLUSTER.getMode(), configDTO.getMode())) {
            log.warn("redis is cluster mode");
            return false;
        }
        Map<String, String> redisInfo = getRedisInfo(configDTO);
        if (redisInfo == null || redisInfo.isEmpty()) {
            saveFailedMetricTasks(subTaskId, metricTasks);
            return false;
        }
        inspectionMetric(subTaskId, redisInfo, metricTasks);
        return true;
    }

    private boolean inspectRedisCluster(String subTaskId, RedisConfigDTO configDTO,
                                        List<InspectionMetricTaskDb> metricTasks) {
        if (!StringUtils.equals(RedisMode.CLUSTER.getMode(), configDTO.getMode())) {
            log.warn("redis is not cluster mode");
            return false;
        }
        Set<InspectionMetricTaskDb> metrics = new HashSet<>();
        List<Map<String, String>> redisInfoList = getClusterInfo(configDTO);
        String item = configDTO.getName();
        if (CollectionUtils.isEmpty(redisInfoList)) {
            saveFailedMetricTasks(subTaskId, metricTasks);
            return false;
        }
        InspectionMetricTaskDb agentLive = null;
        for (Map<String, String> redisInfo : redisInfoList) {
            agentLive = agentLive(subTaskId, redisInfo);
            if (agentLive.getLadder() != InspectLadderEnum.NORMAL.getValue()) {
                break;
            }
        }
        if (agentLive != null) {
            metrics.add(agentLive);
        }

        InspectionMetricTaskDb connectionIndex = null;
        for (Map<String, String> redisInfo : redisInfoList) {
            connectionIndex = connectedClients(subTaskId, redisInfo);
            if (connectionIndex.getLadder() != InspectLadderEnum.NORMAL.getValue()) {
                break;
            }
        }
        if (connectionIndex != null) {
            metrics.add(connectionIndex);
        }
        InspectionMetricTaskDb memoryIndex = null;
        for (Map<String, String> redisInfo : redisInfoList) {
            memoryIndex = memoryUsed(subTaskId, redisInfo);
            if (memoryIndex.getLadder() != InspectLadderEnum.NORMAL.getValue()) {
                break;
            }
        }
        if (memoryIndex != null) {
            metrics.add(memoryIndex);
        }
        InspectionMetricTaskDb aofIndex = null;
        for (Map<String, String> redisInfo : redisInfoList) {
            aofIndex = aof(subTaskId, redisInfo);
            if (aofIndex.getLadder() != InspectLadderEnum.NORMAL.getValue()) {
                break;
            }
        }
        if (aofIndex != null) {
            metrics.add(aofIndex);
        }
        InspectionMetricTaskDb qpsIndex = null;
        for (Map<String, String> redisInfo : redisInfoList) {
            qpsIndex = qps(subTaskId, redisInfo);
            if (qpsIndex.getLadder() != InspectLadderEnum.NORMAL.getValue()) {
                break;
            }
        }
        if (qpsIndex != null) {
            metrics.add(qpsIndex);
        }
        InspectionMetricTaskDb keySpaceIndex = null;
        for (Map<String, String> redisInfo : redisInfoList) {
            keySpaceIndex = keySpace(subTaskId, redisInfo);
            if (keySpaceIndex.getLadder() != InspectLadderEnum.NORMAL.getValue()) {
                break;
            }
        }
        if (keySpaceIndex != null) {
            metrics.add(keySpaceIndex);
        }
        metricTasks.addAll(metrics);
        return true;
    }

    private List<Map<String, String>> getClusterInfo(RedisConfigDTO configDTO) {
        List<Map<String, String>> jedisInfoList = new ArrayList<>();
        Set<HostAndPort> clusterNodes = new HashSet<>();
        String[] hostAndPortArray = configDTO.getHost().split(",");
        for (String hostAndPortStr : hostAndPortArray) {
            clusterNodes.add(HostAndPort.from(hostAndPortStr));
        }
        for (HostAndPort node : clusterNodes) {
            try (Jedis jedis = new Jedis(node.getHost(), node.getPort())) {
                jedis.auth(configDTO.getPwd());
                jedisInfoList.add(buildRedisInfoMap(jedis));
            }
        }
        return jedisInfoList;
    }

    private void inspectionMetric(String subTaskId, Map<String, String> redisInfo, List<InspectionMetricTaskDb> metricTasks) {
        metricTasks.add(agentLive(subTaskId, redisInfo));
        metricTasks.add(connectedClients(subTaskId, redisInfo));
        metricTasks.add(memoryUsed(subTaskId, redisInfo));
        metricTasks.add(aof(subTaskId, redisInfo));
        metricTasks.add(qps(subTaskId, redisInfo));
        metricTasks.add(keySpace(subTaskId, redisInfo));
    }

    private Map<String, String> getRedisInfo(RedisConfigDTO configBo) {
        String mode = configBo.getMode();
        String host = configBo.getHost();
        Integer port = configBo.getPort();
        if (Objects.isNull(mode) || MiddlewareConstants.REDIS_MODE_SINGLE.equals(mode)) {
            // 单机模式
            try (Jedis jedis = new Jedis(host, port)) {
                jedis.auth(redisUser, configBo.getPwd());
                return buildRedisInfoMap(jedis);
            }
        } else {
            // 哨兵模式
            String sentinelPwd = configBo.getRedisSentinelPasswd();
            String masterName = configBo.getRedisSentinelName();
            String username = configBo.getUser();
            String pwd = configBo.getPwd();
            JedisPoolConfig poolConfig = new JedisPoolConfig();
            poolConfig.setMaxWait(Duration.ofSeconds(1L));
            try (JedisSentinelPool pool = new JedisSentinelPool(masterName, new HashSet<>(Arrays.asList(host.split("[,;]"))),
                    poolConfig, Protocol.DEFAULT_TIMEOUT, Protocol.DEFAULT_TIMEOUT, username, pwd, Protocol.DEFAULT_DATABASE, null,
                    Protocol.DEFAULT_TIMEOUT, Protocol.DEFAULT_TIMEOUT, null, sentinelPwd, null);
                 Jedis jedis = pool.getResource()) {
                return buildRedisInfoMap(jedis);
            }
        }
    }


    /**
     * redis.info()
     * <p>
     * # Server
     * redis_version:6.0.16
     * redis_git_sha1:00000000
     * redis_git_dirty:0
     * <p>
     * # Memory
     * used_memory:450377360
     * used_memory_human:429.51M
     * used_memory_rss:649330688
     * used_memory_rss_human:619.25M
     * used_memory_peak:1838202392
     * used_memory_peak_human:1.71G
     * used_memory_peak_perc:24.50%
     * used_memory_overhead:76969608
     * used_memory_startup:1966944
     * <p>
     * # Keyspace
     * db0:keys=458274,expires=382762,avg_ttl=18603956947
     * db2:keys=91,expires=90,avg_ttl=39874950
     * db3:keys=424,expires=360,avg_ttl=1299966310
     * db4:keys=25802,expires=11174,avg_ttl=198581088
     * db7:keys=11,expires=0,avg_ttl=0
     * db8:keys=980,expires=649,avg_ttl=12050840
     * db10:keys=1740,expires=116,avg_ttl=169029474
     *
     * @param jedis .
     * @return .
     */
    private Map<String, String> buildRedisInfoMap(Jedis jedis) {
        // 获取 redis 的 info
        String info = jedis.info();
        if (StringUtils.isBlank(info)) {
            return null;
        }
        // 获取 info 信息
        Map<String, String> infoMap = new HashMap<>();
        // 结果按照换行符分割
        String[] lines = info.split("\r\n");
        for (String line : lines) {
            // 排除掉 # 开头的行
            if (StringUtils.isBlank(line) || line.startsWith("#")) {
                continue;
            }
            // 按照 : 分割
            String[] split = line.split(":");
            if (split.length == 2) {
                // 保存到 map 中
                infoMap.put(split[0], split[1]);
            }
        }
        // 获取 Redis 配置的最大连接数
        /*
            返回结构
                ["maxclients", "30000"]
         */
        List<String> list = jedis.configGet("maxclients");
        if (CollectionUtils.isNotEmpty(list) && list.size() == 2) {
            String maxClients = list.get(1);
            if (StringUtils.isNotBlank(maxClients) && !"nil".equals(maxClients)) {
                infoMap.put("maxclients", list.get(1));
            }
        }
        // 获取所有 DB key数量信息
        String keyspace = jedis.info("Keyspace");
        infoMap.put("keyspace", keyspace);
        return infoMap;
    }

    /**
     * 指标获取失败时，拼接所有指标获取失败的结果
     */
    private void saveFailedMetricTasks(String subTaskId, List<InspectionMetricTaskDb> metricTasks) {
        metricTasks.add(metricTaskDao.buildFailedMetricTaskDb(subTaskId, InspectionMetricKeyEnum.REDIS_AGENT_LIVE.getMetricKey(), "redis连接失败", 0));
        metricTasks.add(metricTaskDao.buildFailedMetricTaskDb(subTaskId, InspectionMetricKeyEnum.REDIS_CONNECTION_COUNT.getMetricKey(), "指标获取失败"));
        metricTasks.add(metricTaskDao.buildFailedMetricTaskDb(subTaskId, InspectionMetricKeyEnum.REDIS_ACTUAL_MEMORY_USAGE.getMetricKey(), "指标获取失败"));
        metricTasks.add(metricTaskDao.buildFailedMetricTaskDb(subTaskId, InspectionMetricKeyEnum.REDIS_AOF.getMetricKey(), "指标获取失败"));
        metricTasks.add(metricTaskDao.buildFailedMetricTaskDb(subTaskId, InspectionMetricKeyEnum.REDIS_QPS.getMetricKey(), "指标获取失败"));
        metricTasks.add(metricTaskDao.buildFailedMetricTaskDb(subTaskId, InspectionMetricKeyEnum.REDIS_KEYSPACE.getMetricKey(), "指标获取失败"));
    }

    public InspectionMetricTaskDb agentLive(String subTaskId, Map<String, String> infoMap) {
        long startTime = System.currentTimeMillis();
        String metricKey = InspectionMetricKeyEnum.REDIS_AGENT_LIVE.getMetricKey();
        if (infoMap == null || infoMap.isEmpty()) {
            return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, "连接redis失败", metricKey, HIGH_RISK, 0);
        }
        return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, "连接redis成功", metricKey, NORMAL, 0);
    }

    /**
     * 获取 redis 连接数
     *
     * @param subTaskId .
     * @param infoMap   redisInfo
     * @return metricTask
     */
    public InspectionMetricTaskDb connectedClients(String subTaskId, Map<String, String> infoMap) {
        long startTime = System.currentTimeMillis();
        String metricKey = InspectionMetricKeyEnum.REDIS_CONNECTION_COUNT.getMetricKey();
        String maxclients = infoMap.get("maxclients");
        String connectedClients = infoMap.get("connected_clients");

        if (StringUtils.isBlank(maxclients) || StringUtils.isBlank(connectedClients)) {
            // 指标获取失败
            return metricTaskDao.buildFailedMetricTaskDb(subTaskId, startTime, "指标获取失败", "指标获取失败", metricKey);
        }

        double maxClients = Double.parseDouble(maxclients);
        double connected = Double.parseDouble(connectedClients);

        double value = connected / maxClients;

        String result = "maxclinets=" + maxclients + "\nconnected_clients=" + connectedClients;
        if (value > 0.3) {
            return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, result, metricKey, HIGH_RISK);
        }
        return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, result, metricKey, NORMAL);
    }

    public InspectionMetricTaskDb memoryUsed(String subTaskId, Map<String, String> infoMap) {
        long startTime = System.currentTimeMillis();
        String metricKey = InspectionMetricKeyEnum.REDIS_ACTUAL_MEMORY_USAGE.getMetricKey();
        String usedMemory = infoMap.get("used_memory");
        String usedMemoryHuman = infoMap.get("used_memory_human");
        String usedMemoryRssHuman = infoMap.get("used_memory_rss_human");
        String usedMemoryPeakHuman = infoMap.get("used_memory_peak_human");
        String totalSystemMemoryHuman = infoMap.get("total_system_memory_human");

        if (StringUtils.isBlank(usedMemory)
                || StringUtils.isBlank(usedMemoryRssHuman)
                || StringUtils.isBlank(usedMemoryPeakHuman)
                || StringUtils.isBlank(totalSystemMemoryHuman)) {
            // 指标获取失败
            return metricTaskDao.buildFailedMetricTaskDb(subTaskId, startTime, "指标获取失败", "指标获取失败", metricKey);
        }

        double value = Double.parseDouble(usedMemory) / 1024 / 1024 / 1024;
        String result = "used_memory_human=" + usedMemoryHuman
                + "\nused_memory_rss_human=" + usedMemoryRssHuman
                + "\nused_memory_peak_human=" + usedMemoryPeakHuman
                + "\ntotal_system_memory_human=" + totalSystemMemoryHuman;
        // 大小超过6G
        if (value > 6) {
            return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, result, metricKey, HIGH_RISK);
        }
        return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, result, metricKey, NORMAL);
    }

    public InspectionMetricTaskDb aof(String subTaskId, Map<String, String> infoMap) {
        long startTime = System.currentTimeMillis();
        String metricKey = InspectionMetricKeyEnum.REDIS_AOF.getMetricKey();
        String aofCurrentSize = infoMap.get("aof_current_size");
        String aofBaseSize = infoMap.get("aof_base_size");
        String aofLastBgrewriteStatus = infoMap.get("aof_last_bgrewrite_status");
        String aofLastRewriteTimeSec = infoMap.get("aof_last_rewrite_time_sec");

        if (StringUtils.isBlank(aofCurrentSize)
                || StringUtils.isBlank(aofBaseSize)
                || StringUtils.isBlank(aofLastBgrewriteStatus)
                || StringUtils.isBlank(aofLastRewriteTimeSec)) {
            // 指标获取失败
            return metricTaskDao.buildFailedMetricTaskDb(subTaskId, startTime, "指标获取失败", "指标获取失败", metricKey);
        }

        double value = Double.parseDouble(aofCurrentSize) / 1024 / 1024 / 1024;
        String result = "aof_current_size=" + getFormatSize(Double.parseDouble(aofCurrentSize))
                + "\naof_base_size=" + getFormatSize(Double.parseDouble(aofBaseSize))
                + "\naof_last_bgrewrite_status=" + aofLastBgrewriteStatus
                + "\naof_last_rewrite_time_sec=" + aofLastRewriteTimeSec;
        if (value > 10) {
            return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, result, metricKey, HIGH_RISK);
        }
        return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, result, metricKey, NORMAL);
    }

    public InspectionMetricTaskDb qps(String subTaskId, Map<String, String> infoMap) {
        long startTime = System.currentTimeMillis();
        String metricKey = InspectionMetricKeyEnum.REDIS_QPS.getMetricKey();
        String instantaneousOpsPerSec = infoMap.get("instantaneous_ops_per_sec");

        if (StringUtils.isBlank(instantaneousOpsPerSec)) {
            // 指标获取失败
            return metricTaskDao.buildFailedMetricTaskDb(subTaskId, startTime, "指标获取失败", "指标获取失败", metricKey);
        }

        long value = Integer.parseInt(instantaneousOpsPerSec);
        String result = "instantaneous_ops_per_sec=" + instantaneousOpsPerSec;
        if (value > 10000) {
            return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, result, metricKey, HIGH_RISK);
        }
        return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, result, metricKey, NORMAL);
    }

    public InspectionMetricTaskDb keySpace(String subTaskId, Map<String, String> infoMap) {
        long startTime = System.currentTimeMillis();
        String metricKey = InspectionMetricKeyEnum.REDIS_KEYSPACE.getMetricKey();
        String result = infoMap.get("keyspace");

        if (StringUtils.isBlank(result)) {
            // 指标获取失败
            return metricTaskDao.buildFailedMetricTaskDb(subTaskId, startTime, "指标获取失败", "指标获取失败", metricKey);
        }

        return metricTaskDao.buildMetricTaskDb(subTaskId, startTime, result, metricKey, NORMAL);
    }

    public static String getFormatSize(double size) {
        double kiloByte = size / 1024;
        if (kiloByte < 1) {
            return size + "Byte(s)";
        }

        double megaByte = kiloByte / 1024;
        if (megaByte < 1) {
            BigDecimal result1 = new BigDecimal(Double.toString(kiloByte));
            return result1.setScale(2, RoundingMode.HALF_UP).toPlainString() + "KB";
        }

        double gigaByte = megaByte / 1024;
        if (gigaByte < 1) {
            BigDecimal result2 = new BigDecimal(Double.toString(megaByte));
            return result2.setScale(2, RoundingMode.HALF_UP).toPlainString() + "MB";
        }

        double teraBytes = gigaByte / 1024;
        if (teraBytes < 1) {
            BigDecimal result3 = new BigDecimal(Double.toString(gigaByte));
            return result3.setScale(2, RoundingMode.HALF_UP).toPlainString() + "GB";
        }
        BigDecimal result4 = new BigDecimal(teraBytes);
        return result4.setScale(2, RoundingMode.HALF_UP).toPlainString() + "TB";
    }
}
