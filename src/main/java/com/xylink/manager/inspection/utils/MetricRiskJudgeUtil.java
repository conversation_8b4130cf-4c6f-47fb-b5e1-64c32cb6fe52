package com.xylink.manager.inspection.utils;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/21 16:42
 */
public class MetricRiskJudgeUtil {
    /**
     * 判断巡检结果是否达到阈值
     *
     * @param v   巡检结果
     * @param op  操作符
     * @param t   阈值
     * @param <E> 判断类型
     * @return 是否达到阈值
     */
    public static <E> boolean compare(Comparable<E> v, String op, E t) {
        boolean judgement = "=".equals(op) && v.equals(t);
        judgement = judgement || "!=".equals(op) && !v.equals(t);
        judgement = judgement || ">=".equals(op) && v.compareTo(t) >= 0;
        judgement = judgement || ">".equals(op) && v.compareTo(t) > 0;
        judgement = judgement || "<=".equals(op) && (v).compareTo(t) <= 0;
        judgement = judgement || "<".equals(op) && (v).compareTo(t) < 0;
        return judgement;
    }
}
