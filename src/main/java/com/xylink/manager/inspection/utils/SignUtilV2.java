package com.xylink.manager.inspection.utils;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/20 15:00
 */
public class SignUtilV2 {

    private static final Logger logger = LoggerFactory.getLogger(SignUtilV2.class);

    // 应用在网关平台注册的clientId
    public static String clientid = "5HtoW7nFaKRpKpxKRVLSPMx5";
    // 应用在网关平台注册的clientSecret
    public static String clientSecret = "ZVLw0TD2a53TavzwjgMRCySGqNwrQTM8";

    public static String clientType = "inspection";

    public static String signatureVersion = "2.0";


    /**
     * 签名所需header的key
     */

    public static final String SIGN_CLIENT_ID_KEY = "x-xy-clientid";

    public static final String SIGN_NONCE_KEY = "x-xy-nonce";

    public static final String SIGN_TIMESTAMP_KEY = "x-xy-timestamp";

    public static final String SIGN_SIGN_TYPE_KEY = "x-xy-signtype";

    public static final String SIGN_SIGNATURE_VERSION_KEY = "x-xy-signatureversion";

    public static final String SIGN_CLIENT_TYPE_KEY = "x-xy-clienttype";

    public static final String SIGN_SIGN_KEY = "x-xy-sign";

//    @Test
//    public void  testInspectionApi(){
//        Map<String, String> headers = getGatewatSignHeaders("GET", "/api/rest/internal/inspection/v1", "");
//        headers.entrySet().forEach(x -> {
//            System.out.println(x.getKey() + ":" + x.getValue());
//        });
//    }

    /**
     * 获得签名所需的header项
     * @param method 方法类型 eg: GET
     * @param uri 接口地址 eg: /api/rest/a/b/c?k1=v1&k2=v2
     * @param requestBody 请求体
     * @return
     */
    public static Map<String, String> getGatewatSignHeaders(String method, String uri, String requestBody) {
        Map<String, String> headersMap = new HashMap<>(16);
        headersMap.put(SIGN_CLIENT_ID_KEY, clientid);
        headersMap.put(SIGN_NONCE_KEY, RandomStringUtils.random(60, true, false));
        headersMap.put(SIGN_TIMESTAMP_KEY, String.valueOf(System.currentTimeMillis()));
        headersMap.put(SIGN_SIGN_TYPE_KEY, SignType.HMAC_SHA256.name());
        headersMap.put(SIGN_SIGNATURE_VERSION_KEY, signatureVersion);
        headersMap.put(SIGN_CLIENT_TYPE_KEY, clientType);
        byte[] requestBodyBytes = requestBody.getBytes();
        // 加密密钥
        String encryptionKey = clientSecret + "&";
        // 签名计算
        String sign = SignUtilV2.getSignV2(
                method,
                headersMap,
                uri,
                requestBodyBytes,
                encryptionKey);
        logger.info("最终签名: {}", sign);
        headersMap.put(SIGN_SIGN_KEY, sign);
        logger.info("最终header: {}", headersMap);
        return headersMap;
    }

    /**
     *计算签名
     * @param method
     * @param headersMap
     * @param uri
     * @param requestBodyBytes
     *@param encryptionKey 加密密钥
     * @return
     */
    private static String getSignV2(String method, Map<String, String> headersMap, String uri, byte[] requestBodyBytes, String encryptionKey) {
        if (headersMap == null) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        //1.添加method
        sb.append(method).append("\n");
        //header排序
        Set<String> keySet = headersMap.keySet();
        String[] keyArray = keySet.toArray(new String[keySet.size()]);
        Arrays.sort(keyArray);
        //2.添加Headers
        for (String k : keyArray) {
            if (k.equals(SIGN_SIGN_KEY)) {
                continue;
            }
            if (k.equals(SIGN_SIGNATURE_VERSION_KEY)) {
                continue;
            }
            if (k.equals(SIGN_CLIENT_TYPE_KEY)) {
                continue;
            }
            Object param = headersMap.get(k);
            String paramStr = String.valueOf(param);
            if (paramStr.trim().length() > 0) {
                // 参数值为空，则不参与签名
                sb.append(k).append("=").append(paramStr.trim()).append("&");
            }
        }
        sb = sb.deleteCharAt(sb.length()-1);//去除最后一个&
        sb.append("\n");
        //3.添加uri
        sb.append(uri);
        //4.添加请求的body
        sb.append("\n");
        if(requestBodyBytes != null && requestBodyBytes.length > 0) {
            sb.append(DigestUtils.md5Hex(requestBodyBytes));
        }else{
            sb.append(DigestUtils.md5Hex("".getBytes(Charset.forName("UTF-8"))));
        }
        //5.添加密钥（signSecret + "&"）
        sb.append("\n");
        sb.append(encryptionKey);
        logger.info("======================sign string=============================");
        logger.info(sb.toString());
        logger.info("======================sign string=============================");
        String signStr = "";
        //加密
        SignType type = null;
        String signType = headersMap.get(SIGN_SIGN_TYPE_KEY);
        if (signType != null && signType.length() > 0) {
            type = SignType.valueOf(signType);
        }
        if (type == null) {
            type = SignType.MD5;
        }
        switch (type) {
            case MD5:
                signStr = DigestUtils.md5Hex(sb.toString()).toUpperCase();
                break;
            case SHA256:
                signStr = DigestUtils.sha256Hex(sb.toString()).toUpperCase();
                break;
            case HMAC_SHA256:
                signStr = sha256HMAC(sb.toString(),encryptionKey).toUpperCase();
                break;
            default:
                break;
        }
        return signStr;
    }

    /**
     * sha256_HMAC加密
     * @param message 消息
     * @param secret  秘钥
     * @return 加密后字符串
     */
    public static String sha256HMAC(String message, String secret) {
        String hash = "";
        try {
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(secret.getBytes("UTF-8"), "HmacSHA256");
            sha256_HMAC.init(secret_key);
            byte[] bytes = sha256_HMAC.doFinal(message.getBytes("UTF-8"));
            hash = byteArrayToHexString(bytes);
        } catch (Exception e) {
        }
        return hash;
    }
    /**
     * 将加密后的字节数组转换成字符串
     *
     * @param b 字节数组
     * @return 字符串
     */
    public  static String byteArrayToHexString(byte[] b) {
        StringBuilder hs = new StringBuilder();
        String stmp;
        for (int n = 0; b!=null && n < b.length; n++) {
            stmp = Integer.toHexString(b[n] & 0XFF);
            if (stmp.length() == 1) {
                hs.append('0');
            }
            hs.append(stmp);
        }
        return hs.toString().toLowerCase();
    }

    public enum SignType {
        MD5,
        SHA256,
        HMAC_SHA256;

        public static boolean contains(String type) {
            for (SignType typeEnum : SignType.values()) {
                if (typeEnum.name().equals(type)) {
                    return true;
                }
            }
            return false;
        }
    }
}
