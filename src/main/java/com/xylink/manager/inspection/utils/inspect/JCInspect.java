package com.xylink.manager.inspection.utils.inspect;

import com.xylink.manager.inspection.dao.InspectionMetricTaskDao;
import com.xylink.manager.inspection.dao.InspectionSubTaskDao;
import com.xylink.manager.inspection.entity.db.InspectionMetricTaskDb;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.db.JasyptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/20
 */
@Service("JC_Inspect")
@Slf4j
public class JCInspect extends DbInspect {

    public JCInspect(InspectionSubTaskDao subTaskDao, InspectionMetricTaskDao metricTaskDao,
                     JasyptService jasyptService, K8sService k8sService) {
        super(subTaskDao, metricTaskDao, jasyptService, k8sService);
    }

    @Override
    void saveFailedMetricTasks(String subTaskId, List<InspectionMetricTaskDb> metricTasks) {

    }

    @Override
    void inspectionMetric(String subTaskId, Connection connection, List<InspectionMetricTaskDb> metricTasks) {

    }
}
