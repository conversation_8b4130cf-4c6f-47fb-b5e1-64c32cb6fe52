package com.xylink.manager.inspection.utils.inspect;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xylink.manager.inspection.dao.InspectionMetricTaskDao;
import com.xylink.manager.inspection.dao.InspectionSubTaskDao;
import com.xylink.manager.inspection.entity.bo.InspectionBigDataTaskDataBo;
import com.xylink.manager.inspection.entity.bo.InspectionTaskDataBo;
import com.xylink.manager.inspection.entity.db.InspectionItemConfigDb;
import com.xylink.manager.inspection.entity.db.InspectionMetricTaskDb;
import com.xylink.manager.inspection.entity.db.InspectionSubTaskDb;
import com.xylink.manager.inspection.entity.enums.InspectBigdataLadderEnum;
import com.xylink.manager.inspection.entity.enums.InspectionItemTypeEnum;
import com.xylink.manager.inspection.entity.enums.InspectionMetricKeyEnum;
import com.xylink.manager.inspection.entity.enums.InspectionThresholdLadderEnum;
import com.xylink.manager.inspection.entity.model.InspectionResult;
import com.xylink.manager.inspection.utils.JsonUtils;
import com.xylink.manager.service.ServerListService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.base.SystemModeConfig;
import com.xylink.util.Ipv6Util;
import com.xylink.util.UUIDGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/7 19:43
 */
@Service
@Slf4j
public class BigdataInspector {


    private static final String DCM_INSPECT_URL = "/api/rest/dcm/internal/inspection/v1";
    private static final String DCM_INSPECT_LOG_URL = "/api/rest/dcm/internal/inspection/log/v1";

    @Autowired
    private InspectionMetricTaskDao metricTaskDao;
    @Autowired
    private InspectionSubTaskDao subTaskDao;
    @Autowired
    private ServerListService serverListService;
    @Autowired
    @Qualifier("inspectionRestTemplate")
    private RestTemplate restTemplate;
    @Autowired
    private K8sService k8sService;


    public static ExecutorService executor = new ThreadPoolExecutor(4, 5,
            30L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(100),
            new ThreadFactoryBuilder().setNameFormat("Inspecting_Service-%d").setDaemon(true).build(),
            new ThreadPoolExecutor.AbortPolicy());

    public List<InspectionSubTaskDb> inspect(String taskId, InspectionItemConfigDb itemConfig) {
        List<InspectionSubTaskDb> subTaskDbs = new ArrayList<>();
        List<String> modules = getBigDataInspectModules();
        List<CompletableFuture<InspectionBigDataTaskDataBo>> futureList = new ArrayList<>(modules.size());
        modules.forEach(module -> {
            CompletableFuture<InspectionBigDataTaskDataBo> future = CompletableFuture.supplyAsync(
                    () -> doInspect(taskId, module),
                    executor
            );
            futureList.add(future);
        });

        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));

        CompletableFuture<List<InspectionBigDataTaskDataBo>> finalResults = allFutures.thenApply(v ->
                futureList.stream().map(CompletableFuture::join).collect(Collectors.toList())
        );
        List<InspectionBigDataTaskDataBo> inspectionResult = finalResults.join();

        for (InspectionBigDataTaskDataBo bigDataTaskData : inspectionResult) {
            subTaskDbs.add(transferSubTask(taskId, bigDataTaskData));
        }
        subTaskDao.saveSubTasks(subTaskDbs);
        return subTaskDbs;
    }

    /**
     * 查询大数据巡检项
     * @return
     */
    private List<String> getBigDataInspectModules() {
        //Arrays.asList("hdfs", "kafka", "hbase", "zookeeper", "phoenix", "yarn");
        boolean yarnDeployed = true;
        boolean hbaseDeployed = true;
        if (SystemModeConfig.isCmsOrXms()) {
            yarnDeployed = k8sService.isConfigDeployed("yarn");
            hbaseDeployed = k8sService.isConfigDeployed("hbase");
        }

        List<String> modules = new ArrayList<>();
        modules.add("kafka");
        modules.add("zookeeper");
        modules.add("phoenix");
        if (yarnDeployed) {
            modules.add("hdfs");
            modules.add("yarn");
        }
        if (hbaseDeployed) {
            modules.add("hbase");
        }
        return modules;
    }

    private InspectionSubTaskDb transferSubTask(String taskId,
                                                InspectionBigDataTaskDataBo bigDataTaskData) {
        log.info("[inspection] bigdata task data: {}", JsonUtils.objectToJson(bigDataTaskData));
        InspectionSubTaskDb subTask = new InspectionSubTaskDb();
        String subTaskId = UUIDGenerator.generate();
        subTask.setId(subTaskId);
        subTask.setTaskId(taskId);
        subTask.setItemName(bigDataTaskData.getModule());
        if (bigDataTaskData.getModule().equals("kafka")) {
            subTask.setItemName("bigdata-kafka");
        }
        if (bigDataTaskData.getModule().equals("zookeeper")) {
            subTask.setItemName("bigdata-zookeeper");
        }
        long startTime = bigDataTaskData.getStartTime();
        subTask.setCreateTime(startTime);
        subTask.setTaskType(InspectionItemTypeEnum.MIDDLEWARE.getId());

        int highRisk = 0;
        int middleRisk = 0;
        int lowRisk = 0;
        String exceptionDesc = null;

        List<InspectionMetricTaskDb> metricTaskDbList = new ArrayList<>();

        List<InspectionTaskDataBo> resultList = bigDataTaskData.getResult();
        long endTime = bigDataTaskData.getEndTime();
        for (InspectionTaskDataBo result : resultList) {
            int ladder = result.getLadder();
            InspectionMetricTaskDb metricTaskDb = new InspectionMetricTaskDb();
            metricTaskDb.setSubTaskId(subTaskId);
            metricTaskDb.setId(UUIDGenerator.generate());
            metricTaskDb.setLadder(ladder);
            metricTaskDb.setMetricResult(result.getTaskValue() + "，采集时间: " + result.getAcquisitionTime());
            metricTaskDb.setMetricKey(result.getTaskIndex());
            metricTaskDb.setFinishedTime(endTime);
            metricTaskDb.setCreateTime(startTime);

            if (ladder == InspectionThresholdLadderEnum.HIGH_RISK.getLadder()) {
                highRisk++;
            }
            if (ladder == InspectionThresholdLadderEnum.MIDDLE_RISK.getLadder()) {
                middleRisk++;
            }
            if (ladder == InspectionThresholdLadderEnum.LOW_RISK.getLadder()) {
                lowRisk++;
            }
            metricTaskDbList.add(metricTaskDb);
        }

        // result 保存到 metric task 表中
        metricTaskDao.saveMetricTasks(metricTaskDbList);

        subTask.setHighRisk(highRisk);
        subTask.setMiddleRisk(middleRisk);
        subTask.setLowRisk(lowRisk);
        subTask.setFinishedTime(endTime);
        subTask.setExceptionDesc(exceptionDesc);
        return subTask;
    }

    private InspectionBigDataTaskDataBo doInspect(String instanceId, String module) {
        String mainIp = serverListService.getMainNodeInternalIP();
        String url = String.format("http://%s:%d%s?modules=%s", Ipv6Util.handlerIpv6Addr(mainIp), 11111, DCM_INSPECT_URL, module);
        ResponseEntity<InspectionResult[]> responseEntity;
        long startTime = System.currentTimeMillis();
        try {
            responseEntity = restTemplate.getForEntity(url, InspectionResult[].class);
            InspectionResult result = Objects.requireNonNull(responseEntity.getBody())[0];

            //取巡检日志
            Long inspectionId = result.getInspectionId();
            String url2 = String.format("http://%s:%d%s?inspectionIds=%d", Ipv6Util.handlerIpv6Addr(mainIp), 11111, DCM_INSPECT_LOG_URL, inspectionId);
            ResponseEntity<String> responseEntity2;
            try {
                responseEntity2 = restTemplate.getForEntity(url2, String.class);
                List<InspectionTaskDataBo> inspectionTaskDataBos = parseDcmResponseBody(instanceId, responseEntity2.getBody());
                return buildBigDataTaskData(module, startTime, inspectionTaskDataBos);
            } catch (Exception e) {
                log.error("[inspection] Get url2 '{}' error. ", url2, e);
                InspectionTaskDataBo inspectionTaskDataBo = dealGetUrlError(instanceId, url2, module, e.getMessage());
                return buildBigDataTaskData(module, startTime, Collections.singletonList(inspectionTaskDataBo));
            }

        } catch (Exception e) {
            log.error("[inspection] Get url '{}' error. ", url, e);
            InspectionTaskDataBo inspectionTaskDataBo = dealGetUrlError(instanceId, url, module, e.getMessage());
            return buildBigDataTaskData(module, startTime, Collections.singletonList(inspectionTaskDataBo));
        }
    }

    private static InspectionBigDataTaskDataBo buildBigDataTaskData(String module, long startTime, List<InspectionTaskDataBo> inspectionTaskDataBos) {
        InspectionBigDataTaskDataBo bigDataTaskDataBo = new InspectionBigDataTaskDataBo();
        bigDataTaskDataBo.setStartTime(startTime);
        bigDataTaskDataBo.setEndTime(System.currentTimeMillis());
        bigDataTaskDataBo.setModule(module);
        bigDataTaskDataBo.setResult(inspectionTaskDataBos);
        return bigDataTaskDataBo;
    }

    private InspectionTaskDataBo dealGetUrlError(String instanceId, String url, String module, String response) {
        InspectionTaskDataBo inspectionTaskDataBo = new InspectionTaskDataBo();
        inspectionTaskDataBo.setInstanceId(instanceId);
        inspectionTaskDataBo.setInspectionName(module);
        inspectionTaskDataBo.setTaskItem("");
        inspectionTaskDataBo.setTaskIndex(InspectionMetricKeyEnum.BIG_DATA_INSPECTION.getMetricKey());
        inspectionTaskDataBo.setTaskValue(String.format("Get url '%s' error. response : %s", url, response));
        inspectionTaskDataBo.setThresholdValue("");
        inspectionTaskDataBo.setLadder(3);
        return inspectionTaskDataBo;
    }

    /**
     * dcm 放回结构
     * {
     * "hdfs": {
     * "hdfs功能检查": [
     * {
     * "id": 1,
     * "metricName": "hdfs读取文件耗时",
     * "metricCalc": "14.0",
     * "riskJudgment": ">1000",
     * "result": "正常",
     * "metricValue": "14毫秒",
     * "acquisitionTime": "2023-08-08 18:36:52",
     * "unit": "毫秒"
     * }
     * ],
     * "hdfs健康检查": [
     * {
     * "id": 13,
     * "metricName": "HDFS DataNode不健康数量",
     * "metricCalc": "0.0+0.0",
     * "riskJudgment": ">0",
     * "result": "正常",
     * "metricValue": "0个",
     * "acquisitionTime": "1970-01-20 21:51:30",
     * "unit": "个"
     * }
     * ]
     * }
     * }
     *
     * @param instanceId 巡检id
     * @param response   dcm 结果
     * @return task
     */
    private List<InspectionTaskDataBo> parseDcmResponseBody(String instanceId, String response) {
        try {
            List<InspectionTaskDataBo> inspectionTaskDataList = new ArrayList<>();
            ObjectMapper mapper = JsonUtils.getMapper();
            JsonNode jsonNode = mapper.readTree(response);
            jsonNode.fields().forEachRemaining(moduleEntry -> {
                String module = moduleEntry.getKey();
                JsonNode moduleValue = moduleEntry.getValue();
                moduleValue.fields().forEachRemaining(metricEntry -> {
                    String metric = metricEntry.getKey();
                    JsonNode metricValue = metricEntry.getValue();
                    metricValue.forEach(task -> {
                        InspectionTaskDataBo inspectionTaskDataBo = new InspectionTaskDataBo();
                        inspectionTaskDataBo.setInstanceId(instanceId);
                        inspectionTaskDataBo.setInspectionName(module);
                        inspectionTaskDataBo.setTaskItem(metric);
                        inspectionTaskDataBo.setTaskIndex(task.get("metricName").asText());
                        inspectionTaskDataBo.setTaskValue(task.get("metricValue").asText());
                        inspectionTaskDataBo.setThresholdValue(task.get("riskJudgment").asText());
                        String result = task.get("result").asText();
                        if (InspectBigdataLadderEnum.NORMAL.getType().equals(result)) {
                            inspectionTaskDataBo.setLadder(InspectionThresholdLadderEnum.NORMAL.getLadder());
                        } else if (InspectBigdataLadderEnum.RISK.getType().equals(result)) {
                            inspectionTaskDataBo.setLadder(InspectionThresholdLadderEnum.MIDDLE_RISK.getLadder());
                        } else {
                            inspectionTaskDataBo.setLadder(InspectionThresholdLadderEnum.HIGH_RISK.getLadder());
                        }
                        inspectionTaskDataBo.setAcquisitionTime(task.get("acquisitionTime").asText());
                        inspectionTaskDataList.add(inspectionTaskDataBo);
                    });
                });
            });

            return inspectionTaskDataList;
        } catch (Exception e) {
            log.error("parseDcmResponseBody invoke failed. instanceId={}, response={}", instanceId, response, e);
            InspectionTaskDataBo inspectionTaskDataBo = new InspectionTaskDataBo();
            inspectionTaskDataBo.setInstanceId(instanceId);
            inspectionTaskDataBo.setInspectionName("");
            inspectionTaskDataBo.setTaskItem("");
            inspectionTaskDataBo.setTaskIndex("组件检查");
            inspectionTaskDataBo.setTaskValue("");
            inspectionTaskDataBo.setLadder(InspectBigdataLadderEnum.EXCEPT.getValue());
            inspectionTaskDataBo.setAcquisitionTime("");
            return Collections.singletonList(inspectionTaskDataBo);
        }
    }

}
