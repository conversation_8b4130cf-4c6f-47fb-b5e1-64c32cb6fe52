package com.xylink.manager.inspection.common.monitor;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MonitorIndicatorDto {

    private Long start;

    private Long end;

    private List<String> endpoints;

    private List<String> nids;

    private List<String> metrics;

    private String aggrFunc;

    private String consolFuc;

    private List<String> groupKey;

    private List<Long> comparisons;

    private String mode;

    private String metric;

     private List<TagKv> tagkv;

    // private List<String> tags;


    // private String dstype;

    private Long step;

    private String query;


}
