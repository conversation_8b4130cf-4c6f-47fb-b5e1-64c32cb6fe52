package com.xylink.manager.inspection.common.monitor;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 机器相关指标tag
 *
 * <AUTHOR>
 * @date 2021/11/26 15:22
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MonitorMetricsDto {

    /**
     * 与设备相关
     */
    private List<String> endpoints;

    /**
     * 指标列表
     */
    private List<String> metrics;

    /**
     * 与设备无关
     */
    private List<String> nids;

    /**
     * 开始时间
     */
    private Long start;

    /**
     * 结束时间
     */
    private Long end;

    /**
     * 指标名称
     */
    private String metric;

    private List<TagKv> tagkv;
}
