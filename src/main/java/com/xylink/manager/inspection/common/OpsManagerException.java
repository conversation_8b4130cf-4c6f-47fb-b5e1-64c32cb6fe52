package com.xylink.manager.inspection.common;

import lombok.Getter;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR> create on 2022/9/15
 */
@Getter
public class OpsManagerException extends RuntimeException {

    /**
     * 响应状态码
     */
    private HttpStatus httpStatus;

    /**
     * 异常code
     */
    private Integer errorCode;

    /**
     * 错误信息（研发使用）
     */
    private String developerMessage;

    /**
     * 错误信息（异常时用户弹窗提示内容）
     */
    private String userMessage;

    /**
     * 额外信息
     */
    private Object moreInfo;

    /**
     * 错误模块
     */
    private String errorCodeModel;

    public OpsManagerException(int errorCode, String userMessage) {
        this(HttpStatus.BAD_REQUEST, errorCode, userMessage.toString());
    }


    public static OpsManagerException paramError() {
        return new OpsManagerException(HttpStatus.BAD_REQUEST, "参数错误");
    }

    public static OpsManagerException serviceError() {
        return new OpsManagerException(HttpStatus.INTERNAL_SERVER_ERROR, "系统错误");
    }

    public OpsManagerException(String userMessage) {
        this(HttpStatus.BAD_REQUEST, userMessage);
    }

    public OpsManagerException(String userMessage, String developerMessage) {
        this(HttpStatus.BAD_REQUEST, userMessage, developerMessage);
    }

    public OpsManagerException(HttpStatus httpStatus, String userMessage) {
        this.errorCode = 0;
        this.httpStatus = httpStatus;
        this.userMessage = userMessage;
    }


    public OpsManagerException(HttpStatus httpStatus, String userMessage, String developerMessage) {
        this.errorCode = 0;
        this.httpStatus = httpStatus;
        this.userMessage = userMessage;
        this.developerMessage = developerMessage;
    }

    public OpsManagerException(HttpStatus httpStatus, int errorCode) {
        this.errorCode = errorCode;
        this.httpStatus = httpStatus;
    }

    public OpsManagerException(HttpStatus httpStatus, int errorCode, String userMessage) {
        this.errorCode = errorCode;
        this.httpStatus = httpStatus;
        this.userMessage = userMessage;
    }

    public OpsManagerException(HttpStatus httpStatus, int errorCode, String userMessage, String developerMessage) {
        this.errorCode = errorCode;
        this.httpStatus = httpStatus;
        this.userMessage = userMessage;
        this.developerMessage = developerMessage;
    }

    public OpsManagerException(HttpStatus httpStatus, Integer errorCode, String userMessage, String developerMessage, Object moreInfo, String errorCodeModel) {
        this.errorCode = errorCode;
        this.httpStatus = httpStatus;
        this.userMessage = userMessage;
        this.developerMessage = developerMessage;
        this.moreInfo = moreInfo;
        this.errorCodeModel = errorCodeModel;
    }

}
