package com.xylink.manager.inspection.common.monitor;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/26 15:37
 */
@Builder
@Data
public class MonitorData {
    private List<String> endpoints;

    /**
     * 设备无关，与endpoints二选一
     */
    private List<String> nids;

    /**
     * 指标名称
     */
    private String metric;

    /**
     * 指标列表
     */
    private List<String> metrics;

    private List<String> tags;

    /**
     * 指标周期
     */
    private long step;

    /**
     * 默认GAUGE
     */
    private String dstype;

    private long count;

    /**
     * 开始时间
     */
    private long start;

    /**
     * 结束时间
     */
    private long end;

    /**
     * 聚合函数，avg:均值，sum:求和，max:最大值，min:最小值
     */

    private String aggrFunc;

    private String consolFuc;

    /**
     * 聚合维度
     */
    private List<String> groupKey;

    /**
     * 环比
     */
    private List<Long> comparisons;

    /**
     * 查询方式 prometheus/nightingale，默认nightingale
     */
    private String mode;
}
