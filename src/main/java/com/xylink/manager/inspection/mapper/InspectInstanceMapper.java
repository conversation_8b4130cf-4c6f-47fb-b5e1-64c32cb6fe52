package com.xylink.manager.inspection.mapper;

import com.xylink.manager.inspection.entity.db.InspectInstanceDb;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/28 14:23
 */
@Mapper
public interface InspectInstanceMapper {

    Long countByJobStatus(@Param("status") int status);

    void insert(@Param("instance") InspectInstanceDb instance);

    void updateRisk(@Param("id") String id,
                    @Param("step") int step,
                    @Param("highRisk") int highRisk,
                    @Param("middleRisk") int middleRisk,
                    @Param("lowRisk") int lowRisk);

    void updateStatus(@Param("id") String id,
                      @Param("startTime") long startTime,
                      @Param("finishTime") long finishTime,
                      @Param("consumedTime") long consumedTime,
                      @Param("status") int status);

    Long count(@Param("startTime") Long startTime,
               @Param("endTime") Long endTime);

    List<InspectInstanceDb> list(@Param("size") int size,
                                 @Param("page") int page,
                                 @Param("startTime") Long startTime,
                                 @Param("endTime") Long endTime,
                                 @Param("asc") Boolean asc);

    InspectInstanceDb selectById(@Param("id") String id);

    void updateStep(@Param("id") String id,@Param("step") int step);

    InspectInstanceDb selectByJobStatus(@Param("status") int status);

    void updateFailed(@Param("instanceId") String instanceId);

    InspectInstanceDb getLatestRecord();
}
