package com.xylink.manager.inspection.mapper;

import com.xylink.manager.inspection.entity.db.InspectionTemplateDb;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/28 14:22
 */
@Mapper
public interface InspectionTemplateMapper {
    void insert(@Param("template") InspectionTemplateDb template);
    void updateStatus(@Param("id") String id, @Param("status") int status);

    Long count(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    List<InspectionTemplateDb> list(@Param("size") int size,
                                    @Param("page") int page,
                                    @Param("startTime") Long startTime,
                                    @Param("endTime") Long endTime,
                                    @Param("asc") Boolean asc);

    InspectionTemplateDb selectById(@Param("id") String id);

    void update(InspectionTemplateDb template);

    void updateEnable(String id);

    String selectIdByName(@Param("name") String name);

    Long selectStatusById(@Param("id") String id);
}
