package com.xylink.manager.inspection.mapper.impl;

import com.xylink.manager.inspection.config.DataSourceManager;
import com.xylink.manager.inspection.entity.db.InspectionServiceResultDB;
import com.xylink.manager.inspection.mapper.InspectionServiceListMapper;
import com.xylink.manager.inspection.mapper.InspectionServiceResultMapper;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/4
 */
@Service
public class InspectionServiceResultMapperImpl implements InspectionServiceResultMapper {

    @Autowired
    private DataSourceManager dataSourceManager;
    @Override
    public void save(List<InspectionServiceResultDB> list) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionServiceResultMapper mapper = session.getMapper(InspectionServiceResultMapper.class);
            mapper.save(list);
        }
    }

    @Override
    public List<InspectionServiceResultDB> getResultListByInspectionId(String inspectionId) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionServiceResultMapper mapper = session.getMapper(InspectionServiceResultMapper.class);
            return mapper.getResultListByInspectionId(inspectionId);
        }
    }

    @Override
    public void deleteByInspectionId(String inspectionId) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionServiceResultMapper mapper = session.getMapper(InspectionServiceResultMapper.class);
            mapper.deleteByInspectionId(inspectionId);
        }
    }
}
