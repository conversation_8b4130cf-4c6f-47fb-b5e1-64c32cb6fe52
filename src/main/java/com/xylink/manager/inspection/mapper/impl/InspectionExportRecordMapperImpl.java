package com.xylink.manager.inspection.mapper.impl;

import com.xylink.manager.inspection.config.DataSourceManager;
import com.xylink.manager.inspection.entity.db.InspectionExportRecordDb;
import com.xylink.manager.inspection.mapper.InspectInstanceMapper;
import com.xylink.manager.inspection.mapper.InspectionExportRecordMapper;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/4
 */
@Service
public class InspectionExportRecordMapperImpl implements InspectionExportRecordMapper {
    @Autowired
    private DataSourceManager dataSourceManager;
    @Override
    public InspectionExportRecordDb getRecordByInstanceId(String instanceId) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionExportRecordMapper mapper = session.getMapper(InspectionExportRecordMapper.class);
            return mapper.getRecordByInstanceId(instanceId);
        }
    }

    @Override
    public void insert(String instanceId, int state) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionExportRecordMapper mapper = session.getMapper(InspectionExportRecordMapper.class);
            mapper.insert(instanceId, state);
        }
    }

    @Override
    public void updateRecord(String instanceId, int state, String filePath) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionExportRecordMapper mapper = session.getMapper(InspectionExportRecordMapper.class);
            mapper.updateRecord(instanceId, state, filePath);
        }
    }

    @Override
    public void updateFailRecord(String instanceId, int state, String failDesc) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionExportRecordMapper mapper = session.getMapper(InspectionExportRecordMapper.class);
            mapper.updateFailRecord(instanceId, state, failDesc);
        }
    }

    @Override
    public List<InspectionExportRecordDb> getRecordByTime(String time) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionExportRecordMapper mapper = session.getMapper(InspectionExportRecordMapper.class);
            return mapper.getRecordByTime(time);
        }
    }
}
