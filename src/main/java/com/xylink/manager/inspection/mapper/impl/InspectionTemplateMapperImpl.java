package com.xylink.manager.inspection.mapper.impl;

import com.xylink.manager.inspection.config.DataSourceManager;
import com.xylink.manager.inspection.entity.db.InspectionTemplateDb;
import com.xylink.manager.inspection.mapper.InspectionTemplateMapper;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/4
 */
@Service
public class InspectionTemplateMapperImpl implements InspectionTemplateMapper {
    @Autowired
    private DataSourceManager dataSourceManager;
    @Override
    public void insert(InspectionTemplateDb template) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()){
            InspectionTemplateMapper mapper = session.getMapper(InspectionTemplateMapper.class);
            mapper.insert(template);
        }
    }

    @Override
    public void updateStatus(String id, int status) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()){
            InspectionTemplateMapper mapper = session.getMapper(InspectionTemplateMapper.class);
            mapper.updateStatus(id, status);
        }
    }

    @Override
    public Long count(Long startTime, Long endTime) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()){
            InspectionTemplateMapper mapper = session.getMapper(InspectionTemplateMapper.class);
            return mapper.count(startTime, endTime);
        }
    }

    @Override
    public List<InspectionTemplateDb> list(int size, int page, Long startTime, Long endTime, Boolean asc) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()){
            InspectionTemplateMapper mapper = session.getMapper(InspectionTemplateMapper.class);
            return mapper.list(size, page, startTime, endTime, asc);
        }
    }

    @Override
    public InspectionTemplateDb selectById(String id) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()){
            InspectionTemplateMapper mapper = session.getMapper(InspectionTemplateMapper.class);
            return mapper.selectById(id);
        }
    }

    @Override
    public void update(InspectionTemplateDb template) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()){
            InspectionTemplateMapper mapper = session.getMapper(InspectionTemplateMapper.class);
            mapper.update(template);
        }
    }

    @Override
    public void updateEnable(String id) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()){
            InspectionTemplateMapper mapper = session.getMapper(InspectionTemplateMapper.class);
            mapper.updateEnable(id);
        }
    }

    @Override
    public String selectIdByName(String name) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()){
            InspectionTemplateMapper mapper = session.getMapper(InspectionTemplateMapper.class);
            return mapper.selectIdByName(name);
        }
    }

    @Override
    public Long selectStatusById(String id) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()){
            InspectionTemplateMapper mapper = session.getMapper(InspectionTemplateMapper.class);
            return mapper.selectStatusById(id);
        }
    }
}
