package com.xylink.manager.inspection.mapper;

import com.xylink.manager.inspection.entity.db.InspectionServiceResultDB;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/11 11:34
 */
@Mapper
public interface InspectionServiceResultMapper {

    /**
     * 批量插入数据
     * @param list .
     */
    void save(List<InspectionServiceResultDB> list);

    List<InspectionServiceResultDB> getResultListByInspectionId(@Param("inspectionId")String inspectionId);

    void deleteByInspectionId(@Param("inspectionId") String inspectionId);
}
