package com.xylink.manager.inspection.mapper.impl;

import com.xylink.manager.inspection.config.DataSourceManager;
import com.xylink.manager.inspection.entity.db.InspectionTaskDb;
import com.xylink.manager.inspection.mapper.InspectionSubTaskMapper;
import com.xylink.manager.inspection.mapper.InspectionTaskMapper;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/4
 */
@Service
public class InspectionTaskMapperImpl implements InspectionTaskMapper {

    @Autowired
    private DataSourceManager dataSourceManager;

    @Override
    public void insertTask(InspectionTaskDb taskDb) {
       try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()){
           InspectionTaskMapper inspectionTaskMapper = session.getMapper(InspectionTaskMapper.class);
           inspectionTaskMapper.insertTask(taskDb);
       }
    }

    @Override
    public void updateTask(String id, long endTime, long highRisk, long middleRisk, long lowRisk) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()){
            InspectionTaskMapper inspectionTaskMapper = session.getMapper(InspectionTaskMapper.class);
            inspectionTaskMapper.updateTask(id, endTime, highRisk, middleRisk, lowRisk);
        }
    }

    @Override
    public void updateTaskExceptionDesc(String id, long endTime, String exceptionMessage) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()){
            InspectionTaskMapper inspectionTaskMapper = session.getMapper(InspectionTaskMapper.class);
            inspectionTaskMapper.updateTaskExceptionDesc(id, endTime, exceptionMessage);
        }
    }

    @Override
    public List<InspectionTaskDb> getTaskByInstanceId(String instanceId) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()){
            InspectionTaskMapper inspectionTaskMapper = session.getMapper(InspectionTaskMapper.class);
            return inspectionTaskMapper.getTaskByInstanceId(instanceId);
        }
    }

    @Override
    public List<String> getIdsByInstanceId(String instanceId) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()){
            InspectionTaskMapper inspectionTaskMapper = session.getMapper(InspectionTaskMapper.class);
            return inspectionTaskMapper.getIdsByInstanceId(instanceId);
        }
    }

    @Override
    public void deleteTaskByInstanceId(String instanceId) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()){
            InspectionTaskMapper inspectionTaskMapper = session.getMapper(InspectionTaskMapper.class);
            inspectionTaskMapper.deleteTaskByInstanceId(instanceId);
        }
    }

    @Override
    public InspectionTaskDb getTaskById(String id) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()){
            InspectionTaskMapper inspectionTaskMapper = session.getMapper(InspectionTaskMapper.class);
            return inspectionTaskMapper.getTaskById(id);
        }
    }
}
