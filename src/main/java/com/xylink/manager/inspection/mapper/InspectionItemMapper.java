package com.xylink.manager.inspection.mapper;

import com.xylink.manager.inspection.entity.db.InspectionItemConfigDb;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/17 14:10
 */

@Mapper
public interface InspectionItemMapper{
    /**
     * 根据巡检项名称获取巡检项信息
     *
     * @param ids 巡检项id
     * @return Items
     */
    List<InspectionItemConfigDb> getItemsByIds(@Param("ids") List<String> ids);

    List<InspectionItemConfigDb> getItems();

    InspectionItemConfigDb getItemById(@Param("itemId") String itemId);
}
