package com.xylink.manager.inspection.mapper;

import com.xylink.manager.inspection.entity.db.InspectionMetricConfigDb;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/17 14:10
 */
@Mapper
public interface InspectionMetricMapper {
    /**
     * 根据巡检项ID 查询巡检项所有指标
     *
     * @param itemId 巡检项 ID
     * @return 巡检项指标
     */
    List<InspectionMetricConfigDb> getInspectionMetricByItemId(@Param("itemId") String itemId);

    InspectionMetricConfigDb getInspectionMetricByKey(@Param("metricKey") String metricKey);

    InspectionMetricConfigDb getInspectionMetricByCode(@Param("metricCode") String metricCode);

    List<InspectionMetricConfigDb> getAll();

    String getSuggestByMetricKey(@Param("metricKey") String metricKey);

    List<InspectionMetricConfigDb> getSuggestByMetricKeys(List<String> metricKeys);
}
