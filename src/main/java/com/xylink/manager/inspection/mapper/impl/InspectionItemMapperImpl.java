package com.xylink.manager.inspection.mapper.impl;

import com.xylink.manager.inspection.config.DataSourceManager;
import com.xylink.manager.inspection.entity.db.InspectionItemConfigDb;
import com.xylink.manager.inspection.mapper.InspectionItemMapper;
import com.xylink.manager.inspection.mapper.InspectionTaskMapper;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/4
 */
@Service
public class InspectionItemMapperImpl implements InspectionItemMapper {
    @Autowired
    private DataSourceManager dataSourceManager;

    @Override
    public List<InspectionItemConfigDb> getItemsByIds(List<String> ids) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionItemMapper itemMapper = session.getMapper(InspectionItemMapper.class);
            return itemMapper.getItemsByIds(ids);
        }
    }

    @Override
    public List<InspectionItemConfigDb> getItems() {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionItemMapper itemMapper = session.getMapper(InspectionItemMapper.class);
            return itemMapper.getItems();
        }
    }

    @Override
    public InspectionItemConfigDb getItemById(String itemId) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionItemMapper itemMapper = session.getMapper(InspectionItemMapper.class);
            return itemMapper.getItemById(itemId);
        }
    }
}
