package com.xylink.manager.inspection.mapper;

import com.xylink.manager.inspection.entity.db.InspectionTemplateItemDb;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/28 14:23
 */
@Mapper
public interface InspectionTemplateItemMapper {
    void insert(@Param("mapDbs") List<InspectionTemplateItemDb> mapDbs);

    List<InspectionTemplateItemDb> selectByTemplateId(@Param("configId") String configId);

    void deleteByTemplateId(@Param("configId") String configId);
}
