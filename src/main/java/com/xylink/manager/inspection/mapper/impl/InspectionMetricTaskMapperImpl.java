package com.xylink.manager.inspection.mapper.impl;

import com.xylink.manager.inspection.config.DataSourceManager;
import com.xylink.manager.inspection.entity.db.InspectionMetricTaskDb;
import com.xylink.manager.inspection.mapper.InspectionMetricMapper;
import com.xylink.manager.inspection.mapper.InspectionMetricTaskMapper;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/4
 */
@Service
public class InspectionMetricTaskMapperImpl implements InspectionMetricTaskMapper {
    @Autowired
    private DataSourceManager dataSourceManager;
    @Override
    public void insertMetricTasks(List<InspectionMetricTaskDb> page) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionMetricTaskMapper mapper = session.getMapper(InspectionMetricTaskMapper.class);
            mapper.insertMetricTasks(page);
        }
    }

    @Override
    public List<InspectionMetricTaskDb> getMetricTaskBySubTaskId(String subTaskId, Integer size, Integer page, List<Integer> ladders) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionMetricTaskMapper mapper = session.getMapper(InspectionMetricTaskMapper.class);
            return mapper.getMetricTaskBySubTaskId(subTaskId, size, page, ladders);
        }
    }

    @Override
    public Long countBySubTaskId(String subTaskId, List<Integer> ladders) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionMetricTaskMapper mapper = session.getMapper(InspectionMetricTaskMapper.class);
            return mapper.countBySubTaskId(subTaskId, ladders);
        }
    }

    @Override
    public void deleteBySubTaskIds(List<String> subTaskIds) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionMetricTaskMapper mapper = session.getMapper(InspectionMetricTaskMapper.class);
            mapper.deleteBySubTaskIds(subTaskIds);
        }
    }

    @Override
    public List<InspectionMetricTaskDb> getMetricTaskBySubTaskIds(List<String> subTaskIds) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionMetricTaskMapper mapper = session.getMapper(InspectionMetricTaskMapper.class);
            return mapper.getMetricTaskBySubTaskIds(subTaskIds);
        }
    }

    @Override
    public void insertMetricTask(InspectionMetricTaskDb metricTask) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionMetricTaskMapper mapper = session.getMapper(InspectionMetricTaskMapper.class);
            mapper.insertMetricTask(metricTask);
        }
    }

    @Override
    public List<InspectionMetricTaskDb> getRiskMetricTasksBySubTaskId(String subTaskId) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionMetricTaskMapper mapper = session.getMapper(InspectionMetricTaskMapper.class);
            return mapper.getRiskMetricTasksBySubTaskId(subTaskId);
        }
    }

    @Override
    public List<InspectionMetricTaskDb> getMetricTasksBySubTaskId(String subTaskId) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionMetricTaskMapper mapper = session.getMapper(InspectionMetricTaskMapper.class);
            return mapper.getMetricTasksBySubTaskId(subTaskId);
        }
    }
}
