package com.xylink.manager.inspection.mapper;

import com.xylink.manager.inspection.entity.db.InspectionSchedulerTimeDb;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/18 11:36
 */
@Mapper
public interface InspectionSchedulerTimeMapper {
    List<InspectionSchedulerTimeDb> getSchedulerTimeByTemplateId(@Param("templateId") String templateId);

    List<InspectionSchedulerTimeDb> getSchedulerTimeList(@Param("page") long page,
                                                         @Param("size") long size,
                                                         @Param("timestamp") long timestamp);

    void insertTimeList(@Param("schedulerTimeDbs") List<InspectionSchedulerTimeDb> schedulerTimeDbs);

    void deleteByTemplateId(@Param("templateId") String templateId);

    void updateNextTime(@Param("id") String schedulerTimeId, @Param("nextStartTime") long nextStartTime);

    void disableByTemplateId(@Param("templateId") String templateId);

    Long countByNextTime(@Param("nextStartTime") long nextStartTime, @Param("nextEndTime") long nextEndTime, @Param("templateId") String templateId);

    Long countNotFinishedByTemplateId(@Param("templateId") String templateId);

    void insert(@Param("timeDb") InspectionSchedulerTimeDb schedulerTimeDb);

    void updateFinishedById(@Param("id") String id);
}
