package com.xylink.manager.inspection.mapper.impl;

import com.xylink.manager.inspection.config.DataSourceManager;
import com.xylink.manager.inspection.entity.db.InspectionServiceListDb;
import com.xylink.manager.inspection.mapper.InspectionSchedulerTimeMapper;
import com.xylink.manager.inspection.mapper.InspectionServiceListMapper;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/4
 */
@Service
public class InspectionServiceListMapperImpl implements InspectionServiceListMapper {
    @Autowired
    private DataSourceManager dataSourceManager;
    @Override
    public List<InspectionServiceListDb> getAll() {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionServiceListMapper mapper = session.getMapper(InspectionServiceListMapper.class);
            return mapper.getAll();
        }
    }

    @Override
    public InspectionServiceListDb getByServiceName(String serviceName) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionServiceListMapper mapper = session.getMapper(InspectionServiceListMapper.class);
            return mapper.getByServiceName(serviceName);
        }
    }
}
