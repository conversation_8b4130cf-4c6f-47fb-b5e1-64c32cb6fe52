package com.xylink.manager.inspection.mapper.impl;

import com.xylink.manager.inspection.config.DataSourceManager;
import com.xylink.manager.inspection.entity.db.InspectionMetricThresholdDb;
import com.xylink.manager.inspection.mapper.InspectionMetricTaskMapper;
import com.xylink.manager.inspection.mapper.InspectionMetricThresholdMapper;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/4
 */
@Service
public class InspectionMetricThresholdMapperImpl implements InspectionMetricThresholdMapper {
    @Autowired
    private DataSourceManager dataSourceManager;
    @Override
    public List<InspectionMetricThresholdDb> getThresholdsByMetricKey(String metricKey) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionMetricThresholdMapper mapper = session.getMapper(InspectionMetricThresholdMapper.class);
            return mapper.getThresholdsByMetricKey(metricKey);
        }
    }

    @Override
    public List<InspectionMetricThresholdDb> getThresholdsByMetricIds(List<String> metricKeys) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionMetricThresholdMapper mapper = session.getMapper(InspectionMetricThresholdMapper.class);
            return mapper.getThresholdsByMetricIds(metricKeys);
        }
    }
}
