package com.xylink.manager.inspection.mapper;

import com.xylink.manager.inspection.entity.db.InspectionTaskDb;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/13 10:25
 */
@Mapper
public interface InspectionTaskMapper {
    void insertTask(@Param("taskDb") InspectionTaskDb taskDb);

    void updateTask(@Param("id") String id,
                    @Param("endTime") long endTime,
                    @Param("highRisk") long highRisk,
                    @Param("middleRisk") long middleRisk,
                    @Param("lowRisk") long lowRisk);

    void updateTaskExceptionDesc(@Param("id") String id,
                                 @Param("endTime") long endTime,
                                 @Param("exceptionMessage") String exceptionMessage);

    List<InspectionTaskDb> getTaskByInstanceId(@Param("instanceId") String instanceId);

    List<String> getIdsByInstanceId(@Param("instanceId") String instanceId);

    void deleteTaskByInstanceId(@Param("instanceId") String instanceId);

    InspectionTaskDb getTaskById(@Param("id") String id);
}
