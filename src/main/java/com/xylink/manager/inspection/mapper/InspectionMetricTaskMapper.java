package com.xylink.manager.inspection.mapper;

import com.xylink.manager.inspection.entity.db.InspectionMetricTaskDb;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/7 22:37
 */
@Mapper
public interface InspectionMetricTaskMapper {
    void insertMetricTasks(@Param("page") List<InspectionMetricTaskDb> page);

    List<InspectionMetricTaskDb> getMetricTaskBySubTaskId(@Param("subTaskId") String subTaskId, @Param("size") Integer size, @Param("page") Integer page, @Param("ladders") List<Integer> ladders);

    Long countBySubTaskId(@Param("subTaskId") String subTaskId, @Param("ladders") List<Integer> ladders);

    void deleteBySubTaskIds(@Param("subTaskIds") List<String> subTaskIds);

    List<InspectionMetricTaskDb> getMetricTaskBySubTaskIds(@Param("subTaskIds") List<String> subTaskIds);

    void insertMetricTask(@Param("metricTask") InspectionMetricTaskDb metricTask);

    List<InspectionMetricTaskDb> getRiskMetricTasksBySubTaskId(@Param("subTaskId") String subTaskId);

    List<InspectionMetricTaskDb> getMetricTasksBySubTaskId(@Param("subTaskId") String subTaskId);
}
