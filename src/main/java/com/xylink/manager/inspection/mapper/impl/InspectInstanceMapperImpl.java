package com.xylink.manager.inspection.mapper.impl;

import com.xylink.manager.inspection.config.DataSourceManager;
import com.xylink.manager.inspection.entity.db.InspectInstanceDb;
import com.xylink.manager.inspection.mapper.InspectInstanceMapper;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/4
 */
@Service
public class InspectInstanceMapperImpl implements InspectInstanceMapper {
    @Autowired
    private DataSourceManager dataSourceManager;
    @Override
    public Long countByJobStatus(int status) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectInstanceMapper mapper = session.getMapper(InspectInstanceMapper.class);
            return mapper.countByJobStatus(status);
        }
    }

    @Override
    public void insert(InspectInstanceDb instance) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectInstanceMapper mapper = session.getMapper(InspectInstanceMapper.class);
            mapper.insert(instance);
        }
    }

    @Override
    public void updateRisk(String id, int step, int highRisk, int middleRisk, int lowRisk) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectInstanceMapper mapper = session.getMapper(InspectInstanceMapper.class);
            mapper.updateRisk(id, step, highRisk, middleRisk, lowRisk);
        }
    }

    @Override
    public void updateStatus(String id, long startTime, long finishTime, long consumedTime, int status) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectInstanceMapper mapper = session.getMapper(InspectInstanceMapper.class);
            mapper.updateStatus(id, startTime, finishTime, consumedTime, status);
        }
    }

    @Override
    public Long count(Long startTime, Long endTime) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectInstanceMapper mapper = session.getMapper(InspectInstanceMapper.class);
            return mapper.count(startTime, endTime);
        }
    }

    @Override
    public List<InspectInstanceDb> list(int size, int page, Long startTime, Long endTime, Boolean asc) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectInstanceMapper mapper = session.getMapper(InspectInstanceMapper.class);
            return mapper.list(size, page, startTime, endTime, asc);
        }
    }

    @Override
    public InspectInstanceDb selectById(String id) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectInstanceMapper mapper = session.getMapper(InspectInstanceMapper.class);
            return mapper.selectById(id);
        }
    }

    @Override
    public void updateStep(String id, int step) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectInstanceMapper mapper = session.getMapper(InspectInstanceMapper.class);
            mapper.updateStep(id, step);
        }
    }

    @Override
    public InspectInstanceDb selectByJobStatus(int status) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectInstanceMapper mapper = session.getMapper(InspectInstanceMapper.class);
            return mapper.selectByJobStatus(status);
        }
    }

    @Override
    public void updateFailed(String instanceId) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectInstanceMapper mapper = session.getMapper(InspectInstanceMapper.class);
            mapper.updateFailed(instanceId);
        }
    }

    @Override
    public InspectInstanceDb getLatestRecord() {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectInstanceMapper mapper = session.getMapper(InspectInstanceMapper.class);
            return mapper.getLatestRecord();
        }
    }
}
