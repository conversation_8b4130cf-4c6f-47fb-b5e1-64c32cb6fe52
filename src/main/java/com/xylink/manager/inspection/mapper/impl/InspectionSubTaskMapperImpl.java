package com.xylink.manager.inspection.mapper.impl;

import com.xylink.manager.inspection.config.DataSourceManager;
import com.xylink.manager.inspection.entity.db.InspectionSubTaskDb;
import com.xylink.manager.inspection.mapper.InspectionServiceResultMapper;
import com.xylink.manager.inspection.mapper.InspectionSubTaskMapper;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/4
 */
@Service
public class InspectionSubTaskMapperImpl implements InspectionSubTaskMapper {
    @Autowired
    private DataSourceManager dataSourceManager;
    @Override
    public void insertSubTask(InspectionSubTaskDb subTaskDb) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionSubTaskMapper mapper = session.getMapper(InspectionSubTaskMapper.class);
            mapper.insertSubTask(subTaskDb);
        }
    }

    @Override
    public void updateSubTask(String id, long endTime, long highRisk, long middleRisk, long lowRisk) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionSubTaskMapper mapper = session.getMapper(InspectionSubTaskMapper.class);
            mapper.updateSubTask(id, endTime, highRisk, middleRisk, lowRisk);
        }
    }

    @Override
    public void updateSubTaskExceptionDesc(String id, long endTime, String exceptionMessage) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionSubTaskMapper mapper = session.getMapper(InspectionSubTaskMapper.class);
            mapper.updateSubTaskExceptionDesc(id, endTime, exceptionMessage);
        }
    }

    @Override
    public List<InspectionSubTaskDb> getSubTasksByTaskId(String taskId) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionSubTaskMapper mapper = session.getMapper(InspectionSubTaskMapper.class);
            return mapper.getSubTasksByTaskId(taskId);
        }
    }

    @Override
    public List<InspectionSubTaskDb> getSubTasksByTaskIdAndIdentPage(String taskId, int size, int page, String ident, List<Integer> ladders) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionSubTaskMapper mapper = session.getMapper(InspectionSubTaskMapper.class);
            return mapper.getSubTasksByTaskIdAndIdentPage(taskId, size, page, ident, ladders);
        }
    }

    @Override
    public List<String> getIdsByTaskIds(List<String> taskIds) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionSubTaskMapper mapper = session.getMapper(InspectionSubTaskMapper.class);
            return mapper.getIdsByTaskIds(taskIds);
        }
    }

    @Override
    public void deleteSubTaskByTaskIds(List<String> taskIds) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionSubTaskMapper mapper = session.getMapper(InspectionSubTaskMapper.class);
            mapper.deleteSubTaskByTaskIds(taskIds);
        }
    }

    @Override
    public Long countByTaskId(String taskId, List<Integer> ladders) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionSubTaskMapper mapper = session.getMapper(InspectionSubTaskMapper.class);
            return mapper.countByTaskId(taskId, ladders);
        }
    }

    @Override
    public Long countIdByTaskIdAndType(String taskId, int type, List<Integer> ladders) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionSubTaskMapper mapper = session.getMapper(InspectionSubTaskMapper.class);
            return mapper.countIdByTaskIdAndType(taskId, type, ladders);
        }
    }

    @Override
    public List<InspectionSubTaskDb> getSubTasksByTaskIdAndType(String taskId, int type, int size, int page, List<Integer> ladders) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionSubTaskMapper mapper = session.getMapper(InspectionSubTaskMapper.class);
            return mapper.getSubTasksByTaskIdAndType(taskId, type, size, page, ladders);
        }
    }

    @Override
    public Long countByTaskIdAndIdent(String taskId, String ident, List<Integer> ladders) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionSubTaskMapper mapper = session.getMapper(InspectionSubTaskMapper.class);
            return mapper.countByTaskIdAndIdent(taskId, ident, ladders);
        }
    }

    @Override
    public void saveSubTasks(List<InspectionSubTaskDb> page) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionSubTaskMapper mapper = session.getMapper(InspectionSubTaskMapper.class);
            mapper.saveSubTasks(page);
        }
    }
}
