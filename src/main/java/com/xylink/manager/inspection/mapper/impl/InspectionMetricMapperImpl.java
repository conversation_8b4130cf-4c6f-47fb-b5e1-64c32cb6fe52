package com.xylink.manager.inspection.mapper.impl;

import com.xylink.manager.inspection.config.DataSourceManager;
import com.xylink.manager.inspection.entity.db.InspectionMetricConfigDb;
import com.xylink.manager.inspection.mapper.InspectionExportRecordMapper;
import com.xylink.manager.inspection.mapper.InspectionMetricMapper;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/4
 */
@Service
public class InspectionMetricMapperImpl implements InspectionMetricMapper {
    @Autowired
    private DataSourceManager dataSourceManager;
    @Override
    public List<InspectionMetricConfigDb> getInspectionMetricByItemId(String itemId) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionMetricMapper mapper = session.getMapper(InspectionMetricMapper.class);
            return mapper.getInspectionMetricByItemId(itemId);
        }
    }

    @Override
    public InspectionMetricConfigDb getInspectionMetricByKey(String metricKey) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionMetricMapper mapper = session.getMapper(InspectionMetricMapper.class);
            return mapper.getInspectionMetricByKey(metricKey);
        }
    }

    @Override
    public InspectionMetricConfigDb getInspectionMetricByCode(String metricCode) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionMetricMapper mapper = session.getMapper(InspectionMetricMapper.class);
            return mapper.getInspectionMetricByCode(metricCode);
        }
    }

    @Override
    public List<InspectionMetricConfigDb> getAll() {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionMetricMapper mapper = session.getMapper(InspectionMetricMapper.class);
            return mapper.getAll();
        }
    }

    @Override
    public String getSuggestByMetricKey(String metricKey) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionMetricMapper mapper = session.getMapper(InspectionMetricMapper.class);
            return mapper.getSuggestByMetricKey(metricKey);
        }
    }

    @Override
    public List<InspectionMetricConfigDb> getSuggestByMetricKeys(List<String> metricKeys) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionMetricMapper mapper = session.getMapper(InspectionMetricMapper.class);
            return mapper.getSuggestByMetricKeys(metricKeys);
        }
    }
}
