package com.xylink.manager.inspection.mapper;

import com.xylink.manager.inspection.entity.db.InspectionSubTaskDb;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/7 15:58
 */
@Mapper
public interface InspectionSubTaskMapper {
    void insertSubTask(@Param("subTaskDb") InspectionSubTaskDb subTaskDb);

    void updateSubTask(@Param("id") String id,
                       @Param("endTime") long endTime,
                       @Param("highRisk") long highRisk,
                       @Param("middleRisk") long middleRisk,
                       @Param("lowRisk") long lowRisk);

    void updateSubTaskExceptionDesc(@Param("id") String id,
                                    @Param("endTime") long endTime,
                                    @Param("exceptionMessage") String exceptionMessage);

    List<InspectionSubTaskDb> getSubTasksByTaskId(@Param("taskId") String taskId);

    List<InspectionSubTaskDb> getSubTasksByTaskIdAndIdentPage(@Param("taskId") String taskId,
                                                              @Param("size") int size,
                                                              @Param("page") int page,
                                                              @Param("ident") String ident,
                                                              @Param("ladders") List<Integer> ladders);

    List<String> getIdsByTaskIds(@Param("taskIds") List<String> taskIds);

    void deleteSubTaskByTaskIds(@Param("taskIds") List<String> taskIds);

    Long countByTaskId(@Param("taskId") String taskId, @Param("ladders") List<Integer> ladders);

    Long countIdByTaskIdAndType(@Param("taskId") String taskId, @Param("type") int type, @Param("ladders") List<Integer> ladders);

    List<InspectionSubTaskDb> getSubTasksByTaskIdAndType(@Param("taskId") String taskId,
                                                         @Param("type") int type,
                                                         @Param("size") int size,
                                                         @Param("page") int page,
                                                         @Param("ladders") List<Integer> ladders);

    Long countByTaskIdAndIdent(@Param("taskId") String taskId,
                               @Param("ident") String ident,
                               @Param("ladders") List<Integer> ladders);

    void saveSubTasks(@Param("page") List<InspectionSubTaskDb> page);
}
