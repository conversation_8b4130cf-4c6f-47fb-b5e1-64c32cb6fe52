package com.xylink.manager.inspection.mapper.impl;

import com.xylink.manager.inspection.config.DataSourceManager;
import com.xylink.manager.inspection.entity.db.InspectionTemplateItemDb;
import com.xylink.manager.inspection.mapper.InspectionTemplateItemMapper;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/4
 */
@Service
public class InspectionTemplateItemMapperImpl implements InspectionTemplateItemMapper {
    @Autowired
    private DataSourceManager dataSourceManager;
    @Override
    public void insert(List<InspectionTemplateItemDb> mapDbs) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()){
            InspectionTemplateItemMapper mapper = session.getMapper(InspectionTemplateItemMapper.class);
            mapper.insert(mapDbs);
        }
    }

    @Override
    public List<InspectionTemplateItemDb> selectByTemplateId(String configId) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()){
            InspectionTemplateItemMapper mapper = session.getMapper(InspectionTemplateItemMapper.class);
            return mapper.selectByTemplateId(configId);
        }
    }

    @Override
    public void deleteByTemplateId(String configId) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()){
            InspectionTemplateItemMapper mapper = session.getMapper(InspectionTemplateItemMapper.class);
            mapper.deleteByTemplateId(configId);
        }
    }
}
