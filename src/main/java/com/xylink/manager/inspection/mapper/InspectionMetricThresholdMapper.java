package com.xylink.manager.inspection.mapper;


import com.xylink.manager.inspection.entity.db.InspectionMetricThresholdDb;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/18 11:01
 */
@Mapper
public interface InspectionMetricThresholdMapper {

    /**
     * 获取指标对应的阈值信息
     *
     * @param metricKey 指标 id
     * @return 阈值集合
     */
    List<InspectionMetricThresholdDb> getThresholdsByMetricKey(@Param("metricKey") String metricKey);

    List<InspectionMetricThresholdDb> getThresholdsByMetricIds(@Param("metricKeys") List<String> metricKeys);
}
