package com.xylink.manager.inspection.mapper;

import com.xylink.manager.inspection.entity.db.InspectionServiceListDb;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/11 11:34
 */
@Mapper
public interface InspectionServiceListMapper {

    List<InspectionServiceListDb> getAll();

    InspectionServiceListDb getByServiceName(@Param("serviceName") String serviceName);
}
