package com.xylink.manager.inspection.mapper.impl;

import com.xylink.manager.inspection.config.DataSourceManager;
import com.xylink.manager.inspection.entity.db.InspectionSchedulerTimeDb;
import com.xylink.manager.inspection.mapper.InspectionMetricThresholdMapper;
import com.xylink.manager.inspection.mapper.InspectionSchedulerTimeMapper;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/4
 */
@Service
public class InspectionSchedulerTimeMapperImpl implements InspectionSchedulerTimeMapper {
    @Autowired
    private DataSourceManager dataSourceManager;
    @Override
    public List<InspectionSchedulerTimeDb> getSchedulerTimeByTemplateId(String templateId) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionSchedulerTimeMapper mapper = session.getMapper(InspectionSchedulerTimeMapper.class);
            return mapper.getSchedulerTimeByTemplateId(templateId);
        }
    }

    @Override
    public List<InspectionSchedulerTimeDb> getSchedulerTimeList(long page, long size, long timestamp) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionSchedulerTimeMapper mapper = session.getMapper(InspectionSchedulerTimeMapper.class);
            return mapper.getSchedulerTimeList(page, size, timestamp);
        }
    }

    @Override
    public void insertTimeList(List<InspectionSchedulerTimeDb> schedulerTimeDbs) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionSchedulerTimeMapper mapper = session.getMapper(InspectionSchedulerTimeMapper.class);
            mapper.insertTimeList(schedulerTimeDbs);
        }
    }

    @Override
    public void deleteByTemplateId(String templateId) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionSchedulerTimeMapper mapper = session.getMapper(InspectionSchedulerTimeMapper.class);
            mapper.deleteByTemplateId(templateId);
        }
    }

    @Override
    public void updateNextTime(String schedulerTimeId, long nextStartTime) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionSchedulerTimeMapper mapper = session.getMapper(InspectionSchedulerTimeMapper.class);
            mapper.updateNextTime(schedulerTimeId, nextStartTime);
        }
    }

    @Override
    public void disableByTemplateId(String templateId) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionSchedulerTimeMapper mapper = session.getMapper(InspectionSchedulerTimeMapper.class);
            mapper.disableByTemplateId(templateId);
        }
    }

    @Override
    public Long countByNextTime(long nextStartTime, long nextEndTime, String templateId) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionSchedulerTimeMapper mapper = session.getMapper(InspectionSchedulerTimeMapper.class);
            return mapper.countByNextTime(nextStartTime, nextEndTime, templateId);
        }
    }

    @Override
    public Long countNotFinishedByTemplateId(String templateId) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionSchedulerTimeMapper mapper = session.getMapper(InspectionSchedulerTimeMapper.class);
            return mapper.countNotFinishedByTemplateId(templateId);
        }
    }

    @Override
    public void insert(InspectionSchedulerTimeDb schedulerTimeDb) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionSchedulerTimeMapper mapper = session.getMapper(InspectionSchedulerTimeMapper.class);
            mapper.insert(schedulerTimeDb);
        }
    }

    @Override
    public void updateFinishedById(String id) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            InspectionSchedulerTimeMapper mapper = session.getMapper(InspectionSchedulerTimeMapper.class);
            mapper.updateFinishedById(id);
        }
    }
}
