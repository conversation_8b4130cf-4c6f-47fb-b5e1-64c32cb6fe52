package com.xylink.manager.inspection.mapper;


import com.xylink.manager.inspection.entity.db.InspectionExportRecordDb;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/11 16:34
 */
@Mapper
public interface InspectionExportRecordMapper {
    InspectionExportRecordDb getRecordByInstanceId(@Param("instanceId") String instanceId);

    void insert(@Param("instanceId") String instanceId, @Param("state") int state);

    void updateRecord(@Param("instanceId") String instanceId,
                      @Param("state") int state, @Param("filePath")
                      String filePath);

    void updateFailRecord(@Param("instanceId") String instanceId,
                          @Param("state") int state,
                          @Param("failDesc") String failDesc);

    List<InspectionExportRecordDb> getRecordByTime(@Param("time")String time);
}
