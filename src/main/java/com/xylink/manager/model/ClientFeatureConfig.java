package com.xylink.manager.model;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ClientFeatureConfig {
    private String modelName;
    private String configName;
    private String clientType;
    private String typeName;
    private String value;
    private String baseConfigType;
    private String oldValue;
    private String sn;

    public ClientFeatureConfig(String modelName, String configName, String clientType, String typeName, String value,
                               String baseConfigType, String oldValue) {
        this.typeName = typeName;
        this.modelName = modelName;
        this.configName = configName;
        this.clientType = clientType;
        this.value = value;
        this.baseConfigType = baseConfigType;
        this.oldValue = oldValue;
    }


    public ClientFeatureConfig(String modelName, String configName, String clientType, String typeName) {
        this.typeName = typeName;
        this.modelName = modelName;
        this.configName = configName;
        this.clientType = clientType;
    }
}
