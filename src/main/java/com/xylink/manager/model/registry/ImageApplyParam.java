package com.xylink.manager.model.registry;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> create on 2024/3/29
 */
@Data
public class ImageApplyParam {
    /**
     * 镜像名称
     */
    @NotBlank(message = "serviceName can not be null")
    private String serviceName;
    /**
     * 配置中心变量名称
     */
    @NotBlank(message = "imageVarName can not be null")
    private String imageVarName;

    /**
     * 镜像地址
     */
    @NotBlank(message = "imagePath can not be null")
    private String imagePath;
}
