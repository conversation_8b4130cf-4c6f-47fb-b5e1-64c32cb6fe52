package com.xylink.manager.model.registry.enums;

/**
 * <AUTHOR> create on 2023/11/21
 */
public enum ImageGenStateEnum {
    /**
     * 镜像包-已经上传
     */
    PK_UPLOADED("PK_UPLOADED"),

    /**
     * 镜像包-构建推送错误
     */
    PK_BUILD_ERROR("PK_BUILD_ERROR"),

    /**
     * 镜像包-已经构建推送完成
     */
    PK_BUILD_END("PK_BUILD_END"),

    /**
     * 镜像-已应用
     */
    IMAGE_APPLIED("IMAGE_APPLIED"),

    /**
     * 镜像-已删除
     */
    IMAGE_DELETED("IMAGE_DELETED");
    private final String state;

    ImageGenStateEnum(String state) {
        this.state = state;
    }

    public String getState() {
        return state;
    }

    public static ImageGenStateEnum match(String state) {
        for (ImageGenStateEnum imageGenStateEnum : ImageGenStateEnum.values()) {
            if (imageGenStateEnum.state.equals(state)) {
                return imageGenStateEnum;
            }
        }
        return null;
    }
}
