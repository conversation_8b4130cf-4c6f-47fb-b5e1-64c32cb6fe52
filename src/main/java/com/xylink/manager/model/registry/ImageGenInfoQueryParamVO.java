package com.xylink.manager.model.registry;

import lombok.Data;

/**
 * 查询参数
 *
 * <AUTHOR> create on 2023/11/22
 */
@Data
public class ImageGenInfoQueryParamVO {

    private Integer current;

    private Integer pageSize;

    /**
     * 所属迭代
     */
    private String releaseVersion;
    /**
     * 当前状态
     */
    private String currentState;
    /**
     * 镜像包上传时间-开始
     */
    private String pkUploadTimeStart;
    /**
     * 镜像包上传时间-结束
     */
    private String pkUploadTimeEnd;


    /**
     * 搜索关键词-镜像地址
     */
    private String keywords;


    /**
     * 根据镜像包上传时间降序
     */
    private Boolean orderByPkUploadDesc;
}
