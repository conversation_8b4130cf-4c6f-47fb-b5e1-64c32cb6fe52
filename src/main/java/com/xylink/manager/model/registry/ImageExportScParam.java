package com.xylink.manager.model.registry;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> create on 2023/11/20
 */
@Data
public class ImageExportScParam {
    /**
     * 流水号
     */
    private String packageNumber;
    /**
     * 包名
     */
    private String packageName;
    /**
     * 包在磁盘保存路径
     */
    private String packagePath;
    /**
     * Registry仓库的（用户名:密码）加密
     */
    private String hubToken;
    /**
     * 导出对象
     */
    private List<AppInfo> appInfos;


    @Data
    public static class AppInfo {
        /**
         * 导出对象类型，纯镜像为image
         */
        private List<String> type;
        /**
         * 导出的镜像源信息
         */
        private ImageDetailInfo image;

        @Data
        public static class ImageDetailInfo {
            /**
             * Registry域名
             */
            private String imageDomain;
            /**
             * 镜像详情
             */
            private List<ImageDetail> imageInfo;


            @Data
            public static class ImageDetail {
                /**
                 * 镜像地址
                 */
                private String imageUrl;
                /**
                 * 导出模式：tiny-镜像模式、full-全量模式
                 */
                private String imageMode;
                /**
                 * 所属迭代
                 */
                private String imageIteration;
            }
        }
    }

}
