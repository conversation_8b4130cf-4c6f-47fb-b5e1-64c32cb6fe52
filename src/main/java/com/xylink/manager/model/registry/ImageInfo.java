package com.xylink.manager.model.registry;

import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> create on 2023/11/16
 */
@Data
public class ImageInfo {

    /**
     * 镜像地址
     */
    private String imageAddress;

    /**
     * 所属迭代
     */
    private String releaseVersion;

    /**
     * 关联服务
     */
    private Set<String> relateServices;

    /**
     * 应用服务
     */
    private List<ImageApplyInfoDTO> imageApplyInfo;

    /**
     * 应用状态
     */
    private Boolean applied;

    /**
     * 引用数量
     */
    private Integer quoteNum;

    /**
     * 大小bytes
     */
    private Long sizeBytes;

    /**
     * 上传时间
     */
    private String uploadTime;
}
