package com.xylink.manager.model.registry;

import lombok.Data;

/**
 * <AUTHOR> create on 2023/11/16
 */
@Data
public class ImageGenInfo {
    /**
     * ID
     */
    private String id;
    /**
     * 包名
     */
    private String packageName;
    /**
     * 镜像地址
     */
    private String imageAddress;
    /**
     * 所属迭代
     */
    private String releaseVersion;
    /**
     * 当前状态
     */
    private String currentState;
    /**
     * 镜像包上传ID
     */
    private String pkUploadId;
    /**
     * 镜像包上传时间
     */
    private String pkUploadTime;
    /**
     * 镜像包上传构建人
     */
    private String pkUploadUsername;
    /**
     * 镜像包上传构建人
     */
    private String pkUploadDisplayName;
    /**
     * 构建完成时间
     */
    private String pkBuildEndTime;
    /**
     * 镜像删除时间
     */
    private String imageDeleteTime;
    /**
     * 镜像删除人
     */
    private String imageDeleteUsername;
    /**
     * 镜像删除人
     */
    private String imageDeleteDisplayName;
    /**
     * 备注
     */
    private String remark;
}
