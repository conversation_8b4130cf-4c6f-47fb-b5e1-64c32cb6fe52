package com.xylink.manager.model.registry;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR> create on 2023/11/20
 */
@Data
public class ImageExportDto {

    /**
     * 流水号
     */
    @NotBlank(message = "packageNumber can not be null")
    private String packageNumber;
    /**
     * 导出模式：tiny-镜像模式、full-全量模式
     */
    @NotBlank(message = "imageMode can not be null")
    private String imageMode;

    /**
     * 镜像列表
     */
    private List<String> imageList;

}
