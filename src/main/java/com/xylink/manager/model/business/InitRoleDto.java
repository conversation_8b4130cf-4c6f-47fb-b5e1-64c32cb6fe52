package com.xylink.manager.model.business;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024-09-20 11;
 */
@Data
public class InitRoleDto implements Serializable {
    @NotBlank(message = "securityAccount不能为空!")
    private String securityAccount;
    @NotBlank(message = "securityPwd不能为空!")
    private String securityPwd;
    @NotBlank(message = "auditAccount不能为空!")
    private String auditAccount;
    @NotBlank(message = "auditPwd不能为空!")
    private String auditPwd;
    @NotBlank(message = "systemAccount不能为空!")
    private String systemAccount;
    @NotBlank(message = "systemPwd不能为空!")
    private String systemPwd;
}
