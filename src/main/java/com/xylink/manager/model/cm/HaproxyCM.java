package com.xylink.manager.model.cm;

import com.xylink.manager.model.HaproxyInfoDto;
import com.xylink.manager.model.em.HaproxyRuleTypeEnum;

import java.util.*;

import com.xylink.config.HaproxyConstants;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: liyang
 * @DateTime: 2021/10/11 5:21 下午
 **/
@Data
public class HaproxyCM implements ICMDto<HaproxyCM>{
    /**
     * XFF开启/关闭，true/false
     */
    private boolean enableXFF;
    private boolean supportIpv6;
    private String nodeName;
    private List<HaproxyInfoDto> data;

    @Override
    public HaproxyCM toModel(Map<String, String> cm, String nodeName) {
        this.enableXFF = Boolean.parseBoolean(cm.get(nodeName + HaproxyConstants.XFF_SUFFIX));
        this.supportIpv6 = Boolean.parseBoolean(cm.get(nodeName + HaproxyConstants.IPV6_SUFFIX));
        this.nodeName = nodeName;
        this.data = new ArrayList<>();
        HaproxyRuleTypeEnum.getAllRuleType().forEach(type -> {
            HaproxyInfoDto haproxyInfoDto = getHaproxyInfoDto(cm, type, nodeName);
            //如果除ruleType字段以外有其他的字段为空，表明当前服务没有此规则类型
            if (StringUtils.isBlank(haproxyInfoDto.getFrontPort())) {
                return;
            }
            this.data.add(haproxyInfoDto);
        });
        return this;
    }

    private HaproxyInfoDto getHaproxyInfoDto(Map<String, String> cm, String typeName, String nodeName) {
        HaproxyRuleTypeEnum ruleType = HaproxyRuleTypeEnum.valueOf(typeName);
        String modeInfo = cm.get(ruleType.modeKeyPrefix(nodeName) + HaproxyConstants.MODE);
        //兼容一之前的逻辑，默认全部为TCP模式
        String mode = StringUtils.isBlank(modeInfo) ? HaproxyConstants.TCP : modeInfo;
        String frontPort = cm.get(ruleType.frontendKeyPrefix(nodeName) + HaproxyConstants.PORT);
        String backMasterIp = cm.get(ruleType.backendMasterKeyPrefix(nodeName) + HaproxyConstants.IP);
        String backMasterPort = cm.get(ruleType.backendMasterKeyPrefix(nodeName) + HaproxyConstants.PORT);
        String backSlaveIp = cm.get(ruleType.backendSlaveKeyPrefix(nodeName) + HaproxyConstants.IP);
        String backSlavePort = cm.get(ruleType.backendSlaveKeyPrefix(nodeName) + HaproxyConstants.PORT);
        return new HaproxyInfoDto(typeName, mode, frontPort, backMasterIp, backMasterPort, backSlaveIp, backSlavePort);
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        HaproxyInfoDto haproxyInfoDto = data.get(0);
        String ruleType = String.valueOf(haproxyInfoDto.getRuleType());
        HaproxyRuleTypeEnum haproxyRuleTypeEnum = HaproxyRuleTypeEnum.valueOf(ruleType);
        cm.put(haproxyRuleTypeEnum.modeKeyPrefix(nodeName) + HaproxyConstants.MODE, haproxyInfoDto.getMode());
        cm.put(haproxyRuleTypeEnum.frontendKeyPrefix(nodeName) + HaproxyConstants.PORT, haproxyInfoDto.getFrontPort());
        cm.put(haproxyRuleTypeEnum.backendMasterKeyPrefix(nodeName) + HaproxyConstants.IP, haproxyInfoDto.getBackMasterIp());
        cm.put(haproxyRuleTypeEnum.backendMasterKeyPrefix(nodeName) + HaproxyConstants.PORT, haproxyInfoDto.getBackMasterPort());
        cm.put(haproxyRuleTypeEnum.backendSlaveKeyPrefix(nodeName) + HaproxyConstants.IP, haproxyInfoDto.getBackSlaveIp());
        cm.put(haproxyRuleTypeEnum.backendSlaveKeyPrefix(nodeName) + HaproxyConstants.PORT, haproxyInfoDto.getBackSlavePort());
        return cm;
    }

}
