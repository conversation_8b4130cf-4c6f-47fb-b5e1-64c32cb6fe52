package com.xylink.manager.model.cm;

import com.xylink.config.DmcuConstants;
import com.xylink.config.mapper.JsonMapper;
import com.xylink.manager.model.CascadeMgrDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/31/10:20
 */
@Setter
@Getter
@Slf4j
public class CascadeGwCM extends DmcuCM {

    private List<CascadeMgrDTO> cascadeMgrDTOList;

    @Override
    public DmcuCM toModel(Map<String, String> cm, String nodeName) {
        super.toModel(cm, nodeName);
        String cascadeMgrAddressList = cm.get(nodeName + DmcuConstants.CASCADEMGR_ADDRESS_LIST);
        if (StringUtils.isEmpty(cascadeMgrAddressList)) {
            this.cascadeMgrDTOList = new ArrayList<>(0);
        } else {
            JsonMapper mapper = JsonMapper.nonEmptyMapper();
            this.cascadeMgrDTOList = mapper.fromJson(cascadeMgrAddressList, mapper.contructCollectionType(List.class, CascadeMgrDTO.class));
        }
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> configmap = super.toConfigmap();
        StringBuilder cascadeMgrWsAddress;
        if (CollectionUtils.isEmpty(cascadeMgrDTOList)) {
            cascadeMgrDTOList = new ArrayList<>(0);
            cascadeMgrWsAddress = new StringBuilder("ws://{CASCADEMGR_IP}:18125/");
        } else {
            cascadeMgrWsAddress = new StringBuilder();
            for (int i = 0;i < cascadeMgrDTOList.size(); i++) {
                if (StringUtils.isEmpty(cascadeMgrDTOList.get(i).getCascademgrIp())) {
                    continue;
                }
                cascadeMgrWsAddress.append("ws://");
                cascadeMgrWsAddress.append(cascadeMgrDTOList.get(i).getCascademgrIp());
                cascadeMgrWsAddress.append(":");
                cascadeMgrWsAddress.append(StringUtils.isEmpty(cascadeMgrDTOList.get(i).getCascademgrPort()) ? "18125" : cascadeMgrDTOList.get(i).getCascademgrPort());
                cascadeMgrWsAddress.append("/");
                if (i != cascadeMgrDTOList.size() - 1) {
                    cascadeMgrWsAddress.append(",");
                }
            }
            if (StringUtils.isEmpty(cascadeMgrWsAddress)) {
                cascadeMgrWsAddress = new StringBuilder("ws://{CASCADEMGR_IP}:18125/");
            }
        }
        JsonMapper mapper = JsonMapper.nonEmptyMapper();
        configmap.put(super.getNodeName() + DmcuConstants.CASCADEMGR_ADDRESS_LIST, mapper.toJson(cascadeMgrDTOList));
        configmap.put(super.getNodeName() + DmcuConstants.CASCADEMGR_ADDRESS, cascadeMgrWsAddress.toString());
        return configmap;
    }
}
