package com.xylink.manager.model.cm;

import com.xylink.config.LiveConstants;
import com.xylink.config.NetworkConstants;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/8/17 4:02 下午
 */
@Data
public class LiveCM implements ICMDto<LiveCM> {
    /**
     * 推流地址
     */
    private String livePushUrlDomain;
    /**
     * 播放地址
     */
    private String livePullUrlDomain;
    /**
     * 拉流地址
     */
    private String livePullUrlRtmpDomain;


    //推流是否开启权限校验
    private String livePushStreamNeedAuth;
    //推流权限校验的key,live会拿去计算出最终的key
    private String livePushStreamAuthKey;
    //拉流是否开启权限校验
    private String livePullStreamNeedAuth;
    //拉流权限校验的key,live会拿去计算出最终的key
    private String livePullStreamAuthKey;
    //推拉流的鉴权平台选择,不同平台计算key的策略不同，需要记录一下
    private String liveStreamAuthPlatform;



    public LiveCM() {
    }

    @Override
    public LiveCM toModel(Map<String, String> cm, String nodeName) {
        String livePushUrlDomainCm = cm.get(LiveConstants.livePushUrlDomain);
        if (StringUtils.isNotBlank(livePushUrlDomainCm)) {
            this.livePushUrlDomain = livePushUrlDomainCm;
        }
        String livePullUrlDomainCm = cm.get(LiveConstants.livePullUrlDomain);
        if (StringUtils.isNotBlank(livePullUrlDomainCm)) {
            this.livePullUrlDomain = livePullUrlDomainCm;
        }
        String livePullUrlRtmpDomainCm = cm.get(LiveConstants.livePullUrlRtmpDomain);
        if (StringUtils.isNotBlank(livePullUrlRtmpDomainCm)) {
            this.livePullUrlRtmpDomain = livePullUrlRtmpDomainCm;
        }


        String livePushStreamNeedAuthCm = cm.get(LiveConstants.livePushStreamNeedAuth);
        if (StringUtils.isNotBlank(livePushStreamNeedAuthCm)) {
            this.livePushStreamNeedAuth = livePushStreamNeedAuthCm;
        }

        String livePushStreamAuthKeyCm = cm.get(LiveConstants.livePushStreamAuthKey);
        if (StringUtils.isNotBlank(livePushStreamAuthKeyCm)) {
            this.livePushStreamAuthKey = livePushStreamAuthKeyCm;
        }

        String livePullStreamNeedAuthCm = cm.get(LiveConstants.livePullStreamNeedAuth);
        if (StringUtils.isNotBlank(livePullStreamNeedAuthCm)) {
            this.livePullStreamNeedAuth = livePullStreamNeedAuthCm;
        }

        String livePullStreamAuthKeyCm = cm.get(LiveConstants.livePullStreamAuthKey);
        if (StringUtils.isNotBlank(livePullStreamAuthKeyCm)) {
            this.livePullStreamAuthKey = livePullStreamAuthKeyCm;
        }

        String liveStreamAuthPlatformCm = cm.get(LiveConstants.liveStreamAuthPlatform);
        if (StringUtils.isNotBlank(liveStreamAuthPlatformCm)) {
            this.liveStreamAuthPlatform = liveStreamAuthPlatformCm;
        }

        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>(16);
        cm.put(LiveConstants.livePushUrlDomain, this.livePushUrlDomain);
        cm.put(LiveConstants.livePullUrlDomain, this.livePullUrlDomain);
        cm.put(LiveConstants.livePullUrlRtmpDomain, this.livePullUrlRtmpDomain);
        cm.put(LiveConstants.livePushStreamNeedAuth, this.livePushStreamNeedAuth);
        cm.put(LiveConstants.livePushStreamAuthKey, this.livePushStreamAuthKey);
        cm.put(LiveConstants.livePullStreamNeedAuth, this.livePullStreamNeedAuth);
        cm.put(LiveConstants.livePullStreamAuthKey, this.livePullStreamAuthKey);
        cm.put(LiveConstants.liveStreamAuthPlatform, this.liveStreamAuthPlatform);
        return cm;
    }

    @Override
    public LiveCM setDefault(String nodeName) {
        Map<String, String> allIp = getDeployService().getConfigMapAllIp().getData();
        this.livePushUrlDomain = allIp.get(NetworkConstants.VOD_INTERNAL_IP) + ":1935";
        this.livePullUrlDomain = allIp.get(NetworkConstants.VOD_DOMAIN_NAME) + port(allIp.get(NetworkConstants.VOD_NGINX_SSL_PORT), "443");

        if ("http/ws".equalsIgnoreCase(allIp.get("SCHEME"))) {
            this.livePullUrlDomain = allIp.get(NetworkConstants.VOD_DOMAIN_NAME) + port(allIp.get(NetworkConstants.VOD_NGINX_PORT), "80");
        }
        this.livePullUrlRtmpDomain = allIp.get(NetworkConstants.VOD_DOMAIN_NAME) + ":1935";

        this.livePushStreamNeedAuth = "false";
        this.livePullStreamNeedAuth = "false";
        this.liveStreamAuthPlatform = "aliyun";

        return this;
    }

    /**
     * 如果是期望值 则返回 "" ：过滤掉标准端口的显示 80 / 443
     *
     * @param portValue
     * @param expectValue
     * @return
     */
    private String port(String portValue, String expectValue) {
        if (StringUtils.isBlank(portValue)) {
            return "";
        }
        if (portValue.equalsIgnoreCase(expectValue)) {
            return "";
        }
        return ":" + portValue;
    }
}
