package com.xylink.manager.model.cm;

import com.xylink.config.DmcuConstants;
import com.xylink.config.VodnetworkConstants;
import com.xylink.manager.service.api.NoahApiService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.base.SystemModeConfig;
import com.xylink.util.SpringBeanUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022/3/16 2:29 下午
 */
@Getter
@Setter
@Slf4j
public class VodnetworkVodeditCM implements ICMDto<VodnetworkVodeditCM> {

    private String nodeName;

    private String siteCode;

    /**
     * vodnetwork-vodedit的http、https上传下载端口冲突，注意不要和本机的vodnetwork-vod内部回源端口
     */
    private String downloadHttpPort;

    private String downloadHttpsPort;

    /**
     * 代理vodnetwork-vod的nginx内网地址
     */
    private String nginxInternalAddress;

    /**
     * 代理vodnetwork-vod的nginx公网地址
     */
    private String nginxExternalAddress;

    /**
     * 与vodclustermgr级联地址,逗号分割: 127.0.0.1,********
     */
    private String vodclustermgrAddress;

    @Override
    public VodnetworkVodeditCM toModel(Map<String, String> cm, String nodeName) {
        String siteCodeKey = nodeName + DmcuConstants.SITECODE;
        String nginxInternalIpKey = nodeName + VodnetworkConstants.VODEDIT_NGINX_INTERNAL_IP;
        String nginxExternalIpKey = nodeName + VodnetworkConstants.VODEDIT_NGINX_EXTERNAL_IP;
        String downloadHttpPortKey = nodeName + VodnetworkConstants.VODEDIT_DOWNLOAD_HTTP_PORT;
        String downloadHttpsPortKey = nodeName + VodnetworkConstants.VODEDIT_DOWNLOAD_HTTPS_PORT;
        String vcmAddressKey = VodnetworkConstants.VODNETWORK_VCM_ADDRESS;

        String siteCodeFromCm = cm.get(siteCodeKey);
        this.siteCode = StringUtils.isNotBlank(siteCodeFromCm) ? siteCodeFromCm : VodnetworkConstants.DEFAULT_SITECODE;
        this.nginxInternalAddress = cm.get(nginxInternalIpKey);
        this.nginxExternalAddress = cm.get(nginxExternalIpKey);

        String downloadHttpPortFromCM = cm.get(downloadHttpPortKey);
        this.downloadHttpPort = StringUtils.isNotBlank(downloadHttpPortFromCM) ? downloadHttpPortFromCM : VodnetworkConstants.DEFAULT_VODEDIT_DOWNLOAD_HTTP_PORT;

        String downloadHttpsPortFromCM = cm.get(downloadHttpsPortKey);
        this.downloadHttpsPort = StringUtils.isNotBlank(downloadHttpsPortFromCM) ? downloadHttpsPortFromCM : VodnetworkConstants.DEFAULT_VODEDIT_DOWNLOAD_HTTPS_PORT;

        this.vodclustermgrAddress = cm.get(vcmAddressKey);
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        String siteCodeKey = nodeName + DmcuConstants.SITECODE;
        String nginxInternalIpKey = nodeName + VodnetworkConstants.VODEDIT_NGINX_INTERNAL_IP;
        String nginxExternalIpKey = nodeName + VodnetworkConstants.VODEDIT_NGINX_EXTERNAL_IP;
        String downloadHttpPortKey = nodeName + VodnetworkConstants.VODEDIT_DOWNLOAD_HTTP_PORT;
        String downloadHttpsPortKey = nodeName + VodnetworkConstants.VODEDIT_DOWNLOAD_HTTPS_PORT;
        String vcmAddressKey = VodnetworkConstants.VODNETWORK_VCM_ADDRESS;

        cm.put(siteCodeKey, StringUtils.isNotBlank(this.siteCode) ? this.siteCode : VodnetworkConstants.DEFAULT_SITECODE);
        cm.put(nginxInternalIpKey, this.nginxInternalAddress);
        cm.put(nginxExternalIpKey, this.nginxExternalAddress);
        cm.put(downloadHttpPortKey, this.downloadHttpPort);
        cm.put(downloadHttpsPortKey, this.downloadHttpsPort);
        cm.put(vcmAddressKey, this.vodclustermgrAddress);

        K8sService k8sService = SpringBeanUtil.getBean(K8sService.class);
        if (k8sService.isNewCms() || SystemModeConfig.isPrivate56()) {
            log.info("new cms notify noah about vodnetwork_vodedit updated");
            NoahApiService noahApiService = SpringBeanUtil.getBean(NoahApiService.class);
            Map<String, String> keyValues = new HashMap<>();
            keyValues.put("vodnetwork-vodedit.svc.http_port", this.downloadHttpPort);
            keyValues.put("vodnetwork-vodedit.svc.https_port", this.downloadHttpsPort);

            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("dataId", "var_env.svc.yaml");
            paramMap.put("keyValues", keyValues);
            paramMap.put("publish", "true");
            noahApiService.notifyNginxPort(paramMap);
        }
        return cm;
    }
}
