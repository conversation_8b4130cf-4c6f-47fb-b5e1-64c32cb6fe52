package com.xylink.manager.model.cm.customize.config;

/**
 * <AUTHOR>
 * @since 2022/12/15 11:12 上午
 */
public enum ChargeConfigKeyEnum {
    /**
     * 是否开启匿名呼叫,true开启，false关闭
     */
    anonymousCallEnable("false", "anonymousCall"),
    /**
     * 仅限预约呼叫下，提前入会时间
     */
    aheadTimeInOnlyScheduled("15", "aheadTimeInOnlyScheduled"),
    /**
     * 仅限预约呼叫下，是否忽略过期时间
     */
    ignoreExpireInOnlyScheduled("false", "ignoreExpireInOnlyScheduled"),
    /**
     * 呼叫规则限制
     */
    callLimitTurnoff("false","callLimitTurnoff"),

    sdkMonitorOpenConfig("false","sdkMonitorOpenConfig")
    ;

    private final Object defaultValue;
    private final String showName;

    ChargeConfigKeyEnum(Object defaultValue, String showName) {
        this.defaultValue = defaultValue;
        this.showName = showName;
    }

    public Object getDefaultValue() {
        return defaultValue;
    }

    public String getShowName() {
        return showName;
    }

    public static ChargeConfigKeyEnum valueOfShowName(String showName) {
        if (showName == null) {
            return null;
        }
        for (ChargeConfigKeyEnum configKeyEnum : ChargeConfigKeyEnum.values()) {
            if (configKeyEnum.getShowName().equals(showName)) {
                return configKeyEnum;
            }
        }
        return null;
    }

}
