package com.xylink.manager.model.cm;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/5/16 2:26 下午
 */
@Data
public class ZookeeperCM implements ICMDto<ZookeeperCM> {
    private static final String DEFAULT_SKIP_ACL_STATUS = "yes";
    private static final String KEY_ACL_WHITE_IPS = "ACL_WHITE_IPS";
    private static final String KEY_SKIP_ACL_STATUS = "SKIP_ACL_STATUS";
    /**
     * zookeeper ip授权
     */
    private Set<String> whiteIps = new HashSet<>();
    private String nodeName;
    private String skipAcl = DEFAULT_SKIP_ACL_STATUS;

    @Override
    public ZookeeperCM toModel(Map<String, String> cm, String nodeName) {
        this.nodeName = nodeName;
        String whiteIpsStr = cm.get(KEY_ACL_WHITE_IPS);
        this.whiteIps = ipToSet(whiteIpsStr);
        String skipAcl = cm.get(KEY_SKIP_ACL_STATUS);
        if (StringUtils.isNotBlank(skipAcl)) {
            this.skipAcl = skipAcl;
        }
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        cm.put(KEY_ACL_WHITE_IPS, ipString(this.whiteIps));
        cm.put(KEY_SKIP_ACL_STATUS, this.skipAcl);
        return cm;
    }

    public static String ipString(Set<String> whiteIps) {
        if (whiteIps == null) {
            return "";
        }
        return String.join(";", whiteIps);
    }

    public static Set<String> ipToSet(String ips) {
        return StringUtils.isNotBlank(ips) ? Arrays.stream(ips.split(";")).collect(Collectors.toSet()) : new HashSet<>();
    }

    public boolean isNotSkipAcl() {
        return !"yes".equals(this.skipAcl);
    }
}
