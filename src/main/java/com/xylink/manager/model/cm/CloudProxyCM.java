package com.xylink.manager.model.cm;

import com.xylink.config.ProxyConstants;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

@Setter
@Getter
public class CloudProxyCM implements ICMDto<CloudProxyCM> {

    private String internalPort;
    private String outPort;
    private String useIPV6;
    private String nodeName;

    @Override
    public CloudProxyCM toModel(Map<String, String> cm, String nodeName) {
        String internalPortInCm = cm.get(nodeName + PropertiesConstants.CLOUD_PROXY_INTERNAL_PORT);
        this.internalPort = StringUtils.isEmpty(internalPortInCm) ? "22222" : internalPortInCm;
        String outPortInCm = cm.get(nodeName + PropertiesConstants.CLOUD_PROXY_OUT_PORT);
        this.outPort = StringUtils.isEmpty(outPortInCm) ? "443" : outPortInCm;
        String useIPV6InCm = cm.get(nodeName + PropertiesConstants.NGINX_USE_IPV6);
        this.useIPV6=StringUtils.isEmpty(useIPV6InCm) ? "false" : useIPV6InCm;
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        cm.put(nodeName + PropertiesConstants.CLOUD_PROXY_INTERNAL_PORT, StringUtils.isBlank(internalPort) ? "22222" : internalPort);
        cm.put(nodeName + PropertiesConstants.CLOUD_PROXY_OUT_PORT, StringUtils.isBlank(outPort) ? "443" : outPort);
        cm.put(nodeName + PropertiesConstants.NGINX_USE_IPV6, StringUtils.isBlank(useIPV6) ? "false" : useIPV6);
        return cm;
    }

    private final static class PropertiesConstants{
        private static final String CLOUD_PROXY_INTERNAL_PORT = "-CLOUD_PROXY_INTERNAL_PORT";
        private static final String CLOUD_PROXY_OUT_PORT = "-CLOUD_PROXY_OUT_PORT";
        private static final String NGINX_USE_IPV6 = "-NGINX-USE-IPV6";
    }

}
