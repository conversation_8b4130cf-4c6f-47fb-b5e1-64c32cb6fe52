package com.xylink.manager.model.cm;

import com.xylink.config.NetworkConstants;
import com.xylink.config.ProxyConstants;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> create on 2025/7/10
 */
@Setter
@Getter
public class NightingaleCategrafCM implements ICMDto<NightingaleCategrafCM> {

    private static final String N9E_ADDRESS = "-N9E-ADDRESS";

    private String n9eAddress;

    private String nodeName;


    @Override
    public NightingaleCategrafCM toModel(Map<String, String> cm, String nodeName) {
        String n9eAddress = cm.get(nodeName + N9E_ADDRESS);
        this.nodeName = nodeName;
        this.n9eAddress = StringUtils.isBlank(n9eAddress) ? "" : n9eAddress;
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>(1);
        cm.put(nodeName + N9E_ADDRESS, n9eAddress);
        return cm;
    }
}
