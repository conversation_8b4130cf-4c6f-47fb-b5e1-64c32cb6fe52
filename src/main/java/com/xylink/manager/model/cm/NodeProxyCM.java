package com.xylink.manager.model.cm;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/8/21 15:36
 */
@Data
public class NodeProxyCM implements ICMDto<NodeProxyCM> {
    private String nodeName;

    /**
     * vpnserver地址，ip+port
     */
    private String vpnServerAddress;

    /**
     * masterVpn地址，ip+port
     */
    private String masterVpnAddress;

    /**
     * nodeProxy监听的http端口
     */
    private String nginxPort;


    @Override
    public NodeProxyCM toModel(Map<String, String> cm, String nodeName) {
        this.vpnServerAddress = cm.get(nodeName + "-VPNSERVER-ADDRESS");
        this.masterVpnAddress = cm.get(nodeName + "-MASTER-VPN-ADDRESS");
        this.nginxPort = cm.get(nodeName + "-NGINX-PORT");
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        HashMap<String, String> map = new HashMap<>();
        map.put(nodeName + "-VPNSERVER-ADDRESS", vpnServerAddress);
        map.put(nodeName + "-MASTER-VPN-ADDRESS", masterVpnAddress);
        map.put(nodeName + "-NGINX-PORT", nginxPort);
        return map;
    }
}
