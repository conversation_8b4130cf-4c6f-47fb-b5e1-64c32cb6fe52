package com.xylink.manager.model.cm;

import com.xylink.manager.model.em.IppbxSiggatewayEnum;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
public class IppbxSiggatewayCM implements ICMDto<IppbxSiggatewayCM> {
    private String proxyIp;
    private String proxyPort;
    private String user;
    private String pwd;
    private String type;

    private String fromPrefix;

    private String toPrefix;

    private String keepAlive;

    private String sn;

    private String nodeName;


    @Override
    public IppbxSiggatewayCM toModel(Map<String, String> siggw, String nodeName) {
        this.setNodeName(nodeName);
        if(null == siggw) return this;
        this.setProxyIp(siggw.get(nodeName + IppbxSiggatewayEnum.IPPBX_PROXY_IP.getValue()));
        this.setProxyPort(siggw.get(nodeName + IppbxSiggatewayEnum.IPPBX_PROXY_PORT.getValue()));
        this.setUser(siggw.get(nodeName + IppbxSiggatewayEnum.IPPBX_USER.getValue()));
        this.setPwd(siggw.get(nodeName + IppbxSiggatewayEnum.IPPBX_PWD.getValue()));
        this.setSn(siggw.get(nodeName + IppbxSiggatewayEnum.IPPBX_SN.getValue()));
        this.setType(siggw.get(nodeName + IppbxSiggatewayEnum.IPPBX_TYPE.getValue()));
        this.setFromPrefix(siggw.get(nodeName + IppbxSiggatewayEnum.IPPBX_SIGGW_FROM_PREFIX.getValue()));
        this.setToPrefix(siggw.get(nodeName + IppbxSiggatewayEnum.IPPBX_SIGGW_TO_PREFIX.getValue()));

        String keepAlive = siggw.get(nodeName + IppbxSiggatewayEnum.IPPBX_SIGGW_KEEPALIVE.getValue());
        if (StringUtils.isNotBlank(keepAlive) && "1".equalsIgnoreCase(keepAlive)) {
            keepAlive = "true";
        } else if (StringUtils.isNotBlank(keepAlive) && "0".equalsIgnoreCase(keepAlive)) {
            keepAlive = "false";
        } else {
            keepAlive = null;
        }
        this.setKeepAlive(keepAlive);
        return this;

    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> siggwConfig = new HashMap<>();

        siggwConfig.put(nodeName + IppbxSiggatewayEnum.IPPBX_PROXY_IP.getValue(), proxyIp);
        siggwConfig.put(nodeName + IppbxSiggatewayEnum.IPPBX_PROXY_PORT.getValue(), proxyPort);
        siggwConfig.put(nodeName + IppbxSiggatewayEnum.IPPBX_USER.getValue(), user);
        siggwConfig.put(nodeName + IppbxSiggatewayEnum.IPPBX_PWD.getValue(), pwd);
        siggwConfig.put(nodeName + IppbxSiggatewayEnum.IPPBX_TYPE.getValue(), type);
        //siggwConfig.put(nodeName + IppbxSiggatewayEnum.IPPBX_SN.getValue(), StringUtils.isBlank(sn) ? UUID.randomUUID().toString() : sn.trim());
        siggwConfig.put(nodeName + IppbxSiggatewayEnum.IPPBX_SIGGW_FROM_PREFIX.getValue(), fromPrefix);
        siggwConfig.put(nodeName + IppbxSiggatewayEnum.IPPBX_SIGGW_TO_PREFIX.getValue(), toPrefix);

        String keepAliveValue;
        if ("true".equalsIgnoreCase(keepAlive)) {
            keepAliveValue = "1";
        } else if ("false".equalsIgnoreCase(keepAlive)) {
            keepAliveValue = "0";
        }else{
            keepAliveValue = "";
        }
        siggwConfig.put(nodeName + IppbxSiggatewayEnum.IPPBX_SIGGW_KEEPALIVE.getValue(), keepAliveValue);
        return siggwConfig;
    }
}
