package com.xylink.manager.model.cm;

import com.xylink.config.Constants;
import com.xylink.config.DmcuConstants;
import com.xylink.manager.service.CalculateMediaParametersService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

@Setter
@Getter
@Slf4j
public class WebrtcMediagwCM extends DmcuCM {

    private String webrtcUdpPortNum;
    private String webrtcUdpPort;
    private String internalIpv4Ip;
    private String publicIpv4Ip;
    private String internalIpv6Ip;
    private String publicIpv6Ip;
    private String mediaAccessTaskNumber;
    //默认值给3，开启 0 关闭
    private String webrtcDbaFlag;


    @Override
    public DmcuCM toModel(Map<String, String> cm, String nodeName) {
        super.toModel(cm, nodeName);

        String udpPort = cm.get(nodeName + DmcuConstants.WEBRTC_UDP_PORT_NUM);
        this.webrtcUdpPortNum = StringUtils.isEmpty(udpPort) ? DmcuConstants.WEBRTC_UDP_PORT_NUM_DEFAULT : udpPort;

        String udp = cm.get(nodeName + DmcuConstants.WEBRTC_UDP_PORT);
        this.webrtcUdpPort = StringUtils.isEmpty(udp) ? DmcuConstants.WEBRTC_UDP_PORT_DEFAULT : udp;

        this.internalIpv4Ip = cm.get(nodeName + DmcuConstants.WEBRTC_INTERNAL_IPV4);
        this.publicIpv4Ip = cm.get(nodeName + DmcuConstants.WEBRTC_PUBLIC_IPV4);

        this.internalIpv6Ip = cm.get(nodeName + DmcuConstants.WEBRTC_INTERNAL_IPV6);
        this.publicIpv6Ip = cm.get(nodeName + DmcuConstants.WEBRTC_PUBLIC_IPV6);

        String taskNumber = cm.get(nodeName + DmcuConstants.MEDIA_ACCESS_TASK_NUMBER);
        String defaultTaskNumber = StringUtils.isEmpty(this.mediaAccessTaskNumber) ? "1" : this.mediaAccessTaskNumber;
        this.mediaAccessTaskNumber = StringUtils.isEmpty(taskNumber) ? defaultTaskNumber : taskNumber;

        String webrtcDbaFlagInCm = cm.get(nodeName + DmcuConstants.WEBRTC_DBA_FLAG);
        String defaultWebrtcDbaFlag = StringUtils.isEmpty(this.webrtcDbaFlag) ? "3" : this.webrtcDbaFlag;
        this.webrtcDbaFlag = StringUtils.isEmpty(webrtcDbaFlagInCm) ? defaultWebrtcDbaFlag : webrtcDbaFlagInCm;

        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> configmap = super.toConfigmap();

        configmap.put(super.getNodeName() + DmcuConstants.WEBRTC_UDP_PORT_NUM, StringUtils.isBlank(this.webrtcUdpPortNum) ? DmcuConstants.WEBRTC_UDP_PORT_NUM_DEFAULT : this.webrtcUdpPortNum);
        configmap.put(super.getNodeName() + DmcuConstants.WEBRTC_UDP_PORT, StringUtils.isBlank(this.webrtcUdpPort) ? DmcuConstants.WEBRTC_UDP_PORT_DEFAULT : this.webrtcUdpPort);
        configmap.put(super.getNodeName() + DmcuConstants.WEBRTC_INTERNAL_IPV4, this.internalIpv4Ip);
        configmap.put(super.getNodeName() + DmcuConstants.WEBRTC_PUBLIC_IPV4, this.publicIpv4Ip);
        configmap.put(super.getNodeName() + DmcuConstants.WEBRTC_INTERNAL_IPV6, this.internalIpv6Ip);
        configmap.put(super.getNodeName() + DmcuConstants.WEBRTC_PUBLIC_IPV6, this.publicIpv6Ip);
        configmap.put(super.getNodeName() + DmcuConstants.MEDIA_ACCESS_TASK_NUMBER, StringUtils.isBlank(this.mediaAccessTaskNumber) ? "1" : this.mediaAccessTaskNumber);
        configmap.put(super.getNodeName() + DmcuConstants.WEBRTC_DBA_FLAG, StringUtils.isBlank(this.webrtcDbaFlag) ? "3" : this.webrtcDbaFlag);

        return configmap;
    }

    @Override
    protected void fillSubclassProperty(CalculateMediaParametersService.MediaParameters mediaParameters) {
        super.fillSubclassProperty(mediaParameters);
        this.mediaAccessTaskNumber = mediaParameters.getMediaAccessTaskNumber();
    }

    @Override
    public void afterSave() {
        if (StringUtils.isNotBlank(this.getUseTransportProxy())) {
            try {
                getDeployService().patchConfigMapAllIp(d -> {
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.getUseTransportProxy());
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }

            try {
                getDeployService().patchConfigMap(Constants.CONFIGMAP_NMSA, Constants.NAMESPACE_DEFAULT, d -> {
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.getUseTransportProxy());
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }

            try {
                getDeployService().patchConfigMap(Constants.CONFIGMAP_ALL_IVR, Constants.NAMESPACE_DEFAULT, d -> {
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.getUseTransportProxy());
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }

            try {
                getDeployService().patchConfigMap(Constants.CONFIGMAP_HLS, Constants.NAMESPACE_DEFAULT, d -> {
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.getUseTransportProxy());
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }


            try {
                getDeployService().patchConfigMap(Constants.CONFIGMAP_ALL_CONVERGED_MEDIAGW, Constants.NAMESPACE_DEFAULT, d -> {
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.getUseTransportProxy());
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }

            try {
                getDeployService().patchConfigMap(Constants.CONFIGMAP_MA, Constants.NAMESPACE_DEFAULT, d -> {
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.getUseTransportProxy());
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }

            try {
                getDeployService().patchConfigMap(Constants.CONFIGMAP_DMCU, Constants.NAMESPACE_DEFAULT, d -> {
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.getUseTransportProxy());
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }
        }
    }

    @Override
    protected String  getMediaParametersServiceType(){
        return  "webrtc-mediagw";
    }
}
