package com.xylink.manager.model.cm;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

@Setter
@Getter
public class OpenrestyEduFileCM implements ICMDto<OpenrestyEduFileCM> {

    private String port;
    private String sslPort;
    private String nodeName;


    @Getter(AccessLevel.PRIVATE)
    final private String NGINX_PORT_KEY = "EDU_FILE_NGINX_PORT";
    @Getter(AccessLevel.PRIVATE)
    final private String NGINX_SSL_PORT_KEY = "EDU_FILE_NGINX_SSL_PORT";


    @Override
    public OpenrestyEduFileCM toModel(Map<String, String> cm, String nodeName) {
        this.nodeName = nodeName;
        String nginxPort = cm.get(  NGINX_PORT_KEY);
        this.port = StringUtils.isBlank(nginxPort) ? "80" : nginxPort;

        String nginxSslPort = cm.get(  NGINX_SSL_PORT_KEY);
        this.sslPort = StringUtils.isBlank(nginxSslPort) ? "443" : nginxSslPort;
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        cm.put(  NGINX_PORT_KEY, StringUtils.isNotBlank(port) ? port : "80");
        cm.put(  NGINX_SSL_PORT_KEY, StringUtils.isNotBlank(sslPort) ? sslPort : "443");

        return cm;
    }
}
