package com.xylink.manager.model.cm;

import com.xylink.config.ProxyConstants;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: liyang
 * @DateTime: 2022/8/23 10:19 上午
 **/
@Setter
@Getter
public class CdnProxyCM implements ICMDto<CdnProxyCM>{

    private String nodeName;

    private String nginxPort;
    private String nginxSSLPort;
    private String allowAppDownLoad;

    /**
     * cdn地址，配置后会将下载的流量转到对应的cdn上
     */
    private String cdnUrl;
    /**
     * cdn配置后，支持转发的类型
     */
    private String supportPlatform;

    /**
     * cdn加速域名替换配置，配置后会将下载的流量转到对应的cdn上（不会替换所有层级目录）
     */
    private String cdnDomainRep;
    /**
     * 开启ipv6
     */
    private String useIPV6;

    @Override
    public CdnProxyCM toModel(Map<String, String> cm, String nodeName) {
        this.nginxPort = cm.get(nodeName + ProxyConstants.NGINX_PORT);
        this.nginxSSLPort = cm.get(nodeName + ProxyConstants.NGINX_SSL_PORT);

        this.allowAppDownLoad = cm.get(nodeName + ProxyConstants.ALLOW_APP_DOWNLOAD);

        this.nodeName = nodeName;

        this.cdnUrl = cm.get(nodeName + ProxyConstants.CDN_URL);

        String supportPlatform = cm.get(nodeName + ProxyConstants.SUPPORT_PLATFORM);
        this.supportPlatform = StringUtils.isBlank(supportPlatform) ? "" : supportPlatform.replace("|android_office", "").replace("|ios_office", "").replace("|", ",");

        this.cdnDomainRep = cm.get(nodeName + ProxyConstants.CDN_DOMAIN_REP);

        String useIPV6 = cm.get(nodeName + ProxyConstants.NGINX_USE_IPV6);
        this.useIPV6 = StringUtils.isBlank(useIPV6) ? ProxyConstants.DEFAULT_NGINX_USE_IPV6 : useIPV6;
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        cm.put(nodeName +ProxyConstants.NGINX_PORT, nginxPort);
        cm.put(nodeName +ProxyConstants.NGINX_SSL_PORT, nginxSSLPort);
        cm.put(nodeName + ProxyConstants.ALLOW_APP_DOWNLOAD, allowAppDownLoad);

        if (StringUtils.isBlank(cdnUrl)) {
            cm.put(nodeName + ProxyConstants.CDN_URL, null);
        }else {
            cm.put(nodeName + ProxyConstants.CDN_URL, cdnUrl.endsWith("/") ? cdnUrl : cdnUrl + "/");
        }

        String resPlatform = "";
        if (StringUtils.isNotBlank(supportPlatform)) {
            resPlatform = supportPlatform.replace("android", "android,android_office").replace("ios", "ios,ios_office");
        }
        cm.put(nodeName + ProxyConstants.SUPPORT_PLATFORM, resPlatform.replace(",", "|"));

        if (StringUtils.isBlank(cdnDomainRep)) {
            cm.put(nodeName + ProxyConstants.CDN_DOMAIN_REP, null);
        }else {
            cm.put(nodeName + ProxyConstants.CDN_DOMAIN_REP, cdnDomainRep.endsWith("/") ? cdnDomainRep : cdnDomainRep + "/");
        }
        cm.put(nodeName + ProxyConstants.NGINX_USE_IPV6, useIPV6);
        return cm;
    }

}
