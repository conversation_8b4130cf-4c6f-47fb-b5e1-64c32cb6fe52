package com.xylink.manager.model.cm;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022/7/15 2:20 下午
 */
@Data
public class ExternalwebCM implements ICMDto<ExternalwebCM>{

    /**
     * 终端用户权限校验
     */
    private String terminalUserPermissionCheck;

    /**
     * 登录认证是否LSX(建行)方式
     */
    private String authCodeTypeIsLsx;

    /**
     * 强制删除过期云会议室开关
     */
    private String delExpiredMeetingRoom;

    /**
     * 登录认证aeskey
     */
    private String loginLsxAesKey;

    /**
     * 登录认证LSX私钥
     */
    private String loginRsaLsxKey;

    /**
     * 登录认证私钥
     */
    private String loginRsaKey;

    /**
     * 登录认证sm4秘钥
     */
    private String loginSm4Key;

    private String nodeName;

    @Override
    public ExternalwebCM toModel(Map<String, String> cm, String nodeName) {
        String userPermissionCheck = cm.get("USER_PERMISSION_CHECK");
        this.terminalUserPermissionCheck = StringUtils.isBlank(userPermissionCheck) ? "false" : userPermissionCheck;

        String authCodeTypeIsLsx = cm.get("AUTHCODE_LOGIN_TYPE_IS_LSX");
        this.authCodeTypeIsLsx = StringUtils.isBlank(authCodeTypeIsLsx) ? "false" : authCodeTypeIsLsx;

        String forceDelExpiredMeetingRoom = cm.get("FORCE_DEL_EXPIRED_MEETINGROOM");
        this.delExpiredMeetingRoom = StringUtils.isBlank(forceDelExpiredMeetingRoom) ? "false" : forceDelExpiredMeetingRoom;

        this.nodeName = nodeName;

        this.loginLsxAesKey = cm.get("AUTHCODE_LOGIN_LSXAESKEY");
        this.loginRsaLsxKey = cm.get("AUTHCODE_LOGIN_RSA_LSXPRKEY");
        this.loginRsaKey = cm.get("AUTHCODE_LOGIN_RSA_PRKEY");
        this.loginSm4Key = cm.get("AUTHCODE_LOGIN_SM4KEY");

        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> map = new HashMap<>();
        map.put("USER_PERMISSION_CHECK", this.terminalUserPermissionCheck);
        map.put("AUTHCODE_LOGIN_TYPE_IS_LSX", this.authCodeTypeIsLsx);
        map.put("FORCE_DEL_EXPIRED_MEETINGROOM", this.delExpiredMeetingRoom);
        map.put("AUTHCODE_LOGIN_LSXAESKEY", this.loginLsxAesKey);
        map.put("AUTHCODE_LOGIN_RSA_LSXPRKEY", this.loginRsaLsxKey);
        map.put("AUTHCODE_LOGIN_RSA_PRKEY", this.loginRsaKey);
        map.put("AUTHCODE_LOGIN_SM4KEY", this.loginSm4Key);
        return map;
    }
}
