package com.xylink.manager.model.cm;

import com.xylink.config.ProxyConstants;
import com.xylink.manager.service.api.NoahApiService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.util.SpringBeanUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022/3/16 2:29 下午
 */
@Getter
@Setter
@Slf4j
public class VodnetworkProxyCM implements ICMDto<VodnetworkProxyCM>{

    private String nginxPort;
    private String nginxSSLPort;
    /**
     * 代理的vodnetwork-vod点播监听端口，注意不要和本机其他端口冲突
     */
    private String ondemandPort;

    /**
     * 代理的vodnetwork-vodedit的http下载端口
     */
    private String downloadPort;

    private String nodeName;

    @Override
    public VodnetworkProxyCM toModel(Map<String, String> cm, String nodeName) {
        this.nginxPort = cm.get(nodeName + ProxyConstants.NGINX_PORT);
        this.nginxSSLPort = cm.get(nodeName + ProxyConstants.NGINX_SSL_PORT);
        this.ondemandPort = cm.get(nodeName + ProxyConstants.VODNETWORK_VOD_PORT);
        this.downloadPort = cm.get(nodeName + ProxyConstants.VODNETWORK_VODEDIT_PORT);
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        cm.put(nodeName + ProxyConstants.NGINX_PORT, this.nginxPort);
        cm.put(nodeName + ProxyConstants.NGINX_SSL_PORT, this.nginxSSLPort);
        cm.put(nodeName + ProxyConstants.VODNETWORK_VOD_PORT, this.ondemandPort);
        cm.put(nodeName + ProxyConstants.VODNETWORK_VODEDIT_PORT, this.downloadPort);
        K8sService k8sService = SpringBeanUtil.getBean(K8sService.class);
        if (k8sService.isNewCms()) {
            log.info("new cms notify noah about vodnetwork-proxy updated");
            NoahApiService noahApiService = SpringBeanUtil.getBean(NoahApiService.class);
            Map<String, String> keyValues = new HashMap<>();
            keyValues.put("vodnetwork-proxy.svc.http_port", this.nginxPort);
            keyValues.put("vodnetwork-proxy.svc.https_port", this.nginxSSLPort);

            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("dataId", "var_env.svc.yaml");
            paramMap.put("keyValues", keyValues);
            paramMap.put("publish", "true");
            noahApiService.notifyNginxPort(paramMap);
        }
        return cm;
    }
}
