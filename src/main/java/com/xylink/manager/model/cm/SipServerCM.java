package com.xylink.manager.model.cm;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xylink.config.SipServerConstants;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 2022/09/14/10:04
 * @Description: sipserver高级配置
 */
@Setter
@Getter
public class SipServerCM implements ICMDto<SipServerCM> {

    private String nodeName;

    /**
     * 是否开启注册
     **/
    private String enableRegister;

    /**
     * 是否开启注册到其他服务器的功能，不能和enableregister一起开启
     **/
    private String enableRegisterToServer;

    /**
     * 是否开启注册鉴权
     **/
    private String enableRegisterAuthentication;

    /**
     * 鉴权模式（远端、本地）
     **/
    private String authenticationType;

    /**
     * user agent映射，前者替换为后者（仅供测试）
     **/
    private String userAgentMapping;

    /**
     * 是否开启线程池使用情况log
     **/
    private String enableStartQueueLog;

    /**
     * 是否开启api网关
     **/
    private String enableApiGw;

    /**
     * 是否开启api网关鉴权
     **/
    private String enableApiGwAuth;

    /**
     * iauth sn
     **/
    @JsonProperty(value = "iAuthSn")
    private String iAuthSn;

    /**
     * api网关客户端id
     **/
    private String apiClientId;

    /**
     * api网关版本
     **/
    private String apiGwVersion;

    /**
     * 是否开启终端helper日志
     **/
    private String enableStartEndpointHelperLog;

    /**
     * 是否开启rest服务
     **/
    private String enableRestServer;

    /**
     * 是否开启网关助手
     **/
    private String enableGwHelper;


    @Override
    public SipServerCM toModel(Map<String, String> cm, String nodeName) {
        String enableRegisterKey = nodeName + SipServerConstants.enableRegister;
        String enableRegisterToServerKey = nodeName + SipServerConstants.enableRegisterToServer;
        String enableRegisterAuthenticationKey = nodeName + SipServerConstants.enableRegisterAuthentication;
        String authenticationTypeKey = nodeName + SipServerConstants.authenticationType;
        String userAgentMappingKey = nodeName + SipServerConstants.userAgentMapping;
        String enableStartQueueLogKey = nodeName + SipServerConstants.enableStartQueueLog;
        String enableApiGwKey = nodeName + SipServerConstants.enableApiGw;
        String enableApiGwAuthKey = nodeName + SipServerConstants.enableApiGwAuth;
        String iAuthSnKey = nodeName + SipServerConstants.iAuthSn;
        String apiClientIdKey = nodeName + SipServerConstants.apiClientId;
        String apiGwVersionKey = nodeName + SipServerConstants.apiGwVersion;
        String enableStartEndpointHelperLogKey = nodeName + SipServerConstants.enableStartEndpointHelperLog;
        String enableRestServerKey = nodeName + SipServerConstants.enableRestServer;
        String enableGwHelperKey = nodeName + SipServerConstants.enableGwHelper;

        this.enableRegister = cm.get(enableRegisterKey) != null ? cm.get(enableRegisterKey) : "true";
        this.enableRegisterToServer = cm.get(enableRegisterToServerKey) != null ? cm.get(enableRegisterToServerKey) : "false";
        this.enableRegisterAuthentication = cm.get(enableRegisterAuthenticationKey) != null ? cm.get(enableRegisterAuthenticationKey) : "true";
        this.authenticationType = cm.get(authenticationTypeKey) != null ? cm.get(authenticationTypeKey) : "local";
        this.userAgentMapping = cm.get(userAgentMappingKey) != null ? cm.get(userAgentMappingKey) : "Polycom:xylink001";
        this.enableStartQueueLog = cm.get(enableStartQueueLogKey) != null ? cm.get(enableStartQueueLogKey) : "true";
        this.enableApiGw = cm.get(enableApiGwKey) != null ? cm.get(enableApiGwKey) : "false";
        this.enableApiGwAuth = cm.get(enableApiGwAuthKey) != null ? cm.get(enableApiGwAuthKey) : "false";
        this.iAuthSn = cm.get(iAuthSnKey);
        this.apiClientId = cm.get(apiClientIdKey);
        this.apiGwVersion = cm.get(apiGwVersionKey) != null ? cm.get(apiGwVersionKey) : "2.0";
        this.enableStartEndpointHelperLog = cm.get(enableStartEndpointHelperLogKey) != null ? cm.get(enableStartEndpointHelperLogKey) : "false";
        this.enableRestServer = cm.get(enableRestServerKey) != null ? cm.get(enableRestServerKey) : "false";
        this.enableGwHelper = cm.get(enableGwHelperKey) != null ? cm.get(enableGwHelperKey) : "false";
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        HashMap<String, String> cm = new HashMap<>();


        String enableRegisterKey = nodeName + SipServerConstants.enableRegister;
        String enableRegisterToServerKey = nodeName + SipServerConstants.enableRegisterToServer;
        String enableRegisterAuthenticationKey = nodeName + SipServerConstants.enableRegisterAuthentication;
        String authenticationTypeKey = nodeName + SipServerConstants.authenticationType;
        String userAgentMappingKey = nodeName + SipServerConstants.userAgentMapping;
        String enableStartQueueLogKey = nodeName + SipServerConstants.enableStartQueueLog;
        String enableApiGwKey = nodeName + SipServerConstants.enableApiGw;
        String enableApiGwAuthKey = nodeName + SipServerConstants.enableApiGwAuth;
        String iAuthSnKey = nodeName + SipServerConstants.iAuthSn;
        String apiClientIdKey = nodeName + SipServerConstants.apiClientId;
        String apiGwVersionKey = nodeName + SipServerConstants.apiGwVersion;
        String enableStartEndpointHelperLogKey = nodeName + SipServerConstants.enableStartEndpointHelperLog;
        String enableRestServerKey = nodeName + SipServerConstants.enableRestServer;
        String enableGwHelperKey = nodeName + SipServerConstants.enableGwHelper;

        cm.put(enableRegisterKey, this.enableRegister);
        cm.put(enableRegisterToServerKey, this.enableRegisterToServer);
        cm.put(enableRegisterAuthenticationKey, this.enableRegisterAuthentication);
        cm.put(authenticationTypeKey, this.authenticationType);
        cm.put(userAgentMappingKey, this.userAgentMapping);
        cm.put(enableStartQueueLogKey, this.enableStartQueueLog);
        cm.put(enableApiGwKey, this.enableApiGw);
        cm.put(enableApiGwAuthKey, this.enableApiGwAuth);
        cm.put(iAuthSnKey, this.iAuthSn);
        cm.put(apiClientIdKey, this.apiClientId);
        cm.put(apiGwVersionKey, this.apiGwVersion);
        cm.put(enableStartEndpointHelperLogKey, this.enableStartEndpointHelperLog);
        cm.put(enableRestServerKey, this.enableRestServer);
        cm.put(enableGwHelperKey, this.enableGwHelper);
        return cm;
    }

}
