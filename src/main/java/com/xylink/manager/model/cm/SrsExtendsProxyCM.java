package com.xylink.manager.model.cm;

import com.xylink.config.ProxyConstants;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Setter
@Getter
public class SrsExtendsProxyCM implements ICMDto<SrsExtendsProxyCM> {

    private String port;
    private String sslPort;
    private String nodeName;

    @Override
    public SrsExtendsProxyCM toModel(Map<String, String> cm, String nodeName) {
        this.port = cm.get(nodeName + ProxyConstants.NGINX_PORT);
        this.sslPort = cm.get(nodeName + ProxyConstants.NGINX_SSL_PORT);
        this.nodeName = nodeName;
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        cm.put(nodeName + ProxyConstants.NGINX_PORT, port);
        cm.put(nodeName + ProxyConstants.NGINX_SSL_PORT, sslPort);
        return cm;
    }
}
