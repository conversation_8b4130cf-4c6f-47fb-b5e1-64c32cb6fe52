package com.xylink.manager.model.cm.customize.config;

/**
 * <AUTHOR>
 * @since 2022/12/15 11:12 上午
 */
public enum ChargeConferenceConfigKeyEnum {
    /**
     * 允许临时账号入会,true开启，false关闭
     */
    allowTempUserCall("true", "allowTempUserCall"),
    /**
     * 由主持人管控入会,true开启，false关闭
     */
    tempUserCallApproved("false", "tempUserCallApproved"),

    /**
     * 主持人管控入会可见开关,true,false
     */
    showTempUserCallApproved("true", "showTempUserCallApproved"),
    /**
     * 强制由主持人管控入会,true开启，false关闭
     */
    forceTempUserCallApproved("false", "forceTempUserCallApproved"),

    ;

    private final Object defaultValue;
    private final String showName;

    ChargeConferenceConfigKeyEnum(Object defaultValue, String showName) {
        this.defaultValue = defaultValue;
        this.showName = showName;
    }

    public Object getDefaultValue() {
        return defaultValue;
    }

    public String getShowName() {
        return showName;
    }

    public static ChargeConferenceConfigKeyEnum valueOfShowName(String showName) {
        if (showName == null) {
            return null;
        }
        for (ChargeConferenceConfigKeyEnum configKeyEnum : ChargeConferenceConfigKeyEnum.values()) {
            if (configKeyEnum.getShowName().equals(showName)) {
                return configKeyEnum;
            }
        }
        return null;
    }

}
