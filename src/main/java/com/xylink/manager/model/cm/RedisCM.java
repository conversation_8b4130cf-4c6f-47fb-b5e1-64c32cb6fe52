package com.xylink.manager.model.cm;

import com.xylink.config.RedisConstants;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * redis高级配置
 * <AUTHOR>
 */
@Data
public class RedisCM implements ICMDto<RedisCM>{
    /**
     * 角色：主master或备slave
     */
    private String role;

    private String nodeName;

    @Override
    public RedisCM toModel(Map<String, String> cm, String nodeName) {
        this.nodeName = nodeName;
        this.role = cm.get(nodeName + RedisConstants.REDIS_ROLE_SUFFIX);
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> allRedis = new HashMap<>();
        allRedis.put(nodeName + RedisConstants.REDIS_ROLE_SUFFIX, this.role);
        return allRedis;
    }
}
