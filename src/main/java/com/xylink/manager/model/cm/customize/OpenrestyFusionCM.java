package com.xylink.manager.model.cm.customize;

import com.xylink.config.ProxyConstants;
import com.xylink.manager.model.cm.ICMDto;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/1/17 15:40
 */
@Getter
@Setter
public class OpenrestyFusionCM implements ICMDto<OpenrestyFusionCM> {
    private String nodeName;
    private String nginxPort;
    private String nginxSslPort;

    @Override
    public OpenrestyFusionCM toModel(Map<String, String> cm, String nodeName) {
        String nginxPortKey = nodeName + ProxyConstants.NGINX_PORT;
        String nginxSslPortKey = nodeName + ProxyConstants.NGINX_SSL_PORT;

        String nginxPort = cm.get(nginxPortKey);
        this.nginxPort = StringUtils.isBlank(nginxPort) ? "80" : nginxPort;

        String nginxSslPort = cm.get(nginxSslPortKey);
        this.nginxSslPort = StringUtils.isBlank(nginxSslPort) ? "443" : nginxSslPort;

        this.nodeName = nodeName;
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        HashMap<String, String> map = new HashMap<>();

        map.put(this.nodeName + ProxyConstants.NGINX_PORT, this.nginxPort);
        map.put(this.nodeName + ProxyConstants.NGINX_SSL_PORT, this.nginxSslPort);
        return map;
    }

}
