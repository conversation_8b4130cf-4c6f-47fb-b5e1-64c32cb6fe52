package com.xylink.manager.model.cm;

import com.xylink.config.Constants;
import com.xylink.config.mapper.JsonMapper;
import com.xylink.manager.controller.dto.SiteInfoDto;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/06/04/10:35
 */
@Getter
@Setter
public class CascadeMgrCM implements ICMDto<CascadeMgrCM> {

    private static final String CASCADE_MGR_VEP_WEBSOCKER_URL = "CASCADE-MGR-VEP-WEBSOCKER-URL";
    private static final String CASCADE_MGR_PUBLIC_VEP_WEBSOCKER_URL = "CASCADE-MGR-PUBLIC-VEP-WEBSOCKER-URL";

    private String nodeName;
    private List<SiteInfoDto> siteInfoList;
    private String vepWebsockerUrl;
    private String publicVepWebsockerUrl;

    @Override
    public CascadeMgrCM toModel(Map<String, String> cm, String nodeName) {
        this.nodeName = nodeName;
        String siteInfoString = cm.get(this.nodeName + Constants.CASCADE_MGR_SITE_INFO);
        JsonMapper mapper = JsonMapper.nonEmptyMapper();
        this.siteInfoList = mapper.fromJson(siteInfoString, mapper.contructCollectionType(List.class, SiteInfoDto.class));
        this.vepWebsockerUrl = cm.get(CASCADE_MGR_VEP_WEBSOCKER_URL);
        this.publicVepWebsockerUrl = cm.get(CASCADE_MGR_PUBLIC_VEP_WEBSOCKER_URL);
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> map = new HashMap<>(1);
        map.put(this.nodeName + Constants.CASCADE_MGR_SITE_INFO, JsonMapper.nonDefaultMapper().toJson(siteInfoList));
        map.put(CASCADE_MGR_VEP_WEBSOCKER_URL, StringUtils.isBlank(this.vepWebsockerUrl) ? "" : this.vepWebsockerUrl);
        map.put(CASCADE_MGR_PUBLIC_VEP_WEBSOCKER_URL, StringUtils.isBlank(this.publicVepWebsockerUrl) ? "" : this.publicVepWebsockerUrl);
        return map;
    }

}