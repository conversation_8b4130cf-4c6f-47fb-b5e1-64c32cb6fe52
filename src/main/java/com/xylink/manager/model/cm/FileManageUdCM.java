package com.xylink.manager.model.cm;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

@Setter
@Getter
public class FileManageUdCM implements ICMDto<FileManageUdCM> {

    private String port;
    private String sslPort;
    private String domain;
    private String nodeName;


    @Getter(AccessLevel.PRIVATE)
    final private String NGINX_PORT_KEY = "EDU_FILE_PLAY_PORT";
    @Getter(AccessLevel.PRIVATE)
    final private String NGINX_SSL_PORT_KEY = "EDU_FILE_PLAY_SSL_PORT";
    @Getter(AccessLevel.PRIVATE)
    final private String DOMAIN_KEY = "EDU_FILE_PLAY_DOMAIN";


    @Override
    public FileManageUdCM toModel(Map<String, String> cm, String nodeName) {
        this.nodeName = nodeName;
        this.port = cm.get(  NGINX_PORT_KEY);;
        this.sslPort = cm.get(NGINX_SSL_PORT_KEY);
        this.domain = cm.get(DOMAIN_KEY);
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        cm.put(NGINX_PORT_KEY, StringUtils.trim(port));
        cm.put(NGINX_SSL_PORT_KEY, StringUtils.trim(sslPort));
        cm.put(DOMAIN_KEY, StringUtils.trim(domain));
        return cm;
    }
}
