package com.xylink.manager.model.cm;

import com.xylink.manager.service.base.IDeployService;
import com.xylink.util.SpringBeanUtil;

import java.util.Map;

/**
 * 场景: 服务器管理系统 各服务的高级配置 <br/>
 * 说明: 主要有以下三个流程需要根据具体业务实现 <br/>
 * 1、toModel: 查询configmap中的配置，转换为用于前端展示的model <br/>
 * 2、toConfigmap: 接收前端录入的数据，将其转换为configmap，用于k8s存储 <br/>
 * 3、setDefault: 前端展示时，在未配置时，进行默认值填充，用于展示 <br/>
 */
public interface ICMDto<T extends ICMDto> {
    default IDeployService getDeployService() {
        return SpringBeanUtil.getBean(IDeployService.class);
    }

    /**
     * configmap数据转换为model
     */
    T toModel(Map<String, String> cm, String nodeName);


    /**
     * model数据转换为configmap
     */
    Map<String, String> toConfigmap();

    /**
     * 补齐默认值
     */
    default T setDefault(String nodeName){
        return (T) this;
    }

    /**
     * 补齐默认值
     */
    default T setDefault(String nodeName,Map<String, String> cm){
        return (T) this;
    }

    default void beforeSave() { }

    default void afterSave() {}
}
