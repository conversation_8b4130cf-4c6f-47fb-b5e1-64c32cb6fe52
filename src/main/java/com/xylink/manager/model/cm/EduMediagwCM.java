package com.xylink.manager.model.cm;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class EduMediagwCM extends DmcuCM {
    @Override
    public EduMediagwCM setDefault(String nodeName) {
        try {
//            Map<String, String> allIp = getDeployService().getConfigMapAllIp().getData();
            String selfThriftIp = getDeployService().getNodeByName(nodeName).getIp();
            setSelfThriftIp(selfThriftIp);


            //先不设置默认的upThriftIp 避免页面实现，实际未设置，引起误会
//            List<Node> nodes = client.nodes().withLabel(Labels.edu_1nsiggw.label(), Constants.XYLINK).list().getItems();
            String upThriftIp = null;
//            if (nodes != null && nodes.size() > 0) {
//                upThriftIp = nodes.get(0).getStatus().getAddresses()
//                        .stream().filter(x -> x.getType().equalsIgnoreCase("InternalIP")).findFirst().get().getAddress();
//            }
            setUpThriftIp(upThriftIp);
        } finally {
            return this;
        }
    }
}
