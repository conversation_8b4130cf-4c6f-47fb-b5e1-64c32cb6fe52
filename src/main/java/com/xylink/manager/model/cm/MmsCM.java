package com.xylink.manager.model.cm;

import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.api.NoahApiService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.util.SpringBeanUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Setter
@Getter
public class MmsCM implements ICMDto<MmsCM> {

    private String nodeName;
    private String mode;

    private static final String MODE_KEY = "MMS_MODE";


    @Override
    public MmsCM toModel(Map<String, String> cm, String nodeName) {
        this.nodeName = nodeName;
        String modeInCm = cm.get(MODE_KEY);
        if (StringUtils.isNotBlank(modeInCm)) {
            this.mode = modeInCm;
        }
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        cm.put(MODE_KEY, this.mode);
        return cm;
    }


    @Override
    public MmsCM setDefault(String nodeName) {
        this.mode = Mode.multi.name();
        return this;
    }

    @Override
    public void afterSave() {
        // 取消/部署 mms-dispatcher
        if (Mode.single.name().equals(this.mode)) {
            getDeployService().listNodesByAppLabel(Labels.mms.label())
                    .forEach(nod -> {
                        log.info("Remove label:[{}] from node [{}]", "mms-dispatcher=xylink", nod.getName());
                        getDeployService().removeNodeAppLabel(nod.getName(), "mms-dispatcher");
                    });
            K8sService k8sService = SpringBeanUtil.getBean(K8sService.class);
            if (k8sService.isNewCms()) {
                log.info("new cms notify noah about mms-dispatcher removed");
                NoahApiService noahApiService = SpringBeanUtil.getBean(NoahApiService.class);
                noahApiService.notifyDeployLabelChange();
            }
        } else {
            getDeployService().listNodesByAppLabel(Labels.mms.label()).forEach(nod -> {
                log.info("Add label:[{}] to node [{}]", "mms-dispatcher=xylink", nod.getName());
                getDeployService().addNodeAppLabel(nod.getName(), "mms-dispatcher");
            });
            K8sService k8sService = SpringBeanUtil.getBean(K8sService.class);
            if (k8sService.isNewCms()) {
                log.info("new cms notify noah about mms-dispatcher added");
                NoahApiService noahApiService = SpringBeanUtil.getBean(NoahApiService.class);
                noahApiService.notifyDeployLabelChange();
            }
        }
    }

    enum Mode {
        single,
        multi
    }
}
