package com.xylink.manager.model.cm;

import com.xylink.config.Constants;
import com.xylink.config.DmcuConstants;
import com.xylink.config.exception.basic.ClientErrorException;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.mapper.JsonMapper;
import com.xylink.manager.model.DmcuThriftDto;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.service.CalculateMediaParametersService;
import com.xylink.util.IpUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.util.*;

@Setter
@Getter
@Slf4j
public class DmcuCM extends AbstractMediaCM implements ICMDto<DmcuCM> {

    private String nodeName;
    private String dmcuComments;
    private String siteCode;
    private String upThriftIp;
    private String upThriftIp2;
    private String selfThriftIp;
    private String selfThriftIp2;
    private String selfThriftPort;
    private String selfThriftPort2;
    private String maxRxBandWidth;
    private String maxTxBandWidth;
    private String mediaProcessTaskNum;
    private String mediaEndpointNumPerMediaTask;
    /**
     * 外网终端连接端口
     */
    private String dmcuClientPort;
    /**
     * 内网终端连接端口
     */
    private String dmcuClientInPort;
    private String dmcuType;
    private String dmcuResourceType;
    private String dmcuConfType;
    private String kafkaIp;
    private String encodeQPAdjust;
    private String transScoreCpuCoefficient;

    /**
     * MCU级联内网IP
     */
    private String peerInternalIp;
    /**
     * MCU级联外网IP
     */
    private String peerPublicIp;
    /**
     * MCU级联端口
     */
    private String peerPort;

    private String nodeUpHookPort;
    private String nodeSelfHookPort;
    private String nodeUpHookExt;
    private String nodeSelfHookExt;
    /**
     * 分区号
     */
    private String cascadableCloudId;

    private String dmcuInternalIpv4;
    private String dmcuPublicIpv4;
    private String dmcuInternalIpv6;
    private String dmcuPublicIpv6;
    private String dmcuClientIpv6Port;


    /**
     * 首帧最大等待时间
     **/
    private String maxWaitTimeForVideoMixer;

    /**
     * MC1远端劫持端口
     */
    private String upThriftHookPort;

    /**
     * MC1远端劫持地址
     */
    private String upThriftHookExt;

    /**
     * MC1本地劫持端口
     */
    private String selfThriftHookPort;

    /**
     * MC1本地劫持地址
     */
    private String selfThriftHookExt;

    private String upThriftHookPort2;
    private String upThriftHookExt2;
    private String selfThriftHookPort2;
    private String selfThriftHookExt2;

    /**
     * 所有dmcu、dmcu-side是否开启MCU间TP功能,保存时重启当前平台所有dmcu和dmcu-side
     */
    private String useTransportProxy;

    public boolean changeUseTransportProxy = false;

    /**
     * @Description: 分部署nmst 323终端容量
     **/
    private String multiFullTrans323EpCapacity;

    /**
     * @Description: 分部署nmst分数使用上
     **/
    private String multiFullTransUsageLimitPercentage;

    /**
     * @Description: ssrc扩展优先使用低63个ssrc
     **/
    private String fullTransAllocateSsrcPriority63;

    /**
     * @Description: ssrc扩展只使用低63个ssrc
     **/
    private String fullTransAllocateSsrcOnlyUseLow63;

    /**
     * @Description: ssrc扩展分配声音ssrc数量
     **/
    private String fullTransAllocateSsrcAudioSsrcNum;

    /**
     * @Description: 过载保护开启
     **/
    private String enableFullTransOverloadProtection;

    /**
     * @Description: 过载保护降级分辨率下限
     **/
    private String fullTransDownLimitRes;

    /**
     * @Description: 过载保护降级帧率下限
     **/
    private String fullTransDownLimitFps;

    /**
     * @Description: 过载保护手动配置总分数
     **/
    private String fullTransAllScore;

    /**
     * 智能服务器地址
     */
    private String transcriptionSrvIp;

    /**
     * 智能服务器监听端口，默认9900
     */
    private String transcriptionSrvPort;

    /**
     * 推送混流开关，默认true
     */
    private String transcriptionMixerSwitch;
    /**
     * 多实例部署
     */
    private String instanceNum;
    /**
     * MCU NAT功能开关
     */
    private String natForce;

    /**
     * nmst开启4K功能
     */
    private String enable4K;
    /**
     * 媒体扩展级联ip
     */
    private String localPeerIpExpand;

    /**
     * RecvTask
     */
    private String enableRecvTask;

    private List<DmcuThriftDto> dmcuThriftInfoList;

    private static final String ipv4Regex = "^.*:(\\d+)$";


    @Override
    public DmcuCM toModel(Map<String, String> cm, String nodeName) {
        String siteCodeKey = nodeName + DmcuConstants.SITECODE;
        String commentsKey = nodeName + DmcuConstants.COMMENTS;
        String maxRxKey = nodeName + DmcuConstants.MAXRXBW;
        String maxTxKey = nodeName + DmcuConstants.MAXTXBW;
        String upThriftKey = nodeName + DmcuConstants.UP_THRIFT_IP;
        String upThriftKey2 = nodeName + DmcuConstants.UP_THRIFT_IP_2;
        String selfThriftKey = nodeName + DmcuConstants.SELF_THRIFT_IP;
        String processTaskNumKey = nodeName + DmcuConstants.PROCESS_TASKNUM;
        String epnumPerTaskKey = nodeName + DmcuConstants.EPNUM_PERTASK;
        String dmcuClientPortKey = nodeName + DmcuConstants.DMCU_CLIENT_PORT;
        String dmcuTypeKey = nodeName + DmcuConstants.DMCU_TYPE;
        String nodeUpHookPortKey = nodeName + DmcuConstants.DMCU_SIDE_NODE_UP_HOOK_PORT;
        String nodeSelfHookPortKey = nodeName + DmcuConstants.DMCU_SIDE_NODE_SELF_HOOK_PORT;
        String nodeUpHookExtKey = nodeName + DmcuConstants.DMCU_SIDE_NODE_UP_HOOK_EXT;
        String nodeSelfHookExtKey = nodeName + DmcuConstants.DMCU_SIDE_NODE_SELF_HOOK_EXT;
        String nodeInternalIpv4Key = nodeName + DmcuConstants.DMCU_INTERNAL_IPV4;
        String nodePublicIpv4Key = nodeName + DmcuConstants.DMCU_PUBLIC_IPV4;
        String nodeInternalIpv6Key = nodeName + DmcuConstants.DMCU_INTERNAL_IPV6;
        String nodePublicIpv6Key = nodeName + DmcuConstants.DMCU_PUBLIC_IPV6;
        String nodeClientIpv6PortKey = nodeName + DmcuConstants.DMCU_CLIENT_IPV6_PORT;
        String nodeTranscriptionSrvIpKey = nodeName + DmcuConstants.TRANSCRIPTION_SRV_IP;
        String nodeTranscriptionSrvPortKey = nodeName + DmcuConstants.TRANSCRIPTION_SRV_PORT;
        String nodeTranscriptionMixerSwitchKey = nodeName + DmcuConstants.TRANSCRIPTION_MIXER_SWITCH;


        String defaultSiteCodeKey = DmcuConstants.DEFAULT_PREFIX + DmcuConstants.SITECODE;
        String defaultMaxRxKey = DmcuConstants.DEFAULT_PREFIX + DmcuConstants.MAXRXBW;
        String defaultMaxTxKey = DmcuConstants.DEFAULT_PREFIX + DmcuConstants.MAXTXBW;
        String defaultTaskNumKey = DmcuConstants.DEFAULT_PREFIX + DmcuConstants.PROCESS_TASKNUM;
        String defaultPerTaskKey = DmcuConstants.DEFAULT_PREFIX + DmcuConstants.EPNUM_PERTASK;
        String defaultDmcuClientPortKey = DmcuConstants.DEFAULT_PREFIX + DmcuConstants.DMCU_CLIENT_PORT;

        String defaultDmcuTranscriptionSrvIpKey = DmcuConstants.DEFAULT_PREFIX + DmcuConstants.TRANSCRIPTION_SRV_IP;
        String defaultDmcuTranscriptionSrvPortKey = DmcuConstants.DEFAULT_PREFIX + DmcuConstants.TRANSCRIPTION_SRV_PORT;
        String defaultDmcuTranscriptionMixerSwitchKey = DmcuConstants.DEFAULT_PREFIX + DmcuConstants.TRANSCRIPTION_MIXER_SWITCH;

        this.nodeUpHookPort = cm.get(nodeUpHookPortKey);
        this.nodeSelfHookPort = cm.get(nodeSelfHookPortKey);
        this.nodeUpHookExt = cm.get(nodeUpHookExtKey);
        this.nodeSelfHookExt = cm.get(nodeSelfHookExtKey);


        String siteCode = cm.get(siteCodeKey);
        this.siteCode = StringUtils.isEmpty(siteCode) ? cm.get(defaultSiteCodeKey) : siteCode;

        String maxRx = cm.get(maxRxKey);
        String defaultMaxRx = StringUtils.isEmpty(this.maxRxBandWidth) ? cm.get(defaultMaxRxKey) : this.maxRxBandWidth;
        this.maxRxBandWidth = StringUtils.isEmpty(maxRx) ? defaultMaxRx : maxRx;

        String maxTx = cm.get(maxTxKey);
        String defaultMaxTx = StringUtils.isEmpty(this.maxTxBandWidth) ? cm.get(defaultMaxTxKey) : this.maxTxBandWidth;
        this.maxTxBandWidth = StringUtils.isEmpty(maxTx) ? defaultMaxTx : maxTx;

        String taskNum = cm.get(processTaskNumKey);
        String defaultTaskNum = StringUtils.isEmpty(this.mediaProcessTaskNum) ? cm.get(defaultTaskNumKey) : this.mediaProcessTaskNum;
        this.mediaProcessTaskNum = StringUtils.isEmpty(taskNum) ? defaultTaskNum : taskNum;

        String perTask = cm.get(epnumPerTaskKey);
        String defaultPerTask = StringUtils.isEmpty(this.mediaEndpointNumPerMediaTask) ? cm.get(defaultPerTaskKey) : this.mediaEndpointNumPerMediaTask;
        this.mediaEndpointNumPerMediaTask = StringUtils.isEmpty(perTask) ? defaultPerTask : perTask;

        String upThrift = cm.get(upThriftKey);
        this.upThriftIp = StringUtils.isBlank(upThrift) ? this.upThriftIp : upThrift;

        this.upThriftIp2 = cm.get(upThriftKey2);

        String selfThrift = cm.get(selfThriftKey);
        this.selfThriftIp = StringUtils.isBlank(selfThrift) ? this.selfThriftIp : selfThrift;

        String selfThrift2 = cm.get(nodeName + DmcuConstants.SELF_THRIFT_IP_2);
        this.selfThriftIp2 = StringUtils.isBlank(selfThrift2) ? this.selfThriftIp2 : selfThrift2;

        String thriftPort = cm.get(nodeName + DmcuConstants.SELF_THRIFT_PORT);
        this.selfThriftPort = StringUtils.isBlank(thriftPort) ? cm.get(DmcuConstants.DEFAULT_PREFIX + DmcuConstants.SELF_THRIFT_PORT) : thriftPort;

        String thriftPort2 = cm.get(nodeName + DmcuConstants.SELF_THRIFT_PORT_2);
        this.selfThriftPort2 = StringUtils.isBlank(thriftPort2) ? cm.get(DmcuConstants.DEFAULT_PREFIX + DmcuConstants.SELF_THRIFT_PORT_2) : thriftPort2;


        String clientPort = cm.get(dmcuClientPortKey);
        this.dmcuClientPort = StringUtils.isEmpty(clientPort) ? cm.get(defaultDmcuClientPortKey) : clientPort;

        this.dmcuComments = cm.get(commentsKey);

        String dmcuType = cm.get(dmcuTypeKey);
        this.dmcuType = StringUtils.isEmpty(dmcuType) ? DmcuConstants.DMCU_TYPE_DEFAULT : dmcuType;

        this.kafkaIp = cm.get(nodeName + DmcuConstants.KAFKA_IP);

        String qp = cm.get(nodeName + DmcuConstants.ENCODE_QP_ADJUST);
        this.encodeQPAdjust = StringUtils.isBlank(qp) ? DmcuConstants.ENCODE_QP_ADJUST_DEFAULT : qp;

        String cpu = cm.get(nodeName + DmcuConstants.TRANS_SCORE_CPU_COEFFICIENT);
        this.transScoreCpuCoefficient = StringUtils.isBlank(cpu) ? DmcuConstants.TRANS_SCORE_CPU_COEFFICIENT_DEFAULT : cpu;


        this.peerInternalIp = cm.get(nodeName + DmcuConstants.PEER_INTERNAL_IP);
        this.peerPublicIp = cm.get(nodeName + DmcuConstants.PEER_PUBLIC_IP);

        String peerPort = cm.get(nodeName + DmcuConstants.PEER_PORT);
        this.peerPort = StringUtils.isEmpty(peerPort) ? cm.get(defaultDmcuClientPortKey) : peerPort;

        String clientInPort = cm.get(nodeName + DmcuConstants.DMCU_CLIENT_IN_PORT);
        this.dmcuClientInPort = StringUtils.isEmpty(clientInPort) ? cm.get(defaultDmcuClientPortKey) : clientInPort;

        String cascadableCloudId = cm.get(nodeName + DmcuConstants.CLOUDID);
        this.cascadableCloudId = StringUtils.isEmpty(cascadableCloudId) ? DmcuConstants.CLOUDID_DEFAULT : cascadableCloudId;

        this.dmcuInternalIpv4 = cm.get(nodeInternalIpv4Key);
        this.dmcuPublicIpv4 = cm.get(nodePublicIpv4Key);
        this.dmcuInternalIpv6 = cm.get(nodeInternalIpv6Key);
        this.dmcuPublicIpv6 = cm.get(nodePublicIpv6Key);
        this.dmcuClientIpv6Port = cm.get(nodeClientIpv6PortKey);

        String maxWaitTimeForVideoMixer = cm.get(nodeName + DmcuConstants.MAX_WAIT_TIME_FOR_VIDEO_MIXER);
        this.maxWaitTimeForVideoMixer = StringUtils.isEmpty(maxWaitTimeForVideoMixer) ? "1500" : maxWaitTimeForVideoMixer;

        this.upThriftHookPort = cm.get(nodeName + DmcuConstants.DMCU_SIDE_NODE_UP_HOOK_PORT);
        this.upThriftHookExt = cm.get(nodeName + DmcuConstants.DMCU_SIDE_NODE_UP_HOOK_EXT);
        this.selfThriftHookPort = cm.get(nodeName + DmcuConstants.DMCU_SIDE_NODE_SELF_HOOK_PORT);
        this.selfThriftHookExt = cm.get(nodeName + DmcuConstants.DMCU_SIDE_NODE_SELF_HOOK_EXT);

        this.upThriftHookPort2 = cm.get(nodeName + DmcuConstants.DMCU_SIDE_NODE_UP_HOOK_PORT_2);
        this.upThriftHookExt2 = cm.get(nodeName + DmcuConstants.DMCU_SIDE_NODE_UP_HOOK_EXT_2);
        this.selfThriftHookPort2 = cm.get(nodeName + DmcuConstants.DMCU_SIDE_NODE_SELF_HOOK_PORT_2);
        this.selfThriftHookExt2 = cm.get(nodeName + DmcuConstants.DMCU_SIDE_NODE_SELF_HOOK_EXT_2);

        String allDmcuUseTransportProxy = cm.get(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY);
        this.useTransportProxy = StringUtils.isBlank(allDmcuUseTransportProxy) ? "false" : allDmcuUseTransportProxy;

        String nmstEnable4K = cm.get(nodeName + DmcuConstants.NMST_ENABLE_4K);
        this.enable4K = StringUtils.isBlank(nmstEnable4K) ? "false" : nmstEnable4K;

        String multiFullTrans323EpCapacity = cm.get(nodeName + DmcuConstants.MULTI_FULL_TRANS323_EP_CAPACITY);
        this.multiFullTrans323EpCapacity = StringUtils.isBlank(multiFullTrans323EpCapacity) ? "0" : multiFullTrans323EpCapacity;

        String multiFullTransUsageLimitPercentage = cm.get(nodeName + DmcuConstants.MULTI_FULL_TRANS_USAGE_LIMIT_PERCENTAGE);
        this.multiFullTransUsageLimitPercentage = StringUtils.isBlank(multiFullTransUsageLimitPercentage) ? "80" : multiFullTransUsageLimitPercentage;

        String fullTransAllocateSsrcPriority63 = cm.get(nodeName + DmcuConstants.FULL_TRANS_ALLOCATE_SSRC_PRIORITY63);
        this.fullTransAllocateSsrcPriority63 = StringUtils.isBlank(fullTransAllocateSsrcPriority63) ? "true" : fullTransAllocateSsrcPriority63;

        String fullTransAllocateSsrcOnlyUseLow63 = cm.get(nodeName + DmcuConstants.FULL_TRANS_ALLOCATE_SSRC_ONLY_USE_LOW63);
        this.fullTransAllocateSsrcOnlyUseLow63 = StringUtils.isBlank(fullTransAllocateSsrcOnlyUseLow63) ? "false" : fullTransAllocateSsrcOnlyUseLow63;

        String fullTransAllocateSsrcAudioSsrcNum = cm.get(nodeName + DmcuConstants.FULL_TRANS_ALLOCATE_SSRC_AUDIO_SSRC_NUM);
        this.fullTransAllocateSsrcAudioSsrcNum = StringUtils.isBlank(fullTransAllocateSsrcAudioSsrcNum) ? "35" : fullTransAllocateSsrcAudioSsrcNum;

        String enableFullTransOverloadProtection = cm.get(nodeName + DmcuConstants.ENABLE_FULL_TRANS_OVERLOAD_PROTECTION);
        this.enableFullTransOverloadProtection = StringUtils.isBlank(enableFullTransOverloadProtection) ? "true" : enableFullTransOverloadProtection;

        String fullTransDownLimitRes = cm.get(nodeName + DmcuConstants.FULL_TRANS_DOWN_LIMIT_RES);
        this.fullTransDownLimitRes = StringUtils.isBlank(fullTransDownLimitRes) ? "720P" : fullTransDownLimitRes;

        String fullTransDownLimitFps = cm.get(nodeName + DmcuConstants.FULL_TRANS_DOWN_LIMIT_FPS);
        this.fullTransDownLimitFps = StringUtils.isBlank(fullTransDownLimitRes) ? "1500" : fullTransDownLimitFps;

        String fullTransAllScore = cm.get(nodeName + DmcuConstants.FULL_TRANS_ALL_SCORE);
        this.fullTransAllScore = StringUtils.isBlank(fullTransDownLimitRes) ? "0" : fullTransAllScore;

        String nodeTranscriptionSrvIp = cm.get(nodeTranscriptionSrvIpKey);
        this.transcriptionSrvIp = StringUtils.isBlank(nodeTranscriptionSrvIp) ? cm.get(defaultDmcuTranscriptionSrvIpKey) : nodeTranscriptionSrvIp;

        String nodeTranscriptionSrvPort = cm.get(nodeTranscriptionSrvPortKey);
        this.transcriptionSrvPort = StringUtils.isBlank(nodeTranscriptionSrvPort) ? cm.get(defaultDmcuTranscriptionSrvPortKey) : nodeTranscriptionSrvPort;

        String nodeTranscriptionMixerSwitch = cm.get(nodeTranscriptionMixerSwitchKey);
        this.transcriptionMixerSwitch = StringUtils.isBlank(nodeTranscriptionMixerSwitch) ? cm.get(defaultDmcuTranscriptionMixerSwitchKey) : nodeTranscriptionMixerSwitch;

        String instanceNum = cm.get(nodeName + DmcuConstants.INSTANCE_NUM);
        String defaultInstanceNum = StringUtils.isEmpty(this.instanceNum) ? "1" : this.instanceNum;
        this.instanceNum = StringUtils.isBlank(instanceNum) ? defaultInstanceNum : instanceNum;

        String natForceInCm = cm.get(nodeName + DmcuConstants.NAT_FORCE);
        this.natForce = StringUtils.isEmpty(natForceInCm) ? "false" : natForceInCm;

        //LOCAL_PEER_IP_EXPAND扩展-20240821媒体级联会议
        String localPeerIpExpandInCm = cm.get(nodeName + DmcuConstants.LOCAL_PEER_IP_EXPAND);
        this.localPeerIpExpand = StringUtils.isEmpty(localPeerIpExpandInCm) ? "[]" : localPeerIpExpandInCm;

        String enableRecvTaskInCm = cm.get(nodeName + DmcuConstants.RECV_TASK);
        this.enableRecvTask = StringUtils.isEmpty(enableRecvTaskInCm) ? "false" : enableRecvTaskInCm;
        String nodeThriftInfo = cm.get(nodeName + DmcuConstants.NODE_THRIFT_INFO);
        JsonMapper mapper = JsonMapper.nonEmptyMapper();
        List<DmcuThriftDto> list = mapper.fromJson(nodeThriftInfo, mapper.contructCollectionType(List.class, DmcuThriftDto.class));
        this.dmcuThriftInfoList = null == list ? new ArrayList<>(0) : list;
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {

        Map<String, String> cm = new HashMap<>();

        String siteCodeKey = this.nodeName + DmcuConstants.SITECODE;
        String commentsKey = this.nodeName + DmcuConstants.COMMENTS;
        String maxRxKey = this.nodeName + DmcuConstants.MAXRXBW;
        String maxTxKey = this.nodeName + DmcuConstants.MAXTXBW;
        String upThriftKey = this.nodeName + DmcuConstants.UP_THRIFT_IP;
        String upThriftKey2 = this.nodeName + DmcuConstants.UP_THRIFT_IP_2;
        String selfThrifyKey = this.nodeName + DmcuConstants.SELF_THRIFT_IP;
        String processTaskNumKey = this.nodeName + DmcuConstants.PROCESS_TASKNUM;
        String epnumPertaskKey = this.nodeName + DmcuConstants.EPNUM_PERTASK;
        String clientPortKey = this.nodeName + DmcuConstants.DMCU_CLIENT_PORT;
        String resourceTypeKey = this.nodeName + DmcuConstants.DMCU_RESOURCE_TYPE;
        String confTypeKey = this.nodeName + DmcuConstants.DMCU_CONF_TYPE;
        String dmcuTypeKey = this.nodeName + DmcuConstants.DMCU_TYPE;
        String nodeUpHookPortKey = this.nodeName + DmcuConstants.DMCU_SIDE_NODE_UP_HOOK_PORT;
        String nodeSelfHookPortKey = this.nodeName + DmcuConstants.DMCU_SIDE_NODE_SELF_HOOK_PORT;
        String nodeUpHookExtKey = this.nodeName + DmcuConstants.DMCU_SIDE_NODE_UP_HOOK_EXT;
        String nodeSelfHookExtKey = this.nodeName + DmcuConstants.DMCU_SIDE_NODE_SELF_HOOK_EXT;
        String maxWaitTimeForVideoMixerKey = this.nodeName + DmcuConstants.MAX_WAIT_TIME_FOR_VIDEO_MIXER;
        String localPeerIpExpandKey = this.nodeName + DmcuConstants.LOCAL_PEER_IP_EXPAND;

        cm.put(nodeUpHookPortKey, Strings.isBlank(this.nodeUpHookPort) ? Strings.EMPTY : this.nodeUpHookPort);
        cm.put(nodeSelfHookPortKey, Strings.isBlank(this.nodeSelfHookPort) ? Strings.EMPTY : this.nodeSelfHookPort);
        cm.put(nodeUpHookExtKey, Strings.isBlank(this.nodeUpHookExt) ? Strings.EMPTY : this.nodeUpHookExt);
        cm.put(nodeSelfHookExtKey, Strings.isBlank(this.nodeSelfHookExt) ? Strings.EMPTY : this.nodeSelfHookExt);
        cm.put(commentsKey, this.dmcuComments);
        cm.put(siteCodeKey, StringUtils.isNotEmpty(this.siteCode) ? this.siteCode : DmcuConstants.SITECODE_DEFAULT);
        cm.put(maxRxKey, StringUtils.isNotEmpty(this.maxRxBandWidth) ? this.maxRxBandWidth : DmcuConstants.MAXRXBW_DEFAULT);
        cm.put(maxTxKey, StringUtils.isNotEmpty(this.maxTxBandWidth) ? this.maxTxBandWidth : DmcuConstants.MAXTXBW_DEFAULT);
        cm.put(upThriftKey, StringUtils.isNotEmpty(this.upThriftIp) ? this.upThriftIp : DmcuConstants.IP_DEFAULT);
        cm.put(upThriftKey2, this.upThriftIp2);
        cm.put(selfThrifyKey, StringUtils.isNotEmpty(this.selfThriftIp) ? this.selfThriftIp : DmcuConstants.IP_DEFAULT);
        cm.put(this.nodeName + DmcuConstants.SELF_THRIFT_IP_2, StringUtils.isNotEmpty(this.selfThriftIp2) ? this.selfThriftIp2 : DmcuConstants.IP_DEFAULT);
        cm.put(this.nodeName + DmcuConstants.SELF_THRIFT_PORT, StringUtils.isNotEmpty(this.selfThriftPort) ? this.selfThriftPort : "9090");
        cm.put(this.nodeName + DmcuConstants.SELF_THRIFT_PORT_2, StringUtils.isNotEmpty(this.selfThriftPort2) ? this.selfThriftPort2 : "9091");
        cm.put(this.nodeName + DmcuConstants.CLOUDID, StringUtils.isEmpty(this.cascadableCloudId) ? DmcuConstants.CLOUDID_DEFAULT : this.cascadableCloudId);

        cm.put(processTaskNumKey, StringUtils.isNotEmpty(this.mediaProcessTaskNum) ? this.mediaProcessTaskNum : DmcuConstants.PROCESS_TASKNUM_DEFAULT);
        cm.put(epnumPertaskKey, StringUtils.isNotEmpty(this.mediaEndpointNumPerMediaTask) ? this.mediaEndpointNumPerMediaTask : DmcuConstants.EPNUM_PERTASK_DEFAULT);
        cm.put(clientPortKey, StringUtils.isNotEmpty(this.dmcuClientPort) ? this.dmcuClientPort : DmcuConstants.DMCU_CLIENT_PORT_DEFAULT);
        cm.put(nodeName + DmcuConstants.KAFKA_IP, IpUtils.getKafkaIp(this.kafkaIp));
        cm.put(nodeName + DmcuConstants.ENCODE_QP_ADJUST, StringUtils.isBlank(this.encodeQPAdjust) ? DmcuConstants.ENCODE_QP_ADJUST_DEFAULT : this.encodeQPAdjust);
        cm.put(nodeName + DmcuConstants.TRANS_SCORE_CPU_COEFFICIENT, StringUtils.isBlank(this.transScoreCpuCoefficient) ? DmcuConstants.TRANS_SCORE_CPU_COEFFICIENT_DEFAULT : this.transScoreCpuCoefficient);

        cm.put(nodeName + DmcuConstants.PEER_INTERNAL_IP, this.peerInternalIp);
        cm.put(nodeName + DmcuConstants.PEER_PUBLIC_IP, this.peerPublicIp);
        cm.put(nodeName + DmcuConstants.PEER_PORT, StringUtils.isNotEmpty(this.peerPort) ? this.peerPort : DmcuConstants.DMCU_CLIENT_PORT_DEFAULT);
        cm.put(nodeName + DmcuConstants.DMCU_CLIENT_IN_PORT, StringUtils.isNotEmpty(this.dmcuClientInPort) ? this.dmcuClientInPort : DmcuConstants.DMCU_CLIENT_PORT_DEFAULT);

        cm.put(nodeName + DmcuConstants.DMCU_INTERNAL_IPV4, this.dmcuInternalIpv4);
        cm.put(nodeName + DmcuConstants.DMCU_PUBLIC_IPV4, this.dmcuPublicIpv4);
        cm.put(nodeName + DmcuConstants.DMCU_INTERNAL_IPV6, this.dmcuInternalIpv6);
        cm.put(nodeName + DmcuConstants.DMCU_PUBLIC_IPV6, this.dmcuPublicIpv6);
        cm.put(nodeName + DmcuConstants.DMCU_CLIENT_IPV6_PORT, this.dmcuClientIpv6Port);

        cm.put(nodeName + DmcuConstants.TRANSCRIPTION_SRV_IP, StringUtils.isNotEmpty(this.transcriptionSrvIp) ? this.transcriptionSrvIp : DmcuConstants.DEFAULT_TRANSCRIPTION_SRV_IP);
        cm.put(nodeName + DmcuConstants.TRANSCRIPTION_SRV_PORT, StringUtils.isNotEmpty(this.transcriptionSrvPort) ? this.transcriptionSrvPort : DmcuConstants.DEFAULT_TRANSCRIPTION_SRV_PORT);
        cm.put(nodeName + DmcuConstants.TRANSCRIPTION_MIXER_SWITCH, StringUtils.isNotEmpty(this.transcriptionMixerSwitch) ? this.transcriptionMixerSwitch : DmcuConstants.DEFAULT_TRANSCRIPTION_MIXER_SWITCH);

        cm.put(dmcuTypeKey, StringUtils.isNotEmpty(this.dmcuType) ? this.dmcuType : DmcuConstants.DMCU_TYPE_DEFAULT);
        if (DmcuConstants.DMCU_TYPE_TRANSCODING.equalsIgnoreCase(this.dmcuType)) {
            cm.put(resourceTypeKey, DmcuConstants.DMCU_TRANSCODING_RESOURCE_TYPE_DEFAULT);
            cm.put(confTypeKey, DmcuConstants.DMCU_TRANSCODING_CONF_TYPE_DEFAULT);
        } else if (DmcuConstants.DMCU_TYPE_ALL_TRANSCODING.equalsIgnoreCase(this.dmcuType)) {
            cm.put(resourceTypeKey, DmcuConstants.DMCU_ALL_TRANSCODING_RESOURCE_TYPE_DEFAULT);
            cm.put(confTypeKey, DmcuConstants.DMCU_ALL_TRANSCODING_CONF_TYPE_DEFAULT);
        } else if (DmcuConstants.DMCU_TYPE_GATHER.equalsIgnoreCase(this.dmcuType)) {
            cm.put(resourceTypeKey, DmcuConstants.DMCU_GATHER_RESOURCE_TYPE_DEFAULT);
            cm.put(confTypeKey, DmcuConstants.DMCU_GATHER_CONF_TYPE_DEFAULT);
        } else if (DmcuConstants.DMCU_TYPE_SIMULTANEOUS.equalsIgnoreCase(this.dmcuType)) {
            cm.put(resourceTypeKey, DmcuConstants.DMCU_SIMULTANEOUS_RESOURCE_TYPE_DEFAULT);
            cm.put(confTypeKey, DmcuConstants.DMCU_SIMULTANEOUS_CONF_TYPE_DEFAULT);
        } else if (DmcuConstants.DMCU_TYPE_PUSH_FLOW.equalsIgnoreCase(this.dmcuType)) {
            cm.put(resourceTypeKey, DmcuConstants.DMCU_PUSH_FLOW_RESOURCE_TYPE_DEFAULT);
            cm.put(confTypeKey, DmcuConstants.DMCU_PUSH_FLOW_CONF_TYPE_DEFAULT);
        } else {
            cm.put(resourceTypeKey, DmcuConstants.DMCU_RESOURCE_TYPE_DEFAULT);
            cm.put(confTypeKey, DmcuConstants.DMCU_CONF_TYPE_DEFAULT);
        }

        if (StringUtils.isBlank(this.maxWaitTimeForVideoMixer)) {
            cm.put(maxWaitTimeForVideoMixerKey, DmcuConstants.DEFAULT_MAX_WAIT_TIME_FOR_VIDEO_MIXER);
        } else if (Integer.parseInt(this.maxWaitTimeForVideoMixer) < 0 || Integer.parseInt(this.maxWaitTimeForVideoMixer) > 10000) {
            throw new ClientErrorException(ErrorStatus.PARAM_ERROR);
        } else {
            cm.put(maxWaitTimeForVideoMixerKey, this.maxWaitTimeForVideoMixer);
        }

        cm.put(this.nodeName + DmcuConstants.DMCU_SIDE_NODE_UP_HOOK_PORT, this.upThriftHookPort);
        cm.put(this.nodeName + DmcuConstants.DMCU_SIDE_NODE_UP_HOOK_EXT, this.upThriftHookExt);
        cm.put(this.nodeName + DmcuConstants.DMCU_SIDE_NODE_SELF_HOOK_PORT, this.selfThriftHookPort);
        cm.put(this.nodeName + DmcuConstants.DMCU_SIDE_NODE_SELF_HOOK_EXT, this.selfThriftHookExt);
        cm.put(this.nodeName + DmcuConstants.DMCU_SIDE_NODE_UP_HOOK_PORT_2, this.upThriftHookPort2);
        cm.put(this.nodeName + DmcuConstants.DMCU_SIDE_NODE_UP_HOOK_EXT_2, this.upThriftHookExt2);
        cm.put(this.nodeName + DmcuConstants.DMCU_SIDE_NODE_SELF_HOOK_PORT_2, this.selfThriftHookPort2);
        cm.put(this.nodeName + DmcuConstants.DMCU_SIDE_NODE_SELF_HOOK_EXT_2, this.selfThriftHookExt2);

        cm.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.useTransportProxy);
        cm.put(nodeName + DmcuConstants.NMST_ENABLE_4K, this.enable4K);

        cm.put(this.nodeName + DmcuConstants.MULTI_FULL_TRANS323_EP_CAPACITY, StringUtils.isNotEmpty(this.multiFullTrans323EpCapacity) ? this.multiFullTrans323EpCapacity : "0");
        cm.put(this.nodeName + DmcuConstants.MULTI_FULL_TRANS_USAGE_LIMIT_PERCENTAGE, StringUtils.isNotEmpty(this.multiFullTransUsageLimitPercentage) ? this.multiFullTransUsageLimitPercentage : "80");
        cm.put(this.nodeName + DmcuConstants.FULL_TRANS_ALLOCATE_SSRC_PRIORITY63, StringUtils.isNotEmpty(this.fullTransAllocateSsrcPriority63) ? this.fullTransAllocateSsrcPriority63 : "true");
        cm.put(this.nodeName + DmcuConstants.FULL_TRANS_ALLOCATE_SSRC_ONLY_USE_LOW63, StringUtils.isNotEmpty(this.fullTransAllocateSsrcOnlyUseLow63) ? this.fullTransAllocateSsrcOnlyUseLow63 : "false");
        cm.put(this.nodeName + DmcuConstants.FULL_TRANS_ALLOCATE_SSRC_AUDIO_SSRC_NUM, StringUtils.isNotEmpty(this.fullTransAllocateSsrcAudioSsrcNum) ? this.fullTransAllocateSsrcAudioSsrcNum : "35");
        cm.put(this.nodeName + DmcuConstants.ENABLE_FULL_TRANS_OVERLOAD_PROTECTION, StringUtils.isNotEmpty(this.enableFullTransOverloadProtection) ? this.enableFullTransOverloadProtection : "true");
        cm.put(this.nodeName + DmcuConstants.FULL_TRANS_DOWN_LIMIT_RES, StringUtils.isNotEmpty(this.fullTransDownLimitRes) ? this.fullTransDownLimitRes : "720P");
        cm.put(this.nodeName + DmcuConstants.FULL_TRANS_DOWN_LIMIT_FPS, StringUtils.isNotEmpty(this.fullTransDownLimitFps) ? this.fullTransDownLimitFps : "1500");
        cm.put(this.nodeName + DmcuConstants.FULL_TRANS_ALL_SCORE, StringUtils.isNotEmpty(this.fullTransAllScore) ? this.fullTransAllScore : "0");
        cm.put(this.nodeName + DmcuConstants.INSTANCE_NUM, StringUtils.isNotEmpty(this.instanceNum) ? this.instanceNum : "1");
        cm.put(this.nodeName + DmcuConstants.NAT_FORCE, StringUtils.isNotEmpty(this.natForce) ? this.natForce : "false");
        cm.put(localPeerIpExpandKey, StringUtils.isNotEmpty(this.localPeerIpExpand) ? this.localPeerIpExpand : DmcuConstants.LOCAL_PEER_IP_EXPAND_DEFAULT);
        cm.put(this.nodeName + DmcuConstants.RECV_TASK, StringUtils.isNotEmpty(this.enableRecvTask) ? this.enableRecvTask : "false");
        cm.put(this.nodeName + DmcuConstants.NODE_THRIFT_INFO, JsonMapper.nonDefaultMapper().toJson(dmcuThriftInfoList));
        return cm;
    }


    @Override
    public DmcuCM setDefault(String nodeName) {
        this.selfThriftIp = getDeployService().getNodeByName(nodeName).getIp();
        this.selfThriftIp2 = this.selfThriftIp;

        McUtils mcUtils = new McUtils();
        McUtils.UpThriftIp priorityMc = mcUtils.mcuDefaultUpThriftIp();
        if (priorityMc != null) {
            this.upThriftIp = priorityMc.getInternalIp();
        }
        this.enableRecvTask = "false";
        this.nodeName = nodeName;
        fillMediaParameters();
        return this;
    }

    @Override
    public DmcuCM setDefault(String nodeName, Map<String, String> cm) {
        this.dmcuType = cm.get(nodeName + DmcuConstants.DMCU_TYPE);
        return setDefault(nodeName);
    }

    protected void fillMediaParameters() {
        CalculateMediaParametersService.MediaParameters mediaParameters = calculateMediaParameters(this.nodeName, getMediaParametersServiceType());
        this.maxRxBandWidth = mediaParameters.getMaxRxBandWidth();
        this.maxTxBandWidth = mediaParameters.getMaxTxBandWidth();
        this.mediaProcessTaskNum = mediaParameters.getMediaProcessTaskNum();
        this.mediaEndpointNumPerMediaTask = mediaParameters.getMediaEndpointNumPerMediaTask();
        this.instanceNum = mediaParameters.getInstanceNum();
        fillSubclassProperty(mediaParameters);
    }

    protected void fillSubclassProperty(CalculateMediaParametersService.MediaParameters mediaParameters) {
    }

    @Override
    public void beforeSave() {
        ConfigMap configMap = getDeployService().getConfigMapByName(Constants.CONFIGMAP_DMCU, Constants.NAMESPACE_DEFAULT);
        if (Objects.isNull(configMap)) {
            return;
        }
        Map<String, String> map = configMap.getData();
        String allDmcuUseTransportProxy = map.get(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY);
        if (StringUtils.isNotBlank(this.useTransportProxy) && !this.useTransportProxy.equalsIgnoreCase(allDmcuUseTransportProxy)) {
            changeUseTransportProxy = true;
        }
    }

    @Override
    public void afterSave() {
        if (StringUtils.isNotBlank(this.useTransportProxy)) {
            try {
                getDeployService().patchConfigMapAllIp(d->{
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.useTransportProxy);
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }

            try {
                getDeployService().patchConfigMap(Constants.CONFIGMAP_NMSA, Constants.NAMESPACE_DEFAULT, d -> {
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.useTransportProxy);
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }

            try {
                getDeployService().patchConfigMap(Constants.CONFIGMAP_ALL_IVR, Constants.NAMESPACE_DEFAULT, d -> {
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.useTransportProxy);
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }

            try {
                getDeployService().patchConfigMap(Constants.CONFIGMAP_HLS, Constants.NAMESPACE_DEFAULT, d -> {
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.useTransportProxy);
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }


            try {
                getDeployService().patchConfigMap(Constants.CONFIGMAP_ALL_CONVERGED_MEDIAGW, Constants.NAMESPACE_DEFAULT, d -> {
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.useTransportProxy);
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }

            try {
                getDeployService().patchConfigMap(Constants.CONFIGMAP_MA, Constants.NAMESPACE_DEFAULT, d -> {
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.useTransportProxy);
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }

            try {
                getDeployService().patchConfigMap(Constants.CONFIGMAP_WEBRTC_MEDIAGW, Constants.NAMESPACE_DEFAULT, d -> {
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.useTransportProxy);
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }
        }
    }

    protected String  getMediaParametersServiceType(){
        // 新增默认值逻辑 需要根据节点资源配置来设置 maxRxBandWidth maxTxBandWidth mediaProcessTaskNum mediaEndpointNumPerMediaTask instanceNum
        return DmcuConstants.DMCU_TYPE_GATHER.equalsIgnoreCase(this.dmcuType)? "gathermcu" : "dmcu";
    }

}
