package com.xylink.manager.model.cm;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

@Setter
@Getter
public class McCm implements ICMDto<McCm> {
    private String nodeName;
    private String mcName;
    private String mcGroupId;

    /**
     * 光闸穿越加的配置，目前只有5.2信创生效
     */
    private String xytpLocalTransPort;
    private String xytpSeparateProcessPort;
    private String xytpEnableClientHook;

    private String nmstAllocationMode;

    @Getter(AccessLevel.PRIVATE)
    final private String NAME = "-MC_NAME";
    @Getter(AccessLevel.PRIVATE)
    final private String GROUP_ID = "-MC_GROUP_ID";

    @Override
    public McCm toModel(Map<String, String> cm, String nodeName) {
        String nameKey = nodeName + NAME;
        String groupKey = nodeName + GROUP_ID;
        String mcNameInCm = cm.get(nameKey);
        String mcGroupIdInCm = cm.get(groupKey);
        if (StringUtils.isNotBlank(mcNameInCm)){
            this.mcName = mcNameInCm;
        }
        if (StringUtils.isNotBlank(mcGroupIdInCm)){
            this.mcGroupId = mcGroupIdInCm;
        }
        String localTransport = cm.get("XYTP_LOCAL_TRANS_PORT");
        this.xytpLocalTransPort = StringUtils.isNotBlank(localTransport) ? localTransport : "0";
        String separatePort = cm.get("XYTP_SEPARATE_PORT");
        this.xytpSeparateProcessPort = StringUtils.isNotBlank(separatePort) ? separatePort : "0";
        String enableClientHook = cm.get("XYTP_ENABLE_CLIENTHOOK");
        this.xytpEnableClientHook = StringUtils.isNotBlank(enableClientHook) ? enableClientHook : "false";

        String nmstAllocationModeInCm = cm.get("NMST_NEARBY_ACCESS");
        this.nmstAllocationMode = StringUtils.isNotBlank(nmstAllocationModeInCm) ? nmstAllocationModeInCm : "true";

        this.nodeName = nodeName;

        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        String nameKey = this.nodeName + NAME;
        String groupKey = this.nodeName + GROUP_ID;
        cm.put(nameKey, StringUtils.isBlank(mcName) ? "prd-mc" : mcName);
        cm.put(groupKey, StringUtils.isBlank(mcGroupId) ? "private_ali_group1" : mcGroupId);

        cm.put("XYTP_LOCAL_TRANS_PORT", this.xytpLocalTransPort);
        cm.put("XYTP_SEPARATE_PORT", this.xytpSeparateProcessPort);
        cm.put("XYTP_ENABLE_CLIENTHOOK", this.xytpEnableClientHook);
        cm.put("NMST_NEARBY_ACCESS", this.nmstAllocationMode);

        return cm;
    }

    @Override
    public McCm setDefault(String nodeName) {
        McUtils mcUtils = new McUtils();
        String deployedNodeName = mcUtils.onlyOneMcNodeName();
        if (StringUtils.isBlank(deployedNodeName) || deployedNodeName.equals(nodeName)) {
            this.mcName = "prd-mc";
            this.mcGroupId = "private_ali_group1";
        }
        return this;
    }
}
