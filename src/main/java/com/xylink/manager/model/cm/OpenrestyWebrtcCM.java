package com.xylink.manager.model.cm;

import com.xylink.config.Constants;
import com.xylink.config.ProxyConstants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.WebException;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.util.NginxServerNameUtils;
import com.xylink.util.NginxValidUtils;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

@Setter
@Getter
public class OpenrestyWebrtcCM implements ICMDto<OpenrestyWebrtcCM> {

    private String gzip;
    private String proxy;
    private String nodeName;
    private String allowAccessFrom;
    private String serverName;
    private String allowAllHost;
    private String allowHttp;
    private String port;
    private String sslPort;
    private List<String> accessControlAllowOrigin;
    private String webrtcServerName;
    private String useIPV6;


    @Getter(AccessLevel.PRIVATE)
    final private String GZIP_KEY = "MAIN_NGINX_GZIP";
    @Getter(AccessLevel.PRIVATE)
    final private String PROXY_KEY = "ACCESS_PROXY";
    @Getter(AccessLevel.PRIVATE)
    final private String ALLOW_ACCESS_FROM_KEY = "ALLOW_ACCESS_FROM";
    @Getter(AccessLevel.PRIVATE)
    final private String SERVER_NAME_KEY = "-NIGNX_SERVER_NAME";
    @Getter(AccessLevel.PRIVATE)
    final private String ALLOW_ALL_HOST_KEY = "-ALLOW_ALL_HOST";
    @Getter(AccessLevel.PRIVATE)
    final private String ALLOW_HTTP_KEY = "-ALLOW_HTTP";
    @Getter(AccessLevel.PRIVATE)
    final private String NGINX_PORT_KEY = "WEBRTC_NGINX_PORT";
    @Getter(AccessLevel.PRIVATE)
    final private String NGINX_SSL_PORT_KEY = "WEBRTC_NGINX_SSL_PORT";
    @Getter(AccessLevel.PRIVATE)
    final private String WEBRTC_SERVER_NAME_KEY = "-WEBRTC_NGINX_SERVER_NAME";


    @Override
    public OpenrestyWebrtcCM toModel(Map<String, String> cm, String nodeName) {
        this.gzip = cm.get(GZIP_KEY);
        this.proxy = cm.get(PROXY_KEY);
        this.allowAccessFrom = cm.get(ALLOW_ACCESS_FROM_KEY);
        this.serverName = cm.get(nodeName + SERVER_NAME_KEY);
        this.nodeName = nodeName;
        this.allowHttp = cm.get(nodeName + ALLOW_HTTP_KEY);
        String nginxPort = cm.get(NGINX_PORT_KEY);
        this.port = StringUtils.isBlank(nginxPort) ? "80" : nginxPort;

        String nginxSslPort = cm.get(NGINX_SSL_PORT_KEY);
        this.sslPort = StringUtils.isBlank(nginxSslPort) ? "443" : nginxSslPort;

        String accessControlAllowOriginKey = nodeName + ProxyConstants.ACCESS_CONTROL_ALLOW_ORIGIN;
        String accessControlAllowOriginValue = cm.get(accessControlAllowOriginKey);
        this.accessControlAllowOrigin = NginxValidUtils.allowedOriginToArrary(accessControlAllowOriginValue);
        this.webrtcServerName = StringUtils.isBlank(cm.get(nodeName + WEBRTC_SERVER_NAME_KEY)) ? "localhost" : cm.get(nodeName + WEBRTC_SERVER_NAME_KEY);
        String nginxUseV6 = cm.get(nodeName + ProxyConstants.NGINX_USE_IPV6);
        this.useIPV6 = StringUtils.isBlank(nginxUseV6) ? "false" : nginxUseV6;
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        cm.put(GZIP_KEY, gzip);
        cm.put(PROXY_KEY, proxy);
        cm.put(ALLOW_ACCESS_FROM_KEY, allowAccessFrom);
        cm.put(nodeName + SERVER_NAME_KEY, StringUtils.isNotBlank(serverName) ? serverName : "localhost");
        cm.put(NGINX_PORT_KEY, StringUtils.isNotBlank(port) ? port : "80");
        cm.put(NGINX_SSL_PORT_KEY, StringUtils.isNotBlank(sslPort) ? sslPort : "443");
        cm.put(nodeName + ALLOW_ALL_HOST_KEY, (StringUtils.isNotBlank(serverName) && !"localhost".equalsIgnoreCase(serverName)) ? "false" : "true");
        cm.put("HTTP_PORT_KEY", "{NGINX_PORT}");
        cm.put("HTTPS_PORT_KEY", "{NGINX_SSL_PORT}");
        cm.put(nodeName + ALLOW_HTTP_KEY, allowHttp);


        cm.put(nodeName + ProxyConstants.ACCESS_CONTROL_ALLOW_ORIGIN, NginxValidUtils.allowedOriginToStr(this.accessControlAllowOrigin));

        cm.put(nodeName + WEBRTC_SERVER_NAME_KEY, StringUtils.isBlank(webrtcServerName) ? "localhost" : webrtcServerName);

        cm.put(nodeName + ProxyConstants.NGINX_USE_IPV6, StringUtils.isBlank(useIPV6) ? "false" : useIPV6);
        return cm;
    }

    @Override
    public void beforeSave() {
        if (StringUtils.isNotBlank(webrtcServerName) && !"localhost".equals(webrtcServerName)) {
            ConfigMap configMap = getDeployService().getConfigMapByNameNotNull("all-openresty-webrtc", Constants.NAMESPACE_DEFAULT);
            Map<String, String> configmap = configMap.getData();
            String domain = configmap.get(nodeName + "-DOMAIN");
            String publicIp = configmap.get(nodeName + "-PUBLIC-IP");
            String interIp = configmap.get(nodeName + "-INTERNAL-IP");

            String[] names = webrtcServerName.split(" ");
            Set<String> nameSet = new HashSet<>(Arrays.asList(names));
            if (StringUtils.isNotBlank(domain) && !nameSet.contains(domain)) {
                throw new WebException(ErrorStatus.MAIN_NGINX_SERVER_NAME_ERROR);
            }
            if (StringUtils.isNotBlank(publicIp) && !nameSet.contains(publicIp)) {
                throw new WebException(ErrorStatus.MAIN_NGINX_SERVER_NAME_ERROR);
            }
            if (StringUtils.isNotBlank(interIp) && !nameSet.contains(interIp)) {
                throw new WebException(ErrorStatus.MAIN_NGINX_SERVER_NAME_ERROR);
            }

            nameSet.remove(domain);
            nameSet.remove(publicIp);
            nameSet.remove(interIp);

            for (String name : nameSet) {
                if (!NginxServerNameUtils.isValid(name)) {
                    throw new WebException(ErrorStatus.MAIN_NGINX_SERVER_NAME_INVALID_ERROR);
                }
            }
        }

        // Allow Origin
        NginxValidUtils.checkAllowedOrigin(accessControlAllowOrigin);
    }

}
