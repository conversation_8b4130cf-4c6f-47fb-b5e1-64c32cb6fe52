package com.xylink.manager.model.cm;

import com.xylink.config.SigServerHaConfigMap;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

@Setter
@Getter
public class SignalCm implements ICMDto<SignalCm> {
    private String nodeName;
    private String domain;

    @Getter(AccessLevel.PRIVATE)
    final private String KEY_SUFFIX_DOMAIN = "-SIGSERVER-DOMAIN";
    @Getter(AccessLevel.PRIVATE)
    final private String KEY_SIGSERVER_DIMAIN_WORKING = "SIGSERVER-DIMAIN-WORKING";

    @Override
    public SignalCm toModel(Map<String, String> cm, String nodeName) {
        String domainKey = nodeName + KEY_SUFFIX_DOMAIN;
        this.domain = cm.get(domainKey);
        this.nodeName = nodeName;
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        String domainKey = nodeName + KEY_SUFFIX_DOMAIN;
        String value = StringUtils.isBlank(domain) ? "bj1" : domain;
        cm.put(domainKey, value);
        return cm;
    }

    @Override
    public SignalCm setDefault(String nodeName) {
        this.nodeName = nodeName;
        this.domain = "bj1";
        return this;
    }

    @Override
    public void afterSave() {
        SigServerHaConfigMap.reCountSigserverDimainWorking();
    }
}
