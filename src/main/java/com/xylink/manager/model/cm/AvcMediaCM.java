package com.xylink.manager.model.cm;

import com.xylink.config.DmcuConstants;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/06/16/15:39
 */
@Getter
@Setter
public class AvcMediaCM implements ICMDto<AvcMediaCM> {
    private String nodeName;
    private String siteCode;
    private String sipGatewayIp1;
    private String sipGatewayIp2;
    private String maxRxBandWidth;
    private String maxTxBandWidth;
    private String mediaProcessTaskNum;
    private String mediaEndpointNumPerMediaTask;
    private String kafkaIp;
    private String enableNATMedia;
    private String maPortStart;
    /**
     * 外网终端连接端口
     */
    private String dmcuClientPort;
    /**
     * 内网终端连接端口
     */
    private String dmcuClientInPort;
    /**
     * MCU级联端口
     */
    private String peerPort;

    @Override
    public AvcMediaCM toModel(Map<String, String> cm, String nodeName) {
        this.siteCode = cm.get(nodeName + DmcuConstants.SITECODE);
        this.maxRxBandWidth = cm.get(nodeName + DmcuConstants.MAXRXBW);
        this.maxTxBandWidth = cm.get(nodeName + DmcuConstants.MAXTXBW);
        this.mediaProcessTaskNum = cm.get(nodeName + DmcuConstants.PROCESS_TASKNUM);
        this.mediaEndpointNumPerMediaTask = cm.get(nodeName + DmcuConstants.EPNUM_PERTASK);
        this.kafkaIp = cm.get(nodeName + DmcuConstants.KAFKA_IP);
        this.dmcuClientPort = cm.get(nodeName + DmcuConstants.DMCU_CLIENT_PORT);
        this.dmcuClientInPort = cm.get(nodeName + DmcuConstants.DMCU_CLIENT_IN_PORT);
        this.peerPort = cm.get(nodeName + DmcuConstants.PEER_PORT);
        this.enableNATMedia = cm.get(nodeName + DmcuConstants.ENABLE_NAT_MEDIA);
        this.maPortStart = cm.get(nodeName + DmcuConstants.MA_PORT_START);

        this.sipGatewayIp1 = cm.get(nodeName + DmcuConstants.SIP_GATEWAY_IP_1);
        this.sipGatewayIp2 = cm.get(nodeName + DmcuConstants.SIP_GATEWAY_IP_2);
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> map = new HashMap<>();

        map.put(nodeName + DmcuConstants.SITECODE, StringUtils.isNotBlank(siteCode) ? siteCode : DmcuConstants.SITECODE_DEFAULT);
        map.put(nodeName + DmcuConstants.MAXRXBW, StringUtils.isNotBlank(maxRxBandWidth) ? maxRxBandWidth : DmcuConstants.MAXRXBW_DEFAULT);
        map.put(nodeName + DmcuConstants.MAXTXBW, StringUtils.isNotBlank(maxTxBandWidth) ? maxTxBandWidth : DmcuConstants.MAXTXBW_DEFAULT);
        map.put(nodeName + DmcuConstants.PROCESS_TASKNUM, StringUtils.isNotBlank(mediaProcessTaskNum) ? mediaProcessTaskNum : DmcuConstants.PROCESS_TASKNUM_SIP_DEFAULT);
        map.put(nodeName + DmcuConstants.EPNUM_PERTASK, StringUtils.isNotBlank(mediaEndpointNumPerMediaTask) ? mediaEndpointNumPerMediaTask : DmcuConstants.EPNUM_PERTASK_DEFAULT);

        map.put(nodeName + DmcuConstants.DMCU_CLIENT_PORT, StringUtils.isNotBlank(dmcuClientPort) ? dmcuClientPort : DmcuConstants.DMCU_CLIENT_PORT_DEFAULT);
        map.put(nodeName + DmcuConstants.DMCU_CLIENT_IN_PORT, StringUtils.isNotBlank(dmcuClientInPort) ? dmcuClientInPort : DmcuConstants.DMCU_CLIENT_PORT_DEFAULT);
        map.put(nodeName + DmcuConstants.PEER_PORT, StringUtils.isNotBlank(peerPort) ? peerPort : DmcuConstants.DMCU_CLIENT_PORT_DEFAULT);
        map.put(nodeName + DmcuConstants.ENABLE_NAT_MEDIA, StringUtils.isNotBlank(enableNATMedia) ? enableNATMedia : DmcuConstants.DEFAULT_ENABLE_NAT_MEDIA);
        map.put(nodeName + DmcuConstants.MA_PORT_START, StringUtils.isNotBlank(maPortStart) ? maPortStart : DmcuConstants.DEFAULT_MA_PORT_START);

        map.put(nodeName + DmcuConstants.SIP_GATEWAY_IP_1, sipGatewayIp1);
        map.put(nodeName + DmcuConstants.SIP_GATEWAY_IP_2, sipGatewayIp2);
        return map;
    }
}
