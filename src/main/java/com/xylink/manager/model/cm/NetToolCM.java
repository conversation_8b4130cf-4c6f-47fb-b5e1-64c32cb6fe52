package com.xylink.manager.model.cm;

import com.xylink.config.NetworkConstants;
import com.xylink.config.ProxyConstants;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/03/25/14:54
 */
@Setter
@Getter
public class NetToolCM implements ICMDto<NetToolCM> {
    /**
     * 客户端tcp访问端口
     */
    private String port;
    /**
     * node名称
     */
    private String nodeName;

    private String internalIpV4;
    private String externalIpV4;
    private String internalIpV6;
    private String externalIpV6;

    @Override
    public NetToolCM toModel(Map<String, String> cm, String nodeName) {
        String port = cm.get(nodeName + ProxyConstants.NET_TOOL_PORT);
        String internalIpV4 = cm.get(nodeName + NetworkConstants.SUFFIX_INTERNAL_IP_V4);
        String externalIpV4 = cm.get(nodeName + NetworkConstants.SUFFIX_PUBLIC_IP_V4);
        String internalIpV6 = cm.get(nodeName + NetworkConstants.SUFFIX_INTERNAL_IP_V6);
        String externalIpV6 = cm.get(nodeName + NetworkConstants.SUFFIX_PUBLIC_IP_V6);
        this.nodeName = nodeName;
        this.port = StringUtils.isBlank(port) ? ProxyConstants.NET_TOOL_DEFAULT_PORT : port;
        this.internalIpV4 = StringUtils.isBlank(internalIpV4) ? "" : internalIpV4;
        this.externalIpV4 = StringUtils.isBlank(externalIpV4) ? "" : externalIpV4;
        this.internalIpV6 = StringUtils.isBlank(internalIpV6) ? "" : internalIpV6;
        this.externalIpV6 = StringUtils.isBlank(externalIpV6) ? "" : externalIpV6;
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>(1);
        cm.put(nodeName + ProxyConstants.NET_TOOL_PORT, StringUtils.isBlank(port) ? ProxyConstants.NET_TOOL_DEFAULT_PORT : port);
        cm.put(nodeName + NetworkConstants.SUFFIX_INTERNAL_IP_V4, StringUtils.isBlank(internalIpV4) ? "" : internalIpV4);
        cm.put(nodeName + NetworkConstants.SUFFIX_PUBLIC_IP_V4, StringUtils.isBlank(externalIpV4) ? "" : externalIpV4);
        cm.put(nodeName + NetworkConstants.SUFFIX_INTERNAL_IP_V6, StringUtils.isBlank(internalIpV6) ? "" : internalIpV6);
        cm.put(nodeName + NetworkConstants.SUFFIX_PUBLIC_IP_V6, StringUtils.isBlank(externalIpV6) ? "" : externalIpV6);
        return cm;
    }

}
