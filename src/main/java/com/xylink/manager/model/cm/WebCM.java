package com.xylink.manager.model.cm;

import com.xylink.config.WebConstants;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

@Setter
@Getter
public class WebCM implements ICMDto<WebCM> {

    private String enterpriseID;
    private String clientID;
    private String clientSecret;
    private String enableWithoutLogin;

    /**
     * 小鱼账号登录,1打开,0关闭,默认1
     */
    private String enableXYAccountLogin;

    /**
     * 短信验证码登录,1打开,0关闭,默认0
     */
    private String enableSMSLogin;

    @Override
    public WebCM toModel(Map<String, String> cm, String nodeName) {

        this.enterpriseID = cm.get(WebConstants.ENTERPRISE_ID);
        this.clientID = cm.get(WebConstants.CLIENT_ID);
        this.clientSecret = cm.get(WebConstants.CLIENT_SECRET);
        this.enableWithoutLogin = cm.get(WebConstants.ENABLE_WITHOUT_LOGIN);

        String xyAccountLogin = cm.get(WebConstants.ENABLE_XYACCOUNT_LOGIN);
        this.enableXYAccountLogin = StringUtils.isBlank(xyAccountLogin) ? "1" : xyAccountLogin;

        String smsLogin = cm.get(WebConstants.ENABLE_SMS_LOGIN);
        this.enableSMSLogin = StringUtils.isBlank(smsLogin) ? "0" : smsLogin;
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> configmap = new HashMap<>();

        configmap.put(WebConstants.ENTERPRISE_ID, this.enterpriseID);
        configmap.put(WebConstants.CLIENT_ID, this.clientID);
        configmap.put(WebConstants.CLIENT_SECRET, this.clientSecret);
        configmap.put(WebConstants.ENABLE_WITHOUT_LOGIN, StringUtils.isBlank(this.enableWithoutLogin) ? "0" : this.enableWithoutLogin);

        configmap.put(WebConstants.ENABLE_XYACCOUNT_LOGIN, StringUtils.isBlank(this.enableXYAccountLogin) ? "1" : enableXYAccountLogin);
        configmap.put(WebConstants.ENABLE_SMS_LOGIN, StringUtils.isBlank(this.enableSMSLogin) ? "0" : enableSMSLogin);
        return configmap;
    }
}
