package com.xylink.manager.model.cm;

import com.xylink.config.MysqlConstants;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/8/31 6:14 下午
 */
@Data
public class MysqlCM implements ICMDto<MysqlCM> {
    private String mode;
    private String nodeName;


    @Override
    public MysqlCM toModel(Map<String, String> cm, String nodeName) {
        String modeInConfigMap = cm.get(nodeName + MysqlConstants.MYSQL_MODE_KEY);
        if (StringUtils.isNotBlank(modeInConfigMap)){
            this.mode = modeInConfigMap;
        }
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        cm.put(nodeName + MysqlConstants.MYSQL_MODE_KEY, this.mode);
        return cm;
    }

    @Override
    public MysqlCM setDefault(String nodeName) {
        this.nodeName = nodeName;
        this.mode = "OFF";
        return this;
    }
}
