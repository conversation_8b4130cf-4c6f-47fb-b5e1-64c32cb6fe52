package com.xylink.manager.model.cm;

import com.xylink.manager.model.em.CloudMeetingRoomEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
public class CloudMeetingRoomCM implements ICMDto<CloudMeetingRoomCM> {

    private String randomEnable;

    private String nodeName;

    @Override
    public CloudMeetingRoomCM toModel(Map<String, String> cm, String nodeName) {
        this.setNodeName(nodeName);
        this.setRandomEnable(cm.get(CloudMeetingRoomEnum.CONFERENCE_ENABLE_RANDOM.getValue()));
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {

        Map<String, String> cm = new HashMap<>();
        cm.put(CloudMeetingRoomEnum.CONFERENCE_ENABLE_RANDOM.getValue(), randomEnable);
        return cm;
    }
}
