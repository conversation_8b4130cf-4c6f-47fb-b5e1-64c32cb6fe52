package com.xylink.manager.model.cm;

import com.xylink.config.Constants;
import com.xylink.config.ProxyConstants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.WebException;
import com.xylink.manager.iptables.service.RefreshNginxPortService;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.service.ConfigService;
import com.xylink.manager.service.api.NoahApiService;
import com.xylink.util.NginxServerNameUtils;
import com.xylink.util.NginxValidUtils;
import com.xylink.util.SpringBeanUtil;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

@Setter
@Getter
@Slf4j
public class MainProxyCM implements ICMDto<MainProxyCM> {
    private static final String DEFAULT_ACCESS_CONTROL_ALLOW_METHODS = "GET,POST,PUT,DELETE,OPTIONS,PATCH";

    private String gzip;
    private String proxy;

    private String nginxPort;
    private String nginxSSLPort;
    private String remoteServerAddress;
    private String remoteServerDomain;
    private String remoteNginxPort;
    private String remoteNginxSSLPort;
    private String useDomainFilter;
    private String useIPV6;
    private String proxyPairHostname;

    private String allowManager;
    private String allowConsole;
    private String allowAppDownLoad;

    private String serverName;
    private String allowAllHost;

    private String nodeName;
    private String type;
    private String allowHttp;

    /**
     * cdn地址，配置后会将下载的流量转到对应的cdn上
     */
    private String cdnUrl;
    /**
     * cdn配置后，支持转发的类型
     */
    private String supportPlatform;
    /**
     * 限制并发
     */
    private String limitConn;
    /**
     * 限制每个连接的带宽
     */
    private String limitBroadband;
    /**
     * 内网IP段 用于nginx标记客户端请求来源
     */
    private String intranetAddressSegment;

    /**
     * 直播探测地址，ip1:port1,ip2:port2
     */
    private String liveProbeList;

    /**
     * 互联网链路接入限制标志 true: 互联网接入入口 false:内网接入入口
     */
    private String intranetProxy;
    /**
     * testlink地址是否允许客户端访问，默认值为允许
     */
    private String allowInnerTestLinkAddrAccess;
    /**
     * nettool服务ip地址
     */
    private String publicNetToolIp;
    /**
     * 禁止访问和下载历史会议资料
     */
    private String forbidAccessAndDownLoadMeetingRecord;
    /**
     * 是否允许h323注册 默认值允许
     */
    private String allowH323Login;
    /**
     * 允许指定型号终端使用 为空：所有终端 终端型号取自：header：n-ua MO字段 多个终端;隔开
     */
    private String allowDeviceInNuaAccess;
    /**
     * 允许的http请求方式，开关打开后其他请求方式返回405
     */
    private String allowHttpMethods;

    private String logUploadMaxConnCount;

    /**
     * 代理console&manager端口
     */
    private String consoleHttpPort;

    /**
     * 代理console&manager加密端口
     */
    private String consoleHttpsPort;

    /**
     * 代理后台console&manager端口
     */
    private String remoteConsoleHttpPort;

    /**
     * 代理后台console&manager加密端口
     */
    private String remoteConsoleHttpsPort;

    @Getter(AccessLevel.PRIVATE)
    final private String LOGUPLOAD_MAX_CONN_COUNT = "-LOGUPLOAD-MAX-CONN-COUNT";

    /**
     * cdn加速域名替换配置，配置后会将下载的流量转到对应的cdn上（不会替换所有层级目录）
     */
    private String cdnDomainRep;

    private String remoteInternalServerAddress;
    private String remoteInternalNginxPort;
    private String sdkIpWhiteList;
    private String amsHttpPort;
    private String amsHttpsPort;
    private String accessControlAllowMethods;
    /**
     * waf 配置
     */
    private String wafEnable;
    private String wafMode;
    private String sdkSign;
    private String thirdBridgeSwitch;
    /**
     * 跨域配置
     */
    private List<String> accessControlAllowOrigin;


    @Override
    public MainProxyCM toModel(Map<String, String> cm, String nodeName) {


        this.gzip = cm.get(nodeName + ProxyConstants.GZIP_KEY);
        this.proxy = cm.get(nodeName + ProxyConstants.PROXY_KEY);

        this.nginxPort = cm.get(nodeName + ProxyConstants.NGINX_PORT);
        this.nginxSSLPort = cm.get(nodeName + ProxyConstants.NGINX_SSL_PORT);
        this.remoteServerAddress = cm.get(nodeName + ProxyConstants.UPSTREAM_ADDR);
        this.remoteServerDomain = cm.get(nodeName + ProxyConstants.UPSTREAM_DOMAIN);
        this.remoteNginxPort = cm.get(nodeName + ProxyConstants.UPSTREAM_PORT);
        this.remoteNginxSSLPort = cm.get(nodeName + ProxyConstants.UPSTREAM_SSL_PORT);
        this.useDomainFilter = cm.get(nodeName + ProxyConstants.USE_PROXY_FILTER);
        this.useIPV6 = cm.get(nodeName + ProxyConstants.NGINX_USE_IPV6);
        this.proxyPairHostname = cm.get(nodeName + ProxyConstants.PAIR_HOSTNAME);

        this.allowManager = cm.get(nodeName + ProxyConstants.ALLOW_MANAGER);
        this.allowConsole = cm.get(nodeName + ProxyConstants.ALLOW_CONSOLE);
        this.allowAppDownLoad = cm.get(nodeName + ProxyConstants.ALLOW_APP_DOWNLOAD);

        this.serverName = cm.get(nodeName + ProxyConstants.SERVER_NAME_KEY);
        this.allowHttp = cm.get(nodeName + ProxyConstants.ALLOW_HTTP_KEY);
        this.nodeName = nodeName;

        this.cdnUrl = cm.get(nodeName + ProxyConstants.CDN_URL);
        this.limitConn = cm.get(nodeName + ProxyConstants.LIMIT_CONN);
        this.limitBroadband = cm.get(nodeName + ProxyConstants.LIMIT_BROADBAND);

        String supportPlatform = cm.get(nodeName + ProxyConstants.SUPPORT_PLATFORM);
        this.supportPlatform = StringUtils.isBlank(supportPlatform) ? "" : supportPlatform.replace("|android_office", "").replace("|ios_office", "").replace("|", ",");

        String serverConf = cm.get(nodeName + ProxyConstants.SERVER_CONF);
        if (StringUtils.isBlank(serverConf)) {
            this.type = "forward";
        } else if (serverConf.contains("include#customer/ssl_https_conf;")) {
            this.type = "distribute";
        } else {
            this.type = "forward";
        }

        String sdkSign = cm.get(nodeName + ProxyConstants.MAIN_NGINX_SDK_SIGN);
        this.sdkSign = StringUtils.isBlank(sdkSign) ? "on" : sdkSign;

        this.amsHttpPort = cm.get(nodeName + "-" + ProxyConstants.AMS_HTTP_PORT);
        this.amsHttpsPort = cm.get(nodeName + "-" + ProxyConstants.AMS_HTTPS_PORT);

        this.intranetAddressSegment = cm.get(nodeName + ProxyConstants.INTRANET_ADDRESS_SEGMENT);

        this.liveProbeList = cm.get(nodeName + ProxyConstants.LIVE_PROBE);

        this.intranetProxy = cm.get(nodeName + ProxyConstants.INTRANET_PROXY);

        this.allowInnerTestLinkAddrAccess = cm.get(nodeName + ProxyConstants.ALLOW_INNER_TEST_LINK_ADDR_ACCESS);
        String publicNetToolIp = cm.get(nodeName + ProxyConstants.PUBLIC_NET_TOOL_IP);
        this.publicNetToolIp = StringUtils.isBlank(publicNetToolIp) ? StringUtils.EMPTY : publicNetToolIp;
        this.allowH323Login = cm.get(nodeName + ProxyConstants.ALLOW_H323_LOGIN);
        this.allowDeviceInNuaAccess = cm.get(nodeName + ProxyConstants.ALLOW_DEVICEINNUA_ACCESS);
        this.remoteInternalServerAddress = cm.get(nodeName + ProxyConstants.INTERNAL_UPSTREAM_ADDR);
        this.remoteInternalNginxPort = cm.get(nodeName + ProxyConstants.INTERNAL_UPSTREAM_PORT);
        this.allowHttpMethods = cm.get(nodeName + ProxyConstants.NGINX_ALLOW_HTTP_METHODS);

        String logUploadMaxConnCountKey = nodeName + LOGUPLOAD_MAX_CONN_COUNT;
        String logUploadMaxConnCountValue = cm.get(logUploadMaxConnCountKey);
        this.logUploadMaxConnCount = StringUtils.isBlank(logUploadMaxConnCountValue) ? "10" : logUploadMaxConnCountValue;
        String forbidAccessAndDownLoadMeetingRecordInCm= cm.get(nodeName + ProxyConstants.FORBID_ACCESS_AND_DOWNLOAD_MEETING_RECORD);
        this.forbidAccessAndDownLoadMeetingRecord = StringUtils.isBlank(forbidAccessAndDownLoadMeetingRecordInCm) ? "false" : forbidAccessAndDownLoadMeetingRecordInCm;

        this.consoleHttpPort = cm.get(nodeName + ProxyConstants.CONSOLE_HTTP_PORT);
        this.consoleHttpsPort = cm.get(nodeName + ProxyConstants.CONSOLE_HTTPS_PORT);
        this.remoteConsoleHttpPort = cm.get(nodeName + ProxyConstants.REMOTE_CONSOLE_HTTP_PORT);
        this.remoteConsoleHttpsPort = cm.get(nodeName + ProxyConstants.REMOTE_CONSOLE_HTTPS_PORT);

        this.cdnDomainRep = cm.get(nodeName + ProxyConstants.CDN_DOMAIN_REP);
        this.sdkIpWhiteList = cm.get(nodeName + ProxyConstants.SDK_IP_WHITE_LIST);
        this.accessControlAllowMethods = StringUtils.isBlank(cm.get(nodeName + ProxyConstants.NGINX_ACCESS_CONTROL_ALLOW_METHODS)) ? DEFAULT_ACCESS_CONTROL_ALLOW_METHODS : cm.get(nodeName + ProxyConstants.NGINX_ACCESS_CONTROL_ALLOW_METHODS);
        this.wafEnable = StringUtils.isBlank(cm.get(ProxyConstants.WAF_ENABLE)) ? "off" : cm.get(ProxyConstants.WAF_ENABLE);
        this.wafMode = StringUtils.isBlank(cm.get(ProxyConstants.WAF_MODE)) ? "record" : cm.get(ProxyConstants.WAF_MODE);
        this.thirdBridgeSwitch = StringUtils.isBlank(cm.get(nodeName + Constants.THIRD_BRIDGE_SWITCH_KEY)) ? "false" : cm.get(nodeName + Constants.THIRD_BRIDGE_SWITCH_KEY);

        String accessControlAllowOriginValue = cm.get(nodeName + ProxyConstants.ACCESS_CONTROL_ALLOW_ORIGIN);
        this.accessControlAllowOrigin = NginxValidUtils.allowedOriginToArrary(accessControlAllowOriginValue);

        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        cm.put(nodeName + ProxyConstants.GZIP_KEY, gzip);
        cm.put(nodeName + ProxyConstants.PROXY_KEY, proxy);
        cm.put(nodeName + ProxyConstants.NGINX_PORT, nginxPort);
        cm.put(nodeName + ProxyConstants.NGINX_SSL_PORT, nginxSSLPort);
        cm.put(nodeName + ProxyConstants.UPSTREAM_ADDR, remoteServerAddress);
        cm.put(nodeName + ProxyConstants.UPSTREAM_DOMAIN, remoteServerDomain);
        cm.put(nodeName + ProxyConstants.UPSTREAM_PORT, remoteNginxPort);
        cm.put(nodeName + ProxyConstants.UPSTREAM_SSL_PORT, remoteNginxSSLPort);
        cm.put(nodeName + ProxyConstants.USE_PROXY_FILTER, useDomainFilter);
        cm.put(nodeName + ProxyConstants.NGINX_USE_IPV6, useIPV6);
        cm.put(nodeName + ProxyConstants.PAIR_HOSTNAME, proxyPairHostname);
        cm.put(nodeName + ProxyConstants.ALLOW_MANAGER, allowManager);
        cm.put(nodeName + ProxyConstants.ALLOW_CONSOLE, allowConsole);
        cm.put(nodeName + ProxyConstants.ALLOW_APP_DOWNLOAD, allowAppDownLoad);

        cm.put(nodeName + ProxyConstants.MAIN_NGINX_SDK_SIGN, StringUtils.isBlank(sdkSign) ? "on" : sdkSign);

        cm.put(nodeName + ProxyConstants.SERVER_NAME_KEY, StringUtils.isNotBlank(serverName) ? serverName : "localhost");
        cm.put(nodeName + ProxyConstants.ALLOW_ALL_HOST_KEY, (StringUtils.isNotBlank(serverName) && !"localhost".equalsIgnoreCase(serverName)) ? "false" : "true");
        cm.put("HTTP_PORT_KEY", "{MAIN_PROXY_PORT}");
        cm.put("HTTPS_PORT_KEY", "{MAIN_PROXY_SSL_PORT}");

        cm.put(nodeName + ProxyConstants.ALLOW_HTTP_KEY, allowHttp);
        cm.put(nodeName + ProxyConstants.LIVE_PROBE, liveProbeList);

        if (StringUtils.isBlank(cdnUrl)) {
            cm.put(nodeName + ProxyConstants.CDN_URL, null);
        } else {
            cm.put(nodeName + ProxyConstants.CDN_URL, cdnUrl.endsWith("/") ? cdnUrl : cdnUrl + "/");
        }
        cm.put(nodeName + ProxyConstants.LIMIT_CONN, limitConn);
        cm.put(nodeName + ProxyConstants.LIMIT_BROADBAND, limitBroadband);

        String resPlatform = "";
        if (StringUtils.isNotBlank(supportPlatform)) {
            resPlatform = supportPlatform.replace("android", "android,android_office").replace("ios", "ios,ios_office");
        }
        cm.put(nodeName + ProxyConstants.SUPPORT_PLATFORM, resPlatform.replace(",", "|"));

        if ("distribute".equals(type)) {
            cm.put(nodeName + ProxyConstants.SERVER_CONF, "false".equals(allowHttp) ?
                    "include#customer/ssl_https_conf;" :
                    "include#customer/ssl_https_conf;include#customer/http_conf;");
        } else {
            cm.put(nodeName + ProxyConstants.SERVER_CONF, "false".equals(allowHttp) ?
                    "include#customer/ssl_https_conf_forward;" :
                    "include#customer/ssl_https_conf_forward;include#customer/http_conf_forward;");
        }
        cm.put(nodeName + ProxyConstants.NGINX_TYPE, type);
        cm.put(nodeName + ProxyConstants.INTRANET_ADDRESS_SEGMENT, this.intranetAddressSegment);
        cm.put(nodeName + ProxyConstants.INTRANET_PROXY, this.intranetProxy);
        cm.put(nodeName + ProxyConstants.ALLOW_INNER_TEST_LINK_ADDR_ACCESS, this.allowInnerTestLinkAddrAccess);
        cm.put(nodeName + ProxyConstants.PUBLIC_NET_TOOL_IP, StringUtils.isBlank(publicNetToolIp) ? StringUtils.EMPTY : publicNetToolIp);
        cm.put(nodeName + ProxyConstants.ALLOW_H323_LOGIN, this.allowH323Login);
        cm.put(nodeName + ProxyConstants.ALLOW_DEVICEINNUA_ACCESS, this.allowDeviceInNuaAccess);
        cm.put(nodeName + ProxyConstants.INTERNAL_UPSTREAM_ADDR, this.remoteInternalServerAddress);
        cm.put(nodeName + ProxyConstants.INTERNAL_UPSTREAM_PORT, this.remoteInternalNginxPort);
        cm.put(nodeName + ProxyConstants.NGINX_ALLOW_HTTP_METHODS, this.allowHttpMethods);
        String logUploadMaxConnCountKey = nodeName + LOGUPLOAD_MAX_CONN_COUNT;
        cm.put(logUploadMaxConnCountKey, StringUtils.isBlank(this.logUploadMaxConnCount) ? "10" : this.logUploadMaxConnCount);
        cm.put(nodeName + ProxyConstants.FORBID_ACCESS_AND_DOWNLOAD_MEETING_RECORD, StringUtils.isBlank(forbidAccessAndDownLoadMeetingRecord) ? "false" : forbidAccessAndDownLoadMeetingRecord);

        cm.put(nodeName + ProxyConstants.CONSOLE_HTTP_PORT, this.consoleHttpPort);
        cm.put(nodeName + ProxyConstants.CONSOLE_HTTPS_PORT, this.consoleHttpsPort);
        cm.put(nodeName + ProxyConstants.REMOTE_CONSOLE_HTTP_PORT, this.remoteConsoleHttpPort);
        cm.put(nodeName + ProxyConstants.REMOTE_CONSOLE_HTTPS_PORT, this.remoteConsoleHttpsPort);

        if (StringUtils.isBlank(cdnDomainRep)) {
            cm.put(nodeName + ProxyConstants.CDN_DOMAIN_REP, null);
        } else {
            cm.put(nodeName + ProxyConstants.CDN_DOMAIN_REP, cdnDomainRep.endsWith("/") ? cdnDomainRep : cdnDomainRep + "/");
        }
        cm.put(nodeName + ProxyConstants.SDK_IP_WHITE_LIST, this.sdkIpWhiteList);
        cm.put(nodeName + "-" + ProxyConstants.ALLOW_ACCESS_AMS, "false");
        cm.put(nodeName + "-" + ProxyConstants.AMS_HTTP_PORT, this.amsHttpPort);
        cm.put(nodeName + "-" + ProxyConstants.AMS_HTTPS_PORT, this.amsHttpsPort);
        cm.put(nodeName + ProxyConstants.NGINX_ACCESS_CONTROL_ALLOW_METHODS, StringUtils.isBlank(accessControlAllowMethods) ? DEFAULT_ACCESS_CONTROL_ALLOW_METHODS : accessControlAllowMethods);
        cm.put(ProxyConstants.WAF_ENABLE, StringUtils.isBlank(wafEnable) ? "off" : wafEnable);
        cm.put(ProxyConstants.WAF_MODE, StringUtils.isBlank(wafMode) ? "record" : wafMode);
        cm.put(nodeName + Constants.THIRD_BRIDGE_SWITCH_KEY, StringUtils.isBlank(thirdBridgeSwitch) ? "false" : thirdBridgeSwitch);

        cm.put(nodeName + ProxyConstants.ACCESS_CONTROL_ALLOW_ORIGIN, NginxValidUtils.allowedOriginToStr(this.accessControlAllowOrigin));
        cm.put("main-proxy.svc.http_port", nginxPort);
        cm.put("main-proxy.svc.https_port", nginxSSLPort);
        //通知配置中心
        try {
            notifyToNoah(nginxPort, nginxSSLPort);
            notifyToIptables(nginxPort, nginxSSLPort);
            notifyToNoahConsole(consoleHttpPort, consoleHttpsPort, remoteConsoleHttpPort, remoteConsoleHttpsPort);
        } catch (Exception ex) {
            log.error("notify noah error:{}", ex.getMessage(), ex);
        }

        if (StringUtils.isBlank(this.amsHttpPort) && StringUtils.isBlank(this.amsHttpsPort)) {
            return cm;
        }
        try {
            ConfigService configService = SpringBeanUtil.getBean(ConfigService.class);
            if (StringUtils.isNotBlank(this.amsHttpPort) && StringUtils.isNotBlank(this.amsHttpsPort)) {
                cm.put(nodeName + "-" + ProxyConstants.ALLOW_ACCESS_AMS, "true");
                configService.saveBuffetConfig("AMS-http代理端口", "HTTP_CONFIG", this.amsHttpPort, this.amsHttpPort);
                configService.saveBuffetConfig("AMS-https代理端口", "HTTPS_CONFIG", this.amsHttpsPort, this.amsHttpsPort);
            } else if (StringUtils.isNotBlank(this.amsHttpPort)) {
                configService.saveBuffetConfig("AMS-http代理端口", "HTTP_CONFIG", this.amsHttpPort, this.amsHttpPort);
            } else if (StringUtils.isNotBlank(this.amsHttpsPort)) {
                configService.saveBuffetConfig("AMS-https代理端口", "HTTPS_CONFIG", this.amsHttpsPort, this.amsHttpsPort);
            }
        } catch (Exception e) {
            log.error("Ams database configuration fails and the data is inconsistent between configmap and db, please perform the operation again！！！");
        }
        return cm;
    }

    private void notifyToNoahConsole(String consoleHttpPort, String consoleHttpsPort, String remoteConsoleHttpPort, String remoteConsoleHttpsPort) {
        NoahApiService noahApiService = SpringBeanUtil.getBean(NoahApiService.class);
        Map<String, String> keyValues = new HashMap<>();
        keyValues.put("main-proxy.console_http_port", consoleHttpPort);
        keyValues.put("main-proxy.console_https_port", consoleHttpsPort);
        keyValues.put("main-proxy.remote_console_http_port", remoteConsoleHttpPort);
        keyValues.put("main-proxy.remote_console_https_port", remoteConsoleHttpsPort);

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("dataId", "var_env.platform.yml");
        paramMap.put("keyValues", keyValues);
        paramMap.put("publish", "true");
        noahApiService.notifyNginxPort(paramMap);
    }

    private void notifyToNoah(String nginxPort, String nginxSSLPort) {
        NoahApiService noahApiService = SpringBeanUtil.getBean(NoahApiService.class);
        Map<String, String> keyValues = new HashMap<>();
        keyValues.put("main-proxy.svc.http_port", nginxPort);
        keyValues.put("main-proxy.svc.https_port", nginxSSLPort);

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("dataId", "var_env.svc.yaml");
        paramMap.put("keyValues", keyValues);
        paramMap.put("publish", "true");
        noahApiService.notifyNginxPort(paramMap);
    }

    private void notifyToIptables(String nginxPort, String nginxSSLPort) {
        RefreshNginxPortService refreshNginxPortService = SpringBeanUtil.getBean(RefreshNginxPortService.class);
        Map<String, String> keyValues = new HashMap<>();
        keyValues.put("main-proxy.svc.http_port", nginxPort);
        keyValues.put("main-proxy.svc.https_port", nginxSSLPort);
        refreshNginxPortService.refreshProxyPort(keyValues);
    }


    @Override
    public void beforeSave() {
        if (StringUtils.isNotBlank(serverName) && !"localhost".equals(serverName)) {
            ConfigMap configMap = getDeployService().getConfigMapByName(Constants.CONFIGMAP_MAIN_PROXY, Constants.NAMESPACE_DEFAULT);
            Map<String, String> configmap = configMap != null ? configMap.getData() : new HashMap<>();

            String domain = configmap.get(nodeName + "-DOMAIN");
            String publicIp = configmap.get(nodeName + "-PUBLIC-IP");
            String interIp = configmap.get(nodeName + "-INTERNAL-IP");

            String[] names = serverName.split(" ");
            Set<String> nameSet = new HashSet<>(Arrays.asList(names));

            if (StringUtils.isNotBlank(domain) && !nameSet.contains(domain)) {
                throw new WebException(ErrorStatus.MAIN_NGINX_SERVER_NAME_ERROR);
            }
            if (StringUtils.isNotBlank(publicIp) && !nameSet.contains(publicIp)) {
                throw new WebException(ErrorStatus.MAIN_NGINX_SERVER_NAME_ERROR);
            }
            if (StringUtils.isNotBlank(interIp) && !nameSet.contains(interIp)) {
                throw new WebException(ErrorStatus.MAIN_NGINX_SERVER_NAME_ERROR);
            }

            nameSet.remove(domain);
            nameSet.remove(publicIp);
            nameSet.remove(interIp);

            for (String name : nameSet) {
                if (!NginxServerNameUtils.isValid(name)) {
                    throw new WebException(ErrorStatus.MAIN_NGINX_SERVER_NAME_INVALID_ERROR);
                }
            }
        }

        // Allow Origin
        NginxValidUtils.checkAllowedOrigin(accessControlAllowOrigin);
    }
}
