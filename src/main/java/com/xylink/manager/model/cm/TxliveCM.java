package com.xylink.manager.model.cm;

import com.xylink.config.TxLiveConstants;
import com.xylink.manager.controller.dto.RtmpConfig;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

@Setter
@Getter
public class TxliveCM implements ICMDto<TxliveCM> {

    private String nodeName;
    private String sdkAppId;
    private String tsaMpAddress;
    private String roomPrivateKey;
    private String roomPublicKey;
    private String defaultRtmpId;
    private List<RtmpConfig> rtmpModels = Arrays.asList(new RtmpConfig("0","trtc"),new RtmpConfig("1","xyrtc"));

    @Override
    public TxliveCM toModel(Map<String, String> cm, String nodeName) {
        this.sdkAppId = cm.get(nodeName + TxLiveConstants.txLiveSdkAppId);
        this.roomPrivateKey = cm.get(nodeName + TxLiveConstants.roomPrivateKey);
        this.roomPublicKey = cm.get(nodeName + TxLiveConstants.roomPublicKey);
        if (StringUtils.isBlank(cm.get(nodeName + TxLiveConstants.defaultRtmpID))) {
            this.defaultRtmpId = "0";
        } else {
            this.defaultRtmpId = cm.get(nodeName + TxLiveConstants.defaultRtmpID);
        }
        if (StringUtils.isBlank(cm.get(nodeName + TxLiveConstants.tsaMpAddress))) {
            this.tsaMpAddress = TxLiveConstants.defaultTsaMpAddress;
        } else {
            this.tsaMpAddress = cm.get(nodeName + TxLiveConstants.tsaMpAddress);
        }
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {

        Map<String, String> cm = new HashMap<>();
        cm.put(nodeName + TxLiveConstants.txLiveSdkAppId, this.sdkAppId);
        cm.put(nodeName + TxLiveConstants.roomPrivateKey, this.roomPrivateKey);
        cm.put(nodeName + TxLiveConstants.roomPublicKey, this.roomPublicKey);
        cm.put(nodeName + TxLiveConstants.defaultRtmpID, this.defaultRtmpId);
        if (StringUtils.isBlank(this.tsaMpAddress)) {
            this.tsaMpAddress = TxLiveConstants.defaultTsaMpAddress;
        }
        cm.put(nodeName + TxLiveConstants.tsaMpAddress, this.tsaMpAddress);
        cm.put(TxLiveConstants.defaultTsaMpAddressKey, TxLiveConstants.defaultTsaMpAddress);
        return cm;
    }

}
