package com.xylink.manager.model.cm;

import com.xylink.manager.model.deploy.Node;
import com.xylink.manager.service.CalculateMediaParametersService;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.util.SpringBeanUtil;

/**
 * <AUTHOR>
 * @since 2025-07-15 16:39
 */
public abstract class AbstractMediaCM {

    protected <T> T getBean(Class<T> clazz) {
        return SpringBeanUtil.getBean(clazz);
    }

    protected CalculateMediaParametersService.MediaParameters calculateMediaParameters(String nodeName, String serviceType) {
        Node node = getBean(IDeployService.class).getNodeByName(nodeName);
        if (node != null) {
            String nodeIp = node.getIp();
            CalculateMediaParametersService calculateMediaParametersService = getBean(CalculateMediaParametersService.class);
            return calculateMediaParametersService.calculateMediaParameters(nodeName, nodeIp, serviceType);
        }
        return new CalculateMediaParametersService.MediaParameters();
    }
}
