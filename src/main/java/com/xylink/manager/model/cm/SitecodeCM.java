package com.xylink.manager.model.cm;

import com.xylink.config.NetworkConstants;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021/12/22 6:07 下午
 */
@Data
public class SitecodeCM implements ICMDto<SitecodeCM> {
    private String accessIpIpSwitch;
    private String nodeName;

    @Override
    public SitecodeCM toModel(Map<String, String> cm, String nodeName) {
        String accessIpIpSwitch = cm.get(NetworkConstants.SITECODE_ACCESS_IPIP_SWITCH);
        if (StringUtils.isBlank(accessIpIpSwitch)) {
            this.accessIpIpSwitch = "false";
        }else {
            this.accessIpIpSwitch = accessIpIpSwitch;
        }
        this.nodeName = nodeName;
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        cm.put(NetworkConstants.SITECODE_ACCESS_IPIP_SWITCH, this.accessIpIpSwitch);
        return cm;
    }
}
