package com.xylink.manager.model.cm;

import com.xylink.config.Constants;
import com.xylink.config.WebrtcSiggwConstants;
import com.xylink.manager.model.deploy.ConfigMap;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/8/31 6:14 下午
 */
@Data
public class WebrtcSiggwCM implements ICMDto<WebrtcSiggwCM> {
    private String nodeName;
    private String webrtcTsaAddress;
    private String webrtcSiteCode;

    @Override
    public WebrtcSiggwCM toModel(Map<String, String> cm, String nodeName) {
        String webrtcTsaAddressValue = cm.get(nodeName + WebrtcSiggwConstants.WEBRTC_TSA_ADDRESS_SUFFIX);
        this.webrtcSiteCode = cm.get(nodeName + WebrtcSiggwConstants.WEBRTC_SITECODE_SUFFIX);
        if (StringUtils.isNotBlank(webrtcTsaAddressValue)) {
            this.webrtcTsaAddress = webrtcTsaAddressValue;
        }
        this.nodeName = nodeName;
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        String webrtcTsaAddressKey = this.nodeName + WebrtcSiggwConstants.WEBRTC_TSA_ADDRESS_SUFFIX;
        String webrtcSiteCodeKey = this.nodeName + WebrtcSiggwConstants.WEBRTC_SITECODE_SUFFIX;
        cm.put(webrtcTsaAddressKey, this.webrtcTsaAddress);
        cm.put(webrtcSiteCodeKey, this.webrtcSiteCode);
        return cm;
    }

    @Override
    public WebrtcSiggwCM setDefault(String nodeName) {
        this.setNodeName(nodeName);
        ConfigMap configMap = getDeployService().getConfigMapByName(Constants.CONFIGMAP_WEBRTC_SIGGW, Constants.NAMESPACE_DEFAULT);
        if (configMap != null) {
            Map<String, String> cm = configMap.getData();
            String webrtcTsaAddressValue = cm.get(nodeName + WebrtcSiggwConstants.WEBRTC_TSA_ADDRESS_SUFFIX);
            if (StringUtils.isNotBlank(webrtcTsaAddressValue)) {
                this.webrtcTsaAddress = webrtcTsaAddressValue;
            } else {
                String defaultWebrtcTsaAddressValue = cm.get(Constants.DEFAULT + WebrtcSiggwConstants.WEBRTC_TSA_ADDRESS_SUFFIX);
                Map<String, String> allIp = getDeployService().getConfigMapAllIp().getData();
                if (StringUtils.isNotBlank(defaultWebrtcTsaAddressValue) && allIp != null) {
                    for (Map.Entry<String, String> allIpItem : allIp.entrySet()) {
                        String joinKey = "{" + allIpItem.getKey() + "}";
                        if (defaultWebrtcTsaAddressValue.contains(joinKey)) {
                            defaultWebrtcTsaAddressValue = defaultWebrtcTsaAddressValue.replace(joinKey, allIpItem.getValue());
                        }
                    }
                }
                this.webrtcTsaAddress = defaultWebrtcTsaAddressValue;
            }
        }
        return this;
    }
}
