package com.xylink.manager.model.cm;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
public class SipplusgatewayCM implements ICMDto<SipplusgatewayCM>{

    private String isMaster;
    private String mediagw;

    private String nodeName;



    @Getter(AccessLevel.PRIVATE)
    final private String IS_MASTER_KEY = "-SIPPLUS_IS_MASTER";
    @Getter(AccessLevel.PRIVATE)
    final private String MEDIAGW_KEY = "-SIPPLUS_MEDIAGW_BIND";

    @Override
    public SipplusgatewayCM toModel(Map<String, String> cm, String nodeName) {
        this.nodeName = nodeName;
        this.isMaster = cm.get(nodeName + IS_MASTER_KEY);
        this.mediagw = cm.get(nodeName + MEDIAGW_KEY);
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        cm.put(nodeName + IS_MASTER_KEY, this.isMaster);
        cm.put(nodeName + MEDIAGW_KEY, this.mediagw);
        return cm;
    }
}
