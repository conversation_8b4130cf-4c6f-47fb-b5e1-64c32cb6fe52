package com.xylink.manager.model.cm;

import com.xylink.config.NetworkConstants;
import com.xylink.config.PushConstants;
import com.xylink.manager.model.em.Labels;
import com.xylink.util.Sha1Util;
import io.fabric8.kubernetes.api.model.Node;
import io.fabric8.kubernetes.api.model.NodeAddress;
import io.fabric8.kubernetes.api.model.NodeList;
import io.fabric8.kubernetes.client.KubernetesClient;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.HttpClientUtils;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Setter
@Getter
public class PushCM implements ICMDto<PushCM> {

    private String WEBHOOK_ADD_TO_ETC_HOSTS = "/hooks/addToEtcHost";
    private String WEBHOOK_DEL_FROM_ETC_HOSTS = "/hooks/delFromEtcHost";

    private String pushXmChannelId;
    private String pushXmAppSk;
    private String pushXmAppPackageName;
    private String pushHwAppId;
    private String pushHwAppSk;
    private String pushOppoAppKey;
    private String pushOppoAppSecret;
    private String pushBundleId;
    private String pushApnsPassword;
    private String pushApnsCertFile;

//    private String pushProxyDomainIpMap;

    @Override
    public PushCM toModel(Map<String, String> cm, String nodeName) {
        this.pushXmAppSk = cm.get(PushConstants.pushXmAppSk);
        this.pushXmChannelId = cm.get(PushConstants.pushXmChannelId);
        this.pushXmAppPackageName = cm.get(PushConstants.pushXmAppPackageName);
        this.pushHwAppId = cm.get(PushConstants.pushHwAppId);
        this.pushHwAppSk = cm.get(PushConstants.pushHwAppSk);
        this.pushOppoAppKey = cm.get(PushConstants.pushOppoAppKey);
        this.pushOppoAppSecret = cm.get(PushConstants.pushOppoAppSecret);
        this.pushBundleId = cm.get(PushConstants.pushBundleId);
        this.pushApnsPassword = cm.get(PushConstants.pushApnsPassword);
        this.pushApnsCertFile = cm.get(PushConstants.pushApnsCertFile);
//        this.pushProxyDomainIpMap = cm.get(PushConstants.pushProxyDomainIpMap);
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        cm.put(PushConstants.pushXmAppSk, this.pushXmAppSk);
        cm.put(PushConstants.pushXmChannelId, this.pushXmChannelId);
        cm.put(PushConstants.pushXmAppPackageName, this.pushXmAppPackageName);
        cm.put(PushConstants.pushHwAppId, this.pushHwAppId);
        cm.put(PushConstants.pushHwAppSk, this.pushHwAppSk);
        cm.put(PushConstants.pushOppoAppKey, this.pushOppoAppKey);
        cm.put(PushConstants.pushOppoAppSecret, this.pushOppoAppSecret);
        cm.put(PushConstants.pushBundleId, this.pushBundleId);
        cm.put(PushConstants.pushApnsPassword, this.pushApnsPassword);
        cm.put(PushConstants.pushApnsCertFile, this.pushApnsCertFile);
//        cm.put(PushConstants.pushProxyDomainIpMap, this.pushProxyDomainIpMap);
        return cm;
    }

//    @Override
//    public void afterSave(KubernetesClient client) {
//        if (StringUtils.isBlank(this.pushProxyDomainIpMap)) {
//            return;
//        }
//        CloseableHttpClient httpClient = null;
//        try {
//            NodeList nodeList = client.nodes().withLabel(Labels.push.label()).list();
//            if (nodeList.getItems().isEmpty()) {
//                return;
//            }
//            httpClient = HttpClients.createDefault();
//            List<String> domainIpMapList = Arrays.asList(this.pushProxyDomainIpMap.split("\n"));
//            List<Node> nodes = nodeList.getItems();
//            for (Node node : nodes) {
//                List<NodeAddress> nodeAddressList = node.getStatus().getAddresses();
//                String nodeIp = nodeAddressList.stream().filter(nodeAddress -> "InternalIP".equals(nodeAddress.getType())).map(NodeAddress::getAddress).findFirst().get();
//                doDel(httpClient, nodeIp, domainIpMapList);
//                doAdd(httpClient, nodeIp, domainIpMapList);
//            }
//        } finally {
//            HttpClientUtils.closeQuietly(httpClient);
//        }
//    }
//    private void doAdd(CloseableHttpClient httpClient, String nodeIp, List<String> domainIpMapList) {
//        for(String domainIpMap : domainIpMapList) {
//            String[] domain_ip = domainIpMap.split(" ");
//            if (IpCheckUtil.isBoolIp(domain_ip[0]) && !IpCheckUtil.isBoolIp(domain_ip[1])) {
//                String body = "{\"domain\":\"" + domain_ip[1].trim() + "\",\"ip\":\"" + domain_ip[0].trim() + "\"}";
//                CloseableHttpResponse response = null;
//                try {
//                    HttpPost request = new HttpPost("http://" + nodeIp + ":" + NetworkConstants.WEB_HOOK + WEBHOOK_ADD_TO_ETC_HOSTS);
//                    request.setEntity(new StringEntity(body, "UTF-8"));
//                    request.setHeader("Content-Type", "application/json");
//                    request.setHeader("X-Hub-Signature", "sha1=" + Sha1Util.hmacSha1Hex(body));
//                    httpClient.execute(request);
//                } catch (Exception e) {
//                    log.error("PushCM doAdd failed.nodeIp={}, domainIpMap={}", nodeIp, domainIpMap, e);
//                } finally {
//                    HttpClientUtils.closeQuietly(response);
//                }
//            }
//        }
//    }
//    private void doDel(CloseableHttpClient httpClient, String nodeIp, List<String> domainIpMapList) {
//        for(String domainIpMap : domainIpMapList) {
//            String[] domain_ip = domainIpMap.split(" ");
//            if (IpCheckUtil.isBoolIp(domain_ip[0]) && !IpCheckUtil.isBoolIp(domain_ip[1])) {
//                String body = "{\"domain\":\"" + domain_ip[1].trim() + "\"}";
//                CloseableHttpResponse response = null;
//                try {
//                    HttpPost request = new HttpPost("http://" + nodeIp + ":" + NetworkConstants.WEB_HOOK + WEBHOOK_DEL_FROM_ETC_HOSTS);
//                    request.setEntity(new StringEntity(body, "UTF-8"));
//                    request.setHeader("Content-Type", "application/json");
//                    request.setHeader("X-Hub-Signature", "sha1=" + Sha1Util.hmacSha1Hex(body));
//                    httpClient.execute(request);
//                } catch (Exception e) {
//                    log.error("PushCM doDel failed.nodeIp={}, domainIpMap={}", nodeIp, domainIpMap, e);
//                } finally {
//                    HttpClientUtils.closeQuietly(response);
//                }
//            }
//        }
//    }
}
