package com.xylink.manager.model.cm;

import com.xylink.config.DmcuConstants;
import com.xylink.config.VodnetworkConstants;
import com.xylink.manager.service.api.NoahApiService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.base.SystemModeConfig;
import com.xylink.util.SpringBeanUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022/3/16 2:28 下午
 */
@Getter
@Setter
@Slf4j
public class VodnetworkVodCM implements ICMDto<VodnetworkVodCM> {

    private String nodeName;

    private String siteCode;

    /**
     * vodnetwork-vod内部回源端口，外部不需要使用，注意不要和本机的vodnetwork-vodedit的http、https上传下载端口冲突
     */
    private String backHttpPort;

    private String backHttpsPort;

    /**
     * vod点播监听端口
     */
    private String vodListenPort;

    /**
     * 代理vodnetwork-vod的nginx内网地址
     */
    private String nginxInternalAddress;

    /**
     * 代理vodnetwork-vod的nginx公网地址
     */
    private String nginxExternalAddress;

    /**
     * 与vodclustermgr级联地址,逗号分割: 127.0.0.1,********
     */
    private String vodclustermgrAddress;

    @Override
    public VodnetworkVodCM toModel(Map<String, String> cm, String nodeName) {
        String siteCodeKey = nodeName + DmcuConstants.SITECODE;
        String vodListenPortKey = nodeName + VodnetworkConstants.VOD_LISTEN_PORT;
        String nginxInternalIpKey = nodeName + VodnetworkConstants.VOD_NGINX_INTERNAL_IP;
        String nginxExternalIpKey = nodeName + VodnetworkConstants.VOD_NGINX_EXTERNAL_IP;
        String backHttpPortKey = nodeName + VodnetworkConstants.VOD_BACK_HTTP_PORT;
        String backHttpsPortKey = nodeName + VodnetworkConstants.VOD_BACK_HTTPS_PORT;
        String vcmAddressKey = VodnetworkConstants.VODNETWORK_VCM_ADDRESS;

        String siteCodeFromCm = cm.get(siteCodeKey);
        this.siteCode = StringUtils.isNotBlank(siteCodeFromCm) ? siteCodeFromCm : VodnetworkConstants.DEFAULT_SITECODE;
        String vodListenPortFromCM = cm.get(vodListenPortKey);
        this.vodListenPort = StringUtils.isNotBlank(vodListenPortFromCM) ? vodListenPortFromCM : VodnetworkConstants.DEFAULT_VOD_LISTEN_PORT;

        this.nginxExternalAddress = cm.get(nginxExternalIpKey);
        this.nginxInternalAddress = cm.get(nginxInternalIpKey);

        String backHttpPortFromCM = cm.get(backHttpPortKey);
        this.backHttpPort = StringUtils.isNotBlank(backHttpPortFromCM) ? backHttpPortFromCM : VodnetworkConstants.DEFAULT_VOD_BACK_HTTP_PORT;

        String backHttpsPortFromCM = cm.get(backHttpsPortKey);
        this.backHttpsPort = StringUtils.isNotBlank(backHttpsPortFromCM) ? backHttpsPortFromCM : VodnetworkConstants.DEFAULT_VOD_BACK_HTTPS_PORT;

        this.vodclustermgrAddress = cm.get(vcmAddressKey);
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();

        String siteCodeKey = nodeName + DmcuConstants.SITECODE;
        String vodListenPortKey = nodeName + VodnetworkConstants.VOD_LISTEN_PORT;
        String nginxInternalIpKey = nodeName + VodnetworkConstants.VOD_NGINX_INTERNAL_IP;
        String nginxExternalIpKey = nodeName + VodnetworkConstants.VOD_NGINX_EXTERNAL_IP;
        String backHttpPortKey = nodeName + VodnetworkConstants.VOD_BACK_HTTP_PORT;
        String backHttpsPortKey = nodeName + VodnetworkConstants.VOD_BACK_HTTPS_PORT;
        String vcmAddressKey = VodnetworkConstants.VODNETWORK_VCM_ADDRESS;

        cm.put(siteCodeKey, StringUtils.isNotBlank(this.siteCode) ? this.siteCode : VodnetworkConstants.DEFAULT_SITECODE);
        cm.put(vodListenPortKey, this.vodListenPort);
        cm.put(nginxExternalIpKey, this.nginxExternalAddress);
        cm.put(nginxInternalIpKey, this.nginxInternalAddress);
        cm.put(backHttpPortKey, this.backHttpPort);
        cm.put(backHttpsPortKey, this.backHttpsPort);
        cm.put(vcmAddressKey, this.vodclustermgrAddress);

        K8sService k8sService = SpringBeanUtil.getBean(K8sService.class);
        if (k8sService.isNewCms() || SystemModeConfig.isPrivate56()) {
            log.info("new cms notify noah about vodnetwork-vod updated");
            NoahApiService noahApiService = SpringBeanUtil.getBean(NoahApiService.class);
            Map<String, String> keyValues = new HashMap<>();
            keyValues.put("vodnetwork-vod.svc.http_port", this.backHttpPort);
            keyValues.put("vodnetwork-vod.svc.https_port", this.backHttpsPort);
            keyValues.put("vodnetwork-vod.svc.listen_port", this.vodListenPort);

            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("dataId", "var_env.svc.yaml");
            paramMap.put("keyValues", keyValues);
            paramMap.put("publish", "true");
            noahApiService.notifyNginxPort(paramMap);
        }
        return cm;
    }
}
