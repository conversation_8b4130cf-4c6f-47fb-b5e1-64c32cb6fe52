package com.xylink.manager.model.cm;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 2023/12/14/16:38
 * @Description:
 */
@Setter
@Getter
public class ThirdBridgeProxyCM implements ICMDto<ThirdBridgeProxyCM> {
    //是否走三方对接服务
    private String thirdBridgeProxyPort;
    private String thirdBridgeProxyHttpPort;
    private String nodeName;

    @Getter(AccessLevel.PRIVATE)
    final private String THIRD_BRIDGE_PUBLIC_PORT_KEY = "THIRD_BRIDGE_PROXY_PUBLIC_PORT";
    @Getter(AccessLevel.PRIVATE)
    final private String THIRD_BRIDGE_HTTP_PORT_KEY = "THIRD_BRIDGE_PROXY_HTTP_PORT";

    @Override
    public ThirdBridgeProxyCM toModel(Map<String, String> cm, String nodeName) {
        this.thirdBridgeProxyPort = StringUtils.isBlank(cm.get(THIRD_BRIDGE_PUBLIC_PORT_KEY))?"8080":cm.get(THIRD_BRIDGE_PUBLIC_PORT_KEY);
        this.thirdBridgeProxyHttpPort = StringUtils.isBlank(cm.get(THIRD_BRIDGE_HTTP_PORT_KEY))?"8079":cm.get(THIRD_BRIDGE_HTTP_PORT_KEY);
        this.nodeName = nodeName;
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> map = new HashMap<>();
        map.put(THIRD_BRIDGE_PUBLIC_PORT_KEY, StringUtils.isBlank(thirdBridgeProxyPort) ? "8080" : thirdBridgeProxyPort);
        map.put(THIRD_BRIDGE_HTTP_PORT_KEY, StringUtils.isBlank(thirdBridgeProxyHttpPort) ? "8079" : thirdBridgeProxyHttpPort);
        return map;
    }
}
