package com.xylink.manager.model.cm;

import com.xylink.config.ProxyConstants;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

@Setter
@Getter
public class VodProxyCM implements ICMDto<VodProxyCM> {


    private String nginxPort;
    private String nginxSSLPort;
    private String remoteServerAddress;
    private String remoteServerDomain;
    private String remoteNginxPort;
    private String remoteNginxSSLPort;
    private String useDomainFilter;
    private String useIPV6;
    private String proxyPairHostname;
    private String accessFileSwitch;
    private String accessDownloadFileSwitch;

    private String nodeName;

    /**
     * 支持vod-proxy 分发/转发模式
     */
    private String type;

    @Override
    public VodProxyCM toModel(Map<String, String> cm, String nodeName) {
        this.nginxPort = cm.get(nodeName + ProxyConstants.VOD_NGINX_PORT);
        this.nginxSSLPort = cm.get(nodeName + ProxyConstants.VOD_NGINX_SSL_PORT);
        this.remoteServerAddress = cm.get(nodeName + ProxyConstants.UPSTREAM_ADDR);
        this.remoteServerDomain = cm.get(nodeName + ProxyConstants.UPSTREAM_DOMAIN);
        this.remoteNginxPort = cm.get(nodeName + ProxyConstants.UPSTREAM_PORT);
        this.remoteNginxSSLPort = cm.get(nodeName + ProxyConstants.UPSTREAM_SSL_PORT);
        this.useDomainFilter = cm.get(nodeName + ProxyConstants.USE_PROXY_FILTER);
        this.useIPV6 = cm.get(nodeName + ProxyConstants.NGINX_USE_IPV6);
        this.proxyPairHostname = cm.get(nodeName + ProxyConstants.PAIR_HOSTNAME);
        String accessFileSwitch = cm.get(nodeName + ProxyConstants.ACCESS_RECORD_FILE_SWITCH);
        if (StringUtils.isBlank(accessFileSwitch)) {
            this.accessFileSwitch = "0";
        } else {
            this.accessFileSwitch = accessFileSwitch;
        }
        String accessDownloadFileSwitch = cm.get(nodeName + ProxyConstants.ACCESS_RECORD_DOWNLOAD_FILE_SWITCH);
        if (StringUtils.isBlank(accessDownloadFileSwitch)) {
            this.accessDownloadFileSwitch = "0";
        } else {
            this.accessDownloadFileSwitch = accessDownloadFileSwitch;
        }
        String serverConf = cm.get(nodeName + ProxyConstants.VOD_PROXY_SERVER_CONF);
        if (StringUtils.isBlank(serverConf)){
            this.type = "forward";
        }else if (serverConf.contains("include#customer/http_conf;")) {
            this.type = "distribute";
        }else{
            this.type = "forward";
        }
        this.nodeName = nodeName;
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        cm.put(nodeName +ProxyConstants.VOD_NGINX_PORT, nginxPort);
        cm.put(nodeName +ProxyConstants.VOD_NGINX_SSL_PORT, nginxSSLPort);
        cm.put(nodeName +ProxyConstants.UPSTREAM_ADDR, remoteServerAddress);
        cm.put(nodeName +ProxyConstants.UPSTREAM_DOMAIN, remoteServerDomain);
        cm.put(nodeName +ProxyConstants.UPSTREAM_PORT, remoteNginxPort);
        cm.put(nodeName +ProxyConstants.UPSTREAM_SSL_PORT, remoteNginxSSLPort);
        cm.put(nodeName +ProxyConstants.USE_PROXY_FILTER, useDomainFilter);
        cm.put(nodeName +ProxyConstants.NGINX_USE_IPV6, useIPV6);
        cm.put(nodeName +ProxyConstants.PAIR_HOSTNAME, proxyPairHostname);
        cm.put(nodeName +ProxyConstants.ACCESS_RECORD_FILE_SWITCH, accessFileSwitch);
        cm.put(nodeName +ProxyConstants.ACCESS_RECORD_DOWNLOAD_FILE_SWITCH, accessDownloadFileSwitch);

        if ("distribute".equals(type)) {
            cm.put(nodeName + ProxyConstants.VOD_PROXY_SERVER_CONF, "include#customer/http_conf;include#customer/ssl_nginx_conf;include#customer/upstream_conf;");
        }else{
            cm.put(nodeName + ProxyConstants.VOD_PROXY_SERVER_CONF, "include#customer/proxy;");
        }
        return cm;
    }
}
