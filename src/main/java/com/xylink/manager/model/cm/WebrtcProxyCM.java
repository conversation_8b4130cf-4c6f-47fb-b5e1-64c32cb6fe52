package com.xylink.manager.model.cm;

import com.xylink.config.Constants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.WebException;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.util.NginxServerNameUtils;
import com.xylink.util.NginxValidUtils;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

@Setter
@Getter
@Slf4j
public class WebrtcProxyCM implements ICMDto<WebrtcProxyCM> {
    private static final String DEFAULT_ACCESS_CONTROL_ALLOW_METHODS = "GET,POST,PUT,DELETE,OPTIONS,PATCH";

    private String nodeName;
    private String nginxPort;
    private String nginxSSLPort;
    private String remoteServerAddress;
    private String remoteServerDomain;
    private String remoteNginxPort;
    private String remoteNginxSSLPort;
    private String useIPV6;
    private String serverName;
    private List<String> accessControlAllowOrigin;
    private String useDomainFilter;
    private String proxyPairHostname;

    @Getter(AccessLevel.PRIVATE)
    final private String NGINX_PORT_KEY = "-WEBRTC_PROXY_NGINX_PORT";
    @Getter(AccessLevel.PRIVATE)
    final private String NGINX_SSL_PORT_KEY = "-WEBRTC_PROXY_NGINX_SSL_PORT";
    @Getter(AccessLevel.PRIVATE)
    final private String WEBRTC_PROXY_UPSTREAM_ADDR_KEY = "-WEBRTC_PROXY_UPSTREAM_ADDR";
    @Getter(AccessLevel.PRIVATE)
    final private String WEBRTC_PROXY_UPSTREAM_DOMAIN_KEY = "-WEBRTC_PROXY_UPSTREAM_DOMAIN";
    @Getter(AccessLevel.PRIVATE)
    final private String WEBRTC_PROXY_UPSTREAM_PORT_KEY = "-WEBRTC_PROXY_UPSTREAM_PORT";
    @Getter(AccessLevel.PRIVATE)
    final private String WEBRTC_PROXY_UPSTREAM_SSL_PORT_KEY = "-WEBRTC_PROXY_UPSTREAM_SSL_PORT";
    @Getter(AccessLevel.PRIVATE)
    final private String WEBRTC_PROXY_NGINX_USE_IPV6_KEY = "-WEBRTC_PROXY_NGINX_USE_IPV6";
    @Getter(AccessLevel.PRIVATE)
    final private String WEBRTC_PROXY_NIGNX_SERVER_NAME_KEY = "-WEBRTC_PROXY_NIGNX_SERVER_NAME";
    @Getter(AccessLevel.PRIVATE)
    final private String WEBRTC_PROXY_ACCESS_CONTROL_ALLOW_ORIGIN_KEY = "-WEBRTC_PROXY_ACCESS_CONTROL_ALLOW_ORIGIN";
    @Getter(AccessLevel.PRIVATE)
    final private String WEBRTC_PROXY_USE_PROXY_FILTER_KEY = "-WEBRTC_PROXY_USE_PROXY_FILTER";
    @Getter(AccessLevel.PRIVATE)
    final private String WEBRTC_PROXY_PAIR_HOSTNAME_KEY = "-WEBRTC_PROXY_PAIR_HOSTNAME";

    @Override
    public WebrtcProxyCM toModel(Map<String, String> cm, String nodeName) {
        this.nodeName = nodeName;
        this.nginxPort = cm.get(nodeName + NGINX_PORT_KEY);
        this.nginxSSLPort = cm.get(nodeName + NGINX_SSL_PORT_KEY);
        this.remoteServerAddress = cm.get(nodeName + WEBRTC_PROXY_UPSTREAM_ADDR_KEY);
        this.remoteServerDomain = cm.get(nodeName + WEBRTC_PROXY_UPSTREAM_DOMAIN_KEY);
        this.remoteNginxPort = cm.get(nodeName + WEBRTC_PROXY_UPSTREAM_PORT_KEY);
        this.remoteNginxSSLPort = cm.get(nodeName + WEBRTC_PROXY_UPSTREAM_SSL_PORT_KEY);
        this.useIPV6 = cm.get(nodeName + WEBRTC_PROXY_NGINX_USE_IPV6_KEY);
        this.serverName = StringUtils.isBlank(cm.get(nodeName + WEBRTC_PROXY_NIGNX_SERVER_NAME_KEY)) ? "localhost" : cm.get(nodeName + WEBRTC_PROXY_NIGNX_SERVER_NAME_KEY);
        String accessControlAllowOriginValue = cm.get(nodeName + WEBRTC_PROXY_ACCESS_CONTROL_ALLOW_ORIGIN_KEY);
        this.accessControlAllowOrigin = NginxValidUtils.allowedOriginToArrary(accessControlAllowOriginValue);
        this.useDomainFilter = cm.get(nodeName + WEBRTC_PROXY_USE_PROXY_FILTER_KEY);
        this.proxyPairHostname = cm.get(nodeName + WEBRTC_PROXY_PAIR_HOSTNAME_KEY);
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        cm.put(nodeName + NGINX_PORT_KEY, nginxPort);
        cm.put(nodeName + NGINX_SSL_PORT_KEY, nginxSSLPort);
        cm.put(nodeName + WEBRTC_PROXY_UPSTREAM_ADDR_KEY, remoteServerAddress);
        cm.put(nodeName + WEBRTC_PROXY_UPSTREAM_DOMAIN_KEY, remoteServerDomain);
        cm.put(nodeName + WEBRTC_PROXY_UPSTREAM_PORT_KEY, remoteNginxPort);
        cm.put(nodeName + WEBRTC_PROXY_UPSTREAM_SSL_PORT_KEY, remoteNginxSSLPort);
        cm.put(nodeName + WEBRTC_PROXY_NGINX_USE_IPV6_KEY, useIPV6);
        cm.put(nodeName + WEBRTC_PROXY_NIGNX_SERVER_NAME_KEY, StringUtils.isBlank(serverName) ? "localhost" : serverName);
        cm.put(nodeName + WEBRTC_PROXY_ACCESS_CONTROL_ALLOW_ORIGIN_KEY, NginxValidUtils.allowedOriginToStr(this.accessControlAllowOrigin));
        cm.put(nodeName + WEBRTC_PROXY_USE_PROXY_FILTER_KEY, useDomainFilter);
        cm.put(nodeName + WEBRTC_PROXY_PAIR_HOSTNAME_KEY, proxyPairHostname);
        return cm;
    }

    @Override
    public void beforeSave() {
        if (StringUtils.isNotBlank(serverName) && !"localhost".equals(serverName)) {
            ConfigMap configMap = getDeployService().getConfigMapByName(Constants.CONFIGMAP_ALL_WEBRTC_PROXY, Constants.NAMESPACE_DEFAULT);
            Map<String, String> configmap = configMap != null ? configMap.getData() : new HashMap<>();

            String domain = configmap.get(nodeName + "-DOMAIN");
            String publicIp = configmap.get(nodeName + "-PUBLIC-IP");
            String interIp = configmap.get(nodeName + "-INTERNAL-IP");

            String[] names = serverName.split(" ");
            Set<String> nameSet = new HashSet<>(Arrays.asList(names));

            if (StringUtils.isNotBlank(domain) && !nameSet.contains(domain)) {
                throw new WebException(ErrorStatus.MAIN_NGINX_SERVER_NAME_ERROR);
            }
            if (StringUtils.isNotBlank(publicIp) && !nameSet.contains(publicIp)) {
                throw new WebException(ErrorStatus.MAIN_NGINX_SERVER_NAME_ERROR);
            }
            if (StringUtils.isNotBlank(interIp) && !nameSet.contains(interIp)) {
                throw new WebException(ErrorStatus.MAIN_NGINX_SERVER_NAME_ERROR);
            }

            nameSet.remove(domain);
            nameSet.remove(publicIp);
            nameSet.remove(interIp);

            for (String name : nameSet) {
                if (!NginxServerNameUtils.isValid(name)) {
                    throw new WebException(ErrorStatus.MAIN_NGINX_SERVER_NAME_INVALID_ERROR);
                }
            }
        }

        // Allow Origin
        NginxValidUtils.checkAllowedOrigin(accessControlAllowOrigin);
    }
}
