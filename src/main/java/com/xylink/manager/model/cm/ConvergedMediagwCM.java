package com.xylink.manager.model.cm;

import com.xylink.config.Constants;
import com.xylink.config.DmcuConstants;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.util.IpUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Setter
@Getter
@Slf4j
public class ConvergedMediagwCM implements ICMDto<ConvergedMediagwCM> {

    private String nodeName;
    private String dmcuComments;
    private String siteCode;
    private String upThriftIp;

    private String selfThriftIp;

    private String maxRxBandWidth;
    private String maxTxBandWidth;

    private String mediaProcessTaskNum;
    private String mediaEndpointNumPerMediaTask;
    /**
     * 外网终端连接端口
     */
    private String dmcuClientPort;
    private String dmcuResourceType;
    private String dmcuConfType;
    private String kafkaIp;

    /**
     * MCU级联内网IP
     */
    private String peerInternalIp;
    /**
     * MCU级联外网IP
     */
    private String peerPublicIp;
    /**
     * MCU级联端口
     */
    private String peerPort;

    private String nodeUpHookPort;
    private String nodeSelfHookPort;
    private String nodeUpHookExt;
    private String nodeSelfHookExt;

    private String convergedSiggwIp;

    private String useTransportProxy;

    public boolean changeUseTransportProxy = false;

    @Override
    public ConvergedMediagwCM toModel(Map<String, String> cm, String nodeName) {
        String siteCodeKey = nodeName + DmcuConstants.SITECODE;
        String commentsKey = nodeName + DmcuConstants.COMMENTS;
        String maxRxKey = nodeName + DmcuConstants.MAXRXBW;
        String maxTxKey = nodeName + DmcuConstants.MAXTXBW;
        String upThriftKey = nodeName + DmcuConstants.UP_THRIFT_IP;
        String selfThriftKey = nodeName + DmcuConstants.SELF_THRIFT_IP;
        String processTaskNumKey = nodeName + DmcuConstants.PROCESS_TASKNUM;
        String epnumPerTaskKey = nodeName + DmcuConstants.EPNUM_PERTASK;
        String dmcuClientPortKey = nodeName + DmcuConstants.DMCU_CLIENT_PORT;
        String nodeUpHookPortKey = nodeName + DmcuConstants.DMCU_SIDE_NODE_UP_HOOK_PORT;
        String nodeSelfHookPortKey = nodeName + DmcuConstants.DMCU_SIDE_NODE_SELF_HOOK_PORT;
        String nodeUpHookExtKey = nodeName + DmcuConstants.DMCU_SIDE_NODE_UP_HOOK_EXT;
        String nodeSelfHookExtKey = nodeName + DmcuConstants.DMCU_SIDE_NODE_SELF_HOOK_EXT;
        String nodeConvergedSiggwIpKey = nodeName + DmcuConstants.RTMP_CONVERGED_MEDIAGW_IP;

        String defaultSiteCodeKey = DmcuConstants.DEFAULT_PREFIX + DmcuConstants.SITECODE;
        String defaultMaxRxKey = DmcuConstants.DEFAULT_PREFIX + DmcuConstants.MAXRXBW;
        String defaultMaxTxKey = DmcuConstants.DEFAULT_PREFIX + DmcuConstants.MAXTXBW;
        String defaultTaskNumKey = DmcuConstants.DEFAULT_PREFIX + DmcuConstants.PROCESS_TASKNUM;
        String defaultPerTaskKey = DmcuConstants.DEFAULT_PREFIX + DmcuConstants.EPNUM_PERTASK;
        String defaultDmcuClientPortKey = DmcuConstants.DEFAULT_PREFIX + DmcuConstants.DMCU_CLIENT_PORT;
        String defaultConvergedSiggwIpKey = DmcuConstants.DEFAULT_PREFIX + DmcuConstants.RTMP_CONVERGED_MEDIAGW_IP;

        this.nodeUpHookPort = cm.get(nodeUpHookPortKey);
        this.nodeSelfHookPort = cm.get(nodeSelfHookPortKey);
        this.nodeUpHookExt = cm.get(nodeUpHookExtKey);
        this.nodeSelfHookExt = cm.get(nodeSelfHookExtKey);

        String convergedSiggwIp = cm.get(nodeConvergedSiggwIpKey);
        this.convergedSiggwIp = StringUtils.isBlank(convergedSiggwIp) ? cm.get(defaultConvergedSiggwIpKey) : convergedSiggwIp;

        String siteCode = cm.get(siteCodeKey);
        this.siteCode = StringUtils.isEmpty(siteCode) ? cm.get(defaultSiteCodeKey) : siteCode;

        String maxRx = cm.get(maxRxKey);
        this.maxRxBandWidth = StringUtils.isEmpty(maxRx) ? cm.get(defaultMaxRxKey) : maxRx;

        String maxTx = cm.get(maxTxKey);
        this.maxTxBandWidth = StringUtils.isEmpty(maxTx) ? cm.get(defaultMaxTxKey) : maxTx;

        String taskNum = cm.get(processTaskNumKey);
        this.mediaProcessTaskNum = StringUtils.isEmpty(taskNum) ? cm.get(defaultTaskNumKey) : taskNum;

        String perTask = cm.get(epnumPerTaskKey);
        this.mediaEndpointNumPerMediaTask = StringUtils.isEmpty(perTask) ? cm.get(defaultPerTaskKey) : perTask;

        String upThrift = cm.get(upThriftKey);
        this.upThriftIp = StringUtils.isBlank(upThrift) ? this.upThriftIp : upThrift;


        String selfThrift = cm.get(selfThriftKey);
        this.selfThriftIp = StringUtils.isBlank(selfThrift) ? this.selfThriftIp : selfThrift;

        String clientPort = cm.get(dmcuClientPortKey);
        this.dmcuClientPort = StringUtils.isEmpty(clientPort) ? cm.get(defaultDmcuClientPortKey) : clientPort;

        this.dmcuComments = cm.get(commentsKey);

        this.kafkaIp = cm.get(nodeName + DmcuConstants.KAFKA_IP);

        this.peerInternalIp = cm.get(nodeName + DmcuConstants.PEER_INTERNAL_IP);
        this.peerPublicIp = cm.get(nodeName + DmcuConstants.PEER_PUBLIC_IP);

        String peerPort = cm.get(nodeName + DmcuConstants.PEER_PORT);
        this.peerPort = StringUtils.isEmpty(peerPort) ? cm.get(defaultDmcuClientPortKey) : peerPort;

        String allDmcuUseTransportProxy = cm.get(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY);
        this.useTransportProxy = StringUtils.isBlank(allDmcuUseTransportProxy) ? "true" : allDmcuUseTransportProxy;
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {

        Map<String, String> cm = new HashMap<>();

        String siteCodeKey = this.nodeName + DmcuConstants.SITECODE;
        String commentsKey = this.nodeName + DmcuConstants.COMMENTS;
        String maxRxKey = this.nodeName + DmcuConstants.MAXRXBW;
        String maxTxKey = this.nodeName + DmcuConstants.MAXTXBW;
        String upThriftKey = this.nodeName + DmcuConstants.UP_THRIFT_IP;
        String selfThrifyKey = this.nodeName + DmcuConstants.SELF_THRIFT_IP;
        String processTaskNumKey = this.nodeName + DmcuConstants.PROCESS_TASKNUM;
        String epnumPertaskKey = this.nodeName + DmcuConstants.EPNUM_PERTASK;
        String clientPortKey = this.nodeName + DmcuConstants.DMCU_CLIENT_PORT;
        String nodeUpHookPortKey = this.nodeName + DmcuConstants.DMCU_SIDE_NODE_UP_HOOK_PORT;
        String nodeSelfHookPortKey = this.nodeName + DmcuConstants.DMCU_SIDE_NODE_SELF_HOOK_PORT;
        String nodeUpHookExtKey = this.nodeName + DmcuConstants.DMCU_SIDE_NODE_UP_HOOK_EXT;
        String nodeSelfHookExtKey = this.nodeName + DmcuConstants.DMCU_SIDE_NODE_SELF_HOOK_EXT;
        String nodeConvergedSiggwIpKey = this.nodeName + DmcuConstants.RTMP_CONVERGED_MEDIAGW_IP;

        cm.put(nodeUpHookPortKey, Strings.isBlank(this.nodeUpHookPort) ? Strings.EMPTY : this.nodeUpHookPort);
        cm.put(nodeSelfHookPortKey, Strings.isBlank(this.nodeSelfHookPort) ? Strings.EMPTY : this.nodeSelfHookPort);
        cm.put(nodeUpHookExtKey, Strings.isBlank(this.nodeUpHookExt) ? Strings.EMPTY : this.nodeUpHookExt);
        cm.put(nodeSelfHookExtKey, Strings.isBlank(this.nodeSelfHookExt) ? Strings.EMPTY : this.nodeSelfHookExt);
        cm.put(commentsKey, this.dmcuComments);
        cm.put(siteCodeKey, StringUtils.isNotEmpty(this.siteCode) ? this.siteCode : DmcuConstants.SITECODE_DEFAULT);
        cm.put(maxRxKey, StringUtils.isNotEmpty(this.maxRxBandWidth) ? this.maxRxBandWidth : DmcuConstants.MAXRXBW_DEFAULT);
        cm.put(maxTxKey, StringUtils.isNotEmpty(this.maxTxBandWidth) ? this.maxTxBandWidth : DmcuConstants.MAXTXBW_DEFAULT);
        cm.put(upThriftKey, StringUtils.isNotEmpty(this.upThriftIp) ? this.upThriftIp : DmcuConstants.IP_DEFAULT);
        cm.put(selfThrifyKey, StringUtils.isNotEmpty(this.selfThriftIp) ? this.selfThriftIp : DmcuConstants.IP_DEFAULT);
        cm.put(processTaskNumKey, StringUtils.isNotEmpty(this.mediaProcessTaskNum) ? this.mediaProcessTaskNum : DmcuConstants.PROCESS_TASKNUM_DEFAULT);
        cm.put(epnumPertaskKey, StringUtils.isNotEmpty(this.mediaEndpointNumPerMediaTask) ? this.mediaEndpointNumPerMediaTask : DmcuConstants.EPNUM_PERTASK_DEFAULT);
        cm.put(clientPortKey, StringUtils.isNotEmpty(this.dmcuClientPort) ? this.dmcuClientPort : DmcuConstants.DMCU_CLIENT_PORT_DEFAULT);
        cm.put(nodeName + DmcuConstants.KAFKA_IP, IpUtils.getKafkaIp(this.kafkaIp));

        cm.put(nodeName + DmcuConstants.PEER_INTERNAL_IP, this.peerInternalIp);
        cm.put(nodeName + DmcuConstants.PEER_PUBLIC_IP, this.peerPublicIp);
        cm.put(nodeName + DmcuConstants.PEER_PORT, StringUtils.isNotEmpty(this.peerPort) ? this.peerPort : DmcuConstants.DMCU_CLIENT_PORT_DEFAULT);
        cm.put(nodeConvergedSiggwIpKey, StringUtils.isNotBlank(this.convergedSiggwIp) ? this.convergedSiggwIp : DmcuConstants.IP_DEFAULT);

        cm.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.useTransportProxy);
        return cm;
    }


    @Override
    public ConvergedMediagwCM setDefault(String nodeName) {
        this.selfThriftIp = getDeployService().getNodeByName(nodeName).getIp();

        McUtils mcUtils = new McUtils();
        McUtils.UpThriftIp priorityMc = mcUtils.mcuDefaultUpThriftIp();
        if (priorityMc != null) {
            this.upThriftIp = priorityMc.getInternalIp();
        }
        return this;
    }


    @Override
    public void beforeSave() {
        ConfigMap configMap = getDeployService().getConfigMapByName(Constants.CONFIGMAP_ALL_CONVERGED_MEDIAGW, Constants.NAMESPACE_DEFAULT);
        if (Objects.isNull(configMap)) {
            return;
        }
        Map<String, String> map = configMap.getData();
        String allDmcuUseTransportProxy = map.get(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY);
        if (StringUtils.isNotBlank(this.useTransportProxy) && !this.useTransportProxy.equalsIgnoreCase(allDmcuUseTransportProxy)) {
            changeUseTransportProxy = true;
        }
    }

    @Override
    public void afterSave() {
        if (StringUtils.isNotBlank(this.useTransportProxy)) {
            try {
                getDeployService().patchConfigMapAllIp(d->{
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.useTransportProxy);
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }

            try {
                getDeployService().patchConfigMap(Constants.CONFIGMAP_DMCU, Constants.NAMESPACE_DEFAULT, d -> {
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.useTransportProxy);
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }

            try {
                getDeployService().patchConfigMap(Constants.CONFIGMAP_ALL_IVR, Constants.NAMESPACE_DEFAULT, d -> {
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.useTransportProxy);
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }

            try {
                getDeployService().patchConfigMap(Constants.CONFIGMAP_NMSA, Constants.NAMESPACE_DEFAULT, d -> {
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.useTransportProxy);
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }

            try {
                getDeployService().patchConfigMap(Constants.CONFIGMAP_HLS, Constants.NAMESPACE_DEFAULT, d -> {
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.useTransportProxy);
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }

            try {
                getDeployService().patchConfigMap(Constants.CONFIGMAP_MA, Constants.NAMESPACE_DEFAULT, d -> {
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.useTransportProxy);
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }

            try {
                getDeployService().patchConfigMap(Constants.CONFIGMAP_WEBRTC_MEDIAGW, Constants.NAMESPACE_DEFAULT, d -> {
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.useTransportProxy);
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }
        }
    }
}
