package com.xylink.manager.model.cm;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 达梦数据库高级配置
 */
@Data
public class DaMengCM implements ICMDto<DaMengCM> {

    /**
     * 达梦数据路部署模式：（默认：single, master-slave）
     */
    private String dmClusterMode;

    /**
     * 达梦数据库集群域名
     */
    private String dmClusterDomain;

    /**
     * 达梦主备ip-port配置
     * 例如：**************:5236,**************:5236
     */
    private String dmClusterIpPort;

    @Override
    public DaMengCM toModel(Map<String, String> cm, String nodeName) {
        this.dmClusterMode = cm.get("DM_CLUSTER_MODE");
        this.dmClusterDomain = cm.get("DM_CLUSTER_DOMAIN");
        this.dmClusterIpPort = cm.get("DM_CLUSTER_IP_PORT");
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        cm.put("DM_CLUSTER_MODE", this.dmClusterMode);
        cm.put("DM_CLUSTER_DOMAIN", this.dmClusterDomain);
        cm.put("DM_CLUSTER_IP_PORT", this.dmClusterIpPort);
        return cm;
    }

    @Override
    public DaMengCM setDefault(String nodeName) {
        this.dmClusterMode = "single";
        this.dmClusterDomain = "xydm";
        this.dmClusterIpPort = "";
        return this;
    }
}
