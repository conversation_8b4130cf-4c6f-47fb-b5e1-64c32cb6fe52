package com.xylink.manager.model.cm;

import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.service.ServerListService;
import com.xylink.util.SpringBeanUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2024/09/06/11:02
 */
@Data
public class KafkaCM implements ICMDto<KafkaCM> {
    private static final String KAFKA_OUT_SIDE_KEY = "-KAFKA-OUT-SIDE-KEY";
    private static final String KAFKA_LISTENERS = "-KAFKA-LISTENERS";
    public static final String KAFKA_LISTENERS_SECURITY = "-KAFKA-LISTENERS-SECURITY";
    private static final String KAFKA_ADVERTISED_LISTENERS = "-KAFKA-ADVERTISED-LISTENERS";
    private static final String ipv4Regex = "^(([1-9]?[0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\\.(?:[1-9]?[0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])){3}):([0-9]|[1-9][0-9]{0,3})$";

    private String nodeName;
    private String kafkaOutSideIp;

    @Override
    public void beforeSave() {
        if (StringUtils.isEmpty(this.kafkaOutSideIp)) {
            return;
        }
        int port;
        List<Integer> ports = new ArrayList<>(5);
        ports.add(9093);
        Pattern pattern = Pattern.compile(ipv4Regex);
        String[] addresses = this.kafkaOutSideIp.split(",");
        if (addresses.length > 3) {
            throw new ServerException(ErrorStatus.KAFKA_OUTSIDE_ADDRESS_MAX);
        }
        for (String temp : addresses) {
            String[] address = pattern.matcher(temp).matches() ? temp.split(":") : temp.split("]:");
            if (address.length != 2) {
                throw new ServerException(ErrorStatus.KAFKA_OUTSIDE_ADDRESS_ERROR);
            }
            try {
                port = Integer.parseInt(address[1]);
            } catch (NumberFormatException e) {
                throw new ServerException(ErrorStatus.KAFKA_OUTSIDE_PORT_ERROR);
            }
            if (ports.contains(port)) {
                throw new ServerException(ErrorStatus.KAFKA_OUTSIDE_PORT_BUG);
            }
            ports.add(port);
        }
    }

    @Override
    public KafkaCM toModel(Map<String, String> cm, String nodeName) {
        this.nodeName = nodeName;
        this.kafkaOutSideIp = cm.get(nodeName + KAFKA_OUT_SIDE_KEY);
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        if (StringUtils.isEmpty(this.kafkaOutSideIp)) {
            Map<String, String> data = new HashMap<>(1);
            data.put(nodeName + KAFKA_OUT_SIDE_KEY, "");
            return data;
        }
        boolean ipv4Flag;
        StringBuilder kafkaListener = new StringBuilder();
        StringBuilder kafkaListenerSecurity = new StringBuilder();
        StringBuilder kafkaAdvertisedListener = new StringBuilder();
        Pattern pattern = Pattern.compile(ipv4Regex);
        String[] addresses = this.kafkaOutSideIp.split(",");
        ServerListService serverListService = SpringBeanUtil.getBean(ServerListService.class);
        Boolean enableAuthorize = serverListService.getKafkaConfig().getEnableAuthorize();
        enableAuthorize = Optional.ofNullable(enableAuthorize).orElse(false);
        for (int i = 0; i < addresses.length; i++) {
            ipv4Flag = pattern.matcher(addresses[i]).matches();
            String[] address = ipv4Flag ? addresses[i].split(":") : addresses[i].split("]:");
            if (address.length != 2) {
                continue;
            }
            String listenerIp = ipv4Flag ? "0.0.0.0:" : "[::]:";
            kafkaListener.append(",OUTSIDE").append(i).append("://").append(listenerIp).append(address[1]);
            kafkaAdvertisedListener.append(",OUTSIDE").append(i).append("://").append(addresses[i]);
            if (enableAuthorize) {
                kafkaListenerSecurity.append(",OUTSIDE").append(i).append(":SASL_PLAINTEXT");
            } else {
                kafkaListenerSecurity.append(",OUTSIDE").append(i).append(":PLAINTEXT");
            }
        }
        Map<String, String> data = new HashMap<>(4);
        data.put(this.nodeName + KAFKA_OUT_SIDE_KEY, this.kafkaOutSideIp);
        data.put(this.nodeName + KAFKA_LISTENERS, kafkaListener.toString());
        data.put(this.nodeName + KAFKA_LISTENERS_SECURITY, kafkaListenerSecurity.toString());
        data.put(this.nodeName + KAFKA_ADVERTISED_LISTENERS, kafkaAdvertisedListener.toString());
        return data;
    }
}
