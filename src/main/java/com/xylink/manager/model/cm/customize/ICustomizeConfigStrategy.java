package com.xylink.manager.model.cm.customize;

import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 * @create 2022/9/7 4:55 下午
 */
public interface ICustomizeConfigStrategy {
    /**
     * 获取标签名
     * @return
     */
    String getLabelName();

    /**
     * 获取当前节点的当前服务高级配置
     * @param nodeName
     * @return
     */
    Object getCustomizeConfig(String nodeName);

    /**
     * 保存当前节点的当前服务高级配置
     * @param data
     */
    void saveCustomizeConfig(LinkedHashMap data);
}
