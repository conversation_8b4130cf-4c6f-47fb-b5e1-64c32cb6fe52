package com.xylink.manager.model.cm;

import com.xylink.config.mapper.JsonMapper;
import com.xylink.manager.controller.dto.*;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/15/15:46
 */
@Setter
@Getter
public class McAccessCM implements ICMDto<McAccessCM> {
    private String nodeName;
    private List<NginxAddrDTO> nginxAddr;
    private List<SrsAddrDTO> srsAddr;
    private List<McserverAddrDTO> mcserverAddr;
    private KafkaAddrDTO kafkaAddr;
    private String internalNginxAddr;
    private List<RmServerAddrDTO> rmserverAddr;
    private String vodclustermgrAddr;
    private List<CascadeMgrAddrDTO> cascadeMgrAddr;

    private static final String KEY = "config.json";
    private static final String KAFKA_DEFAULT_IP = "{KAFKA_INTERNAL_IP}:9092";
    private static final String NGINX_DEFAULT_IP = "{MAIN_INTERNAL_IP}:11111";

    @Override
    public McAccessCM toModel(Map<String, String> cm, String nodeName) {
        String mcAccessStr = cm.get(KEY);
        McAccessDTO mcAccessDTO = getMcAccessDTO(mcAccessStr);
        this.nodeName = nodeName;
        this.nginxAddr = mcAccessDTO.getNginxAddr();
        this.srsAddr = mcAccessDTO.getSrsAddr();
        this.mcserverAddr = mcAccessDTO.getMcserverAddr();
        this.rmserverAddr = mcAccessDTO.getRmserverAddr();
        this.vodclustermgrAddr = mcAccessDTO.getVodclustermgrAddr();
        this.cascadeMgrAddr = mcAccessDTO.getCascadeMgr();
        this.kafkaAddr = null == mcAccessDTO.getKafkaAddr() ? new KafkaAddrDTO() : mcAccessDTO.getKafkaAddr();
        if (StringUtils.isNotBlank(this.kafkaAddr.getKafkaIp()) && KAFKA_DEFAULT_IP.equals(this.kafkaAddr.getKafkaIp())) {
            this.kafkaAddr.setKafkaIp("");
        }
        if (StringUtils.isNotBlank(this.kafkaAddr.getKafkaAlarmAddr()) && KAFKA_DEFAULT_IP.equals(this.kafkaAddr.getKafkaAlarmAddr())) {
            this.kafkaAddr.setKafkaAlarmAddr("");
        }
        if (StringUtils.isNotBlank(this.kafkaAddr.getKafkaAudioEnergyAddr()) && KAFKA_DEFAULT_IP.equals(this.kafkaAddr.getKafkaAudioEnergyAddr())) {
            this.kafkaAddr.setKafkaAudioEnergyAddr("");
        }
        this.internalNginxAddr = StringUtils.isNotBlank(mcAccessDTO.getInternalNginxAddr()) && NGINX_DEFAULT_IP.equals(mcAccessDTO.getInternalNginxAddr()) ? "" : mcAccessDTO.getInternalNginxAddr();
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        if (!CollectionUtils.isEmpty(mcserverAddr)) {
            mcserverAddr.forEach(mcserverAddrDTO -> {
                mcserverAddrDTO.setUpThriftPort("20001");
            });
        }
        if (null == kafkaAddr) {
            kafkaAddr = new KafkaAddrDTO();
        }
        if (StringUtils.isBlank(kafkaAddr.getKafkaIp())) {
            kafkaAddr.setKafkaIp(KAFKA_DEFAULT_IP);
        }
        if (StringUtils.isBlank(kafkaAddr.getKafkaAlarmAddr())) {
            kafkaAddr.setKafkaAlarmAddr(KAFKA_DEFAULT_IP);
        }
        if (StringUtils.isBlank(kafkaAddr.getKafkaAudioEnergyAddr())) {
            kafkaAddr.setKafkaAudioEnergyAddr(KAFKA_DEFAULT_IP);
        }
        if (StringUtils.isBlank(internalNginxAddr)) {
            internalNginxAddr = NGINX_DEFAULT_IP;
        }
        McAccessDTO mcAccessDTO = new McAccessDTO();
        mcAccessDTO.setVersion("1");
        mcAccessDTO.setNginxAddr(nginxAddr);
        mcAccessDTO.setSrsAddr(srsAddr);
        mcAccessDTO.setMcserverAddr(mcserverAddr);
        mcAccessDTO.setKafkaAddr(kafkaAddr);
        mcAccessDTO.setInternalNginxAddr(internalNginxAddr);
        mcAccessDTO.setVodclustermgrAddr(vodclustermgrAddr);
        mcAccessDTO.setRmserverAddr(rmserverAddr);
        mcAccessDTO.setCascadeMgr(cascadeMgrAddr);
        cm.put(KEY, JsonMapper.nonEmptyMapper().toJson(mcAccessDTO));
        return cm;
    }

    private McAccessDTO getMcAccessDTO(String mcAccessStr) {
        McAccessDTO mcAccessDTO = new JsonMapper().fromJson(mcAccessStr, McAccessDTO.class);
        return null == mcAccessDTO ? new McAccessDTO() : mcAccessDTO;
    }

}
