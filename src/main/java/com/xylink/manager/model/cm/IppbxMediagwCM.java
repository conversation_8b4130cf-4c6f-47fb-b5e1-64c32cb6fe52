package com.xylink.manager.model.cm;


import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;


@Getter
@Setter
public class IppbxMediagwCM implements ICMDto<IppbxMediagwCM> {


    private String siteCode;
    private String nodeName;


    @Override
    public IppbxMediagwCM toModel(Map<String, String> cm, String nodeName) {
        this.setNodeName(nodeName);
        if(null == cm) return this;
        String code = cm.get(nodeName + "-SITECODE");
        this.setSiteCode(StringUtils.isBlank(code) ? cm.get("DEFAULT-SITECODE") : code);
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> siggwConfig = new HashMap<>();

        siggwConfig.put(nodeName + "-SITECODE", siteCode);

        return siggwConfig;
    }
}

