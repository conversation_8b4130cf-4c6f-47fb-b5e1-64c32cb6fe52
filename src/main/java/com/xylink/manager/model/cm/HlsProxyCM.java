package com.xylink.manager.model.cm;

import com.xylink.config.ProxyConstants;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022/2/24 10:11 上午
 */
@Getter
@Setter
public class HlsProxyCM implements ICMDto<HlsProxyCM>{
    private String nginxPort;
    private String nginxSSLPort;
    private String remoteServerAddress;
    private String remoteNginxPort;

    private String nodeName;

    @Override
    public HlsProxyCM toModel(Map<String, String> cm, String nodeName) {
        this.nginxPort = cm.get(nodeName + ProxyConstants.NGINX_PORT);
        this.nginxSSLPort = cm.get(nodeName + ProxyConstants.NGINX_SSL_PORT);
        this.remoteServerAddress = cm.get(nodeName + ProxyConstants.UPSTREAM_ADDR);
        this.remoteNginxPort = cm.get(nodeName + ProxyConstants.UPSTREAM_PORT);
        this.nodeName = nodeName;
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        cm.put(nodeName +ProxyConstants.NGINX_PORT, nginxPort);
        cm.put(nodeName +ProxyConstants.NGINX_SSL_PORT, nginxSSLPort);
        cm.put(nodeName +ProxyConstants.UPSTREAM_ADDR, remoteServerAddress);
        cm.put(nodeName +ProxyConstants.UPSTREAM_PORT, remoteNginxPort);
        return cm;
    }
}
