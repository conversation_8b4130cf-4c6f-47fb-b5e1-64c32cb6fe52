package com.xylink.manager.model.cm;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.model.deploy.Node;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.util.SpringBeanUtil;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/9/3 10:39 上午
 */
public final class McUtils {
    private final IDeployService deployService;

    public McUtils() {
        this.deployService = SpringBeanUtil.getBean(IDeployService.class);
    }

    /**
     * mc 是否只部署一个
     *
     * @return boolean
     */
    public String onlyOneMcNodeName() {
        List<Pod> allMc = deployService.listPodsByAppLabel("private-mcserver");
        if (!CollectionUtils.isEmpty(allMc) && allMc.size() == 1) {
            return allMc.get(0).getNodeName();
        }
        return null;
    }

    /**
     * 如果main上部署mc
     * 只有一个mc，dmcu默认显示main上的mc
     * 有2个mc，dmcu默认显示main上的，需要手动输入另一个
     * <p>
     * 如果main上没有部署mc
     * 只有一个mc，dmcu默认显示mc
     * 有2个mc，dmcu全部手动输入
     *
     * @return UpThriftIp
     */
    public UpThriftIp mcuDefaultUpThriftIp() {
        List<UpThriftIp> list = allMcInternalIpList();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        if (list.size() == 1) {
            return list.get(0);
        }
        return mainMc(list);
    }

    private List<UpThriftIp> allMcInternalIpList() {
        ConfigMap configMap = deployService.getConfigMapByName(Constants.CONFIGMAP_MC, Constants.NAMESPACE_DEFAULT);
        if (configMap == null || CollectionUtils.isEmpty(configMap.getData())) {
            return Collections.emptyList();
        }
        final String suffix = "-INTERNAL-IP";
        return configMap.getData().entrySet().stream()
                .filter(data -> data.getKey().endsWith(suffix))
                .map(data -> new UpThriftIp(data.getKey().replace(suffix, ""), data.getValue())).collect(Collectors.toList());
    }

    public String getGatherMcIp() {
        return deployService.getConfigMapAllIp().getData().get(NetworkConstants.GATHER_MC_PRIVATE_IP);
    }

    private UpThriftIp mainMc(List<UpThriftIp> all) {
        Node mainNode = deployService.listNodesByLabels(Constants.TYPE, Constants.NODETYPE_MAIN).stream().findFirst().orElse(null);
        Node commonNode = deployService.listNodesByLabels(Constants.TYPE, Constants.NODE_TYPE_COMMON_MAIN).stream().findFirst().orElse(null);
        String nodeName = "private-docker-main";
        if (commonNode != null) {
            nodeName = commonNode.getName();
        }
        if (mainNode != null) {
            nodeName = mainNode.getName();
        }
        String finalNodeName = nodeName;
        return all.stream().filter(item -> finalNodeName.equals(item.getHostName())).findFirst().orElse(null);
    }

    public static class UpThriftIp {
        private String hostName;
        private String internalIp;

        public UpThriftIp(String hostName, String internalIp) {
            this.hostName = hostName;
            this.internalIp = internalIp;
        }

        public boolean isMainMc() {
            return "private-docker-main".equalsIgnoreCase(this.getHostName());
        }

        public String getHostName() {
            return hostName;
        }

        public void setHostName(String hostName) {
            this.hostName = hostName;
        }

        public String getInternalIp() {
            return internalIp;
        }

        public void setInternalIp(String internalIp) {
            this.internalIp = internalIp;
        }
    }
}
