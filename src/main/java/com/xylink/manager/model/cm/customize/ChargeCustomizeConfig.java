package com.xylink.manager.model.cm.customize;

import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServiceErrorException;
import com.xylink.manager.model.cm.customize.config.ChargeConferenceConfigKeyEnum;
import com.xylink.manager.model.cm.customize.config.ChargeConfigKeyEnum;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.ServerListService;
import com.xylink.manager.service.remote.callpermission.dto.EnterpriseConfigLimitResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022/9/7 5:08 下午
 */
@Component
@Slf4j
public class ChargeCustomizeConfig implements ICustomizeConfigStrategy {
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private ServerListService serverListService;

    @Override
    public String getLabelName() {
        return Labels.charge.label();
    }

    @Override
    public Object getCustomizeConfig(String nodeName) {
        EnterpriseConfigLimitResponse[] arr;
        String url = "http://" + getInnerNginxAddr() + "/api/rest/internal/v1/charge/enterprise/config/list?enterpriseId=default_enterprise";
        try {
            arr = restTemplate.getForObject(url, EnterpriseConfigLimitResponse[].class);
        } catch (Exception e) {
            log.error("invoke charge url:{} failed,get charge config failed", url, e);
            throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_CHARGE_FAILED);
        }

        Map<String, Object> data = new HashMap<>();
        data.put("nodeName", nodeName);
        List<EnterpriseConfigLimitResponse> response = null;
        if (!ObjectUtils.isEmpty(arr)) {
            response = Arrays.stream(arr).collect(Collectors.toList());
        }

        ChargeConfigKeyEnum[] values = ChargeConfigKeyEnum.values();
        for (ChargeConfigKeyEnum keyEnum : values) {
            data.put(keyEnum.getShowName(), getValue(keyEnum, response));
        }


        String conferenceConfigUrl = "http://" + getInnerNginxAddr() + "/api/rest/internal/v1/charge/conferenceConfig/ent/default_enterprise";
        Map<String, Object> resultMap;
        try {
            resultMap = restTemplate.getForObject(conferenceConfigUrl, Map.class);
        } catch (Exception e) {
            log.error("invoke charge url:{} failed,get charge config failed", conferenceConfigUrl, e);
            throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_CHARGE_FAILED);
        }

        ChargeConferenceConfigKeyEnum[] conferenceConfigKeyEnums = ChargeConferenceConfigKeyEnum.values();
        for (ChargeConferenceConfigKeyEnum temp : conferenceConfigKeyEnums) {
            if (!CollectionUtils.isEmpty(resultMap) && resultMap.containsKey(temp.getShowName())) {
                data.put(temp.getShowName(), resultMap.get(temp.getShowName()));
            } else {
                data.put(temp.getShowName(), temp.getDefaultValue());
            }
        }

        return data;
    }

    @Override
    public void saveCustomizeConfig(LinkedHashMap data) {
        List<Map<String, Object>> request = convertToRequest(data);
        String innerNginxAddr = getInnerNginxAddr();
        String url = "http://" + innerNginxAddr + "/api/rest/internal/v1/charge/enterprise/default_enterprise/config";
        for (Map<String, Object> map : request) {
            try {
                restTemplate.postForObject(url, map, Void.class);
            } catch (Exception e) {
                log.error("invoke charge url:{} failed,save charge config failed", url, e);
                throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_CHARGE_FAILED);
            }
        }

        String conferenceUrl = "http://" + innerNginxAddr + "/api/rest/internal/v1/charge/conferenceConfig/ent/default_enterprise";
        try {
            restTemplate.postForObject(conferenceUrl, convertToConferenceRequest(data), Void.class);
        } catch (Exception e) {
            log.error("invoke charge url:{} failed,save charge config failed", conferenceUrl, e);
            throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_CHARGE_FAILED);
        }
    }

    public String getInnerNginxAddr() {
        return serverListService.getMainNodeInternalIP() + ":" + serverListService.getMainNodeInternalPort();
    }

    private Object getValue(ChargeConfigKeyEnum keyEnum, List<EnterpriseConfigLimitResponse> response) {
        if (!CollectionUtils.isEmpty(response)) {
            for (EnterpriseConfigLimitResponse enterpriseConfigLimitResponse : response) {
                if (keyEnum.name().equals(enterpriseConfigLimitResponse.getConfigName())) {
                    response.remove(enterpriseConfigLimitResponse);
                    return enterpriseConfigLimitResponse.getConfigValue();
                }
            }
        }
        return keyEnum.getDefaultValue();
    }

    private List<Map<String, Object>> convertToRequest(LinkedHashMap sourceRequest) {
        List<String> keys = Arrays.stream(ChargeConfigKeyEnum.values()).map(ChargeConfigKeyEnum::getShowName).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(keys)) {
            return Collections.emptyList();
        }
        List<Map<String, Object>> request = new ArrayList<>();
        sourceRequest.forEach((key, value) -> {
            if (keys.contains(key)) {
                // showName--->name
                ChargeConfigKeyEnum chargeConfigKeyEnum = ChargeConfigKeyEnum.valueOfShowName(String.valueOf(key));
                if (chargeConfigKeyEnum != null) {
                    Map<String, Object> item = new HashMap<>();
                    item.put("configName", chargeConfigKeyEnum.name());
                    item.put("configValue", value);
                    request.add(item);
                }
            }
        });
        return request;
    }

    private static HttpEntity convertToConferenceRequest(LinkedHashMap sourceRequest) {
        List<String> keys = Arrays.stream(ChargeConferenceConfigKeyEnum.values()).map(ChargeConferenceConfigKeyEnum::getShowName).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(keys)) {
            return null;
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        Map<String, Object> params = new HashMap<>();
        sourceRequest.forEach((key, value) -> {
            if (keys.contains(key)) {
                ChargeConferenceConfigKeyEnum configKeyEnum = ChargeConferenceConfigKeyEnum.valueOfShowName(String.valueOf(key));
                if (configKeyEnum != null) {
                    params.put(configKeyEnum.name(), value);
                }
            }
        });

        return new HttpEntity<>(params, headers);
    }
}
