package com.xylink.manager.model.cm;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Setter
@Getter
public class EduDatingCM implements ICMDto<EduDatingCM> {

    private String nodeName;
    private String master;
    private String slave;
    private String courseId;
    private String courseTask;


//    @Getter(AccessLevel.PRIVATE)
//    final private String THIRD_PARTY_APPID = "THIRD_PARTY_APPID";
//    @Getter(AccessLevel.PRIVATE)
//    final private String THIRD_PARTY_APPSECRET = "THIRD_PARTY_APPSECRET";
//    @Getter(AccessLevel.PRIVATE)
//    final private String THIRD_PARTY_FTP_HOST = "THIRD_PARTY_FTP_HOST";
//    @Getter(AccessLevel.PRIVATE)
//    final private String THIRD_PARTY_FTP_PASSWORD = "THIRD_PARTY_FTP_PASSWORD";
//    @Getter(AccessLevel.PRIVATE)
//    final private String THIRD_PARTY_FTP_PORT = "THIRD_PARTY_FTP_PORT";
//    @Getter(AccessLevel.PRIVATE)
//    final private String THIRD_PARTY_FTP_USERNAME = "THIRD_PARTY_FTP_USERNAME";
//    @Getter(AccessLevel.PRIVATE)
//    final private String THIRD_PARTY_URI_HOST = "THIRD_PARTY_URI_HOST";
    @Getter(AccessLevel.PRIVATE)
    final private String MASTER_CLOUD_IP = "MASTER_SIP_CLOUD_IP";
    @Getter(AccessLevel.PRIVATE)
    final private String SLAVE_CLOUD_IP = "SLAVE_SIP_CLOUD_IP";
    @Getter(AccessLevel.PRIVATE)
    final private String COURSE_ID = "COURSE_ID";
    @Getter(AccessLevel.PRIVATE)
    final private String COURSE_TASK_FLAG = "COURSE_TASK_FLAG";


    @Override
    public EduDatingCM toModel(Map<String, String> cm, String nodeName) {
        this.master = cm.get(MASTER_CLOUD_IP);
        this.slave = cm.get(SLAVE_CLOUD_IP);
        this.courseId = cm.get(COURSE_ID);
        this.courseTask = cm.get(COURSE_TASK_FLAG);
        this.nodeName = nodeName;
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        cm.put(MASTER_CLOUD_IP, master);
        cm.put(SLAVE_CLOUD_IP, slave);
        cm.put(COURSE_ID, courseId);
        cm.put(COURSE_TASK_FLAG, courseTask);
        return cm;
    }
}
