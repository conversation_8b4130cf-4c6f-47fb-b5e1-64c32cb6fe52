package com.xylink.manager.model.cm;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Setter
@Getter
public class EduSdkCM implements ICMDto<EduSdkCM> {

    private String courseHost;
    private String courseId;

    private String nodeName;

    @Getter(AccessLevel.PRIVATE)
    final private String NODE_EDU_SDK_CLOUD_COURSE_HOST = "-EDU-SDK-CLOUD-COURSE-HOST";

    @Getter(AccessLevel.PRIVATE)
    final private String NODE_EDU_SDK_COURSE_CLOUD_ID = "-EDU-SDK-COURSE-CLOUD-ID";

    @Override
    public EduSdkCM toModel(Map<String, String> cm, String nodeName) {
        this.nodeName = nodeName;
        this.courseHost = cm.get(nodeName + NODE_EDU_SDK_CLOUD_COURSE_HOST);
        this.courseId = cm.get(nodeName + NODE_EDU_SDK_COURSE_CLOUD_ID);
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {

        Map<String, String> cm = new HashMap<>();
        cm.put(nodeName + NODE_EDU_SDK_CLOUD_COURSE_HOST, courseHost);
        cm.put(nodeName + NODE_EDU_SDK_COURSE_CLOUD_ID, courseId);
        return cm;
    }
}
