package com.xylink.manager.model.cm;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.ProxyConstants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.WebException;
import com.xylink.manager.service.ConfigService;
import com.xylink.util.NginxServerNameUtils;
import com.xylink.util.NginxValidUtils;
import com.xylink.util.SpringBeanUtil;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

@Setter
@Getter
@Slf4j
public class OpenrestyMainCM implements ICMDto<OpenrestyMainCM> {
    private static final String DEFAULT_ACCESS_CONTROL_ALLOW_METHODS = "GET,POST,PUT,DELETE,OPTIONS,PATCH";

    private String gzip;
    private String proxy;
    private String nodeName;
    private String allowAccessFrom;
    private String serverName;
    private String allowAllHost;
    private String allowHttp;
    private String port;
    private String sslPort;
    /**
     * 内网IP段 用于nginx标记客户端请求来源
     */
    private String intranetAddressSegment;
    /**
     * 内网testlink.html 地址
     */
    private String innerTestLinkAddr;

    private String signSwitch;

    private String logsBandwidthLimit;

    private String logUploadMaxConnCount;

    private String amsHttpPort;

    private String amsHttpsPort;

    private List<String> accessControlAllowOrigin;

    private String sdkIpWhiteList;

    private String thirdBridgeSwitch;
    /**
     * waf 配置
     */
    private String wafEnable;
    private String wafMode;
    private String accessControlAllowMethods;



    @Getter(AccessLevel.PRIVATE)
    final private String GZIP_KEY = "MAIN_NGINX_GZIP";
    @Getter(AccessLevel.PRIVATE)
    final private String PROXY_KEY = "ACCESS_PROXY";
    @Getter(AccessLevel.PRIVATE)
    final private String ALLOW_ACCESS_FROM_KEY = "ALLOW_ACCESS_FROM";
    @Getter(AccessLevel.PRIVATE)
    final private String SERVER_NAME_KEY = "-NIGNX_SERVER_NAME";
    @Getter(AccessLevel.PRIVATE)
    final private String SERVER_NAME_KEY_WITHOUT_HOSTNAME = "NIGNX_SERVER_NAME";
    @Getter(AccessLevel.PRIVATE)
    final private String ALLOW_ALL_HOST_KEY = "-ALLOW_ALL_HOST";
    @Getter(AccessLevel.PRIVATE)
    final private String ALLOW_ALL_HOST_KEY_WITHOUT_HOSTNAME = "ALLOW_ALL_HOST";
    @Getter(AccessLevel.PRIVATE)
    final private String ALLOW_HTTP_KEY = "-ALLOW_HTTP";
    @Getter(AccessLevel.PRIVATE)
    final private String ALLOW_HTTP_KEY_WITHOUT_HOSTNAME = "ALLOW_HTTP";
    @Getter(AccessLevel.PRIVATE)
    final private String NGINX_PORT_KEY = "MAIN_NGINX_PORT";
    @Getter(AccessLevel.PRIVATE)
    final private String NGINX_SSL_PORT_KEY = "MAIN_NGINX_SSL_PORT";
    @Getter(AccessLevel.PRIVATE)
    final private String INTRANET_ADDRESS_SEGMENT = "INTRANET_ADDRESS_SEGMENT";
    @Getter(AccessLevel.PRIVATE)
    final private String INNER_TEST_LINK_ADDR = "INNER_TEST_LINK_ADDR";
    @Getter(AccessLevel.PRIVATE)
    final private String LIMIT_RATE_FOR_LOGUPLOAD_BANDWIDTH = "-LIMIT-RATE-FOR-LOGUPLOAD-BANDWIDTH";
    @Getter(AccessLevel.PRIVATE)
    final private String LIMIT_RATE_FOR_LOGUPLOAD_BANDWIDTH_WITHOUT_HOSTNAME = "LIMIT-RATE-FOR-LOGUPLOAD-BANDWIDTH";
    @Getter(AccessLevel.PRIVATE)
    final private String LOGUPLOAD_MAX_CONN_COUNT = "-LOGUPLOAD-MAX-CONN-COUNT";
    @Getter(AccessLevel.PRIVATE)
    final private String LOGUPLOAD_MAX_CONN_COUNT_WITHOUT_HOSTNAME = "LOGUPLOAD-MAX-CONN-COUNT";
    @Getter(AccessLevel.PRIVATE)
    final private String SDK_IP_WHITE_LIST = "-SDK_IP_WHITE_LIST";
    @Getter(AccessLevel.PRIVATE)
    final private String SDK_IP_WHITE_LIST_WITHOUT_HOSTNAME = "SDK_IP_WHITE_LIST";
    @Getter(AccessLevel.PRIVATE)
    final private String WAF_ENABLE = "WAF_ENABLE";
    @Getter(AccessLevel.PRIVATE)
    final private String WAF_MODE = "WAF_MODE";
    @Getter(AccessLevel.PRIVATE)
    final private String NGINX_ACCESS_CONTROL_ALLOW_METHODS = "NGINX_ACCESS_CONTROL_ALLOW_METHODS";

    @Getter(AccessLevel.PRIVATE)
    final private String THIRD_BRIDGE_SWITCH_KEY_WITHOUT_HOSTNAME = "THIRD_BRIDGE_SWITCH";
    @Getter(AccessLevel.PRIVATE)
    final private String ACCESS_CONTROL_ALLOW_ORIGIN_WITHOUT_HOSTNAME = "ACCESS_CONTROL_ALLOW_ORIGIN";

    @Override
    public OpenrestyMainCM toModel(Map<String, String> cm, String nodeName) {
        this.gzip = cm.get(GZIP_KEY);
        String sign = cm.get("MAIN_NGINX_SIGN");
        this.signSwitch = StringUtils.isBlank(sign) ? "off" : sign;
        this.proxy = cm.get(PROXY_KEY);
        this.allowAccessFrom = cm.get(ALLOW_ACCESS_FROM_KEY);
        this.serverName = cm.get(SERVER_NAME_KEY_WITHOUT_HOSTNAME);
        this.nodeName = nodeName;
        this.allowHttp = cm.get(ALLOW_HTTP_KEY_WITHOUT_HOSTNAME);
        String nginxPort = cm.get(NGINX_PORT_KEY);
        this.port = StringUtils.isBlank(nginxPort) ? "80" : nginxPort;

        String nginxSslPort = cm.get(NGINX_SSL_PORT_KEY);
        this.sslPort = StringUtils.isBlank(nginxSslPort) ? "443" : nginxSslPort;
        this.intranetAddressSegment = cm.get(INTRANET_ADDRESS_SEGMENT);
        String innerTestLinkAddrInCm = cm.get(INNER_TEST_LINK_ADDR);
        if (StringUtils.isNotBlank(innerTestLinkAddrInCm)) {
            this.innerTestLinkAddr = innerTestLinkAddrInCm;
        }

        String logsBandwidthLimitValue = cm.get(LIMIT_RATE_FOR_LOGUPLOAD_BANDWIDTH_WITHOUT_HOSTNAME);
        this.logsBandwidthLimit = StringUtils.isBlank(logsBandwidthLimitValue) ? "81920" : logsBandwidthLimitValue;

        String logUploadMaxConnCountValue = cm.get(LOGUPLOAD_MAX_CONN_COUNT_WITHOUT_HOSTNAME);
        this.logUploadMaxConnCount = StringUtils.isBlank(logUploadMaxConnCountValue) ? "15" : logUploadMaxConnCountValue;

        this.amsHttpPort = cm.get(ProxyConstants.AMS_HTTP_PORT);
        this.amsHttpsPort = cm.get(ProxyConstants.AMS_HTTPS_PORT);

        String accessControlAllowOriginValue = cm.get(ACCESS_CONTROL_ALLOW_ORIGIN_WITHOUT_HOSTNAME);
        this.accessControlAllowOrigin = NginxValidUtils.allowedOriginToArrary(accessControlAllowOriginValue);
        this.sdkIpWhiteList = cm.get(SDK_IP_WHITE_LIST_WITHOUT_HOSTNAME);
        this.thirdBridgeSwitch = StringUtils.isBlank(cm.get(THIRD_BRIDGE_SWITCH_KEY_WITHOUT_HOSTNAME))?"false":cm.get(THIRD_BRIDGE_SWITCH_KEY_WITHOUT_HOSTNAME);
        this.wafEnable = StringUtils.isBlank(cm.get(WAF_ENABLE)) ? "off" : cm.get(WAF_ENABLE);
        this.wafMode = StringUtils.isBlank(cm.get(WAF_MODE)) ? "record" : cm.get(WAF_MODE);
        this.accessControlAllowMethods = StringUtils.isBlank(cm.get(NGINX_ACCESS_CONTROL_ALLOW_METHODS)) ? DEFAULT_ACCESS_CONTROL_ALLOW_METHODS : cm.get(NGINX_ACCESS_CONTROL_ALLOW_METHODS);
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        cm.put(GZIP_KEY, gzip);
        cm.put(PROXY_KEY, proxy);
        cm.put(ALLOW_ACCESS_FROM_KEY, allowAccessFrom);
        cm.put(nodeName + SERVER_NAME_KEY, StringUtils.isNotBlank(serverName) ? serverName : "localhost");
        cm.put(SERVER_NAME_KEY_WITHOUT_HOSTNAME, StringUtils.isNotBlank(serverName) ? serverName : "localhost");
        cm.put(NGINX_PORT_KEY, StringUtils.isNotBlank(port) ? port : "80");
        cm.put(NGINX_SSL_PORT_KEY, StringUtils.isNotBlank(sslPort) ? sslPort : "443");
        cm.put(nodeName + ALLOW_ALL_HOST_KEY, (StringUtils.isNotBlank(serverName) && !"localhost".equalsIgnoreCase(serverName)) ? "false" : "true");
        cm.put(ALLOW_ALL_HOST_KEY_WITHOUT_HOSTNAME, (StringUtils.isNotBlank(serverName) && !"localhost".equalsIgnoreCase(serverName)) ? "false" : "true");
        cm.put("HTTP_PORT_KEY", "{NGINX_PORT}");
        cm.put("HTTPS_PORT_KEY", "{NGINX_SSL_PORT}");
        cm.put(nodeName + ALLOW_HTTP_KEY, allowHttp);
        cm.put(ALLOW_HTTP_KEY_WITHOUT_HOSTNAME, allowHttp);
        cm.put(INTRANET_ADDRESS_SEGMENT, intranetAddressSegment);
        cm.put(INNER_TEST_LINK_ADDR, innerTestLinkAddr);
        cm.put("MAIN_NGINX_SIGN", signSwitch);

        String logsBandwidthLimitKey = nodeName + LIMIT_RATE_FOR_LOGUPLOAD_BANDWIDTH;
        cm.put(logsBandwidthLimitKey, StringUtils.isBlank(this.logsBandwidthLimit) ? "81920" : this.logsBandwidthLimit);
        cm.put(LIMIT_RATE_FOR_LOGUPLOAD_BANDWIDTH_WITHOUT_HOSTNAME, StringUtils.isBlank(this.logsBandwidthLimit) ? "81920" : this.logsBandwidthLimit);

        String logUploadMaxConnCountKey = nodeName + LOGUPLOAD_MAX_CONN_COUNT;
        cm.put(logUploadMaxConnCountKey, StringUtils.isBlank(this.logUploadMaxConnCount) ? "15" : this.logUploadMaxConnCount);
        cm.put(LOGUPLOAD_MAX_CONN_COUNT_WITHOUT_HOSTNAME, StringUtils.isBlank(this.logUploadMaxConnCount) ? "15" : this.logUploadMaxConnCount);

        if (StringUtils.isNotBlank(this.amsHttpPort) && StringUtils.isNotBlank(this.amsHttpsPort)) {
            cm.put(ProxyConstants.ALLOW_ACCESS_AMS, "true");
            cm.put(ProxyConstants.AMS_HTTP_PORT, this.amsHttpPort);
            cm.put(ProxyConstants.AMS_HTTPS_PORT, this.amsHttpsPort);
            try {
                ConfigService configService = SpringBeanUtil.getBean(ConfigService.class);
                configService.saveBuffetConfig("AMS-http代理端口", "HTTP_CONFIG", this.amsHttpPort, this.amsHttpPort);
                configService.saveBuffetConfig("AMS-https代理端口", "HTTPS_CONFIG", this.amsHttpsPort, this.amsHttpsPort);
            } catch (Exception e) {
                log.error("Ams database configuration fails and the data is inconsistent between configmap and db, please perform the operation again！！！");
            }
        } else {
            cm.put(ProxyConstants.ALLOW_ACCESS_AMS, "false");
            cm.put(ProxyConstants.AMS_HTTP_PORT, "");
            cm.put(ProxyConstants.AMS_HTTPS_PORT, "");
        }
        cm.put(nodeName + ProxyConstants.ACCESS_CONTROL_ALLOW_ORIGIN,NginxValidUtils.allowedOriginToStr(this.accessControlAllowOrigin));
        cm.put(ACCESS_CONTROL_ALLOW_ORIGIN_WITHOUT_HOSTNAME,NginxValidUtils.allowedOriginToStr(this.accessControlAllowOrigin));
        cm.put(nodeName + SDK_IP_WHITE_LIST,this.sdkIpWhiteList);
        cm.put(SDK_IP_WHITE_LIST_WITHOUT_HOSTNAME,this.sdkIpWhiteList);
        cm.put(nodeName + Constants.THIRD_BRIDGE_SWITCH_KEY, StringUtils.isBlank(thirdBridgeSwitch) ? "false" : thirdBridgeSwitch);
        cm.put(THIRD_BRIDGE_SWITCH_KEY_WITHOUT_HOSTNAME, StringUtils.isBlank(thirdBridgeSwitch) ? "false" : thirdBridgeSwitch);
        cm.put(WAF_ENABLE, StringUtils.isBlank(wafEnable) ? "off" : wafEnable);
        cm.put(WAF_MODE, StringUtils.isBlank(wafMode) ? "record" : wafMode);
        cm.put(NGINX_ACCESS_CONTROL_ALLOW_METHODS, StringUtils.isBlank(accessControlAllowMethods) ? DEFAULT_ACCESS_CONTROL_ALLOW_METHODS : accessControlAllowMethods);
        return cm;
    }

    @Override
    public void beforeSave() {

        if (StringUtils.isNotBlank(serverName) && !"localhost".equals(serverName)) {
            Map<String, String> allIpMap = getDeployService().getConfigMapAllIp().getData();

            String domain = allIpMap.get(NetworkConstants.MAIN_DOMAIN_NAME);
            String publicIp = allIpMap.get(NetworkConstants.MAIN_PUBLIC_IP);
            String interIp = allIpMap.get(NetworkConstants.MAIN_INTERNAL_IP);

            String[] names = serverName.split(" ");
            Set<String> nameSet = new HashSet<>(Arrays.asList(names));
            if (!nameSet.contains(domain) || !nameSet.contains(publicIp) || !nameSet.contains(interIp)) {
                throw new WebException(ErrorStatus.MAIN_NGINX_SERVER_NAME_ERROR);
            }

            nameSet.remove(domain);
            nameSet.remove(publicIp);
            nameSet.remove(interIp);

            for (String name : nameSet) {
                if (!NginxServerNameUtils.isValid(name)) {
                    throw new WebException(ErrorStatus.MAIN_NGINX_SERVER_NAME_INVALID_ERROR);
                }
            }

            // Allow Origin
            NginxValidUtils.checkAllowedOrigin(accessControlAllowOrigin);
        }
    }

    @Override
    public OpenrestyMainCM setDefault(String nodeName) {
        this.setNodeName(nodeName);
        Map<String, String> allIpMap = getDeployService().getConfigMapAllIp().getData();
        if (allIpMap != null) {
            String interIp0 = allIpMap.get(NetworkConstants.MAIN_INTERNAL_IP);
            String port0 = allIpMap.get(NetworkConstants.MAIN_NGINX_PORT);
            this.innerTestLinkAddr = "http://" + interIp0 + (StringUtils.isBlank(port0) || "80".equals(port0) ? "" : (":" + port0)) + "/testlink.html";
        }
        return this;
    }
}
