package com.xylink.manager.model.cm;

import com.xylink.config.WebrtcSiggwConstants;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/03/06/11:12
 */
@Setter
@Getter
public class AllocatorServerCM implements ICMDto<AllocatorServerCM> {
    private String nodeName;
    private String allocatorSiteCode;

    @Override
    public AllocatorServerCM toModel(Map<String, String> cm, String nodeName) {
        this.nodeName = nodeName;
        this.allocatorSiteCode = cm.get(nodeName + WebrtcSiggwConstants.ALLOCATOR_SITECODE_SUFFIX);
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        cm.put(this.nodeName + WebrtcSiggwConstants.ALLOCATOR_SITECODE_SUFFIX, this.allocatorSiteCode);
        return cm;
    }
}
