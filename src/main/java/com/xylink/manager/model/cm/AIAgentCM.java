package com.xylink.manager.model.cm;

import joptsimple.internal.Strings;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import org.codehaus.plexus.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public class AI<PERSON>gentCM implements ICMDto<AIAgentCM> {

    @Getter(AccessLevel.PRIVATE)
    private static final String AIAGENT_MODEL_URL = "AIAGENT_MODEL_URL";
    @Getter(AccessLevel.PRIVATE)
    private static final String AIAGENT_MODEL_NAME = "AIAGENT_MODEL_NAME";
    @Getter(AccessLevel.PRIVATE)
    private static final String AIAGENT_MODEL_API_KEY = "AIAGENT_MODEL_API_KEY";
    @Getter(AccessLevel.PRIVATE)
    private static final String AIAGENT_MODEL_TIMEOUT = "AIAGENT_MODEL_TIMEOUT";

    private static final String AIAGENT_FUNCTIONCALL_TYPE = "AIAGENT_FUNCTIONCALL_TYPE";
    private static final String AIAGENT_MODEL_FUNCTIONCAL_NAME = "AIAGENT_MODEL_FUNCTIONCAL_NAME";
    private static final String AIAGENT_MODEL_FUNCTIONCAL_URL = "AIAGENT_MODEL_FUNCTIONCAL_URL";
    private static final String AIAGENT_MODEL_FUNCTIONCAL_API_KEY = "AIAGENT_MODEL_FUNCTIONCAL_API_KEY";
    private static final String AIAGENT_MODEL_FUNCTIONCAL_TIMEOUT = "AIAGENT_MODEL_FUNCTIONCAL_TIMEOUT";

    private String nodeName;
    private String aiAgentModelUrl;
    private String aiAgentModelName;
    private String aiAgentModelApiKey;
    private String aiAgentModelTimeout;

    private String functionCallType;
    private String functionCallName;
    private String functionCallUrl;
    private String functionCallApiKey;
    private String functionCallTimeout;


    @Override
    public AIAgentCM toModel(Map<String, String> cm, String nodeName) {

        this.aiAgentModelUrl = getValueOrDefault(cm.get(AIAGENT_MODEL_URL), Defaults.DEFAULT_MODEL_URL);
        this.aiAgentModelName = getValueOrDefault(cm.get(AIAGENT_MODEL_NAME), Defaults.DEFAULT_MODEL_NAME);
        this.aiAgentModelApiKey = getValueOrDefault(cm.get(AIAGENT_MODEL_API_KEY), Strings.EMPTY);
        this.aiAgentModelTimeout = getValueOrDefault(cm.get(AIAGENT_MODEL_TIMEOUT), Defaults.DEFAULT_MODEL_TIMEOUT);

        this.functionCallType = getValueOrDefault(cm.get(AIAGENT_FUNCTIONCALL_TYPE), Defaults.DEFAULT_FUNCTIONCALL_TYPE);
        this.functionCallName = cm.get(AIAGENT_MODEL_FUNCTIONCAL_NAME);
        this.functionCallUrl = cm.get(AIAGENT_MODEL_FUNCTIONCAL_URL);
        this.functionCallApiKey = cm.get(AIAGENT_MODEL_FUNCTIONCAL_API_KEY);
        this.functionCallTimeout = cm.get(AIAGENT_MODEL_FUNCTIONCAL_TIMEOUT);
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();

        cm.put(AIAGENT_MODEL_URL, getValueOrDefault(aiAgentModelUrl, Defaults.DEFAULT_MODEL_URL));
        cm.put(AIAGENT_MODEL_NAME, getValueOrDefault(aiAgentModelName, Defaults.DEFAULT_MODEL_NAME));
        cm.put(AIAGENT_MODEL_API_KEY, getValueOrDefault(aiAgentModelApiKey, Strings.EMPTY));
        cm.put(AIAGENT_MODEL_TIMEOUT, getValueOrDefault(aiAgentModelTimeout, Defaults.DEFAULT_MODEL_TIMEOUT));

        cm.put(AIAGENT_FUNCTIONCALL_TYPE, getValueOrDefault(functionCallType, Defaults.DEFAULT_FUNCTIONCALL_TYPE));
        cm.put(AIAGENT_MODEL_FUNCTIONCAL_NAME, functionCallName);
        cm.put(AIAGENT_MODEL_FUNCTIONCAL_URL, functionCallUrl);
        cm.put(AIAGENT_MODEL_FUNCTIONCAL_API_KEY, functionCallApiKey);
        cm.put(AIAGENT_MODEL_FUNCTIONCAL_TIMEOUT, functionCallTimeout);

        return cm;
    }

    private String getValueOrDefault(String value, String defaultValue) {
        return StringUtils.isBlank(value) ? defaultValue : value;
    }

    private static final class Defaults {
        public static final String DEFAULT_MODEL_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1";
        public static final String DEFAULT_MODEL_NAME = "deepseek-v3";
        public static final String DEFAULT_MODEL_TIMEOUT = "60.0";

        public static final String DEFAULT_FUNCTIONCALL_TYPE = "resuseMaster";
    }
}
