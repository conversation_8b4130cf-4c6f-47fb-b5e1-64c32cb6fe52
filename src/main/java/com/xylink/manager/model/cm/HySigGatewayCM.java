package com.xylink.manager.model.cm;

import com.xylink.config.HySigConstants;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Getter
@Setter
public class HySigGatewayCM implements ICMDto<HySigGatewayCM>{

    //红云SIP地址
    private String hySigAddr;
    //红云SIP端口
    private String hySigPort;
    //红云信令网关公网地址
    private String hyGatewayPublicIp;
    //红云信令网关内网地址
    private String hyGatewayIp;
    //红云信令网关端口
    private String hyGatewayPort;
    //红云信令网关SN
    private String hyGatewaySn;

    private String nodeName;





    @Override
    public HySigGatewayCM toModel(Map<String, String> cm, String nodeName) {
        this.nodeName = nodeName;
        this.hySigAddr = cm.get(nodeName + HySigConstants.HY_SIG_ADDRESS);
        this.hySigPort = cm.get(nodeName + HySigConstants.HY_SIG_PORT);
        this.hyGatewayPublicIp = cm.get(nodeName + HySigConstants.HY_SIGGATEWAY_PUBLIC_IP);
        this.hyGatewayIp = cm.get(nodeName + HySigConstants.HY_SIGGATEWAY_INTERNA_IP);
        this.hyGatewayPort = cm.get(nodeName + HySigConstants.HY_SIGGATEWAY_PORT);
        this.hyGatewaySn = cm.get(nodeName + HySigConstants.HY_SIGGATEWAY_SN);
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        cm.put(nodeName + HySigConstants.HY_SIG_ADDRESS, this.hySigAddr);
        cm.put(nodeName + HySigConstants.HY_SIG_PORT, this.hySigPort);
        cm.put(nodeName + HySigConstants.HY_SIGGATEWAY_PUBLIC_IP, this.hyGatewayPublicIp);
        cm.put(nodeName + HySigConstants.HY_SIGGATEWAY_INTERNA_IP, this.hyGatewayIp);
        cm.put(nodeName + HySigConstants.HY_SIGGATEWAY_PORT, this.hyGatewayPort);
        cm.put(nodeName + HySigConstants.HY_SIGGATEWAY_SN,
                StringUtils.isBlank(this.hyGatewaySn) ? UUID.randomUUID().toString() : this.hyGatewaySn.trim());

        return cm;
    }
}
