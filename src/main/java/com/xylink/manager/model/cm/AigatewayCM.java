package com.xylink.manager.model.cm;

import com.xylink.config.AiConstants;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: liyang
 * @DateTime: 2023/12/13 10:21 上午
 **/
@Setter
@Getter
public class AigatewayCM implements ICMDto<AigatewayCM> {

    private String nodeName;
    /**
     * 区域码
     */
    private String siteCode;
    /**
     * 公共服务平台地址:人脸检测地址
     */
    private String faceDetectUrl;
    /**
     * 公共服务平台地址:人脸识别地址
     */
    private String faceRecogUrl;
    /**
     * 公共服务平台接口超时
     */
    private String faceTimeout;
    /**
     * 语音合成AI服务地址
     */
    private String ttsUrl;
    /**
     * 服务认证ID
     */
    private String ttsAccessID;
    /**
     * 服务认证key
     */
    private String ttsAccessSecret;
    /**
     * 语音合成接口超时
     */
    private String ttsTimeout;
    /**
     * AI应用ID
     */
    private String aiAppId;
    /**
     * 语音播报人
     */
    private String ttsSpeaker;
    /**
     * 语音播报速度
     */
    private String ttsSpeed;
    /**
     * 语音播报音量
     */
    private String ttsVolume;
    /**
     * 语音播报主机
     */
    private String ttsHost;


    /**
     * 服务类型称（默认1）
     */
    private String serverType;
    /**
     * 开启翻译
     */
    private String enableTranslate;
    /**
     * 三方引擎名称（默认xylink）
     */
    private String thirdAsrEngine;
    /**
     * 三方引擎地址
     */
    private String thirdAsrUrl;
    /**
     * 三方引擎appId
     */
    private String thirdAsrAppId;
    /**
     * 三方引擎AccessKey
     */
    private String thirdAsrAccessKey;
    /**
     * 三方引擎AccessSecret
     */
    private String thirdAsrAccessSecret;


    @Override
    public AigatewayCM toModel(Map<String, String> cm, String nodeName) {
        String sitecode = cm.get(nodeName + AiConstants.AI_GATEWAY_SITE_CODE);
        String faceDetectUrl = cm.get(nodeName + AiConstants.AI_GATEWAY_FACE_DETECT_URL);
        String faceRecogUrl = cm.get(nodeName + AiConstants.AI_GATEWAY_FACE_RECOG_URL);
        String faceTimeout = cm.get(nodeName + AiConstants.AI_GATEWAY_FACE_TIMEOUT);
        String ttsUrl = cm.get(nodeName + AiConstants.AI_GATEWAY_TTS_URL);
        String ttsAccessID = cm.get(nodeName + AiConstants.AI_GATEWAY_TTS_ACCESS_ID);
        String ttsAccessSecret = cm.get(nodeName + AiConstants.AI_GATEWAY_TTS_ACCESS_SECRET);
        String ttsTimeout = cm.get(nodeName + AiConstants.AI_GATEWAY_TTS_TIMEOUT);

        String aiAppId = cm.get(nodeName + AiConstants.AI_GATEWAY_AI_APP_ID);
        String ttsSpeaker = cm.get(nodeName + AiConstants.AI_GATEWAY_TTS_SPEAKER);
        String ttsSpeed = cm.get(nodeName + AiConstants.AI_GATEWAY_TTS_SPEED);
        String ttsVolume = cm.get(nodeName + AiConstants.AI_GATEWAY_TTS_VOLUME);
        String ttsHost = cm.get(nodeName + AiConstants.AI_GATEWAY_TTS_HOST);

        String serverType = cm.get(nodeName + AiConstants.AI_GATEWAY_SERVICE_TYPE);
        String enableTranslate = cm.get(nodeName + AiConstants.AI_GATEWAY_ENABLE_TRANSLATE);
        String thirdAsrEngine = cm.get(nodeName + AiConstants.AI_GATEWAY_ASR_ENGINE);
        String thirdAsrUrl = cm.get(nodeName + AiConstants.AI_GATEWAY_ASR_URL);
        String thirdAsrAppId = cm.get(nodeName + AiConstants.AI_GATEWAY_ASR_APPID);
        String thirdAsrAccessKey = cm.get(nodeName + AiConstants.AI_GATEWAY_ASR_ACCESS_KEY);
        String thirdAsrAccessSecret = cm.get(nodeName + AiConstants.AI_GATEWAY_ASR_ACCESSS_SECURECT);

        this.siteCode = StringUtils.isBlank(sitecode) ? cm.get(AiConstants.DEFAULT_AI_GATEWAY_SITE_CODE) : sitecode;
        this.faceDetectUrl = faceDetectUrl;
        this.faceRecogUrl = faceRecogUrl;
        this.faceTimeout = StringUtils.isBlank(faceTimeout) ? cm.get(AiConstants.DEFAULT_AI_GATEWAY_FACE_TIMEOUT) : faceTimeout;
        this.ttsUrl = ttsUrl;
        this.ttsAccessID = ttsAccessID;
        this.ttsAccessSecret = ttsAccessSecret;
        this.ttsTimeout = StringUtils.isBlank(ttsTimeout) ? cm.get(AiConstants.DEFAULT_AI_GATEWAY_TTS_TIMEOUT) : ttsTimeout;

        this.aiAppId = aiAppId;
        this.ttsSpeaker = StringUtils.isBlank(ttsSpeaker) ? cm.get(AiConstants.DEFAULT_AI_GATEWAY_TTS_SPEAKER) : ttsSpeaker;
        this.ttsSpeed = StringUtils.isBlank(ttsSpeed) ? cm.get(AiConstants.DEFAULT_AI_GATEWAY_TTS_SPEED) : ttsSpeed;
        this.ttsVolume = StringUtils.isBlank(ttsVolume) ? cm.get(AiConstants.DEFAULT_AI_GATEWAY_TTS_VOLUME) : ttsVolume;
        this.ttsHost = ttsHost;

        this.serverType = StringUtils.isBlank(serverType) ? cm.get(AiConstants.DEFAULT_AI_GATEWAY_SERVICE_TYPE) : serverType;
        this.enableTranslate = StringUtils.isBlank(enableTranslate) ? cm.get(AiConstants.DEFAULT_AI_GATEWAY_ENABLE_TRANSLATE) : enableTranslate;
        this.thirdAsrEngine = StringUtils.isBlank(thirdAsrEngine) ? cm.get(AiConstants.DEFAULT_AI_GATEWAY_ASR_ENGINE) : thirdAsrEngine;
        this.thirdAsrUrl = thirdAsrUrl;
        this.thirdAsrAppId = thirdAsrAppId;
        this.thirdAsrAccessKey = thirdAsrAccessKey;
        this.thirdAsrAccessSecret = thirdAsrAccessSecret;

        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();

        cm.put(this.nodeName + AiConstants.AI_GATEWAY_SITE_CODE, this.siteCode);
        cm.put(this.nodeName + AiConstants.AI_GATEWAY_FACE_DETECT_URL, this.faceDetectUrl);
        cm.put(this.nodeName + AiConstants.AI_GATEWAY_FACE_RECOG_URL, this.faceRecogUrl);
        cm.put(this.nodeName + AiConstants.AI_GATEWAY_FACE_TIMEOUT, this.faceTimeout);
        cm.put(this.nodeName + AiConstants.AI_GATEWAY_TTS_URL, this.ttsUrl);
        cm.put(this.nodeName + AiConstants.AI_GATEWAY_TTS_ACCESS_ID, this.ttsAccessID);
        cm.put(this.nodeName + AiConstants.AI_GATEWAY_TTS_ACCESS_SECRET, this.ttsAccessSecret);
        cm.put(this.nodeName + AiConstants.AI_GATEWAY_TTS_TIMEOUT, this.ttsTimeout);

        cm.put(this.nodeName + AiConstants.AI_GATEWAY_AI_APP_ID, this.aiAppId);
        cm.put(this.nodeName + AiConstants.AI_GATEWAY_TTS_SPEAKER, this.ttsSpeaker);

        try {
            double speed = Double.parseDouble(this.ttsSpeed);
            if (AiConstants.DEFAULT_VALUE_MIN_AI_GATEWAY_TTS_SPEED > speed || speed > AiConstants.DEFAULT_VALUE_MAX_AI_GATEWAY_TTS_SPEED) {
                this.ttsSpeed = AiConstants.DEFAULT_VALUE_AI_GATEWAY_TTS_SPEED;
            }
        } catch (Exception e) {
            this.ttsSpeed = AiConstants.DEFAULT_VALUE_AI_GATEWAY_TTS_SPEED;
        }
        try {
            int volume = Integer.parseInt(this.ttsVolume);
            if (volume > AiConstants.DEFAULT_VALUE_MAX_GATEWAY_TTS_VOLUME) {
                this.ttsVolume = AiConstants.DEFAULT_VALUE_GATEWAY_TTS_VOLUME;
            }
        } catch (Exception e) {
            this.ttsVolume = AiConstants.DEFAULT_VALUE_GATEWAY_TTS_VOLUME;
        }

        cm.put(this.nodeName + AiConstants.AI_GATEWAY_TTS_SPEED, this.ttsSpeed);
        cm.put(this.nodeName + AiConstants.AI_GATEWAY_TTS_VOLUME, this.ttsVolume);

        cm.put(this.nodeName + AiConstants.AI_GATEWAY_TTS_HOST, this.ttsHost);

        cm.put(this.nodeName + AiConstants.AI_GATEWAY_SERVICE_TYPE, this.serverType);
        cm.put(this.nodeName + AiConstants.AI_GATEWAY_ENABLE_TRANSLATE, this.enableTranslate);
        cm.put(this.nodeName + AiConstants.AI_GATEWAY_ASR_ENGINE, this.thirdAsrEngine);
        cm.put(this.nodeName + AiConstants.AI_GATEWAY_ASR_URL, this.thirdAsrUrl);
        cm.put(this.nodeName + AiConstants.AI_GATEWAY_ASR_APPID, this.thirdAsrAppId);
        cm.put(this.nodeName + AiConstants.AI_GATEWAY_ASR_ACCESS_KEY, this.thirdAsrAccessKey);
        cm.put(this.nodeName + AiConstants.AI_GATEWAY_ASR_ACCESSS_SECURECT, this.thirdAsrAccessSecret);
        return cm;
    }
}
