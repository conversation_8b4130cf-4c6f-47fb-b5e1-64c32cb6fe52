package com.xylink.manager.model.cm;

import com.xylink.config.NetworkConstants;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: liyang
 * @DateTime: 2024/5/28 4:42 下午
 **/
@Setter
@Getter
public class ThirdBridgeCM implements ICMDto<ThirdBridgeCM> {
    /**
     * 三方对接，访问外网nginx配置
     */
    private String externalAccessPath;
    private String nodeName;

    private final static String THIRD_BRIDGE_EXTERNAL_ACCESS_PATH_SUFFIX = "-THIRD-BRIDGE-EXTERNAL-ACCESS-PATH";

    @Override
    public ThirdBridgeCM setDefault(String nodeName) {
        Map<String, String> allIp = getDeployService().getConfigMapAllIp().getData();
        this.externalAccessPath = "https://" + allIp.get(NetworkConstants.MAIN_INTERNAL_IP) + ":" + allIp.get(NetworkConstants.MAIN_NGINX_SSL_PORT);
        return this;
    }

    @Override
    public ThirdBridgeCM toModel(Map<String, String> cm, String nodeName) {
        if (StringUtils.isNotBlank(cm.get(nodeName + THIRD_BRIDGE_EXTERNAL_ACCESS_PATH_SUFFIX))) {
            this.externalAccessPath = cm.get(nodeName + THIRD_BRIDGE_EXTERNAL_ACCESS_PATH_SUFFIX);
        }
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> map = new HashMap<>();
        if (externalAccessPath.endsWith("/")) {
            this.externalAccessPath = externalAccessPath.substring(0, externalAccessPath.length() - 1);
        }
        map.put(this.nodeName + THIRD_BRIDGE_EXTERNAL_ACCESS_PATH_SUFFIX, externalAccessPath);
        return map;
    }
    //http://{MAIN_INTERNAL_IP}:{NGINX_PORT}

}
