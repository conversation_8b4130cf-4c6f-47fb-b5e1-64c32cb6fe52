package com.xylink.manager.model.cm;

import com.xylink.config.Constants;
import com.xylink.config.DmcuConstants;
import com.xylink.config.HlsConstants;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.service.CalculateMediaParametersService;
import com.xylink.util.IpUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Setter
@Getter
@Slf4j
public class HlsCM extends AbstractMediaCM implements ICMDto<HlsCM> {

    private String nodeName;
    private String dmcuComments;
    private String siteCode;
    private String upThriftIp;
    private String upThriftIp2;
    private String selfThriftIp;
    private String selfThriftIp2;
    private String selfThriftPort;
    private String selfThriftPort2;
    private String maxRxBandWidth;
    private String maxTxBandWidth;
    private String mediaProcessTaskNum;
    private String mediaEndpointNumPerMediaTask;
    /**
     * 外网终端连接端口
     */
    private String dmcuClientPort;
    /**
     * 内网终端连接端口
     */
    private String dmcuClientInPort;
    private String dmcuType;
    private String dmcuResourceType;
    private String dmcuConfType;
    private String kafkaIp;
    private String encodeQPAdjust;
    private String transScoreCpuCoefficient;

    /**
     * MCU级联内网IP
     */
    private String peerInternalIp;
    /**
     * MCU级联外网IP
     */
    private String peerPublicIp;
    /**
     * MCU级联端口
     */
    private String peerPort;

    private String nodeUpHookPort;
    private String nodeSelfHookPort;
    private String nodeUpHookExt;
    private String nodeSelfHookExt;
    /**
     * hls上报地址，ip/domain:port
     */
    private String hlsReportHttpAddress;

    /**
     * hls上报加密地址，ip/domain:port
     */
    private String hlsReportHttpsAddress;

    private String srsHttpPort;

    /**
     * hls探测内网ip
     */
    private String hlsProbeInternalIp;

    private String hlsProbePublicIp;

    private String connectionNum;
    /**
     * MCU NAT功能开关
     */
    private String natForce;

    private String useTransportProxy;

    public boolean changeUseTransportProxy = false;

    @Override
    public HlsCM toModel(Map<String, String> cm, String nodeName) {
        String siteCodeKey = nodeName + DmcuConstants.SITECODE;
        String commentsKey = nodeName + DmcuConstants.COMMENTS;
        String maxRxKey = nodeName + DmcuConstants.MAXRXBW;
        String maxTxKey = nodeName + DmcuConstants.MAXTXBW;
        String upThriftKey = nodeName + DmcuConstants.UP_THRIFT_IP;
        String upThriftKey2 = nodeName + DmcuConstants.UP_THRIFT_IP_2;
        String selfThriftKey = nodeName + DmcuConstants.SELF_THRIFT_IP;
        String processTaskNumKey = nodeName + DmcuConstants.PROCESS_TASKNUM;
        String epnumPerTaskKey = nodeName + DmcuConstants.EPNUM_PERTASK;
        String dmcuClientPortKey = nodeName + DmcuConstants.DMCU_CLIENT_PORT;
        String dmcuTypeKey = nodeName + DmcuConstants.DMCU_TYPE;
        String nodeUpHookPortKey = nodeName + DmcuConstants.DMCU_SIDE_NODE_UP_HOOK_PORT;
        String nodeSelfHookPortKey = nodeName + DmcuConstants.DMCU_SIDE_NODE_SELF_HOOK_PORT;
        String nodeUpHookExtKey = nodeName + DmcuConstants.DMCU_SIDE_NODE_UP_HOOK_EXT;
        String nodeSelfHookExtKey = nodeName + DmcuConstants.DMCU_SIDE_NODE_SELF_HOOK_EXT;
        String hlsReportHttpAddressKey = nodeName + HlsConstants.HLS_REPORT_HTTP_ADDRESS;
        String hlsReportHttpsAddressKey = nodeName + HlsConstants.HLS_REPORT_HTTPS_ADDRESS;
        String srsHttpPortKey = nodeName + HlsConstants.SRS_HTTP_PORT;
        String hlsProbeInternalIpKey = nodeName + HlsConstants.HLS_PROBE_INTERNAL_IP;
        String hlsProbePublicIpKey = nodeName + HlsConstants.HLS_PROBE_PUBLIC_IP;
        String hlsConnectionNumKey = nodeName + HlsConstants.HLS_CONNECTION_NUM;

        String defaultSiteCodeKey = DmcuConstants.DEFAULT_PREFIX + DmcuConstants.SITECODE;
        String defaultMaxRxKey = DmcuConstants.DEFAULT_PREFIX + DmcuConstants.MAXRXBW;
        String defaultMaxTxKey = DmcuConstants.DEFAULT_PREFIX + DmcuConstants.MAXTXBW;
        String defaultTaskNumKey = DmcuConstants.DEFAULT_PREFIX + DmcuConstants.PROCESS_TASKNUM;
        String defaultPerTaskKey = DmcuConstants.DEFAULT_PREFIX + DmcuConstants.EPNUM_PERTASK;
        String defaultDmcuClientPortKey = DmcuConstants.DEFAULT_PREFIX + DmcuConstants.DMCU_CLIENT_PORT;

        this.nodeUpHookPort = cm.get(nodeUpHookPortKey);
        this.nodeSelfHookPort = cm.get(nodeSelfHookPortKey);
        this.nodeUpHookExt = cm.get(nodeUpHookExtKey);
        this.nodeSelfHookExt = cm.get(nodeSelfHookExtKey);

        String siteCode = cm.get(siteCodeKey);
        this.siteCode = StringUtils.isEmpty(siteCode) ? cm.get(defaultSiteCodeKey) : siteCode;

        String maxRx = cm.get(maxRxKey);
        String defaultMaxRx = StringUtils.isEmpty(this.maxRxBandWidth) ? cm.get(defaultMaxRxKey) : this.maxRxBandWidth;
        this.maxRxBandWidth = StringUtils.isEmpty(maxRx) ? defaultMaxRx : maxRx;
        String maxTx = cm.get(maxTxKey);
        String defaultMaxTx = StringUtils.isEmpty(this.maxTxBandWidth) ? cm.get(defaultMaxTxKey) : this.maxTxBandWidth;
        this.maxTxBandWidth = StringUtils.isEmpty(maxTx) ? defaultMaxTx : maxTx;
        String taskNum = cm.get(processTaskNumKey);
        String defaultTaskNum = StringUtils.isEmpty(this.mediaProcessTaskNum) ? cm.get(defaultTaskNumKey) : this.mediaProcessTaskNum;
        this.mediaProcessTaskNum = StringUtils.isEmpty(taskNum) ? defaultTaskNum : taskNum;

        String perTask = cm.get(epnumPerTaskKey);
        String defaultPerTask = StringUtils.isEmpty(this.mediaEndpointNumPerMediaTask) ? cm.get(defaultPerTaskKey) : this.mediaEndpointNumPerMediaTask;
        this.mediaEndpointNumPerMediaTask = StringUtils.isEmpty(perTask) ? defaultPerTask : perTask;

        String upThrift = cm.get(upThriftKey);
        this.upThriftIp = StringUtils.isBlank(upThrift) ? this.upThriftIp : upThrift;

        this.upThriftIp2 = cm.get(upThriftKey2);

        String selfThrift = cm.get(selfThriftKey);
        this.selfThriftIp = StringUtils.isBlank(selfThrift) ? this.selfThriftIp : selfThrift;

        String selfThrift2 = cm.get(nodeName + DmcuConstants.SELF_THRIFT_IP_2);
        this.selfThriftIp2 = StringUtils.isBlank(selfThrift2) ? this.selfThriftIp2 : selfThrift2;

        String thriftPort = cm.get(nodeName + DmcuConstants.SELF_THRIFT_PORT);
        this.selfThriftPort = StringUtils.isBlank(thriftPort) ? cm.get(DmcuConstants.DEFAULT_PREFIX + DmcuConstants.SELF_THRIFT_PORT) : thriftPort;

        String thriftPort2 = cm.get(nodeName + DmcuConstants.SELF_THRIFT_PORT_2);
        this.selfThriftPort2 = StringUtils.isBlank(thriftPort2) ? cm.get(DmcuConstants.DEFAULT_PREFIX + DmcuConstants.SELF_THRIFT_PORT_2) : thriftPort2;


        String clientPort = cm.get(dmcuClientPortKey);
        this.dmcuClientPort = StringUtils.isEmpty(clientPort) ? cm.get(defaultDmcuClientPortKey) : clientPort;

        this.dmcuComments = cm.get(commentsKey);

        String dmcuType = cm.get(dmcuTypeKey);
        this.dmcuType = StringUtils.isEmpty(dmcuType) ? DmcuConstants.DMCU_TYPE_DEFAULT : dmcuType;

        this.kafkaIp = cm.get(nodeName + DmcuConstants.KAFKA_IP);

        String qp = cm.get(nodeName + DmcuConstants.ENCODE_QP_ADJUST);
        this.encodeQPAdjust = StringUtils.isBlank(qp) ? DmcuConstants.ENCODE_QP_ADJUST_DEFAULT : qp;

        String cpu = cm.get(nodeName + DmcuConstants.TRANS_SCORE_CPU_COEFFICIENT);
        this.transScoreCpuCoefficient = StringUtils.isBlank(cpu) ? DmcuConstants.TRANS_SCORE_CPU_COEFFICIENT_DEFAULT : cpu;


        this.peerInternalIp = cm.get(nodeName + DmcuConstants.PEER_INTERNAL_IP);
        this.peerPublicIp = cm.get(nodeName + DmcuConstants.PEER_PUBLIC_IP);

        String peerPort = cm.get(nodeName + DmcuConstants.PEER_PORT);
        this.peerPort = StringUtils.isEmpty(peerPort) ? cm.get(defaultDmcuClientPortKey) : peerPort;

        String clientInPort = cm.get(nodeName + DmcuConstants.DMCU_CLIENT_IN_PORT);
        this.dmcuClientInPort = StringUtils.isEmpty(clientInPort) ? cm.get(defaultDmcuClientPortKey) : clientInPort;

        this.hlsReportHttpAddress = cm.get(hlsReportHttpAddressKey);
        this.hlsReportHttpsAddress = cm.get(hlsReportHttpsAddressKey);
        this.srsHttpPort = cm.get(srsHttpPortKey);
        this.hlsProbeInternalIp = cm.get(hlsProbeInternalIpKey);
        this.hlsProbePublicIp = cm.get(hlsProbePublicIpKey);
        this.connectionNum = cm.get(hlsConnectionNumKey);
        String natForceInCm = cm.get(nodeName + DmcuConstants.NAT_FORCE);
        this.natForce = StringUtils.isEmpty(natForceInCm) ? "false" : natForceInCm;

        String allDmcuUseTransportProxy = cm.get(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY);
        this.useTransportProxy = StringUtils.isBlank(allDmcuUseTransportProxy) ? "true" : allDmcuUseTransportProxy;
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {

        Map<String, String> cm = new HashMap<>();

        String siteCodeKey = this.nodeName + DmcuConstants.SITECODE;
        String commentsKey = this.nodeName + DmcuConstants.COMMENTS;
        String maxRxKey = this.nodeName + DmcuConstants.MAXRXBW;
        String maxTxKey = this.nodeName + DmcuConstants.MAXTXBW;
        String upThriftKey = this.nodeName + DmcuConstants.UP_THRIFT_IP;
        String upThriftKey2 = this.nodeName + DmcuConstants.UP_THRIFT_IP_2;
        String selfThrifyKey = this.nodeName + DmcuConstants.SELF_THRIFT_IP;
        String processTaskNumKey = this.nodeName + DmcuConstants.PROCESS_TASKNUM;
        String epnumPertaskKey = this.nodeName + DmcuConstants.EPNUM_PERTASK;
        String clientPortKey = this.nodeName + DmcuConstants.DMCU_CLIENT_PORT;
        String resourceTypeKey = this.nodeName + DmcuConstants.DMCU_RESOURCE_TYPE;
        String confTypeKey = this.nodeName + DmcuConstants.DMCU_CONF_TYPE;
        String dmcuTypeKey = this.nodeName + DmcuConstants.DMCU_TYPE;
        String nodeUpHookPortKey = this.nodeName + DmcuConstants.DMCU_SIDE_NODE_UP_HOOK_PORT;
        String nodeSelfHookPortKey = this.nodeName + DmcuConstants.DMCU_SIDE_NODE_SELF_HOOK_PORT;
        String nodeUpHookExtKey = this.nodeName + DmcuConstants.DMCU_SIDE_NODE_UP_HOOK_EXT;
        String nodeSelfHookExtKey = this.nodeName + DmcuConstants.DMCU_SIDE_NODE_SELF_HOOK_EXT;
        String hlsReportHttpAddressKey = this.nodeName + HlsConstants.HLS_REPORT_HTTP_ADDRESS;
        String hlsReportHttpsAddressKey = this.nodeName + HlsConstants.HLS_REPORT_HTTPS_ADDRESS;
        String srsHttpPortKey = this.nodeName + HlsConstants.SRS_HTTP_PORT;
        String hlsProbeInternalIpKey = this.nodeName + HlsConstants.HLS_PROBE_INTERNAL_IP;
        String hlsProbePublicIpKey = this.nodeName + HlsConstants.HLS_PROBE_PUBLIC_IP;
        String hlsConnectionNumKey = this.nodeName + HlsConstants.HLS_CONNECTION_NUM;

        if (StringUtils.isNotBlank(this.hlsReportHttpAddress)) {
            String[] split = this.hlsReportHttpAddress.split(":");
            if (split.length > 1) {
                this.srsHttpPort = split[1];
            }
        }

        cm.put(hlsConnectionNumKey, this.connectionNum);
        cm.put(hlsProbeInternalIpKey, this.hlsProbeInternalIp);
        cm.put(hlsProbePublicIpKey, this.hlsProbePublicIp);
        cm.put(srsHttpPortKey, this.srsHttpPort);
        cm.put(hlsReportHttpAddressKey, this.hlsReportHttpAddress);
        cm.put(hlsReportHttpsAddressKey, this.hlsReportHttpsAddress);
        cm.put(nodeUpHookPortKey, Strings.isBlank(this.nodeUpHookPort) ? Strings.EMPTY : this.nodeUpHookPort);
        cm.put(nodeSelfHookPortKey, Strings.isBlank(this.nodeSelfHookPort) ? Strings.EMPTY : this.nodeSelfHookPort);
        cm.put(nodeUpHookExtKey, Strings.isBlank(this.nodeUpHookExt) ? Strings.EMPTY : this.nodeUpHookExt);
        cm.put(nodeSelfHookExtKey, Strings.isBlank(this.nodeSelfHookExt) ? Strings.EMPTY : this.nodeSelfHookExt);
        cm.put(commentsKey, this.dmcuComments);
        cm.put(siteCodeKey, StringUtils.isNotEmpty(this.siteCode) ? this.siteCode : DmcuConstants.SITECODE_DEFAULT);
        cm.put(maxRxKey, StringUtils.isNotEmpty(this.maxRxBandWidth) ? this.maxRxBandWidth : DmcuConstants.MAXRXBW_DEFAULT);
        cm.put(maxTxKey, StringUtils.isNotEmpty(this.maxTxBandWidth) ? this.maxTxBandWidth : DmcuConstants.MAXTXBW_DEFAULT);
        cm.put(upThriftKey, StringUtils.isNotEmpty(this.upThriftIp) ? this.upThriftIp : DmcuConstants.IP_DEFAULT);
        cm.put(upThriftKey2, this.upThriftIp2);
        cm.put(selfThrifyKey, StringUtils.isNotEmpty(this.selfThriftIp) ? this.selfThriftIp : DmcuConstants.IP_DEFAULT);
        cm.put(this.nodeName + DmcuConstants.SELF_THRIFT_IP_2, StringUtils.isNotEmpty(this.selfThriftIp2) ? this.selfThriftIp2 : DmcuConstants.IP_DEFAULT);
        cm.put(this.nodeName + DmcuConstants.SELF_THRIFT_PORT, StringUtils.isNotEmpty(this.selfThriftPort) ? this.selfThriftPort : "9090");
        cm.put(this.nodeName + DmcuConstants.SELF_THRIFT_PORT_2, StringUtils.isNotEmpty(this.selfThriftPort2) ? this.selfThriftPort2 : "9091");

        cm.put(processTaskNumKey, StringUtils.isNotEmpty(this.mediaProcessTaskNum) ? this.mediaProcessTaskNum : DmcuConstants.PROCESS_TASKNUM_DEFAULT);
        cm.put(epnumPertaskKey, StringUtils.isNotEmpty(this.mediaEndpointNumPerMediaTask) ? this.mediaEndpointNumPerMediaTask : DmcuConstants.EPNUM_PERTASK_DEFAULT);
        cm.put(clientPortKey, StringUtils.isNotEmpty(this.dmcuClientPort) ? this.dmcuClientPort : DmcuConstants.DMCU_CLIENT_PORT_DEFAULT);
        cm.put(nodeName + DmcuConstants.KAFKA_IP, IpUtils.getKafkaIp(this.kafkaIp));
        cm.put(nodeName + DmcuConstants.ENCODE_QP_ADJUST, StringUtils.isBlank(this.encodeQPAdjust) ? DmcuConstants.ENCODE_QP_ADJUST_DEFAULT : this.encodeQPAdjust);
        cm.put(nodeName + DmcuConstants.TRANS_SCORE_CPU_COEFFICIENT, StringUtils.isBlank(this.transScoreCpuCoefficient) ? DmcuConstants.TRANS_SCORE_CPU_COEFFICIENT_DEFAULT : this.transScoreCpuCoefficient);

        cm.put(nodeName + DmcuConstants.PEER_INTERNAL_IP, this.peerInternalIp);
        cm.put(nodeName + DmcuConstants.PEER_PUBLIC_IP, this.peerPublicIp);
        cm.put(nodeName + DmcuConstants.PEER_PORT, StringUtils.isNotEmpty(this.peerPort) ? this.peerPort : DmcuConstants.DMCU_CLIENT_PORT_DEFAULT);
        cm.put(nodeName + DmcuConstants.DMCU_CLIENT_IN_PORT, StringUtils.isNotEmpty(this.dmcuClientInPort) ? this.dmcuClientInPort : DmcuConstants.DMCU_CLIENT_PORT_DEFAULT);

        cm.put(dmcuTypeKey, StringUtils.isNotEmpty(this.dmcuType) ? this.dmcuType : DmcuConstants.DMCU_TYPE_DEFAULT);
        if (DmcuConstants.DMCU_TYPE_TRANSCODING.equalsIgnoreCase(this.dmcuType)) {
            cm.put(resourceTypeKey, DmcuConstants.DMCU_TRANSCODING_RESOURCE_TYPE_DEFAULT);
            cm.put(confTypeKey, DmcuConstants.DMCU_TRANSCODING_CONF_TYPE_DEFAULT);
        } else if (DmcuConstants.DMCU_TYPE_ALL_TRANSCODING.equalsIgnoreCase(this.dmcuType)) {
            cm.put(resourceTypeKey, DmcuConstants.DMCU_ALL_TRANSCODING_RESOURCE_TYPE_DEFAULT);
            cm.put(confTypeKey, DmcuConstants.DMCU_ALL_TRANSCODING_CONF_TYPE_DEFAULT);
        } else if (DmcuConstants.DMCU_TYPE_GATHER.equalsIgnoreCase(this.dmcuType)) {
            cm.put(resourceTypeKey, DmcuConstants.DMCU_GATHER_RESOURCE_TYPE_DEFAULT);
            cm.put(confTypeKey, DmcuConstants.DMCU_GATHER_CONF_TYPE_DEFAULT);
        } else if (DmcuConstants.DMCU_TYPE_SIMULTANEOUS.equalsIgnoreCase(this.dmcuType)) {
            cm.put(resourceTypeKey, DmcuConstants.DMCU_SIMULTANEOUS_RESOURCE_TYPE_DEFAULT);
            cm.put(confTypeKey, DmcuConstants.DMCU_SIMULTANEOUS_CONF_TYPE_DEFAULT);
        } else {
            cm.put(resourceTypeKey, DmcuConstants.DMCU_RESOURCE_TYPE_DEFAULT);
            cm.put(confTypeKey, DmcuConstants.DMCU_CONF_TYPE_DEFAULT);
        }

        cm.put(this.nodeName + DmcuConstants.NAT_FORCE, StringUtils.isNotEmpty(this.natForce) ? this.natForce : "false");
        cm.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.useTransportProxy);


        return cm;
    }


    @Override
    public HlsCM setDefault(String nodeName) {
        this.selfThriftIp = getDeployService().getNodeByName(nodeName).getIp();
        this.selfThriftIp2 = this.selfThriftIp;

        this.upThriftIp = new McUtils().getGatherMcIp();
        this.nodeName = nodeName;
        // 新增默认值逻辑 需要根据节点资源配置来设置 maxRxBandWidth maxTxBandWidth mediaProcessTaskNum mediaEndpointNumPerMediaTask instanceNum
        CalculateMediaParametersService.MediaParameters mediaParameters = calculateMediaParameters(nodeName, "hls");
        this.maxRxBandWidth = mediaParameters.getMaxRxBandWidth();
        this.maxTxBandWidth = mediaParameters.getMaxTxBandWidth();
        this.mediaProcessTaskNum = mediaParameters.getMediaProcessTaskNum();
        this.mediaEndpointNumPerMediaTask = mediaParameters.getMediaEndpointNumPerMediaTask();

        return this;
    }


    @Override
    public void beforeSave() {
        ConfigMap configMap = getDeployService().getConfigMapByName(Constants.CONFIGMAP_HLS, Constants.NAMESPACE_DEFAULT);
        if (Objects.isNull(configMap)) {
            return;
        }
        Map<String, String> map = configMap.getData();
        String allDmcuUseTransportProxy = map.get(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY);
        if (StringUtils.isNotBlank(this.useTransportProxy) && !this.useTransportProxy.equalsIgnoreCase(allDmcuUseTransportProxy)) {
            changeUseTransportProxy = true;
        }
    }

    @Override
    public void afterSave() {
        if (StringUtils.isNotBlank(this.useTransportProxy)) {
            try {
                getDeployService().patchConfigMapAllIp(d->{
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.useTransportProxy);
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }

            try {
                getDeployService().patchConfigMap(Constants.CONFIGMAP_DMCU, Constants.NAMESPACE_DEFAULT, d -> {
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.useTransportProxy);
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }

            try {
                getDeployService().patchConfigMap(Constants.CONFIGMAP_ALL_IVR, Constants.NAMESPACE_DEFAULT, d -> {
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.useTransportProxy);
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }

            try {
                getDeployService().patchConfigMap(Constants.CONFIGMAP_ALL_IVR, Constants.NAMESPACE_DEFAULT, d -> {
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.useTransportProxy);
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }

            try {
                getDeployService().patchConfigMap(Constants.CONFIGMAP_ALL_CONVERGED_MEDIAGW, Constants.NAMESPACE_DEFAULT, d -> {
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.useTransportProxy);
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }

            try {
                getDeployService().patchConfigMap(Constants.CONFIGMAP_MA, Constants.NAMESPACE_DEFAULT, d -> {
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.useTransportProxy);
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }

            try {
                getDeployService().patchConfigMap(Constants.CONFIGMAP_WEBRTC_MEDIAGW, Constants.NAMESPACE_DEFAULT, d -> {
                    d.put(DmcuConstants.ALL_DMCU_USE_TRANSPORT_PROXY, this.useTransportProxy);
                });
            } catch (Exception e) {
                log.error("After update configmap error.", e);
            }
        }
    }
}
