package com.xylink.manager.model.cm;

import com.xylink.config.ProxyConstants;
import com.xylink.util.NginxValidUtils;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 2023/11/24/17:21
 * @Description:
 */
@Setter
@Getter
public class OpenrestyVodCM implements ICMDto<OpenrestyVodCM> {

    private List<String> accessControlAllowOrigin;
    private String nodeName;

    @Override
    public OpenrestyVodCM toModel(Map<String, String> cm, String nodeName) {
        String accessControlAllowOriginKey = nodeName + ProxyConstants.ACCESS_CONTROL_ALLOW_ORIGIN;
        String accessControlAllowOriginValue = cm.get(accessControlAllowOriginKey);
        this.accessControlAllowOrigin = NginxValidUtils.allowedOriginToArrary(accessControlAllowOriginValue);
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();

        cm.put(nodeName + ProxyConstants.ACCESS_CONTROL_ALLOW_ORIGIN,NginxValidUtils.allowedOriginToStr(this.accessControlAllowOrigin));

        return cm;
    }

    @Override
    public void beforeSave() {
        // Allow Origin
        NginxValidUtils.checkAllowedOrigin(accessControlAllowOrigin);
    }
}
