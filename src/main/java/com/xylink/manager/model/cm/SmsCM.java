package com.xylink.manager.model.cm;

import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
public class SmsCM implements ICMDto<SmsCM> {
    /**
     * 统一通信组件接入编号
     */
    private String ucCmptId;
    /**
     * 短信类型编号，由电子银行部分配
     */
    private String ucTpId;
    /**
     * 短信所属机构
     */
    private String ucSndr;

    @Override
    public SmsCM toModel(Map<String, String> cm, String nodeName) {
        this.ucCmptId = cm.get(SmsCMKey.KEY_UC_CMPT_ID);
        this.ucTpId = cm.get(SmsCMKey.KEY_UC_IP_ID);
        this.ucSndr = cm.get(SmsCMKey.KEY_UC_SNDR);
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        cm.put(SmsCMKey.KEY_UC_CMPT_ID, ucCmptId);
        cm.put(SmsCMKey.KEY_UC_IP_ID, ucTpId);
        cm.put(SmsCMKey.KEY_UC_SNDR, ucSndr);
        return cm;
    }

    private interface SmsCMKey {
        String KEY_UC_CMPT_ID = "UC_CMPT_ID";
        String KEY_UC_IP_ID = "UC_IP_ID";
        String KEY_UC_SNDR = "UC_SNDR";
    }
}
