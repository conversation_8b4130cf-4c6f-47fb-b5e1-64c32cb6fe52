package com.xylink.manager.model.cm;

import com.fasterxml.jackson.databind.SerializationFeature;
import com.xylink.config.Constants;
import com.xylink.config.RecordConstants;
import com.xylink.config.mapper.JsonMapper;
import com.xylink.util.K8sUtils;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @since 2024-07-08 21:49
 */
public class McAccessRecordCMUtils {

    private static final String KEY = "record.json";

    private static final AtomicInteger COUNT = new AtomicInteger(1);


    public static void recordToMcAccessConfigMap(Map<String, String> recordMap) {
        Map<String, String> configmap = K8sUtils.getConfigMap(Constants.CONFIGMAP_MCACCESS_CONFIG);
        configmap.put(KEY, convertToMcAccessRecordJson(recordMap));
        K8sUtils.createOrReplaceConfigMap(Constants.CONFIGMAP_MCACCESS_CONFIG, configmap);
    }

    private static String convertToMcAccessRecordJson(Map<String, String> recordMap) {

        if (recordMap == null || recordMap.isEmpty()) {
            return "{\"version\":1}";
        }
        Map<String, String> data = new LinkedHashMap<>();
        data.put("version", COUNT.incrementAndGet() + "");
        data.put("vodUploadSite", recordMap.get(RecordConstants.UploadSiteKey));
        data.put("vodCdnDomainLocalName", recordMap.get(RecordConstants.VodCdnLocalDomainKey));
        data.put("vodCdnDomainOssName", recordMap.get(RecordConstants.VodCdnOssDomainKey));

        data.put("vodOssBase", recordMap.get(RecordConstants.UploadBaseKey));
        data.put("vodOssBuck", recordMap.get(RecordConstants.UploadBuckKey));
        data.put("vodOssEndpoint", recordMap.get(RecordConstants.UploadEndpointKey));
        data.put("vodOssId", recordMap.get(RecordConstants.UploadIdKey));
        data.put("vodOssKeypass", recordMap.get(RecordConstants.UploadKeypassKey));
        data.put("bucketRegion", recordMap.get(RecordConstants.BUCKET_REGION));

        data.put("vodOssShareBase", recordMap.get(RecordConstants.ShareBaseKey));
        data.put("vodOssShareBuck", recordMap.get(RecordConstants.ShareBuckKey));
        data.put("vodOssShareEndpoint", recordMap.get(RecordConstants.ShareEndpointKey));
        data.put("vodOssShareId", recordMap.get(RecordConstants.ShareIdKey));
        data.put("vodOssShareKeypass", recordMap.get(RecordConstants.ShareKeypassKey));
        data.put("vodDownloadCdnPrivateKey", recordMap.get(RecordConstants.CdnPrivateKeyKey));
        data.put("vodOssEndpointPort", recordMap.get(RecordConstants.VOD_OSS_ENDPOINT_PORT));
        data.put("vodOssEndpointHost", recordMap.get(RecordConstants.VOD_OSS_ENDPOINT_HOST));

        JsonMapper jsonMapper = new JsonMapper();
        jsonMapper.getMapper().enable(SerializationFeature.INDENT_OUTPUT);
        return jsonMapper.toJson(data);
    }
}
