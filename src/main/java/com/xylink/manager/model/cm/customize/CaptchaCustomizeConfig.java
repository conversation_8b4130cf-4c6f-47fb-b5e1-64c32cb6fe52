package com.xylink.manager.model.cm.customize;

import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.remote.captcha.CaptchaRemoteClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025-02-12 15:26
 */
@Component
@Slf4j
public class CaptchaCustomizeConfig implements ICustomizeConfigStrategy {
    @Resource
    private CaptchaRemoteClient captchaRemoteClient;

    @Override
    public String getLabelName() {
        return Labels.captcha.label();
    }

    @Override
    public Object getCustomizeConfig(String nodeName) {
        Map<String, String> resultMap = new HashMap<>();
        boolean status = captchaRemoteClient.queryUnifiedEncryptionSwitch();
        resultMap.put("unifiedEncryptionSwitch", String.valueOf(status));
        return resultMap;
    }

    @Override
    public void saveCustomizeConfig(LinkedHashMap data) {
        boolean status = Boolean.parseBoolean(data.get("unifiedEncryptionSwitch").toString());
        captchaRemoteClient.updateUnifiedEncryptionSwitch(status);
    }
}
