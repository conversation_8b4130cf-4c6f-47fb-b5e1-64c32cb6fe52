package com.xylink.manager.model.cm.customize;

import com.xylink.config.Constants;
import com.xylink.manager.model.cm.ICMDto;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/06/01/16:32
 */
@Getter
@Setter
public class VodFileManagerCM implements ICMDto<VodFileManagerCM> {

    private String nodeName;
    private String vodThirdTokenAuthUrl;

    @Override
    public VodFileManagerCM toModel(Map<String, String> cm, String nodeName) {
        this.nodeName = nodeName;
        this.vodThirdTokenAuthUrl = cm.get(nodeName + Constants.VOD_THIRD_TOKEN_AUTH_URL_KEY);
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> map = new HashMap<>(2);
        map.put(nodeName + Constants.VOD_THIRD_TOKEN_AUTH_URL_KEY, this.vodThirdTokenAuthUrl);
        return map;
    }

}
