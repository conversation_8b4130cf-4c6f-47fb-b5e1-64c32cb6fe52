package com.xylink.manager.model.cm;

import com.xylink.config.ProxyConstants;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Setter
@Getter
public class NmsaProxyCM implements ICMDto<NmsaProxyCM> {

    private String nginxSSLPort;
    private String nodeName;


    @Override
    public NmsaProxyCM toModel(Map<String, String> cm, String nodeName) {
        this.nginxSSLPort = cm.get(nodeName + ProxyConstants.NGINX_SSL_PORT);
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        cm.put(nodeName + ProxyConstants.NGINX_SSL_PORT, this.nginxSSLPort);
        return cm;
    }

}
