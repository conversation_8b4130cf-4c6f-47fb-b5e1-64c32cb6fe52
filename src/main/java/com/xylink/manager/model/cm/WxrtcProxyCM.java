package com.xylink.manager.model.cm;

/**
 * <AUTHOR>
 * @Date: 2023/01/04/10:52
 */
import com.xylink.config.ProxyConstants;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Setter
@Getter
public class WxrtcProxyCM implements ICMDto<WxrtcProxyCM> {

    private String nginxSSLPort;
    private String nodeName;


    @Override
    public WxrtcProxyCM toModel(Map<String, String> cm, String nodeName) {
        this.nginxSSLPort = cm.get(nodeName + ProxyConstants.NGINX_SSL_PORT);
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        cm.put(nodeName + ProxyConstants.NGINX_SSL_PORT, this.nginxSSLPort);
        return cm;
    }

}
