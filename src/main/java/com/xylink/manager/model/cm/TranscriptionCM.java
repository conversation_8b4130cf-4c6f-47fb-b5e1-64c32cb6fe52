package com.xylink.manager.model.cm;

import com.xylink.config.TranscriptionConstants;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/8/4 3:13 下午
 */
@Data
public class TranscriptionCM implements ICMDto<TranscriptionCM> {

    /**
     * 引擎类型
     */
    private String vendor;
    /**
     * provider address
     */
    private String engineServiceUrl;
    /**
     * appId
     */
    private String engineAppId;
    /**
     * accessKeyId
     */
    private String engineAccessKeyId;
    /**
     * accessKeySecret
     */
    private String engineAccessKeySecret;
    /**
     * 转写指定行业、领域
     */
    private String engineAbilityId;
    /**
     * 转写结果获取url
     */
    private String engineMixServiceUrl;
    /**
     * local
     */
    private String engineLocal;

    @Override
    public TranscriptionCM toModel(Map<String, String> cm, String nodeName) {
        this.engineAbilityId = cm.get(TranscriptionConstants.engineAbilityId);
        this.engineAccessKeyId = cm.get(TranscriptionConstants.engineAccessKeyId);
        this.engineAccessKeySecret = cm.get(TranscriptionConstants.engineAccessKeySecret);
        this.engineAppId = cm.get(TranscriptionConstants.engineAppId);
        this.engineServiceUrl = cm.get(TranscriptionConstants.engineServiceUrl);
        this.vendor = cm.get(TranscriptionConstants.vendor);
        this.engineMixServiceUrl = cm.get(TranscriptionConstants.engineMixServiceUrl);
        this.engineLocal = cm.get(TranscriptionConstants.engineLocal);
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>(16);
        cm.put(TranscriptionConstants.engineAbilityId, this.engineAbilityId);
        cm.put(TranscriptionConstants.engineAccessKeyId, this.engineAccessKeyId);
        cm.put(TranscriptionConstants.engineAccessKeySecret, this.engineAccessKeySecret);
        cm.put(TranscriptionConstants.engineAppId, this.engineAppId);
        cm.put(TranscriptionConstants.engineServiceUrl, this.engineServiceUrl);
        /**
         * local为false时，需要给vendor默认值
         */
        cm.put(TranscriptionConstants.vendor, StringUtils.isBlank(this.vendor) ? "tengxun" : this.vendor);
        cm.put(TranscriptionConstants.engineMixServiceUrl, this.engineMixServiceUrl);
        cm.put(TranscriptionConstants.engineLocal, this.engineLocal);
        return cm;
    }
}
