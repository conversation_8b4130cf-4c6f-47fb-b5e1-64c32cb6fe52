package com.xylink.manager.model.cm;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
public class SipplusmgrCM implements ICMDto<SipplusmgrCM>{

    //SN
    private String sn;

    private String nodeName;



    @Getter(AccessLevel.PRIVATE)
    final private String SN_KEY = "-SIPPLUSMGR-SN";

    @Override
    public SipplusmgrCM toModel(Map<String, String> cm, String nodeName) {
        this.nodeName = nodeName;
        this.sn = cm.get(nodeName + SN_KEY);
        return this;
    }

    @Override
    public Map<String, String> toConfigmap() {
        Map<String, String> cm = new HashMap<>();
        cm.put(nodeName +SN_KEY, this.sn);
        return cm;
    }
}
