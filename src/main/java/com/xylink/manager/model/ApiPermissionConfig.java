package com.xylink.manager.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2024/02/26/14:38
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiPermissionConfig {
    private List<ApiPermissionRequestDto> records;
    private String total;
    private String size;
    private String current;
    private List<String> orders;
    private Boolean optimizeCountSql;
    private Boolean searchCount;
    private Integer maxLimit;
    private String countId;
    private Integer page;
    private Integer limit;
    private String sort;
    private String order;
    private ApiPermissionRequestMap requestMap;
    private String orderBy;
    private String pages;
    private String whiteApiMode;


}
