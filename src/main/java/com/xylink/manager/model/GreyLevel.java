package com.xylink.manager.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by cong yajing on 2018/10/21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GreyLevel implements Serializable {
    private String id;
    private Integer level;
    private String platform;
    private String version;
    private String remark;
    private Date updateTime;
    private String customizedkey;
    private String effectiveStartTime;
    private boolean customized;
    private boolean forceUpdateFlag;//是否强制升级
    private String forceUpdateId;//是否强制升级

}
