package com.xylink.manager.model.strategy;

import com.google.common.collect.ImmutableMap;
import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.service.base.IDeployService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Component
public class KafkaClusterStrategy extends CMRefreshStrategy {
    private final String appLabel = "private-kafka-cluster";
    private final int port = 9093;

    @Autowired
    private IDeployService deployService;
    @Autowired
    private CMRefreshQueueService cmRefreshQueueService;

    @Override
    public String getAppLabel() {
        return appLabel;
    }

    @Override
    public boolean refreshIps() {
        log.info("{} refreshIps invoke .", this.getClass().getSimpleName());
        try {
            List<com.xylink.manager.model.deploy.Pod> pods = deployService.listPodsByAppLabel(appLabel);
            if (CollectionUtils.isEmpty(pods)) {
                return true;
            }
            ConfigMap allIp = deployService.getConfigMapAllIp();
            Map<String, String> allIpMap = allIp.getData();
            String masterKafkaInternalIp = allIpMap.get(NetworkConstants.MASTER_KAFKA_INTERNAL_IP);
            if (isHostConnectable(masterKafkaInternalIp, port)) {
                return true;
            }

            Optional<com.xylink.manager.model.deploy.Pod> optionalPod = pods.stream()
                    .filter(pod -> isHostConnectable(pod.getHostIp(), port))
                    .findFirst();
            if (optionalPod.isPresent()) {
                deployService.patchConfigMap(Constants.CONFIGMAP_ALLIP, Constants.NAMESPACE_DEFAULT,d->{
                    d.putAll(ImmutableMap.of(NetworkConstants.MASTER_KAFKA_INTERNAL_IP, optionalPod.get().getHostIp()));
                });
                return true;
            } else {
                if (!Thread.currentThread().getName().matches("CM_Refresh_Pool-\\d") && !cmRefreshQueueService.cmRefreshQueue.contains(this)) {
                    log.info("Add to Retry-QUEUE. {}", this);
                    cmRefreshQueueService.cmRefreshQueue.add(this);
                }
                return false;
            }
        } catch (Exception e) {
            log.error("{} refreshIps error .", this.getClass().getSimpleName(), e);
            return false;
        }
    }
}
