package com.xylink.manager.model.strategy;

import com.google.common.collect.ImmutableMap;
import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.service.base.IDeployService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
public class ZookeeperClusterStrategy extends CMRefreshStrategy {
    private final String appLabel = "private-zookeeper-cluster";
    private final Pattern pattern = Pattern.compile("Mode: leader");
    private final int port = 2181;

    @Autowired
    private IDeployService deployService;
    @Autowired
    private CMRefreshQueueService cmRefreshQueueService;

    @Override
    public String getAppLabel() {
        return appLabel;
    }

    @Override
    public boolean refreshIps() {
        log.info("{} refreshIps invoke .", this.getClass().getSimpleName());
        try {
            List<Pod> pods = deployService.listPodsByAppLabel(appLabel);
            if (CollectionUtils.isEmpty(pods)) {
                return true;
            }
            ConfigMap allIp = deployService.getConfigMapAllIp();
            Map<String, String> allIpMap = allIp.getData();
            String masterZookeeperIp = allIpMap.get(NetworkConstants.MASTER_ZOOKEEPER_IP);
            if (isHostConnectable(masterZookeeperIp, port) && isZookeeperLeader(masterZookeeperIp)) {
                return true;
            }

            Optional<Pod> optionalPod = pods.stream()
                    .filter(pod -> isHostConnectable(pod.getHostIp(), port) && isZookeeperLeader(pod.getHostIp()))
                    .findFirst();
            if (optionalPod.isPresent()) {
                deployService.patchConfigMap(Constants.CONFIGMAP_ALLIP, Constants.NAMESPACE_DEFAULT,d->{
                    d.putAll(ImmutableMap.of(NetworkConstants.MASTER_ZOOKEEPER_IP, optionalPod.get().getHostIp()));
                });
                return true;
            } else {
                if (!Thread.currentThread().getName().matches("CM_Refresh_Pool-\\d") && !cmRefreshQueueService.cmRefreshQueue.contains(this)) {
                    log.info("Add to Retry-QUEUE. {}", this);
                    cmRefreshQueueService.cmRefreshQueue.add(this);
                }
                return false;
            }
        } catch (Exception e) {
            log.error("{} refreshIps error .", this.getClass().getSimpleName(), e);
            return false;
        }
    }

    private boolean isZookeeperLeader(String hostIp) {
        boolean isZookeeperLeader = false;
        Process process = null;
        InputStream in = null;
        BufferedReader bufferedReader = null;
        try {
            Runtime run = Runtime.getRuntime();
            process = run.exec(new String[]{"/bin/sh","-c", "echo stat | nc "+hostIp+" 2181"});
            in = process.getInputStream();
            bufferedReader = new BufferedReader(new InputStreamReader(in));
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                Matcher matcher = pattern.matcher(line);
                if (matcher.find()) {
                    isZookeeperLeader = true;
                    log.info("Zookeeper[{}] is leader. ", hostIp);
                    break;
                }
            }
        } catch (Exception e) {
            log.warn("Check zookeeper leader error. hostIp = {}", hostIp);
        } finally {
            if (process != null) {
                process.destroy();
            }
            if (in != null) {
                try {
                    in.close();
                } catch (IOException ignore) {
                }
            }
            if (bufferedReader != null) {
                try {
                    bufferedReader.close();
                } catch (IOException ignore) {
                }
            }
        }
        return isZookeeperLeader;
    }



}
