package com.xylink.manager.model.strategy;

import lombok.extern.slf4j.Slf4j;

import java.net.InetSocketAddress;
import java.net.Socket;

@Slf4j
public abstract class CMRefreshStrategy {

    String appLabel = "";

    /**
     * 重试计数
     */
    protected int countRetry = 0;

    /**
     * 最大重试次数
     */
    protected int maxRetry = 1;

    /**
     * 重试时间间隔(默认间隔5分钟重试)
     */
    protected long intervalTimestamp = 5 * 60 * 1000;

    /**
     * 上次完成执行时间点
     */
    protected long lastInvokedTimestamp = 0L;

    public String getAppLabel() {
        return this.appLabel;
    }

    protected void setCountRetry(int countRetry) {
        this.countRetry = countRetry;
    }

    protected int getCountRetry() {
        return this.countRetry;
    }

    protected int getMaxRetry() {
        return maxRetry;
    }

    protected long getIntervalTimestamp() {
        return intervalTimestamp;
    }

    protected void setLastInvokedTimestamp(long lastInvokedTimestamp) {
        this.lastInvokedTimestamp = lastInvokedTimestamp;
    }

    protected long getLastInvokedTimestamp() {
        return lastInvokedTimestamp;
    }


    /**
     * 注意：该方法务必捕获自身异常，不得抛出，否则会造成队列消费异常
     * @return
     */
    public abstract boolean refreshIps();

    protected boolean isHostConnectable(String host, int port) {
        boolean isHostConnectable = false;
        Socket socket = new Socket();
        try {
            socket.connect(new InetSocketAddress(host, port));
            isHostConnectable = true;
        } catch (Exception e) {
            isHostConnectable = false;
        } finally {
            try {
                socket.close();
            } catch (Exception ignored) {
            }
        }
        log.info("Socket {}:{} {}", host, port, isHostConnectable);
        return isHostConnectable;
    }

    protected void resetAll() {
        this.countRetry = 0;
        this.maxRetry = 1;
        this.intervalTimestamp = 1000;
        this.lastInvokedTimestamp = 0;
    }
}
