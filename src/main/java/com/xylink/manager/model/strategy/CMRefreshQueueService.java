package com.xylink.manager.model.strategy;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.Instant;
import java.util.concurrent.*;

@Slf4j
@Service
public class CMRefreshQueueService {

    public BlockingQueue<CMRefreshStrategy> cmRefreshQueue = new LinkedBlockingQueue<>(10);

    @PostConstruct
    public void run() {
        Executors.newSingleThreadExecutor(new ThreadFactoryBuilder().setNameFormat("CM_Refresh_Pool-%d").setDaemon(true).build()).submit(() -> {
            while (true) {
                CMRefreshStrategy cmRefreshStrategy = cmRefreshQueue.take();
                /**
                 * 可执行条件：
                 * 1，上次执行时间为0，代表任务刚添加进来，可以立刻执行
                 * 2，距离上次执行时间已过去 intervalTimestamp ，可以立刻执行
                 * 3，未超过最大重试次数，可以执行
                 */
                boolean invokeResult = false;
                if (
                        (cmRefreshStrategy.getLastInvokedTimestamp() == 0 || (cmRefreshStrategy.getLastInvokedTimestamp() + cmRefreshStrategy.getIntervalTimestamp()) <= Instant.now().toEpochMilli())
                                && (cmRefreshStrategy.getCountRetry() <= cmRefreshStrategy.getMaxRetry())
                ) {
                    invokeResult = cmRefreshStrategy.refreshIps();
                    if (!invokeResult) {
                        cmRefreshStrategy.setCountRetry(cmRefreshStrategy.getCountRetry() + 1);     //累计重试次数
                        log.info("ClassName[{}]-CMRefresh-retry {}", cmRefreshStrategy.getClass().getSimpleName(), cmRefreshStrategy.getCountRetry());
                        cmRefreshStrategy.setLastInvokedTimestamp(Instant.now().toEpochMilli());    //更新上次执行时间
                    }
                }

                //如果执行失败 并且 未超过最大重试次数，则放回队列等待下次重试
                if (!invokeResult && cmRefreshStrategy.getCountRetry() < cmRefreshStrategy.getMaxRetry()) {
                    cmRefreshQueue.add(cmRefreshStrategy);
                } else {
                    cmRefreshStrategy.resetAll();
                }
            }
        });
    }
}

