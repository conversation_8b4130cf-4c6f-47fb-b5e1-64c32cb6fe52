package com.xylink.manager.model.taskcenter;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> create on 2023/12/11
 */
@Data
public class DeviceScheduleTaskParam {

    /**
     * 主键ID
     */
    private String id;
    /**
     * 任务类型
     *
     * @see com.xylink.manager.model.em.taskcenter.DeviceScheduleTaskTypeEnum
     */
    @NotBlank(message = "taskType can not be null")
    private String taskType;

    /**
     * 生效范围
     *
     * @see com.xylink.manager.model.em.taskcenter.DeviceScheduleTaskScopeEnum
     */
    @NotBlank(message = "taskScope can not be null")
    private String taskScope;

    /**
     * 重复类型
     *
     * @see com.xylink.manager.model.em.taskcenter.DeviceScheduleTaskRepeatEnum
     */
    @NotBlank(message = "repeatType can not be null")
    private String repeatType;

    /**
     * 执行时间（精确到分钟），单次-精确时间、每日、每周、每月-取时间
     */
    private String executeTime;

    /**
     * 每周的第x天
     */
    private Integer dayOfWeek;

    /**
     * 每月的第x天
     */
    private Integer dayOfMonth;

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 结束时间
     */
    private String endTime;


    /**
     * 执行类型
     *
     * @see com.xylink.manager.model.em.taskcenter.DeviceScheduleTaskExecuteEnum
     */
    @NotBlank(message = "executeType can not be null")
    private String executeType;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 企业ID
     */
    private String enterpriseId="default_enterprise";

    /**
     * 终端类型
     *
     * @see com.xylink.manager.model.em.taskcenter.DeviceScheduleTypeEnum
     */
    @NotBlank(message = "deviceType can not be null")
    private String deviceType;

    /**
     * 任务状态
     *
     * @see com.xylink.manager.model.em.taskcenter.DeviceScheduleTaskStateEnum
     */
    private String taskState;

    /**
     * 操作人账号
     */
    private String operateUsername;
    /**
     * 操作人名称
     */
    private String operateDisplayName;

    /**
     * 终端信息
     */
    private DeviceInfo[] devices;

    /**
     * 终端型号
     */
    private DeviceCategoryInfo[] deviceCategories;

    @Data
    public static class DeviceInfo {
        /**
         * 设备ID
         */
        private String deviceId;
        /**
         * 终端显示名称
         */
        private String displayName;

        /**
         * 终端callUri
         */
        private String callUri;
        /**
         * 终端号
         */
        private String number;

        /**
         * sn
         */
        private String deviceSn;

        /**
         * 设备类型
         */
        private String deviceType;

        /**
         * 型号
         */
        private String deviceCategory;
        /**
         * 型号
         */
        private String deviceSubType;
    }

    @Data
    public static class DeviceCategoryInfo{
        private String deviceSubType;
        private String deviceCategory;
    }

    @Data
    public static class SelectTarget {
        /**
         * 终端ID
         */
        private DeviceInfo[] devices;

        /**
         * 终端型号
         */
        private DeviceCategoryInfo[] deviceCategories;
    }


}
