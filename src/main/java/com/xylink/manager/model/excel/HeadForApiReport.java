package com.xylink.manager.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;

/**
 * <AUTHOR>
 * @date 2025/5/8
 */
public class HeadForApiReport {
    @ExcelProperty(value = "请求路径", index = 0)
    @ColumnWidth(80)
    private String requestPath;
    @ExcelProperty(value = "请求方法", index = 1)
    @ColumnWidth(25)
    private String requestMethod;
    @ExcelProperty(value = "请求次数", index = 2)
    @ColumnWidth(25)
    private String requestCount;
    @ExcelProperty(value = "最近请求日期", index = 3)
    @ColumnWidth(30)
    private String lastRequestDate;
    @ExcelProperty(value = "模块", index = 4)
    @ColumnWidth(30)
    private String serviceName;
    @ExcelProperty(value = "禁用状态", index = 5)
    @ColumnWidth(25)
    private String status;
}
