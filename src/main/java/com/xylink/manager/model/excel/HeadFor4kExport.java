package com.xylink.manager.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;

/**
 * <AUTHOR>
 * @date 2025/5/8
 */
public class HeadFor4kExport {
    @ExcelProperty(value = "终端SN", index = 0)
    @ColumnWidth(30)
    private String deviceSn;
    @ExcelProperty(value = "终端型号", index = 1)
    @ColumnWidth(25)
    private String deviceCategory;
    @ExcelProperty(value = "终端名称", index = 2)
    @ColumnWidth(30)
    private String displayName;
    @ExcelProperty(value = "添加日期", index = 3)
    @ColumnWidth(25)
    private String createTime;

}
