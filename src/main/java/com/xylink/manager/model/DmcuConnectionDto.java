package com.xylink.manager.model;

import com.xylink.manager.controller.dto.ISearchDto;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/7/8 5:52 下午
 */
@Data
public class DmcuConnectionDto implements Serializable, ISearchDto {
    /**
     * 目标siteCode
     */
    private SiteCodeDto targetSiteCode;
    /**
     * 级联dmcu
     */
    private List<SiteCodeDto> connSiteCode;

    public Optional<String> propertiesStyle() {
        if (targetSiteCode == null) {
            return Optional.empty();
        }
        return Optional.of(targetSiteCode.getSiteCode() + "=" + connSiteCode.stream().map(SiteCodeDto::siteCodeAndLevel).collect(Collectors.joining(",")));
    }

    @Override
    public boolean containSearchContent(String key) {
        StringBuilder builder = new StringBuilder();
        builder.append(this.targetSiteCode.getSiteCode()).append(":");
        if (!CollectionUtils.isEmpty(this.targetSiteCode.getDmcuInfos())) {
            for (DmcuInfoDto infoDto : this.targetSiteCode.getDmcuInfos()) {
                builder.append(infoDto.getInternalIp()).append(",").append(infoDto.getPublicIp()).append(";");
            }
        }
        for (SiteCodeDto siteCodeDto : this.connSiteCode) {
            builder.append(siteCodeDto.getSiteCode()).append(":").append(siteCodeDto.getLevel()).append(",");
        }
        String content = builder.toString();
        return StringUtils.isNotBlank(content) && content.contains(key);
    }
}


