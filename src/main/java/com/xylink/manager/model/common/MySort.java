package com.xylink.manager.model.common;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/1/24 3:27 下午
 */
public class MySort {
    /**
     * active-aes  name-desc
     */
    private final String sort;

    private static final String SORT_SPLIT_CHAR = "-";

    public MySort(String sort) {
        this.sort = sort;
    }

    public String sortColumn() {
        return StringUtils.isNotBlank(sort) ? sort.split(SORT_SPLIT_CHAR)[0] : null;
    }

    public String sortRule() {
        return StringUtils.isNotBlank(sort) ? sort.split(SORT_SPLIT_CHAR)[1] : "AES";
    }

    public boolean orderBy() {
        return StringUtils.isNotBlank(sort);
    }

}
