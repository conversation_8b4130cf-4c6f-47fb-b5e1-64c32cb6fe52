package com.xylink.manager.model.common.pagehelper;

import java.io.Closeable;
import java.io.IOException;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @since 2023/3/10 12:24 AM
 */
public class PageInfo<E> extends ArrayList<E> implements Closeable {
    /**
     * 页码，从1开始
     */
    private long pageNum;
    /**
     * 页面大小
     */
    private long pageSize;
    /**
     * 起始行
     */
    private long startRow;
    /**
     * 末行
     */
    private long endRow;
    /**
     * 总数
     */
    private long total;


    public PageInfo() {
        super();
    }

    public PageInfo(long pageNum, long pageSize) {
        super();
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        calculateStartAndEndRow();
    }

    private void calculateStartAndEndRow() {
        this.startRow = this.pageNum > 0 ? (this.pageNum - 1) * this.pageSize : 0;
        this.endRow = this.startRow + this.pageSize * (this.pageNum > 0 ? 1 : 0);
    }

    @Override
    public void close() throws IOException {
        PageHelper.clearPage();
    }

    public long getPageNum() {
        return pageNum;
    }

    public void setPageNum(long pageNum) {
        this.pageNum = pageNum;
    }

    public long getPageSize() {
        return pageSize;
    }

    public void setPageSize(long pageSize) {
        this.pageSize = pageSize;
    }

    public long getStartRow() {
        return startRow;
    }

    public void setStartRow(long startRow) {
        this.startRow = startRow;
    }

    public long getEndRow() {
        return endRow;
    }

    public void setEndRow(long endRow) {
        this.endRow = endRow;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }
}
