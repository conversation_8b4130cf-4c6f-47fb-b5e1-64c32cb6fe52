package com.xylink.manager.model.common.pagehelper;

import com.xylink.manager.model.common.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/3/9 4:10 下午
 */
public interface Dialect {
    /**
     * 生成count sql
     *
     * @param boundSql
     * @param pageable
     * @return
     */
    String getCountSql(String boundSql, Pageable pageable);

    /**
     * 执行完 count 查询后
     *
     * @param count
     * @param pageable
     * @return true 继续分页查询 false 直接返回
     */
    boolean afterCount(long count, Pageable pageable);

    /**
     * 生成分页sql
     *
     * @param boundSql
     * @param pageable
     * @return
     */
    String getPageSql(String boundSql, Pageable pageable);

    /**
     * 执行完分页查询后
     *
     * @param list
     * @param pageable
     * @return
     */
    Object afterPage(List list, Pageable pageable);

}
