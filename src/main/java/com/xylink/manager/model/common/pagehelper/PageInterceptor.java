package com.xylink.manager.model.common.pagehelper;

import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.model.common.Pageable;
import com.xylink.manager.model.common.pagehelper.dialect.MysqlDialect;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2023/3/9 6:43 PM
 */
@Component
@Aspect
@Slf4j
public class PageInterceptor {

    private final Dialect dialect = new MysqlDialect();

    @Pointcut("execution(* com.xylink.manager.model.common.pagehelper.PageHelper.query(..))")
    public void pageHelper() {

    }

    @Around("pageHelper()")
    public Object around(ProceedingJoinPoint pjp) {
        PageInfo page = PageHelper.getLocalPage();
        if (page == null) {
            try {
                return pjp.proceed();
            } catch (Throwable e) {
                throw new ServerException(e, ErrorStatus.UNEXPECTED_ERROR);
            }
        }
        try {
            Object[] args = pjp.getArgs();
            String boundSql = (String) args[0];
            Pageable pageable = (Pageable) args[1];
            Statement statement = (Statement) args[2];
            Function<ResultSet, List> function = (Function<ResultSet, List>) args[3];

            String countSql = this.dialect.getCountSql(boundSql, pageable);
            log.debug("Query count sql:{}", countSql);
            ResultSet rs = statement.executeQuery(countSql);
            long count = 0;
            while (rs.next()) {
                count = rs.getLong(1);
            }
            if (!this.dialect.afterCount(count, pageable)) {
                return this.dialect.afterPage(new ArrayList<>(), pageable);
            }

            String pageSql = this.dialect.getPageSql(boundSql, pageable);
            log.debug("Query page sql:{}", pageSql);
            rs = statement.executeQuery(pageSql);
            return this.dialect.afterPage(function.apply(rs), pageable);
        } catch (SQLException e) {
            throw new ServerException(e, ErrorStatus.UNEXPECTED_ERROR);
        } finally {
            PageHelper.clearPage();
        }
    }

}
