package com.xylink.manager.model.common.pagehelper.dialect;


import com.xylink.manager.model.common.Pageable;
import com.xylink.manager.model.common.pagehelper.Dialect;
import com.xylink.manager.model.common.pagehelper.PageHelper;
import com.xylink.manager.model.common.pagehelper.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/3/9 4:55 下午
 */
public abstract class AbstractDialect implements Dialect {

    public <T> PageInfo<T> getLocalPage() {
        return PageHelper.getLocalPage();
    }

    @Override
    public boolean afterCount(long count, Pageable pageable) {
        PageInfo<Object> page = getLocalPage();
        page.setTotal(count);
        if (page.getPageSize() < 0) {
            return false;
        }
        return page.getPageNum() > 0 && count > page.getStartRow();
    }

    @Override
    public String getPageSql(String boundSql, Pageable pageable) {
        return getPageSql(boundSql, getLocalPage());
    }

    /**
     * 处理分页
     *
     * @param boundSql
     * @param pageInfo
     * @return
     */
    protected abstract String getPageSql(String boundSql, PageInfo<Object> pageInfo);

    @Override
    public Object afterPage(List list, Pageable pageable) {
        PageInfo<Object> page = getLocalPage();
        if (page == null) {
            return list;
        }
        page.addAll(list);
        return page;
    }
}
