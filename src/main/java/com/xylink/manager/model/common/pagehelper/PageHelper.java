package com.xylink.manager.model.common.pagehelper;

import com.xylink.manager.model.common.Pageable;
import org.springframework.stereotype.Component;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2023/3/9 4:57 下午
 */
@Component
public class PageHelper {

    private static final ThreadLocal<PageInfo> LOCAL_PAGE = new ThreadLocal<>();

    public <T> List<T> query(String boundSql, Pageable pageable, Statement statement, Function<ResultSet, List<T>> function) throws SQLException {
        ResultSet rs = statement.executeQuery(boundSql);
        return function.apply(rs);
    }

    /**
     * 该方法不支持分页
     *
     * @param pageable
     * @param pstm
     * @param function
     * @return
     * @param <T>
     * @throws SQLException
     * @Deprecated 该方法不支持分页
     */
    @Deprecated
    public <T> List<T> queryByPstm(Pageable pageable, PreparedStatement pstm, Function<ResultSet, List<T>> function) throws SQLException {
        ResultSet rs = pstm.executeQuery();
        return function.apply(rs);
    }
    public static void setLocalPage(PageInfo page) {
        LOCAL_PAGE.set(page);
    }

    public static <T> PageInfo<T> getLocalPage() {
        return LOCAL_PAGE.get();
    }

    public static void clearPage() {
        LOCAL_PAGE.remove();
    }

    public static <E> PageInfo<E> startPage(Pageable pageable) {
        PageInfo<E> page = new PageInfo<>(pageable.getPageNumber(), pageable.getPageSize());
        setLocalPage(page);
        return page;
    }

}