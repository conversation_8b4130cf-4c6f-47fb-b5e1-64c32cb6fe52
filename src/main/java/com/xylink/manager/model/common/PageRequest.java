package com.xylink.manager.model.common;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2021/7/12 9:27 上午
 */
public class PageRequest implements Pageable, Serializable {
    private static PageRequest DEFAULT_SINGLETON = new PageRequest(1, 10);

    private final long page;
    private final long size;

    public PageRequest(long page, long size) {
        if (page < 0) {
            throw new IllegalArgumentException("Page index must not be less than zero!");
        }
        if (size < 1) {
            throw new IllegalArgumentException("Page size must not be less than one!");
        }
        this.page = page;
        this.size = size;
    }

    @Override
    public long getPageNumber() {
        return page;
    }

    @Override
    public long getPageSize() {
        return size;
    }

    public static PageRequest getDefault() {
        return DEFAULT_SINGLETON;
    }
}
