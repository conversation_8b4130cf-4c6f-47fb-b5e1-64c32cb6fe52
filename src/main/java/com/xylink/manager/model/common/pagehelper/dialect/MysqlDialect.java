package com.xylink.manager.model.common.pagehelper.dialect;

import com.xylink.manager.model.common.Pageable;
import com.xylink.manager.model.common.pagehelper.PageInfo;

/**
 * <AUTHOR>
 * @since 2023/3/9 6:30 PM
 */
public class MysqlDialect extends AbstractDialect {
    @Override
    public String getCountSql(String boundSql, Pageable pageable) {
        return "select count(*) from (" + boundSql + ") tmp";
    }

    @Override
    public String getPageSql(String boundSql, PageInfo<Object> pageInfo) {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(boundSql);
        sqlBuilder.append(" ").append("LIMIT");
        if (pageInfo.getStartRow() > 0) {
            sqlBuilder.append(" ").append(pageInfo.getStartRow()).append(",");
        }
        sqlBuilder.append(" ").append(pageInfo.getPageSize());
        return sqlBuilder.toString();
    }
}
