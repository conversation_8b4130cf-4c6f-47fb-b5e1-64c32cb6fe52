package com.xylink.manager.model.common;

import com.xylink.manager.model.common.pagehelper.PageInfo;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 分页对象
 *
 * <AUTHOR>
 * @since 2021/5/19 11:07 上午
 */
public class Page<T> implements IPage<T> {
    /**
     * 总数
     */
    private long total = 0;
    /**
     * 当前页
     */
    private long current = 1;
    /**
     * 每页显示条数
     */
    private long pageSize = 10;
    /**
     * 数据列表
     */
    private List<T> records = Collections.emptyList();

    public Page() {
    }

    public Page(List<T> records) {
        this.records = records;
        if (records instanceof PageInfo) {
            PageInfo<T> page = (PageInfo<T>) records;
            this.current = page.getPageNum();
            this.pageSize = page.getPageSize();
            this.total = page.getTotal();
        } else {
            this.total = records.size();
        }
    }

    public Page(long current, long pageSize) {
        this.current = current;
        this.pageSize = pageSize;
    }

    public Page(int pageIndex, int pageSize, Long total) {
        this.current = pageIndex;
        this.total = total;
        this.pageSize = pageSize;
    }

    public Page(long current, long pageSize, long total, List<T> records) {
        this.current = current;
        this.pageSize = pageSize;
        this.records = records;
        this.total = total;
    }

    @Override
    public long getTotal() {
        return this.total;
    }

    @Override
    public Page<T> setTotal(long total) {
        this.total = total;
        return this;
    }

    @Override
    public List<T> getRecords() {
        return this.records;
    }

    @Override
    public Page<T> setRecords(List<T> records) {
        this.records = records;
        return this;
    }

    @Override
    public long getPageSize() {
        return pageSize;
    }

    @Override
    public Page<T> setPageSize(long pageSize) {
        this.pageSize = pageSize;
        return this;
    }

    @Override
    public long getCurrent() {
        return current;
    }

    @Override
    public Page<T> setCurrent(long current) {
        this.current = current;
        return this;
    }

    public static <T> Page<T> emptyPage(Pageable pageable) {
        return new Page<>(pageable.getPageNumber(), pageable.getPageSize(), 0L, Collections.emptyList());
    }

    public static <A> Page<A> emptyPage(int pageIndex, int pageSize) {
        return new Page<>(pageIndex, pageSize, 0L, new ArrayList<>());
    }

    @Override
    public String toString() {
        return "Page{" +
                "total=" + total +
                ", current=" + current +
                ", pageSize=" + pageSize +
                ", records=" + records +
                '}';
    }
}
