package com.xylink.manager.model.common;

import java.io.Serializable;
import java.util.List;

/**
 * 分页 Page 接口
 *
 * <AUTHOR>
 * @since 2021/5/19 10:56 上午
 */
public interface IPage<T> extends Serializable {
    /**
     * 总记录数
     *
     * @return
     */
    long getTotal();

    /**
     * 设置总记录数
     *
     * @param total
     * @return
     */
    IPage<T> setTotal(long total);

    /**
     * 分页记录列表
     *
     * @return 分页对象记录列表
     */
    List<T> getRecords();

    /**
     * 设置分页记录列表
     *
     * @param records
     * @return
     */
    IPage<T> setRecords(List<T> records);

    /**
     * 每页大小
     *
     * @return
     */
    long getPageSize();

    /**
     * 设置每页大小
     *
     * @param pageSize
     * @return
     */
    IPage<T> setPageSize(long pageSize);

    /**
     * 当前页，默认 1
     *
     * @return 当前页
     */
    long getCurrent();

    /**
     * 设置当前页
     *
     * @param current
     */
    IPage<T> setCurrent(long current);

    /**
     * 当前分页总页数
     */
    default long getPages() {
        if (getPageSize() == 0) {
            return 0L;
        }
        long pages = getTotal() / getPageSize();
        if (getTotal() % getPageSize() != 0) {
            pages++;
        }
        return pages;
    }

    /**
     * 内部什么也不干
     * <p>只是为了 json 反序列化时不报错</p>
     *
     * @param pages
     */
    default IPage<T> setPages(long pages) {
        // to do nothing
        return this;
    }

    default long offset() {
        long current = getCurrent();
        return current <= 1L ? 0L : Math.max((current - 1L) * getPageSize(), 0L);
    }

}
