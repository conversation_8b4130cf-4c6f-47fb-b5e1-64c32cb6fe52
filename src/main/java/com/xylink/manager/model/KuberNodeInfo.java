package com.xylink.manager.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xylink.config.mapper.JsonMapper;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/11/15.
 */
public class KuberNodeInfo {
    private String nodeName;
    private String nodeType;
    private Long cpuCores;
    private Long memorySize;
    private String ipAddress;
    private String publicIpAddress;
    private String domain;
    private List<KuberDiskInfo> kuberDiskInfos;
    private Float memoryUsage;
    private Float cpuUsage;
    private Float networkTx;
    private Float networkRx;
    @JsonIgnore
    private String status;

    public KuberNodeInfo() {
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public Long getCpuCores() {
        return cpuCores;
    }

    public void setCpuCores(Long cpuCores) {
        this.cpuCores = cpuCores;
    }

    public Long getMemorySize() {
        return memorySize;
    }

    public void setMemorySize(Long memorySize) {
        this.memorySize = memorySize;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public List<KuberDiskInfo> getKuberDiskInfos() {
        return kuberDiskInfos;
    }

    public void setKuberDiskInfos(List<KuberDiskInfo> kuberDiskInfos) {
        this.kuberDiskInfos = kuberDiskInfos;
    }

    public String getPublicIpAddress() {
        return publicIpAddress;
    }

    public void setPublicIpAddress(String publicIpAddress) {
        this.publicIpAddress = publicIpAddress;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getNodeType() {
        return nodeType;
    }

    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }

    public Float getMemoryUsage() {
        return memoryUsage;
    }

    public void setMemoryUsage(Float memoryUsage) {
        this.memoryUsage = memoryUsage;
    }

    public Float getCpuUsage() {
        return cpuUsage;
    }

    public void setCpuUsage(Float cpuUsage) {
        this.cpuUsage = cpuUsage;
    }

    public Float getNetworkTx() {
        return networkTx;
    }

    public void setNetworkTx(Float networkTx) {
        this.networkTx = networkTx;
    }

    public Float getNetworkRx() {
        return networkRx;
    }

    public void setNetworkRx(Float networkRx) {
        this.networkRx = networkRx;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString(){
        return JsonMapper.nonEmptyMapper().toJson(this);
    }


}
