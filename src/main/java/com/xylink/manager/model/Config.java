package com.xylink.manager.model;

import lombok.Getter;
import lombok.Setter;


@Setter
@Getter
public class Config {

    private String id;
    private String name;
    private String configName;
    private String configValue;
    private String oldConfigValue;

    public Config() {
    }

    public Config(String name, String configName, String configValue) {
        this.name = name;
        this.configName = configName;
        this.configValue = configValue;
    }

    @Override
    public String toString() {
        return "Config{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", configName='" + configName + '\'' +
                ", configValue='" + configValue + '\'' +
                '}';
    }
}
