package com.xylink.manager.model;

import com.xylink.config.exception.basic.ClientErrorException;
import com.xylink.config.exception.basic.ErrorStatus;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
public class PushWhitelistDto {
    private Integer id;

    /**
     * 终端型号码，如207对应终端型号为AE350
     */
    private Integer subType;
    private String categoryDisplay;

    private String applicationName;
    private Integer applicationType;
    private String parameterType;
    private String parameter;

    public static void validate(PushWhitelistDto dto) {
        if (Objects.isNull(dto) || Objects.isNull(dto.getSubType())|| StringUtils.isBlank(dto.getParameter())
                || StringUtils.isBlank(dto.getApplicationName()) || Objects.isNull(dto.getApplicationType()) || StringUtils.isBlank(dto.getParameterType())
        ) {
            throw new ClientErrorException(ErrorStatus.PARAM_NULL);
        }
    }
}
