package com.xylink.manager.model.dts;

/**
 * <AUTHOR>
 * @since 2022/11/7 3:52 下午
 */
public enum CheckListEnum {
    /**
     * 源库连接性检查
     */
    SOURCE_CONNECTION("source_connection", "源库连接性检查", "检查数据传输服务器是否能连通源数据库"),
    /**
     * 源库权限检查
     */
    SOURCE_PERMISSION("source_permission", "源库权限检查", "检查源数据库账号权限是否满足迁移条件"),
    /**
     * 目的库连接性检查
     */
    TARGET_CONNECTION("target_connection", "目的库连接性检查", "检查数据传输服务器是否能连通目的数据库"),
    /**
     * 目的库权限检查
     */
    TARGET_PERMISSION("target_permission", "目的库权限检查", "检查目的数据库账号权限是否满足迁移条件");
    private final String key;
    private final String name;
    private final String comment;

    CheckListEnum(String key, String name, String comment) {
        this.key = key;
        this.name = name;
        this.comment = comment;
    }

    public String getKey() {
        return key;
    }

    public String getName() {
        return name;
    }

    public String getComment() {
        return comment;
    }
}
