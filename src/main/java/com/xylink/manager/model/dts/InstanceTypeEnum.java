package com.xylink.manager.model.dts;

/**
 * <AUTHOR>
 * @since 2022/10/29 5:33 下午
 */
public enum InstanceTypeEnum {
    /**
     * XY
     */
    XY("XY自建数据库", "xy"),
    /**
     * CUSTOMER
     */
    CUSTOMER("客户正式数据库", "customer"),
    /**
     * RECOVERY
     */
    RECOVERY("灾备数据库", "recovery"),
    ;
    private final String name;
    private final String value;

    InstanceTypeEnum(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public String getValue() {
        return value;
    }
}
