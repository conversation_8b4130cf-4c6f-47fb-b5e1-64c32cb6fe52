package com.xylink.manager.model.dts;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022/11/11 8:44 下午
 */
@Data
public class JobListDto implements Serializable, Comparable<JobListDto> {
    private String id;
    private String name;
    private String status;
    private Date startTime;
    private Date endTime;
    private Date createTime;
    private Date updateTime;

    @Override
    public int compareTo(JobListDto o) {
        return this.createTime.compareTo(o.createTime);
    }
}
