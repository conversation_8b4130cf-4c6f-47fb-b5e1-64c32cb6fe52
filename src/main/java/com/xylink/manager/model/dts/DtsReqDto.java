package com.xylink.manager.model.dts;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/10/31 2:27 下午
 */
@Data
public class DtsReqDto implements Serializable {
    @NotEmpty(message = "任务名称不能为空")
    private String name;
    private ConnectionInfoReqDto source;
    private ConnectionInfoReqDto target;
    @NotNull(message = "迁移对象不能为空")
    private List<String> objects;

}
