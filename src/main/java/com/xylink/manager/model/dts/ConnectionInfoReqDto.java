package com.xylink.manager.model.dts;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022/10/29 5:31 下午
 */
@Data
public class ConnectionInfoReqDto implements Serializable {
    @NotEmpty(message = "实例类型不能为空")
    private String instanceType;
    @NotEmpty(message = "数据库类型不能为空")
    private String databaseType;
    @NotEmpty(message = "数据库ip不能为空")
    private String ip;
    @NotNull(message = "数据库端口不能为空")
    private int port;
    @JsonIgnore
    private String username = "dbbak";
    @JsonIgnore
    private String password = "U1O5ZeRyLFd#u9T6TF9h";
    private String database;
}
