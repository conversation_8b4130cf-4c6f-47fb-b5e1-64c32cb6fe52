package com.xylink.manager.model.dts;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2022/10/29 5:26 下午
 */
@Data
public class ConnectionTestDto implements Serializable {
    private boolean connectionResult;
    private String errorMessage;
    private String serverInfo;
    private String driverInfo;

    public static ConnectionTestDto errorOf(String errorMessage) {
        if (StringUtils.isBlank(errorMessage)) {
            errorMessage = "未知错误";
        }
        ConnectionTestDto result = new ConnectionTestDto();
        result.setConnectionResult(false);
        result.setErrorMessage(errorMessage);
        return result;
    }

    public static ConnectionTestDto successOf() {
        ConnectionTestDto result = new ConnectionTestDto();
        result.setConnectionResult(true);
        return result;
    }
}
