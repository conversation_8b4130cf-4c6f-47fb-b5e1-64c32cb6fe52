package com.xylink.manager.model.dts;

/**
 * <AUTHOR>
 * @since 2022/10/29 5:33 下午
 */
public enum DatabaseTypeEnum {
    /**
     * mysql
     */
    MYSQL("Mysql", "MYSQL"),
    /**
     * dm
     */
    DM("达梦8", "DM"),
    /**
     * st
     */
    ST("神通7", "ST"),
    /**
     * jc
     */
    JC("金仓V8R6", "JC");
    private final String name;
    private final String value;

    DatabaseTypeEnum(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public String getValue() {
        return value;
    }
}
