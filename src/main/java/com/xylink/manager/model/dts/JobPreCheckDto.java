package com.xylink.manager.model.dts;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2022/11/3 5:54 下午
 */
@Data
public class JobPreCheckDto implements Serializable {
    private int totalCheckListCount;
    private int successCount;
    private int errorCount;
    private List<CheckItem> items;

    public JobPreCheckDto(List<CheckItem> allItems) {
        Objects.requireNonNull(allItems);
        this.totalCheckListCount = allItems.size();
        this.successCount = Long.valueOf(allItems.stream().filter(CheckItem::isSuccess).count()).intValue();
        this.errorCount = this.totalCheckListCount - this.successCount;
        this.items = allItems;
    }

    @Data
    public static class CheckItem {
        private String checkListKey;
        private String checkListName;
        private String checkListComment;
        private boolean success;
        private String detailMessage;
    }
}
