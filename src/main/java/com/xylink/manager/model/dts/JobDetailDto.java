package com.xylink.manager.model.dts;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/11/11 8:44 下午
 */
@Data
public class JobDetailDto implements Serializable {
    private String id;
    private String name;
    private Date startTime;
    private Date endTime;
    private Date createTime;
    private Date updateTime;
    private ConnectionInfoReqDto source;
    private ConnectionInfoReqDto target;
    private List<ObjectDetail> objects;

    @Data
    public static class ObjectDetail implements Serializable {
        private String id;
        private String jobId;
        private String object;
        private String status;
        private Date startTime;
        private Date endTime;
    }

}
