package com.xylink.manager.model;

import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/8/23.
 */
public class HeapsterInfo {
    private List<HeapsterElement> metrics;
    private Date latestTimestamp;

    public List<HeapsterElement> getMetrics() {
        return metrics;
    }

    public void setMetrics(List<HeapsterElement> metrics) {
        this.metrics = metrics;
    }

    public Date getLatestTimestamp() {
        return latestTimestamp;
    }

    public void setLatestTimestamp(Date latestTimestamp) {
        this.latestTimestamp = latestTimestamp;
    }

    @Override
    public String toString() {
        return "HeapsterInfo{" +
                "metrics=" + metrics +
                ", latestTimestamp=" + latestTimestamp +
                '}';
    }
}