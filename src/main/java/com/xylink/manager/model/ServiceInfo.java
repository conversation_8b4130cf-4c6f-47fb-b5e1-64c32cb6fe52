package com.xylink.manager.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class ServiceInfo {

    private String displayName;
    private String server;
    private String nodeName;

    //未配置，则表示改服务不支持内存配置，
    private String xmxKey;

    //未配置，默认512M
    private int xmxMin;

    //用于服务内存配置页面展示
    private Boolean readOnly;


    private String desc;


    private int priority;


    public ServiceInfo(String server) {
        this.server = server;
    }

    public int getXmxMin() {
        return 0 == xmxMin ? 512 : xmxMin;
    }

    public int getPriority(){
        return -1 == priority ? 99999 : priority;
    }
}
