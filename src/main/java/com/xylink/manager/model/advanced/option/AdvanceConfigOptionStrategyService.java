package com.xylink.manager.model.advanced.option;

import com.xylink.manager.model.advanced.AdvanceConfigOption;
import com.xylink.manager.model.advanced.option.strategy.IComplexOptionStrategy;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 高级配置复杂选项策略
 *
 * <AUTHOR>
 * @create 2024/1/16 15:17
 */
@Service
public class AdvanceConfigOptionStrategyService implements ApplicationContextAware {
    private Map<String, IComplexOptionStrategy> configNameToStrategy = new ConcurrentHashMap<>();

    public List<AdvanceConfigOption> getOptionList(String advanceConfigName) {
        if (StringUtils.isBlank(advanceConfigName)) {
            return Collections.emptyList();
        }
        IComplexOptionStrategy optionStrategy = configNameToStrategy.get(advanceConfigName);
        if (Objects.isNull(optionStrategy)) {
            return Collections.emptyList();
        }
        return optionStrategy.getOptionList();
    }


    public boolean contains(String configName) {
        return configNameToStrategy.containsKey(configName);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, IComplexOptionStrategy> classNameToClass = applicationContext.getBeansOfType(IComplexOptionStrategy.class);
        classNameToClass.values().forEach(x->configNameToStrategy.put(x.getItemEnum(),x));
    }
}
