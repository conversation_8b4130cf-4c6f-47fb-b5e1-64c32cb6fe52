package com.xylink.manager.model.advanced;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2024/1/16 14:45
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AdvanceConfigOption {
    private String label;
    /**
     * 只能为数字、字符串、boolean
     */
    private Object value;

    public static final AdvanceConfigOption SWITCH_ON = new AdvanceConfigOption("打开", true);
    public static final AdvanceConfigOption SWITCH_OFF = new AdvanceConfigOption("关闭", false);
}
