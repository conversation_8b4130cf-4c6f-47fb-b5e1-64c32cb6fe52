package com.xylink.manager.model.advanced;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/1/15 10:56
 */
@Getter
public enum AdvancedConfigItemEnum {

    /**
     * nginx自定义监听端口
     */
    nginxPort(ConfigTypeEnum.input, "nginxPort", "代理服务器端口", null, true),
    nginxSslPort(ConfigTypeEnum.input, "nginxSslPort", "代理服务器加密端口", null, true),


    /**
     * 选项模版
     */
    option_template(ConfigTypeEnum.select, "optionTemplate", "ipv6开关", "ipv6开关默认关闭,按需打开", true, Lists.newArrayList(
            new AdvanceConfigOption("打开","true"),
            new AdvanceConfigOption("关闭","false")
    )),

    /**
     * 复杂选项模版：比如要调用其他接口、读取configmap 才能获得的选项
     */
    complex_option_template(ConfigTypeEnum.select, "complexOptionTemplate", "ipv6开关", "ipv6开关默认关闭,按需打开", true, true),
    ;


    AdvancedConfigItemEnum(ConfigTypeEnum itemType, String id, String label, String extra, boolean required) {
        this.itemType = itemType;
        this.id = id;
        this.label = label;
        this.extra = extra;
        this.required = required;
    }

    AdvancedConfigItemEnum(ConfigTypeEnum itemType, String id, String label, String extra, boolean required, boolean complexOptionLogic) {
        this.itemType = itemType;
        this.id = id;
        this.label = label;
        this.extra = extra;
        this.required = required;
        this.complexOptionLogic = complexOptionLogic;
    }

    AdvancedConfigItemEnum(ConfigTypeEnum itemType, String id, String label, String extra, boolean required, List<AdvanceConfigOption> options) {
        this.itemType = itemType;
        this.id = id;
        this.label = label;
        this.extra = extra;
        this.required = required;
        this.options = options;
    }

    /**
     * 输入框 or 字符串
     */
    private ConfigTypeEnum itemType;
    /**
     * 配置实体类字段属性名，跟ICMDto字段相关联
     */
    private String id;
    /**
     * 前端展示 配置名
     */
    private String label;
    /**
     * 前端展示配置项 下面的小字 解释，解释该配置项是做什么的
     */
    private String extra;
    /**
     * 是否必填 or 必选
     */
    private boolean required;

    /**
     * 是否是 复杂的选项逻辑：比如要调用其他接口、读取configmap 才能获得的选项
     * 如果这种，需要通过xxx策略模式获取
     */
    private boolean complexOptionLogic;

    /**
     * 如果是选项，当前选项框的可选选项，如打开 / 关闭
     */
    private List<AdvanceConfigOption> options;



}
