package com.xylink.manager.model.advanced;

import lombok.Getter;

/**
 *
 * 服务 对应的 高级配置模版渲染项
 * <AUTHOR>
 * @create 2024/1/15 11:29
 */
@Getter
public enum AdvancedConfigTemplateEnum {

    openresty_fusion(new AdvancedConfigItemEnum[]{
            AdvancedConfigItemEnum.nginxPort,
            AdvancedConfigItemEnum.nginxSslPort,
//            AdvancedConfigItemEnum.complex_option_template
    }),

    ;


    AdvancedConfigTemplateEnum(AdvancedConfigItemEnum[] itemEnums) {
        this.itemEnums = itemEnums;
    }

    private AdvancedConfigItemEnum[] itemEnums;



}
