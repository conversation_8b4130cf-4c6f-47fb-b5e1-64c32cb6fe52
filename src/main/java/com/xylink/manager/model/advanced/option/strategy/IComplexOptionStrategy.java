package com.xylink.manager.model.advanced.option.strategy;

import com.xylink.manager.model.advanced.AdvanceConfigOption;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/1/16 15:11
 */
public interface IComplexOptionStrategy {
    /**
     * 当前策略，属于哪个高级配置项，和 AdvancedConfigItemEnum.xxx.name() 联动
     * @return
     */
    String getItemEnum();

    /**
     * 获取这个高级配置项的选项
     * @return
     */
    List<AdvanceConfigOption> getOptionList();
}
