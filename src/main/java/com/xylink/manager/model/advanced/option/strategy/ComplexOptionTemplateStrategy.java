package com.xylink.manager.model.advanced.option.strategy;

import com.google.common.collect.Lists;
import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.model.advanced.AdvanceConfigOption;
import com.xylink.manager.model.advanced.AdvancedConfigItemEnum;
import com.xylink.manager.service.base.K8sService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 复杂选项 模版类
 *
 * <AUTHOR>
 * @create 2024/1/16 15:33
 */
@Component
public class ComplexOptionTemplateStrategy implements IComplexOptionStrategy{
    @Autowired
    private K8sService k8sService;

    @Override
    public String getItemEnum() {
        return AdvancedConfigItemEnum.complex_option_template.name();
    }

    @Override
    public List<AdvanceConfigOption> getOptionList() {
        Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        String mainDbIp = allIp.get(NetworkConstants.DATABASE_IP);
        AdvanceConfigOption option = new AdvanceConfigOption(NetworkConstants.DATABASE_IP, mainDbIp);
        return Lists.newArrayList(option);
    }
}
