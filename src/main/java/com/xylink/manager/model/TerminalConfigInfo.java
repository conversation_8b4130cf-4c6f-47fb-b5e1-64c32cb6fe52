package com.xylink.manager.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TerminalConfigInfo {

    private List<ClientTerminalResult> terminalInfo;

    private List<TerminalFrontPlatform> terminalFrontPlatform;

    private Map<String, Object> terminalFrontInfo;
}
