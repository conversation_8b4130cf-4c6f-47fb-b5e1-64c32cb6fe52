package com.xylink.manager.model.monitor;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/1/24 11:20 上午
 */
@Data
public class TablesDDLInfoDto implements Serializable {

    private String ddlStr;
    private String tableName;
    private String db;
    private List<ColumnsInfo> columns;
    private List<IndexesInfo> indexes;

    @Data
    public static class ColumnsInfo {
        private String columnField;
        private String columnType;
        private String columnComment;
        private String columnIsNull;
        private String columnIsAutoIncrement;
        private String columnDefault;

    }

    @Data
    public static class IndexesInfo {
        private String indexName;
        private String indexType;
        private List<String> indexColumnNames = new ArrayList<>();

        public void addColumn(String column) {
            if (StringUtils.isNotBlank(column)) {
                if (!indexColumnNames.contains(column)) {
                    indexColumnNames.add(column);
                }
            }
        }
    }


}
