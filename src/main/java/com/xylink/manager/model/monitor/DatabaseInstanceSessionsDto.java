package com.xylink.manager.model.monitor;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/1/24 10:50 上午
 */
@Data
public class DatabaseInstanceSessionsDto implements Serializable {
    /**
     * 实例ID
     */
    private String instanceId;
    /**
     * 指标，结构为
     * <blockquote><pre>
     *                {
     *                  "name":"totalSessions",
     *                  "value":"1000"
     *               }
     * </pre></blockquote>
     */
    private List<Map<String, Object>> resumes;

    public enum ResumesKeys {
        /**
         * 会话总数
         */
        totalSessions,
        /**
         * 运行中会话总数
         */
        activeSession,
        /**
         * 运行中会话最长时间
         */
        maxTimeSession
    }
}
