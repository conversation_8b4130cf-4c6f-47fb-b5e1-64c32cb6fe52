package com.xylink.manager.model.dmcu;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2021/7/30 4:32 下午
 */
public enum SiteCodeStructure {
    /**
     * 企业码，公有云默认EDEF，混合云企业注册时，会分配固定的企业码
     */
    ENTERPRISE(1, "EDEF"),
    /**
     * 国家码，默认使用 86，部署到其他国家按照标准国家码定义
     */
    SITE(2, "86"),
    /**
     * 区域码，国内默认机房所在省会区号，国外默认SLDEF
     */
    SUBSITE(3, "SLDEF"),
    /**
     * 企业机房的标识， 多机房部署，需要关注，provider相同，代表内网能通。反之，不同机房内网不通，provider需要单独区分开
     */
    PROVIDER(4, null);
    private final int index;

    private final String defaultValue;

    SiteCodeStructure(int index, String defaultValue) {
        this.index = index;
        this.defaultValue = defaultValue;
    }

    public int getIndex() {
        return index;
    }

    public String getDefaultValue() {
        return defaultValue;
    }


}
