package com.xylink.manager.model.develop;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class WebhookDto {
    private String id;
    @JsonProperty(value = "execute-command")
    private String executeCommand;
    @JsonProperty(value = "command-working-directory")
    private String commandWorkingDirectory;

    @JsonProperty(value = "pass-arguments-to-command")
    private List<PassArgumentsToCommand> passArgumentsToCommand;
    @JsonProperty(value = "include-command-output-in-response")
    private Boolean includeCommandOutputInResponse;
    @JsonProperty(value = "include-command-output-in-response-on-error")
    private Boolean includeCommandOutputInResponseOnError;
    @JsonProperty(value = "trigger-rule")
    private TriggerRule triggerRule = new TriggerRule();

    @Data
    public static class PassArgumentsToCommand {
        private String source = "payload";
        private String name;
    }

    @Data
    public static class TriggerRule{
        private Match match = new Match();
    }

    @Data
    public static class Match{
        private String type = "payload-hash-sha1";
        private String secret = "REMOTE_WEBHOOK_PRIVATE_CLOUD_XYLINK!";
        private Parameter parameter = new Parameter();
    }

    @Data
    public static class Parameter{
        private String source = "header";
        private String name = "X-Hub-Signature";
    }
}
