package com.xylink.manager.model;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/8/31.
 */
public class HeapsterSerie {
    private List<String> columns;
    private String name;
    private Map<String, String> tags;

    private List<List<Long>> values;

    public List<String> getColumns() {
        return columns;
    }

    public void setColumns(List<String> columns) {
        this.columns = columns;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Map<String, String> getTags() {
        return tags;
    }

    public void setTags(Map<String, String> tags) {
        this.tags = tags;
    }

    public List<List<Long>> getValues() {
        return values;
    }

    public void setValues(List<List<Long>> values) {
        this.values = values;
    }

    @Override
    public String toString() {
        return "HeapsterSerie{" +
                "columns=" + columns +
                ", name='" + name + '\'' +
                ", tags=" + tags +
                ", values=" + values +
                '}';
    }
}
