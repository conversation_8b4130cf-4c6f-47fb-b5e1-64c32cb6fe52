package com.xylink.manager.model.proper.vo;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class RouteConfigVo {

    /**
     * 默认路由
     */
    private static final String DEFAULT_RULE = "DEFAULT";

    /**
     * 指定路由
     */
    private static final String APPOINT_RULE = "APPOINT";

    private String srcCloudId;

    private String nextCloudId;

    private String destCloudId;

    private String routeRule;

    private RouteConfigVo() {}

    public static RouteConfigVo build() {
        return new RouteConfigVo();
    }

    public RouteConfigVo srcCloudId(String srcCloudId) {
        this.srcCloudId = srcCloudId;
        return this;
    }

    public RouteConfigVo nextCloudId(String nextCloudId) {
        this.nextCloudId = nextCloudId;
        return this;
    }

    public RouteConfigVo destCloudId(String destCloudId) {
        this.destCloudId = destCloudId;
        return this;
    }

    /**
     * 判断路由为DEFAULT或者APPOINT
     * @param routeRule
     * @return
     */
    public RouteConfigVo routeRule(int routeRule) {
        if (routeRule == 0) {
            this.routeRule = DEFAULT_RULE;
        }
        if (routeRule == 1) {
            this.routeRule = APPOINT_RULE;
        }
        return this;
    }
}
