package com.xylink.manager.model.proper;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class LibraCloudCluster {

    private String cloudUuid;

    private String fingerprint;

    private String lastFingerprint;

    private String cloudClusterId;

    private String cloudId;

    private String createTime;

    public static LibraCloudCluster build() {
        return new LibraCloudCluster();
    }

    public LibraCloudCluster cloudUuid(String cloudUuid) {
        this.cloudUuid = cloudUuid;
        return this;
    }

    public LibraCloudCluster fingerprint(String fingerprint) {
        this.fingerprint = fingerprint;
        return this;
    }

    public LibraCloudCluster lastFingerprint(String lastFingerprint) {
        this.lastFingerprint = lastFingerprint;
        return this;
    }

    public LibraCloudCluster cloudClusterId(String cloudClusterId) {
        this.cloudClusterId = cloudClusterId;
        return this;
    }

    public LibraCloudCluster cloudId(String cloudId) {
        this.cloudId = cloudId;
        return this;
    }
}
