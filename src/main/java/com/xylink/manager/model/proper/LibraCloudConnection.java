package com.xylink.manager.model.proper;

import com.xylink.manager.model.common.GroupA;
import com.xylink.manager.model.common.GroupB;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class LibraCloudConnection {

    @NotNull(message = "id 不允许为空",groups = {GroupA.class})
    private Integer id;

    /**
     * 对应指纹码的id值
     */
    private String cloudUuid;

    @NotEmpty(message = "云团ID不允许为空",groups = {GroupA.class, GroupB.class})
    private String cloudClusterId;

    @NotEmpty(message = "分区自治专有云区号不允许为空",groups = {GroupA.class,GroupB.class})
    private String cloudId;

    @NotEmpty(message = "IP地址/域名不允许为空",groups = {GroupA.class,GroupB.class})
    private String cloudDomain;

    @Length(min = 1, max = 255, message = "名称不允许为空并且长度不能大于255个字符", groups = {GroupA.class, GroupB.class})
    private String cloudName;

    private String createTime;

    private int delStatus;

    public LibraCloudConnection cloudUuid(String cloudUuid) {
        this.cloudUuid = cloudUuid;
        return this;
    }
}
