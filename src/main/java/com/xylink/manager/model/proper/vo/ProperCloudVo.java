package com.xylink.manager.model.proper.vo;

import com.fasterxml.jackson.databind.annotation.JsonAppend;
import com.xylink.manager.model.proper.LibraCallCloudAuthority;
import com.xylink.manager.model.proper.LibraCloudConnection;
import com.xylink.manager.model.proper.LibraRouteConfig;
import lombok.Getter;

import java.util.List;

/**
 * 专有云信息，连接、路由、权限
 * <AUTHOR>
 */
@Getter
public class ProperCloudVo {

    private ProperCloudVo() {}

    public static ProperCloudVo build() {
        return new ProperCloudVo();
    }

    /**
     * 云团ID
     */
    private String cloudClusterId;

    /**
     * 分区云区号
     */
    private String cloudId;

    /**
     * 连接配置信息
     */
    private List<LibraCloudConnection> cloudConnectList;

    /**
     * 路由配置信息
     */
    private List<LibraRouteConfig> routeConfigList;

    /**
     * 默认路由配置
     */
    private String defaultRouteConfig;

    /**
     * 呼入权限
     */
    private List<String> callInCloudIds;

    /**
     * 呼出权限
     */
    private List<String> callOutCloudIds;

    public ProperCloudVo cloudClusterId(String cloudClusterId) {
        this.cloudClusterId = cloudClusterId;
        return this;
    }

    public ProperCloudVo cloudId(String cloudId) {
        this.cloudId = cloudId;
        return this;
    }

    public ProperCloudVo cloudConnectList(List<LibraCloudConnection> cloudConnectList) {
        this.cloudConnectList = cloudConnectList;
        return this;
    }

    public ProperCloudVo defaultRouteConfig(String defaultRouteConfig) {
        this.defaultRouteConfig = defaultRouteConfig;
        return this;
    }

    public ProperCloudVo routeConfigList(List<LibraRouteConfig> routeConfigList) {
        this.routeConfigList = routeConfigList;
        return this;
    }

    public ProperCloudVo callInCloudIds(List<String> callInCloudIds) {
        this.callInCloudIds = callInCloudIds;
        return this;
    }

    public ProperCloudVo callOutCloudIds(List<String> callOutCloudIds) {
        this.callOutCloudIds = callOutCloudIds;
        return this;
    }


}
