package com.xylink.manager.model.proper.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/28 7:43 PM
 */
@Data
public class CloudConnectionEnterpriseVo implements Serializable {

    private String connectionId;

    private List<EnterpriseInfo> enterprise;

    public static class EnterpriseInfo {
        private String enterpriseId;
        private String enterpriseName;

        public String getEnterpriseId() {
            return enterpriseId;
        }

        public void setEnterpriseId(String enterpriseId) {
            this.enterpriseId = enterpriseId;
        }

        public String getEnterpriseName() {
            return enterpriseName;
        }

        public void setEnterpriseName(String enterpriseName) {
            this.enterpriseName = enterpriseName;
        }

        @Override
        public String toString() {
            return "EnterpriseInfo{" +
                    "enterpriseId='" + enterpriseId + '\'' +
                    ", enterpriseName='" + enterpriseName + '\'' +
                    '}';
        }
    }
}
