package com.xylink.manager.model.proper.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public class CallCloudAuthorityVo {

    private String cloudClusterId;

    private Set<String> callInCloudIds;

    private Set<String> callOutCloudIds;

    private CallCloudAuthorityVo() {}

    public static CallCloudAuthorityVo build() {
        return new CallCloudAuthorityVo();
    }

    public CallCloudAuthorityVo cloudClusterId(String cloudClusterId) {
        this.cloudClusterId = cloudClusterId;
        return this;
    }
    public CallCloudAuthorityVo callInCloudIds(Set<String> callInCloudIds) {
        this.callInCloudIds = callInCloudIds;
        return this;
    }

    public CallCloudAuthorityVo callOutCloudIds(Set<String> callOutCloudIds) {
        this.callOutCloudIds = callOutCloudIds;
        return this;
    }
}
