package com.xylink.manager.model.proper;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class LibraRouteConfig {

    private Integer routeId;

    @NotEmpty(message = "源区号不允许为空")
    private String srcCloudId;

    @NotEmpty(message = "路由区号不允许为空")
    private String nextCloudId;

    private String dstCloudId;

    private int routeRule;

    private String createTime;
}
