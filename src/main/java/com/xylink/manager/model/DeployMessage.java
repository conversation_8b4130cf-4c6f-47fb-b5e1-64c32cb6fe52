package com.xylink.manager.model;

import lombok.*;

import java.util.List;

@Getter
@NoArgsConstructor
@ToString
public class DeployMessage {

    private String hostname;

    private String type;

    private String ip;

    private String mainProxyExternalIp;

    private String mainProxyDomain;



    public DeployMessage(String hostname, String type, String ip) {
        this.setHostname(hostname);
        this.setType(type);
        this.setIp(ip);
    }

    public DeployMessage(String hostname, String type, String ip, String mainProxyExternalIp, String mainProxyDomain) {
        this.hostname = hostname;
        this.type = type;
        this.ip = ip;
        this.mainProxyExternalIp = mainProxyExternalIp;
        this.mainProxyDomain = mainProxyDomain;
    }

    public void setHostname(String hostname) {
        this.hostname = hostname == null ? null : hostname;
    }

    public void setType(String type) {
        this.type = type == null ? null : type;
    }

    public void setIp(String ip) {
        this.ip = ip == null ? null : ip;
    }

    public String getHostname() {
        return hostname;
    }

    public String getType() {
        return type;
    }

    public String getIp() {
        return ip;
    }

    public String getMainProxyExternalIp() {
        return mainProxyExternalIp;
    }

    public void setMainProxyExternalIp(String mainProxyExternalIp) {
        this.mainProxyExternalIp = mainProxyExternalIp;
    }

    public String getMainProxyDomain() {
        return mainProxyDomain;
    }

    public void setMainProxyDomain(String mainProxyDomain) {
        this.mainProxyDomain = mainProxyDomain;
    }
}
