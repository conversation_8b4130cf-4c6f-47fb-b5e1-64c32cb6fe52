package com.xylink.manager.model.deploy;

import lombok.Data;

@Data
public final class SVC {
    private String name;
    private String namespace;
    private String clusterIp;

    public static SVC buildSVC(io.fabric8.kubernetes.api.model.Service k8sService) {
        SVC svc = new SVC();
        svc.setName(k8sService.getMetadata().getName());
        svc.setNamespace(k8sService.getMetadata().getNamespace());
        svc.setClusterIp(k8sService.getSpec().getClusterIP());
        return svc;
    }
}
