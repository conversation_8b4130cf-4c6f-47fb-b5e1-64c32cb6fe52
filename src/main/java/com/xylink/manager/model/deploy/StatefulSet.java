package com.xylink.manager.model.deploy;

import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

@Data
public final class StatefulSet {
    private String name;
    private String namespace;
    private List<Container> containers;
    private List<Volume> volumes;

    public static StatefulSet buildStatefulSet(io.fabric8.kubernetes.api.model.apps.StatefulSet k8sStatefulSet) {
        StatefulSet statefulSet = new StatefulSet();
        statefulSet.setName(k8sStatefulSet.getMetadata().getName());
        statefulSet.setNamespace(k8sStatefulSet.getMetadata().getNamespace());
        statefulSet.setContainers(k8sStatefulSet.getSpec()
                .getTemplate()
                .getSpec()
                .getContainers()
                .stream()
                .map(Container::buildContainer)
                .collect(Collectors.toList()));
        statefulSet.setVolumes(k8sStatefulSet.getSpec()
                .getTemplate()
                .getSpec()
                .getVolumes()
                .stream()
                .map(Volume::buildVolume)
                .collect(Collectors.toList()));
        return statefulSet;
    }
}
