package com.xylink.manager.model.deploy;

import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

@Data
public final class DaemonSet {
    private String name;
    private String namespace;
    private List<Container> containers;
    private List<Container> initContainers;
    private List<Volume> volumes;

    public static DaemonSet buildDaemonSet(io.fabric8.kubernetes.api.model.apps.DaemonSet k8sDaemonSet) {
        DaemonSet daemonSet = new DaemonSet();
        daemonSet.setName(k8sDaemonSet.getMetadata().getName());
        daemonSet.setNamespace(k8sDaemonSet.getMetadata().getNamespace());
        daemonSet.setContainers(k8sDaemonSet.getSpec()
                .getTemplate()
                .getSpec()
                .getContainers()
                .stream()
                .map(Container::buildContainer)
                .collect(Collectors.toList()));
        daemonSet.setInitContainers(k8sDaemonSet.getSpec()
                .getTemplate()
                .getSpec()
                .getInitContainers()
                .stream()
                .map(Container::buildContainer)
                .collect(Collectors.toList()));
        daemonSet.setVolumes(k8sDaemonSet.getSpec()
                .getTemplate()
                .getSpec()
                .getVolumes()
                .stream()
                .map(Volume::buildVolume)
                .collect(Collectors.toList()));
        return daemonSet;
    }
}
