package com.xylink.manager.model.deploy;

import lombok.Data;

@Data
public final class Event {
    private String name;
    private String sourceHost;
    private String creationTimestamp;
    private String type;
    private String uid;
    private String message;
    private String reason;
    private String action;
    private String jsonContent;

    public static Event buildEvent(io.fabric8.kubernetes.api.model.Event k8sEvent) {
        Event event = new Event();
        event.setName(k8sEvent.getMetadata().getName());
        event.setSourceHost(k8sEvent.getSource().getHost());
        event.setCreationTimestamp(k8sEvent.getMetadata().getCreationTimestamp());
        event.setType(k8sEvent.getType());
        event.setUid(k8sEvent.getMetadata().getUid());
        event.setMessage(k8sEvent.getMessage());
        event.setReason(k8sEvent.getReason());
        return event;
    }
}
