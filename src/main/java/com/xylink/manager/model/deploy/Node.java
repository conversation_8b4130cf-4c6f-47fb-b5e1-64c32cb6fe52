package com.xylink.manager.model.deploy;

import com.xylink.config.Constants;
import com.xylink.manager.inspection.entity.Constant;
import io.fabric8.kubernetes.api.model.NodeAddress;
import io.fabric8.kubernetes.api.model.NodeCondition;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Data
public final class Node {
    /**
     * 节点名称
     */
    private String name;
    /**
     * 节点hostname
     * 获取路径：labels[hostname]
     * 如果没有标签，则获取： status.address[?type=Hostname].address
     */
    private String hostName;
    /**
     * 节点ip
     * 获取路径：status.address[?type=InternalIP].address
     */
    private String ip;
    /**
     * 节点的标签列表
     */
    private Map<String, String> labels;
    /**
     * 节点的类型
     * 获取路径：labels[type]
     */
    private String type;
    /**
     * 节点状态
     * 获取路径：status.conditions[?type==Ready].status
     * 取第一个type=ready的condition的status
     */
    private String readyStatus;
    /**
     * 节点就绪状态
     */
    private boolean ready;
    /**
     * 节点CPU状态
     * 获取路径：status.allocatable[cpu]
     */
    private Quantity cpu;
    /**
     * 节点内存状态
     * 获取路径：status.allocatable[memory]
     */
    private Quantity memory;
    /**
     * 节点磁盘状态
     * 获取路径：status.capacity[ephemeral-storage]
     */
    private Quantity disk;

    @Data
    public static class Quantity {
        private String format;
        private String amount;
    }

    public static Node buildNode(io.fabric8.kubernetes.api.model.Node k8sNode) {
        Map<String, String> labels = k8sNode.getMetadata().getLabels();
        if(Objects.isNull(labels)){
            labels = new HashMap<>();
        }
        Node node = new Node();
        node.setName(k8sNode.getMetadata().getName());
        if (CollectionUtils.isEmpty(labels)) {
            node.setHostName("");
        } else {
            node.setHostName(labels.getOrDefault(Constant.HOST_NAME, ""));
        }
        if (StringUtils.isBlank(node.getHostName())) {
            node.setHostName(k8sNode.getStatus().getAddresses().stream()
                    .filter(x -> x.getType().equalsIgnoreCase(Constants.HOSTNAME))
                    .findFirst()
                    .map(NodeAddress::getAddress)
                    .orElse(""));
        }
        node.setIp(k8sNode.getStatus().getAddresses().stream()
                .filter(x -> x.getType().equalsIgnoreCase(Constants.INTERNAL_IP))
                .findFirst()
                .map(NodeAddress::getAddress)
                .orElse(""));
        node.setLabels(labels);
        node.setType(labels.get(Constants.TYPE));
        node.setReadyStatus(k8sNode.getStatus().getConditions()
                .stream()
                .filter(it -> Constants.STATUS_READY.equalsIgnoreCase(it.getType()))
                .findFirst()
                .map(NodeCondition::getStatus)
                .orElse(null));
        node.setReady(StringUtils.equals(node.getReadyStatus(), "True"));
        io.fabric8.kubernetes.api.model.Quantity cpuQuantity = k8sNode.getStatus().getAllocatable().get(Constants.CPU);
        if (cpuQuantity != null) {
            Node.Quantity quantity = new Node.Quantity();
            quantity.setFormat(cpuQuantity.getFormat());
            quantity.setAmount(cpuQuantity.getAmount());
            node.setCpu(quantity);
        }
        io.fabric8.kubernetes.api.model.Quantity memoryQuantity = k8sNode.getStatus().getAllocatable().get(Constants.MEMORY);
        if (memoryQuantity != null) {
            Node.Quantity quantity = new Node.Quantity();
            quantity.setFormat(memoryQuantity.getFormat());
            quantity.setAmount(memoryQuantity.getAmount());
            node.setMemory(quantity);
        }
        io.fabric8.kubernetes.api.model.Quantity diskQuantity = k8sNode.getStatus().getCapacity().get("ephemeral-storage");
        Node.Quantity quantity = new Node.Quantity();
        node.setDisk(quantity);
        if (diskQuantity != null) {
            quantity.setFormat(diskQuantity.getFormat());
            quantity.setAmount(diskQuantity.getAmount());
        }
        return node;
    }
}
