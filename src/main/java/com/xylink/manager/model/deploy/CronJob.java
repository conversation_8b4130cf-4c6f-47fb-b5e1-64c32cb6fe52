package com.xylink.manager.model.deploy;

import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

@Data
public final class CronJob {
    private String name;
    private String namespace;
    private List<Container> containers;

    public static CronJob buildJob(io.fabric8.kubernetes.api.model.batch.v1beta1.CronJob k8sCronJob) {
        CronJob job = new CronJob();
        job.setName(k8sCronJob.getMetadata().getName());
        job.setNamespace(k8sCronJob.getMetadata().getNamespace());
        job.setContainers(k8sCronJob.getSpec()
                .getJobTemplate()
                .getSpec()
                .getTemplate()
                .getSpec()
                .getContainers()
                .stream()
                .map(Container::buildContainer)
                .collect(Collectors.toList()));
        return job;
    }

    public static CronJob buildJob(io.fabric8.kubernetes.api.model.batch.v1.CronJob k8sCronJob) {
        CronJob job = new CronJob();
        job.setName(k8sCronJob.getMetadata().getName());
        job.setNamespace(k8sCronJob.getMetadata().getNamespace());
        job.setContainers(k8sCronJob.getSpec()
                .getJobTemplate()
                .getSpec()
                .getTemplate()
                .getSpec()
                .getContainers()
                .stream()
                .map(Container::buildContainer)
                .collect(Collectors.toList()));
        return job;
    }
}
