package com.xylink.manager.model.deploy;

import lombok.Data;

import javax.annotation.Nullable;

@Data
public final class Volume {
    private String name;
    @Nullable
    private String configMapName;

    public static Volume buildVolume(io.fabric8.kubernetes.api.model.Volume k8sVolume) {
        Volume volume = new Volume();
        volume.setName(k8sVolume.getName());
        volume.setConfigMapName(k8sVolume.getConfigMap() != null ? k8sVolume.getConfigMap().getName() : null);
        return volume;
    }
}
