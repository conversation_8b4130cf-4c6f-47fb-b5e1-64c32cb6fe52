package com.xylink.manager.model.deploy;

import lombok.Data;

@Data
public final class Namespace {
    private String name;
    private String uid;

    public static Namespace buildNamespace(io.fabric8.kubernetes.api.model.Namespace k8sNamespace) {
        Namespace namespace = new Namespace();
        namespace.setName(k8sNamespace.getMetadata().getName());
        namespace.setUid(k8sNamespace.getMetadata().getUid());
        return namespace;
    }
}
