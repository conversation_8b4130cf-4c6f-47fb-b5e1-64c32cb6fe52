package com.xylink.manager.model.deploy;

import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

@Data
public final class Deployment {
    private String name;
    private String namespace;
    private List<Container> containers;
    private List<Container> initContainers;
    private List<Volume> volumes;

    public static Deployment buildDeployment(io.fabric8.kubernetes.api.model.apps.Deployment k8sDeployment) {
        Deployment deployment = new Deployment();
        deployment.setName(k8sDeployment.getMetadata().getName());
        deployment.setNamespace(k8sDeployment.getMetadata().getNamespace());
        deployment.setContainers(k8sDeployment.getSpec()
                .getTemplate()
                .getSpec()
                .getContainers()
                .stream()
                .map(Container::buildContainer)
                .collect(Collectors.toList()));
        deployment.setInitContainers(k8sDeployment.getSpec()
                .getTemplate()
                .getSpec()
                .getInitContainers()
                .stream()
                .map(Container::buildContainer)
                .collect(Collectors.toList()));
        deployment.setVolumes(k8sDeployment.getSpec()
                .getTemplate()
                .getSpec()
                .getVolumes()
                .stream()
                .map(Volume::buildVolume)
                .collect(Collectors.toList()));
        return deployment;
    }
}
