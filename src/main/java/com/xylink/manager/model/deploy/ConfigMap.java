package com.xylink.manager.model.deploy;

import com.xylink.manager.service.api.NoahApiService;
import lombok.Data;

import javax.annotation.Nonnull;
import java.util.HashMap;
import java.util.Map;

@Data
public final class ConfigMap {
    private String name;
    private String namespace;
    private Map<String, String> data;

    public static ConfigMap buildConfigMap(io.fabric8.kubernetes.api.model.ConfigMap k8sConfigMap) {
        ConfigMap configMap = new ConfigMap();
        configMap.setName(k8sConfigMap.getMetadata().getName());
        configMap.setNamespace(k8sConfigMap.getMetadata().getNamespace());
        configMap.setData(k8sConfigMap.getData());
        if (k8sConfigMap.getData() == null) {
            configMap.setData(new HashMap<>(0));
        }
        return configMap;
    }

    public static ConfigMap buildConfigMap(NoahApiService.ProxyConfigMap proxyConfigMap) {
        ConfigMap configMap = new ConfigMap();
        configMap.setName(proxyConfigMap.getMetadata().getName());
        configMap.setNamespace(proxyConfigMap.getMetadata().getNamespace());
        configMap.setData(proxyConfigMap.getData());
        if (proxyConfigMap.getData() == null) {
            configMap.setData(new HashMap<>(0));
        }
        return configMap;
    }

    @Nonnull
    public Map<String, String> getData() {
        return data;
    }
}
