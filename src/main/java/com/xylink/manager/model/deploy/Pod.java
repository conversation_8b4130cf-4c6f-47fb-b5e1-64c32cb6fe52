package com.xylink.manager.model.deploy;

import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Data
public final class Pod {
    /**
     * Pod名称
     * 获取路径：metadata.name
     */
    private String podName;
    /**
     * Pod命名空间
     * 获取路径：metadata.namespace
     */
    private String namespace;
    /**
     * Pod IP
     * 获取路径：status.podIP
     */
    private String ip;
    /**
     * 宿主机 IP
     * 获取路径：status.hostIP
     */
    private String hostIp;
    /**
     * 宿主机名称
     * 获取路径：spec.nodeName
     */
    private String nodeName;
    /**
     * 应用标签，如果不存在是空串
     * 获取路径：metadata.labels.app
     */
    private String appLabel;
    /**
     * Pod 运行状态
     * 获取路径：status.phase
     */
    private String statusPhase;
    /**
     * Pod 运行状态
     * 获取路径：status.reason
     */
    private String statusReason;
    /**
     * Pod中包含的卷列表
     * 获取路径：spec.volumes
     */
    private List<Volume> volumes;
    /**
     * Pod 启动时间
     * 获取路径：status.startTime
     */
    private String startTime;
    /**
     * Pod 启动时间
     * 获取路径：metadata.creationTimestamp
     */
    private String creationTimestamp;
    /**
     * 表示与 Pod 相关的容器状态的列表。
     * 每个容器状态对象包含该容器的运行状态及其就绪状态等信息。
     * 使用该字段可以获取或跟踪某个 Pod 内所有容器的状态。
     */
    private List<ContainerStatus> containerStatuses;
    /**
     * initContainerStatuses 表示 Pod 中 init container 的状态列表。
     * 其中每个状态由 ContainerStatus 对象表示，包含初始化容器的就绪状态和当前的运行状态信息。
     * 用于追踪 Pod 中所有 init container 的详细状态。
     */
    private List<ContainerStatus> initContainerStatuses;
    /**
     * 运行容器
     */
    private List<Container> containers;
    private List<Condition> conditions;

    @Data
    public static class Condition {
        private String type;
        private String status;
    }

    public static Pod buildPod(io.fabric8.kubernetes.api.model.Pod k8sPod) {
        Pod pod = new Pod();
        pod.setPodName(k8sPod.getMetadata().getName());
        pod.setNamespace(k8sPod.getMetadata().getNamespace());
        pod.setIp(k8sPod.getStatus().getPodIP());
        pod.setHostIp(k8sPod.getStatus().getHostIP());
        pod.setNodeName(k8sPod.getSpec().getNodeName());
        Map<String, String> labels = k8sPod.getMetadata().getLabels();
        labels = Optional.ofNullable(labels).orElse(new HashMap<>(0));
        pod.setAppLabel(labels.getOrDefault("app", ""));
        pod.setStatusPhase(k8sPod.getStatus().getPhase());
        pod.setStatusReason(k8sPod.getStatus().getReason());
        pod.setVolumes(k8sPod.getSpec().getVolumes().stream()
                .map(Volume::buildVolume)
                .collect(Collectors.toList()));
        pod.setStartTime(k8sPod.getStatus().getStartTime());
        pod.setCreationTimestamp(k8sPod.getMetadata().getCreationTimestamp());
        pod.setInitContainerStatuses(k8sPod.getStatus()
                .getInitContainerStatuses()
                .stream()
                .map(ContainerStatus::buildContainerStatus)
                .collect(Collectors.toList()));
        pod.setContainerStatuses(k8sPod.getStatus()
                .getContainerStatuses()
                .stream()
                .map(ContainerStatus::buildContainerStatus)
                .collect(Collectors.toList()));
        pod.setContainers(k8sPod.getSpec().getContainers().stream().map(Container::buildContainer).collect(Collectors.toList()));
        pod.setConditions(k8sPod.getStatus().getConditions().stream().map(it -> {
            Condition condition = new Condition();
            condition.setStatus(it.getStatus());
            condition.setType(it.getType());
            return condition;
        }).collect(Collectors.toList()));
        return pod;
    }

    @Data
    public static class ContainerStatus {
        private Boolean ready;
        private Integer restartCount;
        private ContainerState state;

        public static ContainerStatus buildContainerStatus(io.fabric8.kubernetes.api.model.ContainerStatus k8sContainerStatus) {
            ContainerStatus containerStatus = new ContainerStatus();
            containerStatus.setReady(k8sContainerStatus.getReady());
            containerStatus.setRestartCount(k8sContainerStatus.getRestartCount());
            containerStatus.setState(ContainerState.buildContainerState(k8sContainerStatus.getState()));
            return containerStatus;
        }
    }

    @Data
    public static class ContainerState {
        private ContainerStateRunning running;
        private ContainerStateTerminated terminated;
        private ContainerStateWaiting waiting;

        public static ContainerState buildContainerState(io.fabric8.kubernetes.api.model.ContainerState k8sContainerState) {
            ContainerState state = new ContainerState();
            if (k8sContainerState.getRunning() != null) {
                state.setRunning(new ContainerStateRunning());
                state.getRunning().setStartedAt(k8sContainerState.getRunning().getStartedAt());
            }
            if (k8sContainerState.getTerminated() != null) {
                state.setTerminated(new ContainerStateTerminated());
                state.getTerminated().setContainerID(k8sContainerState.getTerminated().getContainerID());
                state.getTerminated().setExitCode(k8sContainerState.getTerminated().getExitCode());
                state.getTerminated().setFinishedAt(k8sContainerState.getTerminated().getFinishedAt());
                state.getTerminated().setMessage(k8sContainerState.getTerminated().getMessage());
                state.getTerminated().setReason(k8sContainerState.getTerminated().getReason());
                state.getTerminated().setSignal(k8sContainerState.getTerminated().getSignal());
                state.getTerminated().setStartedAt(k8sContainerState.getTerminated().getStartedAt());
            }
            if (k8sContainerState.getWaiting() != null) {
                state.setWaiting(new ContainerStateWaiting());
                state.getWaiting().setMessage(k8sContainerState.getWaiting().getMessage());
                state.getWaiting().setReason(k8sContainerState.getWaiting().getReason());
            }
            return state;
        }
    }

    @Data
    public static class ContainerStateRunning {
        private String startedAt;
    }

    @Data
    public static class ContainerStateTerminated {
        private String containerID;
        private Integer exitCode;
        private String finishedAt;
        private String message;
        private String reason;
        private Integer signal;
        private String startedAt;
    }

    @Data
    public static class ContainerStateWaiting {
        private String message;
        private String reason;
    }
}
