package com.xylink.manager.model.deploy;

import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

@Data
public final class Job {
    private String name;
    private String namespace;
    private List<Condition> conditions;

    @Data
    public static class Condition {
        private String type;
    }

    public static Job buildJob(io.fabric8.kubernetes.api.model.batch.v1.Job k8sJob) {
        Job job = new Job();
        job.setName(k8sJob.getMetadata().getName());
        job.setNamespace(k8sJob.getMetadata().getNamespace());
        job.setConditions(k8sJob.getStatus().getConditions().stream()
                .map(it -> {
                    Condition condition = new Condition();
                    condition.setType(it.getType());
                    return condition;
                }).collect(Collectors.toList()));
        return job;
    }
}
