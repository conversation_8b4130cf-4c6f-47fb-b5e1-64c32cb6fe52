package com.xylink.manager.model.deploy;

import io.fabric8.kubernetes.api.model.EnvVar;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
public final class Container {
    private String name;
    private List<String> command;
    private Map<String, String> env;
    private String image;

    public static Container buildContainer(io.fabric8.kubernetes.api.model.Container k8sContainer) {
        Container container = new Container();
        container.setName(k8sContainer.getName());
        container.setCommand(k8sContainer.getCommand());
        if (k8sContainer.getEnv() != null) {
            container.setEnv(k8sContainer.getEnv()
                    .stream()
                    .filter(it -> it.getValueFrom() == null)
                    .collect(Collectors.toMap(EnvVar::getName, EnvVar::getValue, (a, b) -> b)));
        }
        container.setImage(k8sContainer.getImage());
        return container;
    }
}
