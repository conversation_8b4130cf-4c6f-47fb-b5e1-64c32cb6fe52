package com.xylink.manager.model;

import com.xylink.manager.controller.dto.ISearchDto;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2021/7/8 5:52 下午
 */
@Data
public class DmcuInfoDto implements Serializable, ISearchDto {

    private String internalIp;
    private String publicIp;
    private String siteCode;
    private String nodeName;

    @Override
    public boolean containSearchContent(String key) {
        String content = this.internalIp + ";" + this.publicIp + ";" + this.siteCode + ";" + this.nodeName;
        return StringUtils.isNotBlank(content) && content.contains(key);
    }
}
