package com.xylink.manager.model;

import java.util.ArrayList;
import java.util.List;

public class Page<T> {
	private Integer sEcho = 1;
	private Integer iTotalRecords = 0;
	private Integer iTotalDisplayRecords = 0;
	private Integer iDisplayStart = 0;
	private Integer iDisplayLength = 0;
	private String sSearch;
	private List<T> aaData = new ArrayList<T>();

	public Integer getsEcho() {
		return sEcho;
	}

	public void setsEcho(Integer sEcho) {
		this.sEcho = sEcho;
	}

	public Integer getiTotalRecords() {
		return iTotalRecords;
	}

	public void setiTotalRecords(Integer iTotalRecords) {
		this.iTotalRecords = iTotalRecords;
	}

	public Integer getiTotalDisplayRecords() {
		return iTotalDisplayRecords;
	}

	public void setiTotalDisplayRecords(Integer iTotalDisplayRecords) {
		this.iTotalDisplayRecords = iTotalDisplayRecords;
	}

	public Integer getiDisplayStart() {
		return iDisplayStart;
	}

	public void setiDisplayStart(Integer iDisplayStart) {
		this.iDisplayStart = iDisplayStart;
	}

	public Integer getiDisplayLength() {
		return iDisplayLength;
	}

	public void setiDisplayLength(Integer iDisplayLength) {
		this.iDisplayLength = iDisplayLength;
	}

	public List<T> getAaData() {
		return aaData;
	}

	public void setAaData(List<T> aaData) {
		this.aaData = aaData;
	}

	public String getsSearch() {
		return sSearch;
	}

	public void setsSearch(String sSearch) {
		this.sSearch = sSearch;
	}

	public <E> Page<E> copy(Page<E> page) {

		page.setiDisplayLength(iDisplayLength);
		page.setiDisplayStart(iDisplayStart);
		page.setiTotalDisplayRecords(iTotalDisplayRecords);
		page.setiTotalRecords(iTotalRecords);
		page.setsEcho(sEcho);
		page.setsSearch(sSearch);

		return page;

	}

}
