package com.xylink.manager.model;

import org.springframework.stereotype.Component;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/11/1.
 */

@Component
public class ManagerData {

    private String adminPwd;
    private String mailConfig;
    private String cpuThreshold;
    private String memoryThreshold;
    private String diskThreshold;
    private String alertMails;

    public ManagerData(){}
    public String getAdminPwd() {
        return adminPwd;
    }

    public void setAdminPwd(String adminPwd) {
        this.adminPwd = adminPwd;
    }

    public String getMailConfig() {
        return this.mailConfig;
    }

    public void setMailConfig(String mailConfig) {
        this.mailConfig = mailConfig;
    }

    public String getCpuThreshold() {
        return cpuThreshold;
    }

    public void setCpuThreshold(String cpuThreshold) {
        this.cpuThreshold = cpuThreshold;
    }

    public String getMemoryThreshold() {
        return memoryThreshold;
    }

    public void setMemoryThreshold(String memoryThreshold) {
        this.memoryThreshold = memoryThreshold;
    }

    public String getDiskThreshold() {
        return diskThreshold;
    }

    public void setDiskThreshold(String diskThreshold) {
        this.diskThreshold = diskThreshold;
    }

    public String getAlertMails() {
        return alertMails;
    }

    public void setAlertMails(String alertMails) {
        this.alertMails = alertMails;
    }
}
