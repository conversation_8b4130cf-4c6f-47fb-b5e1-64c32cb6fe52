package com.xylink.manager.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date: 2024/02/26/11:10
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiPermissionRequestDto {
    private Integer page;
    private Integer limit;
    private String createTime;
    private String updateTime;
    private String whiteApiId;
    private String whiteApiCode;

    private String requestMethod;
    private String requestPath;
    private String serviceName;
    /**
     * 不需要展示该字段，但要防止uaa判空
     */
    private String teamName = "s";
    private String remark;
    private String origin;
    private Integer status;
    private String whiteApiMode;
}
