package com.xylink.manager.model.upgrade.log;

import com.opencsv.bean.CsvBindByPosition;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024-05-29 16:16
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class ImageLog extends Log {
    @CsvBindByPosition(position = 0)
    private String id;
    @CsvBindByPosition(position = 1)
    private String serverName;
    @CsvBindByPosition(position = 2)
    private String status;
    @CsvBindByPosition(position = 3)
    private String error;
    @CsvBindByPosition(position = 4)
    private String mode;
    @CsvBindByPosition(position = 5)
    private String path;
}
