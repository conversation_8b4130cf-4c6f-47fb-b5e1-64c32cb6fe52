package com.xylink.manager.model.upgrade;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/2/28 6:25 PM
 */
@Data
public class ServerUpgradeMetadata {
    private String packageName;
    private String associatedIteration;
    private String createTime;
    private String resourceType;
    private UpgradePackageModifyType modifyType;
    private List<AppInfo> appInfos;
    private UpgradePackageMode packageMode;


    public ServerUpgradeMetadata(String packageName) {
        this.packageName = packageName;
        this.modifyType = UpgradePackageModifyType.SERVICE;
        this.setAppInfos(new ArrayList<>());
    }

    @Data
    public static class AppInfo {
        private String dbType;
        private String dbInstance;
        private String updateType;
    }

    public boolean isResourceUpgradePackage() {
        return UpgradePackageModifyType.APP_RESOURCES.equals(this.modifyType) || UpgradePackageModifyType.BASE_RESOURCES.equals(this.modifyType);
    }

    public boolean isImageUpgradePackage() {
        return UpgradePackageModifyType.APP_IMAGES.equals(this.modifyType) || UpgradePackageModifyType.BASE_IMAGES.equals(this.modifyType);
    }

    public boolean isScriptPackage() {
        return UpgradePackageModifyType.SCRIPTS.equals(this.modifyType);
    }

    public boolean isSqlPackage(){
        return UpgradePackageModifyType.SQL.equals(this.modifyType);
    }

    public boolean isCommonOldUpgradePackage() {
        return UpgradePackageModifyType.COMMON_OLD_SERVICE.equals(this.modifyType);
    }
}
