package com.xylink.manager.model.upgrade;

import com.xylink.manager.model.VersionCompare;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * @Author: liyang
 * @DateTime: 2023/3/31 3:22 下午
 **/
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class VersionCompareHistory {
    /**
     * 部署时间
     */
    private String deployTime;

    /**
     * 记录
     */
    private List<VersionCompare> record;

}
