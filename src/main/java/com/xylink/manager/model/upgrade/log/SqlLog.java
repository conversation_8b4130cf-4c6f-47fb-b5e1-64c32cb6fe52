package com.xylink.manager.model.upgrade.log;

import com.opencsv.bean.CsvBindByPosition;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024-05-29 16:16
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SqlLog extends Log {
    @CsvBindByPosition(position = 0)
    private String sqlShellName;
    @CsvBindByPosition(position = 1)
    private String status;
    @CsvBindByPosition(position = 2)
    private String timeConsuming;
    @CsvBindByPosition(position = 3)
    private String number;
    @CsvBindByPosition(position = 4)
    private String message;
    @CsvBindByPosition(position = 5)
    private String operation;
}
