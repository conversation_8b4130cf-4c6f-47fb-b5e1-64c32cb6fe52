package com.xylink.manager.model.upgrade;

/**
 * <AUTHOR>
 * @since 2024/2/27 5:46 PM
 */
public enum UpgradePackageModifyType {
    SQL("SQL升级包"),
    SERVICE("5.2旧升级包"),
    MANAGER("MANAGER"),
    APP_RESOURCES("业务配置文本包"),
    APP_IMAGES("业务镜像包"),
    BASE_RESOURCES("基础配置文本包"),
    BASE_IMAGES("基础镜像包"),
    UNZIP("未解压升级包"),
    SCRIPTS("脚本包"),
    UNKNOWN("UNKNOWN"),
    NOAH("NOAH(配置中心)"),
    COMMON_OLD_SERVICE("5.2旧业务升级包");

    private final String value;

    UpgradePackageModifyType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
