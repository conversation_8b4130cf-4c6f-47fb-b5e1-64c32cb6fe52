package com.xylink.manager.model.upgrade.log;

import com.opencsv.bean.CsvBindByPosition;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024-05-30 11:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ResourceLog extends Log {
    @CsvBindByPosition(position = 0)
    private String appName;
    @CsvBindByPosition(position = 1)
    private String fileType;
    @CsvBindByPosition(position = 2)
    private String id;
    @CsvBindByPosition(position = 3)
    private String status;
    @CsvBindByPosition(position = 4)
    private String reason;
    @CsvBindByPosition(position = 5)
    private String remark;
}
