package com.xylink.manager.model.upgrade;

import lombok.Data;

import java.io.File;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/2/27 9:43 AM
 */
@Data
public class ServerUpgradePackageContentTree implements Serializable {

    private String title;
    private String path;
    private boolean isLeaf;
    private boolean isShowOpen;
    private List<ServerUpgradePackageContentTree> children;

    public ServerUpgradePackageContentTree() {
    }

    public ServerUpgradePackageContentTree(String title, String path, boolean isLeaf, boolean isShowOpen) {
        this.title = title;
        this.path = path;
        this.isLeaf = isLeaf;
        this.isShowOpen = isShowOpen;
        this.children = new ArrayList<>();
    }

    public void addChild(ServerUpgradePackageContentTree child) {
        children.add(child);
    }

    public static ServerUpgradePackageContentTree buildTree(File directory, String rootPath) {
        boolean isRootPath = directory.getParent().equals(rootPath);
        ServerUpgradePackageContentTree root = new ServerUpgradePackageContentTree(directory.getName(), isRootPath ? "" : directory.getParent().replace(rootPath + "/", ""), false, false);
        File[] files = directory.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    ServerUpgradePackageContentTree child = buildTree(file, rootPath);
                    root.addChild(child);
                } else {
                    ServerUpgradePackageContentTree leaf;
                    if (file.getName().endsWith(".sql") && !file.getPath().contains("/original")) {
                        leaf = new ServerUpgradePackageContentTree(file.getName(), file.getParent().replace(rootPath + "/", ""), true, false);//SQL暂时不允许编辑
                    } else {
                        leaf = new ServerUpgradePackageContentTree(file.getName(), file.getParent().replace(rootPath + "/", ""), true, false);
                    }
                    root.addChild(leaf);
                }
            }
        }
        return root;
    }

}
