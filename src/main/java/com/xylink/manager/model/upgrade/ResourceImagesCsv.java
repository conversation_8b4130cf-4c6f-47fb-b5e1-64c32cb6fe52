package com.xylink.manager.model.upgrade;

import com.opencsv.bean.CsvBindByPosition;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-08-23 15:31
 */
@Data
public class ResourceImagesCsv {
    //app_image.access,10.0.7.133/private_cloud/private-access-server-rpm:release-5.2-3.2-20250328-20250327123952-fe3e8bb9-1743050355-anke
    @CsvBindByPosition(position = 0)
    private String var;
    @CsvBindByPosition(position = 1)
    private String varGroup;
    @CsvBindByPosition(position = 2)
    private String value;

    @Override
    public String toString() {
        return "{" + var + "," + varGroup + "," + value + "}";
    }
}
