package com.xylink.manager.model.upgrade;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/2/27 9:43 AM
 */
@Data
public class ServerUpgradeConfig {

    private String id = "1";

    public static ServerUpgradeConfig DEFAULT = new ServerUpgradeConfig(SqlErrorMode._break.getValue(), SqlUpgradeMode.goinception.name());

    /**
     * sql升级模式 break中断 continue忽略继续
     */
    private String sqlErrorMode;
    /**
     * sql升级模式 shell:执行升级包脚本 goinception:使用代理执行 默认值goinception
     */
    private String sqlUpgradeMode;

    public ServerUpgradeConfig() {
    }

    public ServerUpgradeConfig(String sqlErrorMode, String sqlUpgradeMode) {
        this.sqlErrorMode = sqlErrorMode;
        this.sqlUpgradeMode = sqlUpgradeMode;
    }
}
