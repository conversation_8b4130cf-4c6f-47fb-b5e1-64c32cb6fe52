package com.xylink.manager.model.em.taskcenter;

import com.xylink.config.exception.basic.ServerException;
import lombok.Getter;

/**
 * <AUTHOR> create on 2023/12/11
 */
@Getter
public enum DeviceScheduleTaskScopeEnum {
    /**
     * 仅未入会终端生效
     */
    NOT_INCLUDE_ONLINE("NOT_INCLUDE_ONLINE"),
    /**
     * 所有状态
     */
    INCLUDE_ONLINE("INCLUDE_ONLINE");
    private final String scope;


    DeviceScheduleTaskScopeEnum(String scope) {
        this.scope = scope;
    }

    public static void check(String taskScope) {
        for (DeviceScheduleTaskScopeEnum scopeEnum : DeviceScheduleTaskScopeEnum.values()) {
            if (scopeEnum.scope.equals(taskScope)) {
                return;
            }
        }
        throw new ServerException("Illegal taskScope:" + taskScope);
    }

    public static DeviceScheduleTaskScopeEnum match(String taskScope) {
        for (DeviceScheduleTaskScopeEnum scopeEnum : DeviceScheduleTaskScopeEnum.values()) {
            if (scopeEnum.scope.equals(taskScope)) {
                return scopeEnum;
            }
        }
        throw new ServerException("Illegal taskScope:" + taskScope);
    }
}
