package com.xylink.manager.model.em;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2021年09月17日11:51:33
 */
public enum Platform {
    /**
     * x86
     */
    X86,
    /**
     * 国产
     */
    ANKE,
    ALL {
        @Override
        public Set<String> platform() {
            Set<String> data = new HashSet<>();
            Arrays.stream(Platform.values()).filter(item -> !item.equals(ALL)).forEach(item -> data.addAll(item.platform()));
            return data;
        }
    };

    public Set<String> platform() {
        Set<String> data = new HashSet<>();
        data.add(this.name());
        return data;
    }
}