package com.xylink.manager.model.em;

import com.xylink.config.NetworkConstants;

/**
 * <AUTHOR>
 * @date 2022/12/06/11:15
 */
public enum DamengDBType {
    main(NetworkConstants.DATABASE_IP),
    statis(NetworkConstants.STATIS_DATABASE_IP),
    ;

    private final String allIpKey;

    DamengDBType(String allIpKey) {
        this.allIpKey = allIpKey;
    }

    public String getAllIpKey() {
        return allIpKey;
    }

    public static boolean containsName(String name){
        for (DamengDBType shenTongDBType : DamengDBType.values()) {
            if (shenTongDBType.name().equalsIgnoreCase(name)) {
                return true;
            }
        }
        return false;
    }

}
