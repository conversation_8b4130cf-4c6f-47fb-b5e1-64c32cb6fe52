package com.xylink.manager.model.em.taskcenter;

import com.xylink.config.exception.basic.ServerException;
import lombok.Getter;

/**
 * <AUTHOR> create on 2023/12/11
 */
@Getter
public enum DeviceScheduleTaskTypeEnum {
    /**
     * 即时任务
     */
    INSTANT("INSTANT"),
    /**
     * 定时任务
     */
    TIMED("TIMED"),
    ;
    private final String taskType;


    DeviceScheduleTaskTypeEnum(String taskType) {
        this.taskType = taskType;
    }

    public static void check(String taskType) {
        for (DeviceScheduleTaskTypeEnum typeEnum : DeviceScheduleTaskTypeEnum.values()) {
            if (typeEnum.taskType.equals(taskType)) {
                return;
            }
        }
        throw new ServerException("Illegal taskType: " + taskType);
    }
}
