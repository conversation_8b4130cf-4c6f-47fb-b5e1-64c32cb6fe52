package com.xylink.manager.model.em;

import com.xylink.config.NetworkConstants;

/**
 * <AUTHOR>
 * @date 2022/12/06/11:15
 */
public enum ShenTongDBType {
    main(NetworkConstants.DATABASE_IP),
    statis(NetworkConstants.STATIS_DATABASE_IP),
    ;

    private final String allIpKey;

    ShenTongDBType(String allIpKey) {
        this.allIpKey = allIpKey;
    }

    public String getAllIpKey() {
        return allIpKey;
    }

    public static boolean containsName(String name){
        for (ShenTongDBType shenTongDBType : ShenTongDBType.values()) {
            if (shenTongDBType.name().equalsIgnoreCase(name)) {
                return true;
            }
        }
        return false;
    }

}
