package com.xylink.manager.model.em;

/**
 * <AUTHOR>
 * @create 2022/8/18 4:17 下午
 */
public enum ConfigCheckEnum {

    MAIN_DB("main","主数据库"),
    STATIS_DB("statis","统计服务数据库"),
    UAA_DB("webrtc","UAA服务数据库"),
    SURV_DB("surv","监控服务数据库"),
    EDU_DB("edu","教育服务数据库"),
    MATRIX_DB("matrix","Matrix服务数据库"),
    SDK_TOKEN("sdk-token", "云视讯API-token"),
    MAIL_SERVER("mail-server", "邮件服务器"),
    ;


    private String type;
    private String typeDescription;

    ConfigCheckEnum(String type, String typeDescription) {
        this.type = type;
        this.typeDescription = typeDescription;
    }

    public String getType() {
        return type;
    }

    public String getTypeDescription() {
        return typeDescription;
    }

}
