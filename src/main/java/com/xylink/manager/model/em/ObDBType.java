package com.xylink.manager.model.em;

import com.xylink.config.NetworkConstants;

/**
 * <AUTHOR>
 * @date 2022/12/06/11:15
 */
public enum ObDBType {
    main(NetworkConstants.DATABASE_IP),
    ;

    private final String allIpKey;

    ObDBType(String allIpKey) {
        this.allIpKey = allIpKey;
    }

    public String getAllIpKey() {
        return allIpKey;
    }

    public static boolean containsName(String name) {
        for (ObDBType obDBType : ObDBType.values()) {
            if (obDBType.name().equalsIgnoreCase(name)) {
                return true;
            }
        }
        return false;
    }

}
