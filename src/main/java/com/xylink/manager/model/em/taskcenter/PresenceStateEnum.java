package com.xylink.manager.model.em.taskcenter;

import lombok.Getter;

/**
 * <AUTHOR> create on 2023/12/14
 */
@Getter
public enum PresenceStateEnum {
    /**
     * 空闲
     */
    IDLE("IDLE", "在线", 1),

    /**
     * 呼叫中
     */
    INCALL("INCALL", "呼叫中", 2),

    /**
     * 离线
     */
    OFFLINE("OFFLINE", "离线", 0);
    private final String state;

    private final String stateDesc;

    private final int code;


    PresenceStateEnum(String state, String stateDesc, int code) {
        this.state = state;
        this.stateDesc = stateDesc;
        this.code = code;
    }
}
