package com.xylink.manager.model.em.taskcenter;

import com.xylink.config.exception.basic.ServerException;
import lombok.Getter;

/**
 * <AUTHOR> create on 2023/12/11
 */
@Getter
public enum DeviceScheduleTaskRepeatEnum {
    /**
     * 单次
     */
    SINGLE("SINGLE"),
    /**
     * 每日
     */
    DAILY("DAILY"),
    /**
     * 每周
     */
    WEEKLY("WEEKLY"),
    /**
     * 每月
     */
    MONTHLY("MONTHLY");

    private final String repeatType;

    DeviceScheduleTaskRepeatEnum(String repeatType) {
        this.repeatType = repeatType;
    }

    public static DeviceScheduleTaskRepeatEnum match(String repeatType) {
        for (DeviceScheduleTaskRepeatEnum repeatEnum : DeviceScheduleTaskRepeatEnum.values()) {
            if (repeatEnum.repeatType.equals(repeatType)) {
                return repeatEnum;
            }
        }
        throw new ServerException("Illegal deviceScheduleTask repeatType:" + repeatType);
    }

    public static void check(String repeatType) {
        for (DeviceScheduleTaskRepeatEnum repeatEnum : DeviceScheduleTaskRepeatEnum.values()) {
            if (repeatEnum.repeatType.equals(repeatType)) {
                return;
            }
        }
        throw new ServerException("Illegal deviceScheduleTask repeatType:" + repeatType);
    }
}
