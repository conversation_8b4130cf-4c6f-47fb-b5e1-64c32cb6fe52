package com.xylink.manager.model.em.taskcenter;

import com.xylink.config.exception.basic.ServerException;
import lombok.Getter;

/**
 * <AUTHOR> create on 2023/12/11
 */
@Getter
public enum DeviceScheduleTypeEnum {
    /**
     * 所有终端
     */
    ALL_DEVICE("ALL_DEVICE"),
    /**
     * 部分终端
     */
    PART_DEVICE("PART_DEVICE"),
    /**
     * 部分型号终端
     */
    PART_MODEL_DEVICE("PART_MODEL_DEVICE");

    private final String deviceType;

    DeviceScheduleTypeEnum(String deviceType) {
        this.deviceType = deviceType;
    }

    public static void check(String deviceType) {
        for (DeviceScheduleTypeEnum typeEnum : DeviceScheduleTypeEnum.values()) {
            if (typeEnum.deviceType.equals(deviceType)) {
                return;
            }
        }
        throw new ServerException("Illegal deviceType");
    }

    public static DeviceScheduleTypeEnum match(String deviceType) {
        for (DeviceScheduleTypeEnum typeEnum : DeviceScheduleTypeEnum.values()) {
            if (typeEnum.deviceType.equals(deviceType)) {
                return typeEnum;
            }
        }
        throw new ServerException("Illegal deviceType");
    }
}
