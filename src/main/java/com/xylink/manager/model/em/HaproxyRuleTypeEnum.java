package com.xylink.manager.model.em;

import com.xylink.config.HaproxyConstants;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum HaproxyRuleTypeEnum {
    main_http("main_http"),
    main_https("main_https"),
    vod_http("vod_http"),
    vod_https("vod_https"),
    webrtc_http("webrtc_http"),
    webrtc_https("webrtc_https"),
    srs_live_soft("srs_live_soft"),
    srs_live_hard("srs_live_hard"),
    h323_client("h323_client"),
    h323_range("h323_range"),
    kafka_rule("kafka_rule"),
    ippbx_rule("ippbx_rule"),
    vod_network1_proxy("vod_network1_proxy"),
    vod_network2_proxy("vod_network2_proxy"),
    vod_network3_proxy("vod_network3_proxy"),
    vod_network4_proxy("vod_network4_proxy"),
    vod_network5_proxy("vod_network5_proxy"),
    vod_network6_proxy("vod_network6_proxy"),
    cdn_http("cdn_http"),
    cdn_https("cdn_https"),
    database_rule("database_rule"),
    internal_nginx_rule("internal_nginx_rule"),
    console_manager_http("console_manager_http"),
    console_manager_https("console_manager_https"),
    ams_http("ams_http"),
    ams_https("ams_https"),
    fusion_mms_http("fusion_mms_http"),
    fusion_mms_https("fusion_mms_https"),
    ;

    private String name;
    /**
     * 用于拼接all-haproxy中的key
     */
    private String basicMasterKeyPrefix;
    private String basicSlaveKeyPrefix;

    HaproxyRuleTypeEnum(String name) {
        this.name = name;
        int index = name.lastIndexOf("_");
        String typeNamePrefix = name.substring(0, index).toUpperCase().replaceAll("_","-");
        String typeNameSuffix = name.substring(index + 1).toUpperCase();
        this.basicMasterKeyPrefix = typeNamePrefix + "-" + HaproxyConstants.MASTER + "-" + typeNameSuffix;
        this.basicSlaveKeyPrefix = typeNamePrefix + "-" +HaproxyConstants.SLAVE + "-" + typeNameSuffix;
    }

    public String getName() {
        return name;
    }

    public String backendMasterKeyPrefix(String nodeName){
        return nodeName + "-" + basicMasterKeyPrefix + "-";
    }

    public String backendSlaveKeyPrefix(String nodeName){
        return nodeName + "-" + basicSlaveKeyPrefix + "-";
    }

    public String frontendKeyPrefix(String nodeName){
        return nodeName + "-" + HaproxyConstants.HAPROXY + "-" + name.replaceAll("_","-").toUpperCase() + "-";
    }

    public String modeKeyPrefix(String nodeName){
        return frontendKeyPrefix(nodeName);
    }

    public static List<String> getAllRuleType(){
        return Arrays.stream(HaproxyRuleTypeEnum.values()).map(HaproxyRuleTypeEnum::getName).collect(Collectors.toList());
    }

    public static List<String> getRemoveKey(String nodeName,String nodeType){
        List<String> keyList = new ArrayList<>();
        HaproxyRuleTypeEnum haproxyRuleTypeEnum = HaproxyRuleTypeEnum.valueOf(nodeType);
        keyList.add(haproxyRuleTypeEnum.frontendKeyPrefix(nodeName) + HaproxyConstants.PORT);
        keyList.add(haproxyRuleTypeEnum.backendMasterKeyPrefix(nodeName) + HaproxyConstants.IP);
        keyList.add(haproxyRuleTypeEnum.backendMasterKeyPrefix(nodeName) + HaproxyConstants.PORT);
        keyList.add(haproxyRuleTypeEnum.backendSlaveKeyPrefix(nodeName) + HaproxyConstants.IP);
        keyList.add(haproxyRuleTypeEnum.backendSlaveKeyPrefix(nodeName) + HaproxyConstants.PORT);
        return keyList;
    }

}
