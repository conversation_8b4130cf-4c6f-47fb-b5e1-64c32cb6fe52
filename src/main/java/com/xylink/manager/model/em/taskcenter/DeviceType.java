package com.xylink.manager.model.em.taskcenter;

/**
 * This class should be compatible to com.ainemo.protocol.Device
 */
public enum DeviceType {
    SOFT(1), HARD(2), BROWSER(3), DESKTOP(5), GW_H323(6), BRUCE(7), TVBOX(8);

    private final int value;

    DeviceType(int type) {
        this.value = type;
    }

    public static DeviceType valueOf(int valueInt) {
        for (DeviceType type : DeviceType.values()) {
            if (type.value == valueInt)
                return type;
        }
        return null;
    }

    public static boolean isHardDevice(int valueInt) {
        return valueInt == TVBOX.value || valueInt == HARD.value;
    }

    public int getValue() {
        return value;
    }

    public boolean isHardDevice() {
        return value == TVBOX.value || value == HARD.value;
    }

}
