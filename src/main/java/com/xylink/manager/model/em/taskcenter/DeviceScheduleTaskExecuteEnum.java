package com.xylink.manager.model.em.taskcenter;

import com.xylink.config.exception.basic.ServerException;
import lombok.Getter;

/**
 * <AUTHOR> create on 2023/12/11
 */
@Getter
public enum DeviceScheduleTaskExecuteEnum {
    /**
     * 重启
     */
    REBOOT("REBOOT"),
    /**
     * 升级
     */
    UPGRADE("UPGRADE"),
    /**
     * 关机
     */
    SHUTDOWN("SHUTDOWN"),
    /**
     * 恢复出厂设置
     */
    RECOVERY("RECOVERY");

    private final String executeType;

    DeviceScheduleTaskExecuteEnum(String executeType) {
        this.executeType = executeType;
    }

    public static void check(String executeType) {
        for (DeviceScheduleTaskExecuteEnum executeEnum : DeviceScheduleTaskExecuteEnum.values()) {
            if (executeEnum.executeType.equals(executeType)) {
                return;
            }
        }
        throw new ServerException("Illegal executeType:" + executeType);
    }

    public static DeviceScheduleTaskExecuteEnum match(String executeType) {
        for (DeviceScheduleTaskExecuteEnum executeEnum : DeviceScheduleTaskExecuteEnum.values()) {
            if (executeEnum.executeType.equals(executeType)) {
                return executeEnum;
            }
        }
        throw new ServerException("Illegal executeType:" + executeType);
    }
}
