package com.xylink.manager.model.em;

public enum ClientPlatformEnum {
    TP650_V1("TP650_V1","charlie"),
    TP650_V2("TP650_V2","charliev2"),
    TP650_CAMERA("TP650_CAMERA","charliecamera"),
    TP650_MIC("TP650_MIC","charliemic"),
    TP860("TP860","tad"),
    TP860_CAMERA("TP860_CAMERA","tadcamera"),
    TP860_MIC("TP860_MIC","tadmic"),
    SW_SV30("SW_SV30","sw_sv30"),
    SW_SV31("SW_SV31","sw_sv31"),
    HKVERSION("HKVERSION","hkversion"),
    HQCLOUD("HQCLOUD","hqcloud"),
    HEGANG("HEGANG","hegang"),
    TP860_X_OS("TP860_X_OS","tp860_x_os"),
    ;

    private String clientName;
    private String platform;

    ClientPlatformEnum(String clientName, String platform) {
        this.clientName = clientName;
        this.platform = platform;
    }

    public String getClientName() {
        return clientName;
    }

    public String getPlatform() {
        return platform;
    }
}
