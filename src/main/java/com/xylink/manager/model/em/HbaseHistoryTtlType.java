package com.xylink.manager.model.em;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/07/21/10:54
 */
public enum HbaseHistoryTtlType {
    /**
     * 默认,什么类型也不设置
     */
    defaults("DEFAULT"),
    /**
     * 直播
     */
    live("LIVE"),
    /**
     * 质量
     */
    quality("QUALITY"),
    /**
     * 会议
     */
    meeting("MEETING"),
    /**
     * 会议日志
     */
    meeting_log("MEETING_LOG")
    ;

    private final String type;

    HbaseHistoryTtlType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public static HbaseHistoryTtlType values(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        for (HbaseHistoryTtlType hbaseHistoryType : HbaseHistoryTtlType.values()) {
            if (hbaseHistoryType.type.equals(type)) {
                return hbaseHistoryType;
            }
        }
        return null;
    }
}
