package com.xylink.manager.model.em.taskcenter;


import lombok.Data;

@Data
public class Notification<T> {
    private int subType;
    private T content;

    public Notification(int subType, T content) {
        this.subType = subType;
        this.content = content;
    }


    public static enum NotificationType {
        /**
         * 呼叫
         */
        CALL(1),
        UPLOAD_LOG(2),
        /**
         * 重启
         */
        REBOOT(4),
        /**
         * 恢复
         */
        RECOVERY(8),
        PUSHMESSAGE(10),
        NETTEST(12),
        CHECKFEATURE(14),
        PROMOTIONACTIVITY(15),
        NEMO_CONFIG_CHANGE(16),
        UPLOAD_FULL_LOG(18),
        OPERATION_ACTIVITY(19),
        /**
         * 升级
         */
        UPGRADE_NEMO(20),
        INVITE_CALL(22),
        NEMO_COMMAND(24),
        MEETING_REMINER(26),
        START_RECORD(28),
        STOP_RECORD(30),
        START_CONTENT(32),
        STOP_CONTENT(34),
        SUB_TITLE(35),
        CANCEL_HANGUP(36),
        <PERSON><PERSON><PERSON>_ENTERPRISE(37),
        USER_MSG_NOTIFICATION(38),
        <PERSON>ANGUP(39),
        STATUS_WX_DEVIDE(40),
        STATUS_AUTH_DEVIDE(41),
        CAMERA_CHANGE(42),
        LAYOUT_LIST(43),
        IM_PERMISSION(44),
        DEVICE_STATE_REQUEST(45),
        LOCK_MEETING(46),
        LANGUAGES(47),
        TRANSLATOR(48),
        DEVICE_CONTENT(49),
        HOST(52),
        COCHAIR(53),
        RENAME(54),
        LANGUAGE_CONFIG(55),
        /**
         * 关机
         */
        SHUT_DOWN(56);

        private final int type;

        NotificationType(int type) {
            this.type = type;
        }

        public int getType() {
            return this.type;
        }
    }
}