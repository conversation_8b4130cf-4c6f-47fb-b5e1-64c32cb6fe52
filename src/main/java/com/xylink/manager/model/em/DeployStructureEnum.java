package com.xylink.manager.model.em;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public enum DeployStructureEnum {

    main(new String[]{
            Labels.openresty_main.label(),
            Labels.redis.label(),
            Labels.mms.label(),
            Labels.mc.label(),
            Labels.mc_proxy.label(),
            Labels.access.label(),
            Labels.accesssig.label(),
            Labels.proxysig.label(),
            Labels.iauth.label(),
            Labels.captcha.label(),
            Labels.userpermission.label(),
            Labels.callpermission.label(),
            Labels.pivotor.label(),
            Labels.clientconfig.label(),
            Labels.css.label(),
            Labels.contact.label(),
            Labels.contact_schedule.label(),
            Labels.dmcu.label(),
            Labels.uss.label(),
            Labels.charge.label(),
            Labels.bill.label(),
            Labels.charge_dispatcher.label(),
            Labels.signal.label(),
            Labels.sharing.label(),
            Labels.dating.label(),
            Labels.im.label(),
            Labels.vcs.label(),
            Labels.sitecode.label(),
            Labels.externalweb.label(),
            Labels.meetingmonitor.label(),
            Labels.tsa.label(),
            Labels.tsa_mp.label(),
            Labels.txlive.label(),
            Labels.inspection.label(),
            Labels.basicinfo.label(),
            Labels.presence.label(),
            Labels.sdkcallback.label(),
            Labels.cloud_meetingroom.label(),
            Labels.ocean.label(),
            Labels.vote.label(),
            Labels.vote_statistics.label(),
            Labels.nettool.label(),
            Labels.thirdadapter.label(),
            Labels.sensitiveword.label(),
            Labels.logserver.label(),
            Labels.liveness_probe.label(),
            Labels.basic_management.label(),
            Labels.message_push.label(),
            Labels.sms.label(),
            Labels.allocator_server.label(),
            Labels.basic_xyauth.label(),
            Labels.buffet.label(),
            Labels.page.label(),
            Labels.msg_server.label(),
            Labels.frontend.label(),
            Labels.frontend_buffet.label(),
            Labels.frontend_meeting.label(),
            Labels.frontend_meetingschedule.label(),
            Labels.frontend_pcclient.label(),
            Labels.mcaccess.label(),
            Labels.avcloudapi.label(),
            Labels.nightalarm.label(),
            Labels.nightingale_categraf.label(),
    }),

    main_partner(new String[]{
            Labels.redis.label(),
            Labels.mms.label(),
            Labels.mc.label(),
            Labels.mc_proxy.label(),
            Labels.access.label(),
            Labels.accesssig.label(),
            Labels.proxysig.label(),
            Labels.iauth.label(),
            Labels.captcha.label(),
            Labels.userpermission.label(),
            Labels.callpermission.label(),
            Labels.pivotor.label(),
            Labels.clientconfig.label(),
            Labels.css.label(),
            Labels.contact.label(),
            Labels.contact_schedule.label(),
            Labels.charge.label(),
            Labels.bill.label(),
            Labels.charge_dispatcher.label(),
            Labels.dmcu.label(),
            Labels.uss.label(),
            Labels.signal.label(),
            Labels.sharing.label(),
            Labels.dating.label(),
            Labels.im.label(),
            Labels.vcs.label(),
            Labels.sitecode.label(),
            Labels.externalweb.label(),
            Labels.meetingmonitor.label(),
            Labels.tsa.label(),
            Labels.tsa_mp.label(),
            Labels.txlive.label(),
            Labels.ocean.label(),
            Labels.inspection.label(),
            Labels.basicinfo.label(),
            Labels.presence.label(),
            Labels.vote.label(),
            Labels.vote_statistics.label(),
            Labels.sdkcallback.label(),
            Labels.cloud_meetingroom.label(),
            Labels.nettool.label(),
            Labels.thirdadapter.label(),
            Labels.sensitiveword.label(),
            Labels.logserver.label(),
            Labels.liveness_probe.label(),
            Labels.basic_management.label(),
            Labels.message_push.label(),
            Labels.sms.label(),
            Labels.allocator_server.label(),
            Labels.basic_xyauth.label(),
            Labels.push.label(),
            Labels.push_proxy.label(),
            Labels.nightingale.label(),
            Labels.nightingale_categraf_new.label(),
            Labels.kube_state_metrics.label(),
            Labels.mcaccess.label(),
            Labels.avcloudapi.label(),
            Labels.aibusiness.label(),
            Labels.aiagent.label(),
            Labels.nightalarm.label(),
            Labels.jaeger.label(),
            Labels.nightingale_categraf.label(),
    }),
    vod_base(new String[]{
            Labels.openresty_vod.label(),
            Labels.record.label(),
            Labels.rmserver.label(),
            Labels.live.label(),
            Labels.srs.label(),
            Labels.vodclustermgr.label(),
            Labels.business_download_service.label(),
            Labels.live_audience.label(),
            Labels.vod_share.label(),
            Labels.vod.label(),
            Labels.vod_edit.label(),
            Labels.nodelive.label(),
            Labels.vod_fileserver.label(),
            Labels.vodfilemanager.label(),
            Labels.vodmanager.label(),
            Labels.mcaccess.label(),
            Labels.avcloudapi.label(),
            Labels.vodnetwork_vod.label(),
            Labels.vodnetwork_vodedit.label(),
            Labels.vodnetwork_proxy.label(),
            Labels.nightingale_categraf.label(),
    }),

    vod(new String[]{
            Labels.openresty_vod.label(),
            Labels.record.label(),
            Labels.rmserver.label(),
            Labels.live.label(),
            Labels.live_audience.label(),
            Labels.srs.label(),
            Labels.vodclustermgr.label(),
            Labels.business_download_service.label(),
            Labels.nodelive.label(),
            Labels.vod_fileserver.label(),
            Labels.vodfilemanager.label(),
            Labels.vodmanager.label(),
            Labels.vod_share.label(),
            Labels.vod_edit.label(),
            Labels.vod.label(),
            Labels.mcaccess.label(),
            Labels.avcloudapi.label(),
            Labels.vodnetwork_vod.label(),
            Labels.vodnetwork_vodedit.label(),
            Labels.vodnetwork_proxy.label(),
            Labels.nightingale_categraf.label(),
    }),

    srs_extends(new String[]{
            Labels.srs_extends.label(),
            Labels.srs_extends_proxy.label(),
            Labels.nightingale_categraf.label(),
    }),

    nfs(new String[]{
            Labels.nightingale_categraf.label(),
    }),

    mysql(new String[]{
            Labels.mysql.label(),
            Labels.zookeeper.label(),
            Labels.kafka.label(),
            Labels.mongodb.label(),
            Labels.canal.label(),
            Labels.es.label(),
            Labels.dm.label(),
            Labels.nightingale_categraf.label(),
    }),

    mysqlslave(new String[]{
            Labels.mysql_slave.label(),
            Labels.zookeeper.label(),
            Labels.kafka.label(),
            Labels.mongodb.label(),
            Labels.canal.label(),
            Labels.es.label(),
            Labels.nightingale_categraf.label(),
    }),

    statis(new String[]{
            Labels.statis_quality.label(),
            Labels.statis_monitor.label(),
            Labels.statis_meeting.label(),
            Labels.statis_education.label(),
            Labels.statis_live.label(),
            Labels.hbase.label(),
            Labels.clickhouse.label(),//clickhouse是教培才部署，现在如果上了hadoop就不需要了，没上hadoop的教培还是要部署
            Labels.dcm.label(),
            Labels.data_transfer_server.label(),
            Labels.nightingale_categraf.label(),
    }),
    meeting_quality(new String[]{
            Labels.statis_quality.label(),
            Labels.nightingale_categraf.label(),
    }),


    dmcu(new String[]{
            Labels.dmcu.label(),
            Labels.nettool.label(),
            Labels.ipip_proxy.label(),
            Labels.nightingale_categraf.label(),
    }),

    dmcu_arm(new String[]{
            Labels.dmcu_arm.label(),
            Labels.nettool_arm.label(),
            Labels.nightingale_categraf.label(),
    }),

    surv(new String[]{
            Labels.gather_mc.label(),
            Labels.surv_access.label(),
            Labels.survbiz.label(),
            Labels.survsig.label(),
            Labels.dmcu.label(),
            Labels.nightingale_categraf.label(),
    }),

    edu(new String[]{
            Labels.edu_adapter.label(),
            Labels.edu_manage.label(),
            Labels.edu_dating.label(),
            Labels.edu_resource.label(),
            Labels.education.label(),
            Labels.examination.label(),
            Labels.file_manage.label(),
            Labels.file_manage_ud.label(),
            Labels.edu_vodshare.label(),
            Labels.nightingale_categraf.label(),
    }),

    edu_file(new String[]{
            Labels.openresty_edu_file.label(),
            Labels.file_manage_ud.label(),
            Labels.edu_vodshare.label(),
            Labels.nightingale_categraf.label(),
    }),

    edu_gw(new String[]{
            Labels.edu_hysiggw.label(),
            Labels.edu_hymediagw.label(),
            Labels.edu_1nsiggw.label(),
            Labels.edu_1nmediagw.label(),
            Labels.edu_1nsiggw_mgr.label(),
            Labels.edu_1nsiggw_outproxy.label(),
            Labels.edu_1nmediagw_outproxy.label(),
            Labels.nightingale_categraf.label(),
    }),

    matrix(new String[]{
            Labels.matrix_alg.label(),
            Labels.nightingale_categraf.label(),
    }),

    matrix_transcript(new String[]{
            Labels.matrix.label(),
            Labels.matrix_alg.label(),
            Labels.transcription.label(),
            Labels.dmcu.label(),
            Labels.aiagent.label(),
            Labels.aibusiness.label(),
            Labels.asralg.label(),
            Labels.asralg2.label(),
            Labels.nightingale_categraf.label(),
    }),

    ippbx(new String[]{
            Labels.main_proxy.label(),
            Labels.ippbx_siggw.label(),
            Labels.ippbx_mediagw.label(),
            Labels.nightingale_categraf.label(),
    }),


    h323(new String[]{
            Labels.h323_sig.label(),
            Labels.h323_media.label(),
            Labels.sip_server.label(),
            Labels.sip_media.label(),
            Labels.nightingale_categraf.label(),
    }),

    h323_arm(new String[]{
            Labels.h323_sig_arm.label(),
            Labels.h323_media_arm.label(),
            Labels.nightingale_categraf.label(),
    }),

    avc(new String[]{
            Labels.avc_media.label(),
            Labels.avc_h323.label(),
            Labels.avc_sip.label(),
            Labels.nightingale_categraf.label(),
    }),

    avc_arm(new String[]{
            Labels.avc_media_arm.label(),
            Labels.avc_h323_arm.label(),
            Labels.avc_sip_arm.label(),
            Labels.nightingale_categraf.label(),
    }),

    sip(new String[]{
            Labels.h323_sig.label(),
            Labels.h323_media.label(),
            Labels.sip_server.label(),
            Labels.sip_media.label(),
            Labels.nightingale_categraf.label(),
    }),


    redis(new String[]{
            Labels.redis.label(),
            Labels.nightingale_categraf.label(),
    }),
    charge(new String[]{
            Labels.charge.label(),
            Labels.bill.label(),
            Labels.charge_redis.label(),
            Labels.charge_dispatcher.label(),
            Labels.callpermission.label(),
            Labels.nightingale_categraf.label(),
    }),

    webrtc(new String[]{
            Labels.webrtc_proxy.label(),
            Labels.openresty_webrtc.label(),
            Labels.webrtc_siggw.label(),
            Labels.webrtc_mediagw.label(),
            Labels.allocator_server.label(),
            Labels.msg_server.label(),
            Labels.nightingale_categraf.label(),
    }),

    webrtc_arm(new String[]{
            Labels.webrtc_proxy_arm.label(),
            Labels.openresty_webrtc_arm.label(),
            Labels.webrtc_siggw_arm.label(),
            Labels.webrtc_mediagw_arm.label(),
            Labels.nightingale_categraf.label(),
    }),

    uaa(new String[]{
            Labels.uaa_base.label(),
            Labels.uaa_api.label(),
            Labels.uaa_mysql.label(),
            Labels.uaa_mysqlslave.label(),
            Labels.nightingale_categraf.label(),
    }),

    kafka(new String[]{
            Labels.kafka.label(),
            Labels.nightingale_categraf.label(),
    }),

    logserver(new String[]{
            Labels.logserver.label(),
            Labels.nightingale_categraf.label(),
    }),

    transcript(new String[]{
            Labels.transcription.label(),
            Labels.dmcu.label(),
            Labels.aiagent.label(),
            Labels.aibusiness.label(),
            Labels.asralg.label(),
            Labels.asralg2.label(),
            Labels.aistorage.label(),
            Labels.nightingale_categraf.label(),
    }),

    hadoop(new String[]{
            Labels.azkaban.label(),
            Labels.yarn.label(),
            Labels.hadoop_db.label(),
            Labels.nightingale_categraf.label(),
    }),

    haproxy(new String[]{
            Labels.haproxy.label(),
            Labels.nightingale_categraf.label(),
    }),

    haproxy_arm(new String[]{
            Labels.haproxy_arm.label(),
            Labels.nightingale_categraf.label(),
    }),

    dmcu_side(new String[]{
            Labels.dmcu_side.label(),
            Labels.nettool.label(),
            Labels.nightingale_categraf.label(),
    }),

    hls(new String[]{
            Labels.hls.label(),
            Labels.hls_proxy.label(),
            Labels.nightingale_categraf.label(),
    }),

    vodnetwork(new String[]{
            Labels.vodnetwork_vod.label(),
            Labels.vodnetwork_vodedit.label(),
            Labels.vodnetwork_proxy.label(),
            Labels.nightingale_categraf.label(),
    }),

    vodnetwork_arm(new String[]{
            Labels.vodnetwork_vod_arm.label(),
            Labels.vodnetwork_vodedit_arm.label(),
            Labels.vodnetwork_proxy_arm.label(),
            Labels.nightingale_categraf.label(),
    }),

    rtmp(new String[]{
            Labels.converged_mediagw.label(),
            Labels.converged_siggw.label(),
            Labels.nightingale_categraf.label(),
    }),
    sdk_file(new String[]{
            Labels.nightingale_categraf.label(),
    }),

    main_proxy(new String[]{
            Labels.main_proxy.label(),
            Labels.nettool.label(),
            Labels.nightingale_categraf.label(),
    }),

    main_proxy_arm(new String[]{
            Labels.main_proxy_arm.label(),
            Labels.nettool_arm.label(),
            Labels.nightingale_categraf.label(),
    }),

    nmsa(new String[]{
            Labels.nmsa.label(),
            Labels.nmsa_proxy.label(),
            Labels.nightingale_categraf.label(),
    }),

    txrest(new String[]{
            Labels.txrest.label(),
            Labels.wxrtc_proxy.label(),
            Labels.nightingale_categraf.label(),
    }),

    cascade(new String[]{
            Labels.roster.label(),
            Labels.cascadegw.label(),
            Labels.cascademgr.label(),
            Labels.avstatusserver.label(),
            Labels.nightingale_categraf.label(),
    }),

    middleware(new String[]{
            Labels.redis.label(),
            Labels.redis_sentinel.label(),
            Labels.kafka_cluster.label(),
            Labels.zookeeper_cluster.label(),
            Labels.nightingale_categraf.label(),
    }),

    call_base(new String[]{
            Labels.openresty_internal.label(),
            Labels.mms.label(),
            Labels.nettool.label(),
            Labels.sharing.label(),
            Labels.meetingmonitor.label(),
            Labels.sitecode.label(),
            Labels.cloud_meetingroom.label(),
            Labels.tsa.label(),
            Labels.access.label(),
            Labels.sdkcallback.label(),
            Labels.allocator_server.label(),
            Labels.mc.label(),
            Labels.mc_proxy.label(),
            Labels.uaa_base.label(),
            Labels.uaa_api.label(),
            Labels.uaa_mysql.label(),
            Labels.uaa_mysqlslave.label(),
            Labels.nightingale_categraf.label(),
    }),

    call_sig(new String[]{
            //from call_base
            Labels.openresty_internal.label(),
            Labels.mms.label(),
            Labels.nettool.label(),
            Labels.sharing.label(),
            Labels.meetingmonitor.label(),
            Labels.sitecode.label(),
            Labels.cloud_meetingroom.label(),
            Labels.tsa.label(),
            Labels.access.label(),
            Labels.sdkcallback.label(),
            Labels.allocator_server.label(),
            //from signal_mc
            Labels.accesssig.label(),
            Labels.proxysig.label(),
            Labels.signal.label(),
            Labels.mc.label(),
            Labels.mc_proxy.label(),
            Labels.uaa_base.label(),
            Labels.uaa_api.label(),
            Labels.uaa_mysql.label(),
            Labels.uaa_mysqlslave.label(),
            Labels.nightingale_categraf.label(),
    }),

    charge_base(new String[]{
            Labels.captcha.label(),
            Labels.iauth.label(),
            Labels.userpermission.label(),
            Labels.pivotor.label(),
            Labels.clientconfig.label(),
            Labels.css.label(),
            Labels.uss.label(),
            Labels.externalweb.label(),
            Labels.presence.label(),
            Labels.logserver.label(),
            Labels.charge.label(),
            Labels.charge_dispatcher.label(),
            Labels.callpermission.label(),
            Labels.bill.label(),
            Labels.msg_server.label(),
            Labels.basic_xyauth.label(),
            Labels.nightingale_categraf.label(),
    }),

    basic_base(new String[]{
            Labels.contact.label(),
            Labels.contact_schedule.label(),
            Labels.dating.label(),
            Labels.im.label(),
            Labels.vcs.label(),
            Labels.inspection.label(),
            Labels.basicinfo.label(),
            Labels.ocean.label(),
            Labels.vote.label(),
            Labels.vote_statistics.label(),
            Labels.basic_management.label(),
            Labels.message_push.label(),
            Labels.buffet.label(),
            Labels.page.label(),
            Labels.frontend_pcclient.label(),
            Labels.frontend_meetingschedule.label(),
            Labels.frontend_meeting.label(),
            Labels.frontend_buffet.label(),
            Labels.frontend.label(),
            Labels.sms.label(),
            Labels.thirdadapter.label(),
            Labels.mcaccess.label(),
            Labels.avcloudapi.label(),
            Labels.aibusiness.label(),
            Labels.aiagent.label(),
            Labels.nightingale_categraf.label(),
    }),

    basic_charge(new String[]{
            Labels.captcha.label(),
            Labels.iauth.label(),
            Labels.userpermission.label(),
            Labels.pivotor.label(),
            Labels.clientconfig.label(),
            Labels.css.label(),
            Labels.uss.label(),
            Labels.externalweb.label(),
            Labels.presence.label(),
            Labels.logserver.label(),
            Labels.charge.label(),
            Labels.charge_dispatcher.label(),
            Labels.callpermission.label(),
            Labels.bill.label(),
            Labels.msg_server.label(),
            Labels.basic_xyauth.label(),
            Labels.contact.label(),
            Labels.contact_schedule.label(),
            Labels.dating.label(),
            Labels.im.label(),
            Labels.vcs.label(),
            Labels.inspection.label(),
            Labels.basicinfo.label(),
            Labels.ocean.label(),
            Labels.vote.label(),
            Labels.vote_statistics.label(),
            Labels.basic_management.label(),
            Labels.message_push.label(),
            Labels.buffet.label(),
            Labels.page.label(),
            Labels.frontend_pcclient.label(),
            Labels.frontend_meetingschedule.label(),
            Labels.frontend_meeting.label(),
            Labels.frontend_buffet.label(),
            Labels.frontend.label(),
            Labels.dmcu.label(),
            Labels.nettool.label(),
            Labels.sms.label(),
            Labels.mcaccess.label(),
            Labels.avcloudapi.label(),
            Labels.aibusiness.label(),
            Labels.aiagent.label(),
            Labels.nightingale_categraf.label(),
    }),

    signal_mc(new String[]{
            Labels.accesssig.label(),
            Labels.proxysig.label(),
            Labels.signal.label(),
            Labels.mc.label(),
            Labels.mc_proxy.label(),
            Labels.nightingale_categraf.label(),
    }),
    statis_base(new String[]{
            Labels.datafact.label(),
            Labels.dcs.label(),
            Labels.des.label(),
            Labels.hadoop_node.label(),
            Labels.nightingale_categraf.label(),
    }),

    hadoop_master(new String[]{
            Labels.hadoop_zookeeper_cluster.label(),
            Labels.hadoop_master.label(),
            Labels.hadoop_node.label(),
            Labels.hadoop_cluster.label(),
            Labels.nightingale_categraf.label(),
    }),

    hadoop_single(new String[]{
            Labels.hadoop_zookeeper_cluster.label(),
            Labels.hadoop_single.label(),
            Labels.hadoop_node.label(),
            Labels.hadoop_cluster.label()
    }),

    hadoop_node(new String[]{
            Labels.hadoop_node.label(),
            Labels.nightingale_categraf.label(),
    }),

    mysql_ha(new String[]{
            Labels.mysql.label(),
            Labels.mysql_slave.label(),
            Labels.mongodb.label(),
            Labels.canal.label(),
            Labels.es.label(),
            Labels.dm.label(),
            Labels.nightingale_categraf.label(),
    }),

    vodnetwork_vod(new String[]{
            Labels.vodnetwork_vod.label(),
            Labels.vodnetwork_vodedit.label(),
            Labels.nightingale_categraf.label(),
    }),

    webrtc_siggw(new String[]{
            Labels.allocator_server.label(),
            Labels.webrtc_siggw.label(),
            Labels.openresty_webrtc.label(),
            Labels.nightingale_categraf.label(),
    }),

    avc_siggw(new String[]{
            Labels.h323_sig.label(),
            Labels.sip_server.label(),
            Labels.nightingale_categraf.label(),
    }),

    avc_mediagw(new String[]{
            Labels.h323_media.label(),
            Labels.nightingale_categraf.label(),
    }),

    ippbx_siggateway(new String[]{
            Labels.ippbx_siggw.label(),
            Labels.access.label(),
            Labels.nightingale_categraf.label(),
    }),

    ippbx_mediagateway(new String[]{
            Labels.ippbx_mediagw.label(),
            Labels.nightingale_categraf.label(),
    }),

    mms(new String[]{
            Labels.mms.label(),
            Labels.basicinfo.label(),
            Labels.meetingmonitor.label(),
            Labels.nightingale_categraf.label(),
    }),
    third_bridge(new String[]{
            Labels.third_bridge.label(),
            Labels.third_bridge_mysql.label(),
            Labels.frontend_third_bridge.label(),
            Labels.nightingale_categraf.label(),
    }),
    third_proxy(new String[]{
            Labels.third_proxy.label(),
            Labels.nightingale_categraf.label(),
    }),
    vod_poc(new String[]{
            Labels.mysql.label(),
            Labels.zookeeper.label(),
            Labels.kafka.label(),
            Labels.mongodb.label(),
            Labels.canal.label(),
            Labels.es.label(),
            Labels.uaa_base.label(),
            Labels.uaa_api.label(),
            Labels.record.label(),
            Labels.rmserver.label(),
            Labels.live.label(),
            Labels.live_audience.label(),
            Labels.srs.label(),
            Labels.vodclustermgr.label(),
            Labels.business_download_service.label(),
            Labels.nodelive.label(),
            Labels.vod_fileserver.label(),
            Labels.vodfilemanager.label(),
            Labels.vodmanager.label(),
            Labels.vod_share.label(),
            Labels.vod_edit.label(),
            Labels.vod.label(),
            Labels.openresty_vod.label(),
            Labels.mcaccess.label(),
            Labels.avcloudapi.label(),
            Labels.nightingale_categraf.label(),
    }),

    fusion_mms(new String[]{
            Labels.openresty_fusion.label(),
            Labels.fusion_mms.label(),
            Labels.fusion_uaa_api.label(),
            Labels.fusion_uaa_admin.label(),
            Labels.fusion_uaa_base.label(),
            Labels.nightingale_categraf.label(),
    }),
    nightingale(new String[]{
            Labels.nightingale.label(),
            Labels.nightingale_categraf_new.label(),
            Labels.nightingale_mid.label(),
            Labels.kube_state_metrics.label(),
            Labels.nightingale_kafka.label(),
            Labels.nightingale_zookeeper.label(),
            Labels.nightingale_categraf.label(),
    }),
    jaeger(new String[]{
            Labels.jaeger.label(),
            Labels.nightingale_categraf.label(),
    }),
    ams_proxy(new String[]{
            Labels.main_proxy.label(),
            Labels.vod_proxy.label(),
            Labels.nettool.label(),
            Labels.nightingale_categraf.label(),
    }),
    cascadegw_base(new String[]{
            Labels.cascadegw.label(),
            Labels.dmcu.label(),
            Labels.nightingale_categraf.label(),
    }),
    proxy_base(new String[]{
            Labels.haproxy.label(),
            Labels.main_proxy.label(),
            Labels.vod_proxy.label(),
            Labels.third_proxy.label(),
            Labels.nettool.label(),
            Labels.nightingale_categraf.label(),
    }),

    database(new String[]{
            Labels.st.label(),
            Labels.dm.label(),
            Labels.kingbase.label(),
            Labels.oceanbase.label(),
            Labels.canal.label(),
            Labels.zookeeper.label(),
            Labels.kafka.label(),
            Labels.mongodb.label(),
            Labels.es.label(),
            Labels.nightingale_categraf.label(),
    }),

    xyai(new String[]{
            Labels.aiagent.label(),
            Labels.aibusiness.label(),
            Labels.aicontroller.label(),
            Labels.aigateway.label(),
            Labels.nightingale_categraf.label(),
    }),

    ;

    private String[] services;

    DeployStructureEnum(String[] services) {
        this.services = services;
    }

    public static List<String> services(String type) {
        DeployStructureEnum deployStructureEnum = null;
        try {
            deployStructureEnum = DeployStructureEnum.valueOf(type.replaceAll("-", "_"));
        } catch (Exception e) {
        }

        if (deployStructureEnum == null) {
            return new ArrayList<String>() {{
                add(type);
            }};
        } else {
            return Arrays.asList(deployStructureEnum.services);
        }
    }
}