package com.xylink.manager.model.em;

/**
 * <AUTHOR>
 * @date 2023/04/17/17:08
 */
public enum InspectItemEnum {
    BIGDATA(1),
    SYSTEM(2),
    MIDDLEWARE(3),
    SERVICE(4),
    ;
    private final int value;

    InspectItemEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static InspectItemEnum getItem(Integer value){
        if (null == value) {
            return null;
        }
        for (InspectItemEnum item : values()) {
            if (value == item.value) {
                return item;
            }
        }
        return null;
    }

}
