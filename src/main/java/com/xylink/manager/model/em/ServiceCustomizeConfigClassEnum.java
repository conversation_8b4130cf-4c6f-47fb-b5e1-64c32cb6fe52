package com.xylink.manager.model.em;

import com.xylink.config.Constants;
import com.xylink.manager.model.cm.*;
import com.xylink.manager.model.cm.customize.OpenrestyFusionCM;
import com.xylink.manager.model.cm.customize.VodFileManagerCM;

public enum ServiceCustomizeConfigClassEnum {

    eduDating(EduDatingCM.class, "all-edu-dating", Labels.edu_dating.label()),

    edu_hysiggw(HySigGatewayCM.class, Constants.CONFIGMAP_EDU_HY_SIGGATEWAY, Labels.edu_hysiggw.label()),

    edu_hymediagw(DmcuCM.class, Constants.CONFIGMAP_EDU_DMCU, Labels.edu_hymediagw.label()),

    edu_1nsiggw(SipplusgatewayCM.class, Constants.CONFIGMAP_EDU_SIPPLUSGATEWAY, Labels.edu_1nsiggw.label()),

    edu_1nmediagw(EduMediagwCM.class, Constants.CONFIGMAP_EDU_MEDIAGW, Labels.edu_1nmediagw.label()),

    nginx(OpenrestyMainCM.class, Constants.CONFIGMAP_OPENRESTY_MAIN, Labels.openresty_main.label()),

    openresty_webrtc(OpenrestyWebrtcCM.class, "all-openresty-webrtc", Labels.openresty_webrtc.label()),

    openresty_webrtc_x86(OpenrestyWebrtcCM.class, "all-openresty-webrtc", Labels.openresty_webrtc_x86.label()),

    openresty_webrtc_arm(OpenrestyWebrtcCM.class, "all-openresty-webrtc", Labels.openresty_webrtc_arm.label()),

    openresty_edu_file(OpenrestyEduFileCM.class, "all-openresty-edu-file", Labels.openresty_edu_file.label()),

    file_manager_ud(FileManageUdCM.class, "all-file-manage-ud", Labels.file_manage_ud.label()),

    ippbxSiggw(IppbxSiggatewayCM.class, Constants.CONFIGMAP_IPPBX_SIGGW, Labels.ippbx_siggw.label()),

    ippbxMediagw(IppbxMediagwCM.class, Constants.CONFIGMAP_IPPBX_MEDIAGW, Labels.ippbx_mediagw.label()),
    dmcu_side(DmcuCM.class, Constants.CONFIGMAP_DMCU, Labels.dmcu_side.label()),
    dmcu_side_x86(DmcuCM.class, Constants.CONFIGMAP_DMCU, Labels.dmcu_side_x86.label()),

    dmcu(DmcuCM.class, Constants.CONFIGMAP_DMCU, Labels.dmcu.label()),
    dmcu_x86(DmcuCM.class, Constants.CONFIGMAP_DMCU, Labels.dmcu_x86.label()),
    dmcu_arm(DmcuCM.class, Constants.CONFIGMAP_DMCU, Labels.dmcu_arm.label()),
    nmsa(NmsaCM.class, Constants.CONFIGMAP_NMSA, Labels.nmsa.label()),
    nmsa_proxy(NmsaProxyCM.class, Constants.CONFIGMAP_NMSA, Labels.nmsa_proxy.label()),
    txlive(TxliveCM.class, Constants.CONFIGMAP_TXLIVE, Labels.txlive.label()),
    wxrtc_proxy(WxrtcProxyCM.class, Constants.CONFIGMAP_TXREST, Labels.wxrtc_proxy.label()),

    nmst(NmstCM.class, Constants.CONFIGMAP_DMCU, Labels.nmst.label()),
    nmst_x86(NmstCM.class, Constants.CONFIGMAP_DMCU, Labels.nmst_x86.label()),
    nmst_arm(NmstCM.class, Constants.CONFIGMAP_DMCU, Labels.nmst_arm.label()),

    hls(HlsCM.class, Constants.CONFIGMAP_HLS, Labels.hls.label()),

    webrtc_mediagw(WebrtcMediagwCM.class, Constants.CONFIGMAP_WEBRTC_MEDIAGW, Labels.webrtc_mediagw.label()),

    cloudmeetingroom(CloudMeetingRoomCM.class, Constants.CONFIGMAP_CLOUDMEETINGROOM, Labels.cloud_meetingroom.label()),

    main_proxy(MainProxyCM.class, Constants.CONFIGMAP_MAIN_PROXY, Labels.main_proxy.label()),
    main_proxy_x86(MainProxyCM.class, Constants.CONFIGMAP_MAIN_PROXY, Labels.main_proxy_x86.label()),
    main_proxy_arm(MainProxyCM.class, Constants.CONFIGMAP_MAIN_PROXY, Labels.main_proxy_arm.label()),

    vod_proxy(VodProxyCM.class, Constants.CONFIGMAP_VOD_PROXY, Labels.vod_proxy.label()),
    vod_proxy_x86(VodProxyCM.class, Constants.CONFIGMAP_VOD_PROXY, Labels.vod_proxy_x86.label()),
    vod_proxy_arm(VodProxyCM.class, Constants.CONFIGMAP_VOD_PROXY, Labels.vod_proxy_arm.label()),
    openresty_vod(OpenrestyVodCM.class, Constants.CONFIGMAP_OPENRESTY_VOD, Labels.openresty_vod.label()),

    cloud_proxy(CloudProxyCM.class, Constants.CONFIGMAP_ALL_CLOUD_PROXY, Labels.cloud_proxy.label()),

    mc(McCm.class, Constants.CONFIGMAP_MC, Labels.mc.label()),

    transcription(TranscriptionCM.class, Constants.CONFIGMAP_TRANSCRIPTION, Labels.transcription.label()),

    live(LiveCM.class, Constants.CONFIGMAP_LIVE, Labels.live.label()),

    webrtc_siggw(WebrtcSiggwCM.class, Constants.CONFIGMAP_WEBRTC_SIGGW, Labels.webrtc_siggw.label()),

    mysqlslave(MysqlSlaveCM.class, Constants.CONFIGMAP_ALL_MYSQLSLAVE, Labels.mysql_slave.label()),

    mysql(MysqlCM.class, Constants.CONFIGMAP_ALL_MYSQL, Labels.mysql.label()),

    uaa_mysqlslave(MysqlSlaveCM.class, Constants.CONFIGMAP_ALL_MYSQLSLAVE, Labels.uaa_mysqlslave.label()),

    uaa_mysql(MysqlCM.class, Constants.CONFIGMAP_ALL_MYSQL, Labels.uaa_mysql.label()),

    dm_cluster(DaMengCM.class, Constants.CONFIGMAP_ALLIP, Labels.dm.label()),

    haproxy(HaproxyCM.class, Constants.CONFIGMAP_ALL_HAPROXY, Labels.haproxy.label()),

    converged_mediagw(ConvergedMediagwCM.class, Constants.CONFIGMAP_ALL_CONVERGED_MEDIAGW, Labels.converged_mediagw.label()),

    sitecode(SitecodeCM.class,Constants.CONFIGMAP_ALLIP,Labels.sitecode.label()),

    hls_proxy(HlsProxyCM.class, Constants.CONFIGMAP_HLS_PROXY, Labels.hls_proxy.label()),

    sip_media(SipMediagwCM.class, Constants.CONFIGMAP_ALL_SIP_MEDIAGW, Labels.sip_media.label()),

    sip_mediagw(SipMediagwCM.class, Constants.CONFIGMAP_ALL_SIP_MEDIAGW, Labels.sip_media.label()),

    vodnetwork_vod(VodnetworkVodCM.class, Constants.CONFIGMAP_VODNETWORK, Labels.vodnetwork_vod.label()),

    vodnetwork_vodedit(VodnetworkVodeditCM.class, Constants.CONFIGMAP_VODNETWORK, Labels.vodnetwork_vodedit.label()),

    vodnetwork_proxy(VodnetworkProxyCM.class, Constants.CONFIGMAP_VODNETWORK_PROXY, Labels.vodnetwork_proxy.label()),

    vodnetwork_vod_arm(VodnetworkVodCM.class, Constants.CONFIGMAP_VODNETWORK, Labels.vodnetwork_vod_arm.label()),

    vodnetwork_vodedit_arm(VodnetworkVodeditCM.class, Constants.CONFIGMAP_VODNETWORK, Labels.vodnetwork_vodedit_arm.label()),

    vodnetwork_proxy_arm(VodnetworkProxyCM.class, Constants.CONFIGMAP_VODNETWORK_PROXY, Labels.vodnetwork_proxy_arm.label()),

    net_tool(NetToolCM.class, Constants.CONFIGMAP_NET_TOOL, Labels.nettool.label()),
    net_tool_arm(NetToolCM.class, Constants.CONFIGMAP_NET_TOOL, Labels.nettool_arm.label()),
    net_tool_x86(NetToolCM.class, Constants.CONFIGMAP_NET_TOOL, Labels.nettool_x86.label()),

    externalweb(ExternalwebCM.class, Constants.CONFIGMAP_ALL_EXTERNALWEB, Labels.externalweb.label()),

    sms(SmsCM.class, Constants.CONFIGMAP_ALL_SMS, Labels.sms.label()),
    zookeeper(ZookeeperCM.class, Constants.CONFIGMAP_ZOOKEEPER, Labels.zookeeper.label()),
    kafka(KafkaCM.class, Constants.CONFIGMAP_KAFKA, Labels.kafka.label()),

    sip_server(SipServerCM.class, Constants.CONFIGMAP_ALL_SIP_SERVER, Labels.sip_server.label()),

    cascadegw(CascadeGwCM.class, Constants.CONFIGMAP_DMCU, Labels.cascadegw.label()),
    webrtc_siggw_x86(WebrtcSiggwCM.class, Constants.CONFIGMAP_WEBRTC_SIGGW, Labels.webrtc_siggw_x86.label()),
    webrtc_siggw_arm(WebrtcSiggwCM.class, Constants.CONFIGMAP_WEBRTC_SIGGW, Labels.webrtc_siggw_arm.label()),
    webrtc_mediagw_x86(WebrtcMediagwCM.class, Constants.CONFIGMAP_WEBRTC_MEDIAGW, Labels.webrtc_mediagw_x86.label()),
    webrtc_mediagw_arm(WebrtcMediagwCM.class, Constants.CONFIGMAP_WEBRTC_MEDIAGW, Labels.webrtc_mediagw_arm.label()),
    srs_extends_proxy(SrsExtendsProxyCM.class, Constants.CONFIGMAP_SRS_EXTENDS_PROXY, Labels.srs_extends_proxy.label()),
    avc_sip(AvcSipCM.class, Constants.CONFIGMAP_ALL_AVC_SIP, Labels.avc_sip.label()),
    avc_media(AvcMediaCM.class, Constants.CONFIGMAP_ALL_AVC_MEDIA, Labels.avc_media.label()),
    avc_sip_x86(AvcSipCM.class, Constants.CONFIGMAP_ALL_AVC_SIP, Labels.avc_sip_x86.label()),
    avc_media_x86(AvcMediaCM.class, Constants.CONFIGMAP_ALL_AVC_MEDIA, Labels.avc_media_x86.label()),
    avc_sip_arm(AvcSipCM.class, Constants.CONFIGMAP_ALL_AVC_SIP, Labels.avc_sip_arm.label()),
    avc_media_arm(AvcMediaCM.class, Constants.CONFIGMAP_ALL_AVC_MEDIA, Labels.avc_media_arm.label()),
    ivr(IvrCM.class, Constants.CONFIGMAP_ALL_IVR, Labels.ivr.label()),
    ivr_x86(IvrCM.class, Constants.CONFIGMAP_ALL_IVR, Labels.ivr_x86.label()),
    ivr_arm(IvrCM.class, Constants.CONFIGMAP_ALL_IVR, Labels.ivr_arm.label()),
    redis(RedisCM.class, Constants.CONFIGMAP_REDIS, Labels.redis.label()),
    signal(SignalCm.class, Constants.CONFIGMAP_ALL_SIGSERVER_HA, Labels.signal.label()),
    cdn_proxy(CdnProxyCM.class, Constants.CONFIGMAP_CDN_PROXY, Labels.cdn_proxy.label()),
    node_proxy(NodeProxyCM.class, Constants.CONFIGMAP_NODE_PROXY, Labels.node_proxy.label()),
    node_proxy_x86(NodeProxyCM.class, Constants.CONFIGMAP_NODE_PROXY, Labels.node_proxy_x86.label()),
    web(WebCM.class, Constants.CONFIGMAP_ALL_FRONTEND_WEB, Labels.webrtc.label()),
    mcaccess(McAccessCM.class, Constants.CONFIGMAP_MCACCESS_CONFIG, Labels.mcaccess.label()),
    third_proxy(ThirdBridgeProxyCM.class, Constants.CONFIGMAP_ALL_THIRD_BRIDGE_PROXY, Labels.third_proxy.label()),
    push(PushCM.class, Constants.CONFIGMAP_ALL_PUSH, Labels.push.label()),
    allocatorserver(AllocatorServerCM.class, Constants.CONFIGMAP_ALL_ALLOCATOR, Labels.allocator_server.label()),
    openresty_fusion(OpenrestyFusionCM.class, Constants.CONFIGMAP_OPENRESTY_FUSION, Labels.openresty_fusion.label()),
    cascademgr(CascadeMgrCM.class, Constants.CONFIGMAP_CASCADE_MGR, Labels.cascademgr.label()),
    third_bridge(ThirdBridgeCM.class, Constants.CONFIGMAP_THIRD_BRIDGE, Labels.third_bridge.label()),
    vodfilemanager(VodFileManagerCM.class, Constants.CONFIGMAP_VOD_FILE_MANAGER, Labels.vodfilemanager.label()),
    mms(MmsCM.class, Constants.CONFIGMAP_ALL_MMS, Labels.mms.label()),
    webrtc_proxy(WebrtcProxyCM.class, Constants.CONFIGMAP_ALL_WEBRTC_PROXY, Labels.webrtc_proxy.label()),
    webrtc_proxy_x86(WebrtcProxyCM.class, Constants.CONFIGMAP_ALL_WEBRTC_PROXY, Labels.webrtc_proxy_x86.label()),
    webrtc_proxy_arm(WebrtcProxyCM.class, Constants.CONFIGMAP_ALL_WEBRTC_PROXY, Labels.webrtc_proxy_arm.label()),
    aiagent(AIAgentCM.class, Constants.CONFIGMAP_ALL_AIAGENT, Labels.aiagent.label()),

    oceanbase(OceanBaseCM.class, Constants.CONFIGMAP_ALL_OCEANBASE, Labels.oceanbase.label()),
//    oceanbase_cluster(OceanBaseCM.class, Constants.CONFIGMAP_ALL_OCEANBASE, Labels.oceanbase_cluster.label()),

    aigateway(AigatewayCM.class, Constants.CONFIGMAP_ALL_AIGATEWAY, Labels.aigateway.label()),
    nightingale_categraf(NightingaleCategrafCM.class, Constants.CONFIGMAP_ALL_NIGHTINGALE_CATEGRAF, Labels.nightingale_categraf.label()),
    ;

    private Class clas;
    private String cm;
    private String type;

    ServiceCustomizeConfigClassEnum(Class clas, String configmap, String type) {
        this.clas = clas;
        this.cm = configmap;
        this.type = type;
    }

    public Class clas() {
        return this.clas;
    }

    public String configmap() {
        return this.cm;
    }

    public String label() {
        return this.type;
    }

}