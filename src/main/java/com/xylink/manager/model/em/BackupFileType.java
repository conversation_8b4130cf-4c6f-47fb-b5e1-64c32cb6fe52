package com.xylink.manager.model.em;

/**
 * <AUTHOR>
 * @date 2022/11/07/10:39
 */
public enum BackupFileType {
    EsType(0),
    HbaseType(1),
    RedisType(2),
    StType(3),
    DmType(4),
    DmRestoreType(5),
    StRestoreType(6),

    etcdBackUp(7)
    ;
    private final int value;
    BackupFileType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static BackupFileType valueOf(int value) {
        for(BackupFileType backupFileType: BackupFileType.values()) {
            if (backupFileType.getValue() == value) {
                return backupFileType;
            }
        }
        return null;
    }

}
