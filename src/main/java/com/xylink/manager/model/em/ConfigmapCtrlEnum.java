package com.xylink.manager.model.em;


import com.xylink.config.Constants;

import java.util.*;

/**
 * manager 存在大量修改configmap的需求，基于这个配置，实现一个统一的接口，避免重复工作，多余接口开发
 */
public enum ConfigmapCtrlEnum {

    /**
     * 管理数据库备份方式  master or slave
     */
    db_backup_type(Constants.CONFIGMAP_PRIVATE_DATA ,
            new ArrayList<String>(){{
                add("db_backup_type");
            }} ),

    ios_app_store(Constants.CONFIGMAP_ALLIP ,
            new ArrayList<String>(){{
                add("CUSTOMIZE_IOS_STORE");
                add("COMMON_IOS_STORE");
                add("COMMON_HARMONY_APP_STORE");
                add("COMMON_HARMONY_PC_STORE");
            }} ),
    ;




    private String configmap;
    private List<String> editableKeys;
    private Map<String,String> defaultKeyValues;


    public String getConfigmap() {
        return configmap;
    }

    public List<String> getEditableKeys() {
        return editableKeys;
    }

    public Map<String, String> getDefaultKeyValues() {
        return defaultKeyValues;
    }

    ConfigmapCtrlEnum(String configmap, List<String> editableKeys) {
        this.configmap = configmap;
        this.editableKeys = editableKeys;
        this.defaultKeyValues = Collections.emptyMap();
    }

    ConfigmapCtrlEnum(String configmap,List<String> editableKeys,Map<String, String> defaultKeyValues) {
        this.configmap = configmap;
        this.editableKeys = editableKeys;
        this.defaultKeyValues = defaultKeyValues;
    }


    public static boolean hasOne(String name) {
        return Arrays.stream(ConfigmapCtrlEnum.values()).anyMatch(en -> en.name().equals(name));
    }



}