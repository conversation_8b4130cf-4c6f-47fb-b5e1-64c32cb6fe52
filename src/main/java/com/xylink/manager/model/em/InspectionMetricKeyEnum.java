package com.xylink.manager.model.em;

/**
 * <AUTHOR>
 * @date 2023/04/13/18:12
 */
public enum InspectionMetricKeyEnum {
    KAFKA_PRODUCED_AND_CONSUMED("Kafka消费生产数据"),

    MYSQL_AGENT_LIVE("Mysql探活"),
    MYSQL_CONNECTION_COUNT("Mysql连接数"),
    MYSQL_INNODB_BUFFER_POOL_HIT_RATE("InnoDB Buffer Pool命中率"),
    MYSQL_INNODB_BUFFER_POOL_REQUEST_COUNT("InnoDB Buffer Pool请求次数"),
    MYSQL_INNODB_ROW_OPERATIONS("InnoDB Row Operations"),
    MYSQL_TPS("Mysql TPS"),
    MYSQL_QPS("Mysql QPS"),

    REDIS_AGENT_LIVE("redis探活"),
    REDIS_CONNECTION_COUNT("redis连接数"),
    REDIS_ACTUAL_MEMORY_USAGE("redis实际使用内存"),
    REDIS_AOF("redis aof"),
    REDIS_QPS("redis qps"),
    REDIS_KEYSPACE("各DB的key个数"),

    ZK_AGENT_LIVE("zookeeper探活"),
    ZK_LATENCY("zookeeper请求延时"),
    ZK_RECEIVED("zookeeper收包数"),
    ZK_SEND("zookeeper发包数"),
    ZK_CONNECTED_CLIENTS("zookeeper连接数"),
    ZK_NODE("zookeeper节点数");

    private final String metricKey;
    InspectionMetricKeyEnum(String metricKey) {
        this.metricKey = metricKey;
    }

    public String getMetricKey() {
        return metricKey;
    }
}
