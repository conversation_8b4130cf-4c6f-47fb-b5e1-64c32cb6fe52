package com.xylink.manager.model.em;

public enum IppbxSiggatewayEnum {

    IPPBX_PROXY_IP("-IPPBX-PROXY-IP"),
    IPPBX_PROXY_PORT("-IPPBX-PROXY-PORT"),
    IPPBX_USER("-IPPBX-USER"),
    IPPBX_PWD("-IPPBX-PWD"),
    IPPBX_TYPE("-IPPBX-TYPE"),
    IPPBX_SN("-IPPBX-SN"),
    IPPBX_SIGGW_INTERNA_IP("-IPPBX-SIGGW-INTERNA-IP"),
    IPPBX_SIGGW_PUBLIC_IP("-IPPBX-SIGGW-PUBLIC-IP"),
    IPPBX_SIGGW_DOMAIN("-IPPBX-SIGGW-DOMAIN"),
    IPPBX_SIGGW_FROM_PREFIX("-FROM-PREFIX"),
    IPPBX_SIGGW_TO_PREFIX("-TO-PREFIX"),
    IPPBX_SIGGW_KEEPALIVE("-KEEPALIVECALL"),
    ;

    private String value;

    IppbxSiggatewayEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}