package com.xylink.manager.model.em;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/04/13/15:09
 */
public enum InspectLadderEnum {
    NORMAL("正常", 0),
    RISK("风险",1),
    EXCEPT("异常",2)
    ;
    private final String type;
    private final int value;

    InspectLadderEnum(String type, int value) {
        this.type = type;
        this.value = value;
    }

    /**
     * 获取巡检状态
     */
    public static InspectLadderEnum getInspectLadder(Integer value) {
        if (null == value) {
            return InspectLadderEnum.EXCEPT;
        }
        for (InspectLadderEnum inspectLadder : values()) {
            if (inspectLadder.value == value) {
                return inspectLadder;
            }
        }
        return InspectLadderEnum.EXCEPT;
    }

    /**
     * 获取巡检状态
     */
    public static InspectLadderEnum getInspectLadderByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return InspectLadderEnum.EXCEPT;
        }
        for (InspectLadderEnum inspectLadder : values()) {
            if (type.equals(inspectLadder.getType())) {
                return inspectLadder;
            }
        }
        return InspectLadderEnum.EXCEPT;
    }

    public String getType() {
        return type;
    }
    public int getValue() {
        return value;
    }

}
