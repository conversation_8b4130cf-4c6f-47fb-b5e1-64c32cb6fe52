package com.xylink.manager.model.em;

public enum ShellEnum {

    frpc_auto_start_status(new String[]{
            "/bin/bash",
            "-c",
            "systemctl list-unit-files | grep privatefrpc"
    }),


    frpc_start_status(new String[]{
            "/bin/bash",
            "-c",
            "systemctl status privatefrpc"
    }),

    frpc_start(new String[]{
            "/bin/bash",
            "-c",
            "systemctl restart privatefrpc"
    }),

    frpc_stop(new String[]{
            "/bin/bash",
            "-c",
            "systemctl stop privatefrpc"
    }),

    frpc_auto_start(new String[]{
            "/bin/bash",
            "-c",
            "systemctl enable privatefrpc"
    }),

    frpc_cancel_auto_start(new String[]{
            "/bin/bash",
            "-c",
            "systemctl disable privatefrpc"
    }),

    ;


    private String[] commands;

    ShellEnum (String[] commands) {
        this.commands = commands;
    }

    public String[] cmd(){
        return this.commands;
    }




}
