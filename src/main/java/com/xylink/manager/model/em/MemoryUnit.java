package com.xylink.manager.model.em;

/**
 * 向上转换会出现不足1024的情况下返回 0
 *
 * <AUTHOR>
 * @since 2021/7/6 10:15 上午
 */
public enum MemoryUnit {
    /**
     * kb
     */
    KB {
        @Override
        public long toKb(long duration) {
            return duration;
        }

        @Override
        public long toMb(long duration) {
            return duration / C1;
        }

        @Override
        public long toGb(long duration) {
            return duration / C2;
        }

        @Override
        public long toTb(long duration) {
            return duration / C3;
        }

        @Override
        public long convert(long duration, MemoryUnit unit) {
            return unit.toKb(duration);
        }
    },
    MB {
        @Override
        public long toKb(long duration) {
            return duration * C1;
        }

        @Override
        public long toMb(long duration) {
            return duration;
        }

        @Override
        public long toGb(long duration) {
            return duration / C1;
        }

        @Override
        public long toTb(long duration) {
            return duration / C2;
        }

        @Override
        public long convert(long duration, MemoryUnit unit) {
            return unit.toMb(duration);
        }
    },
    GB {
        @Override
        public long toKb(long duration) {
            return duration * C2;
        }

        @Override
        public long toMb(long duration) {
            return duration * C1;
        }

        @Override
        public long toGb(long duration) {
            return duration;
        }

        @Override
        public long toTb(long duration) {
            return duration / C1;
        }

        @Override
        public long convert(long duration, MemoryUnit unit) {
            return unit.toGb(duration);
        }
    },

    TB {
        @Override
        public long toKb(long duration) {
            return duration * C3;
        }

        @Override
        public long toMb(long duration) {
            return duration * C2;
        }

        @Override
        public long toGb(long duration) {
            return duration * C1;
        }

        @Override
        public long toTb(long duration) {
            return duration;
        }

        @Override
        public long convert(long duration, MemoryUnit unit) {
            return unit.toGb(duration);
        }
    };

    static final long C0 = 1L;
    static final long C1 = C0 * 1024;
    static final long C2 = C1 * 1024;
    static final long C3 = C2 * 1024;

    public long toKb(long duration) {
        throw new AbstractMethodError();
    }

    public long toMb(long duration) {
        throw new AbstractMethodError();
    }

    public long toGb(long duration) {
        throw new AbstractMethodError();
    }

    public long toTb(long duration) {
        throw new AbstractMethodError();
    }

    public long convert(long duration, MemoryUnit unit) {
        throw new AbstractMethodError();
    }

}
