package com.xylink.manager.model.em;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/10/12/17:12
 */
public enum RedisMode {
    //单机
    SINGLE("single"),
    //哨兵
    SENTINEL("sentinel"),
    //集群
    CLUSTER("cluster"),
    ;
    private final String mode;

    RedisMode(String mode) {
        this.mode = mode;
    }

    public String getMode() {
        return mode;
    }

    public static RedisMode getRedisMode(String mode){
        if (StringUtils.isBlank(mode)) {
            return RedisMode.SINGLE;
        }
        for (RedisMode value : values()) {
            if (mode.equals(value.getMode())) {
                return value;
            }
        }
        return RedisMode.SINGLE;
    }
}
