package com.xylink.manager.model.em;

import java.util.Arrays;
import java.util.HashMap;

public enum DefaultConfigmapDataEnum {

    edu_mediagw(Labels.edu_1nmediagw.label(), new HashMap<String,String>(){{
        put("DEFAULT-SITECODE", "86-PXYLINK");
        put("DEFAULT-MAX-RX-BANDWIDTH", "400");
        put("DEFAULT-MAX-TX-BANDWIDTH", "400");
        put("DEFAULT-MEDIA-PROCESS-TASKNUM", "2");
        put("DEFAULT-MEDIA-EPNUM-PERTASK", "100");
        put("DEFAULT-DMCU-CLIENT-PORT", "5000");
    }}),

    default_cm("default_cm", new HashMap()),
    ;


    private String label;
    private HashMap<String,String> data;

    DefaultConfigmapDataEnum(String label ,HashMap<String,String> data) {
        this.label = label;
        this.data = data;
    }

    public HashMap<String,String> getData() {
        return data;
    }

    public String getLabel() {
        return label;
    }


    public static HashMap<String, String> initDefault(String label) {
        DefaultConfigmapDataEnum cm = Arrays.stream(DefaultConfigmapDataEnum.values()).filter(em -> em.label.equals(label)).findFirst().orElse(DefaultConfigmapDataEnum.default_cm);
        return cm.getData();
    }
}