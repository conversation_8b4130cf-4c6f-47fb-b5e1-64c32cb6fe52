package com.xylink.manager.model;

import lombok.*;

/**
 * @Author: liyang
 * @DateTime: 2021/10/11 5:25 下午
 **/
@Setter
@Getter
public class HaproxyInfoDto {
    /**
     * 规则类型
     */
    private String ruleType;
    /**
     * 模式，支持tcp和http模式
     */
    private String mode;
    /**
     * 前端端口
     */
    private String frontPort;
    /**
     * 后端主Ip
     */
    private String backMasterIp;
    /**
     * 后端主端口
     */
    private String backMasterPort;
    /**
     * 后端备Ip
     */
    private String backSlaveIp;
    /**
     * 后端备端口
     */
    private String backSlavePort;

    public HaproxyInfoDto(){}

    public HaproxyInfoDto(String ruleType, String frontPort, String backMasterIp, String backMasterPort, String backSlaveIp, String backSlavePort) {
        this.ruleType = ruleType;
        this.frontPort = frontPort;
        this.backMasterIp = backMasterIp;
        this.backMasterPort = backMasterPort;
        this.backSlaveIp = backSlaveIp;
        this.backSlavePort = backSlavePort;
    }

    public HaproxyInfoDto(String ruleType, String mode, String frontPort, String backMasterIp, String backMasterPort, String backSlaveIp, String backSlavePort) {
        this.ruleType = ruleType;
        this.mode = mode;
        this.frontPort = frontPort;
        this.backMasterIp = backMasterIp;
        this.backMasterPort = backMasterPort;
        this.backSlaveIp = backSlaveIp;
        this.backSlavePort = backSlavePort;
    }
}
