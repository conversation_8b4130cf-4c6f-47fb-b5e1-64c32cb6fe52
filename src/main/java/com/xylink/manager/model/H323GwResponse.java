package com.xylink.manager.model;

import com.xylink.manager.model.em.GateWayEnum;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/12/20.
 */
public class H323GwResponse {
    private String id;
    private String sn;
    private String number;
    private long createdTimestamp;
    private long expiredTimestamp;
    private String type;
    private int maxInOut;
    private String enterpriseId;
    private String ip;
    private String aliasName;

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getAliasName() {
        return aliasName;
    }

    public void setAliasName(String aliasName) {
        this.aliasName = aliasName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public long getCreatedTimestamp() {
        return createdTimestamp;
    }

    public void setCreatedTimestamp(long createdTimestamp) {
        this.createdTimestamp = createdTimestamp;
    }

    public long getExpiredTimestamp() {
        return expiredTimestamp;
    }

    public void setExpiredTimestamp(long expiredTimestamp) {
        this.expiredTimestamp = expiredTimestamp;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getMaxInOut() {
        return maxInOut;
    }

    public void setMaxInOut(int maxInOut) {
        this.maxInOut = maxInOut;
    }

    public String getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(String enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public boolean h323Gw(){
        return GateWayEnum.H323.getValue().equals(this.type);
    }

    public boolean webrtcGw(){
        return GateWayEnum.WEBRTC.getValue().equals(this.type);
    }
    public boolean fusion1nGw(){
        return GateWayEnum.FUSION1NGW.getValue().equals(this.type);
    }

    @Override
    public String toString() {
        return "H323Gw{" +
                "id='" + id + '\'' +
                ", sn='" + sn + '\'' +
                ", number='" + number + '\'' +
                ", createdTimestamp=" + createdTimestamp +
                ", expiredTimestamp=" + expiredTimestamp +
                ", type='" + type + '\'' +
                ", maxInOut=" + maxInOut +
                ", enterpriseId='" + enterpriseId + '\'' +
                '}';
    }

}
