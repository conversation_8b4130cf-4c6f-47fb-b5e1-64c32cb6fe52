package com.xylink.manager.model.miping;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024-09-25 15:13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MmAppInfo extends ThirdAppInfo {
    private static final String NAME = "mm";

    private String keyPairId;
    private String keyPairVersion;
    private String pubKey;
    private String keyId;
    private String keyVersion;

    @Override
    protected String getName() {
        return NAME;
    }

    @Override
    public String toString() {
        return "MmAppInfo{" +
                "keyPairId='" + keyPairId + '\'' +
                ", keyPairVersion='" + keyPairVersion + '\'' +
                ", pubKey='" + pubKey + '\'' +
                ", keyId='" + keyId + '\'' +
                ", keyVersion='" + keyVersion + '\'' +
                ", appId='" + appId + '\'' +
                ", gateWayAddress='" + gateWayAddress + '\'' +
                '}';
    }
}
