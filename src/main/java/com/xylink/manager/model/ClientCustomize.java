package com.xylink.manager.model;

import lombok.Getter;
import lombok.Setter;

/**
 * @Author: congyajing
 * @Date: 2020-04-16 23:09
 */
@Getter
@Setter
public class ClientCustomize {

    private String enterpriseId = "default_enterprise";
    /**
     * 接入限制用的CK
     */
    private String customizedKey;
    private Boolean specialClientLogin;
    // 是否自动跳转定制版下载页面（是： 输入 /app 重定向到 /app/?customizedkey=xxx）
    private Boolean autoRedCustomize;
    /**
     * 跳转定制版本页面用的CK
     */
    private String autoRedCustomizedKey;
    // 拼接下载链接
    private String mainDomain;
    private String mainPort;
    private String mainSslPort;
}
