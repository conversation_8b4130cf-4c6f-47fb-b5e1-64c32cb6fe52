package com.xylink.manager.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by cong yajing on 2018/10/21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GreyMinVersion implements Serializable {
    private String id;
    private Integer strategy;
    private Integer greyLevel;
    private Integer minVersion;
    private String platform;
    private String minVersionPack;
    private String greyVersion;
    private String givenVersion;
    private String customizedKey;
}
