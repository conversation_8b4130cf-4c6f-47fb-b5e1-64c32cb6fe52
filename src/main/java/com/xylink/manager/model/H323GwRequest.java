package com.xylink.manager.model;

import org.apache.commons.lang3.StringUtils;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/12/16.
 */
public class H323GwRequest {

    private String sn;
    private String number;
    private int maxInCount = 10;
    private long expiredTimestamp = Long.MAX_VALUE;
    private String enterpriseId;
    private String ip;
    private String aliasName;

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getAliasName() {
        return aliasName;
    }

    public void setAliasName(String aliasName) {
        this.aliasName = aliasName;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public int getMaxInCount() {
        return maxInCount;
    }

    public void setMaxInCount(int maxInCount) {
        this.maxInCount = maxInCount;
    }

    public long getExpiredTimestamp() {
        return expiredTimestamp;
    }

    public void setExpiredTimestamp(long expiredTimestamp) {
        this.expiredTimestamp = expiredTimestamp;
    }

    public boolean maybeInvalid() {
        return !maybeValid();
    }

    public boolean maybeValid() {
        return StringUtils.isNotBlank(sn) && StringUtils.isNotBlank(number);
    }

    public String getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(String enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    @Override
    public String toString() {
        return "H323GwDevice{" +
                "sn='" + sn + '\'' +
                ", number='" + number + '\'' +
                ", maxInCount=" + maxInCount +
                ", expiredTimestamp=" + expiredTimestamp +
                ", enterpriseId='" + enterpriseId + '\'' +
                '}';
    }
}

