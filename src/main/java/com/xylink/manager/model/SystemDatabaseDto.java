package com.xylink.manager.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 备系统数据库信息
 *
 * <AUTHOR>
 * @since 2022/4/11 2:52 下午
 */
@Data
public class SystemDatabaseDto implements Serializable {
    private String ip;
    private String port;
    private String role;
    private String name;
    private String managerAddress;
    private List<Properties> properties;

    public SystemDatabaseDto() {
        this.name = "main";
    }

    public SystemDatabaseDto(String role) {
        this();
        this.role = role;
    }

    public SystemDatabaseDto(String ip, String port, String role,String managerAddress) {
        this();
        this.ip = ip;
        this.port = port;
        this.role = role;
        this.managerAddress = managerAddress;
    }

    public void addProperty(String key, String value) {
        addProperty(new Properties(key, value));
    }

    public void addProperty(Properties properties) {
        if (this.properties == null) {
            this.properties = new ArrayList<>();
        }
        this.properties.add(properties);
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Properties {
        private String key;
        private String value;
    }

    public enum SystemRole {
        /**
         * master
         */
        MASTER("master"),
        /**
         * slave
         */
        SLAVE("slave");
        private String name;

        SystemRole(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }
    }

}
