package com.xylink.manager.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: liyang
 * @DateTime: 2023/3/15 5:40 下午
 **/
@Data
@NoArgsConstructor
public class VersionCompare{
    private String name;
    private String upgrade;
    private String current;

    //是否升级
    private String upgraded;

    public VersionCompare(String name, String upgrade, String current) {
        this.name = name;
        this.current = current;
        int index = upgrade.lastIndexOf(":");
        String ver = index > 0 ? upgrade.substring(index + 1) : upgrade;
        this.upgrade = ver;
        this.upgraded = StringUtils.isBlank(current) ? "unknown" : String.valueOf(ver.equals(current));
    }
}
