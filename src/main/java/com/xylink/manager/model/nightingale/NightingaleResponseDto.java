package com.xylink.manager.model.nightingale;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NightingaleResponseDto {
    /**
     * v5返回结果集
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Object data;

    /**
     * 返回结果集
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Object dat;

    /**
     * 错误信息
     */
    private String err;

    public static NightingaleResponseDto build() {
        return new NightingaleResponseDto();
    }

    public NightingaleResponseDto dat(Object dat) {
        this.dat = dat;
        return this;
    }
}
