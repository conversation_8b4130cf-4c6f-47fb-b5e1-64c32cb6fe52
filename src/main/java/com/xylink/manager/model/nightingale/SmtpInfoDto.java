package com.xylink.manager.model.nightingale;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class SmtpInfoDto {

    @NotBlank(message = "smtpHost不能为空")
    private String smtpHost;

    @NotBlank(message = "smtpPort不能为空")
    private String smtpPort;

    @NotBlank(message = "smtpUser不能为空")
    private String smtpUser;

    @NotBlank(message = "smtpPass不能为空")
    private String smtpPass;

    private int smtpInsecureSkipVerify;

    private String smtpDefaultReceiver;

    private String[] targets;

    private String[] defaultEmailSeverities;
}
