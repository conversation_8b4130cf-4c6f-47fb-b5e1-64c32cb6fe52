package com.xylink.manager.sms.service;

import com.xylink.manager.service.ServerListService;
import com.xylink.manager.sms.dto.DatingSmsDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/16
 */
@Service
@Slf4j
public class DatingService {
    private final RestTemplate restTemplate;

    private final ServerListService serverListService;

    public DatingService(RestTemplate restTemplate, ServerListService serverListService) {
        this.restTemplate = restTemplate;
        this.serverListService = serverListService;
    }

    /**
     * 获取约会短信模板
     *
     * @return
     */
    public List<DatingSmsDTO> getDatingSmsTemplates() {
        String mainIp = serverListService.getMainNodeInternalIP();
        String url = "http://" + mainIp + ":11111/api/rest/dating/internal/generalconfig/query?configNames={configNames}";
        return restTemplate.exchange(url, HttpMethod.GET, null, new ParameterizedTypeReference<List<DatingSmsDTO>>() {
        }, "schedule_meeting_create_sms_template_user,schedule_meeting_create_sms_template_ops," +
                "schedule_meeting_update_sms_template_user,schedule_meeting_update_sms_template_ops," +
                "schedule_meeting_cancel_sms_template_user,schedule_meeting_cancel_sms_template_ops").getBody();
    }

    /**
     * 保存约会短信模板
     *
     * @param datingSmsDTOS
     */
    public void saveDatingSmsTemplates(List<DatingSmsDTO> datingSmsDTOS) {
        String mainIp = serverListService.getMainNodeInternalIP();
        String url = "http://" + mainIp + ":11111/api/rest/dating/internal/generalconfig/save";
        restTemplate.postForObject(url, datingSmsDTOS, Void.class);
    }
}
