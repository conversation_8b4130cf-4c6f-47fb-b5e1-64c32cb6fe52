package com.xylink.manager.sms.controller;

import com.xylink.config.Constants;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.sms.dto.SmsDTO;
import com.xylink.manager.sms.service.DatingService;
import com.xylink.manager.sms.service.IAuthService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/16
 * @description 短信设置控制器
 */
@RestController
@RequestMapping("/sms/platform/config")
public class SmsController {
    private static final String SMS_SIGNATURE = "SMS_SIGNATURE";
    private final K8sService k8sService;

    private final DatingService datingService;

    private final IAuthService iAuthService;

    public SmsController(K8sService k8sService, DatingService datingService, IAuthService iAuthService) {
        this.k8sService = k8sService;
        this.datingService = datingService;
        this.iAuthService = iAuthService;
    }

    @GetMapping("/v1")
    public SmsDTO current() {
        SmsDTO smsDTO = new SmsDTO();
        //configmap 查询短信签名
        Map<String, String> configmap = k8sService.getConfigmap(Constants.CONFIGMAP_ALL_SMS);
        String smsSignature = "";
        if (configmap != null && configmap.containsKey(SMS_SIGNATURE)) {
            smsSignature = configmap.get(SMS_SIGNATURE);
        }
        smsDTO.setSignature(smsSignature);
        //接口查询登录、重置密码短信模版
        smsDTO.setLoginSms(iAuthService.getLoginSmsTemplates());
        //预约会议短信模版列表
        smsDTO.setDatingSmsList(datingService.getDatingSmsTemplates());
        return smsDTO;
    }

    @PostMapping("/v1")
    public void set(@RequestBody SmsDTO smsDTO) {
        //configmap 更新短信签名
        Map<String, String> configmap = k8sService.getConfigmap(Constants.CONFIGMAP_ALL_SMS);
        if (configmap == null) {
            configmap = new HashMap<>();
        }
        String sourceSignature = configmap.get(SMS_SIGNATURE);
        if (!StringUtils.equals(sourceSignature, smsDTO.getSignature())) {
            configmap.put(SMS_SIGNATURE, smsDTO.getSignature());
            k8sService.editConfigmap(Constants.CONFIGMAP_ALL_SMS, configmap);
            //重启sms服务
            k8sService.restartAllPodByNodeAppLabel(Labels.sms.label());
        }
        //接口更新登录、重置密码短信模版
        datingService.saveDatingSmsTemplates(smsDTO.getDatingSmsList());
        //接口更新预约会议短信模版
        iAuthService.saveLoginSmsTemplates(smsDTO.getLoginSms());
    }
}
