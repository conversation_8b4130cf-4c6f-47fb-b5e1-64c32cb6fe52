package com.xylink.manager.job;

import com.xylink.manager.service.LicenseInfoService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/4/27
 */
@Service
public class JobService {

    private final LicenseInfoService licenseInfoService;

    public JobService(LicenseInfoService licenseInfoService) {
        this.licenseInfoService = licenseInfoService;
    }

    @Scheduled(cron = "0 0/30 * * * ?")
    public void syncChargeFp() {
        licenseInfoService.syncLicenseInfo();
    }
}
