package com.xylink.manager.mapper.impl;

import com.xylink.manager.inspection.config.DataSourceManager;
import com.xylink.manager.mapper.ClientTerminalInfoMapper;
import com.xylink.manager.model.ClientTerminalFrontInfo;
import com.xylink.manager.model.ClientTerminalInfo;
import com.xylink.manager.model.ClientTerminalResult;
import com.xylink.manager.model.TerminalFrontPlatform;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class ClientTerminalInfoMapperImpl implements ClientTerminalInfoMapper {

    @Autowired
    private DataSourceManager dataSourceManager;

    @Override
    public List<ClientTerminalResult> getAllTerminalInfoList() {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            ClientTerminalInfoMapper mapper = session.getMapper(ClientTerminalInfoMapper.class);
            return mapper.getAllTerminalInfoList();
        }
    }

    @Override
    public List<ClientTerminalInfo> getAllClientTerminalInfoList(String terminalType) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            ClientTerminalInfoMapper mapper = session.getMapper(ClientTerminalInfoMapper.class);
            return mapper.getAllClientTerminalInfoList(terminalType);
        }
    }

    @Override
    public List<TerminalFrontPlatform> getClientTerminalFrontPlatformList() {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            ClientTerminalInfoMapper mapper = session.getMapper(ClientTerminalInfoMapper.class);
            return mapper.getClientTerminalFrontPlatformList();
        }
    }

    @Override
    public List<Map<String, String>> getClientTerminalFrontInfoList() {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            ClientTerminalInfoMapper mapper = session.getMapper(ClientTerminalInfoMapper.class);
            return mapper.getClientTerminalFrontInfoList();
        }
    }

    @Override
    public int removeAllTerminalInfo() {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            ClientTerminalInfoMapper mapper = session.getMapper(ClientTerminalInfoMapper.class);
            return mapper.removeAllTerminalInfo();
        }
    }

    @Override
    public int removeAllTerminalFrontPlatform() {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            ClientTerminalInfoMapper mapper = session.getMapper(ClientTerminalInfoMapper.class);
            return mapper.removeAllTerminalFrontPlatform();
        }
    }

    @Override
    public int removeAllTerminalFrontInfo() {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            ClientTerminalInfoMapper mapper = session.getMapper(ClientTerminalInfoMapper.class);
            return mapper.removeAllTerminalFrontInfo();
        }
    }

    @Override
    public int batchAddAllTerminalInfo(List<ClientTerminalResult> terminalInfoList) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            ClientTerminalInfoMapper mapper = session.getMapper(ClientTerminalInfoMapper.class);
            return mapper.batchAddAllTerminalInfo(terminalInfoList);
        }
    }

    @Override
    public int batchAddAllTerminalFrontPlatform(List<TerminalFrontPlatform> terminalFrontPlatformList) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            ClientTerminalInfoMapper mapper = session.getMapper(ClientTerminalInfoMapper.class);
            return mapper.batchAddAllTerminalFrontPlatform(terminalFrontPlatformList);
        }
    }

    @Override
    public int batchAddAllTerminalFrontInfo(List<ClientTerminalFrontInfo> terminalFrontInfoList) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            ClientTerminalInfoMapper mapper = session.getMapper(ClientTerminalInfoMapper.class);
            return mapper.batchAddAllTerminalFrontInfo(terminalFrontInfoList);
        }
    }
}
