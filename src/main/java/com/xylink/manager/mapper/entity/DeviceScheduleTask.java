package com.xylink.manager.mapper.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> create on 2023/12/11
 */
@Data
@TableName("ops_device_schedule_task")
public class DeviceScheduleTask {

    /**
     * 主键ID
     */
    @TableId("id")
    private String id;

    /**
     * 任务编号
     */
    private String taskNo;
    /**
     * 任务类型
     *
     * @see com.xylink.manager.model.em.taskcenter.DeviceScheduleTaskTypeEnum
     */
    private String taskType;

    /**
     * 生效范围
     *
     * @see com.xylink.manager.model.em.taskcenter.DeviceScheduleTaskScopeEnum
     */
    private String taskScope;

    /**
     * 重复周期类型
     *
     * @see com.xylink.manager.model.em.taskcenter.DeviceScheduleTaskRepeatEnum
     */
    private String repeatType;

    /**
     * 执行时间（精确到分钟）
     */
    private String executeTime;

    /**
     * 每周的第x天
     */
    private String dayOfWeek;

    /**
     * 每月的第x天
     */
    private String dayOfMonth;

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * cron表达式
     */
    private String cronStr;

    /**
     * 下次执行时间
     */
    private String nextExecuteTime;

    /**
     * 执行类型
     *
     * @see com.xylink.manager.model.em.taskcenter.DeviceScheduleTaskExecuteEnum
     */
    private String executeType;

    /**
     * 企业ID
     */
    private String enterpriseId;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 终端类型
     *
     * @see com.xylink.manager.model.em.taskcenter.DeviceType
     */
    private String deviceType;

    /**
     * 任务状态
     *
     * @see com.xylink.manager.model.em.taskcenter.DeviceScheduleTaskStateEnum
     */
    private String taskState;

    /**
     * 操作人账号
     */
    private String operateUsername;
    /**
     * 操作人名称
     */
    private String operateDisplayName;

    private Date gmtCreate;
    private Date gmtModified;
}
