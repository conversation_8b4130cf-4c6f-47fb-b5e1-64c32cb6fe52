package com.xylink.manager.mapper.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> create on 2023/12/12
 */
@TableName("ops_device_schedule_target")
@Data
public class DeviceScheduleTarget {

    @TableId("id")
    private String id;
    /**
     * 任务中心ID
     */
    private String taskCenterId;
    /**
     * 设备ID
     */
    private String deviceId;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 终端号
     */
    private String deviceNumber;

    /**
     * 终端callUri
     */
    private String callUri;

    /**
     * sn
     */
    private String deviceSn;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 设备类型
     */
    private String deviceCategory;

    /**
     * 设备类型
     */
    private String deviceSubType;


    private Date gmtCreate;
    private Date gmtModified;


}
