package com.xylink.manager.mapper.taskcenter;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xylink.manager.mapper.entity.DeviceScheduleTarget;
import com.xylink.manager.mapper.entity.DeviceScheduleTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> create on 2023/12/11
 */
@Mapper
public interface DeviceScheduleTaskMapper extends BaseMapper<DeviceScheduleTask> {

    void insertScheduleTargets(@Param("targets") List<DeviceScheduleTarget> targets);

    void deleteTargetByTaskId(@Param("taskCenterId") String taskCenterId);

    List<DeviceScheduleTask> selectByPage(@Param("offset") long offset, @Param("size") long size, @Param("keywords") String keywords);

    Long count(@Param("keywords") String keywords);

    List<DeviceScheduleTarget> selectTargetByTaskCenterId(@Param("taskCenterId") String taskCenterId);

    List<DeviceScheduleTarget> selectTargetByTaskCenterIdLimit(@Param("taskCenterId") String taskCenterId, @Param("limitSize") int limitSize);

    List<DeviceScheduleTarget> selectTargetByPage(@Param("taskCenterId") String taskCenterId,
                                                  @Param("keywords") String keywords,
                                                  @Param("offset") long offset,
                                                  @Param("size") long size);

    Long countTargetByTaskCenterId(@Param("taskCenterId") String taskCenterId, @Param("keywords") String keywords);

    String maxTaskNo(@Param("day") String day);
}
