package com.xylink.manager.mapper.taskcenter;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.xylink.manager.inspection.config.DataSourceManager;
import com.xylink.manager.mapper.entity.DeviceScheduleTarget;
import com.xylink.manager.mapper.entity.DeviceScheduleTask;
import com.xylink.manager.model.em.taskcenter.DeviceScheduleTaskStateEnum;
import com.xylink.manager.model.em.taskcenter.DeviceScheduleTaskTypeEnum;
import org.apache.ibatis.session.SqlSession;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> create on 2023/12/11
 */
@Repository
public class DeviceScheduleTaskDao {

    private final DataSourceManager dataSourceManager;

    public DeviceScheduleTaskDao(DataSourceManager dataSourceManager) {
        this.dataSourceManager = dataSourceManager;
    }


    public void add(DeviceScheduleTask task) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            DeviceScheduleTaskMapper deviceScheduleTaskMapper = session.getMapper(DeviceScheduleTaskMapper.class);
            deviceScheduleTaskMapper.insert(task);
        }
    }

    public void addTargets(List<DeviceScheduleTarget> targets) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            DeviceScheduleTaskMapper deviceScheduleTaskMapper = session.getMapper(DeviceScheduleTaskMapper.class);
            deviceScheduleTaskMapper.insertScheduleTargets(targets);
        }

    }

    public DeviceScheduleTask selectById(String id) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            DeviceScheduleTaskMapper deviceScheduleTaskMapper = session.getMapper(DeviceScheduleTaskMapper.class);
            return deviceScheduleTaskMapper.selectById(id);
        }

    }

    public void deleteById(String id) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            DeviceScheduleTaskMapper deviceScheduleTaskMapper = session.getMapper(DeviceScheduleTaskMapper.class);
            deviceScheduleTaskMapper.deleteById(id);
        }

    }

    public void deleteTargetByTaskId(String taskCenterId) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            DeviceScheduleTaskMapper deviceScheduleTaskMapper = session.getMapper(DeviceScheduleTaskMapper.class);
            deviceScheduleTaskMapper.deleteTargetByTaskId(taskCenterId);
        }

    }

    public void updateById(DeviceScheduleTask newTask, String taskCenterId) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            DeviceScheduleTaskMapper deviceScheduleTaskMapper = session.getMapper(DeviceScheduleTaskMapper.class);
            newTask.setId(taskCenterId);
            deviceScheduleTaskMapper.updateById(newTask);
        }

    }

    public List<DeviceScheduleTask> selectByPage(String keywords, PageDTO<DeviceScheduleTask> pageDTO) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            DeviceScheduleTaskMapper deviceScheduleTaskMapper = session.getMapper(DeviceScheduleTaskMapper.class);
            return deviceScheduleTaskMapper.selectByPage(pageDTO.offset(), pageDTO.getSize(), keywords);
        }

    }

    public long count(String keywords) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            DeviceScheduleTaskMapper deviceScheduleTaskMapper = session.getMapper(DeviceScheduleTaskMapper.class);
            return deviceScheduleTaskMapper.count(keywords);
        }
    }

    public List<DeviceScheduleTarget> selectTargetByTaskCenterId(String scheduleTaskId) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            DeviceScheduleTaskMapper deviceScheduleTaskMapper = session.getMapper(DeviceScheduleTaskMapper.class);
            return deviceScheduleTaskMapper.selectTargetByTaskCenterId(scheduleTaskId);
        }

    }

    public List<DeviceScheduleTarget> selectTargetByTaskCenterIdLimit(String scheduleTaskId, int limit) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            DeviceScheduleTaskMapper deviceScheduleTaskMapper = session.getMapper(DeviceScheduleTaskMapper.class);
            return deviceScheduleTaskMapper.selectTargetByTaskCenterIdLimit(scheduleTaskId, limit);
        }
    }

    public void updateById(DeviceScheduleTask dto) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            DeviceScheduleTaskMapper deviceScheduleTaskMapper = session.getMapper(DeviceScheduleTaskMapper.class);
            deviceScheduleTaskMapper.updateById(dto);
        }
    }


    public List<DeviceScheduleTask> selectTimedRunningTask() {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            DeviceScheduleTaskMapper deviceScheduleTaskMapper = session.getMapper(DeviceScheduleTaskMapper.class);
            QueryWrapper<DeviceScheduleTask> wrapper = new QueryWrapper<>();
            wrapper.eq("task_type", DeviceScheduleTaskTypeEnum.TIMED.getTaskType())
                    .eq("task_state", DeviceScheduleTaskStateEnum.RUNNING.getState());
            return deviceScheduleTaskMapper.selectList(wrapper);
        }
    }


    public List<DeviceScheduleTarget> selectTargetByPage(String scheduleTaskId, String keywords, PageDTO<DeviceScheduleTarget> pageDTO) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            DeviceScheduleTaskMapper deviceScheduleTaskMapper = session.getMapper(DeviceScheduleTaskMapper.class);
            return deviceScheduleTaskMapper.selectTargetByPage(scheduleTaskId, keywords, pageDTO.offset(), pageDTO.getSize());
        }
    }

    public long countTargetByTaskCenterId(String scheduleTaskId, String keywords) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            DeviceScheduleTaskMapper deviceScheduleTaskMapper = session.getMapper(DeviceScheduleTaskMapper.class);
            return deviceScheduleTaskMapper.countTargetByTaskCenterId(scheduleTaskId, keywords);
        }
    }

    public String getNewTaskNo(String day) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            DeviceScheduleTaskMapper deviceScheduleTaskMapper = session.getMapper(DeviceScheduleTaskMapper.class);
            return deviceScheduleTaskMapper.maxTaskNo(day);
        }
    }
}
