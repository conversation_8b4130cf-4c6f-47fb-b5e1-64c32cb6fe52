package com.xylink.manager.mapper.operationlog;

import com.xylink.config.aop.aspect.OperationLogSearchVO;
import com.xylink.config.aop.aspect.OperationLogVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-01-08 15:07
 */
@Mapper
public interface OperationLogMapper {

    /**
     * 保存持久化操作日志
     *
     * @param operationLogVO
     */
    void save(@Param("instance") OperationLogVO operationLogVO);

    /**
     * 批量持久化操作日志
     *
     * @param list
     */
    void batchSave(@Param("list") List<OperationLogVO> list);

    /**
     * 查询
     *
     * @param size
     * @param page
     * @param searchVO
     * @return
     */
    List<OperationLogVO> searchAndPageList(@Param("size") Integer size, @Param("page") Integer page, @Param("search") OperationLogSearchVO searchVO);

    /**
     * count
     *
     * @param searchVO
     * @return
     */
    long count(@Param("search") OperationLogSearchVO searchVO);

}
