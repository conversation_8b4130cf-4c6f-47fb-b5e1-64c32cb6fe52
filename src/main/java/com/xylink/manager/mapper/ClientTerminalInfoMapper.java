package com.xylink.manager.mapper;

import com.xylink.manager.model.ClientTerminalFrontInfo;
import com.xylink.manager.model.ClientTerminalInfo;
import com.xylink.manager.model.ClientTerminalResult;
import com.xylink.manager.model.TerminalFrontPlatform;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 客户端上传-获取终端配置信息
 * <AUTHOR>
 */
@Mapper
public interface ClientTerminalInfoMapper {

    List<ClientTerminalResult> getAllTerminalInfoList();

    List<ClientTerminalInfo> getAllClientTerminalInfoList(@Param("terminalType") String terminalType);

    List<TerminalFrontPlatform> getClientTerminalFrontPlatformList();

    List<Map<String, String>> getClientTerminalFrontInfoList();

    int removeAllTerminalInfo();

    int removeAllTerminalFrontPlatform();

    int removeAllTerminalFrontInfo();

    int batchAddAllTerminalInfo(@Param("terminalInfoList") List<ClientTerminalResult> terminalInfoList);

    int batchAddAllTerminalFrontPlatform(@Param("terminalFrontPlatformList") List<TerminalFrontPlatform> terminalFrontPlatformList);

    int batchAddAllTerminalFrontInfo(@Param("terminalFrontInfoList") List<ClientTerminalFrontInfo> terminalFrontInfoList);

}
