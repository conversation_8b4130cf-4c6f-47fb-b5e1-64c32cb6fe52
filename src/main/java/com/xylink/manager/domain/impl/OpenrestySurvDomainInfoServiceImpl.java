package com.xylink.manager.domain.impl;

import com.xylink.config.NetworkConstants;
import com.xylink.manager.service.api.NoahApiService;
import com.xylink.manager.service.base.IDeployService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/7/2
 */
@Service("private-openresty-survDomainInfoService")
public class OpenrestySurvDomainInfoServiceImpl extends AbstractOpenrestyDomainInfoService {
    public OpenrestySurvDomainInfoServiceImpl(IDeployService deployService, NoahApiService noahApiService) {
        super(deployService, noahApiService);
    }

    @Override
    String getDomainKey() {
        return NetworkConstants.SURV_DOMAIN_NAME;
    }

    @Override
    String getNginxPortKey() {
        return NetworkConstants.SURV_NGINX_PORT;
    }

    @Override
    String getNginxSslPortKey() {
        return NetworkConstants.SURV_NGINX_SSL_PORT;
    }

    @Override
    String getServiceName() {
        return "private-openresty-surv";
    }

    @Override
    String getNoahHttpsPortKey() {
        return null;
    }

    @Override
    String getNoahHttpPortKey() {
        return null;
    }

    @Override
    String getNoahDataId() {
        return null;
    }
}
