package com.xylink.manager.domain.impl;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.service.api.NoahApiService;
import com.xylink.manager.service.base.IDeployService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/7/2
 */
@Service(Constants.POD_NAME_OPENRESTY_MAIN + "DomainInfoService")
public class OpenrestyMainDomainInfoServiceImpl extends AbstractOpenrestyDomainInfoService {

    public OpenrestyMainDomainInfoServiceImpl(IDeployService deployService, NoahApiService noahApiService) {
        super(deployService, noahApiService);
    }

    @Override
    String getDomainKey() {
        return NetworkConstants.MAIN_DOMAIN_NAME;
    }

    @Override
    String getNginxPortKey() {
        return NetworkConstants.MAIN_NGINX_PORT;
    }

    @Override
    String getNginxSslPortKey() {
        return NetworkConstants.MAIN_NGINX_SSL_PORT;
    }

    @Override
    String getServiceName() {
        return Constants.POD_NAME_OPENRESTY_MAIN;
    }

    @Override
    String getNoahHttpsPortKey() {
        return "openresty-main.svc.https1_port";
    }

    @Override
    String getNoahHttpPortKey() {
        return "openresty-main.svc.http1_port";
    }

    @Override
    String getNoahDataId() {
        return "var_env.svc.yaml";
    }

}
