package com.xylink.manager.domain.impl;

import com.xylink.manager.service.api.NoahApiService;
import com.xylink.manager.service.base.IDeployService;
import org.codehaus.plexus.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/3
 */
public abstract class AbstractDomainBaseService {

    protected NoahApiService noahApiService;
    protected IDeployService deployService;

    public AbstractDomainBaseService(IDeployService deployService, NoahApiService noahApiService) {
        this.deployService = deployService;
        this.noahApiService = noahApiService;
    }

    abstract String getNoahHttpsPortKey();

    abstract String getNoahHttpPortKey();

    abstract String getNoahDataId();

    void savePortInfoToNoah(String httpPort, String httpsPort) {
        Map<String, Object> paramMap = new HashMap<>();
        Map<String, String> keyValues = new HashMap<>();
        keyValues.put(getNoahHttpPortKey(), httpPort);
        keyValues.put(getNoahHttpsPortKey(), httpsPort);
        paramMap.put("dataId", "var_env.svc.yaml");
        paramMap.put("keyValues", keyValues);
        paramMap.put("publish", "true");
        if (StringUtils.isNotBlank(getNoahHttpPortKey())) {
            noahApiService.notifyNginxPort(paramMap);
        }
    }

    void restartNginx(String serviceName) {
        deployService.deletePodByAppLabel(serviceName);
    }
}
