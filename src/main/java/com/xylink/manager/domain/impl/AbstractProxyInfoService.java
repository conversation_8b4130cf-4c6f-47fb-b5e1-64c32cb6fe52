package com.xylink.manager.domain.impl;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.ProxyConstants;
import com.xylink.manager.domain.IProxyInfoService;
import com.xylink.manager.domain.dto.DomainDTO;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.service.api.NoahApiService;
import com.xylink.manager.service.base.IDeployService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/7/2
 */
public abstract class AbstractProxyInfoService extends AbstractDomainBaseService implements IProxyInfoService {
    protected IDeployService deployService;
    protected NoahApiService noahApiService;

    public AbstractProxyInfoService(IDeployService deployService, NoahApiService noahApiService) {
        super(deployService, noahApiService);
        this.deployService = deployService;
        this.noahApiService = noahApiService;
    }

    @Override
    public List<DomainDTO> listProxyInfo() {
        List<DomainDTO> domainVOS = new ArrayList<>();
        // 获取所有main节点
        List<Pod> pods = getPodsByLables(getLabels());
        // 获取端口
        String httpPort = null;
        String httpsPort = null;
        if (StringUtils.isNotBlank(getNoahHttpPortKey())) {
            Map<String, String> portMap = noahApiService.selectVars(getNoahDataId(), new String[]{getNoahHttpPortKey(), getNoahHttpsPortKey()});
            httpPort = portMap.get(getNoahHttpPortKey());
            httpsPort = portMap.get(getNoahHttpsPortKey());
        }

        if (!CollectionUtils.isEmpty(pods)) {
            Map<String, String> proxyMap = deployService.getConfigMapByName(getConfigMapName()).getData();
            String finalHttpPort = httpPort;
            String finalHttpsPort = httpsPort;
            pods.forEach(pod -> {
                //通过pod名称获取域名
                String name = pod.getNodeName();
                // 获取域名
                String nodeDomainKey = name + NetworkConstants.SUFFIX_DOMAIN;
                String domain = proxyMap.get(nodeDomainKey);
                DomainDTO dataDTO = new DomainDTO();
                dataDTO.setDomain(domain);
                dataDTO.setNodeName(name);
                dataDTO.setServiceName(getServiceName());
                // 获取端口
                if (StringUtils.isBlank(getNoahHttpPortKey())) {
                    dataDTO.setHttpPort(proxyMap.get(name + getNginxPortSuffix()));
                    dataDTO.setHttpsPort(proxyMap.get(name + getNginxSSLPortSuffix()));
                } else {
                    dataDTO.setHttpPort(finalHttpPort);
                    dataDTO.setHttpsPort(finalHttpsPort);
                }
                domainVOS.add(dataDTO);
            });
        }
        return domainVOS;
    }

    @Override
    public void saveProxyInfo(DomainDTO domainVO) {
        saveToCm(domainVO);
        savePortInfoToNoah(domainVO.getHttpPort(), domainVO.getHttpsPort());
        restartNginx(getServiceName());
    }

    abstract String[] getLabels();

    abstract String getConfigMapName();

    String getNginxPortSuffix() {
        return ProxyConstants.NGINX_PORT;
    }

    String getNginxSSLPortSuffix() {
        return ProxyConstants.NGINX_SSL_PORT;
    }



    private void saveToCm(DomainDTO domainVO) {
        Map<String, String> proxyMap = new HashMap<>();
        proxyMap.put(domainVO.getNodeName() + NetworkConstants.SUFFIX_DOMAIN, domainVO.getDomain());
        if (isSavePortToCm()) {
            proxyMap.put(domainVO.getNodeName() + getNginxPortSuffix(), domainVO.getHttpPort());
            proxyMap.put(domainVO.getNodeName() + getNginxSSLPortSuffix(), domainVO.getHttpsPort());
        }
        deployService.patchConfigMap(getConfigMapName(), Constants.NAMESPACE_DEFAULT, d -> d.putAll(proxyMap));
    }

    boolean isSavePortToCm() {
        return false;
    }


    private List<Pod> getPodsByLables(String[] labels) {
        //给数组中添加上"private-"前缀
        String[] labelsWithPrefix = Arrays.stream(labels).map(label -> "private-" + label).toArray(String[]::new);
        return deployService.listPodsByAppLabels(Arrays.asList(labelsWithPrefix));
    }

    abstract String getServiceName();
}
