package com.xylink.manager.domain.impl;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.domain.dto.DomainDTO;
import com.xylink.manager.service.api.NoahApiService;
import com.xylink.manager.service.base.IDeployService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/2
 */
@Service("private-openresty-webrtcDomainInfoService")
public class OpenrestyWebrtcDomainInfoServiceImpl extends AbstractOpenrestyDomainInfoService {
    public OpenrestyWebrtcDomainInfoServiceImpl(IDeployService deployService, NoahApiService noahApiService) {
        super(deployService, noahApiService);
    }

    @Override
    public DomainDTO getDomainInfo() {
        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();
        DomainDTO domainDTO = new DomainDTO();
        String domain = allIpMap.get(NetworkConstants.WEBRTC_DOMAIN_NAME);
        domainDTO.setDomain(domain);
        Map<String, String> allOpenrestyWebrtcMap = deployService.getConfigMapByName("all-openresty-webrtc").getData();
        String webrtcHttpPort = allOpenrestyWebrtcMap.get("WEBRTC_NGINX_PORT");
        String webrtcHttpsPort = allOpenrestyWebrtcMap.get("WEBRTC_NGINX_SSL_PORT");
        domainDTO.setHttpPort(webrtcHttpPort);
        domainDTO.setHttpsPort(webrtcHttpsPort);
        domainDTO.setServiceName(getServiceName());
        domainDTO.setNodeName(getNodeName());
        return domainDTO;
    }

    @Override
    public void saveDomainInfo(DomainDTO domainVO) {
        Map<String, String> allIpMap = new HashMap<>();
        allIpMap.put(NetworkConstants.WEBRTC_DOMAIN_NAME, domainVO.getDomain());
        Map<String, String> allWebrtcMap = new HashMap<>();
        allWebrtcMap.put(NetworkConstants.WEBRTC_NGINX_PORT, domainVO.getHttpPort());
        allIpMap.put(NetworkConstants.WEBRTC_NGINX_SSL_PORT, domainVO.getHttpsPort());
        deployService.patchConfigMapAllIpForAddData(allIpMap);
        deployService.patchConfigMap("all-openresty-webrtc", Constants.NAMESPACE_DEFAULT, d -> d.putAll(allWebrtcMap));
    }

    @Override
    String getDomainKey() {
        return NetworkConstants.WEBRTC_DOMAIN_NAME;
    }

    @Override
    String getNginxPortKey() {
        return NetworkConstants.WEBRTC_NGINX_PORT;
    }

    @Override
    String getNginxSslPortKey() {
        return NetworkConstants.WEBRTC_NGINX_SSL_PORT;
    }

    @Override
    String getServiceName() {
        return "private-openresty-webrtc";
    }

    @Override
    String getNoahHttpsPortKey() {
        return null;
    }

    @Override
    String getNoahHttpPortKey() {
        return null;
    }

    @Override
    String getNoahDataId() {
        return null;
    }
}
