package com.xylink.manager.domain.impl;

import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.api.NoahApiService;
import com.xylink.manager.service.base.IDeployService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/7/2
 */
@Service("private-main-proxyInfoService")
public class MainProxyInfoServiceImpl extends AbstractProxyInfoService {
    public MainProxyInfoServiceImpl(IDeployService deployService, NoahApiService noahApiService) {
        super(deployService, noahApiService);
    }

    @Override
    String[] getLabels() {
        return new String[]{Labels.main_proxy.label()};
    }

    @Override
    String getConfigMapName() {
        return "all-main-proxy";
    }

    @Override
    String getServiceName() {
        return "private-main-proxy";
    }

    @Override
    String getNoahHttpsPortKey() {
        return "main-proxy.svc.https_port";
    }

    @Override
    String getNoahHttpPortKey() {
        return "main-proxy.svc.http_port";
    }

    @Override
    String getNoahDataId() {
        return "var_env.svc.yaml";
    }
}
