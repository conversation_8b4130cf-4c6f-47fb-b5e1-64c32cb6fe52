package com.xylink.manager.domain.impl;

import com.xylink.config.NetworkConstants;
import com.xylink.manager.domain.IDomainInfoService;
import com.xylink.manager.domain.dto.DomainDTO;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.service.api.NoahApiService;
import com.xylink.manager.service.base.IDeployService;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/2
 */
public abstract class AbstractOpenrestyDomainInfoService extends AbstractDomainBaseService implements IDomainInfoService {
    protected final IDeployService deployService;
    protected final NoahApiService noahApiService;

    public AbstractOpenrestyDomainInfoService(IDeployService deployService, NoahApiService noahApiService) {
        super(deployService, noahApiService);
        this.deployService = deployService;
        this.noahApiService = noahApiService;
    }

    public DomainDTO getDomainInfo() {
        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();
        DomainDTO domainVO = new DomainDTO();
        domainVO.setDomain(allIpMap.get(getDomainKey()));
        domainVO.setHttpPort(allIpMap.get(getNginxPortKey()));
        domainVO.setHttpsPort(allIpMap.get(getNginxSslPortKey()));
        domainVO.setServiceName(getServiceName());
        domainVO.setNodeName(getNodeName());
        return domainVO;
    }

    public void saveDomainInfo(DomainDTO domainVO) {
        saveToCm(domainVO);
        savePortInfoToNoah(domainVO.getHttpPort(), domainVO.getHttpsPort());
        restartNginx(getServiceName());
    }

    private void saveToCm(DomainDTO domainVO) {
        Map<String, String> allIpMap = new HashMap<>();
        allIpMap.put(NetworkConstants.MAIN_DOMAIN_NAME, domainVO.getDomain());
        allIpMap.put(NetworkConstants.MAIN_NGINX_PORT, domainVO.getHttpPort());
        allIpMap.put(NetworkConstants.MAIN_NGINX_SSL_PORT, domainVO.getHttpsPort());
        deployService.patchConfigMapAllIpForAddData(allIpMap);
    }

    abstract String getDomainKey();

    abstract String getServiceName();

    abstract String getNginxPortKey();

    abstract String getNginxSslPortKey();

    final String getNodeName() {
        // 获取所有节点
        List<Pod> pods = deployService.listPodsByAppLabels(Collections.singletonList(getServiceName()));
        List<String> nodeNames = pods.stream().map(Pod::getNodeName).collect(Collectors.toList());
        //nodeNames变成逗号分隔的字符串
        return !nodeNames.isEmpty() ? String.join(",", nodeNames) : null;
    }
}
