package com.xylink.manager.domain.impl;

import com.xylink.config.Constants;
import com.xylink.manager.domain.DomainInfoFactory;
import com.xylink.manager.domain.IDomainService;
import com.xylink.manager.domain.dto.DomainDTO;
import com.xylink.manager.service.base.SystemModeConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2025/7/1
 */
@Service
@Slf4j
public class DomainServiceImpl implements IDomainService {
    private static final String[] ALL_NGINX_TYPES = {Constants.POD_NAME_OPENRESTY_MAIN, "private-openresty-vod",
            "private-openresty-webrtc", "private-openresty-surv"};
    private static final String[] ALL_PROXY_TYPES = {
            Constants.POD_NAME_MAIN_PROXY, "private-vodnetwork-proxy", Constants.POD_NAME_VOD_PROXY,
            "private-webrtc-proxy"};

    @Override
    public List<DomainDTO> listAllRunningNginx() {
        if (!SystemModeConfig.isPrivate56()) {
            log.warn("env is not 5.6 so return empty list");
            return new ArrayList<>();
        }
        List<DomainDTO> domainVOS = new ArrayList<>();
        //ALL_NGINX_TYPES 获取所有openresty域名端口信息
        Arrays.asList(ALL_NGINX_TYPES).forEach(type -> domainVOS.add(DomainInfoFactory.getDomainNameService(type).getDomainInfo()));

        //ALL_PROXY_TYPES 获取所有proxy域名端口信息
        Arrays.asList(ALL_PROXY_TYPES).forEach(proxyType -> domainVOS.addAll(DomainInfoFactory.getProxyInfoService(proxyType).listProxyInfo()));

        return domainVOS;
    }

    @Override
    public void saveDomainAndPort(DomainDTO domainVO) {
        if (!SystemModeConfig.isPrivate56()) {
            log.warn("env is not 5.6 so don't save");
            return;
        }
        if (domainVO.getServiceName().contains("proxy")) {
            DomainInfoFactory.getProxyInfoService(domainVO.getServiceName()).saveProxyInfo(domainVO);
        } else {
            DomainInfoFactory.getDomainNameService(domainVO.getServiceName()).saveDomainInfo(domainVO);
        }
    }
}
