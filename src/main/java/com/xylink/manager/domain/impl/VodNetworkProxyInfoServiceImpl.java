package com.xylink.manager.domain.impl;

import com.xylink.config.Constants;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.api.NoahApiService;
import com.xylink.manager.service.base.IDeployService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/7/2
 */
@Service("private-vodnetwork-proxyInfoService")
public class VodNetworkProxyInfoServiceImpl extends AbstractProxyInfoService {
    public VodNetworkProxyInfoServiceImpl(IDeployService deployService, NoahApiService noahApiService) {
        super(deployService, noahApiService);
    }

    @Override
    String[] getLabels() {
        return new String[]{Labels.vodnetwork_proxy.label()};
    }

    @Override
    String getConfigMapName() {
        return Constants.CONFIGMAP_VODNETWORK_PROXY;
    }

    @Override
    String getServiceName() {
        return "private-vodnetwork-proxy";
    }

    @Override
    String getNoahHttpsPortKey() {
        return "vodnetwork-proxy.svc.https_port";
    }

    @Override
    String getNoahHttpPortKey() {
        return "vodnetwork-proxy.svc.http_port";
    }

    @Override
    String getNoahDataId() {
        return "var_env.svc.yaml";
    }
}
