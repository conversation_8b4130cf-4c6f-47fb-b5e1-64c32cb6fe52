package com.xylink.manager.domain.impl;

import com.xylink.config.NetworkConstants;
import com.xylink.manager.service.api.NoahApiService;
import com.xylink.manager.service.base.IDeployService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/7/2
 */
@Service("private-openresty-vodDomainInfoService")
public class OpenrestyVodDomainInfoServiceImpl extends AbstractOpenrestyDomainInfoService {
    public OpenrestyVodDomainInfoServiceImpl(IDeployService deployService, NoahApiService noahApiService) {
        super(deployService, noahApiService);
    }

    @Override
    String getDomainKey() {
        return NetworkConstants.VOD_DOMAIN_NAME;
    }

    @Override
    String getNginxPortKey() {
        return NetworkConstants.VOD_NGINX_PORT;
    }

    @Override
    String getNginxSslPortKey() {
        return NetworkConstants.VOD_NGINX_SSL_PORT;
    }

    @Override
    String getServiceName() {
        return "private-openresty-vod";
    }

    @Override
    String getNoahHttpsPortKey() {
        return "openresty-vod.svc.https_port";
    }

    @Override
    String getNoahHttpPortKey() {
        return "openresty-vod.svc.http_port";
    }

    @Override
    String getNoahDataId() {
        return "var_env.svc.yaml";
    }
}
