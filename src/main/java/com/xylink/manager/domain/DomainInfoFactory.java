package com.xylink.manager.domain;

import com.xylink.util.SpringBeanUtil;

/**
 * <AUTHOR>
 * @date 2025/7/2
 */
public class DomainInfoFactory {
    public static IDomainInfoService getDomainNameService(String type) {
        return SpringBeanUtil.getBean(type + "DomainInfoService");
    }

    public static IProxyInfoService getProxyInfoService(String type) {
        return SpringBeanUtil.getBean(type + "InfoService");
    }
}
