package com.xylink.manager.domain;

import com.xylink.manager.domain.dto.DomainDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/1
 */
@RestController
@RequestMapping("/external/domain")
@Validated
@Slf4j
public class DomainController {
    private final IDomainService domainService;

    public DomainController(IDomainService domainService) {
        this.domainService = domainService;
    }

    // 查询要配置的nginx服务列表
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public List<DomainDTO> list() {
        return domainService.listAllRunningNginx();
    }

    // 保存域名端口配置
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public void save(@RequestBody DomainDTO domainDTO) {
        domainService.saveDomainAndPort(domainDTO);
    }
}
