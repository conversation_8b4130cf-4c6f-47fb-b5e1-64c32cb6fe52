package com.xylink.manager.handler;


import com.xylink.config.Constants;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.handler.node.BaseHandler;
import com.xylink.manager.handler.node.NodeHandler;
import com.xylink.manager.model.DeployMessage;
import com.xylink.manager.model.cm.ICMDto;
import com.xylink.manager.service.IptablesService;
import com.xylink.util.JDBCUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 处理node节点配置
 */
@Service
public class NodeHelper {

    private static final Logger logger = LoggerFactory.getLogger(NodeHelper.class);

    private Pattern linePattern = Pattern.compile("[_-](\\w)");

    private final String packagePath = "com.xylink.manager.handler.node.";

    @Autowired
    private JDBCUtils jdbcUtils;
    @Autowired
    private IptablesService iptablesService;

    /**
     * 根据node type生成类名，反射实例化 获取对应的业务处理逻辑。
     * type转类名：下划线或横线转驼峰，首字母大写并拼接Handler。
     * e.g. openresty-main --> OpenrestyMainHandler
     */
    public boolean deployConfigure(NodeDto node) throws Exception {
        return generateHandler(node.getType()).configure(node, jdbcUtils);
    }

    /**
     * 根据node type进行节点部署
     */
    public boolean defaultDeployConfigure(DeployMessage deployMessage) throws Exception {
        try{
            boolean isSuccess = generateHandler(deployMessage.getType()).defaultConfigure(deployMessage, jdbcUtils);
            logger.info("defaultDeployConfigure isSuccess:{}", isSuccess);
            return isSuccess;
        }catch (Exception e) {
            logger.error("defaultDeployConfigure error", e);
            return false;
        }finally {
            iptablesService.updateAndRefreshIptables4Node(deployMessage.getIp());
        }
    }

    private NodeHandler generateHandler(String nodeType) throws Exception {
        String type;
        if(nodeType.endsWith("-x86")) {
            type = nodeType.replace("-x86","");
        }else if(nodeType.endsWith(Constants.ARM)) {
            type = nodeType.replace(Constants.ARM,"");
        }else {
            type = lineToHump(nodeType).replaceAll("\\.", "");
        }

        type = type.substring(0, 1).toUpperCase() + type.substring(1);
        String path = packagePath + type + "Handler";

        NodeHandler nodeHandler;
        try {
            nodeHandler = (NodeHandler) Class.forName(path).newInstance();
        } catch (ClassNotFoundException e) {
            //默认处理流程
            nodeHandler = new BaseHandler();
        } catch (Exception e) {
            throw e;
        }
        return nodeHandler;
    }


    public String lineToHump(String str) {
        Matcher matcher = linePattern.matcher(str);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, matcher.group(1).toUpperCase());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }


    /**
     * 配置服务的高级配置
     */
    public <T extends ICMDto> boolean advanceConfigure(T t, String configmap, String type, String nodeName) throws Exception {
        return generateHandler(type).serviceAdvanceConfigure(t, configmap, jdbcUtils, type, nodeName);
    }
}



