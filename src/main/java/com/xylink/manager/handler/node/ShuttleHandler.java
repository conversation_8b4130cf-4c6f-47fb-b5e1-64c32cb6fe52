package com.xylink.manager.handler.node;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * shuttle node 配置处理实现
 *
 * <AUTHOR>
 */
public class ShuttleHandler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(ShuttleHandler.class);


    @Override
    public NodeHandler configureConfigMap() {
        if (node == null) {
            return this;
        }
        configureDistributeIP(node);

        return this;

    }


}
