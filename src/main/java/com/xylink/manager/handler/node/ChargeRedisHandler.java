package com.xylink.manager.handler.node;

import com.xylink.config.NetworkConstants;
import com.xylink.manager.model.em.Labels;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * charge redis node 配置处理实现.
 * charge redis 如果没有部署，需要把ip还原
 */
public class ChargeRedisHandler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(ChargeRedisHandler.class);


    @Override
    public NodeHandler configureConfigMap() {
        if(node==null) return this;

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();

        if (node.getLabelMap().get(Labels.charge_redis.label())) {
            allIpMap.put(NetworkConstants.CHARGE_REDIS_IP, node.getInternalIp());
        }else{
            String ip = getDistributeIp(Labels.charge_redis.label());
            allIpMap.put(NetworkConstants.CHARGE_REDIS_IP, StringUtils.isNotBlank(ip) ? ip : "127.0.0.1");
        }

        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);

        return this;
    }
}
