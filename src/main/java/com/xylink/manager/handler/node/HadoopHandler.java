package com.xylink.manager.handler.node;

import com.xylink.config.NetworkConstants;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.AuditLogService;
import com.xylink.manager.service.ConfigService;
import com.xylink.manager.service.db.JasyptService;
import com.xylink.manager.service.remote.buffet.BuffetRemoteClient;
import com.xylink.util.SecurityContextUtil;
import com.xylink.util.SpringBeanUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class HadoopHandler  extends NodeHandler{
    private final static Logger logger = LoggerFactory.getLogger(HadoopHandler.class);

    @Override
    protected NodeHandler configureConfigMap() {
        if (node == null) {
            return this;
        }

        configureDistributeIP(node);

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();

        allIpMap.put("AZKABAN_IP", node.getInternalIp());
        allIpMap.put("HADOOP_IP", node.getInternalIp());
        String switchStatus = allIpMap.get(NetworkConstants.DB_PASSWORD_ENCRYPT_SWITCH);
        JasyptService jasyptService = SpringBeanUtil.getBean(JasyptService.class);
        // 仅国产数据库（达梦/神通/金仓），需要勾选；当主系统数据库是mysql、oceanbase时，无需勾选
        if (node.getLabelMap().containsKey(Labels.hadoop_db.label()) && node.getLabelMap().get(Labels.hadoop_db.label())) {
            allIpMap.put("STATIS_ETL_DATABASE_IP", node.getInternalIp());
            allIpMap.put("STATIS_ETL_DATABASE_PORT", "3306");
            String value ="Da?548!YZ";
            if (StringUtils.isNotBlank(switchStatus) && Boolean.parseBoolean(switchStatus)) {
                value = jasyptService.encrypt(value);
            }
            allIpMap.put("STATIS_ETL_DB_PASSWORD", value);
        }

        deployService.patchConfigMapAllIpForAddData(allIpMap);

        return this;
    }

    @Override
    protected NodeHandler afterConfigure() {
        AuditLogService auditLogService = SpringBeanUtil.getBean(AuditLogService.class);
        BuffetRemoteClient buffetRemoteClient = SpringBeanUtil.getBean(BuffetRemoteClient.class);
        try {
            boolean exist = jdbcUtils.saveBuffetConfigDict(ConfigService.DATACENTER_SETTING_CONFIG.getConfigName(), ConfigService.DATACENTER_SETTING_CONFIG.getName());
            buffetRemoteClient.saveEnterpriseConfig(ConfigService.DATACENTER_SETTING_CONFIG.getConfigName(), ConfigService.DATACENTER_SETTING_CONFIG.getConfigValue());
            auditLogService.saveEnterpriseAuditLog(ConfigService.DATACENTER_SETTING_CONFIG.getName(), ConfigService.DATACENTER_SETTING_CONFIG.getConfigName(), ConfigService.DATACENTER_SETTING_CONFIG.getConfigValue(), ConfigService.DATACENTER_SETTING_CONFIG.getConfigValue(), exist ? "添加" : "更新", SecurityContextUtil.currentUser());
        } catch (Exception e) {
            logger.error("Save config:{} error.", ConfigService.DATACENTER_SETTING_CONFIG, e);
        }
        return this;
    }
}
