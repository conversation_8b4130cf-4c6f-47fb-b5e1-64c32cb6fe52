package com.xylink.manager.handler.node.mid;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.model.em.DefaultConfigmapDataEnum;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.clustersetting.failover.telnet.TelnetClient;
import com.xylink.util.IdPoolUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 如果选择的服务中是 kafka_cluster 才会真正执行改逻辑 如果是kafka服务则走之前的逻辑 {@link com.xylink.manager.handler.node.BaseHandler#configureConfigMap()}
 *
 * <AUTHOR>
 * @since 2021/11/15 6:58 下午
 */
public class KafkaHandlerAdapter extends AbstractMidHandlerAdapter implements MidHandlerAdapter {

    private static final Logger logger = LoggerFactory.getLogger(KafkaHandlerAdapter.class);
    private static final String ALL_KAFKA_CONFIGMAP = "all-kafka";
    private static final String[] KAFKA_ID_POOL;

    static {
        KAFKA_ID_POOL = new String[]{"1", "2", "3"};
    }

    @Override
    public void doConfigureConfigMap(NodeDto node) {
        //all-ip配置
        Map<String, String> allIpMap = getDeployService().getConfigMapAllIp().getData();
        // 如果改节点部署的是kafka  则忽略kafka_cluster
        List<String> enableLabels = node.getLabelMap().entrySet().stream().filter(Map.Entry::getValue).map(Map.Entry::getKey).collect(Collectors.toList());
        if (enableLabels.contains(Labels.kafka.label())) {
            //内网ip
            String networkName = Constants.interIps.get(node.getType());
            if (StringUtils.isNotEmpty(networkName)) {
                allIpMap.put(networkName, node.getInternalIp());
            }
            //外网ip
            String networkPubName = Constants.exterIps.get(node.getType());
            if (StringUtils.isNotEmpty(networkPubName)) {
                allIpMap.put(networkPubName, StringUtils.isBlank(node.getExternalIp()) ? node.getInternalIp() : node.getExternalIp());
            }
            allIpMap.put(NetworkConstants.MASTER_KAFKA_INTERNAL_IP, node.getInternalIp());
            logger.info("update all-ip: {}", allIpMap);
            getDeployService().patchConfigMapAllIpForAddData(allIpMap);
            return;
        }
        clearNodesRecordInAllConfigMapWithoutLabel(Labels.kafka_cluster, ALL_KAFKA_CONFIGMAP);
        // all-kafka 配置
        if (enableLabels.contains(Labels.kafka_cluster.label())) {
            handleDistributeIP(node, Labels.kafka.label());
            setKafkaBrokerId(node);
        }
        // 非集群模式配置 KAFKA_INTERNAL_IP: **************
        // 集群模式配置   KAFKA_INTERNAL_IP: **************:9093,**************:9093,**************
        // 需要设置 MASTER_KAFKA_INTERNAL_IP
        Pair<Set<String>, Set<String>> ips = getNodeInternalIpAndPublicIpByLabel(Labels.kafka.label(), "private-kafka-cluster");
        Set<String> kafkaPublicIps = ips.getLeft();
        Set<String> kafkaInternalIps = ips.getRight();
        if (!CollectionUtils.isEmpty(kafkaPublicIps)) {
            allIpMap.put(NetworkConstants.KAFKA_PUBLIC_IP, StringUtils.join(kafkaPublicIps, ":9093,"));
        }
        if (!CollectionUtils.isEmpty(kafkaInternalIps)) {
            allIpMap.put(NetworkConstants.KAFKA_INTERNAL_IP, StringUtils.join(kafkaInternalIps, ":9093,"));
        }
        allIpMap.put(NetworkConstants.MASTER_KAFKA_INTERNAL_IP, getRandomIp(kafkaInternalIps, node));
        logger.info("update all-ip: {}", allIpMap);
        getDeployService().patchConfigMapAllIpForAddData(allIpMap);
    }

    private Map<String, String> setKafkaBrokerId(NodeDto node) {
        String configmapName = ALL_KAFKA_CONFIGMAP;
        ConfigMap configMap = getDeployService().getConfigMapByName(configmapName, Constants.NAMESPACE_DEFAULT);
        //没有该all-* configmap 则自动创建
        Map<String, String> configmapData = configMap == null ? DefaultConfigmapDataEnum.initDefault(Labels.kafka.label()) : configMap.getData();
        numberInConfigMap(configmapData, node);
        getDeployService().patchConfigMap(configmapName, Constants.NAMESPACE_DEFAULT, d -> {
            d.putAll(configmapData);
        });
        return configmapData;
    }

    /**
     * HOSTNAME-KAFKA_ID : 每个kafka实例对应的broker.id值
     */
    private void numberInConfigMap(Map<String, String> allKafkaConfigMap, NodeDto node) {
        // key
        String key = node.getName() + "-KAFKA_ID";
        if (allKafkaConfigMap.containsKey(key)) {
            return;
        }
        // 获取部署的kafka, 从池中取【1，2，3】
        List<String> inUsed = new ArrayList<>();
        allKafkaConfigMap.forEach((itemKey, itemValue) -> {
            if (itemKey.endsWith("-KAFKA_ID")) {
                inUsed.add(itemValue);
            }
        });
        allKafkaConfigMap.put(key, IdPoolUtils.getId(KAFKA_ID_POOL, inUsed));
    }

    private String getRandomIp(Set<String> kafkaInternalIps, NodeDto node) {
        if (!CollectionUtils.isEmpty(kafkaInternalIps)) {
            for (String ip : kafkaInternalIps) {
                if (new TelnetClient(ip, 9093).tryTelnet()) {
                    return ip;
                }
            }
        }
        return node.getInternalIp();
    }
}
