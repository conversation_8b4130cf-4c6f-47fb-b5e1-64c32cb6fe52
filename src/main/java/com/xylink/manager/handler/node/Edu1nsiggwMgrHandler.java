package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.util.JDBCUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

public class Edu1nsiggwMgrHandler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(Edu1nsiggwMgrHandler.class);

    @Override
    protected NodeHandler configureConfigMap() {
        if (node == null) return this;
        super.configureDistributeIP(node);

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();
        List<String> enableLabels = node.getLabelMap()
                .entrySet()
                .stream()
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        //内网服务地址
        enableLabels.stream().filter(Constants.interIps::containsKey).forEach(label -> allIpMap.put(Constants.interIps.get(label), node.getInternalIp()));
        logger.info("update all-ip: {}", allIpMap.toString());
        deployService.patchConfigMapAllIpForAddData(allIpMap);
        return this;
    }

//    @Override
//    protected NodeHandler afterConfigure() {
//        if ( jdbcUtils==null || client==null) return this;
//        configureSN(node, client, jdbcUtils);
//        return this;
//    }

    public void configureSN(NodeDto node, JDBCUtils jdbcUtils) {
        ConfigMap configMap = deployService.getConfigMapByName(Constants.CONFIGMAP_EDU_SIPPLUSMGR, Constants.NAMESPACE_DEFAULT);
        Map<String, String> data = configMap.getData();
        String sn = data.get(node.getName() + "-SIPPLUSMGR-SN");
        if (StringUtils.isBlank(sn)) {
            sn = UUID.randomUUID().toString();
            data.put(node.getName() + "-SIPPLUSMGR-SN", sn);

            jdbcUtils.configureSipplusGatewaySN(sn);
            deployService.patchConfigMap(Constants.CONFIGMAP_EDU_SIPPLUSMGR, Constants.NAMESPACE_DEFAULT, d -> {
                d.putAll(data);
            });
        }
    }
}
