package com.xylink.manager.handler.node;

import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.em.Labels;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022/12/23 5:06 下午
 */
public class NmsaHandler extends NodeHandler {
    @Override
    protected NodeHandler configureConfigMap() {
        if (node == null) {
            return this;
        }

        handleDistributeIP(node, Labels.nmsa.label());

        return this;
    }

    @Override
    protected NodeHandler initDefaultNodeConfig() {

        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setExternalIp(deployMessage.getIp());
        node.setDomain(deployMessage.getIp());
        node.setReportInternalIp(deployMessage.getIp());
        node.setType(Labels.nmsa.label());

        Map<String, Boolean > labelMap = new HashMap<>();
        DefaultDeployStructureEnumInvoke.services(node.getType()).forEach(label -> labelMap.put(label, false));
        node.setLabelMap(labelMap);
        super.node = node;
        return this;
    }
}
