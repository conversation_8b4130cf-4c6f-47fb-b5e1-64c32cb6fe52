package com.xylink.manager.handler.node;

import com.xylink.manager.model.em.Labels;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: liyang
 * @DateTime: 2024/5/9 4:41 下午
 **/
@Slf4j
public class BasicChargeHandler extends NodeHandler {

    @Override
    protected NodeHandler configureConfigMap() {
        if (node == null) {
            return this;
        }

        configureDistributeIP(node);
        handleDistributeIP(node, Labels.basic_charge.label());

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();
        List<String> enableLabels = node.getLabelMap().entrySet().stream().filter(Map.Entry::getValue).map(Map.Entry::getKey).collect(Collectors.toList());

        if (enableLabels.contains(Labels.inspection.label())) {
            allIpMap.put("INSPECTION_IP", node.getInternalIp());
        }
        if (enableLabels.contains(Labels.message_push.label())) {
            allIpMap.put("MESSAGE_PUSH_IP", node.getInternalIp());
        }
        allIpMap.put(Labels.basic_base.loadblanceKey(),getDistributeIpByType(Labels.basic_charge.label()));
        //多节点服务
        DefaultDeployStructureEnumInvoke.services(Labels.basic_charge.label())
                .stream()
                .map(Labels::labelOf)
                .filter(Labels::isDistributed)
                .filter(label -> StringUtils.isNotBlank(label.loadblanceKey()))
                .forEach(label -> allIpMap.put(label.loadblanceKey(), getDistributeIp(label.label())));


        log.info("update all-ip: " + allIpMap.toString());
        deployService.patchConfigMapAllIpForAddData(allIpMap);

        updatePresenceServer(enableLabels);
        // 241220-分区云5.2启用点对点号码池
        updateP2PPoolNumber(enableLabels);

        return this;
    }

    /**
     * 和标准流程不一样的是  默认不部署该节点下的服务
     *
     * @return
     */
    @Override
    protected NodeHandler initDefaultNodeConfig() {
        // 默认部署了改节点类型下的所有服务
        super.initDefaultNodeConfig();
        Map<String, Boolean> labels = new HashMap<>();
        String type = deployMessage.getType();
        DefaultDeployStructureEnumInvoke.services(type).forEach(label -> labels.put(label, type.equals(label)));
        this.node.setLabelMap(labels);
        return this ;
    }
}
