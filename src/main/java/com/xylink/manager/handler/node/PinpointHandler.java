package com.xylink.manager.handler.node;

import com.xylink.config.NetworkConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * pinpoint node 配置处理实现
 */
public class PinpointHandler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(PinpointHandler.class);


    @Override
    public NodeHandler configureConfigMap() {
        if(node==null) return this;
//        configureDistributeIP(node);

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();

        allIpMap.put(NetworkConstants.PINPOINT_IP, node.getInternalIp());
        allIpMap.put(NetworkConstants.PINPOINT_HBASE_IP, node.getInternalIp());

        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);
        return this;

    }
}
