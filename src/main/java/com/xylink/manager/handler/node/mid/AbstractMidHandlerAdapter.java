package com.xylink.manager.handler.node.mid;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.model.deploy.Node;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.model.em.DefaultConfigmapDataEnum;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.util.SpringBeanUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/9/5 12:00 PM
 */
public abstract class AbstractMidHandlerAdapter implements MidHandlerAdapter {
    protected Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public void configureConfigMap(NodeDto node) {
        logStartConfig();
        doConfigureConfigMap(node);
        logEndConfig();
    }

    IDeployService getDeployService() {
        return SpringBeanUtil.getBean(IDeployService.class);
    }

    protected abstract void doConfigureConfigMap(NodeDto node);

    protected void logStartConfig() {
        logger.info("Begin configureConfigMap");
    }

    protected void logEndConfig() {
        logger.info("End configureConfigMap");
    }

    /**
     * 返回包含指定标签的节点的IP信息 left:publicIp right:internalIp
     * edit by zgj 2022年06月09日11:23:29
     * 1、k8s 中获取部署节点hostname
     * 2、根据hostname 到对应的configmap: all-xxx 获取内网IP 、外网IP
     *
     * @param appName metadata:labels:app
     */
    protected Pair<Set<String>, Set<String>> getNodeInternalIpAndPublicIpByLabel(String label, String appName) {
        if (StringUtils.isBlank(label)) {
            return ImmutablePair.of(Collections.emptySet(), Collections.emptySet());
        }
        List<Pod> pods = getDeployService().listPodsByAppLabel(appName);
        if (CollectionUtils.isEmpty(pods)) {
            return ImmutablePair.of(Collections.emptySet(), Collections.emptySet());
        }
        List<String> nodeNames = new ArrayList<>();
        pods.forEach(item ->
                nodeNames.add(item.getNodeName())
        );
        String configmapName = "all-" + label;
        ConfigMap configMap = getDeployService().getConfigMapByName(configmapName, Constants.NAMESPACE_DEFAULT);
        Map<String, String> configmapData = configMap == null ? DefaultConfigmapDataEnum.initDefault(label) : configMap.getData();
        Set<String> publicIps = new HashSet<>();
        Set<String> internalIps = new HashSet<>();
        nodeNames.forEach(nodeName -> {
            String internalIp = configmapData.get(nodeName + NetworkConstants.SUFFIX_INTERNAL_IP);
            if (StringUtils.isNotBlank(internalIp)) {
                internalIps.add(internalIp);
            }
            String publicIp = configmapData.get(nodeName + NetworkConstants.SUFFIX_PUBLIC_IP);
            if (StringUtils.isNotBlank(publicIp)) {
                publicIps.add(publicIp);
            }
        });
        return ImmutablePair.of(publicIps, internalIps);
    }

    /**
     * clear nodes record in allConfigMap without label
     */
    protected Map<String, String> clearNodesRecordInAllConfigMapWithoutLabel(Labels label, String configMapName) {
        ConfigMap configMap = getDeployService().getConfigMapByName(configMapName, Constants.NAMESPACE_DEFAULT);
        if (configMap == null) {
            return Collections.emptyMap();
        }
        Map<String, String> allConfigMap = configMap.getData();
        Set<String> runningApp = new HashSet<>();
        List<Node> nodeList = getDeployService().listNodesByAppLabel(label.label());
        nodeList.forEach(node1 ->
                runningApp.add(node1.getName())
        );
        allConfigMap.entrySet().removeIf(entry -> {
            if (label.equals(Labels.zookeeper_cluster) && entry.getKey().startsWith("ZK_")) {
                return false;
            }
            boolean remove = true;
            for (String key : runningApp) {
                if (entry.getKey().startsWith(key)) {
                    remove = false;
                    break;
                }
            }
            return remove;
        });
        getDeployService().patchConfigMap(configMapName, Constants.NAMESPACE_DEFAULT, d -> {
            d.putAll(allConfigMap);
        });
        logger.info("Clear nodes record in allConfigMap without label:{} now data is:{}", label.label(), allConfigMap);
        return allConfigMap;
    }

    protected void handleDistributeIP(NodeDto nodeDto, String label) {
        String configmapName = "all-" + label;
        if (label.endsWith("-x86")) {
            configmapName = "all-" + label.replace("-x86", "");
        }
        ConfigMap configMap = getDeployService().getConfigMapByName(configmapName, Constants.NAMESPACE_DEFAULT);

        //没有该all-* configmap 则自动创建
        Map<String, String> configmapData = configMap == null ? DefaultConfigmapDataEnum.initDefault(label) : configMap.getData();

        String nodeInterIpKey = nodeDto.getName() + NetworkConstants.SUFFIX_INTERNAL_IP;
        String nodePubIpKey = nodeDto.getName() + NetworkConstants.SUFFIX_PUBLIC_IP;

        configmapData.put(nodeInterIpKey, nodeDto.getInternalIp());
        configmapData.put(nodePubIpKey, nodeDto.getExternalIp());
        configmapData.put(nodeDto.getName() + NetworkConstants.SUFFIX_DOMAIN, nodeDto.getDomain());

        if (label.equalsIgnoreCase(Labels.mc.label()) && StringUtils.isBlank(nodeDto.getExternalIp())) {
            configmapData.put(nodePubIpKey, nodeDto.getInternalIp());
        }

        getDeployService().patchConfigMap(configmapName, Constants.NAMESPACE_DEFAULT, d -> {
            d.putAll(configmapData);
        });
    }
}
