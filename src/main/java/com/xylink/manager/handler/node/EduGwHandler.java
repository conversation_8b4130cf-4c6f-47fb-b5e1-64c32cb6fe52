package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.em.Labels;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * edu网关配置处理实现。
 * 教育网关集合体，主要给1+N项目使用
 */
public class EduGwHandler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(EduGwHandler.class);

    @Override
    public NodeHandler configureConfigMap() {
        if(node==null) return this;
        configureDistributeIP(node);

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();
        List<String> enableLabels = node.getLabelMap().entrySet().stream().filter(x->x.getValue()).map(x->x.getKey()).collect(Collectors.toList());
        //内网服务地址
        enableLabels.stream().filter(Constants.interIps::containsKey).forEach(label -> allIpMap.put(Constants.interIps.get(label), node.getInternalIp()));
        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);


        handleDistributeIP(node, Labels.edu_gw.label());
        return this;
    }

    @Override
    protected NodeHandler initDefaultNodeConfig() {

        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setExternalIp(deployMessage.getIp());
        node.setDomain(deployMessage.getIp());
        node.setReportInternalIp(deployMessage.getIp());
        node.setType(Labels.edu_gw.label());

        Map<String, Boolean > labelMap = new HashMap<>();
        DefaultDeployStructureEnumInvoke.services(Labels.edu_gw.label()).forEach(label -> labelMap.put(label, false));

        node.setLabelMap(labelMap);
        super.node = node;
        return this;
    }

//    @Override
//    protected NodeHandler afterConfigure() {
//        if ( jdbcUtils==null || client==null) return this;
//        new Edu1nsiggwMgrHandler().configureSN(new NodeDto(node.getName()), client, jdbcUtils);
//        return this;
//    }

}
