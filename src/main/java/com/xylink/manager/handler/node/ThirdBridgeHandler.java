package com.xylink.manager.handler.node;

import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.em.Labels;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 2023/11/22/11:10
 * @Description:
 */
public class ThirdBridgeHandler extends NodeHandler{
    private final static Logger logger = LoggerFactory.getLogger(ThirdBridgeHandler.class);

    @Override
    protected NodeHandler configureConfigMap() {
        if(node==null) return this;
        configureDistributeIP(node);
        handleDistributeIP(node, Labels.third_bridge.label());
        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();
        boolean mysqlLabels = node.getLabelMap().get(Labels.third_bridge_mysql.label()) != null && node.getLabelMap().get(Labels.third_bridge_mysql.label());
        boolean dMLabels = node.getLabelMap().get(Labels.third_bridge_dameng.label()) != null && node.getLabelMap().get(Labels.third_bridge_dameng.label());
        if(mysqlLabels || dMLabels){
            allIpMap.put("BRIDGE_DATABASE_IP",node.getInternalIp());
        }
        //多节点服务
        DefaultDeployStructureEnumInvoke.services(Labels.third_bridge.label())
                .stream()
                .map(Labels::labelOf)
                .filter(Labels::isDistributed)
                .filter(label -> StringUtils.isNotBlank(label.loadblanceKey()))
                .forEach(label -> allIpMap.put(label.loadblanceKey(), getDistributeIp(label.label())));

        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);
        return this ;
    }

    @Override
    protected NodeHandler initDefaultNodeConfig() {
        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setReportInternalIp(deployMessage.getIp());
        node.setType(Labels.third_bridge.label());
        Map<String, Boolean> labelMap = new HashMap<>();
        DefaultDeployStructureEnumInvoke.services(Labels.third_bridge.label()).forEach(label -> labelMap.put(label, true));
        labelMap.put(Labels.frontend_third_bridge.label(), false);
        node.setLabelMap(labelMap);
        super.node = node;
        return this;
    }
}
