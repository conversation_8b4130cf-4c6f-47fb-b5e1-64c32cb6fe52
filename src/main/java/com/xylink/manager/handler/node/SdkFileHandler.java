package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.em.Labels;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * edu node 配置处理实现
 * 教育主节点，包含教育后台所有服务，三方组件和静态资源等
 */
public class SdkFileHandler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(SdkFileHandler.class);


    @Override
    public NodeHandler configureConfigMap() {
        if (node == null) return this;
        configureDistributeIP(node);

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();

        List<String> enableLabels = node.getLabelMap()
                .entrySet()
                .stream()
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        //内网服务地址
        enableLabels.stream().filter(Constants.interIps::containsKey).forEach(label -> allIpMap.put(Constants.interIps.get(label), node.getInternalIp()));

        //多节点服务
        DefaultDeployStructureEnumInvoke.services(node.getType())
                .stream()
                .map(Labels::labelOf)
                .filter(Labels::isDistributed)
                .filter(label -> StringUtils.isNotBlank(label.loadblanceKey()))
                .forEach(label -> allIpMap.put(label.loadblanceKey(), getDistributeIp(label.label())));


        allIpMap.put(NetworkConstants.SDK_FILE_INTERNAL_IP, node.getInternalIp());
        allIpMap.put(NetworkConstants.SDK_FILE_PUBLIC_IP, node.getExternalIp());
        allIpMap.put(NetworkConstants.SDK_FILE_DOMAIN_NAME, StringUtils.isBlank(node.getDomain()) ? node.getExternalIp() : node.getDomain());
        allIpMap.put(NetworkConstants.SDK_FILE_NGINX_PORT, StringUtils.isBlank(node.getNginxPort()) ? "80" : node.getNginxPort());
        allIpMap.put(NetworkConstants.SDK_FILE_NGINX_SSL_PORT, StringUtils.isBlank(node.getNginxSslPort()) ? "443" : node.getNginxSslPort());


        logger.info("update all-ip: " + allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);

        return this;

    }

    @Override
    protected NodeHandler initDefaultNodeConfig() {

        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setExternalIp(deployMessage.getIp());
        node.setDomain(deployMessage.getIp());
        node.setNginxPort("80");
        node.setNginxSslPort("443");
        node.setReportInternalIp(deployMessage.getIp());
        node.setType(Labels.sdk_file.label());
        super.node = node;
        return this;
    }
}
