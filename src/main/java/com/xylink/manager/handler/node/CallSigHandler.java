package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.config.MmsConfigMap;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.model.deploy.Node;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.base.PlatformConfig;
import com.xylink.manager.service.clustersetting.domain.ClusterConfigEnum;
import com.xylink.util.ClusterUtil;
import com.xylink.util.Ipv6Util;
import com.xylink.util.K8sUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * callbase+sigmc合并
 */
public class CallSigHandler extends NodeHandler {
    private static final Logger logger = LoggerFactory.getLogger(CallSigHandler.class);

    @Override
    protected NodeHandler configureConfigMap() {
        if (node == null) {
            return this;
        }

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();

        List<String> enableLabels = node.getLabelMap().entrySet().stream().filter(Map.Entry::getValue).map(Map.Entry::getKey).collect(Collectors.toList());

        configureDistributeIP(node);
        handleDistributeIP(node, Labels.mc.label());
        handleDistributeIP(node, Labels.mms.label());
        handleDistributeIP(node, Labels.call_sig.label());

        //内网服务地址
        enableLabels.stream().filter(Constants.interIps::containsKey).forEach(label -> allIpMap.put(Constants.interIps.get(label), node.getInternalIp()));
        //外网服务地址
        enableLabels.stream().filter(Constants.exterIps::containsKey).forEach(label -> allIpMap.put(Constants.exterIps.get(label), node.getExternalIp()));
        // 更新sig cm
        updateSigServer(enableLabels);
        // 更新ALLOCATION_IP_PORT
        if(enableLabels.contains(Labels.allocator_server.label())) {
            allIpMap.put(NetworkConstants.ALLOCATION_IP_PORT, getAllocatorIpUrl());
        }
        // all-mms 配置
        MmsConfigMap.updateAllMms(node.getName(), node.getInternalIp());
        // mms相关服务的 all-ip 更新
        allIpMap.put(NetworkConstants.MMS_PERMISSION_IP, node.getInternalIp());
        MmsConfigMap.updateAllIp(allIpMap, getDistributeIp(Labels.mms.label()));
        //多节点服务
        DefaultDeployStructureEnumInvoke.services(Labels.call_sig.label())
                .stream()
                .map(Labels::labelOf)
                .filter(Labels::isDistributed)
                .filter(label -> StringUtils.isNotBlank(label.loadblanceKey()))
                .forEach(label -> allIpMap.put(label.loadblanceKey(), getDistributeIp(label.label())));

        ClusterUtil.updateMcIpConfig(allIpMap, enableLabels.contains(Labels.mc.label()), Ipv6Util.handlerIpv6Addr(node.getInternalIp()), node.getName());
        doUaaHandler(allIpMap, enableLabels);
        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);

        return this;
    }

    /**
     * 和标准流程不一样的是  默认不部署该节点下的服务
     *
     * @return
     */
    @Override
    protected NodeHandler initDefaultNodeConfig() {
        // 默认部署了改节点类型下的所有服务
        super.initDefaultNodeConfig();
        Map<String, Boolean> labels = new HashMap<>();
        String type = deployMessage.getType();
        DefaultDeployStructureEnumInvoke.services(type).forEach(label -> labels.put(label, type.equals(label)));
        this.node.setLabelMap(labels);
        return this;
    }

    // 获取所有allocator_server服务ip，拼接
    private String getAllocatorIpUrl() {
        List<Node> nodeList = deployService.listNodesByAppLabel("allocatorserver");
        if (CollectionUtils.isEmpty(nodeList)) {
            return "http://127.0.0.1:18118";
        }

        List<String> allocatorIps = nodeList.stream().map(Node::getIp).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(allocatorIps)) {
            return "http://127.0.0.1:18118";
        }

        StringBuilder allocAddr = new StringBuilder();
        for (String allocatorIp : allocatorIps) {
            allocAddr.append("http://").append(allocatorIp).append(":18118,");
        }
        return allocAddr.substring(0, allocAddr.length() - 1);
    }

    public void doUaaHandler(Map<String, String> allIpMap, List<String> enableLabels) {
        //高可用模式时,uaa节点保存时不覆盖UAA_DATABASE_IP
        Map<String, String> allCluster = K8sUtils.getConfigMap(Constants.CONFIGMAP_CLUSTER);
        if (StringUtils.isBlank(allCluster.get(ClusterConfigEnum.UAA_DATABASE.getHaAddressKey()))) {
            if(enableLabels.contains(Labels.uaa_mysql.label())) {
                allIpMap.put(NetworkConstants.UAA_DATABASE_IP,node.getInternalIp());
            }
            if(enableLabels.contains(Labels.uaa_mysql.label())) {
                allIpMap.put(NetworkConstants.UAA_DATABASE_IP,node.getInternalIp());
            }

            if(PlatformConfig.isAnKe()) {
                allIpMap.put(NetworkConstants.UAA_DATABASE_IP,allIpMap.get(NetworkConstants.DATABASE_IP));
            }
        }
        allIpMap.put(NetworkConstants.UAA_INTERNAL_IP, node.getInternalIp());
    }
}
