package com.xylink.manager.handler.node;

import com.xylink.config.*;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.config.exception.basic.ServiceErrorException;
import com.xylink.config.haconf.HaConfParam;
import com.xylink.config.haconf.service.PrescnceHaConf;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.controller.dto.ShuttleSnDto;
import com.xylink.manager.model.DeployMessage;
import com.xylink.manager.model.cm.*;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.model.deploy.Node;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.model.em.DefaultConfigmapDataEnum;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.NetToolService;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.base.SystemModeConfig;
import com.xylink.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileWriter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * node节点配置 基础handler，抽象公共操作 <br/>
 * 场景: 服务器管理系统-服务器列表 配置node节点，主要流程：校验部署条件--- 处理node label--- 处理node 关联configmap <br/>
 * 说明:  <br/>
 * 1、根据业务对configureConfigMap进行实现 <br/>
 * 2、根据类名进行调用，类命名规则参见 NodeHelper，无对应实现情况，默认调用BaseHandler <br/>
 */
public abstract class NodeHandler implements INodeHandler {
    private final static Logger logger = LoggerFactory.getLogger(NodeHandler.class);
    protected final IDeployService deployService = SpringBeanUtil.getBean(IDeployService.class);
    protected DeployMessage deployMessage;
    protected NodeDto node;
    protected JDBCUtils jdbcUtils;
    protected ICMDto advanceConfig;

    /**
     * node节点配置流程，node自身配置及相关configmap配置
     */
    @Override
    final public boolean configure(NodeDto node, JDBCUtils jdbcUtils) {
        load(node, jdbcUtils).check().configureNodeTypeAndLabels().configureConfigMap().afterConfigure();
        return true;
    }


    final private NodeHandler load(NodeDto node, JDBCUtils jdbcUtils) {
        this.node = node;
        this.jdbcUtils = jdbcUtils;
        return this;
    }

    final private NodeHandler loadDefaultDeployMessage(DeployMessage deployMessage, JDBCUtils jdbcUtils) {
        this.deployMessage = deployMessage;
        this.jdbcUtils = jdbcUtils;
        return this;
    }


    /**
     * 前置条件校验，校验失败throw exception中断流程
     */
    protected NodeHandler check() {
        return this;
    }


    /**
     * node节点对应configmap的配置逻辑，各实现类自定义
     */
    protected abstract NodeHandler configureConfigMap();


    /**
     * 配置node type  labels
     */
    final private NodeHandler configureNodeTypeAndLabels() {
        logger.info("start configureNodeTypeAndLabels");
        if (node == null) {
            return this;
        }
        Node updateNode = deployService.getNodeByName(node.getName());
        if (updateNode == null) {
            logger.error("no node for name: {}", node.getName());
            throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
        }
        logger.info("nodeName:{} labelMap:{},node already has label:{}", node.getName(), node.getLabelMap(), updateNode.getLabels().keySet());

        Map<String, String> resultNodeLabelMap = updateNode.getLabels();
        //修改node类型（main除外）
        if (StringUtils.isNotEmpty(node.getType()) && !(node.getType().equalsIgnoreCase(Constants.NODETYPE_MAIN))) {
            if (node.getType().equalsIgnoreCase(Constants.NODETYPE_NOTHING)) {
                resultNodeLabelMap.remove(Constants.TYPE);
            } else {
                resultNodeLabelMap.put(Constants.TYPE, node.getType());
                logger.info("update node {} type {}", node.getName(), node.getType());
            }
        }

        List<String> distributedLabels = Labels.distributedLabels();
        distributedLabels.add(Labels.matrix_alg.label());
        distributedLabels.add(Labels.nfs.label());
        distributedLabels.add(Labels.dns_inner.label());
        distributedLabels.add(Labels.dns_outer.label());
        distributedLabels.add(Labels.frontend.label());
        distributedLabels.add(Labels.nettool.label());
        distributedLabels.add(Constants.LABEL_LOGAGENT);
        distributedLabels.add(Constants.LABEL_FILEBEAT);
        distributedLabels.remove(Labels.edu_1nsiggw_mgr.label());

        if (Labels.dmcu.label().equalsIgnoreCase(node.getType())) {
            checkIpIpProxyLabelToChangeSwitch();
        }

        if (node.getLabelMap().containsKey(Labels.hls.label())) {
            checkHlsLabelToChangeAddress(updateNode, node.getLabelMap().get(Labels.hls.label()));
        }

        if (node.getLabelMap().containsKey(Labels.redis.label()) || node.getLabelMap().containsKey(Labels.redis_sentinel.label())) {
            ClusterUtil.setRedisClusterConfig(node);
        }

        if (node.getLabelMap().containsKey(Labels.txlive.label())) {
            setHostNameIPMapping("private-txlive", "TXLIVE_HOSTNAME_IP_MAPPING");
        }

        if (node.getLabelMap().containsKey(Labels.tsa_mp.label())) {
            setHostNameIPMapping("private-tsa-mp", "TSA_MP_HOSTNAME_IP_MAPPING");
        }

        Map<String, Boolean> nodeLabelMap = node.getLabelMap();
        Map<String, String> updateNodeLabelMap = updateNode.getLabels();

        //增删node label
        Labels.all().forEach(label -> {
            if (nodeLabelMap.containsKey(label) && nodeLabelMap.get(label)) {
                //排它
                if (!distributedLabels.contains(label)) {
                    deployService.listNodesByAppLabel(label).stream()
                            .filter(it -> !it.getName().equals(node.getName()))
                            .forEach(node -> deployService.removeNodeLabel(node.getName(), label));
                }
                if (!updateNodeLabelMap.containsKey(label)) {
                    logger.info("update node {} label {}", node.getName(), label);
                    resultNodeLabelMap.put(label, Constants.XYLINK);
                }
            } else if (updateNodeLabelMap.containsKey(label) && !Constants.LABEL_K8SMASTER.equals(label)) {
                resultNodeLabelMap.remove(label);
                logger.info("remove node {} label {}", node.getName(), label);
            }
        });

        //always add logagent label
        labelsAdapt(resultNodeLabelMap, updateNodeLabelMap);

        //main 默认部署 openresty-main
        //52通用适配多活-main节点不部署openresty-main
//        if(node.getType().equalsIgnoreCase(Constants.NODETYPE_MAIN)){
//            nodeBuilder.editMetadata().addToLabels(Labels.openresty_main.label(), Constants.XYLINK).endMetadata();
//        }

        //邮储高可用proxy-base模块勾选main-proxy时会默认部署前端服务
        if (node.getType().equalsIgnoreCase(Labels.proxy_base.label()) && node.getLabelMap().containsKey(Labels.main_proxy.label()) && node.getLabelMap().get(Labels.main_proxy.label())) {
            resultNodeLabelMap.put(Labels.frontend_pcclient.label(), Constants.XYLINK);
            resultNodeLabelMap.put(Labels.frontend_meetingschedule.label(), Constants.XYLINK);
            resultNodeLabelMap.put(Labels.frontend_meeting.label(), Constants.XYLINK);
            resultNodeLabelMap.put(Labels.frontend_buffet.label(), Constants.XYLINK);
            resultNodeLabelMap.put(Labels.frontend.label(), Constants.XYLINK);
        }

        //单点-main+main-proxy默认部署 frontend
        //高可用-frontend相关服务只部署在base+main-proxy模块
        if (node.getType().equalsIgnoreCase(Labels.main_proxy.label())) {
            resultNodeLabelMap.put(Labels.frontend.label(), Constants.XYLINK);
            resultNodeLabelMap.put(Labels.frontend_buffet.label(), Constants.XYLINK);
            resultNodeLabelMap.put(Labels.frontend_meeting.label(), Constants.XYLINK);
            resultNodeLabelMap.put(Labels.frontend_meetingschedule.label(), Constants.XYLINK);
            resultNodeLabelMap.put(Labels.frontend_pcclient.label(), Constants.XYLINK);
        }
        if (Labels.cdn_proxy.label().equalsIgnoreCase(node.getType())) {
            resultNodeLabelMap.put(Labels.frontend_pcclient.label(), Constants.XYLINK);
        }

        if (Labels.fusion_mms.label().equalsIgnoreCase(node.getType())) {
            resultNodeLabelMap.put(Labels.frontend_fusion.label(), Constants.XYLINK);
        }

        if (StringUtils.isNotEmpty(node.getType()) && (node.getType().equalsIgnoreCase(Constants.NODETYPE_MAIN) || node.getType().equalsIgnoreCase(Constants.NODE_TYPE_COMMON_MAIN))) {
            resultNodeLabelMap.put(Constants.LABEL_K8SMASTER, Constants.XYLINK);
        }

        if (node.getType().equalsIgnoreCase(Labels.shuttle.label())) {
            updateShuttle(updateNode.getLabels().containsKey(Labels.shuttle.label()));
        }

        //修改dmcu终端连接端口时，同步将nettool端口设置为dmcu终端连接端口+8
        if ((Constants.NODE_TYPE_COMMON_MAIN.equalsIgnoreCase(node.getType()) ||
                Constants.NODETYPE_MAIN.equalsIgnoreCase(node.getType()) ||
                Labels.main_partner.label().equalsIgnoreCase(node.getType()) ||
                Labels.dmcu.label().equalsIgnoreCase(node.getType()))
                && nodeLabelMap.containsKey(Labels.dmcu.label())
                && null != nodeLabelMap.get(Labels.dmcu.label())
                && nodeLabelMap.get(Labels.dmcu.label())) {
            NetToolService netToolService = SpringBeanUtil.getBean(NetToolService.class);
            netToolService.setNetToolPortByDmcuClientPort(node.getName());
        }

        logger.info("update nodeName:{} label array:{}", node.getName(), resultNodeLabelMap.keySet());
        deployService.patchNodeLabels(node.getName(), d -> {
            d.clear();
            d.putAll(resultNodeLabelMap);
        });
        logger.info("end configureNodeTypeAndLabels");
        return this;
    }

    /**
     * 集群服务 配置流程，根据当前节点勾选的标签，修改all-*
     *
     * @param node
     * @param enableLabels
     */
    protected void configureDistributeIP(NodeDto node, List<String> enableLabels) {
        List<String> distributedLabellist = Labels.distributedLabels();
        enableLabels.stream()
                .filter(distributedLabellist::contains)
                .forEach(label -> handleDistributeIP(node, label));

    }

    /**
     * 集群服务 配置流程，修改all-*
     */
    final protected void configureDistributeIP(NodeDto node) {
        Set<String> nodeLableset = node.getLabelMap().keySet();

        List<String> distributedLabellist = Labels.distributedLabels();
        nodeLableset.stream()
                .filter(distributedLabellist::contains)
                .forEach(label -> handleDistributeIP(node, label));

    }


    final protected void handleDistributeIP(NodeDto nodeDto, String label) {
        logger.info("start handleDistributeIP of {}-{}", nodeDto.getType(), nodeDto.getName());
        String configmapName = "all-" + label;
        if (label.endsWith("-x86")) {
            configmapName = "all-" + label.replace("-x86", "");
        }
        if (label.endsWith(Constants.ARM)) {
            configmapName = "all-" + label.replace(Constants.ARM, "");
        }
        configmapName = configmapName.replace("_", "-");

        if (Labels.h323_sig.label().equalsIgnoreCase(label) || Labels.h323_sig_x86.label().equalsIgnoreCase(label)
                || Labels.h323_sig_arm.label().equalsIgnoreCase(label)) {
            configmapName = Constants.CONFIGMAP_H323_GATEWAY;
        }
        if (nodeDto.getType().equalsIgnoreCase(Labels.dmcu_side.label()) || nodeDto.getType().equalsIgnoreCase(Labels.dmcu_side_x86.label())) {
            configmapName = Constants.CONFIGMAP_DMCU;
        }
        ConfigMap configMap = deployService.getConfigMapByName(configmapName, Constants.NAMESPACE_DEFAULT);

        //没有该all-* configmap 则自动创建
        Map<String, String> configmapData = configMap == null ? DefaultConfigmapDataEnum.initDefault(label) : configMap.getData();

        String nodeInterIpKey = nodeDto.getName() + NetworkConstants.SUFFIX_INTERNAL_IP;
        String nodePubIpKey = nodeDto.getName() + NetworkConstants.SUFFIX_PUBLIC_IP;

        //清除脏数据
        Iterator<Map.Entry<String, String>> iterator = configmapData.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, String> next = iterator.next();
            if (next.getKey() == null || next.getValue() == null) break;
            if (next.getKey().endsWith(NetworkConstants.SUFFIX_INTERNAL_IP) && next.getValue().equalsIgnoreCase(nodeDto.getInternalIp())) {
                iterator.remove();
            }
        }

        configmapData.put(nodeInterIpKey, nodeDto.getInternalIp());
        configmapData.put(nodePubIpKey, nodeDto.getExternalIp());
        configmapData.put(nodeDto.getName() + NetworkConstants.SUFFIX_DOMAIN, nodeDto.getDomain());

        if (Constants.NODE_TYPE_COMMON_MAIN.equalsIgnoreCase(node.getType()) && label.equalsIgnoreCase(Labels.main_proxy.label())) {
            //main_proxy 单独用外网IP和域名，适配ipv6栈
            logger.info("set independent domain and external ip");
            configmapData.put(nodePubIpKey, nodeDto.getMainProxyExternalIp());
            configmapData.put(nodeDto.getName() + NetworkConstants.SUFFIX_DOMAIN, nodeDto.getMainProxyDomain());
        }

        if (label.equalsIgnoreCase(Labels.mc.label()) && StringUtils.isBlank(nodeDto.getExternalIp())) {
            configmapData.put(nodePubIpKey, nodeDto.getInternalIp());
        }
        /*if (label.equalsIgnoreCase(Labels.nettool.label())) {
            try {
                InetAddress internalIp = InetAddress.getByName(nodeDto.getInternalIp());
                if (internalIp instanceof Inet6Address) {
                    configmapData.put(nodeDto.getName() + NetworkConstants.SUFFIX_INTERNAL_IP_V6, nodeDto.getInternalIp());
                    configmapData.put(nodeDto.getName() + NetworkConstants.SUFFIX_PUBLIC_IP_V6, nodeDto.getExternalIp());
                } else {
                    configmapData.put(nodeDto.getName() + NetworkConstants.SUFFIX_INTERNAL_IP_V4, nodeDto.getInternalIp());
                    configmapData.put(nodeDto.getName() + NetworkConstants.SUFFIX_PUBLIC_IP_V4, nodeDto.getExternalIp());
                }
            } catch (UnknownHostException ignored) {
            }
        }*/

        deployService.patchConfigMap(configmapName, Constants.NAMESPACE_DEFAULT, d -> {
            d.putAll(configmapData);
        });
    }

    /**
     * txlive多活部署
     */
    public void setHostNameIPMapping(String appName, String key) {
        ConfigMap configMap = deployService.getConfigMapAllIp();
        Map<String, String> allIpMap = configMap.getData();
        //tsa-mp是有状态的，所以需要将所有tsa-mp服务器节点的hostname和ip映射存储起来
        List<Pod> tsaMpServerPodList = deployService.listPodsByAppLabel(appName);
        if (!org.springframework.util.CollectionUtils.isEmpty(tsaMpServerPodList)) {
            String hostNameIps = tsaMpServerPodList.stream().map(pod -> pod.getNodeName() + ":" + pod.getIp()).collect(Collectors.joining(","));
            // hostname1:ip1,hostname2:ip2
            allIpMap.put(key, hostNameIps);
        }
        deployService.patchConfigMapAllIpForAddData(allIpMap);
    }

    /**
     * 检查hls标签，若取消部署hls，则从all-hls删除hls的上报地址
     *
     * @param updateNode
     * @param selected   hasLabel表示之前是否部署hls，selected表示此次保存是否部署hls
     */
    public void checkHlsLabelToChangeAddress(Node updateNode, boolean selected) {
        boolean hasLabel = updateNode.getLabels().containsKey(Labels.hls.label());
        if (hasLabel == selected || selected) {
            return;
        }

        ConfigMap configMap = deployService.getConfigMapByNameNotNull(Constants.CONFIGMAP_HLS, Constants.NAMESPACE_DEFAULT);
        Map<String, String> allHls = configMap.getData();
        allHls.remove(node.getName() + HlsConstants.HLS_REPORT_HTTP_ADDRESS);
        allHls.remove(node.getName() + HlsConstants.HLS_REPORT_HTTPS_ADDRESS);

        deployService.patchConfigMap(Constants.CONFIGMAP_HLS, Constants.NAMESPACE_DEFAULT, d -> {
            d.putAll(allHls);
        });
    }

    /**
     * dmcu节点检查ipip-proxy标签，去配置sitecode服务是否走ipip库查询逻辑的开关
     */
    public void checkIpIpProxyLabelToChangeSwitch() {
        Map<String, String> allIp = deployService.getConfigMapAllIp().getData();
        boolean accessIpIpSwitch = Boolean.parseBoolean(allIp.get(NetworkConstants.SITECODE_ACCESS_IPIP_SWITCH));
        boolean select = node.getLabelMap().get(Labels.ipip_proxy.label());

        if (select == accessIpIpSwitch) {
            return;
        }
        if (select) {
            allIp.put(NetworkConstants.SITECODE_ACCESS_IPIP_SWITCH, "true");
        } else {
            List<Pod> podList = deployService.listPodsByAppLabel("private-ipip-proxy");
            if (CollectionUtils.isEmpty(podList) ||
                    (podList.size() == 1 && podList.get(0).getNodeName().equals(node.getName()))) {
                allIp.put(NetworkConstants.SITECODE_ACCESS_IPIP_SWITCH, "false");
            }
        }
        deployService.patchConfigMapAllIpForAddData(allIp);
    }

    final protected String getDistributeIp(String label) {
        StringBuilder distributeIp = new StringBuilder();
        List<Node> nodeList = deployService.listNodesByAppLabel(label);
        for (Node node : nodeList) {
            String ip = node.getIp();
            distributeIp.append(Ipv6Util.getHost(ip, node.getName())).append(",");
        }

        if (distributeIp.length() > 0) {
            distributeIp = new StringBuilder(distributeIp.substring(0, distributeIp.length() - 1));
        }

        return distributeIp.toString();
    }

    final protected String getDistributeIpByType(String label) {
        StringBuilder distributeIp = new StringBuilder();
        List<Node> nodeList = deployService.listNodesByLabels(Constants.TYPE, label);
        for (Node node : nodeList) {
            String ip = node.getIp();
            distributeIp.append(ip).append(",");
        }
        if (distributeIp.length() > 0) {
            distributeIp = new StringBuilder(distributeIp.substring(0, distributeIp.length() - 1));
        }

        return distributeIp.toString();
    }


    /**
     * 部署node 入口 (供部署包调用,按照内网进行默认部署配置)
     */
    @Override
    final public boolean defaultConfigure(DeployMessage deployMessage, JDBCUtils jdbcUtils) {
        loadDefaultDeployMessage(deployMessage, jdbcUtils).initDefaultNodeConfig().defaultServiceAdvanceConfig().configureNodeTypeAndLabels().configureConfigMap().afterConfigure();
        return true;
    }


    /**
     * 具体node 部署业务，补充默认配置，复用configure
     */
    protected NodeHandler initDefaultNodeConfig() {
        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setExternalIp(deployMessage.getIp());
        node.setType(deployMessage.getType());
        node.setReportInternalIp(deployMessage.getIp());
        Map<String, Boolean> labelMap = new HashMap<>();
        DefaultDeployStructureEnumInvoke.services(deployMessage.getType()).forEach(label -> labelMap.put(label, true));

        String installType = deployService.getConfigMapManagerData().getData().getOrDefault("install_type", "normal");
        if ("min".equalsIgnoreCase(installType)) {
            if (Labels.meeting_quality.label().equalsIgnoreCase(node.getType())) {
                labelMap.put(Labels.statis_quality.label(), false);
            }
        }
        // dmcu节点默认不部署 ipip-proxy. 如果改需求比较多，需要在 DeployStructureEnum定义改结构：标注是否默认部署
        if (Labels.dmcu.label().equals(deployMessage.getType()) || Constants.NODE_TYPE_COMMON_MAIN.equals(deployMessage.getType())) {
            labelMap.put(Labels.ipip_proxy.label(), false);
        }
        node.setLabelMap(labelMap);
        this.node = node;
        return this;
    }

    ;

    /**
     * 具体node 部署业务, 配置服务的默认高级配置
     */
    protected NodeHandler defaultServiceAdvanceConfig() {
        return this;
    }

    ;


    /**
     * 配置服务的高级自定义配置 入口
     */
    @Override
    final public <T extends ICMDto> boolean serviceAdvanceConfigure(T t, String configmap, JDBCUtils jdbcUtils, String label, String nodeName) {
        loadClients(jdbcUtils).serviceAdvanceConfigure(t, configmap, label, nodeName).afterConfigure().afterConfigureICMDto(t);
        return false;
    }

    private <T extends ICMDto> NodeHandler loadClients(JDBCUtils jdbcUtils) {
        this.jdbcUtils = jdbcUtils;
        return this;
    }


    private <T extends ICMDto> NodeHandler serviceAdvanceConfigure(T t, String configmap, String label, String nodeName) {
        if (StringUtils.isBlank(configmap)) {
            return this;
        }
        this.advanceConfig = t;
        t.beforeSave();
        try {
            Map<String, String> cm = deployService.patchConfigMap(configmap, Constants.NAMESPACE_DEFAULT, d -> {
                ((Map<String, String>) t.toConfigmap()).forEach(d::put);
            });
            t.afterSave();
            if (Constants.NODETYPE_MAIN.equalsIgnoreCase(label) ||
                    Labels.main_partner.label().equalsIgnoreCase(label) ||
                    Labels.dmcu.label().equalsIgnoreCase(label)) {
                //修改dmcu终端连接端口时，同步将nettool端口设置为dmcu终端连接端口+8
                NetToolService netToolService = SpringBeanUtil.getBean(NetToolService.class);
                netToolService.setNetToolPortByDmcuClientPort(nodeName);
            }
            if (Labels.dmcu_arm.label().equalsIgnoreCase(label)) {
                //修改dmcu终端连接端口时，同步将nettool端口设置为dmcu终端连接端口+8
                NetToolService netToolService = SpringBeanUtil.getBean(NetToolService.class);
                netToolService.setNetToolPortByDmcuClientPort(nodeName);
            }
            //重启对应服务
            restartServer(t, nodeName, label);
            if (t instanceof OpenrestyMainCM) {
                try {
                    Map<String, String> allIp = deployService.getConfigMapAllIp().getData();
                    if (allIp != null) {
                        String port = allIp.get(NetworkConstants.MAIN_NGINX_PORT);
                        String innerIp = allIp.get(NetworkConstants.MAIN_INTERNAL_IP);
                        String publicIp = allIp.get(NetworkConstants.MAIN_PUBLIC_IP);
                        jdbcUtils.asyncConfigureNetworkDetection(innerIp, publicIp, port);
                    }
                } catch (Exception e) {
                    logger.error("asyncConfigureNetworkDetection error.", e);
                }
            } else if (t instanceof DaMengCM) {
                String filePath = "/etc/dm_svc.conf";
                File file = new File(filePath);
                if (file.exists()) {
                    file.delete();
                }
                FileWriter fileWriter = new FileWriter(filePath);
                String dmClusterMode = ((DaMengCM) t).getDmClusterMode();
                if ("master-slave".equals(dmClusterMode)) {
                    fileWriter.write("TIME_ZONE=(480)\n" +
                            "LANGUAGE=(cn)\n" +
                            "" + cm.get("DM_CLUSTER_DOMAIN") + "=(" + cm.get("DM_CLUSTER_IP_PORT") + ")\n" +
                            "[" + cm.get("DM_CLUSTER_DOMAIN") + "]\n" +
                            "LOGIN_MODE=(1)\n");
                } else if ("dsc".equals(dmClusterMode)) {
                    fileWriter.write("TIME_ZONE=(480)\n" +
                            "LANGUAGE=(cn)\n" +
                            "" + cm.get("DM_CLUSTER_DOMAIN") + "=(" + cm.get("DM_CLUSTER_IP_PORT") + ")\n" +
                            "[" + cm.get("DM_CLUSTER_DOMAIN") + "]\n" +
                            "EP_SELECTOR=(1)\n" +
                            "AUTO_RECONNECT=(2)\n" +
                            "SWITCH_TIME=(10000)\n" +
                            "SWITCH_INTERVAL=(1000)\n");
                }
                fileWriter.close();
            } else if (DmcuCM.class.equals(t.getClass()) || WebrtcMediagwCM.class.equals(t.getClass())) {
                String dmcuServerType = "0";
                nodeName = ((DmcuCM) t).getNodeName();
                String siteCode = ((DmcuCM) t).getSiteCode();
                jdbcUtils.asynUpdateDmcuSitecode(dmcuServerType, nodeName, siteCode);
            }
        } catch (Exception e) {
            logger.error("fail to save configmap: " + configmap, e);
            throw new ServiceErrorException(ErrorStatus.ADVANCED_CONFIG_SAVE_FAILED);
        }
        return this;
    }

    ;


    private <T extends ICMDto> void restartServer(T t, String nodeName, String label) {
        if (Labels.dmcu.label().equalsIgnoreCase(label)) {
            //部署dmcu时，重启同节点的nettool服务
            K8sService k8sService = SpringBeanUtil.getBean(K8sService.class);
            k8sService.restartOnePodByNodeAppLabelAndNodeName(Labels.nettool.label(), nodeName);
        }

        if (Labels.dmcu_arm.label().equalsIgnoreCase(label)) {
            //部署dmcu时，重启同节点的nettool服务
            K8sService k8sService = SpringBeanUtil.getBean(K8sService.class);
            k8sService.restartOnePodByNodeAppLabelAndNodeName(Labels.nettool_arm.label(), nodeName);
        }
        //信创dmcu-side和dmcu-side-x86比较特殊,更新useTransportProxy配置时需要重启所有dmcu和dmcu-side生成
        if (t instanceof DmcuCM) {

            if (((DmcuCM) t).changeUseTransportProxy) {
                restartUseTransportProxyService();
                return;
            }
        }

        if (t instanceof NmsaCM) {
            if (((NmsaCM) t).changeUseTransportProxy) {
                restartUseTransportProxyService();
                return;
            }
        }

        if (t instanceof HlsCM) {
            if (((HlsCM) t).changeUseTransportProxy) {
                restartUseTransportProxyService();
                return;
            }
        }


        if (t instanceof ConvergedMediagwCM) {
            if (((ConvergedMediagwCM) t).changeUseTransportProxy) {
                restartUseTransportProxyService();
                return;
            }
        }

        if (Labels.dmcu.label().equalsIgnoreCase(label)) {
            //部署dmcu时，重启同节点的nettool服务
            K8sService k8sService = SpringBeanUtil.getBean(K8sService.class);
            k8sService.restartOnePodByNodeAppLabelAndNodeName(Labels.nettool.label(), nodeName);
        }

        if (t instanceof MmsCM) {
            restartMms();
            return;
        }

        restartServerOfNodeName(nodeName, label);
    }

    private void restartUseTransportProxyService() {
        logger.info("dmcu-side useTransportProxy change,restart all dmcu & dmcu-side && webrtc-mediagw && nmst && nmsa && ma && ivr && hls && converged-mediagw && cascadegw ");
        K8sService k8sService = SpringBeanUtil.getBean(K8sService.class);
        k8sService.restartAllPodByNodeAppLabel("restartUseTransportProxyService");
    }

    private void restartMms() {
        K8sService k8sService = SpringBeanUtil.getBean(K8sService.class);
        k8sService.restartAllPodByNodeAppLabel("mms");
    }

    private void restartServerOfNodeName(String nodeName, String label) {
        List<String> services = Constants.labelToPodNames.get(label);
        if (CollectionUtils.isEmpty(services)) {
            logger.info("not found the restart services:{}", label);
            return;
        }
        services.forEach(serviceName -> {
            List<Pod> pods = deployService.listPodsByAppLabel(serviceName);
            String podName = !org.springframework.util.CollectionUtils.isEmpty(pods) ?
                    pods.stream().filter(po -> nodeName.equals(po.getNodeName())).map(Pod::getPodName).findFirst().orElse("") : "";
            if (StringUtils.isEmpty(podName)) {
                logger.info("no pod need delete");
                return;
            }
            logger.info("delete pod :{}", podName);
            deployService.deletePodByName(podName, Constants.NAMESPACE_DEFAULT);
        });
    }

    /**
     * 部署配置完成后的一些额外操作，比如一些数据库脚本执行
     */
    protected NodeHandler afterConfigure() {
        return this;
    }

    ;

    /**
     * 保存高级配置 callback
     *
     * @param t
     * @param <T>
     * @return
     */
    protected <T extends ICMDto> NodeHandler afterConfigureICMDto(T t) {
        return this;
    }

    private void updateShuttle(boolean oldShuttleLabel) {
        boolean newShuttleLabel = node.getLabelMap().get(Labels.shuttle.label());
        String shuttleSnKey = getShuttleSnKey(node.getName());
        if (oldShuttleLabel != newShuttleLabel) {
            ConfigMap configMap = deployService.getConfigMapByName(Constants.CONFIGMAP_SHUTTLE, Constants.NAMESPACE_DEFAULT);
            Map<String, String> allShuttleMap = (configMap == null) ? new HashMap<>() : configMap.getData();
            if (newShuttleLabel) {
                ShuttleSnDto largestShuttleSn = jdbcUtils.getLargestShuttleSn();
                String shuttleSn = largestShuttleSn.getSn();
                String serverId = largestShuttleSn.getServerId();

                String newShuttleName = getShuttleName(node.getName());
                String newShuttleSn = Constants.DEFAULT_SHUTTLE_SN;
                String newServerId = Constants.DEFAULT_SERVER_ID;
                if (StringUtils.isNotBlank(shuttleSn)) {
                    int shuttleSnLength = shuttleSn.length();
                    String substringSn = shuttleSn.substring(shuttleSnLength - 8, shuttleSnLength);
                    newShuttleSn = Constants.PREFIX_SHUTTLE_SN + (Integer.parseInt(substringSn) + 1);
                }
                if (StringUtils.isNotBlank(serverId)) {
                    int serverIdLength = serverId.length();
                    String substringId = serverId.substring(serverIdLength - 8, serverIdLength);
                    newServerId = Constants.PREFIX_SERVER_ID + String.format("%08d", Integer.parseInt(substringId) + 1);
                }

                jdbcUtils.insertShuttleSn(newShuttleSn, newServerId, newShuttleName);
                allShuttleMap.put(shuttleSnKey, newShuttleSn);
            } else {
                String shuttleSn = allShuttleMap.get(shuttleSnKey);
                //从数据库删除
                jdbcUtils.deleteShuttleSn(shuttleSn);
                //从all-shuttle中删除
                allShuttleMap.remove(shuttleSnKey);
            }
            logger.info("update all-shuttle: {}", allShuttleMap);
            deployService.patchConfigMap(Constants.CONFIGMAP_SHUTTLE, Constants.NAMESPACE_DEFAULT, d -> {
                d.putAll(allShuttleMap);
            });
        }
    }

    private String getShuttleName(String name) {
        return Constants.PREFIX_SHUTTLE_NAME + name;
    }

    private String getShuttleSnKey(String nodeName) {
        return nodeName + Constants.SUFFIX_SHUTTLE_SN_KEY;
    }

    /**
     * 混合部署适配x86标签
     */
    private void labelsAdapt(Map<String, String> resultNodeLabelMap, Map<String, String> updateNodeLabelMap) {
        if (!Labels.x86Nodes().contains(node.getType()) && !Labels.ArmNodes().contains(node.getType())) {
            //非x86、arm混合部署
            resultNodeLabelMap.put(Constants.LABEL_LOGAGENT, Constants.XYLINK);
            resultNodeLabelMap.put(Constants.LABEL_FILEBEAT, Constants.XYLINK);
            return;
        }
        if (updateNodeLabelMap.containsKey(Constants.LABEL_LOGAGENT)) {
            //x86机器去除logagent标签
            resultNodeLabelMap.remove(Constants.LABEL_LOGAGENT);
        }
        if (updateNodeLabelMap.containsKey(Constants.LABEL_FILEBEAT)) {
            //x86机器去除filebeat标签
            resultNodeLabelMap.remove(Constants.LABEL_FILEBEAT);
        }
        if (node.getType().equalsIgnoreCase(Labels.main_proxy_arm.label())) {
            resultNodeLabelMap.put(Labels.frontend_arm.label(), Constants.XYLINK);
            resultNodeLabelMap.put(Labels.frontend_buffet_arm.label(), Constants.XYLINK);
            resultNodeLabelMap.put(Labels.frontend_meeting_arm.label(), Constants.XYLINK);
            resultNodeLabelMap.put(Labels.frontend_meetingschedule_arm.label(), Constants.XYLINK);
            resultNodeLabelMap.put(Labels.frontend_pcclient_arm.label(), Constants.XYLINK);
        }
        if (Labels.ArmNodes().contains(node.getType())) {
            resultNodeLabelMap.put(Constants.LABEL_FILEBEAT_ARM, Constants.XYLINK);
            resultNodeLabelMap.put(Constants.LABEL_LOGAGENT_ARM, Constants.XYLINK);
            return;
        }
        if (node.getType().equalsIgnoreCase(Labels.main_proxy_x86.label())) {
            //main_proxy_x86机器增加frontend_x86标签
            resultNodeLabelMap.put(Labels.frontend_x86.label(), Constants.XYLINK);
            resultNodeLabelMap.put(Labels.frontend_buffet_x86.label(), Constants.XYLINK);
            resultNodeLabelMap.put(Labels.frontend_meeting_x86.label(), Constants.XYLINK);
            resultNodeLabelMap.put(Labels.frontend_meetingschedule_x86.label(), Constants.XYLINK);
            resultNodeLabelMap.put(Labels.frontend_pcclient_x86.label(), Constants.XYLINK);
        }
        //x86机器默认增加logagent_x86标签
        resultNodeLabelMap.put(Constants.LABEL_LOGAGENT_X86, Constants.XYLINK);
        //x86机器默认增加filebeat_x86标签
        resultNodeLabelMap.put(Constants.LABEL_FILEBEAT_X86, Constants.XYLINK);
    }

    protected void updatePresenceServer(List<String> enableLabels) {
        if (enableLabels.contains(Labels.presence.label())) {
            PrescnceHaConf prescnceHaConf = new PrescnceHaConf();
            HaConfParam haConfParam = new HaConfParam(node.getName(), Constants.POD_NAME_PRESENCE, Constants.CONFIGMAP_ALL_PRESENCE, HaConfigConstants.PRESENCE_CONSUMER_KEY);
            prescnceHaConf.loadAndUpdateConf(haConfParam);
        }
    }

    /**
     * 更新信令
     */
    protected void updateSigServer(List<String> enableLabels) {
        // 先清理脏数据
        SigServerHaConfigMap.clearNodesRecordInAllConfigMapWithoutLabel(Labels.accesssig);
        SigServerHaConfigMap.clearNodesRecordInAllConfigMapWithoutLabel(Labels.proxysig);
        SigServerHaConfigMap.clearNodesRecordInAllConfigMapWithoutLabel(Labels.signal);
        if (enableLabels.contains(Labels.accesssig.label())) {
            SigServerHaConfigMap.updateAllSigServerHa(Labels.accesssig, node.getName(), node.getInternalIp());
        }
        if (enableLabels.contains(Labels.proxysig.label())) {
            SigServerHaConfigMap.updateAllSigServerHa(Labels.proxysig, node.getName(), node.getInternalIp());
        }
        if (enableLabels.contains(Labels.signal.label())) {
            SigServerHaConfigMap.updateAllSigServerHa(Labels.signal, node.getName(), node.getInternalIp());
        }
    }

    protected void deployedNightingaleServer(Map<String, String> allIpMap) {
        allIpMap.put(NetworkConstants.N9E_SWITCH, "true");
        allIpMap.put(NetworkConstants.N9E_IP, node.getInternalIp());
        if (SystemModeConfig.isNewCms()) {
            allIpMap.put(NetworkConstants.N9E_IP, NetworkConstants.SVC_N9E_DEFAULT_IP);
        }
        allIpMap.put(NetworkConstants.N9E_KAFKA_ADDRESS, allIpMap.get(NetworkConstants.KAFKA_INTERNAL_IP) + ":9093");
    }

    protected void cancelDeployNightingaleServer(Map<String, String> allIpMap) {
        allIpMap.put(NetworkConstants.N9E_SWITCH, "false");
        allIpMap.put(NetworkConstants.N9E_IP, "127.0.0.1");
        allIpMap.put(NetworkConstants.N9E_KAFKA_ADDRESS, "");
    }

    /**
     * 返回包含指定标签的节点的IP信息 left:publicIp right:internalIp
     * edit by zgj 2022年06月09日11:23:29
     * 1、k8s 中获取部署节点hostname
     * 2、根据hostname 到对应的configmap: all-xxx 获取内网IP 、外网IP
     *
     * @param appName metadata:labels:app
     */
    protected Pair<Set<String>, Set<String>> getNodeInternalIpAndPublicIpByLabel(String label, String appName) {
        if (StringUtils.isBlank(label)) {
            return ImmutablePair.of(Collections.emptySet(), Collections.emptySet());
        }
        List<Pod> pods = deployService.listPodsByAppLabel(appName);
        if (pods == null || CollectionUtils.isEmpty(pods)) {
            return ImmutablePair.of(Collections.emptySet(), Collections.emptySet());
        }
        List<String> nodeNames = new ArrayList<>();
        pods.forEach(item ->
                nodeNames.add(item.getNodeName())
        );
        String configmapName = "all-" + label;
        ConfigMap configMap = deployService.getConfigMapByName(configmapName, Constants.NAMESPACE_DEFAULT);
        Map<String, String> configmapData = configMap == null ? DefaultConfigmapDataEnum.initDefault(label) : configMap.getData();
        Set<String> publicIps = new HashSet<>();
        Set<String> internalIps = new HashSet<>();
        nodeNames.forEach(nodeName -> {
            String internalIp = configmapData.get(nodeName + NetworkConstants.SUFFIX_INTERNAL_IP);
            if (StringUtils.isNotBlank(internalIp)) {
                internalIps.add(internalIp);
            }
            String publicIp = configmapData.get(nodeName + NetworkConstants.SUFFIX_PUBLIC_IP);
            if (StringUtils.isNotBlank(publicIp)) {
                publicIps.add(publicIp);
            }
        });
        return ImmutablePair.of(publicIps, internalIps);
    }

    protected Map<String, String> nightingaleCommonMidSetting(Map<String, String> allIpMap) {
        Map<String, String> nightingale = new HashMap<>();
        if (allIpMap != null) {
            Map<String, String> allRedis = K8sUtils.getConfigMap(Constants.CONFIGMAP_REDIS);
            String redisMode = allRedis.get(RedisConstants.REDIS_MODE_KEY);
            if (StringUtils.isBlank(redisMode) || RedisConstants.REDIS_MODE_SINGLE.equals(redisMode)) {
                nightingale.put(NetworkConstants.N9E_REDIS_MODE, allIpMap.get(NetworkConstants.N9E_REDIS_DEFULT_MODE));
                nightingale.put(NetworkConstants.N9E_REDIS_ADDRESS, address(allIpMap.get(NetworkConstants.MAIN_REDIS_IP), "6379"));
            } else {
                nightingale.put(NetworkConstants.N9E_REDIS_MODE, "{REDIS_MODE}");
                nightingale.put(NetworkConstants.N9E_REDIS_ADDRESS, allIpMap.get(NetworkConstants.MAIN_REDIS_IP));
            }
            // 使用占位符替换 逻辑ip-change统一处理
            nightingale.put(NetworkConstants.N9E_REDIS_USER, "{MAIN_REDIS_USERNAME}");
            nightingale.put(NetworkConstants.N9E_REDIS_PWD, "{REDIS_PWD}");


            nightingale.put(NetworkConstants.N9E_MYSQL_ADDRESS, address(allIpMap.get(NetworkConstants.STATIS_DATABASE_IP), allIpMap.get(NetworkConstants.DATABASE_PORT)));
            nightingale.put(NetworkConstants.N9E_MYSQL_USER, "{DB_STATIS_USERNAME}");
            nightingale.put(NetworkConstants.N9E_MYSQL_PASSWORD, allIpMap.get(NetworkConstants.STATIS_DB_PASSWORD));

            allIpMap.put(NetworkConstants.NIGHTINGALE_DATABASE_IP, allIpMap.get(NetworkConstants.STATIS_DATABASE_IP));
            allIpMap.put(NetworkConstants.NIGHTINGALE_DATABASE_PORT, allIpMap.get(NetworkConstants.DATABASE_PORT));
        }
        return nightingale;
    }

    protected String address(String ip, String port) {
        return ip + ":" + port;
    }

    final protected void updateP2PPoolNumber(List<String> enableLabels) {
        try {
            if (enableLabels.contains(Labels.callpermission.label())) {
                int num = jdbcUtils.getP2PPoolNumber(node.getName());
                if (num <= 0) {
                    jdbcUtils.updateP2PPoolNumber(node.getName());
                }
            } else {
                jdbcUtils.clearP2PPoolNumber(node.getName());
            }
        } catch (Exception e) {
            logger.error("updateP2PPoolNumber error,", e);
        }
    }

    /**
     * hadoopsingle、hadoopnode、hadoopmaster节点保存时，返回DATA_IP_LIST
     *
     * @return DATA_IP_LIST
     */
    protected String initHadoopDataIpList() {
        String[] ary = {Labels.hadoop_master.label(), Labels.hadoop_single.label(), Labels.hadoop_node.label(), Labels.hadoop_cluster.label()};
        List<Node> nodeList = deployService.listNodesByLabels(Constants.TYPE, ary);

        if (CollectionUtils.isEmpty(nodeList)) {
            return StringUtils.EMPTY;
        }

        List<String> hadoopNodeIps = nodeList.stream().map(Node::getIp).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(hadoopNodeIps)) {
            return StringUtils.EMPTY;
        }
        return String.join(",", new HashSet<>(hadoopNodeIps));
    }

}
