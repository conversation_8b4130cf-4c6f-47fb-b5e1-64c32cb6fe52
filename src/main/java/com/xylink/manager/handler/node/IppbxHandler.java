package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.model.em.Labels;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * ippbx node 配置处理实现
 */
public class IppbxHandler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(IppbxHandler.class);


    @Override
    public NodeHandler configureConfigMap() {
        if(node==null) return this;

        configureDistributeIP(node);

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();

        allIpMap.put(NetworkConstants.IPPBX_PUBLIC_IP, node.getExternalIp());
        allIpMap.put(NetworkConstants.IPPBX_INTERNAL_IP, node.getInternalIp());
        allIpMap.put(NetworkConstants.IPPBX_DOMAIN, StringUtils.isBlank(node.getDomain()) ? node.getExternalIp() : node.getDomain());

        if(node.getLabelMap().get(Labels.ippbx_siggw.label())) allIpMap.put(Constants.interIps.get(Labels.ippbx_siggw.label()), node.getInternalIp());

        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);

        return this;

    }
}
