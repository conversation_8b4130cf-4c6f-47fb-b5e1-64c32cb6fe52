package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.manager.model.em.Labels;
import com.xylink.util.ClusterUtil;
import com.xylink.util.Ipv6Util;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * main node 配置处理实现
 */
@Component
public class SignalMcHandler extends NodeHandler {

    @Override
    public NodeHandler configureConfigMap() {
        if (Objects.isNull(node)) {
            return this;
        }

        configureDistributeIP(node);
        handleDistributeIP(node, node.getType());
        List<String> enableLabels = node.getLabelMap()
                .entrySet()
                .stream()
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        updateAllIp(enableLabels);
        updateSigServer(enableLabels);
        return this;
    }

    /**
     * 更新all-ip
     *
     * @param enableLabels
     */
    private void updateAllIp(List<String> enableLabels) {
        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();
        enableLabels.stream().filter(Constants.interIps::containsKey).forEach(label -> allIpMap.put(Constants.interIps.get(label), node.getInternalIp()));
        enableLabels.stream().filter(Constants.exterIps::containsKey).forEach(label -> allIpMap.put(Constants.exterIps.get(label), node.getExternalIp()));
        ClusterUtil.updateMcIpConfig(allIpMap, enableLabels.contains(Labels.mc.label()), Ipv6Util.handlerIpv6Addr(node.getInternalIp()), node.getName());
        deployService.patchConfigMapAllIpForAddData(allIpMap);
    }
}
