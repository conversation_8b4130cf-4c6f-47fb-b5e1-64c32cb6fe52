package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.config.MysqlConstants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.cm.ICMDto;
import com.xylink.manager.model.cm.MysqlSlaveCM;
import com.xylink.manager.model.em.Labels;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * mysql-slave node 配置处理实现
 */
public class MysqlslaveHandler extends NodeHandler {
    private final static Logger logger = LoggerFactory.getLogger(MysqlslaveHandler.class);

    @Override
    public NodeHandler configureConfigMap() {
        if (node == null) return this;
        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();
        List<String> enableLabels = node.getLabelMap()
                .entrySet()
                .stream()
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        //内网服务地址
        enableLabels.stream().filter(label -> StringUtils.isNotBlank(Constants.interIps.get(label)))
                .forEach(label -> allIpMap.put(Constants.interIps.get(label), node.getInternalIp()));

        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);
        return this;
    }

    @Override
    protected NodeHandler initDefaultNodeConfig() {

        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setReportInternalIp(deployMessage.getIp());
        node.setType(Labels.mysql_slave.label());

        Map<String, Boolean> labelMap = new HashMap<>();
        DefaultDeployStructureEnumInvoke.services(Labels.mysql_slave.label()).forEach(label -> labelMap.put(label, false));

        //默认只部署mysqlslave, 不部署其他服务，避免服务迁移影响当前业务
        labelMap.put(Labels.mysql_slave.label(), true);
        node.setLabelMap(labelMap);
        super.node = node;
        return this;
    }

    @Override
    protected <T extends ICMDto> NodeHandler afterConfigureICMDto(T t) {
        if (t instanceof MysqlSlaveCM) {
            MysqlSlaveCM mysqlSlaveCM = (MysqlSlaveCM) t;
            Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();
            String ip;
            String port;
            if (StringUtils.isNotBlank(mysqlSlaveCM.getInternalIp())) {
                ip = mysqlSlaveCM.getInternalIp();
                port = "3306";
            } else {
                deployService.patchConfigMapAllIpForAddData(allIpMap);
                ip = allIpMap.get(NetworkConstants.STANDBY_SYSTEM_DATABASE_IP);
                port = allIpMap.get(NetworkConstants.STANDBY_SYSTEM_DATABASE_PORT);
            }
            if (StringUtils.isBlank(ip)) {
                logger.info("Mysql slave ip is null.Cancel SET_READ_ONLY_PROPERTY_TEMPLATE.");
                return this;
            }
            String updateSql = MysqlConstants.SET_READ_ONLY_PROPERTY_TEMPLATE.replace("{value}", mysqlSlaveCM.getMode());
            updateSql += MysqlConstants.SET_SUPER_READ_ONLY_PROPERTY_TEMPLATE.replace("{value}", mysqlSlaveCM.getMode());
            jdbcUtils.setMysqlProperties(updateSql, ip, port, "dbbak", "U1O5ZeRyLFd#u9T6TF9h");
        }
        return this;
    }
}
