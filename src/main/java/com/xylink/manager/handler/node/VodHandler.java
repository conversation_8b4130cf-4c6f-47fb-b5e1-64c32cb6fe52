package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.SigServerHaConfigMap;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.base.SystemModeConfig;
import com.xylink.util.Ipv6Util;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * vod node 配置处理实现
 */
public class VodHandler extends NodeHandler {
    private final static Logger logger = LoggerFactory.getLogger(VodHandler.class);

    @Override
    public NodeHandler configureConfigMap() {
        if (node == null) return this;
        configureDistributeIP(node);

        if (SystemModeConfig.isCmsOrXms()) {
            handleDistributeIP(node, Labels.vodnetwork.label());
        }
        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();
        allIpMap.put(NetworkConstants.VOD_DOMAIN_NAME, StringUtils.isBlank(node.getDomain()) ? node.getExternalIp() : node.getDomain());
        allIpMap.put(NetworkConstants.VOD_NGINX_PORT, StringUtils.isBlank(node.getNginxPort()) ? "80" : node.getNginxPort());
        allIpMap.put(NetworkConstants.VOD_NGINX_SSL_PORT, StringUtils.isBlank(node.getNginxSslPort()) ? "443" : node.getNginxSslPort());
        allIpMap.put(NetworkConstants.VOD_INTERNAL_IP, node.getInternalIp());
        allIpMap.put(NetworkConstants.VOD_PUBLIC_IP, node.getExternalIp());

        List<String> enableLabels = node.getLabelMap()
                .entrySet()
                .stream()
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        // 如果部署有 rmserver
        SigServerHaConfigMap.clearNodesRecordInAllConfigMapWithoutLabel(Labels.rmserver);
        if (enableLabels.contains(Labels.rmserver.label())) {
            SigServerHaConfigMap.updateAllSigServerHa(Labels.rmserver, node.getName(), node.getInternalIp());
        }

        if (enableLabels.contains(Labels.srs.label())) {
            allIpMap.put("SRS_IP", node.getInternalIp());
        }

        if (enableLabels.contains(Labels.avcloudapi.label())) {
            allIpMap.put(NetworkConstants.AVCLOUDAPI_IP, node.getInternalIp());
        }
        if (enableLabels.contains(Labels.mcaccess.label())) {
            allIpMap.put(NetworkConstants.MCACCESS_IP, node.getInternalIp());
        }
        if (enableLabels.contains(Labels.vod_fileserver.label())) {
            allIpMap.put("VOD_FILESERVER_IP", node.getInternalIp());
        }

        //vodserver是有状态的，所以需要将所有vodserver服务器节点的hostname和ip映射存储起来
        if (enableLabels.contains(Labels.vod.label())) {
            List<Pod> pods = deployService.listPodsByAppLabel("private-vod");
            if (!CollectionUtils.isEmpty(pods)) {
                String hostNameIps = pods.stream().map(pod -> pod.getNodeName() + ":" + Ipv6Util.handlerIpv6Addr(pod.getIp())).collect(Collectors.joining(","));
                // hostname1:ip1,hostname2:ip2
                allIpMap.put("VODSERVER_HOSTNAME_IP_MAPPING", hostNameIps);
            }
        }

        //多节点服务
        DefaultDeployStructureEnumInvoke.services(node.getType())
                .stream()
                .map(Labels::labelOf)
                .filter(Labels::isDistributed)
                .filter(label -> StringUtils.isNotBlank(label.loadblanceKey()))
                .forEach(label -> allIpMap.put(label.loadblanceKey(), getDistributeIp(label.label())));

        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);
        return this;

    }

    @Override
    protected NodeHandler initDefaultNodeConfig() {

        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setExternalIp(deployMessage.getIp());
        node.setDomain(deployMessage.getIp());
        node.setNginxPort("80");
        node.setNginxSslPort("443");
        node.setReportInternalIp(deployMessage.getIp());
        node.setType(Labels.vod.label());

        Map<String, Boolean> labelMap = new HashMap<>();
        DefaultDeployStructureEnumInvoke.services(Labels.vod.label()).forEach(label -> labelMap.put(label, true));
        labelMap.put(Labels.srs.label(), false);
        labelMap.put(Labels.vodclustermgr.label(), false);

        String installType = deployService.getConfigMapManagerData().getData().getOrDefault("install_type", "normal");
        if ("min".equalsIgnoreCase(installType)) {
            deployService.scaleDeployment("private-vodedit", Constants.NAMESPACE_DEFAULT, 0);
        }
        node.setLabelMap(labelMap);
        super.node = node;
        return this;
    }
}
