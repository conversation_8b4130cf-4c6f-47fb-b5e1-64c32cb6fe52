package com.xylink.manager.handler.node;

import com.xylink.config.NetworkConstants;
import com.xylink.manager.model.deploy.Deployment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * matrix node 配置处理实现
 * matrix主节点，需要做前置校验
 */
public class MatrixHandler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(MatrixHandler.class);


    @Override
    public NodeHandler configureConfigMap() {
        if (node == null) return this;
        configureDistributeIP(node);

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();

        allIpMap.put(NetworkConstants.MATRIX_INTERNAL_IP, node.getInternalIp());
        List<Deployment> deploymentList = deployService.listDeploymentByAppLabel("private-matrix-mysql");
        if (!CollectionUtils.isEmpty(deploymentList)) {
            allIpMap.put(NetworkConstants.MATRIX_DATABASE_IP, node.getInternalIp());
        }

        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);
        return this;
    }

    @Override
    protected NodeHandler initDefaultNodeConfig() {
        super.initDefaultNodeConfig();
        return this;
    }
}
