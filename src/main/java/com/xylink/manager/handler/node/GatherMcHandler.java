package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.model.deploy.Node;
import com.xylink.manager.model.em.Labels;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * gather mc node 配置处理实现。
 * gather mc 与 mc 有配置关联，gather mc是否部署，会导致mc的配置跟随变更。
 */
public class GatherMcHandler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(GatherMcHandler.class);


    @Override
    public NodeHandler configureConfigMap() {
        if (node == null) return this;

        List<Node> nodes = deployService.listNodesByAppLabel(Labels.gather_mc.label());

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();

        allIpMap.put(NetworkConstants.HAS_GATHER_MC, !nodes.isEmpty() ? "true" : "false");

        if ("false".equalsIgnoreCase(allIpMap.get(NetworkConstants.HAS_GATHER_MC))) {
            allIpMap.put(NetworkConstants.GATHER_MC_PRIVATE_IP, "127.0.0.1");
        }

        //内网ip
        List<String> enableLabels = node.getLabelMap().entrySet().stream().filter(x -> x.getValue()).map(x -> x.getKey()).collect(Collectors.toList());
        enableLabels.stream().filter(Constants.interIps::containsKey).forEach(label -> allIpMap.put(Constants.interIps.get(label), node.getInternalIp()));

        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);

        return this;
    }
}
