package com.xylink.manager.handler.node.mid;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.model.em.DefaultConfigmapDataEnum;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.clustersetting.failover.telnet.TelnetClient;
import com.xylink.util.IdPoolUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/9/5 2:29 PM
 */
public class ZookeeperHandlerAdapter extends AbstractMidHandlerAdapter implements MidHandlerAdapter {
    private static final String ALL_ZOOKEEPER_CONFIGMAP = "all-zookeeper";
    private static final String[] ZOOKEEPER_ID_POOL;

    static {
        ZOOKEEPER_ID_POOL = new String[]{"1", "2", "3"};
    }

    @Override
    public void doConfigureConfigMap(NodeDto node) {
        Map<String, String> allIpMap = getDeployService().getConfigMapAllIp().getData();
        // 如果改节点部署的是zookeeper  则忽略zookeeper_cluster
        List<String> enableLabels = node.getLabelMap().entrySet().stream().filter(Map.Entry::getValue).map(Map.Entry::getKey).collect(Collectors.toList());
        if (enableLabels.contains(Labels.zookeeper.label())) {
            //内网ip
            String networkName = Constants.interIps.get(node.getType());
            if (StringUtils.isNotEmpty(networkName)) {
                allIpMap.put(networkName, node.getInternalIp());
            }
            //外网ip
            String networkPubName = Constants.exterIps.get(node.getType());
            if (StringUtils.isNotEmpty(networkPubName)) {
                allIpMap.put(networkPubName, StringUtils.isBlank(node.getExternalIp()) ? node.getInternalIp() : node.getExternalIp());
            }
            allIpMap.put(NetworkConstants.MASTER_ZOOKEEPER_IP, node.getInternalIp());
            logger.info("update all-ip: {}", allIpMap);
            getDeployService().patchConfigMapAllIpForAddData(allIpMap);
            return;
        }
        clearNodesRecordInAllConfigMapWithoutLabel(Labels.zookeeper_cluster, ALL_ZOOKEEPER_CONFIGMAP);
        // all-kafka 配置
        if (enableLabels.contains(Labels.zookeeper_cluster.label())) {
            handleDistributeIP(node, Labels.zookeeper.label());
            setZookeeperMyId(node);
        } else {
            // 去掉本节点 ZK_ID_IP
            clearNumberMappingIpConfigMap(node);
        }
        // 非集群模式配置 ZOOKEEPER_IP: **************
        // 集群模式配置   ZOOKEEPER_IP: **************:2181,**************:2181,**************
        // 需要设置 MASTER_ZOOKEEPER_IP
        Pair<Set<String>, Set<String>> ips = getNodeInternalIpAndPublicIpByLabel(Labels.zookeeper.label(), "private-zookeeper-cluster");
        Set<String> zookeeperInternalIps = ips.getRight();
        if (!CollectionUtils.isEmpty(zookeeperInternalIps)) {
            allIpMap.put(NetworkConstants.ZOOKEEPER_IP, StringUtils.join(zookeeperInternalIps, ":2181,"));
        }
        allIpMap.put(NetworkConstants.MASTER_ZOOKEEPER_IP, getRandomIp(zookeeperInternalIps, node));
        logger.info("update all-ip: {}", allIpMap);
        getDeployService().patchConfigMapAllIpForAddData(allIpMap);
    }

    private Map<String, String> setZookeeperMyId(NodeDto node) {
        String configmapName = ALL_ZOOKEEPER_CONFIGMAP;
        ConfigMap configMap = getDeployService().getConfigMapByName(configmapName, Constants.NAMESPACE_DEFAULT);
        //没有该all-* configmap 则自动创建
        Map<String, String> configmapData = configMap == null ? DefaultConfigmapDataEnum.initDefault(Labels.zookeeper.label()) : configMap.getData();
        numberInConfigMap(configmapData, node);
        getDeployService().patchConfigMap(configmapName, Constants.NAMESPACE_DEFAULT, d -> {
            d.putAll(configmapData);
        });
        return configmapData;
    }

    /**
     * 去掉本节点 ZK_ID_IP
     *
     * @return
     */
    private Map<String, String> clearNumberMappingIpConfigMap(NodeDto node) {
        ConfigMap configMap = getDeployService().getConfigMapByName(ALL_ZOOKEEPER_CONFIGMAP, Constants.NAMESPACE_DEFAULT);
        Map<String, String> configmapData = configMap == null ? DefaultConfigmapDataEnum.initDefault(Labels.zookeeper.label()) : configMap.getData();
        configmapData.entrySet().removeIf(entry ->
                clearNumberMappingIpConfigMapInner(entry, node)
        );
        getDeployService().patchConfigMap(ALL_ZOOKEEPER_CONFIGMAP, Constants.NAMESPACE_DEFAULT, d -> {
            d.putAll(configmapData);
        });
        return configmapData;
    }

    /**
     * 清除 key  ZK_x_IP && nodename-MYID
     *
     * @param stringEntry
     * @return
     */
    private boolean clearNumberMappingIpConfigMapInner(Map.Entry<String, String> stringEntry, NodeDto node) {
        return (stringEntry.getKey().startsWith("ZK_") && stringEntry.getValue().equals(node.getInternalIp())) || (stringEntry.getKey().equals(node.getName() + "-MYID"));
    }

    /**
     * HOSTNAME-MYID : 每个zk实例对应的myid值
     */
    private void numberInConfigMap(Map<String, String> allZookeeperConfigMap, NodeDto node) {
        // key
        String key = node.getName() + "-MYID";
        // 获取部署的kafka 在之前的计数上增加1
        if (allZookeeperConfigMap.containsKey(key)) {
            return;
        }
        // 获取部署的zookeeper, 从池中取【1，2，3】
        List<String> inUsed = new ArrayList<>();
        allZookeeperConfigMap.forEach((itemKey, itemValue) -> {
            if (itemKey.endsWith("-MYID")) {
                inUsed.add(itemValue);
            }
        });
        String value = IdPoolUtils.getId(ZOOKEEPER_ID_POOL, inUsed);
        allZookeeperConfigMap.put(key, value);
        numberMappingIpConfigMap(allZookeeperConfigMap, value, node);
    }

    /**
     * 每个zk实例对应的myid-ip关系
     *
     * @param allZookeeperConfigMap
     * @param sequence
     */
    private void numberMappingIpConfigMap(Map<String, String> allZookeeperConfigMap, String sequence, NodeDto node) {
        String key = "ZK_" + sequence + "_IP";
        allZookeeperConfigMap.put(key, node.getInternalIp());
    }

    private String getRandomIp(Set<String> zookeeperInternalIps, NodeDto node) {
        if (!CollectionUtils.isEmpty(zookeeperInternalIps)) {
            for (String ip : zookeeperInternalIps) {
                if (new TelnetClient(ip, 2181).tryTelnet()) {
                    return ip;
                }
            }
        }
        return node.getInternalIp();
    }
}
