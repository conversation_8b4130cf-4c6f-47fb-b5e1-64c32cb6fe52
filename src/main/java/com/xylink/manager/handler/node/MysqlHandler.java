package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.config.MysqlConstants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.cm.ICMDto;
import com.xylink.manager.model.cm.MysqlCM;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.clustersetting.domain.ClusterConfigEnum;
import com.xylink.util.K8sUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * mysql node 配置处理实现
 * kafka有可能需要设置公网ip，部分场景dmcu可能需要连接kafka公网IP
 */
public class MysqlHandler extends NodeHandler {
    private final static Logger logger = LoggerFactory.getLogger(MysqlHandler.class);

    @Override
    public NodeHandler configureConfigMap() {
        if (node == null) return this;
        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();
        List<String> enableLabels = node.getLabelMap()
                .entrySet()
                .stream()
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        if (enableLabels.contains(Labels.kafka.label()))
            allIpMap.put(NetworkConstants.KAFKA_PUBLIC_IP, node.getExternalIp());

        //高可用模式时,mysql节点保存时不覆盖DATABASE_IP
        Map<String, String> allCluster = K8sUtils.getConfigMap(Constants.CONFIGMAP_CLUSTER);
        if (StringUtils.isNotBlank(allCluster.get(ClusterConfigEnum.MAIN_DATABASE.getHaAddressKey()))) {
            enableLabels.remove(Labels.mysql.label());
        }

        //内网服务地址
        enableLabels.stream().filter(label -> StringUtils.isNotBlank(Constants.interIps.get(label)))
                .forEach(label -> allIpMap.put(Constants.interIps.get(label), node.getInternalIp()));

        if (StringUtils.isNotBlank(node.getNodeDbPort()))
            allIpMap.put(NetworkConstants.DATABASE_PORT, node.getNodeDbPort());

        if (!enableLabels.contains(Labels.mysql.label()) && !enableLabels.contains(Labels.dm.label())
                && !enableLabels.contains(Labels.st.label()) && !enableLabels.contains(Labels.kingbase.label())
                && !enableLabels.contains(Labels.oceanbase.label())) {
            allIpMap.put(NetworkConstants.MASTER_DATABASE_IP, node.getInternalIp());
        }

        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);

        //init zookeeper client

        return this;
    }


    @Override
    protected NodeHandler initDefaultNodeConfig() {

        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setReportInternalIp(deployMessage.getIp());
        node.setType(Labels.mysql.label());

        Map<String, Boolean> labelMap = new HashMap<>();
        DefaultDeployStructureEnumInvoke.services(Labels.mysql.label()).forEach(label -> labelMap.put(label, true));

        node.setLabelMap(labelMap);
        super.node = node;
        return this;
    }

    @Override
    protected <T extends ICMDto> NodeHandler afterConfigureICMDto(T t) {
        if (t instanceof MysqlCM) {
            MysqlCM mysqlCM = (MysqlCM) t;
            Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();
            String ip = allIpMap.get(NetworkConstants.DATABASE_IP);
            String port = allIpMap.get(NetworkConstants.DATABASE_PORT);
            String updateSql = MysqlConstants.SET_READ_ONLY_PROPERTY_TEMPLATE.replace("{value}", mysqlCM.getMode());
            updateSql += MysqlConstants.SET_SUPER_READ_ONLY_PROPERTY_TEMPLATE.replace("{value}", mysqlCM.getMode());
            jdbcUtils.setMysqlProperties(updateSql, ip, port, "dbbak", "U1O5ZeRyLFd#u9T6TF9h");
        }
        return this;
    }
}
