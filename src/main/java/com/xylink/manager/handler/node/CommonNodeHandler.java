package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.api.NoahApiService;
import com.xylink.manager.service.server.ServerGroupStrategy;
import com.xylink.manager.service.server.ServerGroupStrategyFactory;
import com.xylink.util.SpringBeanUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> create on 2025/2/24
 */
public class CommonNodeHandler extends NodeHandler {

    private static final Logger logger = LoggerFactory.getLogger(CommonNodeHandler.class);

    @Override
    protected NodeHandler initDefaultNodeConfig() {
        logger.info("start configure ConfigMap of common-{}", deployMessage.getHostname());
        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setExternalIp(deployMessage.getIp());
        node.setReportInternalIp(deployMessage.getIp());
        node.setType(deployMessage.getType());
        Map<String, Boolean> labelMap = new HashMap<>();
        getCommonNodeServices().forEach(label -> labelMap.put(label, true));

        //默认不启动
        labelMap.put(Labels.asralg2.label(), false);
        node.setLabelMap(labelMap);
        super.node = node;
        return this;
    }

    @Override
    protected NodeHandler configureConfigMap() {
        logger.info("start configure ConfigMap of common-{}", node.getName());
        if (node == null) {
            return this;
        }
        //配置多活IP：像all-{label}中写入 能部署该label的主机IP等信息，使用主机名区分
        configureDistributeIP(node);

        //更新all-ip
        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();

        //通知配置中心标签变化
        Map<String, Boolean> labelMap = node.getLabelMap();
        try {
            notifyDeployLabelToNoah(labelMap);
            logger.info("notify deploy label to noah finished");
        } catch (Exception ex) {
            logger.info("notify deploy label to noah error {}", ex.getMessage(), ex);
        }

        //更新内网IP
        String nodeDomainKey = node.getName() + NetworkConstants.SUFFIX_DOMAIN;
        String nodeInterIpKey = node.getName() + NetworkConstants.SUFFIX_INTERNAL_IP;
        String nodePubIpKey = node.getName() + NetworkConstants.SUFFIX_PUBLIC_IP;
        allIpMap.put(nodeInterIpKey, node.getInternalIp());
        allIpMap.put(nodePubIpKey, node.getExternalIp());
        allIpMap.put(nodeDomainKey, node.getDomain());


        //多节点服务，配置all-ip中的负载均衡KEY（用于ip-change渲染nginx的访问服务upstream等）
        getCommonNodeServices()
                .stream()
                .map(Labels::labelOf)
                .filter(Labels::isDistributed)
                .filter(label -> StringUtils.isNotBlank(label.loadblanceKey()))
                .forEach(label -> allIpMap.put(label.loadblanceKey(), getDistributeIp(label.label())));

        logger.info("update all-ip: " + allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);
        return this;
    }

    private void notifyDeployLabelToNoah(Map<String, Boolean> labelMap) {
        NoahApiService noahApiService = SpringBeanUtil.getBean(NoahApiService.class);
        noahApiService.notifyDeployLabelChange();
    }


    private List<String> getCommonNodeServices() {
        List<String> services = new ArrayList<>();
        final ServerGroupStrategyFactory strategyFactory = SpringBeanUtil.getBean(ServerGroupStrategyFactory.class);
        List<ServerGroupStrategy> strategyList = strategyFactory.create(Constants.NODE_TYPE_COMMON_NODE);
        for (ServerGroupStrategy strategy : strategyList) {
            services.addAll(strategy.genServices());
        }
        return services;
    }

}
