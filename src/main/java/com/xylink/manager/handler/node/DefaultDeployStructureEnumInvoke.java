package com.xylink.manager.handler.node;

import com.xylink.manager.model.em.DeployStructureAnKeEnum;
import com.xylink.manager.model.em.DeployStructureEnum;
import com.xylink.manager.model.em.Platform;
import com.xylink.manager.service.base.PlatformConfig;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/9/17 2:47 下午
 */
public class DefaultDeployStructureEnumInvoke {

    public static List<String> services(String type) {
        return Platform.ANKE.equals(PlatformConfig.current()) ?
                DeployStructureAnKeEnum.services(type) : DeployStructureEnum.services(type);
    }

}
