package com.xylink.manager.handler.node;

import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.em.Labels;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 2023/11/22/11:39
 * @Description:
 */
public class ThirdProxyHandler extends NodeHandler {
    private final static Logger logger = LoggerFactory.getLogger(ThirdBridgeHandler.class);

    @Override
    protected NodeHandler configureConfigMap() {
        if(node==null) return this;
        configureDistributeIP(node);
        handleDistributeIP(node, Labels.third_proxy.label());
        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();
        if (node.getLabelMap().get(Labels.third_proxy.label())) {
            allIpMap.put("THIRD_BRIDGE_PROXY_PUBLIC_IP", StringUtils.isBlank(node.getDomain()) ? node.getExternalIp() : node.getDomain());
            allIpMap.put("THIRD_BRIDGE_PROXY_INTERNAL_IP", node.getInternalIp());
        }

        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);
        return this ;
    }

    @Override
    protected NodeHandler initDefaultNodeConfig() {
        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setReportInternalIp(deployMessage.getIp());
        node.setType(Labels.third_proxy.label());
        Map<String, Boolean> labelMap = new HashMap<>();
        DefaultDeployStructureEnumInvoke.services(Labels.third_proxy.label()).forEach(label -> labelMap.put(label, true));
        node.setLabelMap(labelMap);
        super.node = node;
        return this;
    }
}
