package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.model.em.Labels;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * h323 node 配置处理实现
 */
public class H323Handler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(H323Handler.class);

    @Override
    public NodeHandler configureConfigMap() {
        if (node == null) return this;
        configureDistributeIP(node);

        //h323 为网关集合，也做一下ip记录, 如果all-h323没创建就 默认创建一个空configmap
        ConfigMap configMap = deployService.getConfigMapByName(Constants.CONFIGMAP_H323, Constants.NAMESPACE_DEFAULT);
        if (configMap == null) {
            logger.info("find no configmap named all-h323, will create it!");
            deployService.createEmptyConfigMap(Constants.CONFIGMAP_H323, Constants.NAMESPACE_DEFAULT);
        }
        handleDistributeIP(node, Labels.h323.label());
        //all-ip配置
        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();
        List<String> enableLabels = node.getLabelMap()
                .entrySet()
                .stream()
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        //内网服务地址
        enableLabels.stream().filter(Constants.interIps::containsKey).forEach(label -> allIpMap.put(Constants.interIps.get(label), node.getInternalIp()));
        logger.info("update all-ip: {}", allIpMap.toString());
        deployService.patchConfigMapAllIpForAddData(allIpMap);

        deployService.patchConfigMap(Constants.CONFIGMAP_H323, Constants.NAMESPACE_DEFAULT, d -> {
            d.put(node.getName() + "-SIP_SERVER_DOMAIN_NAME", StringUtils.isBlank(node.getDomain()) ? node.getExternalIp() : node.getDomain());
        });
        return this;
    }

    @Override
    protected NodeHandler initDefaultNodeConfig() {

        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setExternalIp(deployMessage.getIp());
        node.setDomain(deployMessage.getIp());
        node.setReportInternalIp(deployMessage.getIp());
        node.setType(Labels.h323.label());

        Map<String, Boolean> labelMap = new HashMap<>();
        DefaultDeployStructureEnumInvoke.services(Labels.h323.label()).forEach(label -> labelMap.put(label, true));

        node.setLabelMap(labelMap);
        super.node = node;
        return this;
    }

}
