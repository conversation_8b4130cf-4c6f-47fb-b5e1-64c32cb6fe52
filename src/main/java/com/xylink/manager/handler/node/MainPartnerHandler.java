package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.model.deploy.Node;
import com.xylink.manager.model.em.DefaultConfigmapDataEnum;
import com.xylink.manager.model.em.Labels;
import com.xylink.util.ClusterUtil;
import com.xylink.util.Ipv6Util;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * main-partner node 配置处理实现
 */
public class MainPartnerHandler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(MainPartnerHandler.class);


    @Override
    public NodeHandler configureConfigMap() {
        if (node == null) return this;
        if (StringUtils.isBlank(node.getDomain())) node.setDomain(node.getExternalIp());

        configureDistributeIP(node);

        handleDistributeIP(node, Labels.mc.label());
        //main 默认部署 openresty-main，前端label不显示，但是需要更新all-openresty-main
        handleDistributeIP(node, Labels.main_partner.label());

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();

        List<String> enableLabels = node.getLabelMap().entrySet().stream().filter(Map.Entry::getValue).map(Map.Entry::getKey).collect(Collectors.toList());

        //内网服务地址
        enableLabels.stream().filter(Constants.interIps::containsKey).forEach(label -> allIpMap.put(Constants.interIps.get(label), node.getInternalIp()));

        //外网服务地址
        enableLabels.stream().filter(Constants.exterIps::containsKey).forEach(label -> allIpMap.put(Constants.exterIps.get(label), node.getExternalIp()));

        if (enableLabels.contains(Labels.push.label())) {
            allIpMap.put("PUSH_INTERNAL_IP", node.getInternalIp());
        }

//        if (enableLabels.contains(Labels.accesssig.label())) {
//            allIpMap.put("ACCESS_SIG_IP", node.getInternalIp());
//        }

        if (enableLabels.contains(Labels.mms.label())) {
            allIpMap.put("MMS_DISPATCHER_IP", node.getInternalIp());
            allIpMap.put("DEVICE_IP", node.getInternalIp());
            allIpMap.put("DSU_IP", node.getInternalIp());
            allIpMap.put("MMS_PERMISSION_IP", node.getInternalIp());
            allIpMap.put("MMSPROXY_IP", node.getInternalIp());
        }

        ConfigMap configMap = deployService.getConfigMapByName(Constants.CONFIGMAP_ALL_MMS, Constants.NAMESPACE_DEFAULT);
        Map<String, String> allMms = configMap == null ? DefaultConfigmapDataEnum.initDefault(Labels.mms.label()) : configMap.getData();
        allMms.put(node.getName() + "-MMS_DEVICESTATE_IP", node.getInternalIp());
        allMms.put(node.getName() + "-MMS_DISPATCHER_IP", node.getInternalIp());
        allMms.put(node.getName() + "-MMS_EDGE_IP", node.getInternalIp());
        allMms.put(node.getName() + "-MMS_LOGIC_IP", node.getInternalIp());

        ClusterUtil.updateMcIpConfig(allIpMap, enableLabels.contains(Labels.mc.label()), Ipv6Util.handlerIpv6Addr(node.getInternalIp()), node.getName());

        updateSigServer(enableLabels);

        //多节点服务
        DefaultDeployStructureEnumInvoke.services(Labels.main_partner.label())
                .stream()
                .map(Labels::labelOf)
                .filter(Labels::isDistributed)
                .filter(label -> StringUtils.isNotBlank(label.loadblanceKey()))
                .forEach(label -> allIpMap.put(label.loadblanceKey(), getDistributeIp(label.label())));

        // 夜莺监控
        updateNightingale(enableLabels, allIpMap);
        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);
        deployService.patchConfigMap(Constants.CONFIGMAP_ALL_MMS, Constants.NAMESPACE_DEFAULT, d -> {
            d.putAll(allMms);
        });

        // 调用链jaeger处理
        JaegerHandler.configureJaegerConfigMap(this, enableLabels, node.getInternalIp());

        updatePresenceServer(enableLabels);
        // 241220-分区云5.2启用点对点号码池
        updateP2PPoolNumber(enableLabels);
        return this;
    }

    private void updateNightingale(List<String> enableLabels, Map<String, String> allIpMap) {
        if (enableLabels.contains(Labels.nightingale.label())) {
            deployedNightingaleServer(allIpMap);
            if (enableLabels.contains(Labels.nightingale_kafka.label())) {
                allIpMap.put(NetworkConstants.N9E_KAFKA_ADDRESS, node.getInternalIp() + ":9093");
            }
        } else {
            // 判断是否之前在改节点部署
            if (node.getInternalIp().equalsIgnoreCase(allIpMap.get(NetworkConstants.N9E_IP))) {
                cancelDeployNightingaleServer(allIpMap);
            }
        }
        // 判断是否部署了 nightingale_mid
        List<Node> nodeList = deployService.listNodesByAppLabel("nightingale-mid");
        if (CollectionUtils.isEmpty(nodeList)) {
            allIpMap.putAll(nightingaleCommonMidSetting(allIpMap));
        }
    }

    @Override
    protected NodeHandler initDefaultNodeConfig() {
        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setReportInternalIp(deployMessage.getIp());
        node.setType(Labels.main_partner.label());

        Map<String, Boolean> labelMap = new HashMap<>();
        DefaultDeployStructureEnumInvoke.services(Labels.main_partner.label()).forEach(label -> labelMap.put(label, true));

        labelMap.put(Labels.meetingmonitor.label(), false);
        labelMap.put(Labels.tsa.label(), false);
        labelMap.put(Labels.sdkcallback.label(), false);
        labelMap.put(Labels.mc.label(), false);
        labelMap.put(Labels.dating.label(), false);
        labelMap.put(Labels.vcs.label(), false);
        labelMap.put(Labels.presence.label(), false);
        labelMap.put(Labels.ocean.label(), false);
        labelMap.put(Labels.sensitiveword.label(), false);
        labelMap.put(Labels.message_push.label(), false);
        labelMap.put(Labels.liveness_probe.label(), false);
        labelMap.put(Labels.tsa_mp.label(), false);
        labelMap.put(Labels.allocator_server.label(), false);
        labelMap.put(Labels.txlive.label(), false);
        labelMap.put(Labels.push.label(), false);
        labelMap.put(Labels.avcloudapi.label(), false);
        labelMap.put(Labels.mcaccess.label(), false);

        String installType = deployService.getConfigMapManagerData().getData().getOrDefault("install_type", "normal");
        if ("min".equalsIgnoreCase(installType)) {
            labelMap.put(Labels.vote_statistics.label(), false);
            labelMap.put(Labels.vote.label(), false);
            labelMap.put(Labels.externalweb.label(), false);
            labelMap.put(Labels.inspection.label(), false);
            labelMap.put(Labels.tsa.label(), false);
            labelMap.put(Labels.im.label(), false);
            labelMap.put(Labels.nettool.label(), false);
            labelMap.put(Labels.sharing.label(), false);
            deployService.scaleDeployment("private-mkdoc", Constants.NAMESPACE_DEFAULT, 0);
        }

        node.setLabelMap(labelMap);
        super.node = node;
        return this;
    }

}
