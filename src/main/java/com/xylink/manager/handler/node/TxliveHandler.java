package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.model.em.Labels;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/09/05/17:30
 */
@Slf4j
public class TxliveHandler extends NodeHandler {
    @Override
    protected NodeHandler configureConfigMap() {
        if (node == null) {
            return this;
        }
        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();
        List<String> enableLabels = node.getLabelMap().entrySet().stream().filter(Map.Entry::getValue).map(Map.Entry::getKey).collect(Collectors.toList());
        //内网服务地址
        enableLabels.stream().filter(Constants.interIps::containsKey).forEach(label -> allIpMap.put(Constants.interIps.get(label), node.getInternalIp()));
        if (node.getLabelMap().containsKey(Labels.txlive.label()) && node.getLabelMap().get(Labels.txlive.label())) {
            allIpMap.put(NetworkConstants.TXLIVE_SERVER_IP, node.getInternalIp());
        }
        deployService.patchConfigMapAllIpForAddData(allIpMap);

        ConfigMap configMap = deployService.getConfigMapByNameNotNull(Constants.CONFIGMAP_TXLIVE, Constants.NAMESPACE_DEFAULT);
        Map<String, String> allTxLiveConfigMap = configMap.getData();
        String nodeInterIpKey = node.getName() + NetworkConstants.SUFFIX_INTERNAL_IP;
        String nodePubIpKey = node.getName() + NetworkConstants.SUFFIX_PUBLIC_IP;
        String domainKey = node.getName() + NetworkConstants.SUFFIX_DOMAIN;

        allTxLiveConfigMap.put(nodeInterIpKey, node.getInternalIp());
        allTxLiveConfigMap.put(nodePubIpKey, node.getExternalIp() == null ? "" : node.getExternalIp());
        allTxLiveConfigMap.put(domainKey, node.getDomain() == null ? "" : node.getDomain());
        deployService.patchConfigMap(Constants.CONFIGMAP_TXLIVE, Constants.NAMESPACE_DEFAULT, d -> {
            d.putAll(allTxLiveConfigMap);
        });
        return this;
    }

    @Override
    protected NodeHandler initDefaultNodeConfig() {

        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setExternalIp(deployMessage.getIp());
        node.setDomain(deployMessage.getIp());
        node.setReportInternalIp(deployMessage.getIp());
        node.setType(Labels.txlive.label());

        Map<String, Boolean > labelMap = new HashMap<>();
        DefaultDeployStructureEnumInvoke.services(node.getType()).forEach(label -> labelMap.put(label, false));
        node.setLabelMap(labelMap);
        super.node = node;
        return this;
    }
}
