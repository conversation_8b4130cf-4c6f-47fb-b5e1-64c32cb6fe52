package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.model.em.Labels;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 2023/10/31/10:18
 * @Description:
 */
public class ProxyBaseHandler extends NodeHandler {
    private final static Logger logger = LoggerFactory.getLogger(ProxyBaseHandler.class);


    @Override
    protected NodeHandler configureConfigMap() {
        if (node == null) return this;
        configureDistributeIP(node);
        handleDistributeIP(node, Labels.proxy_base.label());

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();
        List<String> enableLabels = node.getLabelMap().entrySet().stream().filter(Map.Entry::getValue).map(Map.Entry::getKey).collect(Collectors.toList());
        if (enableLabels.contains(Labels.third_proxy.label())) {
            allIpMap.put("THIRD_BRIDGE_PROXY_PUBLIC_IP", StringUtils.isBlank(node.getDomain()) ? node.getExternalIp() : node.getDomain());
            allIpMap.put("THIRD_BRIDGE_PROXY_INTERNAL_IP", node.getInternalIp());
        }

        allIpMap.put(Labels.proxy_base.loadblanceKey(), getDistributeIpByType(Labels.proxy_base.label()));

        checkBothHaproxyVodProxy();

        //多节点服务
        DefaultDeployStructureEnumInvoke.services(Labels.proxy_base.label())
                .stream()
                .map(Labels::labelOf)
                .filter(Labels::isDistributed)
                .filter(label -> StringUtils.isNotBlank(label.loadblanceKey()))
                .forEach(label -> allIpMap.put(label.loadblanceKey(), getDistributeIp(label.label())));


        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);

        return this;
    }

    /**
     * haproxy与vod_proxy同节点部署
     */
    private void checkBothHaproxyVodProxy() {
        Set<String> requiredLabels = new HashSet<>();
        requiredLabels.add(Labels.haproxy.label());
        requiredLabels.add(Labels.vod_proxy.label());
        boolean containsBoth = requiredLabels.stream()
                .allMatch(label -> node.getLabelMap().containsKey(label) && node.getLabelMap().get(label));
        ConfigMap configMap = deployService.getConfigMapByName(Constants.CONFIGMAP_RECORD, Constants.NAMESPACE_DEFAULT);
        // 全新部署新环境时
        if (Objects.isNull(configMap)) {
            return;
        }
        Map<String, String> allRecordMap = configMap.getData();
        if (containsBoth) {
            allRecordMap.put("BOTH_HAPROXY_VODPROXY_" + node.getName(), "true");
        } else {
            allRecordMap.remove("BOTH_HAPROXY_VODPROXY_" + node.getName());
        }
        logger.info("update all-record: {}", allRecordMap);
        deployService.patchConfigMap(Constants.CONFIGMAP_RECORD, Constants.NAMESPACE_DEFAULT, d -> {
            d.putAll(allRecordMap);
        });
    }

}
