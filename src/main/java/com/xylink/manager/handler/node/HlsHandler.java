package com.xylink.manager.handler.node;

import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.em.Labels;

import java.util.HashMap;
import java.util.Map;

/**
 * vod node 配置处理实现
 */
public class HlsHandler extends NodeHandler {

    @Override
    public NodeHandler configureConfigMap() {
        if(node==null) return this;

        configureDistributeIP(node);

        return this;

    }

    @Override
    protected NodeHandler initDefaultNodeConfig() {

        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setExternalIp(deployMessage.getIp());
        node.setDomain(deployMessage.getIp());
        node.setNginxPort("80");
        node.setNginxSslPort("443");
        node.setReportInternalIp(deployMessage.getIp());
        node.setType(Labels.hls.label());

        Map<String, Boolean > labelMap = new HashMap<>();
        DefaultDeployStructureEnumInvoke.services(Labels.hls.label()).forEach(label -> labelMap.put(label, true));
        node.setLabelMap(labelMap);
        super.node = node;
        return this;
    }
}
