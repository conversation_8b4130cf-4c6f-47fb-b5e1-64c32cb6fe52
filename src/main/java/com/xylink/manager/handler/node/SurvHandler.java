package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.deploy.Node;
import com.xylink.manager.model.em.Labels;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * surv node 配置处理实现
 */
public class SurvHandler extends NodeHandler {
    private final static Logger logger = LoggerFactory.getLogger(SurvHandler.class);

    @Override
    public NodeHandler configureConfigMap() {
        if (node == null) return this;
        configureDistributeIP(node);
        List<Node> nodes = deployService.listNodesByAppLabel(Labels.gather_mc.label());
        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();

        allIpMap.put(NetworkConstants.SURV_INTERNAL_IP, node.getInternalIp());
        allIpMap.put(NetworkConstants.SURV_PUBLIC_IP, node.getExternalIp());
        allIpMap.put(NetworkConstants.SURV_DOMAIN_NAME, StringUtils.isBlank(node.getDomain()) ? node.getExternalIp() : node.getDomain());
        allIpMap.put(NetworkConstants.SURV_NGINX_PORT, StringUtils.isBlank(node.getNginxPort()) ? "80" : node.getNginxPort());
        allIpMap.put(NetworkConstants.SURV_NGINX_SSL_PORT, StringUtils.isBlank(node.getNginxSslPort()) ? "443" : node.getNginxSslPort());


        allIpMap.put(NetworkConstants.HAS_GATHER_MC, !nodes.isEmpty() ? "true" : "false");

        if ("false".equalsIgnoreCase(allIpMap.get(NetworkConstants.HAS_GATHER_MC))){
            allIpMap.put(NetworkConstants.GATHER_MC_PRIVATE_IP, "127.0.0.1");
        }

        //内网ip
        List<String> enableLabels = node.getLabelMap()
                .entrySet()
                .stream()
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        enableLabels.stream().filter(Constants.interIps::containsKey).forEach(label -> allIpMap.put(Constants.interIps.get(label), node.getInternalIp()));

        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);
        return this;

    }

    @Override
    protected NodeHandler initDefaultNodeConfig() {


        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setExternalIp(deployMessage.getIp());
        node.setDomain(deployMessage.getIp());
        node.setNginxPort("80");
        node.setNginxSslPort("443");
        node.setReportInternalIp(deployMessage.getIp());
        node.setType(Labels.surv.label());

        Map<String, Boolean> labelMap = new HashMap<>();
        DefaultDeployStructureEnumInvoke.services(Labels.surv.label()).forEach(label -> labelMap.put(label, true));

        labelMap.put(Labels.gather_mc.label(), false);
        node.setLabelMap(labelMap);
        super.node = node;
        return this;
    }
}
