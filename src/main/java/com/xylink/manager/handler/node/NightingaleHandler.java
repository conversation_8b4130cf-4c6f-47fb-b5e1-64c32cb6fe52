package com.xylink.manager.handler.node;

import com.xylink.config.NetworkConstants;
import com.xylink.manager.model.em.Labels;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/04/11 11:58 上午
 */
public class NightingaleHandler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(NightingaleHandler.class);


    @Override
    public NodeHandler configureConfigMap() {
        if (node == null) return this;

        configureDistributeIP(node);

        List<String> enableLabels = node.getLabelMap().entrySet().stream().filter(Map.Entry::getValue).map(Map.Entry::getKey).collect(Collectors.toList());
        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();
        if (enableLabels.contains(Labels.nightingale.label())) {
            deployedNightingaleServer(allIpMap);
            if (enableLabels.contains(Labels.nightingale_kafka.label())) {
                allIpMap.put(NetworkConstants.N9E_KAFKA_ADDRESS, node.getInternalIp() + ":9093");
            }
        } else {
            // 判断是否之前在改节点部署
            if (node.getInternalIp().equalsIgnoreCase(allIpMap.get(NetworkConstants.N9E_IP))) {
                cancelDeployNightingaleServer(allIpMap);
            }
        }
        if (enableLabels.contains(Labels.nightingale_kafka.label())) {
            allIpMap.put(NetworkConstants.KAFKA_NIGHTINGALE_INTERNAL_IP, node.getInternalIp());
        } else {
            allIpMap.put(NetworkConstants.KAFKA_NIGHTINGALE_INTERNAL_IP, "127.0.0.1");
        }

        if (enableLabels.contains(Labels.nightingale_zookeeper.label())) {
            allIpMap.put(NetworkConstants.NIGHTINGALE_ZOOKEEPER_IP, node.getInternalIp());
        } else {
            allIpMap.put(NetworkConstants.NIGHTINGALE_ZOOKEEPER_IP, "127.0.0.1");
        }

        if (enableLabels.contains(Labels.nightingale_mid.label())) {
            allIpMap.put(NetworkConstants.N9E_REDIS_ADDRESS, address(this.node.getInternalIp(), NetworkConstants.N9E_REDIS_DEFULT_PORT));
            allIpMap.put(NetworkConstants.N9E_REDIS_USER, allIpMap.get(NetworkConstants.N9E_REDIS_DEFULT_USER));
            allIpMap.put(NetworkConstants.N9E_REDIS_PWD, allIpMap.get(NetworkConstants.N9E_REDIS_DEFULT_PWD));
            allIpMap.put(NetworkConstants.N9E_REDIS_MODE, allIpMap.get(NetworkConstants.N9E_REDIS_DEFULT_MODE));

            allIpMap.put(NetworkConstants.N9E_MYSQL_ADDRESS, address(this.node.getInternalIp(), NetworkConstants.N9E_MYSQL_DEFULT_PORT));
            allIpMap.put(NetworkConstants.N9E_MYSQL_USER, allIpMap.get(NetworkConstants.N9E_MYSQL_DEFULT_USER));
            allIpMap.put(NetworkConstants.N9E_MYSQL_PASSWORD, allIpMap.get(NetworkConstants.N9E_MYSQL_DEFULT_PWD));
            allIpMap.put(NetworkConstants.NIGHTINGALE_DATABASE_IP, this.node.getInternalIp());
            allIpMap.put(NetworkConstants.NIGHTINGALE_DATABASE_PORT, NetworkConstants.N9E_MYSQL_DEFULT_PORT);
        } else {
            allIpMap.putAll(nightingaleCommonMidSetting(allIpMap));
        }

        logger.info("update all-ip: " + allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);

        return this;
    }


}
