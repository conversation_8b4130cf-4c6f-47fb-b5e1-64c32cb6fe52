package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.manager.model.cm.IppbxSiggatewayCM;
import com.xylink.manager.model.em.Labels;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * ippbx 信令网关 node 配置处理实现
 * ippbx 信令网关 需要设置sn
 */
public class IppbxSiggatewayHandler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(IppbxSiggatewayHandler.class);


    @Override
    public NodeHandler configureConfigMap() {
        if(node==null) return this;
        configureDistributeIP(node);

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();


        if(node.getLabelMap().get(Labels.ippbx_siggw.label())) allIpMap.put(Constants.interIps.get(Labels.ippbx_siggw.label()), node.getInternalIp());

        logger.info("update all-ip: {}", allIpMap);
        if(node.getLabelMap().get(Labels.ippbx_siggw.label()))  deployService.patchConfigMapAllIpForAddData(allIpMap);


        return this;

    }


    @Override
    protected NodeHandler afterConfigure() {
        if (advanceConfig==null || jdbcUtils==null) return this;
        IppbxSiggatewayCM siggw = (IppbxSiggatewayCM) advanceConfig;
        if (StringUtils.isNotBlank(siggw.getSn())) {
            jdbcUtils.configureIppbxSigGatewaySN(siggw.getSn());
        }
        return this;
    }
}
