package com.xylink.manager.handler.node;

import com.google.common.collect.Lists;
import com.xylink.config.*;
import com.xylink.config.mapper.JsonMapper;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.cm.OpenrestyMainCM;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.model.deploy.DaemonSet;
import com.xylink.manager.model.deploy.Node;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.model.em.DefaultConfigmapDataEnum;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.AuditLogService;
import com.xylink.manager.service.ConfigService;
import com.xylink.manager.service.api.NoahApiService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.base.K8sSvcService;
import com.xylink.manager.service.base.PlatformConfig;
import com.xylink.manager.service.base.SystemModeConfig;
import com.xylink.manager.service.clustersetting.domain.ClusterConfigEnum;
import com.xylink.manager.service.db.JasyptService;
import com.xylink.manager.service.remote.buffet.BuffetRemoteClient;
import com.xylink.manager.service.server.ServerGroupStrategy;
import com.xylink.manager.service.server.ServerGroupStrategyFactory;
import com.xylink.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.xylink.manager.service.base.SystemModeConfig.isNewCms;

/**
 * <AUTHOR> create on 2024/10/28
 */
@Slf4j
public class CommonMainHandler extends NodeHandler {


    private static final Logger logger = LoggerFactory.getLogger(CommonMainHandler.class);

    @Override
    protected NodeHandler configureConfigMap() {
        logger.info("CommonMainHandler start configureConfigMap");
        return doThat();
    }

    public NodeHandler doThat() {
        if (node == null) return this;

        //配置多活IP
        configureDistributeIP(node);

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();
        List<String> enableLabels = node.getLabelMap().entrySet().stream().filter(Map.Entry::getValue).map(Map.Entry::getKey).collect(Collectors.toList());

        K8sService k8sService = SpringBeanUtil.getBean(K8sService.class);
        boolean newCms = k8sService.isNewCms();
        logger.info("notifyNginxPortToNoah isNewCms:{}", newCms);
        if (newCms) {
            //cms特有逻辑
            doNewCmsInit();
        }

        //特定逻辑
        doUaaHandler(allIpMap, enableLabels);
        doVodHandler(allIpMap, enableLabels);
        doMainPartnerHandler(allIpMap, enableLabels);
        doBaseHandler(allIpMap, enableLabels);
        doCascadeHandler(allIpMap, enableLabels);
        doChargeHandler(allIpMap, enableLabels);
        doMainHandler(allIpMap, enableLabels);
        doStatisHandler(allIpMap, enableLabels);
        doHadoopHandler(allIpMap, enableLabels);
        doDatabaseHandler(allIpMap, enableLabels);
        doCommonHandler(allIpMap, enableLabels);

        getCommonMainServices()
                .stream()
                .map(Labels::labelOf)
                .filter(Labels::isDistributed)
                .filter(label -> StringUtils.isNotBlank(label.loadblanceKey()))
                .forEach(label -> allIpMap.put(label.loadblanceKey(), getDistributeIp(label.label())));


        //内网服务地址
        enableLabels.stream().filter(Constants.interIps::containsKey).forEach(label -> {
            String key = Constants.interIps.get(label);
            if (NetworkConstants.DATABASE_IP.equals(key) && isNewCms()) {
                return;
            }
            String internalIp = node.getInternalIp();
            if (NetworkConstants.KAFKA_INTERNAL_IP.equals(key) && isNewCms()) {
                K8sSvcService k8sSvcService = SpringBeanUtil.getBean(K8sSvcService.class);
                String kafkaSvcIp = k8sSvcService.getServiceIpByDefaultNs(K8sSvcConstants.KAFKA_NAME);
                if (StringUtils.isNotBlank(kafkaSvcIp)) {
                    internalIp = kafkaSvcIp;
                }
            }
            logger.info("{} write internal ip {}", key, internalIp);
            allIpMap.put(key, internalIp);
        });

        //夜莺特殊逻辑，放到后面
        doNightingaleHander(allIpMap, enableLabels);
        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);
        logger.info("CommonMainHandler end endConfigMap");
        return this;
    }

    private List<String> getCommonMainServices() {
        List<String> services = new ArrayList<>();
        final ServerGroupStrategyFactory strategyFactory = SpringBeanUtil.getBean(ServerGroupStrategyFactory.class);
        List<ServerGroupStrategy> strategyList = strategyFactory.create(Constants.NODE_TYPE_COMMON_MAIN);
        for (ServerGroupStrategy strategy : strategyList) {
            services.addAll(strategy.genServices());
        }
        return services;
    }

    private void doNewCmsInit() {
        //1、通知配置中心nginx端口变动
        try {
            notifyNginxPortToNoah();
            logger.info("notify to noah finished");
        } catch (Exception ex) {
            logger.info("notifyNginxPortToNoah error {}", ex.getMessage(), ex);
        }
        //2、添加all-cascade-mgr，cascademgr启动时需要
        ConfigMap cascadeMgrCm = deployService.getConfigMapByName(Constants.CONFIGMAP_CASCADE_MGR, Constants.NAMESPACE_DEFAULT);
        if (cascadeMgrCm == null || CollectionUtils.isEmpty(cascadeMgrCm.getData())) {
            deployService.patchConfigMap(Constants.CONFIGMAP_CASCADE_MGR, Constants.NAMESPACE_DEFAULT, d -> {
                d.put(node.getName() + Constants.CASCADE_MGR_SITE_INFO, JsonMapper.nonDefaultMapper().toJson(Lists.newArrayList()));
            });
            logger.info("init add cascadeMgrDataMap finished");
        }
        //3、添加all-vod-file-manager，vod-file-manager启动时需要
        ConfigMap vodFileManagerCm = deployService.getConfigMapByName(Constants.CONFIGMAP_VOD_FILE_MANAGER, Constants.NAMESPACE_DEFAULT);
        if (vodFileManagerCm == null || CollectionUtils.isEmpty(vodFileManagerCm.getData())) {
            deployService.patchConfigMap(Constants.CONFIGMAP_VOD_FILE_MANAGER, Constants.NAMESPACE_DEFAULT, d -> {
                d.put(node.getName() + Constants.VOD_THIRD_TOKEN_AUTH_URL_KEY, "");
            });
            logger.info("init add vodFileManagerDataMap finished");
        }
        //4、添加mms-dispatcher标签，强制会控多活模式
        deployService.addNodeAppLabel(node.getName(), "mms-dispatcher");
        logger.info("init add mms-dispatcher to {}", node.getName());
        //5、通知配置中心标签变化
        Map<String, Boolean> labelMap = node.getLabelMap();
        try {
            notifyDeployLabelToNoah(labelMap);
            logger.info("notify deploy label to noah finished");
        } catch (Exception ex) {
            logger.info("notify deploy label to noah error {}", ex.getMessage(), ex);
        }

    }

    private void notifyDeployLabelToNoah(Map<String, Boolean> labelMap) {
        NoahApiService noahApiService = SpringBeanUtil.getBean(NoahApiService.class);
        noahApiService.notifyDeployLabelChange();
    }


    private void notifyNginxPortToNoah() {
        String nginxPort = StringUtils.isBlank(node.getNginxPort()) ? "80" : node.getNginxPort();
        String nginxSslPort = StringUtils.isBlank(node.getNginxSslPort()) ? "443" : node.getNginxSslPort();

        String vodNginxPort = StringUtils.isBlank(node.getCommonNodeVodNginxPort()) ? "80" : node.getCommonNodeVodNginxPort();
        String vodNginxSslPort = StringUtils.isBlank(node.getCommonNodeVodNginxSslPort()) ? "443" : node.getCommonNodeVodNginxSslPort();

        Map<String, String> keyValues = new HashMap<>();
        keyValues.put("openresty-main.svc.http1_port", nginxPort);
        keyValues.put("openresty-main.svc.https1_port", nginxSslPort);
        keyValues.put("openresty-main.svc.http2_port", node.getBackendServerNginxPort());
        keyValues.put("openresty-main.svc.https2_port", node.getBackendServerNginxSslPort());
        keyValues.put("openresty-vod.svc.http_port", vodNginxPort);
        keyValues.put("openresty-vod.svc.https_port", vodNginxSslPort);

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("dataId", "var_env.svc.yaml");
        paramMap.put("keyValues", keyValues);
        paramMap.put("publish", "true");

        NoahApiService noahApiService = SpringBeanUtil.getBean(NoahApiService.class);
        noahApiService.notifyNginxPort(paramMap);
    }

    private void doCommonHandler(Map<String, String> allIpMap, List<String> enableLabels) {
        allIpMap.put(NetworkConstants.COMMON_MAIN_DOMAIN_NAME, StringUtils.isBlank(node.getDomain()) ? node.getExternalIp() : node.getDomain());
        allIpMap.put(NetworkConstants.COMMON_MAIN_IP, node.getInternalIp());
        //高可用模式时,common节点保存时不覆盖COMMON_MAIN_INTERNAL_IP
        Map<String, String> allCluster = K8sUtils.getConfigMap(Constants.CONFIGMAP_CLUSTER);
        if (StringUtils.isBlank(allCluster.get(ClusterConfigEnum.INTERNAL_HAPROXY.getHaAddressKey()))) {
            allIpMap.put(NetworkConstants.COMMON_MAIN_INTERNAL_IP, node.getInternalIp());
        }
        allIpMap.put(NetworkConstants.COMMON_MAIN_PUBLIC_IP, node.getExternalIp());
    }

    public void doUaaHandler(Map<String, String> allIpMap, List<String> enableLabels) {
        //内网服务地址
        enableLabels.stream().filter(DefaultDeployStructureEnumInvoke.services(Labels.uaa.label())::contains).filter(Constants.interIps::containsKey).forEach(label -> allIpMap.put(Constants.interIps.get(label), node.getInternalIp()));
        //高可用模式时,uaa节点保存时不覆盖UAA_DATABASE_IP
        Map<String, String> allCluster = K8sUtils.getConfigMap(Constants.CONFIGMAP_CLUSTER);
        if (StringUtils.isBlank(allCluster.get(ClusterConfigEnum.UAA_DATABASE.getHaAddressKey()))) {
            if (enableLabels.contains(Labels.uaa_mysql.label())) {
                allIpMap.put(NetworkConstants.UAA_DATABASE_IP, node.getInternalIp());
            }
            if (PlatformConfig.isAnKe()) {
                allIpMap.put(NetworkConstants.UAA_DATABASE_IP, allIpMap.get(NetworkConstants.DATABASE_IP));
            }
        }
        allIpMap.put(NetworkConstants.UAA_INTERNAL_IP, node.getInternalIp());
    }

    public void doVodHandler(Map<String, String> allIpMap, List<String> enableLabels) {
        allIpMap.put(NetworkConstants.VOD_DOMAIN_NAME, StringUtils.isBlank(node.getDomain()) ? node.getExternalIp() : node.getDomain());
        String defaultVodNginxPort = SystemModeConfig.isNewCms() ? "8001" : "80";
        String defaultVodNginxSslPort = SystemModeConfig.isNewCms() ? "8443" : "443";
        allIpMap.put(NetworkConstants.VOD_NGINX_PORT, StringUtils.isBlank(node.getCommonNodeVodNginxPort()) ? defaultVodNginxPort : node.getCommonNodeVodNginxPort());
        allIpMap.put(NetworkConstants.VOD_NGINX_SSL_PORT, StringUtils.isBlank(node.getCommonNodeVodNginxSslPort()) ? defaultVodNginxSslPort : node.getCommonNodeVodNginxSslPort());
        allIpMap.put(NetworkConstants.VOD_INTERNAL_IP, node.getInternalIp());
        allIpMap.put(NetworkConstants.VOD_PUBLIC_IP, node.getExternalIp());
        // 如果部署有 rmserver
        SigServerHaConfigMap.clearNodesRecordInAllConfigMapWithoutLabel(Labels.rmserver);
        if (enableLabels.contains(Labels.rmserver.label())) {
            SigServerHaConfigMap.updateAllSigServerHa(Labels.rmserver, node.getName(), node.getInternalIp());
        }
        if (enableLabels.contains(Labels.srs.label())) {
            allIpMap.put("SRS_IP", node.getInternalIp());
        }
        if (enableLabels.contains(Labels.vod_fileserver.label())) {
            allIpMap.put("VOD_FILESERVER_IP", node.getInternalIp());
        }
        //vodserver是有状态的，所以需要将所有vodserver服务器节点的hostname和ip映射存储起来
        if (enableLabels.contains(Labels.vod.label())) {
            List<Pod> pods = deployService.listPodsByAppLabel("private-vod");
            if (!CollectionUtils.isEmpty(pods)) {
                String hostNameIps = pods.stream().map(pod -> pod.getNodeName() + ":" + Ipv6Util.handlerIpv6Addr(pod.getIp())).collect(Collectors.joining(","));
                // hostname1:ip1,hostname2:ip2
                allIpMap.put("VODSERVER_HOSTNAME_IP_MAPPING", hostNameIps);
            }
        }
    }

    public void doMainPartnerHandler(Map<String, String> allIpMap, List<String> enableLabels) {
        //内网服务地址
        enableLabels.stream().filter(DefaultDeployStructureEnumInvoke.services(Labels.main_partner.label())::contains).filter(Constants.interIps::containsKey).forEach(label -> allIpMap.put(Constants.interIps.get(label), node.getInternalIp()));
        //外网服务地址
        enableLabels.stream().filter(DefaultDeployStructureEnumInvoke.services(Labels.main_partner.label())::contains).filter(Constants.exterIps::containsKey).forEach(label -> allIpMap.put(Constants.exterIps.get(label), node.getExternalIp()));
        if (enableLabels.contains(Labels.push.label())) {
            allIpMap.put("PUSH_INTERNAL_IP", node.getInternalIp());
        }
        if (enableLabels.contains(Labels.mms.label())) {
            allIpMap.put("MMS_DISPATCHER_IP", node.getInternalIp());
            allIpMap.put("DEVICE_IP", node.getInternalIp());
            allIpMap.put("DSU_IP", node.getInternalIp());
            allIpMap.put("MMS_PERMISSION_IP", node.getInternalIp());
            allIpMap.put("MMSPROXY_IP", node.getInternalIp());
        }
        updateAllMms();
        ClusterUtil.updateMcIpConfig(allIpMap, enableLabels.contains(Labels.mc.label()), Ipv6Util.handlerIpv6Addr(node.getInternalIp()), node.getName());
        updateSigServer(enableLabels);
        updatePresenceServer(enableLabels);
        // 241220-分区云5.2启用点对点号码池
        updateP2PPoolNumber(enableLabels);
    }

    private void updateAllMms() {
        ConfigMap configMap = deployService.getConfigMapByName(Constants.CONFIGMAP_ALL_MMS, Constants.NAMESPACE_DEFAULT);
        Map<String, String> allMms = configMap == null ? DefaultConfigmapDataEnum.initDefault(Labels.mms.label()) : configMap.getData();
        allMms.put(node.getName() + "-MMS_DEVICESTATE_IP", node.getInternalIp());
        allMms.put(node.getName() + "-MMS_DISPATCHER_IP", node.getInternalIp());
        allMms.put(node.getName() + "-MMS_EDGE_IP", node.getInternalIp());
        allMms.put(node.getName() + "-MMS_LOGIC_IP", node.getInternalIp());
        deployService.patchConfigMap(Constants.CONFIGMAP_ALL_MMS, Constants.NAMESPACE_DEFAULT, d -> {
            d.putAll(allMms);
        });
    }

    private void updateNightingale(Map<String, String> allIpMap, List<String> enableLabels) {
        if (enableLabels.contains(Labels.nightingale.label())) {
            deployedNightingaleServer(allIpMap);
            if (enableLabels.contains(Labels.nightingale_kafka.label())) {
                allIpMap.put(NetworkConstants.N9E_KAFKA_ADDRESS, node.getInternalIp() + ":9093");
                if (isNewCms()) {
                    allIpMap.put(NetworkConstants.N9E_KAFKA_ADDRESS, NetworkConstants.SVC_MID_DEFAULT_IP + ":9093");
                }
            }
        } else {
            // 判断是否之前在改节点部署
            cancelDeployNightingaleServer(allIpMap);
        }
        // 判断是否部署了 nightingale_mid
        List<Node> nodeList = deployService.listNodesByAppLabel("nightingale-mid");
        if (CollectionUtils.isEmpty(nodeList)) {
            allIpMap.putAll(nightingaleCommonMidSetting(allIpMap));
        }

    }

    @Override
    protected NodeHandler initDefaultNodeConfig() {
        logger.info("init commonMainHandler start");
        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setExternalIp(deployMessage.getIp());
        node.setReportInternalIp(deployMessage.getIp());
        node.setType(deployMessage.getType());
        node.setMainProxyExternalIp(deployMessage.getMainProxyExternalIp());
        node.setMainProxyDomain(deployMessage.getMainProxyDomain());
        Map<String, Boolean> labelMap = new HashMap<>();
        getCommonMainServices().forEach(label -> labelMap.put(label, true));

        if (isNewCms()) {
            //适配cms默认不启动的
            labelMap.put(Labels.roster.label(), false);
            labelMap.put(Labels.avstatusserver.label(), false);
            labelMap.put(Labels.cascademgr.label(), false);
            labelMap.put(Labels.cascadegw.label(), false);
            labelMap.put(Labels.mc_proxy.label(), false);
            labelMap.put(Labels.thirdadapter.label(), false);
            labelMap.put(Labels.tsa_mp.label(), false);//cms无
            labelMap.put(Labels.liveness_probe.label(), false);//cms无
            labelMap.put(Labels.dmcu.label(), false);
            labelMap.put(Labels.sms.label(), false);
            labelMap.put(Labels.push.label(), false);
            labelMap.put(Labels.nightingale_zookeeper.label(), false);
            labelMap.put(Labels.nightingale_kafka.label(), false);
            labelMap.put(Labels.nightingale_mid.label(), false);
            labelMap.put(Labels.statis_live.label(), false);
            labelMap.put(Labels.vodnetwork_proxy.label(), false);
            labelMap.put(Labels.vodnetwork_vodedit.label(), false);
            labelMap.put(Labels.vodnetwork_vod.label(), false);
            labelMap.put(Labels.vodfilemanager.label(), false);
            labelMap.put(Labels.live_audience.label(), false);
            labelMap.put(Labels.srs.label(), false);
            labelMap.put(Labels.record.label(), false);
            labelMap.put(Labels.openresty_vod.label(), false);
            labelMap.put(Labels.canal.label(), false);
            labelMap.put(Labels.gatekeeper.label(), false);
            labelMap.put(Labels.message_push.label(), false);
            labelMap.put(Labels.main_proxy.label(), false);

            labelMap.put(Labels.charge_redis.label(), false);
            labelMap.put(Labels.hadoop_node.label(), false);
            labelMap.put(Labels.statis_dameng.label(), false);
            labelMap.put(Labels.statis_education.label(), false);
            labelMap.put(Labels.statis_shentong.label(), false);
            labelMap.put(Labels.txlive.label(), false);//cms无
            node.setLabelMap(labelMap);
            super.node = node;
            return this;
        } else {
            //适配5.2通用默认不启动的
            //1、mainPartner
            labelMap.put(Labels.meetingmonitor.label(), false);
            labelMap.put(Labels.tsa.label(), false);
            labelMap.put(Labels.sdkcallback.label(), false);
            labelMap.put(Labels.mc.label(), false);
            labelMap.put(Labels.dating.label(), false);
            labelMap.put(Labels.vcs.label(), false);
            labelMap.put(Labels.presence.label(), false);
            labelMap.put(Labels.ocean.label(), false);
            labelMap.put(Labels.sensitiveword.label(), false);
            labelMap.put(Labels.message_push.label(), false);
            labelMap.put(Labels.liveness_probe.label(), false);
            labelMap.put(Labels.tsa_mp.label(), false);
            labelMap.put(Labels.allocator_server.label(), false);
            labelMap.put(Labels.txlive.label(), false);
            labelMap.put(Labels.push.label(), false);
            //2、vod
            labelMap.put(Labels.srs.label(), false);
            labelMap.put(Labels.avcloudapi.label(), false);
            labelMap.put(Labels.mcaccess.label(), false);
            labelMap.put(Labels.vodclustermgr.label(), false);
            //3、vodnetwork
            DefaultDeployStructureEnumInvoke.services(Labels.vodnetwork.label()).forEach(label -> labelMap.put(label, false));
            String installType = deployService.getConfigMapManagerData().getData().getOrDefault("install_type", "normal");
            if ("min".equalsIgnoreCase(installType)) {
                labelMap.put(Labels.vote_statistics.label(), false);
                labelMap.put(Labels.vote.label(), false);
                labelMap.put(Labels.externalweb.label(), false);
                labelMap.put(Labels.inspection.label(), false);
                labelMap.put(Labels.tsa.label(), false);
                labelMap.put(Labels.im.label(), false);
                labelMap.put(Labels.nettool.label(), false);
                labelMap.put(Labels.sharing.label(), false);
                deployService.scaleDeployment("private-mkdoc", Constants.NAMESPACE_DEFAULT, 0);
            }
            //3、BaseHandler
            if ("min".equalsIgnoreCase(installType)) {
                if (Labels.meeting_quality.label().equalsIgnoreCase(node.getType())) {
                    labelMap.put(Labels.statis_quality.label(), false);
                }
            }
            // dmcu节点默认不部署 ipip-proxy. 如果改需求比较多，需要在 DeployStructureEnum定义改结构：标注是否默认部署
            //labelMap.put(Labels.ipip_proxy.label(), false);
            //4、ChargeHandler ：默认不部署charge-redis 避免影响当前业务
            DefaultDeployStructureEnumInvoke.services(Labels.charge.label()).forEach(label -> labelMap.put(label, false));
            labelMap.put(Labels.charge.label(), true);
            //5、MainHandler
            node.setNginxPort("80");
            node.setNginxSslPort("443");
            node.setReportInternalIp(deployMessage.getIp());
            labelMap.put(Labels.sdkcallback.label(), false);
            labelMap.put(Labels.sensitiveword.label(), false);
            labelMap.put(Labels.message_push.label(), false);
            labelMap.put(Labels.liveness_probe.label(), false);
            labelMap.put(Labels.tsa_mp.label(), false);
            labelMap.put(Labels.allocator_server.label(), false);
            labelMap.put(Labels.txlive.label(), false);
            if ("min".equalsIgnoreCase(installType)) {
                labelMap.put(Labels.vote_statistics.label(), false);
                labelMap.put(Labels.vote.label(), false);
                labelMap.put(Labels.externalweb.label(), false);
                labelMap.put(Labels.inspection.label(), false);
                labelMap.put(Labels.tsa.label(), false);
                labelMap.put(Labels.im.label(), false);
                labelMap.put(Labels.nettool.label(), false);
                labelMap.put(Labels.sharing.label(), false);
                deployService.scaleDeployment("private-mkdoc", Constants.NAMESPACE_DEFAULT, 0);
            }
            //6、StatisHandler
            DefaultDeployStructureEnumInvoke.services(Labels.statis.label()).forEach(label -> labelMap.put(label, true));
            labelMap.put(Labels.statis_education.label(), false);
            labelMap.put(Labels.clickhouse.label(), false);
            if ("min".equalsIgnoreCase(installType)) {
                labelMap.put(Labels.statis_quality.label(), false);
            }
            //7、DatabaseHandler
            DefaultDeployStructureEnumInvoke.services(Labels.database.label()).forEach(label -> labelMap.put(label, true));
            labelMap.put(Labels.st.label(), false);
            labelMap.put(Labels.dm.label(), false);
            labelMap.put(Labels.kingbase.label(), false);
            //labelMap.put(Labels.oceanbase.label(), false);
            //labelMap.put(Labels.mysql.label(), false);
            labelMap.put(Labels.canal.label(), false);
            node.setLabelMap(labelMap);
            super.node = node;
            return this;
        }
    }

    private void doDatabaseHandler(Map<String, String> allIpMap, List<String> enableLabels) {
        if (enableLabels.contains(Labels.kafka.label())) {
            allIpMap.put(NetworkConstants.KAFKA_PUBLIC_IP, node.getExternalIp());
        }

        if (StringUtils.isNotBlank(node.getNodeDbPort()) && !isNewCms()) {
            allIpMap.put(NetworkConstants.DATABASE_PORT, node.getNodeDbPort());
        }

        // 使用容器化oceanbase时，hadoop共用主业务的oceanbase库
        if (enableLabels.contains(Labels.oceanbase.label())) {
            if (!isNewCms()) {
                //newCms不写这个值
                allIpMap.put("STATIS_ETL_DATABASE_IP", node.getInternalIp());
                allIpMap.put("STATIS_ETL_DATABASE_PORT", "2883");
                String switchStatus = allIpMap.get(NetworkConstants.DB_PASSWORD_ENCRYPT_SWITCH);
                JasyptService jasyptService = SpringBeanUtil.getBean(JasyptService.class);
                String value = "Da?548!YZ";
                if (StringUtils.isNotBlank(switchStatus) && Boolean.parseBoolean(switchStatus)) {
                    value = jasyptService.encrypt(value);
                }
                allIpMap.put("STATIS_ETL_DB_PASSWORD", value);
            }
        }

        if (!enableLabels.contains(Labels.mysql.label()) && !enableLabels.contains(Labels.dm.label())
                && !enableLabels.contains(Labels.st.label()) && !enableLabels.contains(Labels.kingbase.label())
                && !enableLabels.contains(Labels.oceanbase.label())) {
            allIpMap.put(NetworkConstants.MASTER_DATABASE_IP, node.getInternalIp());
        }
    }

    private void doHadoopHandler(Map<String, String> allIpMap, List<String> enableLabels) {
        allIpMap.put("AZKABAN_IP", node.getInternalIp());
        allIpMap.put("HADOOP_IP", node.getInternalIp());
        // 仅国产数据库（达梦/神通/金仓），需要勾选；当主系统数据库是mysql、oceanbase时，无需勾选
        if (node.getLabelMap().containsKey(Labels.hadoop_db.label()) && node.getLabelMap().get(Labels.hadoop_db.label())) {
            if (!isNewCms()) {
                //newCms不写这个值
                allIpMap.put("STATIS_ETL_DATABASE_IP", node.getInternalIp());
                allIpMap.put("STATIS_ETL_DATABASE_PORT", "3306");
                String switchStatus = allIpMap.get(NetworkConstants.DB_PASSWORD_ENCRYPT_SWITCH);
                JasyptService jasyptService = SpringBeanUtil.getBean(JasyptService.class);
                String value = "Da?548!YZ";
                if (StringUtils.isNotBlank(switchStatus) && Boolean.parseBoolean(switchStatus)) {
                    value = jasyptService.encrypt(value);
                }
                allIpMap.put("STATIS_ETL_DB_PASSWORD", value);
            }
        }
    }

    private void doStatisHandler(Map<String, String> allIpMap, List<String> enableLabels) {
        allIpMap.put(NetworkConstants.DCS_SERVER_INTERNAL_IP, node.getInternalIp());
        allIpMap.put(NetworkConstants.DES_SERVER_INTERNAL_IP, node.getInternalIp());
        allIpMap.put(NetworkConstants.DATAFACT_IP, node.getInternalIp());
        allIpMap.put(NetworkConstants.STATIS_INTERNAL_IP, node.getInternalIp());
        allIpMap.put(NetworkConstants.DATA_TRANSFER_SERVER_IP, node.getInternalIp());
        if (PlatformConfig.isAnKe()) {
            if (enableLabels.contains(Labels.statis_dameng.label()) || enableLabels.contains(Labels.statis_shentong.label())) {
                if (!isNewCms()) {
                    //newCms不写这个值
                    allIpMap.put(NetworkConstants.STATIS_DATABASE_IP, node.getInternalIp());
                }
            }
        } else {
            if (!isNewCms()) {
                //newCms不写这个值
                allIpMap.put(NetworkConstants.STATIS_DATABASE_IP, node.getInternalIp());
            }
        }

        if (enableLabels.contains(Labels.hbase.label())) {
            allIpMap.put(NetworkConstants.HBASE_ZOOKEEPER_IP, node.getInternalIp());
        }
    }

    private void doNightingaleHander(Map<String, String> allIpMap, List<String> enableLabels) {
        updateNightingale(allIpMap, enableLabels); // 夜莺监控，main-partner逻辑
        if (enableLabels.contains(Labels.nightingale.label())) {
            deployedNightingaleServer(allIpMap);
            if (enableLabels.contains(Labels.nightingale_kafka.label())) {
                allIpMap.put(NetworkConstants.N9E_KAFKA_ADDRESS, node.getInternalIp() + ":9093");
            }
        } else {
            // 判断是否之前在改节点部署
            cancelDeployNightingaleServer(allIpMap);
        }
        if (enableLabels.contains(Labels.nightingale_kafka.label())) {
            allIpMap.put(NetworkConstants.KAFKA_NIGHTINGALE_INTERNAL_IP, node.getInternalIp());
        } else {
            allIpMap.put(NetworkConstants.KAFKA_NIGHTINGALE_INTERNAL_IP, "127.0.0.1");
        }

        if (enableLabels.contains(Labels.nightingale_zookeeper.label())) {
            allIpMap.put(NetworkConstants.NIGHTINGALE_ZOOKEEPER_IP, node.getInternalIp());
        } else {
            allIpMap.put(NetworkConstants.NIGHTINGALE_ZOOKEEPER_IP, "127.0.0.1");
        }

        if (enableLabels.contains(Labels.nightingale_mid.label()) && !isNewCms()) {
            allIpMap.put(NetworkConstants.N9E_REDIS_ADDRESS, address(this.node.getInternalIp(), NetworkConstants.N9E_REDIS_DEFULT_PORT));
            allIpMap.put(NetworkConstants.N9E_REDIS_USER, allIpMap.get(NetworkConstants.N9E_REDIS_DEFULT_USER));
            allIpMap.put(NetworkConstants.N9E_REDIS_PWD, allIpMap.get(NetworkConstants.N9E_REDIS_DEFULT_PWD));
            allIpMap.put(NetworkConstants.N9E_REDIS_MODE, allIpMap.get(NetworkConstants.N9E_REDIS_DEFULT_MODE));

            allIpMap.put(NetworkConstants.N9E_MYSQL_ADDRESS, address(this.node.getInternalIp(), NetworkConstants.N9E_MYSQL_DEFULT_PORT));
            allIpMap.put(NetworkConstants.N9E_MYSQL_USER, allIpMap.get(NetworkConstants.N9E_MYSQL_DEFULT_USER));
            allIpMap.put(NetworkConstants.N9E_MYSQL_PASSWORD, allIpMap.get(NetworkConstants.N9E_MYSQL_DEFULT_PWD));
            allIpMap.put(NetworkConstants.NIGHTINGALE_DATABASE_IP, this.node.getInternalIp());
            allIpMap.put(NetworkConstants.NIGHTINGALE_DATABASE_PORT, NetworkConstants.N9E_MYSQL_DEFULT_PORT);
        } else {
            allIpMap.putAll(nightingaleCommonMidSetting(allIpMap));
        }
    }

    private void doMainHandler(Map<String, String> allIpMap, List<String> enableLabels) {
        //node 配置
        allIpMap.put(NetworkConstants.MAIN_DOMAIN_NAME, StringUtils.isBlank(node.getDomain()) ? node.getExternalIp() : node.getDomain());
        String defaultNginxPort = SystemModeConfig.isNewCms() ? "7001" : "80";
        String defaultNginxSslPort = SystemModeConfig.isNewCms() ? "7443" : "443";
        allIpMap.put(NetworkConstants.MAIN_NGINX_PORT, StringUtils.isBlank(node.getNginxPort()) ? defaultNginxPort : node.getNginxPort());
        allIpMap.put(NetworkConstants.MAIN_NGINX_SSL_PORT, StringUtils.isBlank(node.getNginxSslPort()) ? defaultNginxSslPort : node.getNginxSslPort());
        allIpMap.put(NetworkConstants.MAIN_IP, node.getInternalIp());
        //高可用模式时,main节点保存时不覆盖MAIN_INTERNAL_IP
        Map<String, String> allCluster = K8sUtils.getConfigMap(Constants.CONFIGMAP_CLUSTER);
        if (StringUtils.isBlank(allCluster.get(ClusterConfigEnum.INTERNAL_HAPROXY.getHaAddressKey()))) {
            allIpMap.put(NetworkConstants.MAIN_INTERNAL_IP, node.getInternalIp());
        }
        allIpMap.put(NetworkConstants.MAIN_PUBLIC_IP, node.getExternalIp());
        //mms
        if (enableLabels.contains(Labels.mms.label())) {
            allIpMap.put("MMS_DISPATCHER_IP", node.getInternalIp());
            allIpMap.put("DEVICE_IP", node.getInternalIp());
            allIpMap.put("DSU_IP", node.getInternalIp());
            allIpMap.put("MMS_PERMISSION_IP", node.getInternalIp());
            allIpMap.put("MMSPROXY_IP", node.getInternalIp());
        }
        //nginx
        allIpMap.put(NetworkConstants.MAIN_BACKEND_SERVER_NGINX_PORT, node.getBackendServerNginxPort());
        allIpMap.put(NetworkConstants.MAIN_BACKEND_SERVER_NGINX_SSL_PORT, node.getBackendServerNginxSslPort());
        //all-mms
        ConfigMap configMap = deployService.getConfigMapByName(Constants.CONFIGMAP_ALL_MMS, Constants.NAMESPACE_DEFAULT);
        Map<String, String> allMms = configMap == null ? DefaultConfigmapDataEnum.initDefault(Labels.mms.label()) : configMap.getData();
        allMms.put(node.getName() + "-MMS_DEVICESTATE_IP", node.getInternalIp());
        allMms.put(node.getName() + "-MMS_DISPATCHER_IP", node.getInternalIp());
        allMms.put(node.getName() + "-MMS_EDGE_IP", node.getInternalIp());
        allMms.put(node.getName() + "-MMS_LOGIC_IP", node.getInternalIp());
        //nginx
        if (StringUtils.isNoneBlank(node.getBackendServerNginxPort(), node.getBackendServerNginxSslPort())) {
            allIpMap.put(ProxyConstants.ALLOW_SINGLE_ACCESS_CONSOLE, "true");
        } else {
            allIpMap.put(ProxyConstants.ALLOW_SINGLE_ACCESS_CONSOLE, "false");
        }
        //mc
        ClusterUtil.updateMcIpConfig(allIpMap, enableLabels.contains(Labels.mc.label()), Ipv6Util.handlerIpv6Addr(node.getInternalIp()), node.getName());
        //sig_server
        updateSigServer(enableLabels);

        deployService.patchConfigMap(Constants.CONFIGMAP_ALL_MMS, Constants.NAMESPACE_DEFAULT, d -> {
            d.putAll(allMms);
        });
    }

    private void doChargeHandler(Map<String, String> allIpMap, List<String> enableLabels) {
        if (enableLabels.contains(Labels.charge_redis.label())) {
            allIpMap.put(NetworkConstants.CHARGE_REDIS_IP, node.getInternalIp());
        } else {
            String ip = getDistributeIp(Labels.charge_redis.label());
            allIpMap.put(NetworkConstants.CHARGE_REDIS_IP, StringUtils.isNotBlank(ip) ? ip : "127.0.0.1");
        }
    }

    private void doBaseHandler(Map<String, String> allIpMap, List<String> enableLabels) {
        //内网ip //todo
        String networkName = Constants.interIps.get(node.getType());
        if (StringUtils.isNotEmpty(networkName)) allIpMap.put(networkName, node.getInternalIp());

        //hbase
        if (enableLabels.contains(Labels.hbase.label())) {
            allIpMap.put(NetworkConstants.HBASE_ZOOKEEPER_IP, node.getInternalIp());
        }

        //ipip_proxy
        if (enableLabels.contains(Labels.ipip_proxy.label())) {
            allIpMap.put(NetworkConstants.IPIP_PROXY_IP, node.getInternalIp());
        }

        //txlive
        if (enableLabels.contains(Labels.txlive.label())) {
            allIpMap.put(NetworkConstants.TXLIVE_SERVER_IP, node.getInternalIp());
        }

        //外网ip //todo
        String networkPubName = Constants.exterIps.get(node.getType());
        if (StringUtils.isNotEmpty(networkPubName))
            allIpMap.put(networkPubName, StringUtils.isBlank(node.getExternalIp()) ? node.getInternalIp() : node.getExternalIp());

        //cloud_proxy
        allIpMap.put("CLOUD_PROXY_OUT_DOMAIN", StringUtils.isBlank(node.getDomain()) ? node.getExternalIp() : node.getDomain());
        allIpMap.put("CLOUD_PROXY_OUT_IP", node.getExternalIp());
        allIpMap.put("CLOUD_PROXY_INTERNAL_DOMAIN", node.getInternalIp());

        //other
        updatePresenceServer(enableLabels);
    }

    private void doCascadeHandler(Map<String, String> allIpMap, List<String> enableLabels) {
        if (enableLabels.contains(Labels.cascadegw.label())) {
            allIpMap.put(NetworkConstants.CASCADEGW_IP, node.getInternalIp());
        }
        if (enableLabels.contains(Labels.cascademgr.label())) {
            allIpMap.put(NetworkConstants.CASCADEMGR_IP, node.getInternalIp());
        }
        if (enableLabels.contains(Labels.avstatusserver.label())) {
            allIpMap.put(NetworkConstants.AVSTATUS_IP, node.getInternalIp());
        }
        if (enableLabels.contains(Labels.roster.label())) {
            allIpMap.put(NetworkConstants.ROSTER_IP1, node.getInternalIp());
        }
    }

    @Override
    protected NodeHandler afterConfigure() {
        logger.info("start CommonMainHandler afterConfigure,advanceConfig :[{}]", advanceConfig);
        //1、OpenrestyMainHandler
        try {
            if (advanceConfig != null) {
                //设置crossdomain
                OpenrestyMainCM openresty = (OpenrestyMainCM) advanceConfig;
                String allowAccessFrom = openresty.getAllowAccessFrom();
                StringBuffer buffer = new StringBuffer();
                buffer.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n");
                buffer.append("<cross-domain-policy>\n");

                if (StringUtils.isBlank(allowAccessFrom) || "*".equals(allowAccessFrom)) {
                    buffer.append("  <allow-access-from domain=\"*\" secure=\"false\"/>\n");
                } else {
                    Arrays.stream(allowAccessFrom.split(",")).forEach(allow -> buffer.append("  <allow-access-from domain=\"" + allow + "\" secure=\"false\"/>\n"));
                }
                buffer.append("</cross-domain-policy>");
                DaemonSet daemonSet = deployService.getDaemonSetByName("private-openresty-main", Constants.NAMESPACE_DEFAULT);
                if (daemonSet != null) {
                    daemonSet.getVolumes().stream()
                            .filter(it -> StringUtils.isNotBlank(it.getConfigMapName()))
                            .filter(it -> StringUtils.equals(it.getName(), "private-openresty-main-crossdomain"))
                            .findFirst()
                            .ifPresent(v -> {
                                assert v.getConfigMapName() != null;
                                logger.info("update crossdomain.xml to : \n{}", buffer);
                                deployService.patchConfigMap(v.getConfigMapName(), Constants.NAMESPACE_DEFAULT, d -> {
                                    d.put("crossdomain.xml", buffer.toString());
                                });
                            });
                }
            }
        } catch (Exception e) {
            logger.info("common OpenrestyMainHandler error:{}", e.getMessage(), e);
        }

        //2、HadoopHandler
        AuditLogService auditLogService = SpringBeanUtil.getBean(AuditLogService.class);
        BuffetRemoteClient buffetRemoteClient = SpringBeanUtil.getBean(BuffetRemoteClient.class);
        try {
            logger.info("start saveBuffetConfigDict {},{}", ConfigService.DATACENTER_SETTING_CONFIG.getConfigName(), ConfigService.DATACENTER_SETTING_CONFIG.getName());
            boolean exist = jdbcUtils.saveBuffetConfigDict(ConfigService.DATACENTER_SETTING_CONFIG.getConfigName(), ConfigService.DATACENTER_SETTING_CONFIG.getName());
            buffetRemoteClient.saveEnterpriseConfig(ConfigService.DATACENTER_SETTING_CONFIG.getConfigName(), ConfigService.DATACENTER_SETTING_CONFIG.getConfigValue());
            auditLogService.saveEnterpriseAuditLog(ConfigService.DATACENTER_SETTING_CONFIG.getName(), ConfigService.DATACENTER_SETTING_CONFIG.getConfigName(), ConfigService.DATACENTER_SETTING_CONFIG.getConfigValue(), ConfigService.DATACENTER_SETTING_CONFIG.getConfigValue(), exist ? "添加" : "更新", SecurityContextUtil.currentUser());
        } catch (Exception e) {
            logger.error("common HadoopHandler Save config:{} error.", ConfigService.DATACENTER_SETTING_CONFIG, e);
        }
        logger.info("CommonMainHandler afterConfigure end");
        return this;
    }

}
