package com.xylink.manager.handler.node;

import com.xylink.config.NetworkConstants;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.em.Labels;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * transcript node 配置处理实现
 */
public class TranscriptHandler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(TranscriptHandler.class);

    @Override
    public NodeHandler configureConfigMap() {
        if (node == null) {
            return this;
        }
        configureDistributeIP(node);
        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();
        allIpMap.put(NetworkConstants.TRANSCRIPTION_INTERNAL_IP, node.getInternalIp());
        allIpMap.put(NetworkConstants.TRANSCRIPTION_PUBLIC_IP, node.getExternalIp());
        //多节点服务
        DefaultDeployStructureEnumInvoke.services(Labels.transcript.label())
                .stream()
                .map(Labels::labelOf)
                .filter(Labels::isDistributed)
                .filter(label -> StringUtils.isNotBlank(label.loadblanceKey()))
                .forEach(label -> allIpMap.put(label.loadblanceKey(), getDistributeIp(label.label())));

        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);
        return this;
    }


    @Override
    protected NodeHandler initDefaultNodeConfig() {
        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setExternalIp(deployMessage.getIp());
        node.setReportInternalIp(deployMessage.getIp());
        node.setType(Labels.transcript.label());

        Map<String, Boolean> labelMap = new HashMap<>();
        DefaultDeployStructureEnumInvoke.services(Labels.transcript.label()).forEach(label -> labelMap.put(label, true));
        node.setLabelMap(labelMap);
        super.node = node;
        return this;
    }
}
