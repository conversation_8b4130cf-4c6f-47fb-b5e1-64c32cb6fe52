package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.manager.model.em.Labels;
import com.xylink.util.K8sUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023/9/11 14:38
 */
public class ChargeBaseHandler extends NodeHandler {

    @Override
    protected NodeHandler configureConfigMap() {
        configureDistributeIP(node);
        handleDistributeIP(node, node.getType());

        Map<String, String> allIpMap = new HashMap<>();

        DefaultDeployStructureEnumInvoke.services(node.getType())
                .stream()
                .map(Labels::labelOf)
                .filter(Labels::isDistributed)
                .filter(label -> StringUtils.isNotBlank(label.loadblanceKey()))
                .forEach(label -> allIpMap.put(label.loadblanceKey(), getDistributeIp(label.label())));

        K8sUtils.patchConfigMap(Constants.CONFIGMAP_ALLIP, allIpMap);
        List<String> enableLabels = node.getLabelMap().entrySet().stream().filter(Map.Entry::getValue).map(Map.Entry::getKey).collect(Collectors.toList());
        updatePresenceServer(enableLabels);
        // 241220-分区云5.2启用点对点号码池
        updateP2PPoolNumber(enableLabels);
        return this;
    }
}
