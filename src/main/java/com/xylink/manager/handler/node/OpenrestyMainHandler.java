package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.manager.model.cm.OpenrestyMainCM;
import com.xylink.manager.model.deploy.DaemonSet;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;

public class OpenrestyMainHandler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(OpenrestyMainHandler.class);


    @Override
    protected NodeHandler configureConfigMap() {
        return this;
    }

    @Override
    protected NodeHandler afterConfigure() {
        if (advanceConfig == null) return this;

        //设置crossdomain
        OpenrestyMainCM openresty = (OpenrestyMainCM) advanceConfig;
        String allowAccessFrom = openresty.getAllowAccessFrom();
        StringBuffer buffer = new StringBuffer();
        buffer.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n");
        buffer.append("<cross-domain-policy>\n");

        if (StringUtils.isBlank(allowAccessFrom) || "*".equals(allowAccessFrom)) {
            buffer.append("  <allow-access-from domain=\"*\" secure=\"false\"/>\n");
        } else {
            Arrays.stream(allowAccessFrom.split(",")).forEach(allow -> buffer.append("  <allow-access-from domain=\"" + allow + "\" secure=\"false\"/>\n"));
        }
        buffer.append("</cross-domain-policy>");

        DaemonSet daemonSet = deployService.getDaemonSetByName("private-openresty-main", Constants.NAMESPACE_DEFAULT);
        if (daemonSet != null) {
            daemonSet.getVolumes().stream()
                    .filter(it -> StringUtils.isNotBlank(it.getConfigMapName()))
                    .filter(it -> StringUtils.equals(it.getName(), "private-openresty-main-crossdomain"))
                    .findFirst()
                    .ifPresent(v -> {
                        assert v.getConfigMapName() != null;
                        logger.info("update crossdomain.xml to : \n{}", buffer);
                        deployService.patchConfigMap(v.getConfigMapName(), Constants.NAMESPACE_DEFAULT, d -> {
                            d.put("crossdomain.xml", buffer.toString());
                        });
                    });
        }
        return this;
    }
}
