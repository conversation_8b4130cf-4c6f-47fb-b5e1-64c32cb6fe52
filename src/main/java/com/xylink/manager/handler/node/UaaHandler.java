package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.base.PlatformConfig;
import com.xylink.manager.service.clustersetting.domain.ClusterConfigEnum;
import com.xylink.util.K8sUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * webrtc node 配置处理实现
 */
public class UaaHandler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(UaaHandler.class);


    @Override
    public NodeHandler configureConfigMap() {
        if(node==null) return this;
        configureDistributeIP(node);

        handleDistributeIP(node, Labels.uaa.label());

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();


        List<String> enableLabels = node.getLabelMap().entrySet().stream().filter(x->x.getValue()).map(x->x.getKey()).collect(Collectors.toList());
        //内网服务地址
        enableLabels.stream().filter(Constants.interIps::containsKey).forEach(label -> allIpMap.put(Constants.interIps.get(label), node.getInternalIp()));

        //高可用模式时,uaa节点保存时不覆盖UAA_DATABASE_IP
        Map<String, String> allCluster = K8sUtils.getConfigMap(Constants.CONFIGMAP_CLUSTER);
        if (StringUtils.isBlank(allCluster.get(ClusterConfigEnum.UAA_DATABASE.getHaAddressKey()))) {
            if(enableLabels.contains(Labels.uaa_mysql.label())) {
                allIpMap.put(NetworkConstants.UAA_DATABASE_IP,node.getInternalIp());
            }
            if(enableLabels.contains(Labels.uaa_mysql.label())) {
                allIpMap.put(NetworkConstants.UAA_DATABASE_IP,node.getInternalIp());
            }

            if(PlatformConfig.isAnKe()) {
                allIpMap.put(NetworkConstants.UAA_DATABASE_IP,allIpMap.get(NetworkConstants.DATABASE_IP));
            }
        }

        //多节点服务
        DefaultDeployStructureEnumInvoke.services(Labels.uaa.label())
                .stream()
                .map(Labels::labelOf)
                .filter(Labels::isDistributed)
                .filter(label -> StringUtils.isNotBlank(label.loadblanceKey()))
                .forEach(label -> allIpMap.put(label.loadblanceKey(), getDistributeIp(label.label())));

        allIpMap.put(NetworkConstants.UAA_INTERNAL_IP, node.getInternalIp());

        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);

        return this;

    }


    @Override
    protected NodeHandler initDefaultNodeConfig() {

        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setReportInternalIp(deployMessage.getIp());
        node.setType(Labels.uaa.label());

        Map<String, Boolean > labelMap = new HashMap<>();
        DefaultDeployStructureEnumInvoke.services(Labels.uaa.label()).forEach(label -> labelMap.put(label, true));

        labelMap.put(Labels.uaa_mysql.label(), Boolean.FALSE);
        labelMap.put(Labels.uaa_mysqlslave.label(), Boolean.FALSE);
        node.setLabelMap(labelMap);
        super.node = node;
        return this;
    }

    @Override
    protected NodeHandler afterConfigure() {
        //20231117网关路由优化
        //configureUaaRoute(configureUaaGatewayRoute());
        return this;
    }
}
