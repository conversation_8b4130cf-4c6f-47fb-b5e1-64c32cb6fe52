package com.xylink.manager.handler.node;

import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.em.Labels;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * vod node 配置处理实现
 */
public class VodnetworkHandler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(VodnetworkHandler.class);


    @Override
    public NodeHandler configureConfigMap() {
        if(node==null) return this;

        handleDistributeIP(node, Labels.vodnetwork.label());

        return this;
    }

    @Override
    protected NodeHandler initDefaultNodeConfig() {

        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setExternalIp(deployMessage.getIp());
        node.setDomain(deployMessage.getIp());
        node.setReportInternalIp(deployMessage.getIp());
        node.setType(Labels.vodnetwork.label());

        Map<String, Boolean > labelMap = new HashMap<>();
        DefaultDeployStructureEnumInvoke.services(node.getType()).forEach(label -> labelMap.put(label, false));
        node.setLabelMap(labelMap);
        super.node = node;
        return this;
    }
}
