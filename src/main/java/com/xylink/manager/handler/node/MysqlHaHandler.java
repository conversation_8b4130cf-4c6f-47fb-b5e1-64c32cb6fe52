package com.xylink.manager.handler.node;

/**
 * <AUTHOR>
 * @Date: 2023/09/07/14:54
 * @Description:
 */

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.clustersetting.domain.ClusterConfigEnum;
import com.xylink.util.K8sUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * mysql_ha node 配置处理实现
 */
public class MysqlHaHandler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(MysqlHaHandler.class);


    @Override
    public NodeHandler configureConfigMap() {
        if (node == null) return this;

        //如果支持多活, 在all-* configmap 配置该节点内外网IP信息
        handleDistributeIP(node, node.getType().replaceAll("_", "-"));

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();

        List<String> enableLabels = node.getLabelMap()
                .entrySet()
                .stream()
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        //高可用模式时,mysql节点保存时不覆盖DATABASE_IP
        Map<String, String> allCluster = K8sUtils.getConfigMap(Constants.CONFIGMAP_CLUSTER);
        if (StringUtils.isNotBlank(allCluster.get(ClusterConfigEnum.MAIN_DATABASE.getHaAddressKey()))) {
            enableLabels.remove(Labels.mysql.label());
        }

        //内网服务地址
        enableLabels.stream().filter(label -> StringUtils.isNotBlank(Constants.interIps.get(label)))
                .forEach(label -> allIpMap.put(Constants.interIps.get(label), node.getInternalIp()));

        if (enableLabels.contains(Labels.mysql.label())) {
            allIpMap.put(NetworkConstants.MASTER_DATABASE_IP, node.getInternalIp());
        }

        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);


        return this;
    }


    @Override
    protected NodeHandler initDefaultNodeConfig() {

        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setReportInternalIp(deployMessage.getIp());
        node.setType(Labels.mysql_ha.label());

        Map<String, Boolean> labelMap = new HashMap<>();
        DefaultDeployStructureEnumInvoke.services(Labels.mysql_ha.label()).forEach(label -> labelMap.put(label, false));

        node.setLabelMap(labelMap);
        super.node = node;
        return this;
    }

}
