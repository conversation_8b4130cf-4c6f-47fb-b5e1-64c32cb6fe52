package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.deploy.Namespace;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.db.JasyptService;
import com.xylink.util.SpringBeanUtil;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * database node 配置处理实现
 * kafka有可能需要设置公网ip，部分场景dmcu可能需要连接kafka公网IP
 */
public class DatabaseHandler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(DatabaseHandler.class);

    private static final String DEFAULT_OCEANBASE_CURRENT_CLUSTER = "cluster1";

    @Override
    protected NodeHandler check() {
        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();

        List<String> enableLabels = node.getLabelMap().entrySet().stream().filter(Map.Entry::getValue).map(Map.Entry::getKey).collect(Collectors.toList());
        if (enableLabels.contains(Labels.oceanbase.label())) {
            allIpMap.put(NetworkConstants.OCEANBASE_IP, node.getInternalIp());
            String currentCluster = allIpMap.get(NetworkConstants.OCEANBASE_CURRENT_CLUSTER);
            if (StringUtils.isBlank(currentCluster)) {
                String uid = null;
                Namespace namespace = deployService.getNamespaceByName(Constants.NAMESPACE_KUBE_SYSTEM);
                if (namespace != null) {
                    uid = namespace.getUid();
                }
                String clusterName = DigestUtils.md5Hex(
                    StringUtils.isBlank(uid) ? DEFAULT_OCEANBASE_CURRENT_CLUSTER : uid
                ).substring(0, 16);
                allIpMap.put(NetworkConstants.OCEANBASE_CURRENT_CLUSTER, clusterName);
            }
        }
        deployService.patchConfigMapAllIpForAddData(allIpMap);
        logger.info("Before oceanbase pod schedule,set OCEANBASE_IP:{} OCEANBASE_CURRENT_CLUSTER:{}", allIpMap.get(NetworkConstants.OCEANBASE_IP), allIpMap.get(NetworkConstants.OCEANBASE_CURRENT_CLUSTER));

        return this;
    }

    @Override
    public NodeHandler configureConfigMap() {
        if (node == null) {
            return this;
        }

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();

        List<String> enableLabels = node.getLabelMap().entrySet().stream().filter(Map.Entry::getValue).map(Map.Entry::getKey).collect(Collectors.toList());

        if (enableLabels.contains(Labels.kafka.label())) {
            allIpMap.put(NetworkConstants.KAFKA_PUBLIC_IP, node.getExternalIp());
        }

        //内网服务地址
        enableLabels.stream().filter(label -> StringUtils.isNotBlank(Constants.interIps.get(label)))
                .forEach(label -> allIpMap.put(Constants.interIps.get(label), node.getInternalIp()));

        if (StringUtils.isNotBlank(node.getNodeDbPort())) {
            allIpMap.put(NetworkConstants.DATABASE_PORT, node.getNodeDbPort());
        }
        String switchStatus = allIpMap.get(NetworkConstants.DB_PASSWORD_ENCRYPT_SWITCH);
        JasyptService jasyptService = SpringBeanUtil.getBean(JasyptService.class);
        // 使用容器化oceanbase时，hadoop共用主业务的oceanbase库
        if (enableLabels.contains(Labels.oceanbase.label())) {
            allIpMap.put("STATIS_ETL_DATABASE_IP", node.getInternalIp());
            allIpMap.put("STATIS_ETL_DATABASE_PORT", "2883");
            String value ="Da?548!YZ";
            if (Boolean.parseBoolean(switchStatus)) {
                value = jasyptService.encrypt(value);
            }
            allIpMap.put("STATIS_ETL_DB_PASSWORD", value);
        }

        if (!enableLabels.contains(Labels.mysql.label()) && !enableLabels.contains(Labels.dm.label())
                && !enableLabels.contains(Labels.st.label()) && !enableLabels.contains(Labels.kingbase.label())
                && !enableLabels.contains(Labels.oceanbase.label())) {
            allIpMap.put(NetworkConstants.MASTER_DATABASE_IP, node.getInternalIp());
        }

        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);
        return this;
    }


    @Override
    protected NodeHandler initDefaultNodeConfig() {

        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setReportInternalIp(deployMessage.getIp());
        node.setType(Labels.database.label());

        Map<String, Boolean> labelMap = new HashMap<>();
        DefaultDeployStructureEnumInvoke.services(Labels.database.label()).forEach(label -> labelMap.put(label, true));

        labelMap.put(Labels.st.label(), false);
        labelMap.put(Labels.dm.label(), false);
        labelMap.put(Labels.kingbase.label(), false);
        labelMap.put(Labels.oceanbase.label(), false);
        labelMap.put(Labels.mysql.label(), false);
        labelMap.put(Labels.canal.label(), false);
        node.setLabelMap(labelMap);
        super.node = node;
        return this;
    }

}
