package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.base.PlatformConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * statis node 配置处理实现
 */
public class StatisHandler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(StatisHandler.class);


    @Override
    public NodeHandler configureConfigMap() {
        if (node == null) return this;
        configureDistributeIP(node);

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();

        allIpMap.put(NetworkConstants.DCS_SERVER_INTERNAL_IP, node.getInternalIp());
        allIpMap.put(NetworkConstants.DES_SERVER_INTERNAL_IP, node.getInternalIp());
        allIpMap.put(NetworkConstants.DATAFACT_IP, node.getInternalIp());
        allIpMap.put(NetworkConstants.STATIS_INTERNAL_IP, node.getInternalIp());
        allIpMap.put(NetworkConstants.DATA_TRANSFER_SERVER_IP, node.getInternalIp());


        List<String> enableLabels = node.getLabelMap()
                .entrySet()
                .stream()
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        if (PlatformConfig.isAnKe()) {
            if (enableLabels.contains(Labels.statis_dameng.label()) || enableLabels.contains(Labels.statis_shentong.label())) {
                allIpMap.put(NetworkConstants.STATIS_DATABASE_IP, node.getInternalIp());
            }
        } else {
            if (enableLabels.contains(Labels.statis_mysql.label())) {
                allIpMap.put(NetworkConstants.STATIS_DATABASE_IP, node.getInternalIp());
            }
        }

        if (enableLabels.contains(Labels.hbase.label())) {
            allIpMap.put(NetworkConstants.HBASE_ZOOKEEPER_IP, node.getInternalIp());
        }
        //内网服务地址
        enableLabels.stream().filter(Constants.interIps::containsKey).forEach(label -> allIpMap.put(Constants.interIps.get(label), node.getInternalIp()));


        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);

        return this;

    }

    @Override
    protected NodeHandler initDefaultNodeConfig() {

        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setReportInternalIp(deployMessage.getIp());
        node.setType(Labels.statis.label());

        Map<String, Boolean> labelMap = new HashMap<>();
        DefaultDeployStructureEnumInvoke.services(Labels.statis.label()).forEach(label -> labelMap.put(label, true));

        labelMap.put(Labels.statis_education.label(), false);
        labelMap.put(Labels.clickhouse.label(), false);

        String installType = deployService.getConfigMapManagerData().getData().getOrDefault("install_type", "normal");
        if ("min".equalsIgnoreCase(installType)) {
            labelMap.put(Labels.statis_quality.label(), false);
        }
        node.setLabelMap(labelMap);
        super.node = node;
        return this;
    }
}
