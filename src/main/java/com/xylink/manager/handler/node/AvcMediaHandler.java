package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.deploy.ConfigMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/06/16/15:35
 */
public class AvcMediaHandler extends NodeHandler {
    private final static Logger logger = LoggerFactory.getLogger(AvcMediaHandler.class);

    @Override
    public NodeHandler configureConfigMap() {
        if(node==null) return this;
        configureDistributeIP(node);

        //如果all-avc-media没创建就 默认创建一个空configmap
        ConfigMap configMap = deployService.getConfigMapByName(Constants.CONFIGMAP_ALL_AVC_MEDIA, Constants.NAMESPACE_DEFAULT);
        if (configMap == null) {
            logger.info("find no configmap named all-avc-media, will create it!");
            deployService.createEmptyConfigMap(Constants.CONFIGMAP_ALL_AVC_MEDIA, Constants.NAMESPACE_DEFAULT);
        }
        handleDistributeIP(node, node.getType());
        //all-ip配置
        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();
        List<String> enableLabels = node.getLabelMap().entrySet().stream().filter(Map.Entry::getValue).map(Map.Entry::getKey).collect(Collectors.toList());
        //内网服务地址
        enableLabels.stream().filter(Constants.interIps::containsKey).forEach(label -> allIpMap.put(Constants.interIps.get(label), node.getInternalIp()));
        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);
        return this;
    }

    @Override
    protected NodeHandler initDefaultNodeConfig() {

        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setExternalIp(deployMessage.getIp());
        node.setDomain(deployMessage.getIp());
        node.setReportInternalIp(deployMessage.getIp());
        String type = deployMessage.getType();
        node.setType(type);

        Map<String, Boolean > labelMap = new HashMap<>(1);
        DefaultDeployStructureEnumInvoke.services(type).forEach(label -> labelMap.put(label, true));

        node.setLabelMap(labelMap);
        super.node = node;
        return this;
    }
}
