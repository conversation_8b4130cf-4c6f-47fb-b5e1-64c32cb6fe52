package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.model.em.Labels;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/05/12 14:16 下午
 */
public class JaegerHandler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(JaegerHandler.class);

    /**
     * 配置Jaeger相关的ConfigMap
     *
     * @param handler  节点处理器
     * @param enableLabels  启用的标签列表
     * @param internalIp    内部IP地址
     */
    public static void configureJaegerConfigMap(NodeHandler handler, List<String> enableLabels, String internalIp) {
        //Map<String, String> allIpMap = handler.deployService.getConfigMapAllIp().getData();
        ConfigMap allXmx = handler.deployService.getConfigMapByName(Constants.ALL_XMX);
        Map<String, String> allXmxMap = allXmx.getData();

        if (enableLabels.contains(Labels.jaeger.label())) {
            // jaeger部署
            // 1.把服务ip刷新到configmap all-ip中 JAEGER_IP
            Map<String, String> addMap = new HashMap<>();
            addMap.put(NetworkConstants.JAEGER_IP, internalIp);

            // 2.修改JVM参数
            String currentJvmTraceAgent = allXmxMap.get("JVM_TRACE_AGENT");
            if (currentJvmTraceAgent != null && currentJvmTraceAgent.contains("-Dotel.traces.exporter=none")) {
                // 只修改traces.exporter和添加必要的配置
                String jaegerJvmTraceAgent = currentJvmTraceAgent
                        .replace("-Dotel.traces.exporter=none", "-Dotel.traces.exporter=otlp")
                        + " -Dotel.exporter.otlp.endpoint=http://" + internalIp + ":4318"
                        + " -Dotel.service.name=$SERVER_NAME"
                        + " -Dotel.service.name.default=unknown-service"
                        + " -Dotel.java.disabled.resource.providers=io.opentelemetry.instrumentation.resources.ProcessResourceProvider"
                        + " -Dotel.instrumentation.kafka.enabled=false"

                        ;
                allXmxMap.put("JVM_TRACE_AGENT", jaegerJvmTraceAgent);
            }

            handler.deployService.patchConfigMapAllIpForAddData(addMap);

        } else {
            // jaeger取消部署
            // 1.删除all-ip 中的JAEGER_IP，方便后续nginx进行处理
            handler.deployService.patchConfigMap(Constants.CONFIGMAP_ALLIP, Constants.NAMESPACE_DEFAULT, d -> {
                d.remove(NetworkConstants.JAEGER_IP);
            });

            // 2.恢复JVM参数到原始状态
            String currentJvmTraceAgent = allXmxMap.get("JVM_TRACE_AGENT");
            if (currentJvmTraceAgent != null && currentJvmTraceAgent.contains("-Dotel.traces.exporter=otlp")) {
                String originalJvmTraceAgent = currentJvmTraceAgent
                        .replace("-Dotel.traces.exporter=otlp", "-Dotel.traces.exporter=none")
                        .replaceAll("-Dotel.exporter.otlp.endpoint=http://[^\\s]+:4318", "")
                        .replace("-Dotel.service.name=$SERVER_NAME", "")
                        .replace("-Dotel.service.name.default=unknown-service", "")
                        .replace("-Dotel.instrumentation.kafka.enabled=false", "")
                        .replace(
                                "-Dotel.java.disabled.resource.providers=io.opentelemetry.instrumentation.resources.ProcessResourceProvider",
                                "")
                        .replaceAll("\\s+", " ")
                        .trim();
                allXmxMap.put("JVM_TRACE_AGENT", originalJvmTraceAgent);
            }
        }

        handler.deployService.patchConfigMap(Constants.CONFIGMAP_XMX, Constants.NAMESPACE_DEFAULT, d -> {
            d.putAll(allXmxMap);
        });

    }

    @Override
    public NodeHandler configureConfigMap() {
        if (node == null)
            return this;

        // 判断标签
        List<String> enableLabels = node.getLabelMap().entrySet().stream()
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        configureJaegerConfigMap(this, enableLabels, node.getInternalIp());

        return this;
    }

}
