package com.xylink.manager.handler.node;

import com.xylink.config.NetworkConstants;
import com.xylink.manager.handler.node.mid.MidHandlerAdapter;
import com.xylink.util.ClusterUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.ServiceLoader;

/**
 * <AUTHOR>
 * @create 2023/9/1 14:58
 */
public class MiddlewareHandler extends NodeHandler {

    private static final Logger logger = LoggerFactory.getLogger(MiddlewareHandler.class);

    @Override
    protected NodeHandler configureConfigMap() {
        if (Objects.isNull(node)) {
            return this;
        }
        handleDistributeIP(node, node.getType());
        //看着什么都没做，为啥要patch
//        Map<String, String> allIp = deployService.getConfigMapAllIp().getData();
//        logger.info("update all-ip: {}", allIp);
//        deployService.patchConfigMapAllIpForAddData(allIp);
        // 其他中间件
        ServiceLoader<MidHandlerAdapter> serviceLoader = ServiceLoader.load(MidHandlerAdapter.class);
        serviceLoader.forEach(provider -> provider.configureConfigMap(node));
        return this;
    }

    /**
     * 处理amq-cluster标签逻辑
     *
     * @param allIp
     * @param selected
     */
    private void updateAmqClusterConfig(Map<String, String> allIp, boolean selected) {
        String hosts = allIp.get(NetworkConstants.MAIN_AMQ_IP);
        //选中标签 or 去除标签
        if (selected) {
            if (StringUtils.isBlank(hosts)) {
                hosts = node.getInternalIp();
            } else if (!hosts.contains(node.getInternalIp())) {
                hosts = hosts + ":61616,tcp://" + node.getInternalIp();
            }

            if (StringUtils.isBlank(allIp.get(NetworkConstants.MAIN_MASTER_AMQ_IP))) {
                allIp.put(NetworkConstants.MAIN_MASTER_AMQ_IP, node.getInternalIp());
            }
        } else {
            hosts = ClusterUtil.removeNodeIpForAmq(hosts, node.getInternalIp());
            if (node.getInternalIp().equalsIgnoreCase(allIp.get(NetworkConstants.MAIN_MASTER_AMQ_IP))) {
                allIp.remove(NetworkConstants.MAIN_MASTER_AMQ_IP);
            }
        }

        allIp.put(NetworkConstants.MAIN_AMQ_IP, hosts);
    }


    /**
     * 和标准流程不一样的是  默认不部署该节点下的服务
     *
     * @return
     */
    @Override
    protected NodeHandler initDefaultNodeConfig() {
        // 默认部署了改节点类型下的所有服务
        super.initDefaultNodeConfig();
        Map<String, Boolean> labels = new HashMap<>();
        String type = deployMessage.getType();
        //默认不部署所有服务
        DefaultDeployStructureEnumInvoke.services(type).forEach(label -> labels.put(label, false));
        this.node.setLabelMap(labels);
        return this;
    }
}
