package com.xylink.manager.handler.node;

import com.xylink.config.NetworkConstants;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.em.Labels;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * charge node 配置处理实现。
 * charge 节点上可能部署charge-redis，该redis只给charge使用，如果没有部署单独charge-redis，则使用主redis
 */
public class ChargeHandler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(ChargeHandler.class);


    @Override
    public NodeHandler configureConfigMap() {
        if(node==null) return this;
        configureDistributeIP(node);
        handleDistributeIP(node, Labels.charge.label());

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();


        if (node.getLabelMap().get(Labels.charge_redis.label())) {
            allIpMap.put(NetworkConstants.CHARGE_REDIS_IP, node.getInternalIp());
        }else{
            String ip = getDistributeIp(Labels.charge_redis.label());
            allIpMap.put(NetworkConstants.CHARGE_REDIS_IP, StringUtils.isNotBlank(ip) ? ip : "127.0.0.1");
        }


//        allIpMap.put(NetworkConstants.CHARGE_IP, node.getInternalIp());
        //多节点服务
        DefaultDeployStructureEnumInvoke.services(Labels.charge.label())
                .stream()
                .map(Labels::labelOf)
                .filter(Labels::isDistributed)
                .filter(label -> StringUtils.isNotBlank(label.loadblanceKey()))
                .forEach(label -> allIpMap.put(label.loadblanceKey(), getDistributeIp(label.label())));

        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);

        return this;
    }

    @Override
    protected NodeHandler initDefaultNodeConfig() {

        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setReportInternalIp(deployMessage.getIp());
        node.setType(Labels.charge.label());

        Map<String, Boolean > labelMap = new HashMap<>();
        DefaultDeployStructureEnumInvoke.services(Labels.charge.label()).forEach(label -> labelMap.put(label, false));

        //默认不部署charge-redis 避免影响当前业务
        labelMap.put(Labels.charge.label(), true);
        node.setLabelMap(labelMap);
        super.node = node;
        return this;
    }
}
