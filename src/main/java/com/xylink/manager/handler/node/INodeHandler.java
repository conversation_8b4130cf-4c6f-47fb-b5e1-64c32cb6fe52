package com.xylink.manager.handler.node;

import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.DeployMessage;
import com.xylink.manager.model.cm.ICMDto;
import com.xylink.util.JDBCUtils;

public interface INodeHandler {

    boolean configure(NodeDto node, JDBCUtils jdbcUtils);

    boolean defaultConfigure(DeployMessage deployMessage, JDBCUtils jdbcUtils);

    <T extends ICMDto> boolean serviceAdvanceConfigure(T t, String configmap, JDBCUtils jdbcUtils, String label, String nodeName);
}
