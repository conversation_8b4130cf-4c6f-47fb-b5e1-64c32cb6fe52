package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.config.MysqlConstants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.SigServerHaConfigMap;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.cm.ICMDto;
import com.xylink.manager.model.cm.MysqlCM;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.base.PlatformConfig;
import com.xylink.util.Ipv6Util;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * vod node 配置处理实现
 */
public class VodPocHandler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(VodPocHandler.class);
    public static final String NODETYPE_VOD_POC = "vod-poc";
    public static final String NODETYPE_VOD = "vod";
    public static final String NODETYPE_MYSQL = "mysql";


    @Override
    public NodeHandler configureConfigMap() {
        if (node == null) return this;
        configureDistributeIP(node);
        handleDistributeIP(node, Labels.vod_poc.label());

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();


        allIpMap.put(NetworkConstants.VOD_DOMAIN_NAME, StringUtils.isBlank(node.getDomain()) ? node.getExternalIp() : node.getDomain());
        allIpMap.put(NetworkConstants.VOD_NGINX_PORT, StringUtils.isBlank(node.getNginxPort()) ? "80" : node.getNginxPort());
        allIpMap.put(NetworkConstants.VOD_NGINX_SSL_PORT, StringUtils.isBlank(node.getNginxSslPort()) ? "443" : node.getNginxSslPort());
        allIpMap.put(NetworkConstants.VOD_INTERNAL_IP, node.getInternalIp());
        allIpMap.put(NetworkConstants.VOD_PUBLIC_IP, node.getExternalIp());

        List<String> enableLabels = node.getLabelMap()
                .entrySet()
                .stream()
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        // 如果部署有 rmserver
        SigServerHaConfigMap.clearNodesRecordInAllConfigMapWithoutLabel(Labels.rmserver);
        if (enableLabels.contains(Labels.rmserver.label())) {
            SigServerHaConfigMap.updateAllSigServerHa(Labels.rmserver, node.getName(), node.getInternalIp());
        }

        if (enableLabels.contains(Labels.srs.label())) {
            allIpMap.put("SRS_IP", node.getInternalIp());
        }

        if (enableLabels.contains(Labels.avcloudapi.label())) {
            allIpMap.put(NetworkConstants.AVCLOUDAPI_IP, node.getInternalIp());
        }
        if (enableLabels.contains(Labels.mcaccess.label())) {
            allIpMap.put(NetworkConstants.MCACCESS_IP, node.getInternalIp());
        }
        if (enableLabels.contains(Labels.vod_fileserver.label())) {
            allIpMap.put("VOD_FILESERVER_IP", node.getInternalIp());
        }

        //vodserver是有状态的，所以需要将所有vodserver服务器节点的hostname和ip映射存储起来
        if (enableLabels.contains(Labels.vod.label())) {
            List<Pod> pods = deployService.listPodsByAppLabel("private-vod");
            if (!CollectionUtils.isEmpty(pods)) {
                String hostNameIps = pods.stream().map(pod -> pod.getNodeName() + ":" + Ipv6Util.handlerIpv6Addr(pod.getIp())).collect(Collectors.joining(","));
                // hostname1:ip1,hostname2:ip2
                allIpMap.put("VODSERVER_HOSTNAME_IP_MAPPING", hostNameIps);
            }
        }

        //内网服务地址
        enableLabels.stream().filter(Constants.interIps::containsKey).forEach(label -> allIpMap.put(Constants.interIps.get(label), node.getInternalIp()));

        allIpMap.put(NetworkConstants.UAA_DATABASE_IP, node.getInternalIp());

        if (PlatformConfig.isAnKe()) {
            allIpMap.put(NetworkConstants.UAA_DATABASE_IP, allIpMap.get(NetworkConstants.DATABASE_IP));
        }

        if (enableLabels.contains(Labels.kafka.label()))
            allIpMap.put(NetworkConstants.KAFKA_PUBLIC_IP, node.getExternalIp());

        if (StringUtils.isNotBlank(node.getNodeDbPort()))
            allIpMap.put(NetworkConstants.DATABASE_PORT, node.getNodeDbPort());

        allIpMap.put(NetworkConstants.MASTER_DATABASE_IP, node.getInternalIp());


        //多节点服务
        DefaultDeployStructureEnumInvoke.services(node.getType())
                .stream()
                .map(Labels::labelOf)
                .filter(Labels::isDistributed)
                .filter(label -> StringUtils.isNotBlank(label.loadblanceKey()))
                .forEach(label -> allIpMap.put(label.loadblanceKey(), getDistributeIp(label.label())));

        allIpMap.put(NetworkConstants.UAA_INTERNAL_IP, node.getInternalIp());
        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);
        return this;
    }


    @Override
    protected NodeHandler initDefaultNodeConfig() {

        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setExternalIp(deployMessage.getIp());
        node.setDomain(deployMessage.getIp());
        node.setNginxPort("80");
        node.setNginxSslPort("443");
        node.setReportInternalIp(deployMessage.getIp());
        node.setType(Labels.vod_poc.label());

        Map<String, Boolean> labelMap = new HashMap<>();
        DefaultDeployStructureEnumInvoke.services(Labels.vod_poc.label()).forEach(label -> labelMap.put(label, true));
        labelMap.put(Labels.srs.label(), false);
        labelMap.put(Labels.vodclustermgr.label(), false);
        labelMap.put(Labels.es.label(), false);

        String installType = deployService.getConfigMapManagerData().getData().getOrDefault("install_type", "normal");
        if ("min".equalsIgnoreCase(installType)) {
            deployService.scaleDeployment("private-vodedit", Constants.NAMESPACE_DEFAULT, 0);
        }
        node.setLabelMap(labelMap);
        super.node = node;
        return this;
    }

    @Override
    protected <T extends ICMDto> NodeHandler afterConfigureICMDto(T t) {
        if (t instanceof MysqlCM) {
            MysqlCM mysqlCM = (MysqlCM) t;
            Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();
            String ip = allIpMap.get(NetworkConstants.DATABASE_IP);
            String port = allIpMap.get(NetworkConstants.DATABASE_PORT);
            String updateSql = MysqlConstants.SET_READ_ONLY_PROPERTY_TEMPLATE.replace("{value}", mysqlCM.getMode());
            updateSql += MysqlConstants.SET_SUPER_READ_ONLY_PROPERTY_TEMPLATE.replace("{value}", mysqlCM.getMode());
            jdbcUtils.setMysqlProperties(updateSql, ip, port, "dbbak", "U1O5ZeRyLFd#u9T6TF9h");
        }
        return this;
    }
}
