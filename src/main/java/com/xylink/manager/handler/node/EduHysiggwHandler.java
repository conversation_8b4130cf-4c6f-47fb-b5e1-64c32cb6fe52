package com.xylink.manager.handler.node;

/**
 * 红云网关
 * configureHySigGatewaySN 给红云网关配置SN，但是实际项目中，为了方便，红云网关的sn可能已经配置固定，该逻辑为遗留逻辑保留，实际无用
 */
public class EduHysiggwHandler extends NodeHandler {

    @Override
    protected NodeHandler configureConfigMap() {
        if(node==null) return this;
        super.configureDistributeIP(node);
        return this;
    }

//    @Override
//    protected NodeHandler afterConfigure() {
//        if (advanceConfig==null || jdbcUtils==null) return this;
//        HySigGatewayCM hy = (HySigGatewayCM) advanceConfig;
//        if (StringUtils.isNotBlank(hy.getHyGatewaySn())) {
//            jdbcUtils.configureHySigGatewaySN(hy.getHyGatewaySn());
//        }
//        return this;
//    }
}
