package com.xylink.manager.handler.node;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * ippbx node 配置处理实现
 */
public class IppbxMediagwHandler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(IppbxMediagwHandler.class);


    @Override
    public NodeHandler configureConfigMap() {
        if(node==null) return this;
        configureDistributeIP(node);
        return this;

    }

}
