package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.clustersetting.failover.telnet.TelnetClient;
import com.xylink.util.IdPoolUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

public class HadoopMasterHandler extends NodeHandler {
    private final static Logger logger = LoggerFactory.getLogger(HadoopMasterHandler.class);

    private static final String ALL_CONFIG_NAME = "all-hadoop-zookeeper";
    private static final String[] ZOOKEEPER_ID_POOL;
    private static final String[] HADOOP_HOST_ID_POOL;
    private static final String HADOOP_HOST_ID_CURRENT_INDEX_KEY = "HADOOP_HOSTNAME_CURRENT";


    static {
        ZOOKEEPER_ID_POOL = new String[]{"1", "2", "3"};
        HADOOP_HOST_ID_POOL = new String[]{"HADOOP_HOSTNAME1", "HADOOP_HOSTNAME2"};
    }

    @Override
    protected NodeHandler configureConfigMap() {
        if (Objects.isNull(node)) {
            return this;
        }
        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();

        List<String> enableLabels = node.getLabelMap().entrySet().stream().filter(Map.Entry::getValue).map(Map.Entry::getKey).collect(Collectors.toList());

        if (enableLabels.contains(Labels.hadoop_master.label())) {
            configHadoopHosts(allIpMap);
        }
        if (enableLabels.contains(Labels.hadoop_zookeeper_cluster.label())) {
            handleDistributeIP(node, "hadoop-zookeeper");
            setZookeeperMyId();
        } else {
            // 去掉本节点 ZK_ID_IP
            clearNumberMappingIpConfigMap();
        }
        // 集群模式配置 HADOOP_ZOOKEEPER_IP: **************:2181,**************:2181,**************
        // 需要设置 MASTER_HADOOP_ZOOKEEPER_IP
        Pair<Set<String>, Set<String>> ips = getNodeInternalIpAndPublicIpByLabel("hadoop-zookeeper", "private-hadoop-zookeeper-cluster");
        Set<String> zookeeperInternalIps = ips.getRight();
        /*if (!CollectionUtils.isEmpty(zookeeperInternalIps)) {
            allIpMap.put(NetworkConstants.HADOOP_ZOOKEEPER_IP, StringUtils.join(zookeeperInternalIps, ":2181,"));
        }*/
        allIpMap.put(NetworkConstants.MASTER_HADOOP_ZOOKEEPER_IP, getRandomIp(zookeeperInternalIps));
        allIpMap.put(NetworkConstants.HBASE_ZOOKEEPER_IP, String.join(",", zookeeperInternalIps));
        allIpMap.put(NetworkConstants.HADOOP_DATA_IP_LIST, initHadoopDataIpList());
        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);
        return this;
    }

    private void configHadoopHosts(Map<String, String> allIpMap) {
        // 循环使用
        String newValue = this.node.getName();
        if (!allIpMap.containsValue(newValue)) {
            int current = -1;
            String currentStr = allIpMap.get(HADOOP_HOST_ID_CURRENT_INDEX_KEY);
            if (StringUtils.isNotBlank(currentStr)) {
                current = Integer.parseInt(currentStr);
            }
            int index = IdPoolUtils.getCycleIndex(HADOOP_HOST_ID_POOL, current);
            String key = HADOOP_HOST_ID_POOL[index];
            allIpMap.put(key, newValue);
            allIpMap.put(HADOOP_HOST_ID_CURRENT_INDEX_KEY, String.valueOf(index));
        }
    }

    private Map<String, String> setZookeeperMyId() {
        return deployService.patchConfigMap(ALL_CONFIG_NAME, Constants.NAMESPACE_DEFAULT, d -> {
            numberInConfigMap(d);
            d.put(node.getName() + Constants.HADOOP_TYPE, Labels.hadoop_master.label());
        });
    }

    /**
     * 去掉本节点 ZK_ID_IP
     *
     * @return
     */
    private Map<String, String> clearNumberMappingIpConfigMap() {
        return deployService.patchConfigMap(ALL_CONFIG_NAME, Constants.NAMESPACE_DEFAULT, d -> {
            d.entrySet().removeIf(this::clearNumberMappingIpConfigMapInner);
            d.put(node.getName() + Constants.HADOOP_TYPE, Labels.hadoop_single.label());
        });
    }

    /**
     * 清除 key  ZK_x_IP && nodename-MYID
     *
     * @param stringEntry
     * @return
     */
    private boolean clearNumberMappingIpConfigMapInner(Map.Entry<String, String> stringEntry) {
        return (stringEntry.getKey().startsWith("ZK_") && stringEntry.getValue().equals(node.getInternalIp())) || (stringEntry.getKey().equals(node.getName() + "-MYID"));
    }

    /**
     * HOSTNAME-MYID : 每个zk实例对应的myid值
     */
    private void numberInConfigMap(Map<String, String> allZookeeperConfigMap) {
        // key
        String key = node.getName() + "-MYID";
        // 获取部署的kafka 在之前的计数上增加1
        if (allZookeeperConfigMap.containsKey(key)) {
            return;
        }
        // 获取部署的zookeeper, 从池中取【1，2，3】
        List<String> inUsed = new ArrayList<>();
        allZookeeperConfigMap.forEach((itemKey, itemValue) -> {
            if (itemKey.endsWith("-MYID")) {
                inUsed.add(itemValue);
            }
        });
        String value = IdPoolUtils.getId(ZOOKEEPER_ID_POOL, inUsed);
        allZookeeperConfigMap.put(key, value);
        numberMappingIpConfigMap(allZookeeperConfigMap, value);
    }

    /**
     * 每个zk实例对应的myid-ip关系
     *
     * @param allZookeeperConfigMap
     * @param sequence
     */
    private void numberMappingIpConfigMap(Map<String, String> allZookeeperConfigMap, String sequence) {
        String key = "ZK_" + sequence + "_IP";
        allZookeeperConfigMap.put(key, node.getInternalIp());
    }

    private String getRandomIp(Set<String> zookeeperInternalIps) {
        if (!CollectionUtils.isEmpty(zookeeperInternalIps)) {
            for (String ip : zookeeperInternalIps) {
                if (new TelnetClient(ip, 2181).tryTelnet()) {
                    return ip;
                }
            }
        }
        return node.getInternalIp();
    }
}
