package com.xylink.manager.handler.node;

import com.xylink.config.NetworkConstants;
import com.xylink.config.SigServerHaConfigMap;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.model.em.Labels;
import com.xylink.util.Ipv6Util;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * vod node 配置处理实现
 */
public class VodBaseHandler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(VodBaseHandler.class);


    @Override
    public NodeHandler configureConfigMap() {
        if(node==null) return this;
        configureDistributeIP(node);
        handleDistributeIP(node, Labels.vod_base.label());

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();


        allIpMap.put(NetworkConstants.VOD_DOMAIN_NAME, StringUtils.isBlank(node.getDomain()) ? node.getExternalIp() : node.getDomain());
        allIpMap.put(NetworkConstants.VOD_NGINX_PORT, StringUtils.isBlank(node.getNginxPort()) ? "80" : node.getNginxPort());
        allIpMap.put(NetworkConstants.VOD_NGINX_SSL_PORT, StringUtils.isBlank(node.getNginxSslPort()) ? "443" : node.getNginxSslPort());
        allIpMap.put(NetworkConstants.VOD_INTERNAL_IP, node.getInternalIp());
        allIpMap.put(NetworkConstants.VOD_PUBLIC_IP, node.getExternalIp());
        allIpMap.put(NetworkConstants.VOD_BASE_INTERNAL_IP, node.getInternalIp());

        List<String> enableLabels = node.getLabelMap()
                .entrySet()
                .stream()
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        if (enableLabels.contains(Labels.mcaccess.label())) {
            allIpMap.put(NetworkConstants.MCACCESS_IP, node.getInternalIp());
        }

        // 如果部署有 rmserver
        SigServerHaConfigMap.clearNodesRecordInAllConfigMapWithoutLabel(Labels.rmserver);
        if (enableLabels.contains(Labels.rmserver.label())) {
            SigServerHaConfigMap.updateAllSigServerHa(Labels.rmserver, node.getName(), node.getInternalIp());
        }
        if (enableLabels.contains(Labels.srs.label())) {
            allIpMap.put("SRS_IP", node.getInternalIp());
        }
        if (enableLabels.contains(Labels.vod_fileserver.label())) {
            allIpMap.put("VOD_FILESERVER_IP", node.getInternalIp());
        }

        //多节点服务
        DefaultDeployStructureEnumInvoke.services(node.getType())
                .stream()
                .map(Labels::labelOf)
                .filter(Labels::isDistributed)
                .filter(label -> StringUtils.isNotBlank(label.loadblanceKey()))
                .forEach(label -> allIpMap.put(label.loadblanceKey(), getDistributeIp(label.label())));

        //vodserver是有状态的，所以需要将所有vodserver服务器节点的hostname和ip映射存储起来
        if (enableLabels.contains(Labels.vod.label())) {
            List<Pod> pods = deployService.listPodsByAppLabel("private-vod");
            if (!CollectionUtils.isEmpty(pods)) {
                String hostNameIps = pods.stream().map(pod -> pod.getNodeName() + ":" + Ipv6Util.handlerIpv6Addr(pod.getIp())).collect(Collectors.joining(","));
                // hostname1:ip1,hostname2:ip2
                allIpMap.put("VODSERVER_HOSTNAME_IP_MAPPING", hostNameIps);
            }
        }

        logger.info("update all-ip:{} ", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);
        return this;

    }

    @Override
    protected NodeHandler initDefaultNodeConfig() {
        // 默认不部署改节点类型下的所有服务
        super.initDefaultNodeConfig();
        Map<String, Boolean> labels = new HashMap<>();
        String type = deployMessage.getType();
        DefaultDeployStructureEnumInvoke.services(type).forEach(label -> labels.put(label, false));
        this.node.setLabelMap(labels);
        return this ;
    }
}
