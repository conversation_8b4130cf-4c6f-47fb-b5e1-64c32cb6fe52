package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.em.Labels;
import com.xylink.util.K8sUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/9/11 14:38
 */
public class FusionMmsHandler extends NodeHandler {

    @Override
    protected NodeHandler configureConfigMap() {
        configureDistributeIP(node);
        handleDistributeIP(node, node.getType());

        Map<String, String> allIpMap = new HashMap<>();

        if (node.getLabelMap().containsKey(Labels.openresty_fusion.label()) && node.getLabelMap().get(Labels.openresty_fusion.label())) {
            allIpMap.put(NetworkConstants.FUSION_INTERNAL_IP, node.getInternalIp());
        }

        DefaultDeployStructureEnumInvoke.services(node.getType())
                .stream()
                .map(Labels::labelOf)
                .filter(Labels::isDistributed)
                .filter(label -> StringUtils.isNotBlank(label.loadblanceKey()))
                .forEach(label -> allIpMap.put(label.loadblanceKey(), getDistributeIp(label.label())));

        K8sUtils.patchConfigMap(Constants.CONFIGMAP_ALLIP, allIpMap);
        return this;
    }


    @Override
    protected NodeHandler initDefaultNodeConfig() {

        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setReportInternalIp(deployMessage.getIp());
        node.setType(deployMessage.getType());

        Map<String, Boolean > labelMap = new HashMap<>();
        DefaultDeployStructureEnumInvoke.services(node.getType()).forEach(label -> labelMap.put(label, false));
        node.setLabelMap(labelMap);
        super.node = node;
        return this;
    }
}
