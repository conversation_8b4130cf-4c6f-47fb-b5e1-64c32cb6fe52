package com.xylink.manager.handler.node;

import com.xylink.config.NetworkConstants;
import com.xylink.manager.model.deploy.Deployment;
import com.xylink.manager.model.em.Labels;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> create on 2024/9/23
 */
public class MatrixTranscriptHandler extends NodeHandler {
    private final static Logger logger = LoggerFactory.getLogger(MatrixTranscriptHandler.class);


    @Override
    public NodeHandler configureConfigMap() {
        if (node == null) return this;
        configureDistributeIP(node);
        handleDistributeIP(node, Labels.matrix_transcript.label());

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();

        List<Deployment> deploymentList = deployService.listDeploymentByAppLabel("private-matrix-mysql");
        if (!CollectionUtils.isEmpty(deploymentList)) {
            allIpMap.put(NetworkConstants.MATRIX_DATABASE_IP, node.getInternalIp());
        }
        allIpMap.put(NetworkConstants.MATRIX_INTERNAL_IP, node.getInternalIp());
        allIpMap.put(NetworkConstants.TRANSCRIPTION_INTERNAL_IP, node.getInternalIp());
        allIpMap.put(NetworkConstants.TRANSCRIPTION_PUBLIC_IP, node.getExternalIp());

        //多节点服务
        DefaultDeployStructureEnumInvoke.services(Labels.matrix_transcript.label())
                .stream()
                .map(Labels::labelOf)
                .filter(Labels::isDistributed)
                .filter(label -> StringUtils.isNotBlank(label.loadblanceKey()))
                .forEach(label -> allIpMap.put(label.loadblanceKey(), getDistributeIp(label.label())));

        logger.info("update all-ip: {}", allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);
        return this;

    }

    @Override
    protected NodeHandler initDefaultNodeConfig() {
        super.initDefaultNodeConfig();
        return this;
    }

}
