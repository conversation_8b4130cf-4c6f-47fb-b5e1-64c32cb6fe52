package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.model.em.Labels;
import com.xylink.util.ClusterUtil;
import com.xylink.util.Ipv6Util;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 基础 node 配置
 * 主要针对节点只部署单个服务的情况，对于集中部署服务的节点可能不使用
 *
 */
public class BaseHandler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(BaseHandler.class);


    @Override
    public NodeHandler configureConfigMap() {
        if(node==null) return this;

        //如果支持多活, 在all-* configmap 配置该节点内外网IP信息
        configureDistributeIP(node);
        handleDistributeIP(node, node.getType());

        //all-ip配置
        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();
        //内网ip
        String networkName = Constants.interIps.get(node.getType());
        if(StringUtils.isNotEmpty(networkName)) allIpMap.put(networkName, node.getInternalIp());

        if (isContainHbaseLabel()) {
            allIpMap.put(NetworkConstants.HBASE_ZOOKEEPER_IP, node.getInternalIp());
        }

        String ipipProxy;
        if(Labels.dmcu.label().equalsIgnoreCase(node.getType()) && node.getLabelMap().containsKey(Labels.ipip_proxy.label()) && node.getLabelMap().get(Labels.ipip_proxy.label())) {
            ipipProxy = Constants.interIps.get(Labels.ipip_proxy.label());
            allIpMap.put(ipipProxy, node.getInternalIp());
        }

        if (node.getLabelMap().containsKey(Labels.txlive.label()) && node.getLabelMap().get(Labels.txlive.label())) {
            allIpMap.put(NetworkConstants.TXLIVE_SERVER_IP, node.getInternalIp());
        }

        //外网ip
        String networkPubName = Constants.exterIps.get(node.getType());
        if (StringUtils.isNotEmpty(networkPubName))
            allIpMap.put(networkPubName, StringUtils.isBlank(node.getExternalIp()) ? node.getInternalIp() : node.getExternalIp());

        //多节点服务
        Labels label = Labels.labelOf(node.getType());
        if (label.isDistributed() && StringUtils.isNotBlank(label.loadblanceKey())) {
            allIpMap.put(label.loadblanceKey(), getDistributeIp(label.label()));
        }

        //多节点服务
        DefaultDeployStructureEnumInvoke.services(node.getType())
                .stream()
                .map(Labels::labelOf)
                .filter(Labels::isDistributed)
                .filter(temLabel -> StringUtils.isNotBlank(temLabel.loadblanceKey()))
                .forEach(temLabel -> allIpMap.put(temLabel.loadblanceKey(), getDistributeIp(temLabel.label())));

        logger.info("update all-ip: {}", allIpMap);
        List<String> enableLabels = node.getLabelMap().entrySet().stream().filter(Map.Entry::getValue).map(Map.Entry::getKey).collect(Collectors.toList());
        if (Labels.mc.label().equals(node.getType())) {
            ClusterUtil.updateMcIpConfig(allIpMap, enableLabels.contains(Labels.mc.label()), Ipv6Util.handlerIpv6Addr(node.getInternalIp()), node.getName());
        }
        deployService.patchConfigMapAllIpForAddData(allIpMap);
        updatePresenceServer(enableLabels);
        // 241220-分区云5.2启用点对点号码池
        updateP2PPoolNumber(enableLabels);
        return this;

    }

    private boolean isContainHbaseLabel() {
        return !CollectionUtils.isEmpty(node.getLabelMap()) && node.getLabelMap().containsKey(Labels.hbase.label()) && node.getLabelMap().get(Labels.hbase.label());
    }

}
