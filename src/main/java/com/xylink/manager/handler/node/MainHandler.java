package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.ProxyConstants;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.model.em.DefaultConfigmapDataEnum;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.clustersetting.domain.ClusterConfigEnum;
import com.xylink.util.ClusterUtil;
import com.xylink.util.Ipv6Util;
import com.xylink.util.K8sUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * main node 配置处理实现
 */
@Component
public class MainHandler extends NodeHandler {

    private final static Logger logger = LoggerFactory.getLogger(MainHandler.class);


    @Override
    public NodeHandler configureConfigMap() {
        if (node == null) return this;
        configureDistributeIP(node);

        handleDistributeIP(node, Labels.mc.label());
        //main 默认部署 openresty-main，前端label不显示，但是需要更新all-openresty-main
        handleDistributeIP(node, Labels.openresty_main.label());

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();

        List<String> enableLabels = node.getLabelMap()
                .entrySet()
                .stream()
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());


        //内网服务地址
        enableLabels.stream().filter(Constants.interIps::containsKey).forEach(label -> allIpMap.put(Constants.interIps.get(label), node.getInternalIp()));

        //外网服务地址
        enableLabels.stream().filter(Constants.exterIps::containsKey).forEach(label -> allIpMap.put(Constants.exterIps.get(label), node.getExternalIp()));

        //node 配置
        allIpMap.put(NetworkConstants.MAIN_DOMAIN_NAME, StringUtils.isBlank(node.getDomain()) ? node.getExternalIp() : node.getDomain());
        allIpMap.put(NetworkConstants.MAIN_NGINX_PORT, StringUtils.isBlank(node.getNginxPort()) ? "80" : node.getNginxPort());
        allIpMap.put(NetworkConstants.MAIN_NGINX_SSL_PORT, StringUtils.isBlank(node.getNginxSslPort()) ? "443" : node.getNginxSslPort());
        allIpMap.put(NetworkConstants.MAIN_IP, node.getInternalIp());

        //高可用模式时,main节点保存时不覆盖MAIN_INTERNAL_IP
        Map<String, String> allCluster = K8sUtils.getConfigMap(Constants.CONFIGMAP_CLUSTER);
        if (StringUtils.isBlank(allCluster.get(ClusterConfigEnum.INTERNAL_HAPROXY.getHaAddressKey()))) {
            allIpMap.put(NetworkConstants.MAIN_INTERNAL_IP, node.getInternalIp());
        }

        allIpMap.put(NetworkConstants.MAIN_PUBLIC_IP, node.getExternalIp());

        if (enableLabels.contains(Labels.mms.label())) {
            allIpMap.put("MMS_DISPATCHER_IP", node.getInternalIp());
            allIpMap.put("DEVICE_IP", node.getInternalIp());
            allIpMap.put("DSU_IP", node.getInternalIp());
            allIpMap.put("MMS_PERMISSION_IP", node.getInternalIp());
            allIpMap.put("MMSPROXY_IP", node.getInternalIp());
        }

        allIpMap.put(NetworkConstants.MAIN_BACKEND_SERVER_NGINX_PORT, node.getBackendServerNginxPort());
        allIpMap.put(NetworkConstants.MAIN_BACKEND_SERVER_NGINX_SSL_PORT, node.getBackendServerNginxSslPort());

        ConfigMap configMap = deployService.getConfigMapByName(Constants.CONFIGMAP_ALL_MMS, Constants.NAMESPACE_DEFAULT);
        Map<String, String> allMms = configMap == null ? DefaultConfigmapDataEnum.initDefault(Labels.mms.label()) : configMap.getData();
        allMms.put(node.getName() + "-MMS_DEVICESTATE_IP", node.getInternalIp());
        allMms.put(node.getName() + "-MMS_DISPATCHER_IP", node.getInternalIp());
        allMms.put(node.getName() + "-MMS_EDGE_IP", node.getInternalIp());
        allMms.put(node.getName() + "-MMS_LOGIC_IP", node.getInternalIp());

        if (StringUtils.isNoneBlank(node.getBackendServerNginxPort(), node.getBackendServerNginxSslPort())) {
            allIpMap.put(ProxyConstants.ALLOW_SINGLE_ACCESS_CONSOLE, "true");
        } else {
            allIpMap.put(ProxyConstants.ALLOW_SINGLE_ACCESS_CONSOLE, "false");
        }

        ClusterUtil.updateMcIpConfig(allIpMap, enableLabels.contains(Labels.mc.label()), Ipv6Util.handlerIpv6Addr(node.getInternalIp()), node.getName());

        updateSigServer(enableLabels);

        //多节点服务
        DefaultDeployStructureEnumInvoke.services("main")
                .stream()
                .map(Labels::labelOf)
                .filter(Labels::isDistributed)
                .filter(label -> StringUtils.isNotBlank(label.loadblanceKey()))
                .forEach(label -> allIpMap.put(label.loadblanceKey(), getDistributeIp(label.label())));


        logger.info("update all-ip: " + allIpMap);
        deployService.patchConfigMapAllIpForAddData(allIpMap);
        deployService.patchConfigMap(Constants.CONFIGMAP_ALL_MMS,Constants.NAMESPACE_DEFAULT,d->{
            d.putAll(allMms);
        });

        updatePresenceServer(enableLabels);

        // 241220-分区云5.2启用点对点号码池
        updateP2PPoolNumber(enableLabels);
        return this;
    }


    @Override
    protected NodeHandler initDefaultNodeConfig() {
        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setExternalIp(deployMessage.getIp());
        node.setDomain(deployMessage.getIp());
        node.setNginxPort("80");
        node.setNginxSslPort("443");
        node.setReportInternalIp(deployMessage.getIp());
        node.setType("main");

        Map<String, Boolean> labelMap = new HashMap<>();
        DefaultDeployStructureEnumInvoke.services("main").forEach(label -> labelMap.put(label, false));
        node.setLabelMap(labelMap);
        super.node = node;
        return this;
    }

}
