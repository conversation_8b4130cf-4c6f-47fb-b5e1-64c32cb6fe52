package com.xylink.manager.handler.node;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.model.deploy.Node;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.multiregion.MultiRegionConfigService;
import com.xylink.util.Ipv6Util;
import com.xylink.util.SpringBeanUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * webrtc node 配置处理实现
 */
public class WebrtcHandler extends NodeHandler {
    private final static Logger logger = LoggerFactory.getLogger(WebrtcHandler.class);

    @Override
    public NodeHandler configureConfigMap() {
        if (node == null) return this;
        configureDistributeIP(node);

        ConfigMap configMap = deployService.getConfigMapByName(Constants.CONFIGMAP_WEBRTC, Constants.NAMESPACE_DEFAULT);
        Map<String, String> allWebrtcMap = configMap == null ? new HashMap<>() : configMap.getData();

        //node 配置
        allWebrtcMap.put(node.getName() + "-" + NetworkConstants.WEBRTC_INTERNAL_IP, node.getInternalIp());
        allWebrtcMap.put(node.getName() + "-" + NetworkConstants.WEBRTC_PUBLIC_IP, StringUtils.isBlank(node.getExternalIp()) ? node.getInternalIp() : node.getExternalIp());
        allWebrtcMap.put(node.getName() + "-" + NetworkConstants.WEBRTC_DOMAIN_NAME, StringUtils.isBlank(node.getDomain()) ? allWebrtcMap.get(node.getName() + "-" + NetworkConstants.WEBRTC_PUBLIC_IP) : node.getDomain());


        List<String> enableLabels = node.getLabelMap()
                .entrySet()
                .stream()
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        //内网服务地址
        enableLabels.stream().filter(Constants.interIps::containsKey).forEach(label -> {
            if (Constants.unableParseHostServices.contains(label)) {
                allWebrtcMap.put(node.getName() + "-" + Constants.interIps.get(label), Ipv6Util.handlerIpv6Addr(node.getInternalIp()));
            } else {
                allWebrtcMap.put(node.getName() + "-" + Constants.interIps.get(label), node.getInternalIp());
            }
        });

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();

        if (enableLabels.contains(Labels.uaa_mysql.label())) {
            allIpMap.put(NetworkConstants.UAA_DATABASE_IP, node.getInternalIp());
        }
        if (enableLabels.contains(Labels.allocator_server.label())) {
            allIpMap.put(NetworkConstants.ALLOCATION_IP_PORT, getAllocatorIpUrl());
            allIpMap.put(NetworkConstants.ALLOCATOR_IP, node.getInternalIp());
        }

        ConfigMap configMapWebRtcResource = deployService.getConfigMapByName("all-openresty-webrtc", Constants.NAMESPACE_DEFAULT);
        Map<String, String> allWebRtcMap = configMapWebRtcResource.getData();
        allIpMap.put(NetworkConstants.WEBRTC_DOMAIN_NAME, StringUtils.isBlank(node.getDomain()) ? allWebrtcMap.get(node.getName() + "-" + NetworkConstants.WEBRTC_PUBLIC_IP) : node.getDomain());
        allIpMap.put(NetworkConstants.WEBRTC_NGINX_SSL_PORT, StringUtils.isBlank(allWebRtcMap.get("WEBRTC_NGINX_SSL_PORT")) ? "443" : allWebRtcMap.get("WEBRTC_NGINX_SSL_PORT"));

        //webrtc-siggw是有状态的，所以需要将所有webrtc-siggw服务器节点的hostname和ip映射存储起来
        if (enableLabels.contains(Labels.webrtc_siggw.label())
                || enableLabels.contains(Labels.webrtc_siggw_x86.label())
                || enableLabels.contains(Labels.webrtc_siggw_arm.label())) {
            // hostname1:ip1,hostname2:ip2
            String webrtcSigHostnameMapping = allIpMap.get("WEBRTC_SIG_HOSTNAME_IP_MAPPING");
            allIpMap.put("WEBRTC_SIG_HOSTNAME_IP_MAPPING", getWebrtcSigHostname(webrtcSigHostnameMapping));
        }

        //多节点服务
        DefaultDeployStructureEnumInvoke.services(Labels.webrtc.label())
                .stream()
                .map(Labels::labelOf)
                .filter(Labels::isDistributed)
                .filter(label -> StringUtils.isNotBlank(label.loadblanceKey()))
                .forEach(label -> allIpMap.put(label.loadblanceKey(), getDistributeIp(label.label())));

        logger.info("update all-ip: {}", allIpMap);
        logger.info("update all-webrtc: {}", allWebrtcMap);

        deployService.patchConfigMapAllIpForAddData(allIpMap);
        deployService.patchConfigMap(Constants.CONFIGMAP_WEBRTC, Constants.NAMESPACE_DEFAULT, d -> {
            d.putAll(allWebrtcMap);
        });
        if (enableLabels.contains(Labels.openresty_webrtc.label()) ||
                enableLabels.contains(Labels.openresty_webrtc_x86.label()) ||
                enableLabels.contains(Labels.openresty_webrtc_arm.label())
        ) {
            //多区域如果没有配置过webrtc，默认填入第一个创建的webrtc地址
            SpringBeanUtil.getBean(MultiRegionConfigService.class).
                    refreshWebrtcData(allIpMap.get(NetworkConstants.WEBRTC_DOMAIN_NAME), allWebRtcMap.get("WEBRTC_NGINX_SSL_PORT")
                            , allWebRtcMap.get("WEBRTC_NGINX_PORT"));
        }

        return this;

    }

    // 获取所有allocator_server服务ip，拼接
    private String getAllocatorIpUrl() {
        List<Node> nodes = deployService.listNodesByAppLabel("allocatorserver");
        if (CollectionUtils.isEmpty(nodes)) {
            return "http://127.0.0.1:18118";
        }
        List<String> allocatorIps = nodes.stream().map(Node::getIp).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(allocatorIps)) {
            return "http://127.0.0.1:18118";
        }

        StringBuilder allocAddr = new StringBuilder();
        for (String allocatorIp : allocatorIps) {
            allocAddr.append("http://").append(Ipv6Util.getHost(allocatorIp, node.getName())).append(":18118,");
        }
        return allocAddr.substring(0, allocAddr.length() - 1);
    }

    private String getWebrtcSigHostname(String webrtcSigHostnameMapping) {
        String localWebrtcSigHost = node.getName() + ":" + Ipv6Util.handlerIpv6Addr(node.getInternalIp());
        if (StringUtils.isBlank(webrtcSigHostnameMapping)) {
            return localWebrtcSigHost;
        }
        if (!webrtcSigHostnameMapping.contains(",")) {
            String[] webrtcSigHostnames = webrtcSigHostnameMapping.split(":");
            if (webrtcSigHostnames.length != 2) {
                logger.error("webrtcSigHostnameMapping error, {}", webrtcSigHostnameMapping);
                return localWebrtcSigHost;
            }
            if (node.getName().equals(webrtcSigHostnames[0])) {
                return localWebrtcSigHost;
            }
            return webrtcSigHostnameMapping + "," + localWebrtcSigHost;
        }
        String[] webrtcSigHostnames;
        try {
            webrtcSigHostnames = webrtcSigHostnameMapping.split(",");
        } catch (Exception e) {
            logger.error("webrtcSigHostnameMapping error, {}", webrtcSigHostnameMapping);
            return localWebrtcSigHost;
        }
        String newMapping = "";
        boolean change = false;
        for (String webrtcSigHostname : webrtcSigHostnames) {
            String[] split = webrtcSigHostname.split(":");
            if (split.length != 2 && !node.getName().equals(split[0])) {
                logger.error("webrtcSigHostname error, {}", webrtcSigHostname);
                continue;
            }
            if (StringUtils.isNotBlank(newMapping)) {
                newMapping += ",";
            }
            if (node.getName().equals(split[0])) {
                change = true;
                newMapping += localWebrtcSigHost;
            } else {
                newMapping = newMapping + split[0] + ":" + split[1];
            }
        }
        if (!change) {
            if (StringUtils.isBlank(newMapping)) {
                newMapping = localWebrtcSigHost;
            } else {
                newMapping = newMapping + "," + localWebrtcSigHost;
            }
        }
        return newMapping;
    }


    @Override
    protected NodeHandler initDefaultNodeConfig() {

        NodeDto node = new NodeDto();
        node.setName(deployMessage.getHostname());
        node.setInternalIp(deployMessage.getIp());
        node.setExternalIp(deployMessage.getIp());
        node.setDomain(deployMessage.getIp());
        node.setReportInternalIp(deployMessage.getIp());
        String type = deployMessage.getType();
        node.setType(type);

        Map<String, Boolean> labelMap = new HashMap<>();
        DefaultDeployStructureEnumInvoke.services(type).forEach(label -> labelMap.put(label, false));

        node.setLabelMap(labelMap);
        super.node = node;
        return this;
    }

    @Override
    protected NodeHandler afterConfigure() {
        //20231117网关路由优化
        //configureUaaRoute(configureUaaGatewayRoute());
        deployService.deletePodByLabels("app", new String[]{"private-openresty-webrtc", "private-openresty-webrtc-arm"});
        return this;
    }
}
