package com.xylink.manager.handler.node;

import com.xylink.config.NetworkConstants;
import com.xylink.manager.model.em.Labels;

import java.util.Map;

/**
 * @Author: liyang
 * @DateTime: 2024/6/21 5:16 下午
 **/
public class HiveMysqlHandler extends NodeHandler {
    @Override
    protected NodeHandler configureConfigMap() {
        if (node == null) {
            return this;
        }

        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();

        if (node.getLabelMap().containsKey(Labels.hive_mysql.label())) {
            allIpMap.put(NetworkConstants.HIVE_MYSQL_IP, node.getInternalIp());
        }

        deployService.patchConfigMapAllIpForAddData(allIpMap);

        return this;
    }
}
