package com.xylink.manager.rest.dto;

import org.apache.commons.codec.binary.Base64;

import java.nio.charset.StandardCharsets;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/3/23.
 */
public class Mail {

	private String subject;

	private String content;

	private String to;

	private String encode = Encode.NONE.name();

	private enum Encode{
		NONE,BASE64;
	}
	private String from;

	public Mail() {
	}

	public Mail(String subject, String content, String to) {
		this.subject = subject;
		this.content = content;
		this.to = to;
	}

	public String getEncode() {
		return encode;
	}

	public void setEncode(String encode) {
		Encode.valueOf(encode);
		this.encode = encode;
	}

	public String getSubject() {
		return subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public String getContent() {
		if(Encode.valueOf(encode) == Encode.BASE64){
			return new String(Base64.decodeBase64(content), StandardCharsets.UTF_8);
		}
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getTo() {
		return to;
	}

	public void setTo(String to) {
		this.to = to;
	}

	public String getFrom() {
		return from;
	}

	public void setFrom(String from) {
		this.from = from;
	}

	@Override
	public String toString() {
		return "Mail{" +
				"subject='" + getSubject() + '\'' +
				", content='" + getContent() + '\'' +
				", to='" + getTo() + '\'' +
				", from='" + getFrom() + '\'' +
				'}';
	}
}
