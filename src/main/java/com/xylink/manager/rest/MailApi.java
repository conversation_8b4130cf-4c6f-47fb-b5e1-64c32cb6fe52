package com.xylink.manager.rest;


import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.rest.dto.Mail;
import com.xylink.manager.service.MailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@Controller
@RequestMapping("/sms")
public class MailApi {

	Logger logger = LoggerFactory.getLogger(this.getClass());

	@Autowired
	private MailService mailService;

	@PostMapping(value="/mail")
	@ResponseStatus(value= HttpStatus.ACCEPTED)
	public void sendMail(@RequestBody Mail mail){
		logger.info("Get mail for sending:{}",mail);
		try {
			mailService.send(mail);
		} catch (Exception e) {
			logger.info("send mail error",e);
			throw new ServerException(ErrorStatus.MAIL_SEND_ERROR);
		}
	}

	@RequestMapping(value="/async/mail",method = RequestMethod.POST)
	@ResponseStatus(value= HttpStatus.ACCEPTED)
	public void asyncSendMail(@RequestBody Mail mail){
		logger.info("Get mail for async sending:{}",mail);
		try {
			mailService.asyncSend(mail);
		} catch (Exception e) {
			logger.info("async send mail error",e);
			throw new ServerException(ErrorStatus.MAIL_SEND_ERROR);
		}
	}
}
