//package com.xylink.manager.rest.resource;
//
//import com.xylink.config.Constants;
//import com.xylink.manager.controller.dto.NetworkDto;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.context.annotation.Scope;
//import org.springframework.stereotype.Component;
//
//import javax.ws.rs.GET;
//import javax.ws.rs.POST;
//import javax.ws.rs.Path;
//
///**
// * Created by <PERSON><PERSON><PERSON> on 2017/4/27.
// */
//@Component
//@Scope("prototype")
//@Path("/network/config")
//public class NetworkResource {
//    private static final Logger logger = LoggerFactory.getLogger(NetworkResource.class);
//
//    @POST
//    public void configureNetwork(NetworkDto config) {
//    }
//
//    @GET
//    public String hello() {
//        return "hello !";
//    }
//}
