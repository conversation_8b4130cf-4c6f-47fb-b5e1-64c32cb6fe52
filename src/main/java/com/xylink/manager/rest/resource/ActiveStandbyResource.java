package com.xylink.manager.rest.resource;

import com.xylink.config.Constants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.controller.dto.MailConfigDto;
import com.xylink.manager.rest.dto.Mail;
import com.xylink.manager.service.MailService;
import com.xylink.manager.service.MailTemplateService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by haijiao on 2017/4/27.
 */
//@Scope("prototype")
@RestController
@RequestMapping("/activestandby")
public class ActiveStandbyResource {
    private static final Logger logger = LoggerFactory.getLogger(ActiveStandbyResource.class);

    @Autowired
    private MailService mailService;
    @Autowired
    private MailTemplateService mailTemplateService;

    @RequestMapping(value = "",method = RequestMethod.GET)
    public String sendMail() {

        MailConfigDto mailConfigDto = mailService.getMailConfig();
        if(null == mailConfigDto) {
            logger.info("No mail config data, can not send alert.");
            throw new ServerException(ErrorStatus.MAIL_NOT_CONFIG);
        }
        try {
            Mail mail = mailTemplateService.generateActiveStandbyMail();
            if (null == mail){
                throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
            }
            mailService.sendMail(mail, mailConfigDto);
        }catch (Exception e) {
            logger.error("send alert email failed.",e);
            throw new ServerException(ErrorStatus.MAIL_SEND_ERROR);
        }

        return Constants.SUCCESS_RESPONSE;
    }
}
