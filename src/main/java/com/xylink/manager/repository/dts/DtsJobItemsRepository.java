package com.xylink.manager.repository.dts;

import com.xylink.manager.repository.dts.entity.DtsJobItemsEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/10/29 9:01 下午
 */
public interface DtsJobItemsRepository {
    /**
     * save
     *
     * @param dtsJobItemsEntities
     * @param jobId
     */
    void save(List<DtsJobItemsEntity> dtsJobItemsEntities, String jobId);

    /**
     * query by jobId
     *
     * @param jobId
     * @return
     */
    List<DtsJobItemsEntity> queryByJobId(String jobId);

    /**
     * delete by jobId
     *
     * @param jobId
     */
    void deleteByJodId(String jobId);

}
