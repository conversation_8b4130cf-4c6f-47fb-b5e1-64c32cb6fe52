package com.xylink.manager.repository.dts;

import com.xylink.config.Constants;
import com.xylink.manager.repository.core.SimpleK8sRepository;
import com.xylink.manager.repository.dts.entity.DtsJobEntity;
import com.xylink.manager.service.base.K8sService;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/10/29 9:01 下午
 */
@Component
public class K8sDtsJobRepositoryImpl extends SimpleK8sRepository<DtsJobEntity> implements DtsJobRepository {

    public K8sDtsJobRepositoryImpl(K8sService k8sService) {
        super(k8sService, Constants.CONFIGMAP_PRIVATE_DTS_DATA, DtsJobEntity.class);
    }

}
