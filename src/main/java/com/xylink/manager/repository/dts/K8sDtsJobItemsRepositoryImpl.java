package com.xylink.manager.repository.dts;

import com.xylink.config.Constants;
import com.xylink.manager.repository.dts.entity.DtsJobItemsEntity;
import com.xylink.manager.service.base.K8sService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/11/8 10:11 下午
 */
@Component
public class K8sDtsJobItemsRepositoryImpl implements DtsJobItemsRepository {
    @Resource
    private K8sService k8sService;

    @Override
    public void save(List<DtsJobItemsEntity> dtsJobItemsEntities, String jobId) {
        save0(dtsJobItemsEntities, jobId);
    }

    @Override
    public List<DtsJobItemsEntity> queryByJobId(String jobId) {
        return Arrays.stream(EntityUtils.getObjFromStr(getMetadata().get(jobId), DtsJobItemsEntity[].class)).collect(Collectors.toList());
    }

    @Override
    public void deleteByJodId(String jobId) {
        Map<String, String> data = getMetadata();
        data.remove(jobId);
        k8sService.editConfigmap(Constants.CONFIGMAP_PRIVATE_DTS_ITEM_DATA, data);
    }

    private Map<String, String> getMetadata() {
        return k8sService.getConfigmap(Constants.CONFIGMAP_PRIVATE_DTS_ITEM_DATA);
    }

    private void save0(List<DtsJobItemsEntity> all, String jobId) {
        Map<String, String> data = new HashMap<>();
        data.put(jobId, EntityUtils.toJsonStr(all));
        k8sService.editConfigmap(Constants.CONFIGMAP_PRIVATE_DTS_ITEM_DATA, data);
    }
}
