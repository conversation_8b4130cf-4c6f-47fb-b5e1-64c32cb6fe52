package com.xylink.manager.repository.dts;

import com.xylink.config.mapper.JsonMapper;

/**
 * <AUTHOR>
 * @since 2022/11/8 9:41 下午
 */
public abstract class EntityUtils {
    private EntityUtils() {

    }

    public static <T> T getObjFromStr(String jsonStr, Class<T> clazz) {
        return JsonMapper.nonEmptyMapper().fromJson(jsonStr, clazz);
    }

    public static <T> String toJsonStr(T source) {
        return JsonMapper.nonEmptyMapper().toJson(source);
    }
}
