package com.xylink.manager.repository.dts.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022/10/29 9:07 下午
 */
@Data
public class DtsJobEntity implements Serializable {
    private String id;
    private String name;
    private String status;
    private String databaseType;
    /**
     * source
     */
    private String sourceInstanceType;
    private String sourceIp;
    private int sourcePort;
    private String sourceUsername;
    private String sourcePassword;
    private String sourceDatabase;
    /**
     * target
     */
    private String targetInstanceType;
    private String targetIp;
    private int targetPort;
    private String targetUsername;
    private String targetPassword;
    private String targetDatabase;

    private Date startTime;
    private Date endTime;
    private Date createTime;
    private Date updateTime;
}
