package com.xylink.manager.repository.core;

import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.repository.dts.EntityUtils;
import com.xylink.manager.service.base.K8sService;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/11/9 4:11 下午
 */
public class SimpleK8sRepository<T> implements CrudRepository<T, String> {

    protected K8sService k8sService;

    protected String configmap;

    protected Class<T> domainClass;

    public SimpleK8sRepository(K8sService k8sService, String configmap, Class<T> domainClass) {
        Objects.requireNonNull(domainClass);
        this.k8sService = k8sService;
        this.configmap = configmap;
        this.domainClass = domainClass;
    }

    @Override
    public <S extends T> S save(S entity) {
        try {
            Field field = entity.getClass().getDeclaredField("id");
            field.setAccessible(true);
            Object value = field.get(entity);
            if (value instanceof String) {
                persist(entity, String.valueOf(value));
                return entity;
            } else {
                throw new ServerException("Entity:" + entity.getClass().getName() + " id type not String.");
            }
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new ServerException("Entity:" + entity.getClass().getName() + " no id field.", e);
        }
    }

    @Override
    public List<T> findAll() {
        return getMetadata().values().stream().map(item -> EntityUtils.getObjFromStr(item, getDomainClass())).collect(Collectors.toList());
    }

    @Override
    public Optional<T> findOne(String id) {
        return Optional.ofNullable(EntityUtils.getObjFromStr(getMetadata().get(id), getDomainClass()));
    }

    @Override
    public void deleteById(String id) {
        Map<String, String> all = getMetadata();
        all.remove(id);
        k8sService.replaceConfigmap(configmap, all);
    }

    protected Class<T> getDomainClass() {
        return domainClass;
    }

    protected Map<String, String> getMetadata() {
        return k8sService.getConfigmap(configmap);
    }

    protected <S extends T> void persist(S entity, String id) {
        Map<String, String> data = new HashMap<>();
        data.put(id, EntityUtils.toJsonStr(entity));
        k8sService.editConfigmap(configmap, data);
    }
}
