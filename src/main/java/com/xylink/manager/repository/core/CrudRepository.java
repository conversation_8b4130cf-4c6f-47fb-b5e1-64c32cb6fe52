package com.xylink.manager.repository.core;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2022/11/7 9:57 下午
 */
public interface CrudRepository<T, ID> extends Repository<T, ID> {
    /**
     * save
     *
     * @param entity
     * @param <S>
     * @return
     */
    <S extends T> S save(S entity);

    /**
     * find all
     *
     * @return
     */
    List<T> findAll();

    /**
     * find
     *
     * @param id
     * @return
     */
    Optional<T> findOne(ID id);

    /**
     * delete by id
     *
     * @param id
     */
    void deleteById(ID id);
}
