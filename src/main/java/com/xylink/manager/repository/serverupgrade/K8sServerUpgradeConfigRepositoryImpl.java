package com.xylink.manager.repository.serverupgrade;

import com.xylink.config.Constants;
import com.xylink.manager.model.upgrade.ServerUpgradeConfig;
import com.xylink.manager.repository.core.SimpleK8sRepository;
import com.xylink.manager.service.base.K8sService;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/2/27 11:20 AM
 */
@Component
public class K8sServerUpgradeConfigRepositoryImpl extends SimpleK8sRepository<ServerUpgradeConfig> implements ServerUpgradeConfigRepository {

    public K8sServerUpgradeConfigRepositoryImpl(K8sService k8sService) {
        super(k8sService, Constants.CONFIGMAP_SERVER_UPGRADE_CONFIG, ServerUpgradeConfig.class);
    }

}
