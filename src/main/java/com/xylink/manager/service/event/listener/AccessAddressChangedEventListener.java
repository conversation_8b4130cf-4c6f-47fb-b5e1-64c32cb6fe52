package com.xylink.manager.service.event.listener;

import com.xylink.manager.service.appdownload.AppDownloadService;
import com.xylink.manager.service.event.AccessAddressChangedEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2024/4/6 10:59
 */
@Slf4j
@Component
public class AccessAddressChangedEventListener implements ApplicationListener<AccessAddressChangedEvent> {

    @Resource
    private AppDownloadService appDownloadService;

    @Async
    @Override
    public void onApplicationEvent(AccessAddressChangedEvent event) {
        log.info("Changed source:[{}],message:[{}]", event.getSource(), event.getMessage());
        try {
            TimeUnit.SECONDS.sleep(5);
        } catch (InterruptedException ignore) {
        }
        appDownloadService.refreshDefaultUpdateServerAddress();
    }
}
