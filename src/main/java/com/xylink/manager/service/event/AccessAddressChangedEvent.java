package com.xylink.manager.service.event;

import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @since 2024/4/6 10:59
 */
public class AccessAddressChangedEvent extends ApplicationEvent {
    private final String message;

    public AccessAddressChangedEvent(Object source, String message) {
        super(source);
        this.message = message;
    }

    public String getMessage() {
        return message;
    }
}
