package com.xylink.manager.service.event.listener;

import com.xylink.config.Constants;
import com.xylink.config.GmModeEnum;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.event.BuffetConfigChangedEvent;
import com.xylink.manager.service.event.source.BuffetConfigChangeObj;
import com.xylink.manager.service.remote.pivotor.PivotorRemoteClient;
import com.xylink.util.CertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2024/4/6 10:59
 */
@Slf4j
@Component
public class BuffetConfigChangedEventListener implements ApplicationListener<BuffetConfigChangedEvent> {
    @Resource
    private K8sService k8sService;
    @Resource
    private PivotorRemoteClient pivotorRemoteClient;
    private static final String[] NGINX_TYPES_FOR_OPTIONAL_CERT = CertUtil.NGINX_TYPES_FOR_OPTIONAL_GM_CERT;


    @Async
    @Override
    public void onApplicationEvent(BuffetConfigChangedEvent event) {
        BuffetConfigChangeObj config = event.getBuffetConfigChangeObj();
        /*
         * 国密开关改变
         * GM：纯国密 GM_SSL：国密国际兼容 NOT_GM： 国际
         * 1、改变为GM，需要检查是否应用国密证书
         * 2、重启所有支持应用的nginx
         */
        if ("GM_MODE".equals(config.getConfigName())) {
            Map<String, String> data = new HashMap<String, String>() {
                {
                    put("GM_MODE", (String) config.getNewConfigValue());
                }
            };
            k8sService.patchConfigMap(Constants.CONFIGMAP_ALLIP, data);

            try {
                TimeUnit.SECONDS.sleep(3);
            } catch (InterruptedException ignore) {
            }

            boolean gmImage = false;
            if (GmModeEnum.GM.name().equals(data.get("GM_MODE")) || GmModeEnum.GM_SSL.name().equals(data.get("GM_MODE"))) {
                gmImage = true;
            }

            // 修改nginx镜像
            for (String nginx : NGINX_TYPES_FOR_OPTIONAL_CERT) {
                if ("private-openresty-vod".equals(nginx)) {
                    if (gmImage) {
                        k8sService.changeDeploymentKindToGmImage(nginx);
                    } else {
                        k8sService.changeDeploymentKindToCommonImage(nginx);
                    }
                } else {
                    if (gmImage) {
                        k8sService.changeDaemonSetKindToGmImage(nginx);
                    } else {
                        k8sService.changeDaemonSetKindToCommonImage(nginx);
                    }
                }
            }
        }
        //  回调pivotor 清理缓存
        try {
            pivotorRemoteClient.enterpriseConfigChanged();
            log.info("Notify pivotor clear enterprise config.");
        } catch (Exception e) {
            log.error("Notify pivotor clear enterprise config error.", e);
        }
    }
}
