package com.xylink.manager.service.event;

import com.xylink.manager.service.event.source.BuffetConfigChangeObj;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @since 2024/4/6 10:59
 */
public class BuffetConfigChangedEvent extends ApplicationEvent {
    private final BuffetConfigChangeObj buffetConfigChangeObj;

    public BuffetConfigChangedEvent(Object source, BuffetConfigChangeObj buffetConfigChangeObj) {
        super(source);
        this.buffetConfigChangeObj = buffetConfigChangeObj;
    }

    public BuffetConfigChangeObj getBuffetConfigChangeObj() {
        return buffetConfigChangeObj;
    }
}
