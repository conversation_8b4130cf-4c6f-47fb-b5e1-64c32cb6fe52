package com.xylink.manager.service;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.RoleConfig;
import com.xylink.manager.model.LoginInfo;
import com.xylink.manager.model.Menus;
import com.xylink.manager.model.SystemInfo;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.model.em.SystemTypes;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.base.SystemModeConfig;
import com.xylink.manager.service.haproxy.HaproxyService;
import com.xylink.manager.service.nightingale.MonitorN9eService;
import com.xylink.util.JDBCUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class LoginInfoService {
    @Autowired
    private JDBCUtils jdbcUtils;
    @Autowired
    private RoleConfig roles;
    @Autowired
    private IDeployService deployService;
    @Resource
    private K8sService k8sService;
    @Resource
    private MonitorN9eService monitorN9eService;
    @Resource
    private HaproxyService haproxyServcie;

    @Resource
    private ServiceManageService serviceManageService;

    public LoginInfo getMenusByUser(String user) {
        List<Menus> menus = roles.menus().get(user);

        for (Menus temp : menus) {
            if (temp.getChildren().contains("WhiteList") && checkConfigValue()) {
                List<String> menuList = new ArrayList<>(temp.getChildren());
                menuList.remove("WhiteList");
                temp.setChildren(menuList);
            }
        }
        ConfigMap configMap;
        if (SystemModeConfig.isPrivate56()) {
            //5.6 登录依赖private-manager-data，由manager自己创
            configMap = deployService.getConfigMapByName(Constants.CONFIGMAP_PRIVATE_DATA, Constants.NAMESPACE_DEFAULT);
            if (Objects.isNull(configMap)) {
                newDefaultPrivateManageData();
                configMap = deployService.getConfigMapByName(Constants.CONFIGMAP_PRIVATE_DATA, Constants.NAMESPACE_DEFAULT);
            }
        } else {
            configMap = deployService.getConfigMapByNameNotNull(Constants.CONFIGMAP_PRIVATE_DATA, Constants.NAMESPACE_DEFAULT);
        }
        Map<String, String> privateManagerData = configMap.getData();
        String configCenterSwitch = privateManagerData.get("configCenterSwitch");
        if (!k8sService.isNewCms() && !SystemModeConfig.isPrivate56() && !"open".equals(configCenterSwitch)) {
            //移除配置中心
            menus = menus.stream().filter(menu -> !StringUtils.equalsIgnoreCase(menu.getKey(), "configCenter")).collect(Collectors.toList());
        }
        //信创环境 临时开关 隐藏PlatformGreyList页面
        ignoreMenu(privateManagerData, menus);

        if (!nightingaleSwitch()) {
            log.info("the n9e isn't deploy");
            removePages(menus, "develop", "MonitorDetail", "AdvancedQuery");
            removePages(menus, "serverMonitor", "Middleware");
            removePages(menus, "monitorView", "MonitorDashboard");
            removePages(menus, "warningManage", "CurrentWarning", "HistoryWarning", "WarningRule", "WarningConfig", "ShieldRule");
        } else {
            removePages(menus, "monitorView", "ServerWatchList", "MonitorConfig");
            removePages(menus, "warningManage", "WarnDetail", "WarnConfig");
        }

        // haproxy不部署的情况下，不返回该页面
        haproxyMenu(menus);

        //教育菜单隐藏
        eduMenu(menus);

        //5.6菜单逻辑
        menuForPrivate56(menus);

        // cms
        cmsMenu(menus);

        //数据库备份恢复旧版
        removePages(menus, "develop", "Tools", "DBBackup", "Deploy");

        // jaeger调用链菜单才处理
        jaegerMenu(menus);

        return new LoginInfo(user, menus);
    }

    private void newDefaultPrivateManageData() {
        deployService.patchConfigMap(Constants.CONFIGMAP_PRIVATE_DATA, Constants.NAMESPACE_DEFAULT, data -> {
            data.put("diskConfig", "{\"systemdisk1\":\"\",\"systemdisk2\":\"\",\"datadisk\":\"/dev/mapper/vg_xylink-lv_xylink\"}");
            data.put("props", "{\"host\":\"pops.xylink.com\"}");
            data.put("sync_mongodb", "false");
            data.put("isRefreshIptable", "true");
            data.put("ignoreMenu", "DatabaseList");
        });
    }

    private void haproxyMenu(List<Menus> menus) {
        if (haproxyServcie.haproxyIsNotAvailable()) {
            removePages(menus, "baseService", "HaproxyConfigMonitor");
        } else {
            try {
                SystemInfo systemInfo = serviceManageService.loadSystemInfo();
                if (systemInfo == null || (!SystemTypes.master.name().equals(systemInfo.getType()) && !SystemTypes.backup.name().equals(systemInfo.getType()))) {
                    removePages(menus, "baseService", "HaproxyConfigMonitor");
                }
            } catch (Exception e) {
                log.error("Menu:[HaproxyConfigMonitor] query system type is error.", e);
            }
        }
    }

    private void eduMenu(List<Menus> menus) {
        if (!k8sService.getNode("edu").isPresent()) {
            removePages(menus, "develop", "EduConfig");
        }
    }

    private void menuForPrivate56(List<Menus> menus) {
        if (SystemModeConfig.isPrivate56()) {
            removePages(menus, "baseService", "ClusterConfig");
            removePages(menus, "serverManager", "RecoveryConfig");
            removePages(menus, "develop", "DataMigration", "CryptoConfig");
            removePages(menus, "serverMonitor", "DatabaseList");
            if (!k8sService.isDeployed(Constants.POD_NAME_RECORD)) {
                removePages(menus, "serverManager", "StorageConfig");
            }
        } else {
            removePages(menus,"baseService", "DomainManager");
        }
    }

    private void cmsMenu(List<Menus> menus) {
        if (SystemModeConfig.isCmsOrXms()) {
            removePages(menus, "baseService", "ClusterConfig");
            removePages(menus, "serverManager", "RecoveryConfig");
            removePages(menus, "develop", "DataMigration", "CryptoConfig");
            removePages(menus, "serverMonitor", "DatabaseList");
            if (!k8sService.isDeployed(Constants.POD_NAME_RECORD)) {
                removePages(menus, "serverManager", "StorageConfig");
            }
        } else {
            removePages(menus, "serverMonitor", "Oceanbase");
            removePages(menus, "baseService", "TrustedDevice", "ActiveStandbySetting");
        }
    }

    private void jaegerMenu(List<Menus> menus) {
        Map<String, String> configmap = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        String jaegerIP = configmap.get(NetworkConstants.JAEGER_IP);
        if(StringUtils.isBlank(jaegerIP)){
            removePages(menus, "monitorView", "ServiceTracing");
        }

    }

    private static void ignoreMenu(Map<String, String> privateManagerData, List<Menus> menus) {
        String ignoreMenus = StringUtils.isBlank(privateManagerData.get("ignoreMenu")) ? "" : privateManagerData.get("ignoreMenu");
        String[] split = ignoreMenus.split(",");
        for (Menus temp : menus) {
            List<String> menuList = new ArrayList<>(temp.getChildren());
            for (String ignoreMenu : split) {
                if (temp.getChildren().contains(ignoreMenu.trim())) {
                    menuList.remove(ignoreMenu.trim());
                }
            }
            temp.setChildren(menuList);
        }
    }
    private static void removePages(List<Menus> menus, String menuGroup, String... pages) {
        Menus baseServiceMenus = menus.stream().filter(menu -> StringUtils.equalsIgnoreCase(menu.getKey(), menuGroup)).findFirst().orElse(null);
        if (baseServiceMenus != null && pages != null) {
            List<String> children = baseServiceMenus.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                children.removeAll(Arrays.stream(pages).collect(Collectors.toList()));
            }
        }
    }

    /**
     * 检查configValue值
     *
     * @return
     */
    public boolean checkConfigValue() {
        String value = jdbcUtils.getConfigValueByAppSetting();
        return StringUtils.isBlank(value) || "false".equalsIgnoreCase(value);
    }

    public List<Menus> getMenus(String user) {
        return getMenusByUser(user).getAuth();
    }

    private boolean nightingaleSwitch() {
        return monitorN9eService.nightingaleSwitch();
    }
}
