package com.xylink.manager.service;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.exception.basic.ClientErrorException;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.config.exception.basic.ServiceErrorException;
import com.xylink.manager.controller.dto.AudioConfig;
import com.xylink.manager.model.ClientFeatureConfig;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.nginxupload.ClientUploadService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 2023/04/25/11:15
 */
@Service
@Slf4j
public class AudioPseDFModelNcnnPacketService extends ClientUploadService {
    public final static String EXCEPT_FILENAME = "audioPseDFModelNcnnPacket.zip";
    public final static String COPY_FILENAME = "copyaudioPseDFModelNcnnPacket.zip";
    private final static String MD5REPLACE = "md5=";
    private final static String VERSIONREPLACE = "version=";
    @Autowired
    private K8sService k8sService;

    @Autowired
    private ServerListService serverListService;

    @Autowired
    private RestTemplate restTemplate;

    @PostConstruct
    private void loadDir() {
        String dir = StringUtils.isBlank(k8sService.getConfigmap(Constants.CONFIGMAP_PRIVATE_DATA).get("fileUploadDir")) ? "/mnt/xylink/openresty/nginx_main/oss/client" : k8sService.getConfigmap(Constants.CONFIGMAP_PRIVATE_DATA).get("fileUploadDir");
        expectFile = new File(dir + "/" + EXCEPT_FILENAME);
        copyFile = new File(dir + "/" + COPY_FILENAME);
    }

    @Override
    protected void check(MultipartFile file) {
        long maxSizeInBytes = 100L * 1024L * 1024L;
        if (file.getSize() > maxSizeInBytes) {
            throw new ClientErrorException(ErrorStatus.FILE_SIZE_ILLEGAL);
        }
    }

    @Override
    protected void saveFile(MultipartFile file, ClientFeatureConfig config) {
        try {
            if (!expectFile.exists() || renameFile(config)) {
                file.transferTo(expectFile);
            }
        } catch (IOException e) {
            log.error("save file failed", e);
            throw new ServiceErrorException(ErrorStatus.UNEXPECTED_ERROR);
        }
    }

    @Override
    protected void afterSave(MultipartFile file, ClientFeatureConfig config) {
        String md5sum = md5sum();
        String version = System.currentTimeMillis() + "";

        Map<String, String> all = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        String mainDomain = all.get(NetworkConstants.MAIN_DOMAIN_NAME);
        String nginxSslPort = all.get(NetworkConstants.MAIN_NGINX_SSL_PORT);
        String mainSsl = "443".equals(nginxSslPort) ? mainDomain : mainDomain + ":" + nginxSslPort;
        String downloadUrl = "https://" + mainSsl + "/oss/client/" + expectFile.getName() + "?" + MD5REPLACE + md5sum + "&" + VERSIONREPLACE + version;
        config.setValue(downloadUrl);
        updateMessage(config);
    }

    private void updateMessage(ClientFeatureConfig config) {

        try {
            //1、调用pivotor接口 存bwurl
            editClientFeatureConfig(config);

        } catch (ServerException e) {
            rollbackFile();
            log.error("rollback success", e);
            throw e;
        }
    }

    public void editClientFeatureConfig(ClientFeatureConfig config) {
        config.setModelName("mediaConfig");
        config.setConfigName("audioPseDFModelNcnnPacket");
        String mainIp = serverListService.getMainNodeInternalIP();
        String url = "http://" + mainIp + ":11111/api/rest/internal/v1/en/enterprisenemo/profileV2";
        AudioConfig deviceConfig1 = new AudioConfig(config.getConfigName(), config.getValue(), config.getModelName(), "1");
        AudioConfig deviceConfig2 = new AudioConfig(config.getConfigName(), config.getValue(), config.getModelName(), "2");
        AudioConfig deviceConfig5 = new AudioConfig(config.getConfigName(), config.getValue(), config.getModelName(), "5");
        AudioConfig deviceConfig7 = new AudioConfig(config.getConfigName(), config.getValue(), config.getModelName(), "7");
        AudioConfig deviceConfig8 = new AudioConfig(config.getConfigName(), config.getValue(), config.getModelName(), "8");

        Map<String, Object> reqParams = new HashMap<>();
        reqParams.put("enterpriseId", "default_enterprise");
        reqParams.put("configs", Arrays.asList(deviceConfig1, deviceConfig2, deviceConfig5, deviceConfig7, deviceConfig8));

        log.info("Config request:[{}]", reqParams);
        restTemplate.put(url, reqParams);
    }


    @Override
    protected Boolean renameFile(ClientFeatureConfig config) {
        if (!copyFile.exists() || copyFile.delete()) {
            return expectFile.renameTo(copyFile);
        }
        return false;
    }

}
