package com.xylink.manager.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.exception.basic.ClientErrorException;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.controller.dto.GWH323Dto;
import com.xylink.manager.controller.dto.H323ClusterDto;
import com.xylink.config.util.JsonUtil;
import com.xylink.manager.model.*;
import com.xylink.manager.model.deploy.Node;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.base.SystemModeConfig;
import com.xylink.util.Ipv6Util;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Created by liujian on 2017/12/15.
 */
@Service
@EnableScheduling
public class GatewayService {
    private static final Logger logger = LoggerFactory.getLogger(GatewayService.class);

    @Autowired
    private ServerListService serverListService;

    @Autowired
    private K8sService k8sService;

    private static final String default_enterprise = "default_enterprise";
    private static final String H323_TYPE = "H323";
    private static final String IPPBX_TYPE = "PSTNGWM";
    private static final String WEBRTC_TYPE = "WEBRTC";
    private static final String FUSION1NGW_TYPE = "FUSION1NGW";
    private Pattern pattern = Pattern.compile("\\{.*\\}");
    private static HashMap<String, String> cmNameToSNName = new HashMap<>();

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private IDeployService deployService;

    static {
        cmNameToSNName.put(Constants.CONFIGMAP_H323_GATEWAY, "-SN");
        cmNameToSNName.put(Constants.CONFIGMAP_IPPBX_SIGGW, "-IPPBX-SN");
        cmNameToSNName.put(Constants.CONFIGMAP_WEBRTC_SIGGW, "-WEBRTC-SN");
        cmNameToSNName.put(Constants.CONFIGMAP_GATEKEEPER, "-FUSION1NGW-SN");
    }

    public List<GWH323Dto> getGatewayList() {
        List<GWH323Dto> result = new ArrayList<>();

        List<H323GwResponse> allGws = getGwResponses(false);

        result.addAll(getH323GwList(allGws));
        result.addAll(getIppbxGwList());
        result.addAll(getWebrtcGwList(allGws));
        result.addAll(getFusion1nGwList(allGws));
        return result;
    }

    public String deleteGateway(String sn, String type) {
        deleteFromRemote(sn, type);
        deleteFromConfigMap(sn, type);
        return Constants.SUCCESS_RESPONSE;
    }

    private void deleteFromRemote(String sn, String type) {
        String delGwUrl;
        if (IPPBX_TYPE.equals(type)) {
            delGwUrl = "http://" + serverListService.getMainNodeInternalIP() + ":" + NetworkConstants.INTERNAL_PORT + "/api/rest/internal/v1/h323/gwManager/default_enterprise?sn=" + sn;
        } else {
            delGwUrl = "http://" + serverListService.getMainNodeInternalIP() + ":" + NetworkConstants.INTERNAL_PORT + "/api/rest/internal/v1/h323/gws/default_enterprise?sn=" + sn;
        }
        try {
            restTemplate.exchange(delGwUrl, HttpMethod.DELETE, null, String.class);
        } catch (HttpClientErrorException e) {
            logger.error("fail to delete gateway from remote!", e);
            String errorCode = e.getResponseBodyAsString();
            if (errorCode.contains("10008006")) {
                throw new ServerException(ErrorStatus.DELETE_GATEWAY_BIND_ERROR);
            } else {
                throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
            }
        }
    }

    private void deleteFromConfigMap(String sn, String type) {
        try {
            GWH323Dto gwh323Dto = new GWH323Dto();
            gwh323Dto.setSn(sn);
            gwh323Dto.setType(type);
            //删除sn和remark
            deleteSnRemarkFromCM(gwh323Dto);
        } catch (Exception e) {
            logger.error("fail to delete gateway!\n", e);
            throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
        }
    }

    public String saveGateway(GWH323Dto gwh323Dto) {
        List<GWH323Dto> gatewayList = getGatewayList();
        for (GWH323Dto dto : gatewayList) {
            if (dto.getGatewayId().equalsIgnoreCase(gwh323Dto.getSn()) || dto.getSn().equalsIgnoreCase(gwh323Dto.getSn())) {
                throw new ClientErrorException(ErrorStatus.DUPLICATE_GATEWAY_ID);
            }
        }
        if (Objects.isNull(gwh323Dto) || StringUtils.isBlank(gwh323Dto.getType())) {
            throw new ServerException(ErrorStatus.PARAM_ERROR);
        }

        if (StringUtils.isNotBlank(gwh323Dto.getInternalIp())) {
            gwh323Dto.setInternalIp(gwh323Dto.getInternalIp().replaceAll("\\(WEBRTC\\)", "").replaceAll("\\(IPPBX\\)", "").replaceAll("\\(H323\\)", "").replaceAll("\\(FUSION1NGW\\)", ""));
        }

        if (StringUtils.isNotBlank(gwh323Dto.getRemark()) && gwh323Dto.getRemark().length() > 256) {
            throw new ServerException(ErrorStatus.PARAM_ERROR);
        }

        if (H323_TYPE.equals(gwh323Dto.getType())) {
            saveH323Gateway(gwh323Dto);
        } else if (IPPBX_TYPE.equals(gwh323Dto.getType())) {
            saveIppbxGateway(gwh323Dto);
        } else if (WEBRTC_TYPE.equals(gwh323Dto.getType())) {
            saveWebrtcGateway(gwh323Dto);
        } else if (FUSION1NGW_TYPE.equals(gwh323Dto.getType())) {
            saveFusion1ngw(gwh323Dto);
        } else {
            throw new ServerException(ErrorStatus.PARAM_ERROR);
        }
        return Constants.SUCCESS_RESPONSE;
    }

    public List<String> ips() {
        List<String> result = new ArrayList<>();
        result.addAll(serverListService.getPods("private-avc-h323").stream().map(pod -> Ipv6Util.getHost(pod.getIp(), pod.getNodeName()) + "(H323)").collect(Collectors.toList()));
        result.addAll(serverListService.getPods("private-h323-sig").stream().map(pod -> Ipv6Util.getHost(pod.getIp(), pod.getNodeName()) + "(H323)").collect(Collectors.toList()));
        result.addAll(serverListService.getPods("private-h323-sig-arm").stream().map(pod -> Ipv6Util.getHost(pod.getIp(), pod.getNodeName()) + "(H323)").collect(Collectors.toList()));
        result.addAll(serverListService.getPods("private-h323-sig-x86").stream().map(pod -> Ipv6Util.getHost(pod.getIp(), pod.getNodeName()) + "(H323)").collect(Collectors.toList()));
        result.addAll(serverListService.getPods("private-ippbx-siggateway").stream().map(pod -> Ipv6Util.getHost(pod.getIp(), pod.getNodeName()) + "(IPPBX)").collect(Collectors.toList()));
        result.addAll(serverListService.getPods("private-webrtc-siggw").stream().map(pod -> Ipv6Util.getHost(pod.getIp(), pod.getNodeName()) + "(WEBRTC)").collect(Collectors.toList()));
        result.addAll(serverListService.getPods("private-webrtc-siggw-arm").stream().map(pod -> Ipv6Util.getHost(pod.getIp(), pod.getNodeName()) + "(WEBRTC)").collect(Collectors.toList()));
        result.addAll(serverListService.getPods("private-webrtc-siggw-x86").stream().map(pod -> Ipv6Util.getHost(pod.getIp(), pod.getNodeName()) + "(WEBRTC)").collect(Collectors.toList()));
        result.addAll(serverListService.getPods("private-gatekeeper").stream().map(pod -> Ipv6Util.getHost(pod.getIp(), pod.getNodeName()) + "(FUSION1NGW)").collect(Collectors.toList()));
        return result;
    }

    private List<GWH323Dto> getH323GwList(List<H323GwResponse> gwList) {
        List<GWH323Dto> h323Dtos = new ArrayList<>();
        try {
            Map<String, String> configmap = new HashMap<>();
            Map<String, String> h323Configmap = k8sService.getConfigmap(Constants.CONFIGMAP_H323_GATEWAY);
            Map<String, String> avcH323Configmap = k8sService.getConfigmap(Constants.CONFIGMAP_AVC_H323_GATEWAY);
            if (null != h323Configmap) {
                configmap.putAll(h323Configmap);
            }
            if (null != avcH323Configmap) {
                configmap.putAll(avcH323Configmap);
            }
            List<H323GwResponse> h323Gws = getH323GwResponses(gwList);
            h323Gws.forEach(x -> {
                GWH323Dto gwh323Dto = new GWH323Dto();
                gwh323Dto.setMaxInOut(x.getMaxInOut());
                gwh323Dto.setSn(x.getSn());
                gwh323Dto.setType(H323_TYPE);
                gwh323Dto.setGatewayId(x.getNumber());
                gwh323Dto.setInternalIp(x.getIp());
                gwh323Dto.setRemark(x.getAliasName());
                h323Dtos.add(gwh323Dto);
            });
        } catch (Exception e) {
            logger.warn("getH323GwList error", e);
            return Collections.emptyList();
        }
        return h323Dtos;
    }

    private List<GWH323Dto> getWebrtcGwList(List<H323GwResponse> gwList) {
        List<GWH323Dto> h323Dtos = new ArrayList<>();
        try {
            Map<String, String> configmap = k8sService.getConfigmap(Constants.CONFIGMAP_WEBRTC_SIGGW);
            List<H323GwResponse> webrtcGws = getWebrtcGwResponses(gwList);
            webrtcGws.forEach(x -> {
                GWH323Dto gwh323Dto = new GWH323Dto();
                gwh323Dto.setMaxInOut(x.getMaxInOut());
                gwh323Dto.setSn(x.getSn());
                gwh323Dto.setType(WEBRTC_TYPE);
                gwh323Dto.setGatewayId(x.getNumber());
                gwh323Dto.setInternalIp(x.getIp());
                gwh323Dto.setRemark(x.getAliasName());
                h323Dtos.add(gwh323Dto);
            });
        } catch (Exception e) {
            logger.warn("getH323GwList error", e);
            return Collections.emptyList();
        }
        return h323Dtos;
    }

    private List<GWH323Dto> getFusion1nGwList(List<H323GwResponse> gwList) {
        List<GWH323Dto> h323Dtos = new ArrayList<>();
        try {
            Map<String, String> configmap = k8sService.getConfigmap(Constants.CONFIGMAP_GATEKEEPER);
            List<H323GwResponse> fusion1nGws = getFusion1nGwResponses(gwList);
            fusion1nGws.forEach(x -> {
                GWH323Dto gwh323Dto = new GWH323Dto();
                gwh323Dto.setMaxInOut(x.getMaxInOut());
                gwh323Dto.setSn(x.getSn());
                gwh323Dto.setType(FUSION1NGW_TYPE);
                gwh323Dto.setGatewayId(x.getNumber());
                gwh323Dto.setInternalIp(x.getIp());
                gwh323Dto.setRemark(x.getAliasName());
                h323Dtos.add(gwh323Dto);
            });
        } catch (Exception e) {
            logger.warn("getFusion1nGwList error", e);
            return Collections.emptyList();
        }
        return h323Dtos;
    }

    private List<GWH323Dto> getIppbxGwList() {
        List<GWH323Dto> ippbxDtos = new ArrayList<>();
        try {
            Map<String, String> configmap = k8sService.getConfigmap(Constants.CONFIGMAP_IPPBX_SIGGW);
            String getGwUrl = "http://" + serverListService.getMainNodeInternalIP() + ":" + NetworkConstants.INTERNAL_PORT + "/api/rest/internal/v1/h323/gwManager/default_enterprise";
            H323GwResponse[] ippbxGws = restTemplate.getForObject(getGwUrl, H323GwResponse[].class);
            if (Objects.isNull(ippbxGws) || ippbxGws.length <= 0) {
                return Collections.emptyList();
            }
            List<H323GwResponse> ippbxGwList = Arrays.asList(ippbxGws);

            ippbxGwList.stream().filter(res -> "PSTNGWM".equals(res.getType())).forEach(x -> {
                GWH323Dto gwh323Dto = new GWH323Dto();
                gwh323Dto.setMaxInOut(x.getMaxInOut());
                gwh323Dto.setSn(x.getSn());
                gwh323Dto.setType(IPPBX_TYPE);
                gwh323Dto.setGatewayId(x.getNumber());
                if (configmap != null) {
                    configmap.entrySet().stream()
                            .filter(entry -> x.getSn().equals(entry.getValue()) && entry.getKey().endsWith("-IPPBX-SN"))
                            .findFirst()
                            .ifPresent(entry -> {
                                String hostName = entry.getKey().replace("-IPPBX-SN", "");
                                gwh323Dto.setInternalIp(configmap.get(hostName + NetworkConstants.SUFFIX_INTERNAL_IP));
                                gwh323Dto.setRemark(getRemark(configmap, gwh323Dto.getSn(), hostName));
                            });

                    if (StringUtils.isBlank(gwh323Dto.getRemark())) {
                        gwh323Dto.setRemark(getRemark(configmap, gwh323Dto.getSn(), null));
                    }

                }

                ippbxDtos.add(gwh323Dto);
            });
        } catch (Exception e) {
            logger.warn("getIppbxGwList error", e);
            return Collections.emptyList();
        }
        return ippbxDtos;
    }

    private List<H323GwResponse> getGwResponses(boolean excludeAms) {
        String getGwUrl = "http://" + serverListService.getMainNodeInternalIP() + ":" + NetworkConstants.INTERNAL_PORT + "/api/rest/internal/v1/h323/gws/default_enterprise?excludeAms={excludeAms}";
        H323GwResponse[] h323Gws = restTemplate.getForObject(getGwUrl, H323GwResponse[].class, excludeAms);
        if (Objects.isNull(h323Gws) || h323Gws.length <= 0) {
            return Collections.emptyList();
        }
        return Arrays.asList(h323Gws);
    }

    private List<H323GwResponse> getH323GwResponses(List<H323GwResponse> gwList) {
        return gwList.stream().filter(H323GwResponse::h323Gw).collect(Collectors.toList());
    }

    private List<H323GwResponse> getWebrtcGwResponses(List<H323GwResponse> gwList) {
        return gwList.stream().filter(H323GwResponse::webrtcGw).collect(Collectors.toList());
    }

    private List<H323GwResponse> getFusion1nGwResponses(List<H323GwResponse> gwList) {
        return gwList.stream().filter(H323GwResponse::fusion1nGw).collect(Collectors.toList());
    }

    private void saveH323Gateway(GWH323Dto gwh323Dto) {
        try {
            String saveGwUrl = "http://" + serverListService.getMainNodeInternalIP() + ":" + NetworkConstants.INTERNAL_PORT + "/api/rest/internal/v1/h323gw";

            H323GwRequest h323Gw = new H323GwRequest();
            h323Gw.setSn(gwh323Dto.getSn());
            h323Gw.setMaxInCount(gwh323Dto.getMaxInOut());

            h323Gw.setNumber(gwh323Dto.getSn());
            h323Gw.setEnterpriseId(default_enterprise);

            h323Gw.setIp(gwh323Dto.getInternalIp());
            h323Gw.setAliasName(gwh323Dto.getRemark());

            logger.info("save h323Gw: [{}] ", h323Gw);

            ResponseEntity<Void> responseEntity = restTemplate.postForEntity(saveGwUrl, h323Gw, Void.class);
            if (HttpStatus.OK == responseEntity.getStatusCode()) {
                if (StringUtils.isNotBlank(gwh323Dto.getInternalIp())) {
                    try {
                        Map<String, String> configmap = new HashMap<>();
                        Map<String, String> h323Configmap = k8sService.getConfigmap(Constants.CONFIGMAP_H323_GATEWAY);
                        if (null != h323Configmap) {
                            configmap.putAll(h323Configmap);
                        }
                        Map<String, String> avcH323Configmap = k8sService.getConfigmap(Constants.CONFIGMAP_AVC_H323_GATEWAY);
                        if (!CollectionUtils.isEmpty(avcH323Configmap)) {
                            configmap.putAll(avcH323Configmap);
                        }
                        for (Map.Entry<String, String> stringStringEntry : configmap.entrySet()) {
                            if (gwh323Dto.getInternalIp().equals(stringStringEntry.getValue()) && stringStringEntry.getKey().endsWith(NetworkConstants.SUFFIX_INTERNAL_IP)) {
                                String hostName = stringStringEntry.getKey().replace(NetworkConstants.SUFFIX_INTERNAL_IP, "");
                                Node node = deployService.getNodeByName(hostName);
                                if (node != null) {
                                    if (node.getType().contains("h323") && null != h323Configmap) {
                                        h323Configmap.put(hostName + "-SN", gwh323Dto.getSn());
                                        h323Configmap.putAll(buildRemark(gwh323Dto.getRemark(), gwh323Dto.getSn(), hostName));
                                        k8sService.replaceConfigmap(Constants.CONFIGMAP_H323_GATEWAY, h323Configmap);
                                    } else if (node.getType().contains("avc") && avcH323Configmap != null) {
                                        avcH323Configmap.put(hostName + "-SN", gwh323Dto.getSn());
                                        avcH323Configmap.putAll(buildRemark(gwh323Dto.getRemark(), gwh323Dto.getSn(), hostName));
                                        k8sService.replaceConfigmap(Constants.CONFIGMAP_AVC_H323_GATEWAY, avcH323Configmap);
                                    }
                                    break;
                                }
                            }
                        }
                    } catch (Exception e) {
                        throw e;
                    }
                } else {
                    Map<String, String> h323Configmap = k8sService.getConfigmap(Constants.CONFIGMAP_H323_GATEWAY);
                    h323Configmap.putAll(buildRemark(gwh323Dto.getRemark(), gwh323Dto.getSn(), null));
                    k8sService.replaceConfigmap(Constants.CONFIGMAP_H323_GATEWAY, h323Configmap);
                }
            } else {
                String error = "save gw error: " + h323Gw + ", pass rest error! responseEntity:" + responseEntity;
                logger.error(error);
                throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
            }
        } catch (Exception e) {
            logger.error("fail to save gateway!\n", e);
            throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
        }
    }

    private void saveWebrtcGateway(GWH323Dto gwh323Dto) {
        try {
            String saveGwUrl = "http://" + serverListService.getMainNodeInternalIP() + ":" + NetworkConstants.INTERNAL_PORT + "/api/rest/internal/v1/h323gw";

            WebrtcGwRequest webrtcGwRequest = new WebrtcGwRequest();
            webrtcGwRequest.setSn(gwh323Dto.getSn());
            webrtcGwRequest.setMaxInCount(gwh323Dto.getMaxInOut());

            webrtcGwRequest.setNumber(gwh323Dto.getSn());
            webrtcGwRequest.setEnterpriseId(default_enterprise);
            webrtcGwRequest.setGwType(WEBRTC_TYPE);

            webrtcGwRequest.setIp(gwh323Dto.getInternalIp());
            webrtcGwRequest.setAliasName(gwh323Dto.getRemark());


            logger.info("webrtcGw:  " + webrtcGwRequest + ", type: " + gwh323Dto.getType());
            //调用pivotor接口删除sn相关数据
            deleteSnFromPivotor(gwh323Dto.getSn());

            ResponseEntity<Void> responseEntity = restTemplate.postForEntity(saveGwUrl, webrtcGwRequest, Void.class);
            if (HttpStatus.OK == responseEntity.getStatusCode()) {
                if (StringUtils.isNotBlank(gwh323Dto.getInternalIp())) {
                    try {
                        deleteSnRemarkFromCM(gwh323Dto);
                        Map<String, String> cm = k8sService.getConfigmap(Constants.CONFIGMAP_WEBRTC_SIGGW);
                        for (Map.Entry<String, String> stringStringEntry : cm.entrySet()) {
                            if (gwh323Dto.getInternalIp().equals(stringStringEntry.getValue()) && stringStringEntry.getKey().endsWith(NetworkConstants.SUFFIX_INTERNAL_IP)) {
                                String hostName = stringStringEntry.getKey().replace(NetworkConstants.SUFFIX_INTERNAL_IP, "");
                                Node node = deployService.getNodeByName(hostName);
                                boolean nodeContainsWebrtc;
                                if (SystemModeConfig.isPrivate56()) {
                                    nodeContainsWebrtc = nodeContainsWebrtc(node);
                                } else {
                                    nodeContainsWebrtc = node != null && (node.getType().contains("webrtc"));
                                }
                                if (nodeContainsWebrtc) {
                                    cm.put(hostName + "-WEBRTC-SN", gwh323Dto.getSn());
                                    cm.putAll(buildRemark(gwh323Dto.getRemark(), gwh323Dto.getSn(), hostName));
                                    k8sService.replaceConfigmap(Constants.CONFIGMAP_WEBRTC_SIGGW, cm);
                                    break;
                                }
                            }
                        }
                    } catch (Exception e) {
                        throw e;
                    }
                } else {
                    Map<String, String> cm = k8sService.getConfigmap(Constants.CONFIGMAP_WEBRTC_SIGGW);
                    cm.putAll(buildRemark(gwh323Dto.getRemark(), gwh323Dto.getSn(), null));
                    k8sService.replaceConfigmap(Constants.CONFIGMAP_WEBRTC_SIGGW, cm);
                }
            } else {
                String error = "save gw error: " + webrtcGwRequest + ", pass rest error! responseEntity:" + responseEntity;
                logger.error(error);
                throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
            }
        } catch (Exception e) {
            logger.error("fail to save gateway!\n", e);
            throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
        }
    }

    private boolean nodeContainsWebrtc(Node node) {
        if (Objects.isNull(node)) {
            logger.warn("nodeContainsWebrtc: node is null");
            return false;
        }
        Map<String, String> labels = node.getLabels();
        if (Objects.isNull(labels)) {
            logger.warn("nodeContainsWebrtc: not labels");
            return false;
        }
        if (labels.containsKey("webrtc") && "xylink".equals(labels.get("webrtc"))) {
            logger.warn("nodeContainsWebrtc: labels not contains webrtc");
            return true;
        }
        return false;
    }

    private void saveFusion1ngw(GWH323Dto gwh323Dto) {
        try {
            String saveGwUrl = "http://" + serverListService.getMainNodeInternalIP() + ":" + NetworkConstants.INTERNAL_PORT + "/api/rest/bm/internal/gwDevice/v1";

            Fusion1nGwRequest fusion1nGwRequest = new Fusion1nGwRequest();
            fusion1nGwRequest.setSn(gwh323Dto.getSn());
            fusion1nGwRequest.setMaxInCount(gwh323Dto.getMaxInOut());

            fusion1nGwRequest.setNumber(gwh323Dto.getSn());
            fusion1nGwRequest.setEnterpriseId(default_enterprise);
            fusion1nGwRequest.setGwType(FUSION1NGW_TYPE);
            fusion1nGwRequest.setIp(gwh323Dto.getInternalIp());
            fusion1nGwRequest.setAliasName(gwh323Dto.getRemark());


            logger.info("fusion1ngw:  " + fusion1nGwRequest);
            //调用pivotor接口删除sn相关数据
            deleteSnFromPivotor(gwh323Dto.getSn());

            ResponseEntity<Void> responseEntity = restTemplate.postForEntity(saveGwUrl, fusion1nGwRequest, Void.class);
            if (HttpStatus.OK == responseEntity.getStatusCode()) {
                if (StringUtils.isNotBlank(gwh323Dto.getInternalIp())) {
                    try {
                        deleteSnRemarkFromCM(gwh323Dto);
                        Map<String, String> cm = k8sService.getConfigmap(Constants.CONFIGMAP_GATEKEEPER);
                        for (Map.Entry<String, String> stringStringEntry : cm.entrySet()) {
                            if (gwh323Dto.getInternalIp().equals(stringStringEntry.getValue()) && stringStringEntry.getKey().endsWith(NetworkConstants.SUFFIX_INTERNAL_IP)) {
                                String hostName = stringStringEntry.getKey().replace(NetworkConstants.SUFFIX_INTERNAL_IP, "");
                                Node node = deployService.getNodeByName(hostName);
                                if (node != null && (node.getLabels().get("gatekeeper").contains("xylink"))) {
                                    cm.put(hostName + "-FUSION1NGW-SN", gwh323Dto.getSn());
                                    cm.putAll(buildRemark(gwh323Dto.getRemark(), gwh323Dto.getSn(), hostName));
                                    k8sService.replaceConfigmap(Constants.CONFIGMAP_GATEKEEPER, cm);
                                    break;
                                }
                            }
                        }
                    } catch (Exception e) {
                        throw e;
                    }
                } else {
                    Map<String, String> cm = k8sService.getConfigmap(Constants.CONFIGMAP_GATEKEEPER);
                    cm.putAll(buildRemark(gwh323Dto.getRemark(), gwh323Dto.getSn(), null));
                    k8sService.replaceConfigmap(Constants.CONFIGMAP_GATEKEEPER, cm);
                }
            } else {
                String error = "save gw error: " + fusion1nGwRequest + ", pass rest error! responseEntity:" + responseEntity;
                logger.error(error);
                throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
            }
        } catch (Exception e) {
            logger.error("fail to save gateway!\n", e);
            throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
        }
    }

    private void saveIppbxGateway(GWH323Dto gwh323Dto) {
        try {
            String saveGwUrl = "http://" + serverListService.getMainNodeInternalIP() + ":" + NetworkConstants.INTERNAL_PORT + "/api/rest/internal/v1/gateway/manager/create";

            IppbxGwRequest h323Gw = new IppbxGwRequest();
            h323Gw.setSn(gwh323Dto.getSn());
            h323Gw.setMaxInCount(gwh323Dto.getMaxInOut());

            h323Gw.setNumber(gwh323Dto.getSn());
            h323Gw.setEnterpriseId(default_enterprise);
            h323Gw.setType(IPPBX_TYPE);

            logger.info("ippbxGw: [{}] ", h323Gw);
            //调用pivotor接口删除sn相关数据
            deleteSnFromPivotor(gwh323Dto.getSn());

            HttpEntity entity = new HttpEntity<>(h323Gw);
            ResponseEntity<Void> responseEntity = restTemplate.exchange(saveGwUrl, HttpMethod.PUT, entity, Void.class);
            if (HttpStatus.OK == responseEntity.getStatusCode()) {
                if (StringUtils.isNotBlank(gwh323Dto.getInternalIp())) {
                    try {
                        deleteSnRemarkFromCM(gwh323Dto);
                        Map<String, String> cm = k8sService.getConfigmap(Constants.CONFIGMAP_IPPBX_SIGGW);
                        for (Map.Entry<String, String> stringStringEntry : cm.entrySet()) {
                            if (gwh323Dto.getInternalIp().equals(stringStringEntry.getValue()) && stringStringEntry.getKey().endsWith(NetworkConstants.SUFFIX_INTERNAL_IP)) {
                                String hostName = stringStringEntry.getKey().replace(NetworkConstants.SUFFIX_INTERNAL_IP, "");
                                Node node = deployService.getNodeByName(hostName);
                                if (node != null && (node.getType().contains("ippbx"))) {
                                    cm.put(hostName + "-IPPBX-SN", gwh323Dto.getSn());
                                    cm.putAll(buildRemark(gwh323Dto.getRemark(), gwh323Dto.getSn(), hostName));
                                    k8sService.replaceConfigmap(Constants.CONFIGMAP_IPPBX_SIGGW, cm);
                                    break;
                                }
                            }
                        }
                    } catch (Exception e) {
                        throw e;
                    }
                } else {
                    Map<String, String> cm = k8sService.getConfigmap(Constants.CONFIGMAP_IPPBX_SIGGW);
                    cm.putAll(buildRemark(gwh323Dto.getRemark(), gwh323Dto.getSn(), null));
                    k8sService.replaceConfigmap(Constants.CONFIGMAP_IPPBX_SIGGW, cm);
                }
            } else {
                String error = "save gw error: " + h323Gw + ", pass rest error! responseEntity:" + responseEntity;
                logger.error(error);
                throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
            }
        } catch (Exception e) {
            logger.error("fail to save gateway!\n", e);
            throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
        }
    }

    private void deleteSnRemarkFromCM(GWH323Dto gwh323Dto) {
        deleteSnRemarkFromCM(Constants.CONFIGMAP_H323_GATEWAY, gwh323Dto);
        deleteSnRemarkFromCM(Constants.CONFIGMAP_IPPBX_SIGGW, gwh323Dto);
        deleteSnRemarkFromCM(Constants.CONFIGMAP_WEBRTC_SIGGW, gwh323Dto);
        deleteSnRemarkFromCM(Constants.CONFIGMAP_GATEKEEPER, gwh323Dto);
    }

    /**
     * h323融合服务器集群列表
     *
     * @return
     */
    public List<H323ClusterDto> listH323Cluster() {
        List<H323ClusterDto> h323ClusterList = new ArrayList<>();

        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        List<String> list = new ArrayList<>();
        list.add("application/json;charset=UTF-8");
        headers.put("Content-Type", list);

        String listClusterUrl = "http://" + serverListService.getMainNodeInternalIP() + ":" + NetworkConstants.INTERNAL_PORT + "/api/rest/internal/v1/en/gateway/clusters/default_enterprise";
        ResponseEntity<List<H323ClusterDto>> responseEntity = null;
        try {
            responseEntity = restTemplate.exchange(listClusterUrl, HttpMethod.GET, new HttpEntity<>(headers), new ParameterizedTypeReference<List<H323ClusterDto>>() {
            });
            h323ClusterList = responseEntity.getBody();

            if (h323ClusterList == null) {
                return Collections.emptyList();
            }

            //根据number匹配融合服务器id
            List<Map<String, String>> h323ServerList = listH323Server(false, null);

            //查询集群容量信息
            String getClusterCapacity = "http://" + serverListService.getMainNodeInternalIP() + ":" + NetworkConstants.INTERNAL_PORT + "/api/rest/internal/v1/charge/gatewaycluster/get?chargeId=default_enterprise";
            h323ClusterList.forEach(h323Cluster -> {
                List<String> gatewayDeviceNumberList = h323Cluster.getGatewayDeviceNumberList();

                if (!CollectionUtils.isEmpty(gatewayDeviceNumberList) && !CollectionUtils.isEmpty(h323ServerList)) {
                    List<String> gatewayDeviceList = gatewayDeviceNumberList.stream().map(gatewayDeviceNumber -> {
                                Optional<Map<String, String>> mapping = h323ServerList.stream().filter(h323Server -> h323Server.get("number").equals(gatewayDeviceNumber)).findFirst();
                                return mapping.map(stringStringMap -> stringStringMap.get("id")).orElse("");
                            })
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toList());
                    h323Cluster.setGatewayDeviceList(gatewayDeviceList);
                }

                ResponseEntity<JsonNode> response = null;
                try {
                    response = restTemplate.getForEntity(getClusterCapacity + "&clusterId=" + h323Cluster.getClusterId(), JsonNode.class);
                    JsonNode bodyJSON = response.getBody();
                    h323Cluster.setCapacity(bodyJSON == null ? -1 : bodyJSON.get("capacity").asInt());
                } catch (HttpClientErrorException e) {
                    logger.error("Get h323 cluster capacity error. url={}, clusterId={}, response={}", getClusterCapacity, h323Cluster.getClusterId(), JsonUtil.toJson(response));
                }
            });
        } catch (HttpClientErrorException e) {
            logger.error("Get h323 cluster list error. listClusterUrl={}, responseEntity={}", listClusterUrl, JsonUtil.toJson(responseEntity), e);
            dealHttpClientErrorException(e);
        }
        return h323ClusterList;
    }

    /**
     * 查询h323融合服务器列表
     *
     * @param withEnable 是否带"可用"标示
     * @return
     */
    public List<Map<String, String>> listH323Server(Boolean withEnable, Integer clusterId) {
        Map<String, String> h323ServerDeviceNumberClusterNameMap = new HashMap<>();     //融合服务器Sn-集群名称 映射

        if (withEnable) {
            MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
            List<String> list = new ArrayList<>();
            list.add("application/json;charset=UTF-8");
            headers.put("Content-Type", list);
            String listClusterUrl = "http://" + serverListService.getMainNodeInternalIP() + ":" + NetworkConstants.INTERNAL_PORT + "/api/rest/internal/v1/en/gateway/clusters/default_enterprise";
            ResponseEntity<List<H323ClusterDto>> responseEntity = null;
            try {
                responseEntity = restTemplate.exchange(listClusterUrl, HttpMethod.GET, new HttpEntity<>(headers), new ParameterizedTypeReference<List<H323ClusterDto>>() {
                });
                List<H323ClusterDto> h323ClusterList = responseEntity.getBody();
                h323ClusterList.forEach(h323Cluster -> {
                    h323Cluster.getGatewayDeviceNumberList().forEach(gatewayDeviceNum -> {
                        h323ServerDeviceNumberClusterNameMap.put(gatewayDeviceNum, h323Cluster.getClusterName());
                    });
                });
            } catch (HttpClientErrorException e) {
                logger.error("Get h323 cluster list error. listClusterUrl={}, responseEntity={}", listClusterUrl, JsonUtil.toJson(responseEntity), e);
            }
        }
        List<H323GwResponse> h323GwResponseList = getGwResponses(true);
        return h323GwResponseList.stream().filter(h323GwResponse -> "H323".equals(h323GwResponse.getType())).map(h323GwResponse -> {
            Map<String, String> map = new HashMap<>(2);
            map.put("id", h323GwResponse.getId());
            map.put("number", h323GwResponse.getNumber());
            map.put("sn", h323GwResponse.getSn());
            map.put("clusterName", h323ServerDeviceNumberClusterNameMap.get(h323GwResponse.getNumber()));
            return map;
        }).collect(Collectors.toList());
    }

    /**
     * 判断h323融合服务器是否全部加入集群
     *
     * @return
     */
    public Boolean isAddedH323Cluster() {
        List<String> clusterNumberClustered = new ArrayList<>();    //已加入集群的融合服务器number
        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        List<String> list = new ArrayList<>();
        list.add("application/json;charset=UTF-8");
        headers.put("Content-Type", list);
        String listClusterUrl = "http://" + serverListService.getMainNodeInternalIP() + ":" + NetworkConstants.INTERNAL_PORT + "/api/rest/internal/v1/en/gateway/clusters/default_enterprise";
        ResponseEntity<List<H323ClusterDto>> responseEntity = null;
        try {
            responseEntity = restTemplate.exchange(listClusterUrl, HttpMethod.GET, new HttpEntity<>(headers), new ParameterizedTypeReference<List<H323ClusterDto>>() {
            });
            List<H323ClusterDto> h323ClusterList = responseEntity.getBody();
            h323ClusterList.forEach(h323Cluster -> {
                clusterNumberClustered.addAll(h323Cluster.getGatewayDeviceNumberList());
            });
        } catch (HttpClientErrorException e) {
            logger.error("Get h323 cluster list error. listClusterUrl={}, responseEntity={}", listClusterUrl, JsonUtil.toJson(responseEntity), e);
        }
        List<H323GwResponse> h323GwResponseList = getGwResponses(false);
        return h323GwResponseList.stream().filter(h323GwResponse -> "H323".equals(h323GwResponse.getType())).allMatch(h323GwResponse -> clusterNumberClustered.contains(h323GwResponse.getSn()));
    }

    /**
     * 添加或修改h323集群
     *
     * @return
     */
    public Integer updH323Cluster(H323ClusterDto h323Cluster) {
        Integer result = 0;
        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        List<String> list = new ArrayList<>();
        list.add("application/json;charset=UTF-8");
        headers.put("Content-Type", list);

        if (h323Cluster.getClusterId() == null) {   //添加集群
            String addClusterUrl = "http://" + serverListService.getMainNodeInternalIP() + ":" + NetworkConstants.INTERNAL_PORT + "/api/rest/internal/v1/en/gateway/cluster";
            HttpEntity<String> jsonEntity = new HttpEntity<String>(JsonUtil.toJson(h323Cluster), headers);
            ResponseEntity<Integer> responseEntity = null;
            try {
                responseEntity = restTemplate.postForEntity(addClusterUrl, jsonEntity, Integer.class);
                result = responseEntity.getBody();
                h323Cluster.setClusterId(result);
            } catch (HttpClientErrorException e) {
                logger.error("Add h323 cluster error. addClusterUrl={}, h323Cluster={}, responseEntity={}", addClusterUrl, JsonUtil.toJson(h323Cluster), JsonUtil.toJson(responseEntity), e);
                dealHttpClientErrorException(e);
            } catch (Exception e) {
                logger.error("Add h323 cluster error. addClusterUrl={}, h323Cluster={}, responseEntity={}", addClusterUrl, JsonUtil.toJson(h323Cluster), JsonUtil.toJson(responseEntity), e);
                throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
            }
        } else {    //修改集群
            String updClusterUrl = "http://" + serverListService.getMainNodeInternalIP() + ":" + NetworkConstants.INTERNAL_PORT + "/api/rest/internal/v1/en/gateway/cluster/" + h323Cluster.getClusterId();
            HttpEntity<String> jsonEntity = new HttpEntity<String>(JsonUtil.toJson(h323Cluster), headers);
            ResponseEntity<Boolean> responseEntity = null;
            try {
                responseEntity = restTemplate.exchange(updClusterUrl, HttpMethod.PUT, jsonEntity, Boolean.class);
                result = responseEntity.getBody() ? 1 : 0;
            } catch (HttpClientErrorException e) {
                logger.error("Upd h323 cluster error. updClusterUrl={}, h323Cluster={}, responseEntity={}", updClusterUrl, JsonUtil.toJson(h323Cluster), JsonUtil.toJson(responseEntity), e);
                dealHttpClientErrorException(e);
            } catch (Exception e) {
                logger.error("Upd h323 cluster error. updClusterUrl={}, h323Cluster={}, responseEntity={}", updClusterUrl, JsonUtil.toJson(h323Cluster), JsonUtil.toJson(responseEntity), e);
                throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
            }
        }

        //保存集群容量
        if (result != null && result > 0) {
            if (h323Cluster.getCapacity() < 0) {
                //删除集群容量
                String delClusterCapacity = "http://" + serverListService.getMainNodeInternalIP() + ":" + NetworkConstants.INTERNAL_PORT + "/api/rest/internal/v1/charge/gatewaycluster/delete?chargeId=default_enterprise&clusterId=" + h323Cluster.getClusterId();
                try {
                    restTemplate.delete(delClusterCapacity);
                } catch (Exception e) {
                    logger.error("Del H323 cluster capacity error. delClusterCapacity={}", delClusterCapacity, e);
                }
            } else {
                String saveCapacityUrl = "http://" + serverListService.getMainNodeInternalIP() + ":" + NetworkConstants.INTERNAL_PORT + "/api/rest/internal/v1/charge/gatewaycluster/save";
                ObjectNode bodyData = JsonNodeFactory.instance.objectNode();
                bodyData.put("chargeId", "default_enterprise");
                bodyData.put("clusterId", h323Cluster.getClusterId());
                bodyData.put("capacity", h323Cluster.getCapacity());
                HttpEntity<String> jsonEntity = new HttpEntity<String>(JsonUtil.toJson(bodyData), headers);
                try {
                    restTemplate.postForEntity(saveCapacityUrl, jsonEntity, Void.class);
                } catch (Exception e) {
                    logger.error("Save H323 cluster capacity error. saveCapacityUrl={}, bodyData={}", saveCapacityUrl, bodyData.toString(), e);
                }
            }
        }

        return result;
    }

    /**
     * 删除h323集群
     *
     * @param clusterId
     * @return
     */
    public Boolean delH323Cluster(Integer clusterId) {
        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        List<String> list = new ArrayList<>();
        list.add("application/json;charset=UTF-8");
        headers.put("Content-Type", list);

        String delClusterUrl = "http://" + serverListService.getMainNodeInternalIP() + ":" + NetworkConstants.INTERNAL_PORT + "/api/rest/internal/v1/en/gateway/cluster/" + clusterId;
        HttpEntity<String> jsonEntity = new HttpEntity<String>("{}", headers);
        ResponseEntity<Boolean> responseEntity = null;
        try {
            responseEntity = restTemplate.exchange(delClusterUrl, HttpMethod.DELETE, jsonEntity, Boolean.class);
            //删除集群容量
            String delClusterCapacity = "http://" + serverListService.getMainNodeInternalIP() + ":" + NetworkConstants.INTERNAL_PORT + "/api/rest/internal/v1/charge/gatewaycluster/delete?chargeId=default_enterprise&clusterId=" + clusterId;
            try {
                restTemplate.delete(delClusterCapacity);
            } catch (Exception e) {
                logger.error("Del H323 cluster capacity error. delClusterCapacity={}", delClusterCapacity, e);
            }
            return responseEntity.getBody();
        } catch (HttpClientErrorException e) {
            logger.error("Del h323 cluster error. delClusterUrl={}, clusterId={}, responseEntity={}", delClusterUrl, clusterId, JsonUtil.toJson(responseEntity), e);
            dealHttpClientErrorException(e);
        } catch (Exception e) {
            logger.error("Del h323 cluster error. delClusterUrl={}, clusterId={}, responseEntity={}", delClusterUrl, clusterId, JsonUtil.toJson(responseEntity), e);
        }
        return false;
    }

    /**
     * 查询h323集群未限制的容量
     *
     * @return
     */
    public Integer getH323UnlimitedCapacity() {
        String getUnlimitedCapacityUrl = "http://" + serverListService.getMainNodeInternalIP() + ":" + NetworkConstants.INTERNAL_PORT + "/api/rest/internal/v1/charge/gatewaycluster/getUnlimitedCapacity?chargeId=default_enterprise";
        ResponseEntity<Integer> responseEntity = null;
        try {
            responseEntity = restTemplate.getForEntity(getUnlimitedCapacityUrl, Integer.class);
            return responseEntity.getBody();
        } catch (HttpClientErrorException e) {
            logger.error("Get h323 UnlimitedCapacity error. getUnlimitedCapacityUrl={}", getUnlimitedCapacityUrl);
            dealHttpClientErrorException(e);
            return 0;
        }
    }

    /**
     * 处理解析http请求异常信息
     *
     * @param e
     */
    private void dealHttpClientErrorException(HttpClientErrorException e) {
        Matcher matcher = pattern.matcher(e.getMessage());
        matcher.find();
        String group = matcher.group();
        if (StringUtils.isNotBlank(group)) {
            ServerException serverException = JsonUtil.parseJson(group, ServerException.class);
            if (serverException.getErrorCode() == 400027) {
                serverException.setUserMessage("默认集群融合服务器不能为空");
            }
            throw serverException;
        }
    }

    /**
     * @Description: 删除configmap中sn和remark数据
     **/
    private void deleteSnRemarkFromCM(String configmapName, GWH323Dto gwh323Dto) {

        Map<String, String> configMapData = k8sService.getConfigmap(configmapName);

        if (!CollectionUtils.isEmpty(configMapData)) {
            ConcurrentHashMap<String, String> cm = new ConcurrentHashMap<>(configMapData);
            cm.forEach((key, value) -> {
                if (value.equalsIgnoreCase(gwh323Dto.getSn()) && key.endsWith(cmNameToSNName.get(configmapName))) {
                    String[] split = key.split(cmNameToSNName.get(configmapName));
                    String hostName = split[0];
                    cm.remove(hostName + cmNameToSNName.get(configmapName));
                    cm.remove(hostName + "-REMARK");
                }
            });
            cm.remove(gwh323Dto.getSn() + "-REMARK");
            k8sService.replaceConfigmap(configmapName, cm);
        }

    }

    /**
     * @Description: 调用pivotor接口删除sn记录
     **/
    public void deleteSnFromPivotor(String sn) {
        ArrayList<String> ippbxSn = new ArrayList<>();
        ArrayList<String> otherSn = new ArrayList<>();
        List<GWH323Dto> gatewayList = getGatewayList();
        gatewayList.forEach(gwh323Dto -> {
            if (gwh323Dto.getType().equals(IPPBX_TYPE)) {
                ippbxSn.add(gwh323Dto.getSn());
            } else {
                otherSn.add(gwh323Dto.getSn());
            }
        });
        if (ippbxSn.contains(sn)) {
            String ippbxDelGwUrl = "http://" + serverListService.getMainNodeInternalIP() + ":" + NetworkConstants.INTERNAL_PORT + "/api/rest/internal/v1/h323/gwManager/default_enterprise?sn=" + sn;
            restTemplate.exchange(ippbxDelGwUrl, HttpMethod.DELETE, null, Void.class);
        }
        if (otherSn.contains(sn)) {
            String otherDelGwUrl = "http://" + serverListService.getMainNodeInternalIP() + ":" + NetworkConstants.INTERNAL_PORT + "/api/rest/internal/v1/h323/gws/default_enterprise?sn=" + sn;
            restTemplate.exchange(otherDelGwUrl, HttpMethod.DELETE, null, Void.class);
        }
    }

    private Map<String, String> buildRemark(String inputRemark, String sn, String hostname) {
        if (StringUtils.isBlank(sn)) {
            return Collections.emptyMap();
        }
        Map<String, String> remark = new HashMap<>();
        if (StringUtils.isNotBlank(hostname)) {
            remark.put(hostname + "-REMARK", inputRemark);
        }
        remark.put(sn + "-REMARK", inputRemark);
        return remark;
    }

    private String getRemark(Map<String, String> configmap, String sn, String hostname) {
        if (StringUtils.isBlank(sn)) {
            return null;
        }
        String remark = null;
        if (StringUtils.isNotBlank(hostname)) {
            remark = configmap.get(hostname + "-REMARK");
        }
        if (StringUtils.isBlank(remark)) {
            remark = configmap.get(sn + "-REMARK");
        }
        return remark;
    }

    public void syncData() {
        syncH323Gw();
        syncWebrtcGw();
        synFusion1ngw();
    }

    public void syncH323Gw() {
        Map<String, String> h323Cm = k8sService.getConfigmap(Constants.CONFIGMAP_H323_GATEWAY);
        H323GwRequest h323Gw = new H323GwRequest();
        String saveGwUrl = "http://" + serverListService.getMainNodeInternalIP() + ":" + NetworkConstants.INTERNAL_PORT + "/api/rest/internal/v1/h323gw";

        h323Cm.keySet().forEach(key -> {
            try {
                if (key.endsWith("-SN")) {
                    String hostName = key.split("-SN")[0];
                    String sn = h323Cm.get(hostName + "-SN");
                    String ip = h323Cm.get(hostName + NetworkConstants.SUFFIX_INTERNAL_IP);
                    String aliasName = h323Cm.get(hostName + "-REMARK");
                    h323Gw.setSn(sn);
                    h323Gw.setNumber(sn);
                    h323Gw.setEnterpriseId(default_enterprise);
                    h323Gw.setIp(ip);
                    h323Gw.setAliasName(aliasName);
                    logger.info("sync h323Gw:  " + h323Gw);
                    ResponseEntity<Void> responseEntity = restTemplate.postForEntity(saveGwUrl, h323Gw, Void.class);
                    if (HttpStatus.OK != responseEntity.getStatusCode()) {
                        String error = "save gw error: " + h323Gw + ", pass rest error! responseEntity:" + responseEntity;
                        logger.error(error);
                        throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
                    }
                }
            } catch (Exception e) {
                logger.error("fail to save gateway!\n", e);
                throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
            }
        });
    }

    public void syncWebrtcGw() {
        Map<String, String> webrtcCm = k8sService.getConfigmap(Constants.CONFIGMAP_WEBRTC_SIGGW);
        WebrtcGwRequest webrtcGw = new WebrtcGwRequest();
        String saveGwUrl = "http://" + serverListService.getMainNodeInternalIP() + ":" + NetworkConstants.INTERNAL_PORT + "/api/rest/internal/v1/h323gw";

        webrtcCm.keySet().forEach(key -> {
            try {
                if (key.endsWith("-WEBRTC-SN")) {
                    String hostName = key.split("-WEBRTC-SN")[0];
                    String sn = webrtcCm.get(hostName + "-WEBRTC-SN");
                    String ip = webrtcCm.get(hostName + NetworkConstants.SUFFIX_INTERNAL_IP);
                    String aliasName = webrtcCm.get(hostName + "-REMARK");
                    webrtcGw.setSn(sn);
                    webrtcGw.setNumber(sn);
                    webrtcGw.setEnterpriseId(default_enterprise);
                    webrtcGw.setIp(ip);
                    webrtcGw.setAliasName(aliasName);
                    webrtcGw.setGwType(WEBRTC_TYPE);
                    logger.info("sync webrtcGw:  " + webrtcGw);
                    ResponseEntity<Void> responseEntity = restTemplate.postForEntity(saveGwUrl, webrtcGw, Void.class);
                    if (HttpStatus.OK != responseEntity.getStatusCode()) {
                        String error = "save gw error: " + webrtcGw + ", pass rest error! responseEntity:" + responseEntity;
                        logger.error(error);
                        throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
                    }
                }
            } catch (Exception e) {
                logger.error("fail to save gateway!\n", e);
                throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
            }
        });
    }

    public void synFusion1ngw() {
        Map<String, String> fusion1ngw = k8sService.getConfigmap(Constants.CONFIGMAP_GATEKEEPER);
        Fusion1nGwRequest fusion1nGwRequest = new Fusion1nGwRequest();
        String saveGwUrl = "http://" + serverListService.getMainNodeInternalIP() + ":" + NetworkConstants.INTERNAL_PORT + "/api/rest/internal/v1/h323gw";

        fusion1ngw.keySet().forEach(key -> {
            try {
                if (key.endsWith("-FUSION1NGW-SN")) {
                    String hostName = key.split("-FUSION1NGW-SN")[0];
                    String sn = fusion1ngw.get(hostName + "-FUSION1NGW-SN");
                    String ip = fusion1ngw.get(hostName + NetworkConstants.SUFFIX_INTERNAL_IP);
                    String aliasName = fusion1ngw.get(hostName + "-REMARK");
                    fusion1nGwRequest.setSn(sn);
                    fusion1nGwRequest.setNumber(sn);
                    fusion1nGwRequest.setEnterpriseId(default_enterprise);
                    fusion1nGwRequest.setGwType(FUSION1NGW_TYPE);
                    fusion1nGwRequest.setIp(ip);
                    fusion1nGwRequest.setAliasName(aliasName);
                    logger.info("sync fusion1ngw:  " + fusion1nGwRequest);
                    ResponseEntity<Void> responseEntity = restTemplate.postForEntity(saveGwUrl, fusion1nGwRequest, Void.class);
                    if (HttpStatus.OK != responseEntity.getStatusCode()) {
                        String error = "save gw error: " + fusion1nGwRequest + ", pass rest error! responseEntity:" + responseEntity;
                        logger.error(error);
                        throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
                    }
                }
            } catch (Exception e) {
                logger.error("fail to save gateway!\n", e);
                throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
            }
        });
    }


}
