package com.xylink.manager.service.hbase;

import com.xylink.config.NetworkConstants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.config.exception.basic.WebException;
import com.xylink.manager.model.DbBackupFile;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.base.K8sSvcService;
import com.xylink.manager.service.db.HasRunningTaskException;
import com.xylink.manager.service.db.RunningHBaseTask;
import com.xylink.manager.service.db.RunningHBaseTaskHolder;
import com.xylink.util.Ipv6Util;
import com.xylink.util.TaskUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
public class HBaseService {
    private static final Logger logger = LoggerFactory.getLogger(HBaseService.class);

    @javax.annotation.Resource(name = "longtimeRestTemplate")
    private RestTemplate restTemplate;
    @Autowired
    private K8sService k8sService;
    @Autowired
    private K8sSvcService k8sSvcService;
    @Autowired
    private RunningHBaseTaskHolder runningHBaseTaskHolder;

    /**
     * 获取当前的HBase备份任务
     */
    public RunningHBaseTask getCurrentRunningHBaseTask() {
        return runningHBaseTaskHolder.getRunningHBaseTask();
    }

    /**
     * 获取HBase节点IP（判断HBase是否正在运行）
     */
    public String getHBaseNodeIp() {
        return k8sService.getHBaseNodeIp();
    }

    /**
     * 备份HBase
     */
    public RunningHBaseTask backup(String name) {
        if (runningHBaseTaskHolder.attemptToBeginHBaseTask()) {
            try {
                RunningHBaseTask runningHBaseTask = backupHBase(name);
                runningHBaseTaskHolder.setRunningHBaseTask(runningHBaseTask);
                return runningHBaseTask;
            } finally {
                runningHBaseTaskHolder.cancelAttemptToBeginHBaseTask();
            }
        } else {
            logger.warn("There is another HBase task is running...");
            throw new HasRunningTaskException(null);
        }
    }

    /**
     * 上传并还原HBase
     */
    public String uploadAndRestore(MultipartFile file) {
        // 上传文件
        uploadHBaseBackupFile(file);
        // 还原
        return restore(file.getOriginalFilename());
    }

    /**
     * 还原HBase
     */
    public String restore(String restoreFileName) {
        if (!restoreFileName.endsWith(".tar.gz")) {
            throw new WebException(ErrorStatus.FILE_PATH_ILLEGAL);
        }

        if (runningHBaseTaskHolder.attemptToBeginHBaseTask()) {
            try {
                RunningHBaseTask runningHBaseTask = restoreHBase(restoreFileName);
                runningHBaseTaskHolder.setRunningHBaseTask(runningHBaseTask);
                return runningHBaseTask.uuid;
            } finally {
                runningHBaseTaskHolder.cancelAttemptToBeginHBaseTask();
            }
        } else {
            logger.warn("There is another HBase task is running...");
            throw new HasRunningTaskException(null);
        }
    }

    /**
     * 获取HBase备份文件列表
     */
    public List<DbBackupFile> listHBaseBackupFiles() {
        String hBaseNodeIp = k8sService.getHBaseNodeIp();
        if (StringUtils.isBlank(hBaseNodeIp)) {
            return Collections.emptyList();
        }
        String tmpIp = hBaseNodeIp;
        String logAgentIp = k8sSvcService.getLogAgentPodIpByNodeIp(hBaseNodeIp);
        if (StringUtils.isNotBlank(logAgentIp)) {
            tmpIp = logAgentIp;
        }
        String clientVersionUrl = "http://" + Ipv6Util.handlerIpv6Addr(tmpIp) + ":" + NetworkConstants.LOGAGENT_PORT + "/hbase/list";
        logger.info("listHBaseBackupFiles http url : " + clientVersionUrl);
        DbBackupFile[] dbBackupFiles = restTemplate.getForObject(clientVersionUrl, DbBackupFile[].class);
        if (ArrayUtils.isNotEmpty(dbBackupFiles)) {
            return Arrays.asList(dbBackupFiles);
        } else {
            return Collections.emptyList();
        }
    }

    /**
     * 删除HBase备份文件
     */
    public void deleteBackupFile(String fileName) {
        String hBaseNodeIp = k8sService.getHBaseNodeIp();
        if (StringUtils.isBlank(hBaseNodeIp)) {
            return;
        }
        String tmpIp = hBaseNodeIp;
        String logAgentIp = k8sSvcService.getLogAgentPodIpByNodeIp(hBaseNodeIp);
        if (StringUtils.isNotBlank(logAgentIp)) {
            tmpIp = logAgentIp;
        }
        String clientVersionUrl = "http://" + Ipv6Util.handlerIpv6Addr(tmpIp) + ":" + NetworkConstants.LOGAGENT_PORT + "/hbase/delete/" + fileName;
        logger.info("deleteBackupFile http url : " + clientVersionUrl);
        ResponseEntity<Void> responseEntity = restTemplate.exchange(clientVersionUrl, HttpMethod.GET, null, Void.class);
        logger.info("deleteBackupFile status:{}", responseEntity.getStatusCode());
    }

    /**
     * 定时任务调用：删除过期的备份文件(过期时间30天)
     */
    public void batchDeleteBackupFile() {
        String hBaseNodeIp = k8sService.getHBaseNodeIp();
        if (StringUtils.isBlank(hBaseNodeIp)) {
            return;
        }
        String tmpIp = hBaseNodeIp;
        String logAgentIp = k8sSvcService.getLogAgentPodIpByNodeIp(hBaseNodeIp);
        if (StringUtils.isNotBlank(logAgentIp)) {
            tmpIp = logAgentIp;
        }
        String clientVersionUrl = "http://" + Ipv6Util.handlerIpv6Addr(tmpIp) + ":" + NetworkConstants.LOGAGENT_PORT + "/hbase/delete?expiredInterval=30";
        logger.info("batchDeleteBackupFile http url : " + clientVersionUrl);
        ResponseEntity<Void> responseEntity = restTemplate.exchange(clientVersionUrl, HttpMethod.GET, null, Void.class);
        logger.info("batchDeleteBackupFile status:{}", responseEntity.getStatusCode());
    }

    /**
     * 远程下载HBase备份文件
     */
    public boolean downloadBackupFile(HttpServletResponse response, String fileName) {
        String hBaseNodeIp = k8sService.getHBaseNodeIp();
        if (StringUtils.isBlank(hBaseNodeIp)) {
            return Boolean.FALSE;
        }
        String tmpIp = hBaseNodeIp;
        String logAgentIp = k8sSvcService.getLogAgentPodIpByNodeIp(hBaseNodeIp);
        if (StringUtils.isNotBlank(logAgentIp)) {
            tmpIp = logAgentIp;
        }
        String clientVersionUrl = "http://" + Ipv6Util.handlerIpv6Addr(tmpIp) + ":" + NetworkConstants.LOGAGENT_PORT + "/hbase/download/" + fileName;
        logger.info("downloadBackupFile http url : " + clientVersionUrl);
        ResponseEntity<Resource> responseEntity = restTemplate.exchange(clientVersionUrl, HttpMethod.GET, null, Resource.class);
        if (responseEntity.getStatusCode().is2xxSuccessful()) {
            TaskUtils.downloadBackupFile(fileName, responseEntity.getBody(), response);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 调用logagent上传HBase备份文件
     */
    private void uploadHBaseBackupFile(MultipartFile file) {
        String hBaseNodeIp = k8sService.getHBaseNodeIp();
        if (StringUtils.isBlank(hBaseNodeIp)) {
            throw new ServerException("该服务数据库未启动!");
        }
        String tmpIp = hBaseNodeIp;
        String logAgentIp = k8sSvcService.getLogAgentPodIpByNodeIp(hBaseNodeIp);
        if (StringUtils.isNotBlank(logAgentIp)) {
            tmpIp = logAgentIp;
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        MultiValueMap<String, Object> parts = new LinkedMultiValueMap<>();
        parts.add(file.getOriginalFilename(), file.getResource());
        HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(parts, headers);
        String clientVersionUrl = "http://" + Ipv6Util.handlerIpv6Addr(tmpIp) + ":" + NetworkConstants.LOGAGENT_PORT + "/hbase/upload";
        logger.info("uploadHBaseBackupFile http url : " + clientVersionUrl);

        try {
            ResponseEntity<Void> responseEntity = restTemplate.postForEntity(clientVersionUrl, httpEntity, Void.class);
            HttpStatus status = responseEntity.getStatusCode();
            logger.info("uploadHBaseBackupFile status:{}", status);
            if (HttpStatus.INTERNAL_SERVER_ERROR == status || HttpStatus.EXPECTATION_FAILED == status) {
                throw new ServerException("上传文件校验失败!");
            }
            if (!status.is2xxSuccessful()) {
                throw new ServerException("上传文件失败!");
            }
        } catch (Exception e) {
            logger.error("uploadHBaseBackupFile error", e);
            throw new ServerException("上传文件失败!");
        }
    }

    private RunningHBaseTask backupHBase(String name) {
        Optional<Pod> hBasePod = k8sService.getHBasePod();
        if (!hBasePod.isPresent()) {
            return new RunningHBaseTask();
        }
        Pod pod = hBasePod.get();
        String masterIp = k8sService.getConfigmap("all-ip").get(NetworkConstants.MAIN_IP);

        RunningHBaseTask baseTask = new RunningHBaseTask();
        baseTask.newBackupRun(pod, name, masterIp);
        return baseTask;
    }

    private RunningHBaseTask restoreHBase(String restoreFileName) {
        Optional<Pod> hBasePod = k8sService.getHBasePod();
        if (!hBasePod.isPresent()) {
            logger.error("hBasePod is not run");
            return new RunningHBaseTask();
        }
        Pod pod = hBasePod.get();
        RunningHBaseTask baseTask = new RunningHBaseTask();
        baseTask.newRestoreRun(pod, restoreFileName);
        return baseTask;
    }

    public void hBaseTtl(String ttlType, String ttlTime) {
        Optional<Pod> hBasePod = k8sService.getHBasePod();
        if (!hBasePod.isPresent()) {
            return;
        }
        Pod pod = hBasePod.get();
        RunningHBaseTask baseTask = new RunningHBaseTask();
        baseTask.newTtlRun(pod, ttlType, ttlTime);
    }
}
