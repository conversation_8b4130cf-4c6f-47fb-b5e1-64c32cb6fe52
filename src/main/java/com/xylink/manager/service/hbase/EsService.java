package com.xylink.manager.service.hbase;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.WebException;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.db.HasRunningTaskException;
import com.xylink.manager.service.db.RunningEsTask;
import com.xylink.manager.service.db.RunningEsTaskHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class EsService {
    private static final Logger logger = LoggerFactory.getLogger(EsService.class);

    @Autowired
    private K8sService k8sService;
    @Autowired
    private RunningEsTaskHolder runningEsTaskHolder;

    /**
     * 备份es
     */
    public RunningEsTask backup(String name) {
        if (runningEsTaskHolder.attemptToBeginEsTask()) {
            try {
                RunningEsTask runningEsTask = backupEs(name);
                runningEsTaskHolder.setRunningEsTask(runningEsTask);
                return runningEsTask;
            } finally {
                runningEsTaskHolder.cancelAttemptToBeginEsTask();
            }
        } else {
            logger.warn("There is another es backup task is running...");
            throw new HasRunningTaskException(null);
        }
    }

    /**
     * 还原es
     */
    public String restore(String restoreFileName) {
        if (!restoreFileName.endsWith(".tar.gz")) {
            throw new WebException(ErrorStatus.FILE_PATH_ILLEGAL);
        }

        if (runningEsTaskHolder.attemptToBeginEsTask()) {
            try {
                RunningEsTask runningEsTask = restoreEs(restoreFileName);
                runningEsTaskHolder.setRunningEsTask(runningEsTask);
                return runningEsTask.uuid;
            } finally {
                runningEsTaskHolder.cancelAttemptToBeginEsTask();
            }
        } else {
            logger.warn("There is another es restore task is running...");
            throw new HasRunningTaskException(null);
        }
    }

    private RunningEsTask backupEs(String name) {
        Optional<Pod> esPod = k8sService.getEsPod();
        if (!esPod.isPresent()) {
            return new RunningEsTask();
        }
        Pod pod = esPod.get();
        String masterIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP).get(NetworkConstants.MAIN_IP);

        RunningEsTask baseTask = new RunningEsTask();
        baseTask.newBackupRun(pod, name, masterIp);
        return baseTask;
    }

    private RunningEsTask restoreEs(String restoreFileName) {
        Optional<Pod> esPod = k8sService.getEsPod();
        if (!esPod.isPresent()) {
            return new RunningEsTask();
        }
        Pod pod = esPod.get();
        RunningEsTask baseTask = new RunningEsTask();
        baseTask.newRestoreRun(pod, restoreFileName);
        return baseTask;
    }
}
