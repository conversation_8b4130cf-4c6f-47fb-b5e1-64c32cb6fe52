package com.xylink.manager.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xylink.config.Constants;
import com.xylink.config.StatisItem;
import com.xylink.manager.controller.dto.HybridStaticDto;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.controller.dto.kubelet.stats.Filesystem;
import com.xylink.manager.model.KuberDiskInfo;
import com.xylink.manager.model.KuberNodeInfo;
import com.xylink.manager.model.deploy.Node;
import com.xylink.manager.model.em.MemoryUnit;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.manager.service.base.K8sClientBuilder;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.cache.bean.NodeCache;
import com.xylink.manager.service.cache.bean.NodeConditionCache;
import com.xylink.manager.service.cache.bean.QuantityCache;
import com.xylink.manager.service.cache.service.ICacheService;
import com.xylink.manager.service.nightingale.MonitorN9eService;
import com.xylink.manager.service.nightingale.dto.N9eQueryRequestSQL;
import com.xylink.util.MemoryUtil;
import com.xylink.util.SecurityContextUtil;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by liujian on 2017/11/15.
 */
@Service
public class KuberNodeInfoService {
    private final static Logger logger = LoggerFactory.getLogger(KuberNodeInfoService.class);

    private static final String STATS = "/stats/";

    @Value("${k8s.protocol.default.http}")
    private boolean k8sProtocolHttp;
    @Autowired
    private PrivateDataService privateDataService;
    @Autowired
    private ServerListService serverListService;
    @Autowired
    private InfluxDBService influxDBService;
    @Autowired
    private K8sClientBuilder clientBuilder;
    @Autowired
    private K8sService k8sService;
    @Autowired
    private ICacheService cacheService;
    @javax.annotation.Resource
    private MonitorN9eService monitorN9eService;
    @Autowired
    private IDeployService deployService;
    @Autowired
    private ObjectMapper objectMapper;

    private static final ExecutorService executor = new ThreadPoolExecutor(4, 8,
            30L, TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>(10),
            new ThreadFactoryBuilder().setNameFormat("Get_Node_Info-%d").setDaemon(true).build(),
            new ThreadPoolExecutor.CallerRunsPolicy());

    private Map<String, KuberNodeInfo> nodeMap = new HashMap<>();

    //@Scheduled(initialDelay = 10000L)
    public void updateNodeMap() {
        try {
            for (Node node : deployService.listAllNodes()) {
                KuberNodeInfo kuberNodeInfo = generateKuberNodeInfo(node, null, null, true);
                nodeMap.put(node.getName(), kuberNodeInfo);
            }
        } catch (Exception e) {
            logger.error("fail to update node map \n", e);
        }
    }

    private KuberNodeInfo generateKuberNodeInfo(String nodeName) {
        try {
            Node node = deployService.listAllNodes()
                    .stream()
                    .filter(it -> StringUtils.equalsIgnoreCase(it.getName(), nodeName))
                    .findFirst()
                    .get();
            return generateKuberNodeInfo(node, null, null, true);
        } catch (Exception e) {
            logger.error("fail to generate kube node info", e);
        }
        return null;
    }

    public KuberNodeInfo getKuberNodeInfo(NodeCache node, JsonNode diskConfig) {
        return generateKuberNodeInfo(node, diskConfig, true, true);
    }


    public KuberNodeInfo generateKuberNodeInfo(NodeCache node, JsonNode diskConfig, boolean includeDiskInfo, boolean includeStatisItem) {
        KuberNodeInfo kuberNodeInfo = new KuberNodeInfo();
        String name = node.getMetadata().getName();
        kuberNodeInfo.setNodeName(name);
        kuberNodeInfo.setNodeType(node.getMetadata().getLabels().get(Constants.TYPE));

        String cpu = node.getStatus().getAllocatable().get(Constants.CPU).getAmount();
        QuantityCache quantity = node.getStatus().getAllocatable().get(Constants.MEMORY);
        String memory = quantity.getAmount() + quantity.getFormat();
        String ip = node.getStatus().getAddresses().stream().filter(x -> x.getType().equalsIgnoreCase("InternalIP")).collect(Collectors.toList()).get(0).getAddress();
        kuberNodeInfo.setCpuCores(Long.valueOf(cpu));
        kuberNodeInfo.setMemorySize(MemoryUtil.convert(memory, MemoryUnit.KB));
        kuberNodeInfo.setIpAddress(ip);

        String status = node.getStatus().getConditions().stream().filter(x -> x.getType().equalsIgnoreCase(Constants.STATUS_READY)).findFirst().orElse(new NodeConditionCache()).getStatus();
        logger.info(ip + ", Node status: " + status);
        if (includeDiskInfo) {
            kuberNodeInfo.setKuberDiskInfos("True".equalsIgnoreCase(status) ? getNodeFilesystem(ip, diskConfig) : new ArrayList<>());
        } else {
            kuberNodeInfo.setKuberDiskInfos(new ArrayList<>());
        }
        if (includeStatisItem) {
            completeNodeInfo(kuberNodeInfo, null);
        }
        return kuberNodeInfo;
    }

    private void completeNodeInfo(KuberNodeInfo kuberNodeInfo, Map<StatisItem, Map<String, HybridStaticDto>> statisItemMap) {
        long time = new Date().getTime();
        String name = kuberNodeInfo.getNodeName();
        String ip = kuberNodeInfo.getIpAddress();
        try {
            //statisItemMap 全量查询结果，如果无对应node数据，以此为准(多为节点不可达)，不再单独查询统计，避免耗时
            //内存
            boolean needQuery = statisItemMap != null && statisItemMap.get(StatisItem.NODE_MEMORY) != null;

            HybridStaticDto memoryDto = needQuery ? statisItemMap.get(StatisItem.NODE_MEMORY).get(name) : influxDBService.getStaticsByTypeAndNode(StatisItem.NODE_MEMORY, name, Constants.LAST30MIN);
            if (memoryDto != null && null != memoryDto.getSeries() && !memoryDto.getSeries().isEmpty()) {
                Float memoryUsage = (Float) memoryDto.getSeries().get(memoryDto.getSeries().size() - 1);
                kuberNodeInfo.setMemoryUsage(memoryUsage);
            }

            //cpu
            needQuery = statisItemMap != null && statisItemMap.get(StatisItem.NODE_CPU) != null;

            HybridStaticDto cpuDto = needQuery ? statisItemMap.get(StatisItem.NODE_CPU).get(name) : influxDBService.getStaticsByTypeAndNode(StatisItem.NODE_CPU, name, Constants.LAST30MIN);
            if (cpuDto != null && null != cpuDto.getSeries() && !cpuDto.getSeries().isEmpty()) {
                Float cpuUsage = (Float) cpuDto.getSeries().get(cpuDto.getSeries().size() - 1);
                kuberNodeInfo.setCpuUsage(cpuUsage);
            }

            //流量 TX
            needQuery = statisItemMap != null && statisItemMap.get(StatisItem.NODE_NETWORK_TX) != null;

            HybridStaticDto networkTxDto = needQuery ? statisItemMap.get(StatisItem.NODE_NETWORK_TX).get(name) : influxDBService.getStaticsByTypeAndNode(StatisItem.NODE_NETWORK_TX, name, Constants.LAST30MIN);
            if (networkTxDto != null && null != networkTxDto.getSeries() && !networkTxDto.getSeries().isEmpty()) {
                Float networkTx = (Float) networkTxDto.getSeries().get(networkTxDto.getSeries().size() - 1);
                kuberNodeInfo.setNetworkTx(networkTx);
            }

            //流量 RX
            needQuery = statisItemMap != null && statisItemMap.get(StatisItem.NODE_NETWORK_RX) != null;

            HybridStaticDto networkRxDto = needQuery ? statisItemMap.get(StatisItem.NODE_NETWORK_RX).get(name) : influxDBService.getStaticsByTypeAndNode(StatisItem.NODE_NETWORK_RX, name, Constants.LAST30MIN);
            if (networkRxDto != null && null != networkRxDto.getSeries() && !networkRxDto.getSeries().isEmpty()) {
                Float networkRx = (Float) networkRxDto.getSeries().get(networkRxDto.getSeries().size() - 1);
                kuberNodeInfo.setNetworkRx(networkRx);
            }
        } catch (Exception e) {
            logger.error("error\n", e);
        }
        logger.info(ip + " query in influxdb or memory: " + (new Date().getTime() - time));
    }

    public KuberNodeInfo generateKuberNodeInfo(Node node, JsonNode diskConfig, Map<StatisItem, Map<String, HybridStaticDto>> statisItemMap, Boolean includeDisInfo) {
        KuberNodeInfo kuberNodeInfo = new KuberNodeInfo();
        String name = node.getName();
        kuberNodeInfo.setNodeName(name);
        kuberNodeInfo.setNodeType(node.getType());

        String cpu = node.getCpu().getAmount();
        String memory = node.getMemory().getAmount() + node.getMemory().getFormat();
        String ip = node.getIp();
        kuberNodeInfo.setCpuCores(Long.valueOf(cpu));
        kuberNodeInfo.setMemorySize(MemoryUtil.convert(memory, MemoryUnit.KB));
        kuberNodeInfo.setIpAddress(ip);

        String status = node.getReadyStatus();
        logger.info("{}, Node status: {}", ip, status);
        kuberNodeInfo.setStatus(status);
        if (includeDisInfo) {
            kuberNodeInfo.setKuberDiskInfos("True".equalsIgnoreCase(status) ? getNodeFilesystem(kuberNodeInfo, diskConfig) : new ArrayList<>());
        }

        completeNodeInfo(kuberNodeInfo, statisItemMap);

        return kuberNodeInfo;
    }

    /**
     * 查询节点服务器磁盘文件系统
     *
     * @param kuberNodeInfo
     * @param diskConfig
     * @return
     */
    public List<KuberDiskInfo> getNodeFilesystem(KuberNodeInfo kuberNodeInfo, JsonNode diskConfig) {
        List<KuberDiskInfo> kuberDiskInfoList;
        try {
            kuberDiskInfoList = getNodeFilesystem(kuberNodeInfo.getIpAddress(), diskConfig);
        } catch (Exception e) {
            logger.error("Get node file system failed . nodeIp = {} ", kuberNodeInfo.getIpAddress(), e);
            kuberDiskInfoList = getDiskInfoListByInternalIp(kuberNodeInfo, diskConfig);
        }
        return kuberDiskInfoList;
    }

    /**
     * 通过manager服务器列表展示的内网ip再获取一次磁盘信息
     *
     * @param kuberNodeInfo
     * @param diskConfig
     * @return
     */
    public List<KuberDiskInfo> getDiskInfoListByInternalIp(KuberNodeInfo kuberNodeInfo, JsonNode diskConfig) {
        Node node = deployService.getNodeByName(kuberNodeInfo.getNodeName());
        NodeDto nodeDto = serverListService.generateBaseNodeDto(node);

        //k8s的节点ip==节点ip,则不进行探测
        if (StringUtils.isBlank(nodeDto.getInternalIp()) || nodeDto.getInternalIp().equals(kuberNodeInfo.getIpAddress())) {
            return Collections.emptyList();
        }

        try {
            return getNodeFilesystem(nodeDto.getInternalIp(), diskConfig);
        } catch (Exception ex) {
            logger.error("Get node file system failed . nodeIp = {}, internalIp = {} ", kuberNodeInfo.getIpAddress(), nodeDto.getInternalIp(), ex);
        }
        return Collections.emptyList();
    }

    public List<KuberDiskInfo> getNodeFilesystem(String nodeIp, JsonNode diskConfig) {
        if (diskConfig == null) {
            diskConfig = privateDataService.getDiskConfigCache();
        }
        long begin = new Date().getTime();
        String statsUrl = k8sProtocolHttp ? ("http://" + nodeIp + ":10255" + STATS) : k8sService.getNodeProxyPre(nodeIp) + STATS;
        logger.info("statsUrl = {}", statsUrl);

        String resultStr = "";
        ResponseBody responseBody = null;
        try {
            Request request = new Request.Builder().get().url(statsUrl).build();
            Response response = clientBuilder.getHttpClient().newCall(request).execute();
            if (!response.isSuccessful()) {
                logger.error("getNodeFilesystem error,", response);
                return new ArrayList<>();
            }
            responseBody = response.body();
            resultStr = responseBody.string();
            if (StringUtils.isBlank(resultStr)) {
                logger.error("getNodeFilesystem error, result is blank,", response);
                return new ArrayList<>();
            }
        } catch (Exception e) {
            logger.error("statsUrl = {}", statsUrl, e);
            return new ArrayList<>();
        } finally {
            if (responseBody != null) {
                responseBody.close();
            }
        }

        JsonNode rootNode;
        try {
            rootNode = objectMapper.readTree(resultStr);
        } catch (JsonProcessingException e) {
            logger.error("Failed to parse JSON response: {}", resultStr, e);
            return new ArrayList<>();
        }

        ArrayNode fss = (ArrayNode) rootNode.at("/stats/-1/filesystem");

        String systemdisk1 = diskConfig.get("systemdisk1").asText();
        String systemdisk2 = diskConfig.get("systemdisk2").asText();

        List<KuberDiskInfo> kuberDiskInfos = Collections.synchronizedList(new ArrayList<>());
        List<JsonNode> fssList = new ArrayList<>();
        fss.forEach(fssList::add);
        fssList.parallelStream().forEach(fssItem -> {
            try {
                Filesystem filesystem = objectMapper.treeToValue(fssItem, Filesystem.class);
                if (filesystem.getDevice().equalsIgnoreCase("/dev/mapper/vg_xylink-lv_xylink") ||
                        filesystem.getDevice().equalsIgnoreCase(systemdisk1) ||
                        filesystem.getDevice().equalsIgnoreCase(systemdisk2)
                ) {
                    kuberDiskInfos.add(new KuberDiskInfo(filesystem.getDevice(),
                            filesystem.getDevice().equalsIgnoreCase("/dev/mapper/vg_xylink-lv_xylink") ? 1 : 0,
                            filesystem.getCapacity(),
                            filesystem.getUsage(),
                            filesystem.getAvailable()));
                }
            } catch (Exception e) {
                logger.error("parallel stream filesystem disk info failed . fssItem = {} ", fssItem.toString());
            }
        });
        logger.info("url:  " + statsUrl + " , cost " + (new Date().getTime() - begin));
        return kuberDiskInfos;
    }

    public KuberNodeInfo getNodeInfo(String nodeName) {
        KuberNodeInfo nodeInfo = nodeMap.get(nodeName);
        if (null == nodeInfo) {
            nodeInfo = generateKuberNodeInfo(nodeName);
            nodeMap.put(nodeName, nodeInfo);
        }
        return nodeInfo;
    }

    public List<String> getNodeNames() {
        updateNodeMap();
        List<String> nameList = new ArrayList<>();
        nameList.addAll(nodeMap.keySet());
        return nameList;
    }


    public void removeNode(String nodeName) {
        nodeMap.remove(nodeName);
    }

    /**
     * 获取所有node节点的信息，cpu 内存  磁盘 网络
     *
     * @return
     */
    public List<KuberNodeInfo> getNodeInfos() {
        List<KuberNodeInfo> nodes = new ArrayList<>();
        try {
            List<Node> nodeList = deployService.listAllNodes();
            JsonNode diskConfig = privateDataService.getDiskConfigCache();

            Map<StatisItem, Map<String, HybridStaticDto>> statisItemMap = new HashMap<>();
            statisItemMap.put(StatisItem.NODE_CPU, influxDBService.getLatestStaticsByType(StatisItem.NODE_CPU));
            statisItemMap.put(StatisItem.NODE_MEMORY, influxDBService.getLatestStaticsByType(StatisItem.NODE_MEMORY));
            statisItemMap.put(StatisItem.NODE_NETWORK_RX, influxDBService.getLatestStaticsByType(StatisItem.NODE_NETWORK_RX));
            statisItemMap.put(StatisItem.NODE_NETWORK_TX, influxDBService.getLatestStaticsByType(StatisItem.NODE_NETWORK_TX));

            for (Node node : nodeList) {
                nodes.add(generateKuberNodeInfo(node, diskConfig, statisItemMap, true));
            }
        } catch (Exception e) {
            logger.error("fail to get node infos \n", e);
        }
        return nodes;
    }

    public List<KuberNodeInfo> getNodeInfosByParallelStream() {
        List<Node> nodeList = serverListService.getNodeListFilterByUser(SecurityContextUtil.currentUser());
        if (CollectionUtils.isEmpty(nodeList)) {
            return new ArrayList<>(0);
        }
        try {
            JsonNode diskConfig = privateDataService.getDiskConfigCache();
            Map<StatisItem, Map<String, HybridStaticDto>> statisItemMap = getLatestStaticsByType(StatisItem.NODE_CPU, StatisItem.NODE_MEMORY, StatisItem.NODE_NETWORK_RX, StatisItem.NODE_NETWORK_TX);

            return nodeList.parallelStream().map(node -> {
                try {
                    return generateKuberNodeInfo(node, diskConfig, statisItemMap, false);
                } catch (Exception e) {
                    logger.error("parallel stream generateKuberNodeInfo failed . node = {}, disConfig = {}, statisItemMap = {} ",
                            node.toString(), diskConfig.toString(), statisItemMap.toString(), e);
                }
                return null;
            }).filter(Objects::nonNull).collect(Collectors.toList());

        } catch (Exception e) {
            logger.error("fail to get node infos \n", e);
        }
        return new ArrayList<>(0);
    }

    private Map<StatisItem, Map<String, HybridStaticDto>> getLatestStaticsByType(StatisItem... statisItems) {
        Map<StatisItem, Map<String, HybridStaticDto>> statisItemMap = new HashMap<>();
        for (StatisItem statisItem : statisItems) {
            statisItemMap.put(statisItem, influxDBService.getLatestStaticsByType(statisItem));
        }
        return statisItemMap;
    }

    public NodeCache getMainNode() {
        return cacheService.cacheMainNode();
    }

    public KuberNodeInfo generateKuberNodeInfoForInspect(NodeCache node, JsonNode diskConfig, boolean includeDiskInfo, boolean includeStatisItem) {
        KuberNodeInfo kuberNodeInfo = new KuberNodeInfo();
        String name = node.getMetadata().getName();
        kuberNodeInfo.setNodeName(name);
        kuberNodeInfo.setNodeType(node.getMetadata().getLabels().get(Constants.TYPE));

        String cpu = node.getStatus().getAllocatable().get(Constants.CPU).getAmount();
        QuantityCache quantity = node.getStatus().getAllocatable().get(Constants.MEMORY);
        String memory = quantity.getAmount() + quantity.getFormat();
        String ip = node.getStatus().getAddresses().stream().filter(x -> x.getType().equalsIgnoreCase("InternalIP")).collect(Collectors.toList()).get(0).getAddress();
        kuberNodeInfo.setCpuCores(Long.valueOf(cpu));
        kuberNodeInfo.setMemorySize(MemoryUtil.convert(memory, MemoryUnit.KB));
        kuberNodeInfo.setIpAddress(ip);

        String status = node.getStatus().getConditions().stream().filter(x -> x.getType().equalsIgnoreCase(Constants.STATUS_READY)).findFirst().orElse(new NodeConditionCache()).getStatus();
        logger.info(ip + ", Node status: " + status);
        if (includeDiskInfo) {
            kuberNodeInfo.setKuberDiskInfos("True".equalsIgnoreCase(status) ? getNodeFilesystem(ip, diskConfig) : new ArrayList<>());
        } else {
            kuberNodeInfo.setKuberDiskInfos(new ArrayList<>());
        }
        if (includeStatisItem) {
            completeNodeInfoForInspect(kuberNodeInfo);
        }
        return kuberNodeInfo;
    }

    /**
     * 数据来源：夜莺 || influxdb
     *
     * @param kuberNodeInfo
     */
    private void completeNodeInfoForInspect(KuberNodeInfo kuberNodeInfo) {
        long time = new Date().getTime();
        String name = kuberNodeInfo.getNodeName();
        String ip = kuberNodeInfo.getIpAddress();
        if (monitorN9eService.nightingaleSwitch()) {
            queryFromN9E(kuberNodeInfo, name, ip, time);
        } else {
            queryFromInfluxdb(kuberNodeInfo, name, ip, time);
        }
    }

    private void queryFromN9E(KuberNodeInfo kuberNodeInfo, String name, String ip, long time) {
        try {
            String s = name + "-" + ip;
            String query = String.format(N9eQueryRequestSQL.QUERY_NODE_CPU_MEMORY, s, s);
            JsonNode rootNode = monitorN9eService.query(query);
            JsonNode dataNode = rootNode.path("data");
            JsonNode resultNode = dataNode.path("result");

            for (JsonNode item : resultNode) {
                JsonNode metricNode = item.path("metric");
                String metric = metricNode.path("__name__").asText();

                if (N9eQueryRequestSQL.METRIC_MEM_USED_PERCENT.equals(metric)) {
                    JsonNode valueNode = item.path("value");
                    float memoryUsage = Float.parseFloat(valueNode.get(1).asText());
                    kuberNodeInfo.setMemoryUsage(memoryUsage);
                } else if (N9eQueryRequestSQL.METRIC_CPU_USAGE_IDLE.equals(metric)) {
                    JsonNode valueNode = item.path("value");
                    float cpuUsage = 100 - Float.parseFloat(valueNode.get(1).asText());
                    kuberNodeInfo.setCpuUsage(cpuUsage);
                }
            }
            logger.info(ip + " query in n9e take: " + (new Date().getTime() - time));
        } catch (Exception e) {
            logger.error("error\n", e);
        }
    }

    private void queryFromInfluxdb(KuberNodeInfo kuberNodeInfo, String name, String ip, long time) {
        try {
            //内存
            HybridStaticDto memoryDto = influxDBService.getStaticsByTypeAndNode(StatisItem.NODE_MEMORY, name, Constants.LAST30MIN);
            if (memoryDto != null && null != memoryDto.getSeries() && !memoryDto.getSeries().isEmpty()) {
                Float memoryUsage = (Float) memoryDto.getSeries().get(memoryDto.getSeries().size() - 1);
                kuberNodeInfo.setMemoryUsage(memoryUsage);
            }

            //cpu
            HybridStaticDto cpuDto = influxDBService.getStaticsByTypeAndNode(StatisItem.NODE_CPU, name, Constants.LAST30MIN);
            if (cpuDto != null && null != cpuDto.getSeries() && !cpuDto.getSeries().isEmpty()) {
                Float cpuUsage = (Float) cpuDto.getSeries().get(cpuDto.getSeries().size() - 1);
                kuberNodeInfo.setCpuUsage(cpuUsage);
            }
            logger.info("{} query in influxdb take: {}", ip, new Date().getTime() - time);
        } catch (Exception e) {
            logger.error("error\n", e);
        }
    }
}
