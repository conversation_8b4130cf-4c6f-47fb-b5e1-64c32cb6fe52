package com.xylink.manager.service;

import com.xylink.config.exception.basic.ClientErrorException;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.mapper.JsonMapper;
import com.xylink.manager.controller.dto.NewImageDTO;
import com.xylink.manager.controller.vo.image.ImageCheckVO;
import com.xylink.manager.controller.vo.image.ImageVO;
import com.xylink.manager.controller.vo.image.ImageVerifyBO;
import com.xylink.manager.service.cache.service.ImageCodeCache;
import com.xylink.util.ImgUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.UUID;

/**
 * 图片验证码服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/8/25 11:05 上午
 */
@Service
@Slf4j
public class ImageCodeService {

    @Autowired
    private ImageCodeCache imageCodeCache;


    public ImageVO getImage() throws IOException {
        NewImageDTO imageDTO = new ImgUtil().imageResult();
        String rid = UUID.randomUUID().toString();
        imageDTO.setRid(rid);
        ImageVO imageVO = new ImageVO();
        BeanUtils.copyProperties(imageDTO, imageVO);
        JsonMapper jsonMapper = new JsonMapper();
        imageCodeCache.set(rid,jsonMapper.toJson(imageDTO));
        return imageVO;
    }


    public ImageCheckVO check(ImageVerifyBO verifyBO) {
        log.info("image code check param:{}", verifyBO);
        NewImageDTO imageDTO = new JsonMapper().fromJson(imageCodeCache.get(verifyBO.getRid()), NewImageDTO.class);
        if (imageDTO == null) {
            throw new ClientErrorException(ErrorStatus.IMAGE_IS_EXPIRED);
        }

        //缩放后抠图x坐标
        int xPosCache = imageDTO.getXpos() * 3 / 4;
        String rid = imageDTO.getRid();
        //x坐标误差
        int moveCheckError = 6;
        int moveX = verifyBO.getMoveX();
        List<Integer> yMoveList = verifyBO.getMoveYList();

        int sum = 0;
        for (Integer data : yMoveList) {
            sum += data;
        }
        double avg = sum * 1.0 / yMoveList.size();

        double sum2 = 0.0;
        for (Integer data : yMoveList) {
            sum2 += Math.pow(data - avg, 2);
        }
        //y轴移动方差
        double variance = sum2 / yMoveList.size();

        if ((moveX <= (xPosCache + moveCheckError))
                && (moveX >= (xPosCache - moveCheckError))
                && variance != 0
                && rid.equals(verifyBO.getRid())) {
            log.info("image code check success");
            ImageCheckVO imageCheckVO = new ImageCheckVO();
            imageCheckVO.setToken(UUID.randomUUID().toString());
            imageCheckVO.setRid(rid);
            return imageCheckVO;
        }
        imageCodeCache.delete(rid);
        log.warn("image code check failed, xPosCache:{}, variance:{}, rid:{}", xPosCache, variance, rid);
        throw new ClientErrorException(ErrorStatus.IMAGE_CHECK_ERROR);
    }

    public void recordSuccessToken(String rid, String token) {
        imageCodeCache.checkSuccess(rid, token);
    }

    public String getImageToken(String rid) {
        return imageCodeCache.getTokenFromCheckSuccessCache(rid);
    }

    public void delSuccessToken(String rid) {
        imageCodeCache.deleteTokenFromCheckSuccessCache(rid);
    }
}
