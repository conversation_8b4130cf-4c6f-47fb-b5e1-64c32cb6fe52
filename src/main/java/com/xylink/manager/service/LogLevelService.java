package com.xylink.manager.service;

import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.ReadContext;
import com.xylink.config.Constants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.controller.dto.LogLevelDto;
import com.xylink.manager.model.ServiceLogInfo;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/2/12.
 */
@Service
public class LogLevelService {
    private final static Logger logger = LoggerFactory.getLogger(LogLevelService.class);

    private final static String PrivateManager = "PrivateManager";
    private final static String PrivateDating = "PrivateDating";

    private final static String LOCALIP = "127.0.0.1";


    @Autowired
    private RestTemplate restTemplate;

    private static Map<String, ServiceLogInfo> packageMap = new HashMap<>();

    {
        ServiceLogInfo privateLogInfo = generateServiceLogInfo(PrivateManager, LOCALIP, 18028L, "manager/spring", "com.xylink.manager");
        ServiceLogInfo datingLogInfo = generateServiceLogInfo(PrivateDating, LOCALIP, 80L, "meetingSchduler/spring", "com.ainemo.dating");

        packageMap.put(PrivateManager, privateLogInfo);
        packageMap.put(PrivateDating, datingLogInfo);

    }

    private ServiceLogInfo generateServiceLogInfo(String serviceName, String ip, long port, String warName, String packageName) {

        ServiceLogInfo serviceLogInfo = new ServiceLogInfo();
        serviceLogInfo.setServiceName(serviceName);
        serviceLogInfo.setServiceLogLevelUrl("http://"+ ip + ":" + port +"/"+ warName+ "/loggers/"+ packageName);
        logger.info(serviceName + "log level url: " + serviceLogInfo.getServiceLogLevelUrl());

        serviceLogInfo.setPackageName(packageName);

        return serviceLogInfo;


    }


    public List<LogLevelDto> getLogLevelList(){
        List<LogLevelDto> logLevelDtos = new ArrayList<>();
        for(Map.Entry<String, ServiceLogInfo> entry : packageMap.entrySet()){
            LogLevelDto privateDto = new LogLevelDto();
            privateDto.setServiceName(entry.getKey());
            String level = getLogLevel(entry.getKey());
            if(null == level)
                continue;
            privateDto.setLogLevel(level);
            privateDto.setPackageName(entry.getValue().getPackageName());
            logLevelDtos.add(privateDto);
        };

        return logLevelDtos;
    }

    private String getLogLevel(String serviceName){

        ServiceLogInfo serviceLogInfo = packageMap.get(serviceName);
        if(null == serviceLogInfo) {
            return null;
        }
        String packageUrl = serviceLogInfo.getServiceLogLevelUrl();
        try {
            String logLevelStr = restTemplate.getForObject(packageUrl, String.class);
            logger.info("logLevelStr: " + logLevelStr);
            ReadContext ctx = JsonPath.parse(logLevelStr);
            //String level = ctx.read("$.configuredLevel");
            String levels = ctx.read("$.effectiveLevel");
            return levels;
        }catch (Exception e){
            logger.info("serviceName: " + serviceName + ", url: " + packageUrl );
            logger.info(ExceptionUtils.getStackTrace(e));
        }
        return null;

    }

    public String saveLevel(LogLevelDto logLevelDto) {
        ServiceLogInfo serviceLogInfo = packageMap.get(logLevelDto.getServiceName());
        if(null == serviceLogInfo) {
            logger.error("fail to get service log info for " + logLevelDto.getServiceName());
            throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
        }
        return setLogLevel(logLevelDto.getLogLevel(),logLevelDto.getServiceName());

//        if(PrivateManager.equalsIgnoreCase(logLevelDto.getServiceName())){
//            return setLogLevel(logLevelDto.getLogLevel(),PrivateManager);
//        }

        //return Constants.FAIL_RESPONSE;
    }

    private String setLogLevel(String logLevel, String serviceName) {
        logger.info("set " + serviceName +" log level: " + logLevel);
        ServiceLogInfo serviceLogInfo = packageMap.get(serviceName);
        if(null == serviceLogInfo) {
            logger.error("fail to get service log info for " + serviceName);
            throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
        }

        String packageUrl = serviceLogInfo.getServiceLogLevelUrl();
        String logLevelStr ="{\"configuredLevel\":\""+logLevel+"\"}";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<String> requestEntity = new HttpEntity<String>(
                logLevelStr,headers);

        ResponseEntity<Void> responseEntity = restTemplate.postForEntity(packageUrl,requestEntity,Void.class);
        if (HttpStatus.OK == responseEntity.getStatusCode()) {
            return Constants.SUCCESS_RESPONSE;
        } else {
            String error = "save log level failed responseEntity:" + responseEntity;
            logger.error(error);
            throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
        }
    }


}
