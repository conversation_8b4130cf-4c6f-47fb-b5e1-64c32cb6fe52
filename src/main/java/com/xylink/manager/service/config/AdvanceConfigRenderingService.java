package com.xylink.manager.service.config;

import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServiceErrorException;
import com.xylink.manager.model.advanced.AdvanceConfigOption;
import com.xylink.manager.model.advanced.AdvancedConfigItemEnum;
import com.xylink.manager.model.advanced.AdvancedConfigTemplateEnum;
import com.xylink.manager.model.advanced.option.AdvanceConfigOptionStrategyService;
import com.xylink.manager.service.dto.advance.AdvanceConfigTemplateDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 高级配置 前后端交互模版 渲染
 * <AUTHOR>
 * @create 2024/1/16 15:43
 */
@Service
@Slf4j
public class AdvanceConfigRenderingService {

    @Autowired
    private AdvanceConfigOptionStrategyService optionStrategyService;

    public List<AdvanceConfigTemplateDto> services(String label) {

        AdvancedConfigTemplateEnum advancedConfigTemplateEnum = null;

        try {
            advancedConfigTemplateEnum = AdvancedConfigTemplateEnum.valueOf(label.replaceAll("-", "_"));
        } catch (Exception e) {
            return Collections.emptyList();
        }

        List<AdvanceConfigTemplateDto> res = new ArrayList<>();
        for (AdvancedConfigItemEnum temp : advancedConfigTemplateEnum.getItemEnums()) {
            AdvanceConfigTemplateDto dto = AdvanceConfigTemplateDto.transform(temp);

            //如果是选项框，那么选项不能为空
            if (temp.isComplexOptionLogic() && !optionStrategyService.contains(temp.name())) {
                throw new ServiceErrorException(ErrorStatus.ADVANCED_TEMPLATE_OPTION_IS_NULL);
            }

            if (temp.isComplexOptionLogic() && optionStrategyService.contains(temp.name())) {
                List<AdvanceConfigOption> optionList = optionStrategyService.getOptionList(temp.name());
                //如果是选项框，那么选项不能为空
                if (CollectionUtils.isEmpty(optionList)) {
                    throw new ServiceErrorException(ErrorStatus.ADVANCED_TEMPLATE_OPTION_IS_NULL);
                }
                dto.setOptions(optionList);
            }
            res.add(dto);
        }

        return res;
    }
}
