package com.xylink.manager.service.config.strategy;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025-03-28 16:48
 */
@Data
public class ObShellResponseDto implements Serializable {
    private String timestamp;
    private String role;
    private String status;
    private String state;
    private String errorMessage;

    public ObShellResponseDto() {
    }

    public ObShellResponseDto(String timestamp, String role, String status, String state, String errorMessage) {
        this.timestamp = timestamp;
        this.role = role;
        this.status = status;
        this.state = state;
        this.errorMessage = errorMessage;
    }

    public boolean reHa() {
        return StringUtils.isNotBlank(errorMessage) && errorMessage.contains("Prepare:replicaset");
    }
}
