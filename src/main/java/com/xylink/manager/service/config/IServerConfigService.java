package com.xylink.manager.service.config;

import com.xylink.manager.model.SystemDatabaseDto;
import com.xylink.manager.model.SystemDatabaseProperties;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2022/4/6 2:51 下午
 */
public interface IServerConfigService {

    /**
     * 获取备系统数据库配置信息
     *
     * @param extraQueryProperty true:查询数据库variables  false : 不查询
     * @param role               MASTER:主系统  SLAVE : 从系统
     * @return
     */
    List<SystemDatabaseDto> getProperties(boolean extraQueryProperty, String role);

    /**
     * getSystemDatabaseDto
     *
     * @param role
     * @param address
     * @return
     */
    SystemDatabaseDto getSystemDatabaseDto(String role, Pair<String, String> address);

    /**
     * 系统数据库 IP port manager 信息
     */
    SystemDatabaseProperties mainDatabaseBaseInfo();

    /**
     * 设置系统数据库 IP port manager 信息
     *
     * @param systemDatabaseProperties
     */
    void setMainDatabaseBaseInfo(SystemDatabaseProperties systemDatabaseProperties);

    /**
     * 从库--》主库同步
     * 流量在主库时：1、创建主从关系
     * 流量在从库是：1、备份从库数据 2、还原到主库 3、创建主从关系
     */
    void replicationForMaster();

    /**
     * 主--》从库同步
     * 流量在主库时：1、备份主库数据 2、还原到从库 3、创建主从关系
     * 流量在从库是：1、创建主从关系
     */
    void replicationForSlave();

    /**
     * 从--》主库中断
     */
    void noReplicationForMaster();

    /**
     * 主--》从库中断
     */
    void noReplicationForSlave();

    /**
     * 更改mysql master property：readonly
     *
     * @param readonly
     */
    void changeMysqlConfigMapReadonly(String readonly);

    /**
     * 更改mysql slave property：readonly
     *
     * @param readonly
     */
    void changeMysqlSlaveConfigMapReadonly(String readonly);

    /**
     * 主从同步
     *
     * @param masterIp
     * @param masterPort
     * @param slaveIp
     * @param slavePort
     */
    void replication(String masterIp, String masterPort, String slaveIp, String slavePort);

    /**
     * 备份master 主从同步
     *
     * @param masterIp
     * @param masterPort
     * @param slaveIp
     * @param slavePort
     */
    void backUpAndRestoreThenReplication(String masterIp, String masterPort, String slaveIp, String slavePort);

    /**
     * 停止指定IP的主从同步
     *
     * @param ip
     * @param port
     */
    void stopSlave(String ip, String port);

    /**
     * 修改haproxy  mode
     *
     * @param targetMode
     * @param peerSystemManagerAddress
     */
    void editPeerSystemCurrentModeConfig(String targetMode, String peerSystemManagerAddress);

    /**
     * 返回系统相关信息 目前返回数据库类型
     *
     * @return
     */
    Map<String, Object> info();

    /**
     * 创建主从同步 请确定主 备IP
     *
     * @param database 数据库类型
     */
    void replication(String database);

    /**
     * 取消主从同步 请确定主 备IP
     *
     * @param database 数据库类型
     */
    void cancelReplication(String database);

    /**
     * 主备切换 请确定主 备IP
     *
     * @param currentMode
     */
    void failover(String currentMode);

    /**
     * 数据库配置以及属性
     *
     * @return
     */
    SystemDatabaseDto selfProperties();

    /**
     * 取消主从
     *
     * @return
     */
    boolean cancelSelfReplication(String database);

    /**
     * 切换
     *
     * @param database
     * @param currentMode
     */
    void selfFailover(String database, String currentMode);

    /**
     * 建立主从
     *
     * @param database
     */
    void selfReplication(String database, String targetMode);

    /**
     * @param systemDatabaseProperties
     * @return
     */
    Optional<String> peerDatabaseAddress(SystemDatabaseProperties systemDatabaseProperties);


}
