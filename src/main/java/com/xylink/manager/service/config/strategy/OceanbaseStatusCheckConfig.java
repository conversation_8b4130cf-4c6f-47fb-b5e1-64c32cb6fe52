package com.xylink.manager.service.config.strategy;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-03-31 11:28
 */
@Data
public class OceanbaseStatusCheckConfig {

    public static final OceanbaseStatusCheckConfig DEFAULT_START = new OceanbaseStatusCheckConfig(true, 30);
    public static final OceanbaseStatusCheckConfig DEFAULT_STOP = new OceanbaseStatusCheckConfig(false, 0);

    /**
     * 是否自动开启
     */
    private boolean auto;
    /**
     * 巡检周期,注释:单位s
     */
    private long periodOfSecond;

    public OceanbaseStatusCheckConfig(boolean auto, long periodOfSecond) {
        this.auto = auto;
        this.periodOfSecond = periodOfSecond;
    }
}
