package com.xylink.manager.service.config.strategy;

import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.google.common.base.Predicates;
import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.model.SystemDatabaseProperties;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.service.ServerListService;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.manager.service.base.K8sService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <pre>
 *     建立主备：那就主集群执行 ChangeDbToHa.sh $(ob_passwd) $(db_peer_ip) master；从执行 ChangeDbToHa.sh $(ob_passwd) $(db_peer_ip) slave
 *     断开主备：主、从集群都执行 ChangeDbToStandby.sh $(ob_passwd) $(db_peer_ip) master
 *     切换主备：要成为主的：changeDbState.sh $(ob_passwd) $(db_peer_ip) master 要成为从的：changeDbState.sh $(ob_passwd) $(db_peer_ip) slave
 *     查询：getDbstate.sh $(ob_passwd)
 * </pre>
 *
 * <AUTHOR>
 * @since 2025-03-05 16:21
 */
@Slf4j
@Service
public class OceanBaseDatabaseMasterSlaveService {

    @Resource
    private K8sService k8sService;
    @Resource
    private ServerListService serverListService;
    @Resource
    private IDeployService deployService;

    private static final String GETDBSTATE_PATH = "/home/<USER>";
    private static final String CHANGEDBSTATE_PATH = "/home/<USER>";
    private static final String CHANGEDBTOSTANDBY_PATH = "/home/<USER>";
    private static final String CHANGEDBTOHA_PATH = "/home/<USER>";

    /**
     * 重试条件：异常 、false
     * 重试策略：每次间隔3s 重试10次
     */
    Retryer<Boolean> retryer = RetryerBuilder.<Boolean>newBuilder()
            .retryIfException()
            .retryIfResult(Predicates.equalTo(false))
            .withStopStrategy(StopStrategies.stopAfterAttempt(20))
            .withWaitStrategy(WaitStrategies.incrementingWait(0, TimeUnit.SECONDS, 2, TimeUnit.SECONDS))
            .build();

    public ObShellResponseDto getDbStates() {
        Optional<Pod> podOptional = getOceanbasePod();
        if (podOptional.isPresent()) {
            Pod pod = podOptional.get();
            String exec = GETDBSTATE_PATH + " " + getDbBakPassword();
            log.info("dbstate request is : {}", exec);
            String result = deployService.executeCommandForPod(pod.getPodName(), pod.getNamespace(), new String[]{"/bin/bash", "-c", exec});
            log.info("dbstate result is : {}", result);
            return parseGetDbstateScriptOutput(result);
        }
        return null;
    }

    public void changeDbToHa(SystemDatabaseProperties systemDatabaseProperties) {
        Optional<Pod> podOptional = getOceanbasePod();
        if (podOptional.isPresent()) {
            Pod pod = podOptional.get();
            String params = getDbBakPassword() + " " + systemDatabaseProperties.getPeerIp() + " " + systemDatabaseProperties.getTargetMode();
            String exec = CHANGEDBTOHA_PATH + " " + params;
            log.info("changeDbToHa request is : {}", exec);
            String result = deployService.executeCommandForPod(pod.getPodName(), pod.getNamespace(), new String[]{"/bin/bash", "-c", exec});
            log.info("changeDbToHa result is : {}", result);
            ObShellResponseDto response = buildResponse(result);
            if (!"done".equals(response.getState())) {
                try {
                    log.info("==>轮训查看设置同步结果");
                    retryer.call(() -> {
                        ObShellResponseDto dbstate = getDbStates();
                        return "done".equals(dbstate.getState());
                    });
                } catch (Exception e) {
                    throw new ServerException(ErrorStatus.HAPROXY_REPLICATION_ERROR);
                }
            }
        }
    }

    public void changeDbToStandby(SystemDatabaseProperties systemDatabaseProperties) {
        Optional<Pod> podOptional = getOceanbasePod();
        if (podOptional.isPresent()) {
            Pod pod = podOptional.get();
            String params = getDbBakPassword() + " " + systemDatabaseProperties.getPeerIp() + " " + "master";
            String exec = CHANGEDBTOSTANDBY_PATH + " " + params;
            log.info("cancelReplication request is : {}", exec);
            String result = deployService.executeCommandForPod(pod.getPodName(), pod.getNamespace(), new String[]{"/bin/bash", "-c", exec});
            log.info("cancelReplication result is : {}", result);
            ObShellResponseDto response = buildResponse(result);
            if (!"done".equals(response.getState())) {
                try {
                    log.info("==>轮训查看取消同步结果");
                    retryer.call(() -> {
                        ObShellResponseDto dbstate = getDbStates();
                        return "done".equals(dbstate.getState());
                    });
                } catch (Exception e) {
                    throw new ServerException(ErrorStatus.HAPROXY_CANCEL_REPLICATION_ERROR);
                }
            }
        }
    }

    public void changeDbState(SystemDatabaseProperties systemDatabaseProperties) {
        Optional<Pod> podOptional = getOceanbasePod();
        if (podOptional.isPresent()) {
            Pod pod = podOptional.get();
            String params = getDbBakPassword() + " " + systemDatabaseProperties.getPeerIp() + " " + systemDatabaseProperties.getTargetMode();
            String exec = CHANGEDBSTATE_PATH + " " + params;
            log.info("changeDbState request is : {}", exec);
            String result = deployService.executeCommandForPod(pod.getPodName(), pod.getNamespace(), new String[]{"/bin/bash", "-c", exec});
            log.info("changeDbState result is : {}", result);
            ObShellResponseDto response = buildResponse(result);
            if (!"done".equals(response.getState())) {
                try {
                    log.info("==>轮训查看切换结果");
                    retryer.call(() -> {
                        ObShellResponseDto dbstate = getDbStates();
                        return "done".equals(dbstate.getState());
                    });
                } catch (Exception e) {
                    throw new ServerException(ErrorStatus.HAPROXY_DATABASE_FAILOVER_ERROR);
                }
            }

            if (response.reHa()) {
                log.info("==>需要重新创建主备");
                changeDbToHa(systemDatabaseProperties);
            }
        }
    }

    private Optional<Pod> getOceanbasePod() {
        Optional<Pod> pod = k8sService.getPodWithLabelInApp("private-oceanbase");
        if (!pod.isPresent()) {
            log.error("Get private-oceanbase pod is null.");
            return Optional.empty();
        }
        return pod;
    }

    private ObShellResponseDto parseGetDbstateScriptOutput(String scriptOutput) {
        return buildResponse(scriptOutput);
    }

    private ObShellResponseDto buildResponse(String scriptOutput) {
        if (StringUtils.isBlank(scriptOutput)) {
            return new ObShellResponseDto();
        }
        // 按 "|" 分割输出
        String[] parts = scriptOutput.split("\\|");
        // 提取各部分内容
        String timestamp = parts[0].trim();
        String role = parts[1].trim();
        String status = parts[2].trim();
        String state = parts[3].trim();
        String errorMessage = parts[4].trim();
        return new ObShellResponseDto(timestamp, role, status, state, errorMessage);
    }

    public K8sService getK8sService() {
        return k8sService;
    }

    public void setK8sService(K8sService k8sService) {
        this.k8sService = k8sService;
    }

    public ServerListService getServerListService() {
        return serverListService;
    }

    public void setServerListService(ServerListService serverListService) {
        this.serverListService = serverListService;
    }

    private String getDbBakPassword() {
        return serverListService.getUsernameOrPwdFromCM(k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP), NetworkConstants.MAIN_DB_BACKUP_PASSWORD);
    }
}
