package com.xylink.manager.service.config.strategy;

import com.xylink.manager.controller.dto.HaproxyAutoSwitchDto;
import com.xylink.manager.model.SystemDatabaseProperties;
import com.xylink.manager.service.config.IServerConfigService;
import com.xylink.manager.service.haproxy.HaproxyService;
import com.xylink.util.LogBackTraceIdUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Optional;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * OceanbaseStatusCheck 切主
 *
 * <AUTHOR>
 * @since 2023/9/6 10:04 AM
 */
public class OceanbaseStatusCheckTaskManager {
    private static final Logger LOG = LoggerFactory.getLogger(OceanbaseStatusCheckTaskManager.class);
    private ScheduledExecutorService schedule;
    private TaskStatus taskStatus = TaskStatus.NOT_STARTED;
    private OceanbaseStatusCheckConfig oceanbaseStatusCheckConfig;
    private OceanBaseDatabaseMasterSlaveService oceanBaseDatabaseMasterSlaveService;
    private HaproxyService haproxyService;
    private IServerConfigService iServerConfigService;

    public OceanbaseStatusCheckTaskManager(OceanbaseStatusCheckConfig oceanbaseStatusCheckConfig, OceanBaseDatabaseMasterSlaveService oceanBaseDatabaseMasterSlaveService, HaproxyService haproxyService, IServerConfigService iServerConfigService) {
        this.oceanbaseStatusCheckConfig = oceanbaseStatusCheckConfig;
        this.oceanBaseDatabaseMasterSlaveService = oceanBaseDatabaseMasterSlaveService;
        this.haproxyService = haproxyService;
        this.iServerConfigService = iServerConfigService;
    }

    public OceanbaseStatusCheckTaskManager() {
    }

    public enum TaskStatus {
        /**
         * not started
         */
        NOT_STARTED,
        /**
         * started
         */
        STARTED,
        /**
         * completed
         */
        COMPLETED
    }

    public void start() {
        if (oceanbaseStatusCheckConfig == null) {
            LOG.info("Starting Oceanbase status check task with config:null");
            return;
        }

        LOG.info("Starting oceanbase status check task with config:{}", oceanbaseStatusCheckConfig);

        if (oceanbaseStatusCheckConfig.isAuto()) {
            if (TaskStatus.STARTED == taskStatus) {
                shutdown();
            }
            doStart();
        } else {
            if (TaskStatus.STARTED == taskStatus) {
                shutdown();
            }
        }
    }

    public void shutdown() {
        if (schedule != null && TaskStatus.STARTED == taskStatus) {
            LOG.info("Shutting down oceanbase status check task.");
            schedule.shutdown();
            taskStatus = TaskStatus.COMPLETED;
        } else {
            LOG.warn("Oceanbase status check task not started. Ignoring shutdown!");
        }
    }

    private void doStart() {

        schedule = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r);
            thread.setName("thread-oceanbaseStatusCheckTask-1");
            return thread;
        });

        schedule.scheduleAtFixedRate(() -> {
            try {
                // 检查ob状态和系统状态是否一致
                // 不一致 重新建立主备
                LogBackTraceIdUtil.beginLogTrace();
                ObShellResponseDto obShellResponseDto = oceanBaseDatabaseMasterSlaveService.getDbStates();
                if (obShellResponseDto != null) {
                    String obRole = obShellResponseDto.getRole();
                    HaproxyAutoSwitchDto autoSwitchConfig = haproxyService.getAutoSwitchConfig();
                    String currentMode = autoSwitchConfig.getCurrentMode();
                    boolean systemIsBackupMode = systemIsBackupMode();
                    LOG.info("systemIsBackupMode is {},currentMode is {},obRole is {}", systemIsBackupMode, currentMode, obRole);
                    if (autoSwitchConfig.isAutoSwitch()) {
                        String correctingRole = null;
                        if (systemIsBackupMode) {
                            if ("slave".equals(currentMode) && "slave".equalsIgnoreCase(obRole)) {
                                correctingRole = "master";
                            }
                            if ("master".equals(currentMode) && "master".equalsIgnoreCase(obRole)) {
                                correctingRole = "slave";
                            }
                        } else {
                            if ("slave".equals(currentMode) && "master".equalsIgnoreCase(obRole)) {
                                correctingRole = "slave";
                            }
                            if ("master".equals(currentMode) && "slave".equalsIgnoreCase(obRole)) {
                                correctingRole = "master";
                            }
                        }

                        if (StringUtils.isNotBlank(correctingRole)) {
                            SystemDatabaseProperties systemDatabaseProperties = iServerConfigService.mainDatabaseBaseInfo();
                            Optional<String> peerIp = iServerConfigService.peerDatabaseAddress(systemDatabaseProperties);
                            if (peerIp.isPresent()) {
                                systemDatabaseProperties.setTargetMode(correctingRole);
                                systemDatabaseProperties.setPeerIp(peerIp.get());
                                oceanBaseDatabaseMasterSlaveService.changeDbState(systemDatabaseProperties);
                            }
                        }
                    }

                }

            } catch (Exception e) {
                LOG.error("oceanbase status check task execution failed.", e);
            }finally {
                LogBackTraceIdUtil.endLogTrace();
            }

        }, 600, oceanbaseStatusCheckConfig.getPeriodOfSecond(), TimeUnit.SECONDS);

        this.taskStatus = TaskStatus.STARTED;
        LOG.info("Oceanbase status check task running.");
    }


    private boolean systemIsBackupMode() {
        return haproxyService.systemIsBackupMode();
    }

}
