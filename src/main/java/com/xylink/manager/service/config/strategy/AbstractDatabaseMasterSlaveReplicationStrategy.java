package com.xylink.manager.service.config.strategy;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.service.ServerListService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.config.DatabaseMasterSlaveReplicationStrategy;
import com.xylink.manager.service.remote.ocean.OceanRemoteClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @since 2025-03-07 14:17
 */
public abstract class AbstractDatabaseMasterSlaveReplicationStrategy implements DatabaseMasterSlaveReplicationStrategy {
    protected Logger logger = LoggerFactory.getLogger(getClass());

    protected static final String KEY_REPLICA_STATUS = "replica_status";
    protected static final String KEY_REPLICA_STATUS_SLAVE_IO_RUNNING = "Slave_IO_Running";
    protected static final String KEY_REPLICA_STATUS_SLAVE_SQL_RUNNING = "Slave_SQL_Running";
    protected static final String KEY_SUPER_READ_ONLY = "super_read_only";
    protected K8sService k8sService;
    protected ServerListService serverListService;
    protected OceanRemoteClient oceanRemoteClient;

    public AbstractDatabaseMasterSlaveReplicationStrategy(K8sService k8sService, ServerListService serverListService, OceanRemoteClient oceanRemoteClient) {
        this.k8sService = k8sService;
        this.serverListService = serverListService;
        this.oceanRemoteClient = oceanRemoteClient;
    }

    protected String getDbBakPassword() {
        return serverListService.getUsernameOrPwdFromCM(k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP), NetworkConstants.MAIN_DB_BACKUP_PASSWORD);
    }

    protected void mongodbReplication() {
        try {
            oceanRemoteClient.oceanEtlSync();
        } catch (Exception e) {
            logger.error("mongodbReplication error", e);
        }
    }

}
