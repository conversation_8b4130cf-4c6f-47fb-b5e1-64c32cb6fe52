package com.xylink.manager.service.config;

import com.xylink.manager.model.SystemDatabaseProperties;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025-03-05 15:37
 */
public interface DatabaseMasterSlaveReplicationStrategy {
    /**
     * 数据库类型
     *
     * @return
     */
    String getDatabaseName();

    /**
     * 主备切换
     *
     * @return
     */
    boolean failover(SystemDatabaseProperties systemDatabaseProperties);

    /**
     * 主从配置相关属性
     *
     * @return
     */
    Map<String, String> replicationProperties(String ip, String port);

    /**
     * 创建主从同步 请确定主 备IP
     *
     * @param systemDatabaseProperties 主备信息
     */
    void replication(SystemDatabaseProperties systemDatabaseProperties);

    /**
     * 取消主从同步 请确定主 备IP
     *
     * @param systemDatabaseProperties 主备信息
     */
    void cancelReplication(SystemDatabaseProperties systemDatabaseProperties);

}
