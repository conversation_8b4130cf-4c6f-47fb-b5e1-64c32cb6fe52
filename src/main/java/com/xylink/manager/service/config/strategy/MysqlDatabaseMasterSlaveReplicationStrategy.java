package com.xylink.manager.service.config.strategy;

import com.xylink.config.MysqlConstants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.controller.dto.alert.AlertApiReq;
import com.xylink.manager.model.SystemDatabaseDto;
import com.xylink.manager.model.SystemDatabaseProperties;
import com.xylink.manager.service.ServerListService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.config.DatabaseMasterSlaveReplicationStrategy;
import com.xylink.manager.service.remote.ocean.OceanRemoteClient;
import com.xylink.manager.validate.AlertApiReqValidate;
import com.xylink.util.JDBCUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025-03-05 16:21
 */
@Service
public class MysqlDatabaseMasterSlaveReplicationStrategy extends AbstractDatabaseMasterSlaveReplicationStrategy implements DatabaseMasterSlaveReplicationStrategy {
    @Resource
    private JDBCUtils jdbcUtils;
    @Resource(name = "longtimeRestTemplate")
    private RestTemplate restTemplate;
    private static final String DATABASE_NAME = "mysql";

    private static final String SET_READ_ONLY_PROPERTY = MysqlConstants.SET_READ_ONLY_PROPERTY_TEMPLATE;
    private static final String SET_SUPER_READ_ONLY_PROPERTY = MysqlConstants.SET_SUPER_READ_ONLY_PROPERTY_TEMPLATE;

    private static final String QUERY_SUPER_READ_ONLY_PROPERTY = MysqlConstants.QUERY_SUPER_READ_ONLY_PROPERTY;
    private static final String QUERY_REPLICATION_PROPERTY = MysqlConstants.QUERY_REPLICATION_PROPERTY;

    private static final List<String> REPLICATION_FILTER_LIST = Arrays.asList(KEY_REPLICA_STATUS_SLAVE_IO_RUNNING, KEY_REPLICA_STATUS_SLAVE_SQL_RUNNING);

    public MysqlDatabaseMasterSlaveReplicationStrategy(K8sService k8sService, ServerListService serverListService, OceanRemoteClient oceanRemoteClient) {
        super(k8sService, serverListService, oceanRemoteClient);
    }

    @Override
    public String getDatabaseName() {
        return DATABASE_NAME;
    }

    @Override
    public boolean failover(SystemDatabaseProperties systemDatabaseProperties) {
        /*
         * 切换主备数据库读写权限
         * 备切主 备变为只读，主变为读写
         * 主切换到备 主变只读，备变读写
         */
        String currentMode = systemDatabaseProperties.getCurrentMode();
        if ("slave".equalsIgnoreCase(currentMode)) {
            try {
                this.changeMysqlSlaveReadonly("ON", systemDatabaseProperties);
            } catch (Exception e) {
                logger.error("Haproxy switch:changeMysqlSlaveReadonly ON error.", e);
            }
            try {
                this.changeMysqlReadonly("OFF", systemDatabaseProperties);
            } catch (Exception e) {
                logger.error("Haproxy switch:changeMysqlReadonly OFF error.", e);
            }

        } else {
            try {
                this.changeMysqlReadonly("ON", systemDatabaseProperties);
            } catch (Exception e) {
                logger.error("Haproxy switch:changeMysqlReadonly ON error.", e);
            }
            try {
                this.changeMysqlSlaveReadonly("OFF", systemDatabaseProperties);
            } catch (Exception e) {
                logger.error("Haproxy switch:changeMysqlSlaveReadonly OFF error.", e);
            }
        }
        mongodbReplication();
        return true;
    }

    @Override
    public Map<String, String> replicationProperties(String ip, String port) {
        Map<String, String> map = new HashMap<>();
        try {
            String dbBakPassword = getDbBakPassword();
            map.putAll(jdbcUtils.getMysqlProperties(QUERY_SUPER_READ_ONLY_PROPERTY, ip, port, "dbbak", dbBakPassword));
        } catch (Exception e) {
            logger.error("MysqlProperties superReadOnlyProperty error.", e);
        }

        try {
            Map<String, String> temp = jdbcUtils.getMysqlSlaveStatus(QUERY_REPLICATION_PROPERTY, ip, port, "dbbak", "U1O5ZeRyLFd#u9T6TF9h");
            if (temp != null) {
                temp.entrySet().removeIf(item ->
                        !REPLICATION_FILTER_LIST.contains(item.getKey())
                );
                map.putAll(temp);
            }
        } catch (Exception e) {
            logger.error("MysqlProperties replicationProperty error.", e);
        }
        return map;
    }

    @Override
    public void replication(SystemDatabaseProperties systemDatabaseProperties) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void cancelReplication(SystemDatabaseProperties systemDatabaseProperties) {
        throw new UnsupportedOperationException();
    }

    public void changeMysqlSlaveReadonly(String readonly, SystemDatabaseProperties systemDatabaseProperties) {
        String ip = systemDatabaseProperties.getStandbyIp();
        String port = systemDatabaseProperties.getStandbyPort();
        if (StringUtils.isBlank(ip)) {
            logger.error("ChangeMysqlSlaveReadonly error , cause by {}", ErrorStatus.NO_ENV_DATABASE_IP.getUserMessage());
            return;
        }
        if (StringUtils.isBlank(port)) {
            port = "3306";
        }
        editTemporary(readonly, ip, port);
        editPersistent(SystemDatabaseDto.SystemRole.SLAVE.getName(), readonly, systemDatabaseProperties.getStandbyManagerAddress());
    }

    public void changeMysqlReadonly(String readonly, SystemDatabaseProperties systemDatabaseProperties) {
        String ip = systemDatabaseProperties.getMasterIp();
        String port = systemDatabaseProperties.getMasterPort();
        if (StringUtils.isBlank(ip)) {
            logger.error("ChangeMysqlSlaveReadonly error , cause by {}", ErrorStatus.NO_ENV_DATABASE_IP.getUserMessage());
            return;
        }
        if (StringUtils.isBlank(port)) {
            port = "3306";
        }
        editTemporary(readonly, ip, port);
        editPersistent(SystemDatabaseDto.SystemRole.MASTER.getName(), readonly, systemDatabaseProperties.getMasterManagerAddress());
    }

    /**
     * 修改当前运行容器配置
     *
     * @param readonly
     */
    private void editTemporary(String readonly, String ip, String port) throws ServerException {
        logger.info("Update database property:super_read_only,ip:{} ,port:{} ,value:{}", ip, port, readonly);
        String updateSql = SET_READ_ONLY_PROPERTY.replace("{value}", readonly);
        updateSql += SET_SUPER_READ_ONLY_PROPERTY.replace("{value}", readonly);
        jdbcUtils.setMysqlProperties(updateSql, ip, port, "dbbak", "U1O5ZeRyLFd#u9T6TF9h");
    }

    private void editPersistent(String role, String value, String managerAddress) {
        if (StringUtils.isBlank(managerAddress)) {
            logger.info("Cancel editPersistent. Cause by 'managerAddress' is null.");
            return;
        }
        try {
            String url = "http://" + managerAddress + "/manager/server/config/mysql/" + role + "/readonly/" + value;
            AlertApiReq alertApiReq = new AlertApiReq();
            String key = "inner";
            long timestamp = System.currentTimeMillis();
            alertApiReq.setKey(key);
            alertApiReq.setTimestamp(timestamp);
            alertApiReq.setSign(AlertApiReqValidate.sign(key, "changeMysqlMode", timestamp));
            restTemplate.postForEntity(url, alertApiReq, Void.class);
        } catch (Exception e) {
            logger.error("Notify:{} edit:{} readonly:{} error.", managerAddress, role, value, e);
        }
    }

    public JDBCUtils getJdbcUtils() {
        return jdbcUtils;
    }

    public void setJdbcUtils(JDBCUtils jdbcUtils) {
        this.jdbcUtils = jdbcUtils;
    }
}
