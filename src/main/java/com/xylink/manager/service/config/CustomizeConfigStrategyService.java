package com.xylink.manager.service.config;

import com.xylink.config.Constants;
import com.xylink.manager.model.cm.customize.ICustomizeConfigStrategy;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @create 2022/9/7 5:43 下午
 */
@Service
public class CustomizeConfigStrategyService implements ApplicationContextAware {
    private static final Map<String, ICustomizeConfigStrategy> serviceToStrategyMap = new ConcurrentHashMap<>();

    public Object getCustomizeConfig(String nodeName, String service) {
        ICustomizeConfigStrategy strategy = serviceToStrategyMap.get(service);
        if (Objects.isNull(strategy)) {
            return null;
        }
        return strategy.getCustomizeConfig(nodeName);
    }

    public String saveCustomizeConfig(String service, LinkedHashMap data, String nodeName) {
        ICustomizeConfigStrategy strategy = serviceToStrategyMap.get(service);
        if (Objects.isNull(strategy)) {
            return null;
        }
        strategy.saveCustomizeConfig(data);
        //重启对应服务
//        strategy.restartOnePodByLabel(k8sClientBuilder.getClient(), strategy.getLabelName(), nodeName);
        return Constants.SUCCESS_RESPONSE;
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, ICustomizeConfigStrategy> contextBeansOfType = applicationContext.getBeansOfType(ICustomizeConfigStrategy.class);
        contextBeansOfType.values().forEach(x-> serviceToStrategyMap.put(x.getLabelName(),x));
    }

    public boolean containsService(String service) {
        return serviceToStrategyMap.containsKey(service);
    }
}
