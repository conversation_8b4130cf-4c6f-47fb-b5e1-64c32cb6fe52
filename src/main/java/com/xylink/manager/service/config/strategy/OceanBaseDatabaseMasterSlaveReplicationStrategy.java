package com.xylink.manager.service.config.strategy;

import com.xylink.manager.model.SystemDatabaseProperties;
import com.xylink.manager.service.ServerListService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.config.DatabaseMasterSlaveReplicationStrategy;
import com.xylink.manager.service.config.IServerConfigService;
import com.xylink.manager.service.haproxy.HaproxyService;
import com.xylink.manager.service.remote.ocean.OceanRemoteClient;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025-03-05 16:21
 */
@Service
public class OceanBaseDatabaseMasterSlaveReplicationStrategy extends AbstractDatabaseMasterSlaveReplicationStrategy implements DatabaseMasterSlaveReplicationStrategy {

    private static final String DATABASE_NAME = "OB";
    private OceanbaseStatusCheckTaskManager oceanbaseStatusCheckTaskManager;
    private final OceanBaseDatabaseMasterSlaveService oceanBaseDatabaseMasterSlaveService;

    private HaproxyService haproxyService;
    private IServerConfigService iServerConfigService;

    public OceanBaseDatabaseMasterSlaveReplicationStrategy(K8sService k8sService, ServerListService serverListService, OceanRemoteClient oceanRemoteClient, OceanBaseDatabaseMasterSlaveService oceanBaseDatabaseMasterSlaveService, HaproxyService haproxyService, IServerConfigService iServerConfigService) {
        super(k8sService, serverListService, oceanRemoteClient);
        this.oceanBaseDatabaseMasterSlaveService = oceanBaseDatabaseMasterSlaveService;
        this.haproxyService = haproxyService;
        this.iServerConfigService = iServerConfigService;
        oceanbaseStatusCheckTaskManager = singleOceanbaseStatusCheckTaskManager();
    }

    @Override
    public String getDatabaseName() {
        return DATABASE_NAME;
    }

    @Override
    public boolean failover(SystemDatabaseProperties systemDatabaseProperties) {
        oceanbaseStatusCheckTaskManager.shutdown();
        oceanBaseDatabaseMasterSlaveService.changeDbState(systemDatabaseProperties);
        mongodbReplication();
        oceanbaseStatusCheckTaskManager.start();
        return true;
    }

    @Override
    public Map<String, String> replicationProperties(String ip, String port) {
        ObShellResponseDto obShellResponseDto = oceanBaseDatabaseMasterSlaveService.getDbStates();
        if (obShellResponseDto != null) {
            Map<String, String> properties = new HashMap<>();
            String role = obShellResponseDto.getRole();
            String status = obShellResponseDto.getStatus();
            if ("master".equalsIgnoreCase(role)) {
                properties.put(KEY_SUPER_READ_ONLY, "OFF");
            } else if ("slave".equalsIgnoreCase(role)) {
                properties.put(KEY_SUPER_READ_ONLY, "ON");
                properties.put(KEY_REPLICA_STATUS, "ok".equalsIgnoreCase(status) ? "YES" : "NO");
                properties.put(KEY_REPLICA_STATUS_SLAVE_IO_RUNNING, "ok".equalsIgnoreCase(status) ? "YES" : "NO");
                properties.put(KEY_REPLICA_STATUS_SLAVE_SQL_RUNNING, "ok".equalsIgnoreCase(status) ? "YES" : "NO");
            }
            return properties;
        }
        return Collections.emptyMap();
    }

    @Override
    public void replication(SystemDatabaseProperties systemDatabaseProperties) {
        if ("slave".equals(systemDatabaseProperties.getTargetMode())) {
            try {
                oceanBaseDatabaseMasterSlaveService.changeDbToStandby(systemDatabaseProperties);
            } catch (Exception e) {
                logger.error("同步修复：【slave->取消主备异常】,", e);
            }
        }
        oceanBaseDatabaseMasterSlaveService.changeDbToHa(systemDatabaseProperties);
        // 开启检测 如果状态不一致 需要 ChangeDbToHa.sh slave/master
        oceanbaseStatusCheckTaskManager.start();
    }

    @Override
    public void cancelReplication(SystemDatabaseProperties systemDatabaseProperties) {
        oceanBaseDatabaseMasterSlaveService.changeDbToStandby(systemDatabaseProperties);
        // 取消检测 如果状态不一致 需要 ChangeDbToHa.sh slave/master
        oceanbaseStatusCheckTaskManager.shutdown();
    }

    public K8sService getK8sService() {
        return k8sService;
    }

    public void setK8sService(K8sService k8sService) {
        this.k8sService = k8sService;
    }

    public ServerListService getServerListService() {
        return serverListService;
    }

    public void setServerListService(ServerListService serverListService) {
        this.serverListService = serverListService;
    }

    private OceanbaseStatusCheckTaskManager singleOceanbaseStatusCheckTaskManager() {
        if (this.oceanbaseStatusCheckTaskManager == null) {
            this.oceanbaseStatusCheckTaskManager = new OceanbaseStatusCheckTaskManager(OceanbaseStatusCheckConfig.DEFAULT_START, oceanBaseDatabaseMasterSlaveService, haproxyService, iServerConfigService);
        }
        return this.oceanbaseStatusCheckTaskManager;
    }
}
