package com.xylink.manager.service.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-03-05 16:23
 */
@Slf4j
@Component
public class DatabaseMasterSlaveReplicationStrategyFactory implements InitializingBean {

    @Resource
    private List<DatabaseMasterSlaveReplicationStrategy> databaseMasterSlaveReplicationStrategyList;

    public DatabaseMasterSlaveReplicationStrategy create(String database) {
        return createOptional(database).orElseThrow(IllegalArgumentException::new);
    }

    public Optional<DatabaseMasterSlaveReplicationStrategy> createOptional(String database) {
        return databaseMasterSlaveReplicationStrategyList.stream()
                .filter(databaseMasterSlaveReplicationStrategy ->
                        database.equalsIgnoreCase(databaseMasterSlaveReplicationStrategy.getDatabaseName()))
                .findFirst();
    }

    public List<DatabaseMasterSlaveReplicationStrategy> getDatabaseMasterSlaveReplicationStrategyList() {
        return databaseMasterSlaveReplicationStrategyList;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("Register info: {}", printRegisterInfo());
    }

    private String printRegisterInfo() {
        if (databaseMasterSlaveReplicationStrategyList != null || databaseMasterSlaveReplicationStrategyList.isEmpty()) {
            return "[]";
        }
        return databaseMasterSlaveReplicationStrategyList.stream().map(item -> item.getClass().getName()).collect(Collectors.toList()).toString();
    }
}
