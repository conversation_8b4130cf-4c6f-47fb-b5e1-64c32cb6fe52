package com.xylink.manager.service.config;

import com.xylink.config.Constants;
import com.xylink.config.MysqlConstants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.controller.dto.HaproxyAutoSwitchDto;
import com.xylink.manager.controller.dto.StandardConsoleLogDto;
import com.xylink.manager.controller.dto.alert.AlertApiReq;
import com.xylink.manager.model.SystemDatabaseDto;
import com.xylink.manager.model.SystemDatabaseProperties;
import com.xylink.manager.model.SystemInfo;
import com.xylink.manager.model.em.SystemTypes;
import com.xylink.manager.service.ServiceManageService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.haproxy.HaproxyService;
import com.xylink.manager.validate.AlertApiReqValidate;
import com.xylink.util.CommandUtils;
import com.xylink.util.JDBCUtils;
import com.xylink.util.TraceIdUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2022/4/6 2:57 下午
 */
@Slf4j
@Service
public class ServerConfigServiceImpl implements IServerConfigService {
    @Resource
    private K8sService k8sService;
    @Resource
    private JDBCUtils jdbcUtils;
    @Resource
    private HaproxyService haproxyService;
    @Resource(name = "longtimeRestTemplate")
    private RestTemplate restTemplate;
    @Resource
    private DatabaseMasterSlaveReplicationStrategyFactory databaseMasterSlaveReplicationStrategyFactory;
    @Resource
    private ServiceManageService serviceManageService;
    @Value("${base.dir}")
    private String baseDir;

    @Override
    @Transactional
    public List<SystemDatabaseDto> getProperties(boolean extraQueryProperty, String role) {
        String database = getDatabase();
        List<SystemDatabaseDto> data = new ArrayList<>();
        if ("mysql".equalsIgnoreCase(database)) {
            if ("all".equals(role)) {
                Arrays.stream(SystemDatabaseDto.SystemRole.values()).forEach(e -> {
                    Triple<String, String, String> databaseInfo = this.getSystemMainDatabaseInfo(e.getName());
                    data.add(getSystemDatabaseDto(extraQueryProperty, databaseInfo, e.getName()));
                });
            } else {
                Triple<String, String, String> databaseInfo = this.getSystemMainDatabaseInfo(role);
                data.add(getSystemDatabaseDto(extraQueryProperty, databaseInfo, role));
            }
        } else if ("ob".equalsIgnoreCase(database)) {
            // 本地状态
            data.add(selfProperties());
            // 对端状态
            SystemDatabaseDto peer = peerProperties();
            if (peer != null) {
                data.add(peer);
            }
            // 查看主系统 properties中存在 主从同步属性的话 复制到 备系统中properties
            Optional<SystemDatabaseDto> masterSystemDatabaseDto = data.stream().filter(systemDatabaseDto -> SystemDatabaseDto.SystemRole.MASTER.getName().equalsIgnoreCase(systemDatabaseDto.getRole())).findFirst();
            Optional<SystemDatabaseDto> slaveSystemDatabaseDto = data.stream().filter(systemDatabaseDto -> SystemDatabaseDto.SystemRole.SLAVE.getName().equalsIgnoreCase(systemDatabaseDto.getRole())).findFirst();
            if (masterSystemDatabaseDto.isPresent() && slaveSystemDatabaseDto.isPresent()) {
                SystemDatabaseDto master = masterSystemDatabaseDto.get();
                SystemDatabaseDto slave = slaveSystemDatabaseDto.get();
                List<SystemDatabaseDto.Properties> masterProperties = master.getProperties();
                if (masterProperties != null) {
                    masterProperties.forEach(property -> {
                        if ("REPLICA_STATUS".equalsIgnoreCase(property.getKey()) || "Slave_IO_Running".equalsIgnoreCase(property.getKey()) || "Slave_SQL_Running".equalsIgnoreCase(property.getKey())) {
                            slave.addProperty(property);
                        }
                    });
                }
                data.clear();
                data.add(master);
                data.add(slave);
            }
        }
        log.info("Properties is:[{}]", data);
        return data;
    }

    private SystemDatabaseDto peerProperties() {
        String url = null;
        try {
            Optional<String> managerAddress = peerManagerAddress();
            if (managerAddress.isPresent()) {
                url = "http://" + managerAddress.get() + "/manager/peer/server/config/ob/properties";
                AlertApiReq alertApiReq = getAlertApiReq(AlertApiReqValidate.PEER_PROPERTIES);
                ResponseEntity<SystemDatabaseDto> peerProperties = restTemplate.postForEntity(url, alertApiReq, SystemDatabaseDto.class);
                return peerProperties.getBody();
            }
        } catch (Exception e) {
            log.error("Request peer address:{} error.", url, e);
        }
        return null;
    }

    @NotNull
    private static AlertApiReq getAlertApiReq(String eventType) {
        AlertApiReq alertApiReq = new AlertApiReq();
        String key = "inner";
        long timestamp = System.currentTimeMillis();
        alertApiReq.setKey(key);
        alertApiReq.setTimestamp(timestamp);
        alertApiReq.setSign(AlertApiReqValidate.sign(key, eventType, timestamp));
        return alertApiReq;
    }

    private Optional<String> peerManagerAddress() {
        String managerAddress = null;
        try {
            Triple<String, String, String> databaseInfo = this.getSystemMainDatabaseInfo(haproxyService.systemIsBackupMode() ? SystemDatabaseDto.SystemRole.MASTER.getName() : SystemDatabaseDto.SystemRole.SLAVE.getName());
            managerAddress = databaseInfo.getMiddle();
        } catch (Exception e) {
            log.error("Query peer address:{} error.", managerAddress, e);
        }

        return Optional.ofNullable(managerAddress);
    }

    @Override
    public Optional<String> peerDatabaseAddress(SystemDatabaseProperties systemDatabaseProperties) {
        String databaseAddress = null;
        try {
            databaseAddress = haproxyService.systemIsBackupMode() ? systemDatabaseProperties.getMasterIp() : systemDatabaseProperties.getStandbyIp();
        } catch (Exception e) {
            log.error("Query peer address:{} error.", databaseAddress, e);
        }
        return Optional.ofNullable(databaseAddress);
    }

    @Override
    public SystemDatabaseDto getSystemDatabaseDto(String role, Pair<String, String> address) {
        return getSystemDatabaseDto(true, Triple.of(address.getLeft(), "", String.valueOf(address.getRight())), role);
    }

    @Override
    public SystemDatabaseDto selfProperties() {
        SystemInfo systemInfo = serviceManageService.loadSystemInfo();
        if (systemInfo != null) {
            String role = SystemDatabaseDto.SystemRole.MASTER.getName();
            if (SystemTypes.backup.name().equalsIgnoreCase(systemInfo.getType())) {
                role = SystemDatabaseDto.SystemRole.SLAVE.getName();
            }
            Triple<String, String, String> databaseInfo = this.getSystemMainDatabaseInfo(role);
            return getSystemDatabaseDto(true, databaseInfo, role);
        }
        log.info("未设置系统主备标志！！！无法查询信息");
        return null;
    }

    @Override
    public boolean cancelSelfReplication(String database) {
        DatabaseMasterSlaveReplicationStrategy databaseMasterSlaveReplicationStrategy = databaseMasterSlaveReplicationStrategyFactory.create(database);
        try {
            SystemDatabaseProperties systemDatabaseProperties = mainDatabaseBaseInfo();
            systemDatabaseProperties.setPeerIp(peerDatabaseAddress(systemDatabaseProperties).orElse("127.0.0.1"));
            databaseMasterSlaveReplicationStrategy.cancelReplication(systemDatabaseProperties);
        } catch (Exception e) {
            log.error("cancelSelfReplication error.", e);
            return false;
        }
        return true;
    }

    @Override
    public void selfFailover(String database, String currentMode) {
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        new Thread(() -> {
            try {
                if (contextMap != null) {
                    MDC.setContextMap(contextMap);
                }
                String traceId = MDC.get("traceId");
                if (StringUtils.isBlank(traceId)) {
                    MDC.put("traceId", TraceIdUtil.getTraceId());
                }
                String targetMode = haproxyService.systemIsBackupMode() ? currentMode : haproxyService.getTargetMode(currentMode);
                DatabaseMasterSlaveReplicationStrategy databaseMasterSlaveReplicationStrategy = databaseMasterSlaveReplicationStrategyFactory.create(database);
                SystemDatabaseProperties systemDatabaseProperties = mainDatabaseBaseInfo();
                systemDatabaseProperties.setCurrentMode(currentMode);
                systemDatabaseProperties.setTargetMode(targetMode);
                systemDatabaseProperties.setPeerIp(peerDatabaseAddress(systemDatabaseProperties).orElse("127.0.0.1"));
                databaseMasterSlaveReplicationStrategy.failover(systemDatabaseProperties);
            } finally {
                MDC.clear();
            }
        }).start();
    }

    @Override
    public void selfReplication(String database, String targetMode) {
        DatabaseMasterSlaveReplicationStrategy strategy = databaseMasterSlaveReplicationStrategyFactory.create(database);
        SystemDatabaseProperties systemDatabaseProperties = mainDatabaseBaseInfo();
        systemDatabaseProperties.setPeerIp(peerDatabaseAddress(systemDatabaseProperties).orElse("127.0.0.1"));
        systemDatabaseProperties.setTargetMode(targetMode);
        strategy.replication(systemDatabaseProperties);
    }

    private SystemDatabaseDto getSystemDatabaseDto(boolean extraQueryProperty, Triple<String, String, String> databaseInfo, String role) {
        String ip = databaseInfo.getLeft();
        String port = databaseInfo.getRight();
        String managerAddress = databaseInfo.getMiddle();
        if (StringUtils.isBlank(ip) || !extraQueryProperty) {
            return new SystemDatabaseDto(ip, port, role, managerAddress);
        }

        if (StringUtils.isBlank(port)) {
            port = "3306";
        }
        SystemDatabaseDto systemDatabaseDto = new SystemDatabaseDto();
        systemDatabaseDto.setIp(ip);
        systemDatabaseDto.setPort(port);
        systemDatabaseDto.setManagerAddress(managerAddress);
        systemDatabaseDto.setRole(role);

        Map<String, String> properties = replicationProperty(ip, port, getDatabase());
        properties.forEach(systemDatabaseDto::addProperty);
        return systemDatabaseDto;
    }

    private String getDatabase() {
        Map<String, Object> databaseMap = info();
        return (String) databaseMap.get("database");
    }

    private Map<String, String> replicationProperty(String ip, String port, String database) {
        DatabaseMasterSlaveReplicationStrategy databaseMasterSlaveReplicationStrategy = databaseMasterSlaveReplicationStrategyFactory.create(database);
        return databaseMasterSlaveReplicationStrategy.replicationProperties(ip, port);
    }

    @Override
    public SystemDatabaseProperties mainDatabaseBaseInfo() {
        Map<String, String> allIps = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        String masterIp = allIps.get(NetworkConstants.MASTER_SYSTEM_DATABASE_IP);
        String masterPort = allIps.get(NetworkConstants.MASTER_SYSTEM_DATABASE_PORT);
        String standbyIp = allIps.get(NetworkConstants.STANDBY_SYSTEM_DATABASE_IP);
        String standbyPort = allIps.get(NetworkConstants.STANDBY_SYSTEM_DATABASE_PORT);
        String masterManagerAddress = allIps.get(NetworkConstants.MASTER_SYSTEM_MANAGER_ADDRESS);
        String standbyManagerAddress = allIps.get(NetworkConstants.STANDBY_SYSTEM_MANAGER_ADDRESS);
        SystemDatabaseProperties systemDatabaseProperties = new SystemDatabaseProperties();
        systemDatabaseProperties.setMasterIp(masterIp);
        systemDatabaseProperties.setMasterPort(masterPort);
        systemDatabaseProperties.setMasterManagerAddress(masterManagerAddress);
        systemDatabaseProperties.setStandbyIp(standbyIp);
        systemDatabaseProperties.setStandbyPort(standbyPort);
        systemDatabaseProperties.setStandbyManagerAddress(standbyManagerAddress);
        return systemDatabaseProperties;
    }

    /**
     * <pre>
     * 1. 检查数据库配置的主备信息是否完整
     * 2. 查询当前系统状态
     * 3. 设置 replication 中主备用信息
     * 4. 执行k8s远程调用
     * </pre>
     *
     * @param database 数据库类型
     */
    @Override
    public void replication(String database) {
        // 根据当前的系统状态 设置主
        String currentMode = systemCurrentMode();

        log.info("replication currentMode:{},systemMode:{}", currentMode, serviceManageService.loadSystemInfo());

        if ((haproxyService.systemIsBackupMode() && "master".equalsIgnoreCase(currentMode)) || (!haproxyService.systemIsBackupMode() && "slave".equalsIgnoreCase(currentMode))) {
            String url = null;
            try {
                Optional<String> managerAddress = peerManagerAddress();
                if (managerAddress.isPresent()) {
                    url = "http://" + managerAddress.get() + "/manager/peer/server/config/ob/replication/master";
                    AlertApiReq alertApiReq = getAlertApiReq(AlertApiReqValidate.PEER_REPLICATION);
                    restTemplate.postForEntity(url, alertApiReq, Void.class);
                }
            } catch (Exception e) {
                log.error("Request peer address:{} error.", url, e);
            }
            selfReplication(database, "slave");
        }

        if ((haproxyService.systemIsBackupMode() && "slave".equalsIgnoreCase(currentMode)) || (!haproxyService.systemIsBackupMode() && "master".equalsIgnoreCase(currentMode))) {
            selfReplication(database, "master");
            String url = null;
            try {
                Optional<String> managerAddress = peerManagerAddress();
                if (managerAddress.isPresent()) {
                    url = "http://" + managerAddress.get() + "/manager/peer/server/config/ob/replication/slave";
                    AlertApiReq alertApiReq = getAlertApiReq(AlertApiReqValidate.PEER_REPLICATION);
                    restTemplate.postForEntity(url, alertApiReq, Void.class);
                }
            } catch (Exception e) {
                log.error("Request peer address:{} error.", url, e);
            }
        }
    }

    /**
     * <pre>
     * 1. 检查数据库配置的主备信息是否完整
     * 2. 查询当前系统状态
     * 3. 设置 replication 中主备用信息
     * 4. 执行k8s远程调用
     * </pre>
     *
     * @param database 数据库类型
     */
    @Override
    public void cancelReplication(String database) {
        // 取消自己
        cancelSelfReplication(database);
        // 取消对端
        String url = null;
        try {
            Optional<String> managerAddress = peerManagerAddress();
            if (managerAddress.isPresent()) {
                url = "http://" + managerAddress.get() + "/manager/peer/server/config/ob/cancelReplication";
                AlertApiReq alertApiReq = getAlertApiReq(AlertApiReqValidate.PEER_CANCEL_REPLICATION);
                restTemplate.postForEntity(url, alertApiReq, Void.class);
            }
        } catch (Exception e) {
            log.error("Request peer address:{} error.", url, e);
        }
    }

    @Override
    public void failover(String currentMode) {
        String database = getDatabase();
        if ("mysql".equalsIgnoreCase(database)) {
            selfFailover(database, currentMode);
            peerFailover(database, currentMode);
        } else {
            // 先切换当前master-->slave 再切换slave-->master
            // currentMode:master--> 如果当前是 slave_system 先调用对端,再调用本机
            // currentMode:slave--> 如果当前是 master_system 先调用对端,再调用本机
            boolean systemIsBackupMode = haproxyService.systemIsBackupMode();

            if ((systemIsBackupMode && "master".equals(currentMode)) || (!systemIsBackupMode && "slave".equals(currentMode))) {
                log.info("peerFailover then selfFailover");
                peerFailover(database, currentMode);
                try {
                    TimeUnit.MILLISECONDS.sleep(500);
                } catch (InterruptedException e) {
                    log.error("sleep error.", e);
                }
                selfFailover(database, currentMode);
            } else {
                log.info("selfFailover then peerFailover");
                selfFailover(database, currentMode);
                try {
                    TimeUnit.MILLISECONDS.sleep(500);
                } catch (InterruptedException e) {
                    log.error("sleep error.", e);
                }
                peerFailover(database, currentMode);
            }

            try {
                TimeUnit.SECONDS.sleep(2);
            } catch (InterruptedException e) {
                log.error("sleep error.", e);
            }

        }
    }

    private void peerFailover(String database,String currentMode) {
        String url = null;
        try {
            Optional<String> managerAddress = peerManagerAddress();
            if (managerAddress.isPresent()) {
                url = "http://" + managerAddress.get() + "/manager/peer/server/config/" + database + "/failover/" + currentMode;
                AlertApiReq alertApiReq = getAlertApiReq(AlertApiReqValidate.PEER_FAILOVER);
                restTemplate.postForEntity(url, alertApiReq, Void.class);
            }
        } catch (Exception e) {
            log.error("Request peer address:{} error.", url, e);
        }
    }

    @Override
    public void setMainDatabaseBaseInfo(SystemDatabaseProperties systemDatabaseProperties) {
        Map<String, String> allIps = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        allIps.put(NetworkConstants.MASTER_SYSTEM_DATABASE_IP, systemDatabaseProperties.getMasterIp());
        allIps.put(NetworkConstants.MASTER_SYSTEM_DATABASE_PORT, systemDatabaseProperties.getMasterPort());
        allIps.put(NetworkConstants.STANDBY_SYSTEM_DATABASE_IP, systemDatabaseProperties.getStandbyIp());
        allIps.put(NetworkConstants.STANDBY_SYSTEM_DATABASE_PORT, systemDatabaseProperties.getStandbyPort());
        allIps.put(NetworkConstants.MASTER_SYSTEM_MANAGER_ADDRESS, systemDatabaseProperties.getMasterManagerAddress());
        allIps.put(NetworkConstants.STANDBY_SYSTEM_MANAGER_ADDRESS, systemDatabaseProperties.getStandbyManagerAddress());
        k8sService.editConfigmap(Constants.CONFIGMAP_ALLIP, allIps);
    }

    @Override
    public void replicationForMaster() {
        String currentMode = systemCurrentMode();
        Triple<String, String, String> master = getSystemMainDatabaseInfo(SystemDatabaseDto.SystemRole.MASTER.getName());
        Triple<String, String, String> slave = getSystemMainDatabaseInfo(SystemDatabaseDto.SystemRole.SLAVE.getName());
        assertLegalDataBaseInfo(master);
        assertLegalDataBaseInfo(slave);
        if ("master".equals(currentMode)) {
            // 异步执行
            new Thread(() -> replication(slave.getLeft(), slave.getRight(), master.getLeft(), master.getRight())).start();
        } else {
            // 异步执行
            new Thread(() -> backUpAndRestoreThenReplication(slave.getLeft(), slave.getRight(), master.getLeft(), master.getRight())).start();
        }
    }


    @Override
    public void replicationForSlave() {
        String currentMode = systemCurrentMode();
        Triple<String, String, String> master = getSystemMainDatabaseInfo(SystemDatabaseDto.SystemRole.MASTER.getName());
        Triple<String, String, String> slave = getSystemMainDatabaseInfo(SystemDatabaseDto.SystemRole.SLAVE.getName());
        assertLegalDataBaseInfo(master);
        assertLegalDataBaseInfo(slave);
        if ("master".equals(currentMode)) {
            // 异步执行
            new Thread(() -> backUpAndRestoreThenReplication(master.getLeft(), master.getRight(), slave.getLeft(), slave.getRight())).start();
        } else {
            // 异步执行
            new Thread(() -> replication(master.getLeft(), master.getRight(), slave.getLeft(), slave.getRight())).start();
        }
    }

    @Override
    public void noReplicationForMaster() {
        Triple<String, String, String> master = getSystemMainDatabaseInfo(SystemDatabaseDto.SystemRole.MASTER.getName());
        assertLegalDataBaseInfo(master);
        stopSlave(master.getLeft(), master.getRight());
    }

    @Override
    public void noReplicationForSlave() {
        Triple<String, String, String> slave = getSystemMainDatabaseInfo(SystemDatabaseDto.SystemRole.SLAVE.getName());
        assertLegalDataBaseInfo(slave);
        stopSlave(slave.getLeft(), slave.getRight());
    }

    @Override
    public void changeMysqlConfigMapReadonly(String readonly) {
        Map<String, String> configmap = k8sService.getConfigmapOrCreate(Constants.CONFIGMAP_ALL_MYSQL);
        if (configmap != null) {
            Map<String, String> updateMap = new HashMap<>();
            configmap.forEach((k, v) -> {
                if (k.endsWith(MysqlConstants.MYSQL_MODE_KEY)) {
                    updateMap.put(k, readonly);
                }
            });
            configmap.putAll(updateMap);
            k8sService.editConfigmap(Constants.CONFIGMAP_ALL_MYSQL, configmap);
        }
    }

    @Override
    public void changeMysqlSlaveConfigMapReadonly(String readonly) {
        Map<String, String> configmap = k8sService.getConfigmapOrCreate(Constants.CONFIGMAP_ALL_MYSQLSLAVE);
        if (configmap != null) {
            Map<String, String> updateMap = new HashMap<>();
            configmap.forEach((k, v) -> {
                if (k.endsWith(MysqlConstants.MYSQL_MODE_KEY)) {
                    updateMap.put(k, readonly);
                }
            });
            configmap.putAll(updateMap);
            k8sService.editConfigmap(Constants.CONFIGMAP_ALL_MYSQLSLAVE, configmap);
        }
    }


    private Triple<String, String, String> getSystemMainDatabaseInfo(String role) {
        if (SystemDatabaseDto.SystemRole.MASTER.getName().equals(role)) {
            return k8sService.getMainDataBaseInfo();
        } else {
            return k8sService.getStandbyMainDataBaseInfo();
        }
    }

    private String systemCurrentMode() {
        HaproxyAutoSwitchDto haproxyAutoSwitchDto = haproxyService.getAutoSwitchConfig();
        String currentMode = haproxyAutoSwitchDto.getCurrentMode();
        if (StringUtils.isBlank(currentMode)) {
            throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
        }
        return currentMode;
    }

    private void assertLegalDataBaseInfo(Triple<String, String, String> info) {
        if (info != null && StringUtils.isNotBlank(info.getLeft())) {
            return;
        }
        throw new ServerException(ErrorStatus.NO_ENV_DATABASE_IP);
    }

    /**
     * 重建主从同步
     *
     * @param masterIp
     * @param masterPort
     * @param slaveIp
     * @param slavePort
     */
    @Override
    public void replication(String masterIp, String masterPort, String slaveIp, String slavePort) {
        log.info("Describe: Mysql -h {} -p {} -e \"stop slave;change master to master_host={} ,master_port={}\".", slaveIp, slavePort, masterIp, masterPort);
        String shell = baseDir + "/private_manager/script/mysql_repslave_docker.sh";
        copyFile(shell);
        String cmd = k8sService.getExportKubeConfigExec() + ";" + shell + " " + masterIp + " " + masterPort + " " + slaveIp + " " + slavePort + " 'U1O5ZeRyLFd#u9T6TF9h' 'U8kkWjeX7#HeYOv6TXIG' 'U1O5ZeRyLFd#u9T6TF9h'";
        String logFile = baseDir + "/logs/db/mysql-replication.log";
        long startTime = System.nanoTime();
        printStartLog("mysql_repslave_docker.sh", logFile);
        CommandUtils.execLogExist(new String[]{"/bin/sh", "-c", cmd}, new File(logFile));
        printEndLog("mysql_repslave_docker.sh", logFile, startTime);
    }

    /**
     * 备份masterIp数据 还原数据到slaveIp 重建主从同步
     *
     * @param masterIp
     * @param masterPort
     * @param slaveIp
     * @param slavePort
     */
    @Override
    public void backUpAndRestoreThenReplication(String masterIp, String masterPort, String slaveIp, String slavePort) {
        log.info("Describe: backUp {} restore to {} then replication.", masterIp, slaveIp);
        String shell = baseDir + "/private_manager/script/mysql_bakmaster_toslave_docker.sh";
        copyFile(shell);
        String cmd = k8sService.getExportKubeConfigExec() + ";" + shell + " " + masterIp + " " + masterPort + " " + slaveIp + " " + slavePort + " 'U1O5ZeRyLFd#u9T6TF9h' 'U8kkWjeX7#HeYOv6TXIG' 'U1O5ZeRyLFd#u9T6TF9h'";
        String logFile = baseDir + "/logs/db/mysql-replication.log";
        long startTime = System.nanoTime();
        printStartLog("mysql_bakmaster_toslave_docker.sh", logFile);
        CommandUtils.execLogExist(new String[]{"/bin/sh", "-c", cmd}, new File(logFile));
        printEndLog("mysql_bakmaster_toslave_docker.sh", logFile, startTime);
    }

    /**
     * 中断主从同步
     *
     * @param ip
     * @param port
     */
    @Override
    public void stopSlave(String ip, String port) {
        log.info("Describe:  Mysql -h {} -p {} -e \"stop slave;\".", ip, port);
        jdbcUtils.setMysqlProperties(MysqlConstants.STOP_REPLICATION_TEMPLATE, ip, port, "dbbak", "U1O5ZeRyLFd#u9T6TF9h");
    }

    @Override
    public void editPeerSystemCurrentModeConfig(String targetMode, String peerSystemManagerAddress) {
        if (StringUtils.isBlank(peerSystemManagerAddress)) {
            log.info("Cancel editPersistent. Cause by 'peerSystemManagerAddress' is null.");
            return;
        }
        try {
            String url = "http://" + peerSystemManagerAddress + "/manager/server/config/haproxy/" + targetMode;
            AlertApiReq alertApiReq = new AlertApiReq();
            String key = "inner";
            long timestamp = System.currentTimeMillis();
            alertApiReq.setKey(key);
            alertApiReq.setTimestamp(timestamp);
            alertApiReq.setSign(AlertApiReqValidate.sign(key, "changeHaproxyMode", timestamp));
            restTemplate.postForEntity(url, alertApiReq, Void.class);
        } catch (Exception e) {
            log.error("Notify:{} change haproxy mode to :{} error.", peerSystemManagerAddress, targetMode, e);
        }
    }

    @Override
    public Map<String, Object> info() {
        Map<String, Object> result = new HashMap<>();
        String database = k8sService.getDataBaseType();
        result.put("database", database.toLowerCase());
        return result;
    }

    /**
     * cp classpath:scripts/shell.sh baseDir{@link ServerConfigServiceImpl#baseDir}/private_manager/script/shell.sh
     *
     * @param path
     */
    private void copyFile(String path) {
        try {
            Files.deleteIfExists(Paths.get(path));
            String name = path.substring(path.lastIndexOf("/") + 1);
            ClassPathResource classPathResource = new ClassPathResource("scripts/" + name);

            File file = new File(path);
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }

            FileCopyUtils.copy(classPathResource.getInputStream(), Files.newOutputStream(file.toPath()));

            file.setExecutable(true);
        } catch (IOException e) {
            throw new ServerException(e, ErrorStatus.MYSQL_REPLICATION_SHELL_PROBLEM);
        }
    }

    private void printStartLog(String shellName, String logFile) {
        File file = FileUtils.getFile(logFile);
        if (file.exists()) {
            file.delete();
        }
        try {
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
            file.createNewFile();
        } catch (IOException e) {
            log.error("create new file error!", e);
        }
        try {
            FileUtils.writeStringToFile(file, "Run shell:[" + shellName + "] on " + LocalDateTime.now() + "\r\n", StandardCharsets.UTF_8, false);
        } catch (Exception e) {
            log.error(ExceptionUtils.getStackTrace(e));
        }
    }

    private void printEndLog(String shellName, String logFile, long startTime) {
        try {
            Duration timeTakenToExec = Duration.ofNanos(System.nanoTime() - startTime);
            FileUtils.writeStringToFile(new File(logFile), "\r\n" + StandardConsoleLogDto.EOFConsoleLogFlag.FLAG + "Run shell:[" + shellName + "] in " + (double) timeTakenToExec.toMillis() / 1000.0 + " seconds", StandardCharsets.UTF_8, true);
        } catch (Exception e) {
            log.error(ExceptionUtils.getStackTrace(e));
        }
    }
}
