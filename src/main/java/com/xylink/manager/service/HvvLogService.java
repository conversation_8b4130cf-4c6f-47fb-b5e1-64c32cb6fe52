package com.xylink.manager.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.service.base.K8sSvcService;
import com.xylink.manager.service.cache.bean.NodeAddressCache;
import com.xylink.manager.service.cache.service.ICacheService;
import com.xylink.util.Ipv6Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @Author: liyang
 * @DateTime: 2023/5/18 2:39 下午
 **/
@Service
@Slf4j
public class HvvLogService {
    @Autowired
    private ICacheService cacheService;

    @Autowired
    private K8sSvcService k8sSvcService;

    @Autowired
    private ServerLogService serverLogService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${hvv.log.root.path}")
    private String rootHvvPath;

    private final String LOGAGENT_HVV_LOG = "/logagent/hvv/log/";
    private final String LOGAGENT_HVV_LOG_DOWNLOAD = "/logagent/hvv/download";

    public ArrayNode getInfo(String nodeName) {
        String address = cacheService.cacheNodeByName(nodeName).getStatus().getAddresses().stream()
                .filter(x -> x.getType().equalsIgnoreCase(Constants.INTERNAL_IP)).findFirst().orElse(new NodeAddressCache()).getAddress();
        String loagentPodIp = k8sSvcService.getLogAgentPodIpByNodeIp(address);
        String url = "http://" + Ipv6Util.handlerIpv6Addr(loagentPodIp) + ":" + NetworkConstants.LOGAGENT_PORT + LOGAGENT_HVV_LOG;
        log.info("[HvvLogService.getInfo] start get {} dir", nodeName);
        ResponseEntity<String> entity;
        try {
            entity = restTemplate.getForEntity(url, String.class);
        } catch (Exception e) {
            log.debug("[HvvLogService.getInfo] failed , try to req the configured IP in all-ip", e);
            //兼容nat
            entity = retryGetInfo(nodeName);
        }
        return parseResponseToArrayNode(entity);
    }

    private ResponseEntity<String> retryGetInfo(String nodeName) {
        String address = cacheService.cacheNodeByName(nodeName).getStatus().getAddresses().stream()
                .filter(x -> x.getType().equalsIgnoreCase(Constants.INTERNAL_IP)).findFirst().orElse(new NodeAddressCache()).getAddress();
        String loagentPodIp = k8sSvcService.getLogAgentPodIpByNodeIp(address);
        String url = "http://" + Ipv6Util.handlerIpv6Addr(loagentPodIp) + ":" + NetworkConstants.LOGAGENT_PORT + LOGAGENT_HVV_LOG;
        ResponseEntity<String> entity = null;
        try {
            entity = restTemplate.getForEntity(url, String.class);
        } catch (Exception e) {
            log.error("[HvvLogService.retryGetInfo] failed", e);
        }
        return entity;
    }

    public ArrayNode getLogInfo(String nodeName, String path) {
        String address = cacheService.cacheNodeByName(nodeName).getStatus().getAddresses().stream()
                .filter(x -> x.getType().equalsIgnoreCase(Constants.INTERNAL_IP)).findFirst().orElse(new NodeAddressCache()).getAddress();
        String loagentPodIp = k8sSvcService.getLogAgentPodIpByNodeIp(address);
        String url = "http://" + Ipv6Util.handlerIpv6Addr(loagentPodIp) + ":" + NetworkConstants.LOGAGENT_PORT + LOGAGENT_HVV_LOG + path;
        log.info("[HvvLogService.getLogInfo] start get {}:{} dir", nodeName, path);
        ResponseEntity<String> entity;
        try {
            entity = restTemplate.getForEntity(url, String.class);
        } catch (Exception e) {
            log.debug("[HvvLogService.getLogInfo] failed , try to req the configured IP in all-ip", e);
            //兼容nat
            entity = retryGetLogInfo(nodeName, path);
        }
        return parseResponseToArrayNode(entity);
    }

    private ResponseEntity<String> retryGetLogInfo(String nodeName, String path) {
        String address = cacheService.cacheNodeByName(nodeName).getStatus().getAddresses().stream()
                .filter(x -> x.getType().equalsIgnoreCase(Constants.INTERNAL_IP)).findFirst().orElse(new NodeAddressCache()).getAddress();
        String loagentPodIp = k8sSvcService.getLogAgentPodIpByNodeIp(address);
        String url = "http://" + Ipv6Util.handlerIpv6Addr(loagentPodIp) + ":" + NetworkConstants.LOGAGENT_PORT + LOGAGENT_HVV_LOG + path;
        ResponseEntity<String> entity = null;
        try {
            entity = restTemplate.getForEntity(url, String.class);
        } catch (Exception e) {
            log.error("[HvvLogService.retryGetLogInfo] failed", e);
        }
        return entity;
    }

    public void downloadHvvLog(String nodeName, String logPath, HttpServletResponse response) {
        String nodeIp = cacheService.cacheNodeByName(nodeName).getStatus().getAddresses().stream()
                .filter(x -> x.getType().equalsIgnoreCase(Constants.INTERNAL_IP)).findFirst().orElse(new NodeAddressCache()).getAddress();
        String fileName = logPath.substring(logPath.lastIndexOf("/") + 1);
        serverLogService.downloadLog(logPath, LOGAGENT_HVV_LOG_DOWNLOAD, response, nodeIp, fileName);
    }

    private ArrayNode parseResponseToArrayNode(ResponseEntity<String> entity) {
        if (entity == null || StringUtils.isBlank(entity.getBody())) {
            return objectMapper.createArrayNode();
        }
        try {
            JsonNode jsonNode = objectMapper.readTree(entity.getBody());
            if (jsonNode.isArray()) {
                return (ArrayNode) jsonNode;
            }
        } catch (IOException e) {
            log.error("[HvvLogService.parseResponseToArrayNode] failed to parse response", e);
        }
        return objectMapper.createArrayNode();
    }
}