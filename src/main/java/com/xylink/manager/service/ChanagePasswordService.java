package com.xylink.manager.service;

import com.xylink.config.RoleConfig;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.WebException;
import com.xylink.config.security.LicenseAuthenticationToken;
import com.xylink.manager.controller.dto.ChangePasswordSetting;
import com.xylink.manager.service.db.JasyptService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

/**
 * Created by l<PERSON><PERSON><PERSON> on 2017/9/7.
 */

@Service
public class ChanagePasswordService {


    private static final Logger logger = LoggerFactory.getLogger(ChanagePasswordService.class);

    private static final String ADMIN_PWD="admin.pwd";

    @Value("${server.user.config.dir}")
    private String userConfigDir;

    @Autowired
    private RoleConfig roles;

    @Autowired
    private JasyptService jasyptService;

    @Autowired
    private PrivateDataService privateDataService;

    @Autowired
    private ChangePasswordValidityService changePasswordValidityService;


    public boolean changePassword(ChangePasswordSetting pwdSetting) {
        String encrypt = jasyptService.encrypt(pwdSetting.getNew_password());
        privateDataService.savePwd(pwdSetting.getUser(), encrypt);
        return true;
    }

    public String getPwd(String user) {
        String pwd = privateDataService.getPwd(user);
        return StringUtils.isBlank(pwd) ? roles.users().get(user) : pwd;
    }

    public boolean check(String pwd, String user) {
        if (StringUtils.isBlank(user)) {
            throw new WebException(ErrorStatus.ERROR_USER);
        }

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication instanceof LicenseAuthenticationToken) {
            LicenseAuthenticationToken devAuthentication = (LicenseAuthenticationToken) authentication;
            if (devAuthentication.getLicense().equals(pwd)) {
                return true;
            }
        } else {
            String adminPwd = getPwd(user);
            if (adminPwd.equals(pwd)) {
                return true;
            }
        }
        throw new WebException(ErrorStatus.PWD_ERROR);
    }
}
