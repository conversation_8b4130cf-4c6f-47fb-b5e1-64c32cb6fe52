package com.xylink.manager.service;

import com.xylink.manager.model.em.BackupFileType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/12/28/15:26
 */
@Service
public class BackUpNotifyService {

    private static final Logger log = LoggerFactory.getLogger(BackUpNotifyService.class);

    private static final String SPLIT = "-";
    private static final String BACK_UP_SPLIT = "_backup_";
    private static final long defaultBackUpTime = 10 * 60 * 1000L;
    private static final long hbaseBackUpTime = 30 * 60 * 1000L;
    private static final Set<String> notifySet = new HashSet<>(10);

    /**
     * 添加key
     */
    public void putBackUpNotify(int type, String fileName) {
        BackupFileType backupFileType = BackupFileType.valueOf(type);
        if (null == backupFileType) {
            log.error("backupFileType is null, type {}, fileName {}", type, fileName);
            return;
        }
        int indexOf = fileName.indexOf(".");
        if (indexOf <= 0) {
            log.error("lastIndexOf is error, type {}, fileName {}", type, fileName);
            return;
        }
        String tempFileName = fileName.substring(0, indexOf);
        String[] backup_s = tempFileName.split(BACK_UP_SPLIT);
        if (backup_s.length != 2) {
            log.error("fileName is error, type {}, fileName {}", type, fileName);
            return;
        }
        String time = backup_s[1];
        long timeMillis;
        try {
            timeMillis = Long.parseLong(time);
        } catch (Exception e) {
            log.error("fileName is error, type {}, fileName {}", type, fileName);
            return;
        }
        removeExpiredKey(timeMillis);
        String key = backup_s[0] + SPLIT + backupFileType.name() + SPLIT + time;
        notifySet.add(key);
    }

    /**
     * 删除过期key
     */
    private void removeExpiredKey(long timeMillis) {
        notifySet.stream().sorted().forEach(oldKey -> {
            String[] split = oldKey.split(SPLIT);
            if (split.length != 3) {
                notifySet.remove(oldKey);
            }
            long oldTimeMillis = 0;
            try {
                oldTimeMillis = Long.parseLong(split[2]);
            } catch (Exception e) {
                notifySet.remove(oldKey);
            }
            if (timeMillis - oldTimeMillis >= 10 * 60 * 1000) {
                notifySet.remove(oldKey);
            }
        });
    }

    /**
     * 检测是否收到备份通知
     *
     * @param fileName 备份文件名称
     */
    public boolean checkBackUpNotify(BackupFileType backupFileType, String fileName) {
        int indexOf = fileName.indexOf(".");
        if (indexOf <= 0) {
            log.error("lastIndexOf is error, fileName {}", fileName);
            return true;
        }
        String tempFileName = fileName.substring(0, indexOf);
        String[] backup_s = tempFileName.split(BACK_UP_SPLIT);
        if (backup_s.length != 2) {
            log.error("fileName is error, fileName {}", fileName);
            return true;
        }
        String time = backup_s[1];
        String key = backup_s[0] + SPLIT + backupFileType.name() + SPLIT + time;
        long overTime = BackupFileType.HbaseType == backupFileType ? hbaseBackUpTime : defaultBackUpTime;
        long startTime = System.currentTimeMillis();
        while (true) {
            if (System.currentTimeMillis() - startTime > overTime) {
                return true;
            }
            if (notifySet.contains(key)) {
                notifySet.remove(key);
                return false;
            }
            try {
                TimeUnit.SECONDS.sleep(2);
            } catch (InterruptedException e) {
                log.error("checkBackUpNotify sleep error!", e);
            }
        }
    }

}
