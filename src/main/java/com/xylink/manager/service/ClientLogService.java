package com.xylink.manager.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.controller.dto.ClientLogReq;
import com.xylink.manager.controller.dto.ClientLogRes;
import com.xylink.manager.model.common.Page;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.util.FilePathUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
public class ClientLogService {
    private static final Logger logger = LoggerFactory.getLogger(ClientLogService.class);

    @Autowired
    private ServerListService serverListService;
    @Autowired
    private IDeployService deployService;
    @Autowired
    private ServerLogService serverLogService;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private ObjectMapper objectMapper;

    private String initUrl(String search) {
        String mainIp = serverListService.getMainNodeInternalIP();
        if (StringUtils.isBlank(search)) {
            return "http://" + mainIp + ":11111/api/rest/v1/log/data/query/page";
        } else {
            return "http://" + mainIp + ":11111/api/rest/v1/log/data/query";
        }
    }

    private String initBody(ClientLogReq logReq) {
        ObjectNode param = JsonNodeFactory.instance.objectNode();
        param.put("pageIndex", Objects.isNull(logReq.getCurrent()) ? 1 : logReq.getCurrent());
        param.put("pageCount", Objects.isNull(logReq.getPageSize()) ? 30 : logReq.getPageSize());
        param.put("date", logReq.getDate());
        if (StringUtils.isNotBlank(logReq.getSearch())) {
            param.put("value", logReq.getSearch());
        }
        return param.toString();
    }

    public Page<ClientLogRes> pageClientLog(ClientLogReq logReq) {
        try {
            String url = initUrl(logReq.getSearch());
            String body = initBody(logReq);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> requestEntity = new HttpEntity<>(body, headers);

            ResponseEntity<JsonNode> entity = restTemplate.postForEntity(url, requestEntity, JsonNode.class);
            if (entity.getStatusCodeValue() == HttpStatus.OK.value()) {
                List<ClientLogRes> records;
                JsonNode result = entity.getBody();
                if (Objects.isNull(result)) {
                    throw new ServerException("api返回空数据");
                }
                JsonNode dataNode = result.get("data");
                if (dataNode == null || dataNode.size() <= 0) {
                    records = Collections.emptyList();
                } else {
                    records = objectMapper.readValue(dataNode.toString(), new TypeReference<List<ClientLogRes>>(){});
                }
                FilePathUtils.filterRootInfo(records);
                return new Page<>(
                    result.get("pageIndex").asInt(),
                    result.get("pageCount").asInt(), 
                    result.get("allCount").asInt(),
                    records
                );
            } else {
                logger.error("call logserver api error, {}, {}", entity.getStatusCode(), entity.getBody());
                return new Page<>(1, 30, 0, Collections.emptyList());
            }
        } catch (Exception e) {
            logger.error("pageClientLog error", e);
            return new Page<>(1, 30, 0, Collections.emptyList());
        }
    }

    public void downloadClientLog(String logPath, String inputFileName, HttpServletResponse response) {
        String nodeIp = deployService.listNodesByAppLabel("logserver").get(0).getIp();
        if (StringUtils.isEmpty(nodeIp))
            return;
        String fileName = logPath.substring(logPath.lastIndexOf("/") + 1);
        logger.info("download fileName:[{}],inputFileName:[{}] ", fileName, inputFileName);

        serverLogService.downloadLog(logPath, serverLogService.CLIENT_LOG_PREFIX, response, nodeIp, fileName, inputFileName);
    }
}
