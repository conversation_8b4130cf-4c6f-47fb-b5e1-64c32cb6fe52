package com.xylink.manager.service;

import com.xylink.config.Constants;
import com.xylink.manager.controller.bo.inspect.InspectionConditionBO;
import com.xylink.manager.controller.dto.inspect.InspectResult;
import com.xylink.manager.controller.dto.inspect.InspectionInstanceDTO;
import com.xylink.manager.service.inspect.InspectBigDataHandler;
import com.xylink.manager.service.inspect.InspectHostHandler;
import com.xylink.manager.service.inspect.InspectMiddlewareHandler;
import com.xylink.manager.service.inspect.InspectServiceHandler;
import com.xylink.util.InspectJDBCUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @date 2023/04/18/16:55
 */
@Service
public class InspectAsyncService {

    @Autowired
    private InspectJDBCUtils inspectJDBCUtils;

    @Autowired
    private InspectHostHandler inspectHostHandler;

    @Autowired
    private InspectBigDataHandler inspectBigDataHandler;

    @Autowired
    private InspectServiceHandler inspectServiceHandler;

    @Autowired
    private InspectMiddlewareHandler inspectMiddlewareHandler;

    /**
     * 执行巡检
     */
    @Async
    public void inspectExecute(long id, InspectionConditionBO inspectionConditionBO) {
        InspectResult result = doHandler(id, inspectionConditionBO);
        InspectionInstanceDTO inspectionInstance = new InspectionInstanceDTO();
        inspectionInstance.setId(id);
        inspectionInstance.setJobStatus(3);
        List<String> serverItemList = inspectionConditionBO.getServerItemList();
        if (CollectionUtils.isNotEmpty(serverItemList) && serverItemList.contains(Constants.INSPECT_ITEM_INSPECTION)){
            inspectionInstance.setJobStatus(2);
        }
        inspectionInstance.setRiskNumber(result.getRiskNumber());
        inspectionInstance.setNormalNumber(result.getNormalNumber());
        inspectionInstance.setExceptNumber(result.getExceptNumber());
        inspectionInstance.setFinalTime(System.currentTimeMillis());
        try {
            inspectJDBCUtils.updateInspectInstance(inspectionInstance);
        } catch (Exception e) {
            //终止巡检，修改数据库状态为异常
            inspectionInstance.setJobStatus(-1);
            inspectJDBCUtils.updateInspectInstance(inspectionInstance);
        }
    }

    /**
     * 异步执行巡检
     */
    private InspectResult doHandler(long id, InspectionConditionBO inspectionConditionBO) {
        InspectResult result = InspectResult.empty();
        ExecutorService executor = Executors.newFixedThreadPool(4);
        List<CompletableFuture<Void>> futureList = new ArrayList<>(4);
        //大数据巡检
        CompletableFuture<Void> bigDataCompletableFuture = CompletableFuture.runAsync(() -> inspectBigDataHandler.exec(id, inspectionConditionBO.getBigDataItemList(), result), executor);
        futureList.add(bigDataCompletableFuture);
        //中间件巡检
        CompletableFuture<Void> middlewareCompletableFuture = CompletableFuture.runAsync(() -> inspectMiddlewareHandler.exec(id, inspectionConditionBO.getMiddlewareItemList(), result), executor);
        futureList.add(middlewareCompletableFuture);
        //主机巡检
        CompletableFuture<Void> hostCompletableFuture = CompletableFuture.runAsync(() -> inspectHostHandler.exec(id, inspectionConditionBO.getHostItemList(), result), executor);
        futureList.add(hostCompletableFuture);
        //服务巡检
        CompletableFuture<Void> serviceCompletableFuture = CompletableFuture.runAsync(() -> inspectServiceHandler.exec(id, inspectionConditionBO.getServerItemList(), result), executor);
        futureList.add(serviceCompletableFuture);
        //等待结果
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
        return result;
    }

}
