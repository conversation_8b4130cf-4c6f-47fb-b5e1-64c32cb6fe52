package com.xylink.manager.service.inspect;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.controller.dto.inspect.InspectionTaskMiddlewareDTO;
import com.xylink.manager.controller.dto.inspect.ZookeeperConfigDTO;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.model.em.InspectLadderEnum;
import com.xylink.manager.model.em.InspectionMetricKeyEnum;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.util.InspectJDBCUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/04/14/15:14
 */
@Service
public class ZookeeperInspector {

    private static final Logger log = LoggerFactory.getLogger(MysqlInspector.class);

    @Autowired
    private K8sService k8sService;
    @Autowired
    private IDeployService deployService;
    @Autowired
    private InspectJDBCUtils inspectJDBCUtils;

    private static final int DEFAULT_PORT = 2181;
    private static final String DEFAULT_TASK_ITEM = "zookeeper";

    /**
     * 巡检
     */
    public boolean inspect(long instanceId) {
        ZookeeperConfigDTO config = getZookeeperConfig();
        try {
            return inspectZookeeper(instanceId, config);
        } catch (Exception e) {
            log.error("[inspection] error, zookeeper host " + config.getHost(), e);
            return false;
        }
    }

    /**
     * zookeeper巡检
     */
    private boolean inspectZookeeper(long instanceId, ZookeeperConfigDTO config) {
        Pod pod = getZookeeperPod();
        if (null == pod) {
            List<InspectionTaskMiddlewareDTO> middlewareList = saveFailedMetricTasks(instanceId, config.getName());
            inspectJDBCUtils.insertInspectTaskMiddleware(middlewareList);
            return false;
        }
        boolean valid = false;
        List<InspectionTaskMiddlewareDTO> middlewareList = new ArrayList<>(6);
        try {
            middlewareList.add(agentLive(instanceId, pod, config));
            Map<String, String> infoMap = getZookeeperInfoMap(pod, config);
            if (infoMap == null || infoMap.isEmpty()) {
                middlewareList = saveFailedMetricTasks(instanceId, config.getName());
            } else {
                middlewareList.add(latency(instanceId, config.getName(), infoMap));
                middlewareList.add(received(instanceId, config.getName(), infoMap));
                middlewareList.add(sent(instanceId, config.getName(), infoMap));
                middlewareList.add(connectedClients(instanceId, config.getName(), infoMap));
                middlewareList.add(znodeCount(instanceId, config.getName(), infoMap));
                valid = true;
            }
        } catch (Exception e) {
            middlewareList = saveFailedMetricTasks(instanceId, config.getName());
        }
        inspectJDBCUtils.insertInspectTaskMiddleware(middlewareList);
        return valid;
    }

    /**
     * 获取zookeeper Pod对象
     */
    private Pod getZookeeperPod() {
        Optional<Pod> zookeeperClusterPod = k8sService.getPodWithLabelInApp("private-zookeeper-cluster");
        if (zookeeperClusterPod.isPresent()) {
            return zookeeperClusterPod.get();
        }
        Optional<Pod> zookeeperPod = k8sService.getPodWithLabelInApp("private-zookeeper");
        return zookeeperPod.orElse(null);
    }

    /**
     * 节点数
     */
    public InspectionTaskMiddlewareDTO znodeCount(long instanceId, String taskItem, Map<String, String> infoMap) {
        String metricKey = InspectionMetricKeyEnum.ZK_NODE.getMetricKey();
        String zkZnodeCount = infoMap.get("zk_znode_count");
        if (StringUtils.isBlank(zkZnodeCount)) {
            // 指标获取失败
            return new InspectionTaskMiddlewareDTO(instanceId, taskItem, metricKey, "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
        }
        String result = "zk_znode_count=" + zkZnodeCount;
        return new InspectionTaskMiddlewareDTO(instanceId, taskItem, metricKey, result, InspectLadderEnum.NORMAL.getValue());
    }

    /**
     * 连接数
     */
    public InspectionTaskMiddlewareDTO connectedClients(long instanceId, String taskItem, Map<String, String> infoMap) {
        String metricKey = InspectionMetricKeyEnum.ZK_CONNECTED_CLIENTS.getMetricKey();
        String zkNumAliveConnections = infoMap.get("zk_num_alive_connections");
        if (StringUtils.isBlank(zkNumAliveConnections)) {
            // 指标获取失败
            return new InspectionTaskMiddlewareDTO(instanceId, taskItem, metricKey, "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
        }
        long value = Long.parseLong(zkNumAliveConnections);
        String result = "连接活跃数=" + zkNumAliveConnections;
        // 活跃数大于两百，高风险
        if (value > 200) {
            return new InspectionTaskMiddlewareDTO(instanceId, taskItem, metricKey, result, InspectLadderEnum.RISK.getValue());
        }
        return new InspectionTaskMiddlewareDTO(instanceId, taskItem, metricKey, result, InspectLadderEnum.NORMAL.getValue());
    }

    /**
     * 发包数
     */
    public InspectionTaskMiddlewareDTO sent(long instanceId, String taskItem, Map<String, String> infoMap) {
        String metricKey = InspectionMetricKeyEnum.ZK_SEND.getMetricKey();
        String zkPacketsSent = infoMap.get("zk_packets_sent");
        if (StringUtils.isBlank(zkPacketsSent)) {
            // 指标获取失败
            return new InspectionTaskMiddlewareDTO(instanceId, taskItem, metricKey, "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
        }
        String result = "zk_packets_sent=" + zkPacketsSent;
        return new InspectionTaskMiddlewareDTO(instanceId, taskItem, metricKey, result, InspectLadderEnum.NORMAL.getValue());
    }

    /**
     * 收包数
     */
    public InspectionTaskMiddlewareDTO received(long instanceId, String taskItem, Map<String, String> infoMap) {
        String metricKey = InspectionMetricKeyEnum.ZK_RECEIVED.getMetricKey();
        String zkPacketsReceived = infoMap.get("zk_packets_received");
        if (StringUtils.isBlank(zkPacketsReceived)) {
            // 指标获取失败
            return new InspectionTaskMiddlewareDTO(instanceId, taskItem, metricKey, "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
        }
        String result = "zk_packets_received=" + zkPacketsReceived;
        return new InspectionTaskMiddlewareDTO(instanceId, taskItem, metricKey, result, InspectLadderEnum.NORMAL.getValue());
    }

    /**
     * 请求延时
     */
    public InspectionTaskMiddlewareDTO latency(long instanceId, String taskItem, Map<String, String> infoMap) {
        String metricKey = InspectionMetricKeyEnum.ZK_LATENCY.getMetricKey();
        String zkMaxLatency = infoMap.get("zk_max_latency");
        String zkMinLatency = infoMap.get("zk_min_latency");
        String zkAvgLatency = infoMap.get("zk_avg_latency");
        if (StringUtils.isBlank(zkMaxLatency) || StringUtils.isBlank(zkMinLatency) || StringUtils.isBlank(zkAvgLatency)) {
            // 指标获取失败
            return new InspectionTaskMiddlewareDTO(instanceId, taskItem, metricKey, "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
        }
        String result = "zk_max_latency=" + zkMaxLatency + "\nzk_min_latency=" + zkMinLatency + "\nzk_avg_latency" + zkAvgLatency;
        return new InspectionTaskMiddlewareDTO(instanceId, taskItem, metricKey, result, InspectLadderEnum.NORMAL.getValue());
    }

    /**
     * 探活
     */
    public InspectionTaskMiddlewareDTO agentLive(long instanceId, Pod pod, ZookeeperConfigDTO config) {
        String metricKey = InspectionMetricKeyEnum.ZK_AGENT_LIVE.getMetricKey();
        String exec = "echo ruok | nc " + config.getHost() + " " + config.getPort();
        String s = deployService.executeCommandForPod(pod.getPodName(), pod.getNamespace(), new String[]{"/bin/bash", "-c", exec});
        if (s.contains("imok")) {
            return new InspectionTaskMiddlewareDTO(instanceId, config.getName(), metricKey, "zookeeper 正常运行", InspectLadderEnum.NORMAL.getValue());
        }
        return new InspectionTaskMiddlewareDTO(instanceId, config.getName(), metricKey, "zookeeper 未运行", InspectLadderEnum.EXCEPT.getValue());
    }

    /**
     * mntr 命令返回结果:
     * zk_server_state standalone
     * zk_ephemerals_count     24
     * zk_num_alive_connections        31
     * zk_avg_latency  0.128
     * zk_outstanding_requests 0
     * zk_znode_count  1511
     * zk_global_sessions      30
     * zk_non_mtls_remote_conn_count   0
     * zk_last_client_response_size    16
     * zk_packets_sent 1195693
     * zk_packets_received     1193063
     *
     * @param pod pod 名
     * @return .
     */
    public Map<String, String> getZookeeperInfoMap(Pod pod, ZookeeperConfigDTO config) {
        String exec = "echo mntr | nc " + config.getHost() + " " + config.getPort();
        String s = deployService.executeCommandForPod(pod.getPodName(), pod.getNamespace(), new String[]{"/bin/bash", "-c", exec});
        if (StringUtils.isBlank(s)) {
            return null;
        }
        Map<String, String> infoMap = new HashMap<>();
        // 按照行分割
        String[] lines = s.split("\n");
        for (String line : lines) {
            if (StringUtils.isBlank(line)) {
                continue;
            }
            // 按照 空格 分割
            String[] split = line.split("\\s");
            if (split.length == 2) {
                infoMap.put(split[0], split[1]);
            }
        }
        return infoMap;
    }

    /**
     * 获取异常指标
     */
    private List<InspectionTaskMiddlewareDTO> saveFailedMetricTasks(long instanceId, String taskItem) {
        InspectionTaskMiddlewareDTO agentLive = new InspectionTaskMiddlewareDTO(instanceId, taskItem, InspectionMetricKeyEnum.ZK_AGENT_LIVE.getMetricKey(), "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
        InspectionTaskMiddlewareDTO latency = new InspectionTaskMiddlewareDTO(instanceId, taskItem, InspectionMetricKeyEnum.ZK_LATENCY.getMetricKey(), "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
        InspectionTaskMiddlewareDTO received = new InspectionTaskMiddlewareDTO(instanceId, taskItem, InspectionMetricKeyEnum.ZK_RECEIVED.getMetricKey(), "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
        InspectionTaskMiddlewareDTO sent = new InspectionTaskMiddlewareDTO(instanceId, taskItem, InspectionMetricKeyEnum.ZK_SEND.getMetricKey(), "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
        InspectionTaskMiddlewareDTO connectedClients = new InspectionTaskMiddlewareDTO(instanceId, taskItem, InspectionMetricKeyEnum.ZK_CONNECTED_CLIENTS.getMetricKey(), "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
        InspectionTaskMiddlewareDTO znodeCount = new InspectionTaskMiddlewareDTO(instanceId, taskItem, InspectionMetricKeyEnum.ZK_NODE.getMetricKey(), "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
        List<InspectionTaskMiddlewareDTO> taskList = new ArrayList<>(6);
        taskList.add(agentLive);
        taskList.add(latency);
        taskList.add(received);
        taskList.add(sent);
        taskList.add(connectedClients);
        taskList.add(znodeCount);
        return taskList;
    }

    /**
     * 获取zookeeper配置信息
     */
    private ZookeeperConfigDTO getZookeeperConfig() {
        Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        String address = allIp.get(NetworkConstants.ZOOKEEPER_IP);
        Optional<Pod> zookeeperClusterPod = k8sService.getPodWithLabelInApp("private-zookeeper-cluster");
        if (zookeeperClusterPod.isPresent()) {
            address = allIp.get(NetworkConstants.MASTER_ZOOKEEPER_IP);
        }
        ZookeeperConfigDTO config = new ZookeeperConfigDTO();
        config.setHost(address);
        config.setPort(DEFAULT_PORT);
        config.setName(DEFAULT_TASK_ITEM);
        return config;
    }

}
