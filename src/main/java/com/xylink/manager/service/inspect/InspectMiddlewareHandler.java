package com.xylink.manager.service.inspect;

import com.xylink.manager.controller.dto.inspect.InspectResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/04/12/16:41
 */
@Service
public class InspectMiddlewareHandler extends AbstractInspectHandler {

    @Autowired
    private MysqlInspector mysqlInspector;
    @Autowired
    private kafkaInspector kafkaInspector;
    @Autowired
    private RedisInspector redisInspector;
    @Autowired
    private ZookeeperInspector zookeeperInspector;

    @Override
    public InspectResult exec(long instanceId, List<String> itemKeyList) {
        InspectResult inspectResult = InspectResult.empty();
        for (String itemKey : itemKeyList) {
            boolean valid = false;
            switch (itemKey) {
                case "mysql" :
                    valid = mysqlInspector.inspect(instanceId);
                    break;
                case "kafka" :
                    valid = kafkaInspector.inspect(instanceId);
                    break;
                case "redis" :
                    valid = redisInspector.inspect(instanceId);
                    break;
                case "zookeeper" :
                    valid = zookeeperInspector.inspect(instanceId);
                    break;
                default: continue;
            }
            if (valid) {
                inspectResult.setNormalNumber(inspectResult.getNormalNumber() + 1);
            } else {
                inspectResult.setExceptNumber(inspectResult.getExceptNumber() + 1);
            }
        }
        return inspectResult;
    }

}
