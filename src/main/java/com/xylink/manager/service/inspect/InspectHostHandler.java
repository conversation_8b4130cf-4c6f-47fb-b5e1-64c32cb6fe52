package com.xylink.manager.service.inspect;

import com.fasterxml.jackson.databind.JsonNode;
import com.xylink.config.StatisItem;
import com.xylink.manager.controller.dto.alert.AlertConfigDto;
import com.xylink.manager.controller.dto.inspect.InspectResult;
import com.xylink.manager.controller.dto.inspect.InspectionTaskHostDTO;
import com.xylink.manager.model.KuberDiskInfo;
import com.xylink.manager.model.KuberNodeInfo;
import com.xylink.manager.model.em.InspectLadderEnum;
import com.xylink.manager.service.AlertConfigService;
import com.xylink.manager.service.KuberNodeInfoService;
import com.xylink.manager.service.PrivateDataService;
import com.xylink.manager.service.cache.bean.NodeCache;
import com.xylink.manager.service.cache.service.ICacheService;
import com.xylink.util.InspectJDBCUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2023/04/12/17:17
 */
@Service
public class InspectHostHandler extends AbstractInspectHandler {
    private static final Logger log = LoggerFactory.getLogger(InspectHostHandler.class);
    @Autowired
    private PrivateDataService privateDataService;
    @Autowired
    private KuberNodeInfoService kuberNodeInfoService;
    @Autowired
    private AlertConfigService alertConfigService;
    @Autowired
    private InspectJDBCUtils inspectJDBCUtils;
    private static final String DEFAULT_DATA_DISK_NAME = "/dev/mapper/vg_xylink-lv_xylink";
    private static final String DISK_KEY = "NODE_DISK";
    @Autowired
    private ICacheService cacheService;

    @Override
    public InspectResult exec(long id, List<String> itemKeyList) {
        if (CollectionUtils.isEmpty(itemKeyList)) {
            return InspectResult.empty();
        }
        AtomicInteger normalNumber = new AtomicInteger();
        AtomicInteger riskNumber = new AtomicInteger();
        AtomicInteger exceptNumber = new AtomicInteger();
        AtomicBoolean includeStatisItem = new AtomicBoolean(false);
        AtomicBoolean includeDiskInfo = new AtomicBoolean(false);
        AtomicBoolean includeCPUInfo = new AtomicBoolean(false);
        AtomicBoolean includeMemoryInfo = new AtomicBoolean(false);
        if (itemKeyList.contains(StatisItem.NODE_CPU.name())) {
            includeStatisItem.set(true);
            includeCPUInfo.set(true);
        }
        if (itemKeyList.contains(StatisItem.NODE_MEMORY.name())) {
            includeStatisItem.set(true);
            includeMemoryInfo.set(true);
        }
        if (itemKeyList.contains(DISK_KEY)) {
            includeDiskInfo.set(true);
        }
        List<NodeCache> nodeCacheList = cacheService.cacheNodeList();
        JsonNode diskConfig = privateDataService.getDiskConfigCache();
        List<InspectionTaskHostDTO> hostDTOS = Collections.synchronizedList(new ArrayList<>());
        nodeCacheList.parallelStream().forEach(nodeCache -> {
            String nodeName = nodeCache.getMetadata().getName();
            KuberNodeInfo nodeInfo;
            InspectionTaskHostDTO inspectionTaskHostDTO = new InspectionTaskHostDTO();
            inspectionTaskHostDTO.setHostName(nodeName);
            inspectionTaskHostDTO.setInstanceId(id);
            Long cpuUsage = 0L;
            Long memUsage = 0L;
            Long sysDisk1Remain = 0L;
            Long dataDiskRemain = 0L;
            StringBuffer taskValue = new StringBuffer();
            try {
                nodeInfo = kuberNodeInfoService.generateKuberNodeInfoForInspect(nodeCache, diskConfig, includeDiskInfo.get(), includeStatisItem.get());
                AlertConfigDto alertConfigDto = alertConfigService.getAlertConfigByname(nodeName);
                int cpuLimit = alertConfigDto.getCpuThreshold();
                int memLimit = alertConfigDto.getMemoryThreshold();
                int systemDisklimit = alertConfigDto.getSystemDiskThreshold();
                int dataDisklimit = alertConfigDto.getDataDiskThreshold();
                boolean cpuOverThreshold = false;
                boolean memoryOverThreshold = false;
                boolean sd1OverThreshold = false;
                boolean ddOverThreshold = false;

                cpuUsage = nodeInfo.getCpuUsage() == null || !includeCPUInfo.get() ? 0L : nodeInfo.getCpuUsage().longValue();
                memUsage = nodeInfo.getMemoryUsage() == null || !includeMemoryInfo.get() ? 0L : nodeInfo.getMemoryUsage().longValue();
                List<KuberDiskInfo> diskInfos = nodeInfo.getKuberDiskInfos();
                for (KuberDiskInfo diskInfo : diskInfos) {
                    if (diskInfo.getDevice().equalsIgnoreCase(getDataDiskName(diskConfig))) {
                        dataDiskRemain = (diskInfo.getCapacity() - diskInfo.getUsage()) / 1024 / 1024 / 1024;
                    }

                    if (diskInfo.getDevice().equalsIgnoreCase(diskConfig.get("systemdisk1").asText())) {
                        sysDisk1Remain = (diskInfo.getCapacity() - diskInfo.getUsage()) / 1024 / 1024 / 1024;
                    }

                }
                if (sysDisk1Remain > 0 && sysDisk1Remain <= systemDisklimit) {
                    sd1OverThreshold = true;
                    taskValue.append("系统磁盘剩余容量小于阈值").append(systemDisklimit).append("G，");
                }

                if (dataDiskRemain > 0 && dataDiskRemain <= dataDisklimit) {
                    ddOverThreshold = true;
                    taskValue.append("数据磁盘剩余容量小于阈值").append(dataDisklimit).append("G，");
                }
                if (cpuUsage >= cpuLimit && includeCPUInfo.get()) {
                    cpuOverThreshold = true;
                    taskValue.append("CPU使用率超过阈值").append(cpuLimit).append("%，");
                }

                if (memUsage >= memLimit && includeMemoryInfo.get()) {
                    memoryOverThreshold = true;
                    taskValue.append("内存使用率超过阈值").append(memLimit).append("%，");
                }
                int ladder = InspectLadderEnum.NORMAL.getValue();
                if (cpuOverThreshold || memoryOverThreshold || sd1OverThreshold || ddOverThreshold) {
                    ladder = InspectLadderEnum.EXCEPT.getValue();
                    exceptNumber.getAndIncrement();
                } else if (cpuUsage == 0L && memUsage == 0L && sysDisk1Remain == 0L && dataDiskRemain == 0L) {
                    ladder = InspectLadderEnum.EXCEPT.getValue();
                    exceptNumber.getAndIncrement();
                    taskValue.append("主机信息获取失败，");
                } else {
                    normalNumber.getAndIncrement();
                }
                inspectionTaskHostDTO.setLadder(ladder);
            } catch (Exception e) {
                log.error("generateKuberNodeInfo error,nodeName:{}", nodeName, e);
                inspectionTaskHostDTO.setLadder(InspectLadderEnum.EXCEPT.getValue());
                exceptNumber.getAndIncrement();
                taskValue.append("主机信息获取失败，");
            }
            if (taskValue.length() > 0) {
                taskValue.setCharAt(taskValue.length() - 1, ' ');
            }
            inspectionTaskHostDTO.setTaskValue(taskValue.toString());
            inspectionTaskHostDTO.setCpuUsed(String.valueOf(cpuUsage));
            inspectionTaskHostDTO.setMemoryUsed(String.valueOf(memUsage));
            inspectionTaskHostDTO.setSystemDiskUsed(String.valueOf(sysDisk1Remain));
            inspectionTaskHostDTO.setDataDiskUsed(String.valueOf(dataDiskRemain));
            hostDTOS.add(inspectionTaskHostDTO);
        });
        inspectJDBCUtils.batchExecuteInspectHost(hostDTOS);
        return new InspectResult(normalNumber.get(), riskNumber.get(), exceptNumber.get());

    }

    private String getDataDiskName(JsonNode diskConfig){
        String dataDiskName = diskConfig.get("datadisk").asText();
        return StringUtils.isNotBlank(dataDiskName) ? dataDiskName : DEFAULT_DATA_DISK_NAME;
    }
}
