package com.xylink.manager.service.inspect;

import com.xylink.config.NetworkConstants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServiceErrorException;
import com.xylink.manager.controller.dto.inspect.InspectionTaskMiddlewareDTO;
import com.xylink.manager.controller.dto.inspect.KafkaConfigDTO;
import com.xylink.manager.model.em.InspectLadderEnum;
import com.xylink.manager.model.em.InspectionMetricKeyEnum;
import com.xylink.manager.service.base.K8sService;
import com.xylink.util.InspectJDBCUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/04/14/11:26
 */
@Service
public class kafkaInspector {

    private static final Logger log = LoggerFactory.getLogger(MysqlInspector.class);

    @Autowired
    private K8sService k8sService;

    @Autowired
    private InspectJDBCUtils inspectJDBCUtils;

    private static final int DEFAULT_PORT = 9093;
    private static final String DEFAULT_TASK_ITEM = "kafka";
    private static final String GROUP_ID = "manager-inspection";
    private static final String TEST_TOPIC = "manager-inspection-topic";


    /**
     * 巡检
     */
    public boolean inspect(long instanceId) {
        KafkaConfigDTO config = getMainKafkaConfig();
        try {
            return inspectKafka(instanceId, config);
        } catch (Exception e) {
            log.error("[inspection] error, kafka host " + config.getHost(), e);
        }
        return false;
    }

    /**
     * 获取数据库指标信息
     */
    private boolean inspectKafka(long instanceId, KafkaConfigDTO config) {
        boolean valid = false;
        InspectionTaskMiddlewareDTO middleware;
        List<InspectionTaskMiddlewareDTO> middlewareList = new ArrayList<>(1);
        Properties properties = constructProperties(config.getHost(), config.getPort());
        String metricKey = InspectionMetricKeyEnum.KAFKA_PRODUCED_AND_CONSUMED.getMetricKey();
        try {
            producedTestMessage(properties);
            consumeTestMessage(properties);
            valid = true;
            middleware = new InspectionTaskMiddlewareDTO(instanceId, config.getName(), metricKey, "正常生产消费数据", InspectLadderEnum.NORMAL.getValue());
        } catch (ServiceErrorException e) {
            middleware = new InspectionTaskMiddlewareDTO(instanceId, config.getName(), metricKey, e.getMessage(), InspectLadderEnum.EXCEPT.getValue());
        } catch (Exception e) {
            log.error("inspectKafka error, ", e);
            middleware = new InspectionTaskMiddlewareDTO(instanceId, config.getName(), metricKey, "kafka 连接失败", InspectLadderEnum.EXCEPT.getValue());
        }
        middlewareList.add(middleware);
        inspectJDBCUtils.insertInspectTaskMiddleware(middlewareList);
        return valid;
    }

    /**
     * 向测试 topic 发送 kafka 消息
     */
    private void producedTestMessage(Properties properties) {
        try (Producer<String, String> producer = new KafkaProducer<>(properties)) {
            RecordMetadata recordMetadata = producer.send(new ProducerRecord<>(TEST_TOPIC, "manager-inspection-test", "inspection-test")).get(5000L, TimeUnit.MILLISECONDS);
            log.info("inspect producer: partition=" + recordMetadata.partition());
        } catch (Exception e) {
            log.error("produce message error", e);
            throw new ServiceErrorException(ErrorStatus.KAFKA_PRODUCED_TEST_DATA_ERROR);
        }
    }

    /**
     * 消费测试 topic 中的消息
     */
    private void consumeTestMessage(Properties properties) {
        try (KafkaConsumer<String, String> consumer = new KafkaConsumer<>(properties)) {
            consumer.subscribe(Collections.singletonList(TEST_TOPIC));
            ConsumerRecords<String, String> records = consumer.poll(Duration.ofSeconds(10));
            if (records.isEmpty()){
                log.error("consumeTestMessage records isEmpty");
                throw new ServiceErrorException(ErrorStatus.KAFKA_PRODUCED_TEST_DATA_ERROR);
            }
            for (ConsumerRecord<String, String> record : records) {
                log.debug("inspect consumer: " + record.key() + "=" + record.value());
            }
        } catch (Exception e) {
            log.error("consume message error", e);
            throw new ServiceErrorException(ErrorStatus.KAFKA_CONSUME_TEST_DATA_ERROR);
        }
    }

    /**
     * 获取配置信息
     */
    private Properties constructProperties(String kafkaHost, int port) {
        Properties properties = new Properties();
        properties.put("bootstrap.servers", kafkaHost + ":" + port);
        properties.put("group.id", GROUP_ID);
        // 响应超时时间
        properties.put("request.timeout.ms", 3000);
        // 空闲超时时间
        properties.put("connections.max.idle.ms", 5000);
        // acks=all 消息在所有的 ISR 都被持久化成功后才告知生产者消息发送成功
        properties.put("acks", "all");
        // 重试次数
        properties.put("retries", 0);
        properties.put("auto.offset.reset", "earliest");
        properties.put("allow.auto.create.topics", false);
        properties.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        properties.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        properties.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        properties.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        return properties;
    }

    /**
     * 获取kafka配置
     */
    private KafkaConfigDTO getMainKafkaConfig() {
        Map<String, String> allIp = k8sService.getConfigmap("all-ip");
        String kafkaIp = allIp.get(NetworkConstants.KAFKA_INTERNAL_IP);
        KafkaConfigDTO kafkaConfig = new KafkaConfigDTO();
        kafkaConfig.setName(DEFAULT_TASK_ITEM);
        kafkaConfig.setHost(kafkaIp);
        kafkaConfig.setPort(DEFAULT_PORT);
        return kafkaConfig;
    }

}
