package com.xylink.manager.service.inspect;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.VodnetworkConstants;
import com.xylink.manager.controller.dto.inspect.InspectResult;
import com.xylink.manager.controller.dto.inspect.InspectSvcAvailableDto;
import com.xylink.manager.controller.dto.inspect.InspectionServiceListDTO;
import com.xylink.manager.controller.dto.inspect.InspectionTaskServiceDTO;
import com.xylink.manager.model.em.InspectLadderEnum;
import com.xylink.manager.service.cache.bean.ContainerCache;
import com.xylink.manager.service.cache.bean.ContainerStatusCache;
import com.xylink.manager.service.cache.bean.PodCache;
import com.xylink.manager.service.cache.service.ICacheService;
import com.xylink.util.InspectJDBCUtils;
import com.xylink.util.Ipv6Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/04/12/17:17
 */
@Service
@Slf4j
public class InspectServiceHandler extends AbstractInspectHandler {
    @Autowired
    private ICacheService cacheService;
    @Autowired
    private InspectJDBCUtils inspectJDBCUtils;
    @Autowired
    private RestTemplate restTemplate;
    private static final String STATUS_RUNNING = "Running";
    private static final String STATUS_SUCCEEDED = "Succeeded";
    private static final String inspectServiceUrl = "/api/rest/service-inspection/internal/instance/v1";
    private static final String SERVICE_VODNETWORK_VOD = "private-vodnetwork-vod";
    private static final String SERVICE_VODNETWORK_VODEDIT = "private-vodnetwork-vodedit";


    @Override
    public InspectResult exec(long id, List<String> itemKeyList) {
        AtomicInteger normalNumber = new AtomicInteger();
        AtomicInteger riskNumber = new AtomicInteger();
        AtomicInteger exceptNumber = new AtomicInteger();
        if (CollectionUtils.isEmpty(itemKeyList)) {
            return new InspectResult(normalNumber.get(), riskNumber.get(), exceptNumber.get());
        }
        List<PodCache> podCacheList = cacheService.cachePodList();
        podCacheList = podCacheList.parallelStream().filter(pod -> !pod.getMetadata().getOwnerReferences().stream()
                .anyMatch(ownerReference -> ownerReference.getKind().equalsIgnoreCase(Constants.Job))).collect(Collectors.toList());
        if (itemKeyList.contains(Constants.INSPECT_ITEM_LIVENESS)) {
            execServiceLiveness(podCacheList, id, normalNumber, riskNumber, exceptNumber);
        }
        if (itemKeyList.contains(Constants.INSPECT_ITEM_INSPECTION)) {
            execServiceInspection(id, podCacheList);
        }
        return new InspectResult(normalNumber.get(), riskNumber.get(), exceptNumber.get());
    }

    private void execServiceLiveness(List<PodCache> podCacheList, long id, AtomicInteger normalNumber, AtomicInteger riskNumber, AtomicInteger exceptNumber) {
        List<InspectionTaskServiceDTO> serviceDTOS = Collections.synchronizedList(new ArrayList<>());
        podCacheList.parallelStream().forEach(pod -> {
            InspectionTaskServiceDTO serviceDTO = new InspectionTaskServiceDTO();
            serviceDTO.setInstanceId(id);
            serviceDTO.setPodName(pod.getMetadata().getName());
            String seviceName = pod.getMetadata().getName();
            if (StringUtils.isNotBlank(pod.getMetadata().getLabels().get("app"))) {
                seviceName = pod.getMetadata().getLabels().get("app");
            }

            serviceDTO.setServiceName(seviceName);
            serviceDTO.setTaskIndex(Constants.INSPECT_SERVICE_PROBE);
            String status = pod.getStatus().getPhase();
            List<ContainerStatusCache> containerStatuses = pod.getStatus().getContainerStatuses();
            Integer restartCount = 0;
            if (CollectionUtils.isNotEmpty(containerStatuses)) {
                restartCount = containerStatuses.stream().mapToInt(ContainerStatusCache::getRestartCount).max().getAsInt();
            }
            if (StringUtils.equals(STATUS_SUCCEEDED, status) || StringUtils.equals(STATUS_RUNNING, status)) {
                if (restartCount <= 2) {
                    serviceDTO.setLadder(InspectLadderEnum.NORMAL.getValue());
                    serviceDTO.setTaskValue(status);
                    normalNumber.getAndIncrement();
                } else {
                    serviceDTO.setLadder(InspectLadderEnum.RISK.getValue());
                    serviceDTO.setTaskValue(status + ",但是重启了" + restartCount + "次");
                    riskNumber.getAndIncrement();
                }
            } else {
                serviceDTO.setLadder(InspectLadderEnum.EXCEPT.getValue());
                serviceDTO.setTaskValue(status);
                exceptNumber.getAndIncrement();
            }
            serviceDTOS.add(serviceDTO);
        });
        inspectJDBCUtils.batchExecuteInspectService(serviceDTOS);
    }

    private void execServiceInspection(long id, List<PodCache> podCacheList) {
        String mainIp = cacheService.cacheConfigMapAllIp().getData().get(NetworkConstants.MAIN_INTERNAL_IP);
        String url = String.format("http://%s:%d%s?instanceId=%d", Ipv6Util.handlerIpv6Addr(mainIp), 11111, inspectServiceUrl, id);
        List<InspectionServiceListDTO> inspectServiceList = inspectJDBCUtils.getInspectServiceList();
        if (CollectionUtils.isEmpty(inspectServiceList)) {
            return;
        }
        List<InspectSvcAvailableDto> dtos = Collections.synchronizedList(new ArrayList<>());
        podCacheList.parallelStream().forEach(pod -> {
            String seviceName = pod.getMetadata().getName();
            if (StringUtils.isNotBlank(pod.getMetadata().getLabels().get("app"))) {
                seviceName = pod.getMetadata().getLabels().get("app");
            }
            String finalSeviceName = seviceName;
            Optional<InspectionServiceListDTO> server = inspectServiceList.stream().filter(s -> StringUtils.equals(s.getServiceName(), finalSeviceName)).findFirst();
            if (server.isPresent()) {
                InspectSvcAvailableDto dto = new InspectSvcAvailableDto();
                dto.setPodName(pod.getMetadata().getName());
                dto.setServiceName(seviceName);
                dto.setInspectionType(server.get().getInspectionType());
                if (Constants.INSPECT_TYPE_REST.equals(server.get().getInspectionType())) {
                    StringBuffer stringBuffer = new StringBuffer();
                    stringBuffer.append("http://");
                    String podIp = "";
                    if (pod.getStatus() != null) {
                        podIp = pod.getStatus().getHostIP();
                    }
                    String port = "";
                    if (StringUtils.equals(SERVICE_VODNETWORK_VOD, seviceName) || StringUtils.equals(SERVICE_VODNETWORK_VODEDIT, seviceName)) {
                        String nodeName = pod.getSpec().getNodeName();
                        Map<String, String> data = cacheService.cacheConfigMapByName(Constants.CONFIGMAP_VODNETWORK).getData();
                        if (StringUtils.equals(SERVICE_VODNETWORK_VOD, seviceName)) {
                            port = StringUtils.isBlank(data.get(nodeName + VodnetworkConstants.VOD_BACK_HTTP_PORT)) ?
                                    VodnetworkConstants.DEFAULT_VOD_BACK_HTTP_PORT : data.get(nodeName + VodnetworkConstants.VOD_BACK_HTTP_PORT);
                        } else {
                            port = StringUtils.isBlank(data.get(nodeName + VodnetworkConstants.VODEDIT_DOWNLOAD_HTTP_PORT)) ?
                                    VodnetworkConstants.DEFAULT_VODEDIT_DOWNLOAD_HTTP_PORT : data.get(nodeName + VodnetworkConstants.VODEDIT_DOWNLOAD_HTTP_PORT);
                        }
                    } else {
                        for (ContainerCache c : pod.getSpec().getContainers()) {
                            if (c.getLivenessProbe() != null && c.getLivenessProbe().getHttpGet() != null && c.getLivenessProbe().getHttpGet().getPort() != null) {
                                port = String.valueOf(c.getLivenessProbe().getHttpGet().getPort().getIntVal());
                            }
                        }
                    }
                    if (StringUtils.isNotBlank(podIp) && StringUtils.isNotBlank(port)) {
                        stringBuffer.append(Ipv6Util.handlerIpv6Addr(podIp));
                        stringBuffer.append(":");
                        stringBuffer.append(port);
                        stringBuffer.append(server.get().getInspectionUrlPre());
                        dto.setInspectionUrl(stringBuffer.toString());
                    }
                }
                dtos.add(dto);
            }

        });
        try {
            restTemplate.postForObject(url, dtos, Void.class);
        } catch (Exception e) {
            log.error("execServiceInspection error,instanceId:{},url:{}", id, url, e);
        }
    }
}
