package com.xylink.manager.service.inspect;

import com.xylink.config.NetworkConstants;
import com.xylink.manager.controller.dto.inspect.InspectionTaskMiddlewareDTO;
import com.xylink.manager.controller.dto.inspect.MysqlConfigDTO;
import com.xylink.manager.model.em.InspectLadderEnum;
import com.xylink.manager.model.em.InspectionMetricKeyEnum;
import com.xylink.manager.service.base.K8sService;
import com.xylink.util.DBPwdAESEncryptUtil;
import com.xylink.util.InspectJDBCUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/04/13/16:08
 */
@Service
public class MysqlInspector {

    private static final Logger log = LoggerFactory.getLogger(MysqlInspector.class);

    @Autowired
    private K8sService k8sService;

    @Autowired
    private InspectJDBCUtils inspectJDBCUtils;

    @Autowired
    private DBPwdAESEncryptUtil aesEncryptUtil;

    private static final String DEFAULT_TASK_ITEM = "mysql";

    /**
     * 巡检
     */
    public boolean inspect(long instanceId) {
        List<MysqlConfigDTO> configs = getMainMysqlConfig();
        try {
            for (MysqlConfigDTO config : configs) {
                log.info("[inspection] db name {}, db url {}", config.getName(), config.getHost());
                return inspectMysql(instanceId, config);
            }
        } catch (Exception e) {
            log.error("[inspection] mysql inspect error", e);
        }
        return false;
    }

    /**
     * 获取数据库指标信息
     */
    private boolean inspectMysql(long instanceId, MysqlConfigDTO config) {
        boolean valid;
        String mysqlName = config.getName();
        List<InspectionTaskMiddlewareDTO> taskList;
        try {
            Connection connection = getConnection(config);
            valid = connection.isValid(60);
            if (valid) {
                taskList = inspectionMetric(instanceId, mysqlName, connection);
            } else {
                taskList = saveFailedMetricTasks(instanceId, mysqlName);
            }
        } catch (SQLException e) {
            valid = false;
            taskList = saveFailedMetricTasks(instanceId, mysqlName);
        } catch (Exception e) {
            valid = false;
            taskList = saveFailedMetricTasks(instanceId, mysqlName);
            log.error("[inspection] error, mysql host " + config.getHost(), e);
        }
        inspectJDBCUtils.insertInspectTaskMiddleware(taskList);
        return valid;
    }

    /**
     * mysql巡检
     */
    private List<InspectionTaskMiddlewareDTO> inspectionMetric(long instanceId, String mysqlName, Connection connection) {
        List<InspectionTaskMiddlewareDTO> taskList = new ArrayList<>(7);
        taskList.add(agentLive(instanceId, mysqlName, connection));
        taskList.add(connectionCount(instanceId, mysqlName, connection));
        taskList.add(bufferPoolHitRate(instanceId, mysqlName, connection));
        taskList.add(bufferPoolRequestCount(instanceId, mysqlName, connection));
        taskList.add(rowOperations(instanceId, mysqlName, connection));
        taskList.add(tps(instanceId, mysqlName, connection));
        taskList.add(qps(instanceId, mysqlName, connection));
        return taskList;
    }

    /**
     * 探活
     */
    private InspectionTaskMiddlewareDTO agentLive(long instanceId, String mysqlName, Connection connection) {
        InspectionTaskMiddlewareDTO agentLive = new InspectionTaskMiddlewareDTO(instanceId, mysqlName,
                InspectionMetricKeyEnum.MYSQL_AGENT_LIVE.getMetricKey(), "mysql 连接成功", InspectLadderEnum.NORMAL.getValue());
        try {
            boolean valid = connection.isValid(60);
            if (valid) {
                return agentLive;
            }
            agentLive.setTaskValue("mysql 不可用");
        } catch (Exception e) {
            log.error("[inspection] mysql agent live info get error", e);
            agentLive.setTaskValue("指标获取失败");
        }
        agentLive.setLadder(InspectLadderEnum.EXCEPT.getValue());
        return agentLive;
    }

    /**
     * 连接数
     */
    public InspectionTaskMiddlewareDTO connectionCount(long instanceId, String mysqlName, Connection connection) {
        String metricKey = InspectionMetricKeyEnum.MYSQL_CONNECTION_COUNT.getMetricKey();
        String maxConnectionsSql = "show variables like 'max_connections';";
        String sessionTotalSql = "select count(*) as '会话总数' from information_schema.PROCESSLIST;";
        String runningSessionTotalSql = "select count(*) as '运行中会话总数' from information_schema.PROCESSLIST where COMMAND not in ('Sleep','Binlog Dump');";
        String maxRunningSessionTimeSql = "select max(TIME) as '运行中会话最长时间' from information_schema.PROCESSLIST where COMMAND not in ('Sleep','Binlog Dump');";
        try (PreparedStatement maxConnectionsPs = connection.prepareStatement(maxConnectionsSql);
             PreparedStatement sessionTotalPs = connection.prepareStatement(sessionTotalSql);
             PreparedStatement runningSessionTotalPs = connection.prepareStatement(runningSessionTotalSql);
             PreparedStatement maxRunningSessionTimePs = connection.prepareStatement(maxRunningSessionTimeSql);
             ResultSet maxConnectionsRs = maxConnectionsPs.executeQuery();
             ResultSet sessionTotalRs = sessionTotalPs.executeQuery();
             ResultSet runningSessionTotalRs = runningSessionTotalPs.executeQuery();
             ResultSet maxRunningSessionTimeRs = maxRunningSessionTimePs.executeQuery()
        ) {
            if (!maxConnectionsRs.next()
                    || !sessionTotalRs.next()
                    || !runningSessionTotalRs.next()
                    || !maxRunningSessionTimeRs.next()) {
                log.error("[inspection] Mysql inspection connection count failed, instanceId={}", instanceId);
                // 指标获取失败
                return new InspectionTaskMiddlewareDTO(instanceId, mysqlName, metricKey, "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
            }
            long maxConnections = Long.parseLong(maxConnectionsRs.getString("Value"));
            long sessionTotal = sessionTotalRs.getLong(1);
            long runningSessionTotal = runningSessionTotalRs.getLong(1);
            long maxRunningSessionTime = maxRunningSessionTimeRs.getLong(1);

            String result = "最大连接数=" + maxConnections +
                    "\n会话总数=" + sessionTotal +
                    "\n运行中会话总数=" + runningSessionTotal +
                    "\n运行中会话最长时间" + maxRunningSessionTime;

            double value = sessionTotal * 1.0 / maxConnections;

            if (value >= 0.5) {
                // 高风险
                return new InspectionTaskMiddlewareDTO(instanceId, mysqlName, metricKey, result, InspectLadderEnum.RISK.getValue());
            }
            // 正常
            return new InspectionTaskMiddlewareDTO(instanceId, mysqlName, metricKey, result, InspectLadderEnum.NORMAL.getValue());
        } catch (Exception e) {
            log.error("[inspection] mysql connection count info get error", e);
            return new InspectionTaskMiddlewareDTO(instanceId, mysqlName, metricKey, "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
        }
    }

    /**
     * InnoDB Buffer Pool 命中率
     */
    public InspectionTaskMiddlewareDTO bufferPoolHitRate(long instanceId, String mysqlName, Connection connection) {
        String metricKey = InspectionMetricKeyEnum.MYSQL_INNODB_BUFFER_POOL_HIT_RATE.getMetricKey();
        String innodbBufferPoolStatusSQL = "select * from performance_schema.global_status where VARIABLE_NAME in ('Innodb_buffer_pool_pages_data', 'Innodb_buffer_pool_read_requests', 'Innodb_buffer_pool_reads', 'Innodb_buffer_pool_pages_free');";
        try (PreparedStatement innodbBufferPoolStatusPs = connection.prepareStatement(innodbBufferPoolStatusSQL);
             ResultSet innodbBufferPoolStatusRs = innodbBufferPoolStatusPs.executeQuery()
        ) {
            Map<String, Long> map = new HashMap<>();
            while (innodbBufferPoolStatusRs.next()) {
                map.put(innodbBufferPoolStatusRs.getString(1), innodbBufferPoolStatusRs.getLong(2));
            }
            Long innodbBufferPoolPagesData = map.get("Innodb_buffer_pool_pages_data");
            Long innodbBufferPoolReadRequests = map.get("Innodb_buffer_pool_read_requests");
            Long innodbBufferPoolReads = map.get("Innodb_buffer_pool_reads");
            Long innodbBufferPoolPagesFree = map.get("Innodb_buffer_pool_pages_free");
            if (innodbBufferPoolPagesData == null
                    || innodbBufferPoolReadRequests == null
                    || innodbBufferPoolReads == null
                    || innodbBufferPoolPagesFree == null) {
                // 指标获取失败
                return new InspectionTaskMiddlewareDTO(instanceId, mysqlName, metricKey, "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
            }
            double readCacheHitRatio = ((innodbBufferPoolReadRequests - innodbBufferPoolReads) * 1.0 / innodbBufferPoolReadRequests) * 100;
            double cacheUsage = (innodbBufferPoolPagesData * 1.0 / (innodbBufferPoolPagesData + innodbBufferPoolPagesFree)) * 100;
            String result = "Buffer Pool读缓存命中率=" + String.format("%.2f", readCacheHitRatio) + "\nBuffer Pool使用率=" + String.format("%.2f", cacheUsage);
            if (readCacheHitRatio < 98) {
                // 高风险
                return new InspectionTaskMiddlewareDTO(instanceId, mysqlName, metricKey, result, InspectLadderEnum.RISK.getValue());
            }
            // 正常
            return new InspectionTaskMiddlewareDTO(instanceId, mysqlName, metricKey, result, InspectLadderEnum.NORMAL.getValue());
        } catch (Exception e) {
            log.error("[inspection] mysql buffer pool hit rate info get error", e);
            return new InspectionTaskMiddlewareDTO(instanceId, mysqlName, metricKey, "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
        }
    }

    /**
     * InnoDB Buffer Pool 请求次数
     */
    public InspectionTaskMiddlewareDTO bufferPoolRequestCount(long instanceId, String mysqlName, Connection connection) {
        String metricKey = InspectionMetricKeyEnum.MYSQL_INNODB_BUFFER_POOL_REQUEST_COUNT.getMetricKey();
        String innodbBufferPoolStatusSQL = "select * from performance_schema.global_status where VARIABLE_NAME in ('Innodb_buffer_pool_read_requests','Innodb_buffer_pool_write_requests');";
        try (PreparedStatement innodbBufferPoolStatusPs = connection.prepareStatement(innodbBufferPoolStatusSQL);
             ResultSet innodbBufferPoolStatusRs = innodbBufferPoolStatusPs.executeQuery()
        ) {
            Map<String, Long> map = new HashMap<>();
            while (innodbBufferPoolStatusRs.next()) {
                map.put(innodbBufferPoolStatusRs.getString(1), innodbBufferPoolStatusRs.getLong(2));
            }
            Long innodbBufferPoolReadRequests = map.get("Innodb_buffer_pool_read_requests");
            Long innodbBufferPoolWriteRequests = map.get("Innodb_buffer_pool_write_requests");
            if (innodbBufferPoolReadRequests == null || innodbBufferPoolWriteRequests == null) {
                // 指标获取失败
                return new InspectionTaskMiddlewareDTO(instanceId, mysqlName, metricKey, "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
            }
            String result = "InnoDB平均每秒从Buffer Pool读页次数=" + innodbBufferPoolReadRequests + "\nInnoDB平均每秒从Buffer Pool写页次数=" + innodbBufferPoolWriteRequests;
            return new InspectionTaskMiddlewareDTO(instanceId, mysqlName, metricKey, result, InspectLadderEnum.NORMAL.getValue());
        } catch (Exception e) {
            log.error("[inspection] mysql buffer pool request count info get error", e);
            return new InspectionTaskMiddlewareDTO(instanceId, mysqlName, metricKey, "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
        }
    }

    /**
     * InnoDB Row Operations
     */
    public InspectionTaskMiddlewareDTO rowOperations(long instanceId, String mysqlName, Connection connection) {
        String metricKey = InspectionMetricKeyEnum.MYSQL_INNODB_ROW_OPERATIONS.getMetricKey();
        String rowOperationsSql = "select * from performance_schema.global_status where VARIABLE_NAME in ('Innodb_rows_deleted','Innodb_rows_read','Innodb_rows_inserted','Innodb_log_writes','Innodb_rows_updated');";
        try (PreparedStatement executionCountSqlPs = connection.prepareStatement(rowOperationsSql);
             ResultSet executionCountSqlRs = executionCountSqlPs.executeQuery()
        ) {
            Map<String, Long> map = new HashMap<>();
            while (executionCountSqlRs.next()) {
                map.put(executionCountSqlRs.getString(1), executionCountSqlRs.getLong(2));
            }
            Long innodbRowsDeleted = map.get("Innodb_rows_deleted");
            Long innodbRowsRead = map.get("Innodb_rows_read");
            Long innodbRowsInserted = map.get("Innodb_rows_inserted");
            Long innodbLogWrites = map.get("Innodb_log_writes");
            Long innodbRowsUpdated = map.get("Innodb_rows_updated");
            if (innodbRowsDeleted == null
                    || innodbRowsRead == null
                    || innodbRowsInserted == null
                    || innodbLogWrites == null
                    || innodbRowsUpdated == null) {
                // 指标获取失败
                return new InspectionTaskMiddlewareDTO(instanceId, mysqlName, metricKey, "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
            }
            String result = "innodb_rows_deleted=" + innodbRowsDeleted
                    + "\ninnodb_rows_read=" + innodbRowsRead
                    + "\ninnodb_rows_inserted=" + innodbRowsInserted
                    + "\ninnodb_log_writes=" + innodbLogWrites
                    + "\ninnodb_rows_updated=" + innodbRowsUpdated;
            return new InspectionTaskMiddlewareDTO(instanceId, mysqlName, metricKey, result, InspectLadderEnum.NORMAL.getValue());
        } catch (Exception e) {
            log.error("[inspection] mysql row operations info get error", e);
            return new InspectionTaskMiddlewareDTO(instanceId, mysqlName, metricKey, "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
        }
    }

    /**
     * TPS
     */
    public InspectionTaskMiddlewareDTO tps(long instanceId, String mysqlName, Connection connection) {
        String metricKey = InspectionMetricKeyEnum.MYSQL_TPS.getMetricKey();
        String rowOperationsSql = "show global status where VARIABLE_NAME in ('Com_commit','Com_rollback');";

        try (PreparedStatement executionCountSqlPs = connection.prepareStatement(rowOperationsSql);
             ResultSet executionCountSqlRs = executionCountSqlPs.executeQuery()
        ) {
            Map<String, Long> map = new HashMap<>();
            while (executionCountSqlRs.next()) {
                map.put(executionCountSqlRs.getString(1), executionCountSqlRs.getLong(2));
            }
            Long commit = map.get("Com_commit");
            Long rollback = map.get("Com_rollback");
            if (commit == null || rollback == null) {
                // 指标获取失败
                return new InspectionTaskMiddlewareDTO(instanceId, mysqlName, metricKey, "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
            }
            long tps = commit + rollback;
            String result = "TPS=" + tps;
            return new InspectionTaskMiddlewareDTO(instanceId, mysqlName, metricKey, result, InspectLadderEnum.NORMAL.getValue());
        } catch (Exception e) {
            log.error("[inspection] mysql tps info get error", e);
            return new InspectionTaskMiddlewareDTO(instanceId, mysqlName, metricKey, "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
        }
    }

    /**
     * QPS
     */
    public InspectionTaskMiddlewareDTO qps(long instanceId, String mysqlName, Connection connection) {
        String metricKey = InspectionMetricKeyEnum.MYSQL_QPS.getMetricKey();
        String rowOperationsSql = "select * from performance_schema.global_status where VARIABLE_NAME in ('Questions');";

        try (PreparedStatement executionCountSqlPs = connection.prepareStatement(rowOperationsSql);
             ResultSet executionCountSqlRs = executionCountSqlPs.executeQuery()
        ) {
            Map<String, Long> map = new HashMap<>();
            while (executionCountSqlRs.next()) {
                map.put(executionCountSqlRs.getString(1), executionCountSqlRs.getLong(2));
            }
            Long questions = map.get("Questions");
            if (questions == null) {
                // 指标获取失败
                return new InspectionTaskMiddlewareDTO(instanceId, mysqlName, metricKey, "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
            }
            String result = "QPS=" + questions;
            return new InspectionTaskMiddlewareDTO(instanceId, mysqlName, metricKey, result, InspectLadderEnum.NORMAL.getValue());
        } catch (Exception e) {
            log.error("[inspection] mysql qps info get error", e);
            return new InspectionTaskMiddlewareDTO(instanceId, mysqlName, metricKey, "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
        }
    }

    /**
     * 获取异常指标
     */
    private List<InspectionTaskMiddlewareDTO> saveFailedMetricTasks(long instanceId, String taskItem) {
        InspectionTaskMiddlewareDTO agentLive = new InspectionTaskMiddlewareDTO(instanceId, taskItem, InspectionMetricKeyEnum.MYSQL_AGENT_LIVE.getMetricKey(), "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
        InspectionTaskMiddlewareDTO connectionCount = new InspectionTaskMiddlewareDTO(instanceId, taskItem, InspectionMetricKeyEnum.MYSQL_CONNECTION_COUNT.getMetricKey(), "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
        InspectionTaskMiddlewareDTO bufferPoolHitRate = new InspectionTaskMiddlewareDTO(instanceId, taskItem, InspectionMetricKeyEnum.MYSQL_INNODB_BUFFER_POOL_HIT_RATE.getMetricKey(), "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
        InspectionTaskMiddlewareDTO bufferPoolRequestCount = new InspectionTaskMiddlewareDTO(instanceId, taskItem, InspectionMetricKeyEnum.MYSQL_INNODB_BUFFER_POOL_REQUEST_COUNT.getMetricKey(), "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
        InspectionTaskMiddlewareDTO rowOperations = new InspectionTaskMiddlewareDTO(instanceId, taskItem, InspectionMetricKeyEnum.MYSQL_INNODB_ROW_OPERATIONS.getMetricKey(), "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
        InspectionTaskMiddlewareDTO tps = new InspectionTaskMiddlewareDTO(instanceId, taskItem, InspectionMetricKeyEnum.MYSQL_TPS.getMetricKey(), "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
        InspectionTaskMiddlewareDTO qps = new InspectionTaskMiddlewareDTO(instanceId, taskItem, InspectionMetricKeyEnum.MYSQL_QPS.getMetricKey(), "指标获取失败", InspectLadderEnum.EXCEPT.getValue());
        List<InspectionTaskMiddlewareDTO> taskList = new ArrayList<>(7);
        taskList.add(agentLive);
        taskList.add(connectionCount);
        taskList.add(bufferPoolHitRate);
        taskList.add(bufferPoolRequestCount);
        taskList.add(rowOperations);
        taskList.add(tps);
        taskList.add(qps);
        return taskList;
    }

    private synchronized Connection getConnection(MysqlConfigDTO config) throws ClassNotFoundException, SQLException {
        String driver = "com.mysql.cj.jdbc.Driver";
        Class.forName(driver);
        return DriverManager.getConnection(config.jdbcUrl(), config.getUsername(), config.getPassword());
    }

    /**
     * 获取主节点mysql数据库连接
     */
    private List<MysqlConfigDTO> getMainMysqlConfig() {
        Map<String, String> allIp = k8sService.getConfigmap("all-ip");

        List<MysqlConfigDTO> mysqlConfigList = new ArrayList<>();
        //main数据库
        MysqlConfigDTO mainMysql = new MysqlConfigDTO();
        mainMysql.setName(DEFAULT_TASK_ITEM);
        mainMysql.setDatabaseName("ainemo");
        mainMysql.setHost(allIp.get(NetworkConstants.DATABASE_IP));
        mainMysql.setPort(allIp.get(NetworkConstants.DATABASE_PORT));
        mainMysql.setPassword(getPassword(allIp));
        mainMysql.setUsername(allIp.get(NetworkConstants.MAIN_DB_SUPER_USERNAME));
        mysqlConfigList.add(mainMysql);
        return mysqlConfigList;
    }

    /**
     * 获取所有mysql数据库连接
     */
    private List<MysqlConfigDTO> getMysqlConfig() {
        Map<String, String> allIp = k8sService.getConfigmap("all-ip");
        List<MysqlConfigDTO> mysqlConfigList = new ArrayList<>();
        //main数据库
        MysqlConfigDTO mainMysql = new MysqlConfigDTO();
        mainMysql.setName("main-mysql");
        mainMysql.setDatabaseName("ainemo");
        mainMysql.setHost(allIp.get(NetworkConstants.DATABASE_IP));
        mainMysql.setPort(allIp.get(NetworkConstants.DATABASE_PORT));
        mainMysql.setPassword(allIp.get(NetworkConstants.MAIN_DB_PASSWORD));
        mainMysql.setUsername(allIp.get(NetworkConstants.MAIN_DB_SUPER_USERNAME));
        mysqlConfigList.add(mainMysql);
        //statis
        MysqlConfigDTO statisMysql = new MysqlConfigDTO();
        statisMysql.setName("statis-mysql");
        statisMysql.setDatabaseName("statis");
        statisMysql.setHost(allIp.get(NetworkConstants.STATIS_DATABASE_IP));
        statisMysql.setPort(allIp.get(NetworkConstants.DATABASE_PORT));
        statisMysql.setPassword(allIp.get(NetworkConstants.STATIS_DB_PASSWORD));
        statisMysql.setUsername(allIp.get(NetworkConstants.MAIN_DB_SUPER_USERNAME));
        mysqlConfigList.add(statisMysql);
        //uaa
        MysqlConfigDTO uaaMysql = new MysqlConfigDTO();
        uaaMysql.setName("uaa-mysql");
        uaaMysql.setDatabaseName("uaa");
        uaaMysql.setHost(allIp.get(NetworkConstants.UAA_DATABASE_IP));
        uaaMysql.setPort(allIp.get(NetworkConstants.DATABASE_PORT));
        uaaMysql.setPassword(allIp.get(NetworkConstants.UAA_DB_PASSWORD));
        uaaMysql.setUsername(allIp.get(NetworkConstants.MAIN_DB_SUPER_USERNAME));
        mysqlConfigList.add(uaaMysql);
        //matrix
        MysqlConfigDTO matrixMysql = new MysqlConfigDTO();
        matrixMysql.setName("matrix-mysql");
        matrixMysql.setDatabaseName("matrix");
        matrixMysql.setHost(allIp.get(NetworkConstants.MATRIX_DATABASE_IP));
        matrixMysql.setPort(allIp.get(NetworkConstants.DATABASE_PORT));
        matrixMysql.setPassword(allIp.get(NetworkConstants.MATRIX_DB_PASSWORD));
        matrixMysql.setUsername(allIp.get(NetworkConstants.MAIN_DB_SUPER_USERNAME));
        mysqlConfigList.add(matrixMysql);
        //surv
        MysqlConfigDTO survMysql = new MysqlConfigDTO();
        survMysql.setName("surveillance-mysql");
        survMysql.setDatabaseName("surveillance");
        survMysql.setHost(allIp.get(NetworkConstants.SURV_INTERNAL_IP));
        survMysql.setPort(allIp.get(NetworkConstants.DATABASE_PORT));
        survMysql.setPassword(allIp.get(NetworkConstants.SURV_DB_PASSWORD));
        survMysql.setUsername(allIp.get(NetworkConstants.MAIN_DB_SUPER_USERNAME));
        mysqlConfigList.add(survMysql);
        //edu
        MysqlConfigDTO eduMysql = new MysqlConfigDTO();
        eduMysql.setName("education-mysql");
        eduMysql.setDatabaseName("education");
        eduMysql.setHost(allIp.get(NetworkConstants.EDU_ADAPTER_IP));
        eduMysql.setPort(allIp.get(NetworkConstants.DATABASE_PORT));
        eduMysql.setPassword(allIp.get(NetworkConstants.EDU_DB_PASSWORD));
        eduMysql.setUsername(allIp.get(NetworkConstants.MAIN_DB_SUPER_USERNAME));
        mysqlConfigList.add(eduMysql);
        return mysqlConfigList;
    }

    /**
     * 获取数据库密码
     * @param allIp
     * @return
     */
    private String getPassword(Map<String, String> allIp) {
        String value = allIp.get(NetworkConstants.MAIN_DB_PASSWORD);
        if (StringUtils.isBlank(value)) {
            return "";
        }
        String switchStatus = allIp.get(NetworkConstants.DB_PASSWORD_ENCRYPT_SWITCH);
        if (Boolean.parseBoolean(switchStatus)) {
            return aesEncryptUtil.decryption(value);
        }
        return value;
    }

}
