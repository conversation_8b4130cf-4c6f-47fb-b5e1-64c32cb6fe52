package com.xylink.manager.service;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.exception.basic.ClientErrorException;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.config.exception.basic.ServiceErrorException;
import com.xylink.manager.controller.dto.AudioConfig;
import com.xylink.manager.controller.dto.CloudBandWidthDto;
import com.xylink.manager.model.ClientFeatureConfig;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.nginxupload.ClientUploadService;
import com.xylink.util.ErrorCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 2023/04/25/11:15
 */
@Service
@Slf4j
public class CloudTableUploadService extends ClientUploadService {
    public final static String EXCEPT_FILENAME = "cloudbwtable.json";
    public final static String COPY_FILENAME = "copycloudbwtable.json";
    private final static String MD5REPLACE = "md5=";
    private final static String VERSIONREPLACE = "version=";

    @Autowired
    private K8sService k8sService;

    @Autowired
    private ServerListService serverListService;

    @Autowired
    private RestTemplate restTemplate;

    @PostConstruct
    private void loadDir() {
        String dir = StringUtils.isBlank(k8sService.getConfigmap(Constants.CONFIGMAP_PRIVATE_DATA).get("fileUploadDir"))?"/mnt/xylink/openresty/nginx_main/oss/client":k8sService.getConfigmap(Constants.CONFIGMAP_PRIVATE_DATA).get("fileUploadDir");
        expectFile = new File(dir + "/" + EXCEPT_FILENAME);
        copyFile = new File(dir + "/" + COPY_FILENAME);
    }

    @Override
    protected void check(MultipartFile file) {
        long maxSizeInBytes = 3L * 1024L * 1024L;
        if (file.getSize() > maxSizeInBytes) {
            throw new ClientErrorException(ErrorStatus.FILE_SIZE_ILLEGAL);
        }
        CloudBandWidthDto cloudBandWidthDto = ErrorCodeUtil.getJson(file, CloudBandWidthDto.class);
        cloudBandWidthDto.checkNotNull();
    }

    @Override
    protected void afterSave(MultipartFile file, ClientFeatureConfig config) {
        String md5sum = md5sum();
        String version = System.currentTimeMillis() + "";

        Map<String, String> all = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        String mainDomain = all.get(NetworkConstants.MAIN_DOMAIN_NAME);
        String nginxSslPort = all.get(NetworkConstants.MAIN_NGINX_SSL_PORT);
        String mainSsl = "443".equals(nginxSslPort) ? mainDomain : mainDomain + ":" + nginxSslPort;
        String bwurl = "https://" + mainSsl + "/oss/client/" + expectFile.getName() + "?" + MD5REPLACE + md5sum + "&" + VERSIONREPLACE + version;
        updateMessage(bwurl, config);
    }

    private void updateMessage(String bwurl, ClientFeatureConfig config) {

        try {
            //1、调用pivotor接口 存bwurl
            editClientFeatureConfig(bwurl, config);

        } catch (ServerException e) {
            rollbackFile();
            log.error("rollback success", e);
            throw e;
        }
    }

    public void editClientFeatureConfig(String bwurl, ClientFeatureConfig config) {
        config.setModelName("mediaConfig");
        config.setConfigName("bwUrl");
        config.setValue(bwurl);
        if (StringUtils.isBlank(config.getClientType())) {
            throw new ServerException(ErrorStatus.TERMINAL_CANNOT_NULL);
        }
        String mainIp = serverListService.getMainNodeInternalIP();
        String url = "http://" + mainIp + ":11111/api/rest/internal/v1/en/enterprisenemo/profileV2";
        AudioConfig deviceConfig = new AudioConfig(config.getConfigName(), config.getValue(), config.getModelName(), config.getClientType());
        Map<String, Object> reqParams = new HashMap<>();
        reqParams.put("enterpriseId", "default_enterprise");
        reqParams.put("configs", Arrays.asList(deviceConfig));
        restTemplate.put(url, reqParams);
    }

    protected void saveFile(MultipartFile file, ClientFeatureConfig config) {
        if (config == null || StringUtils.isBlank(config.getTypeName()) || StringUtils.isBlank(config.getClientType()) ) {
            throw new ServerException(ErrorStatus.PARAM_NULL);
        }
        try {
            if (expectFile.getName().endsWith(CloudTableUploadService.EXCEPT_FILENAME)) {
                String[] split = expectFile.getAbsolutePath().split("/");
                StringBuilder stringBuilder = new StringBuilder();
                for (int i = 0; i < split.length - 1; i++) {
                    if (i == 0) continue;
                    stringBuilder.append("/").append(split[i]);
                }
                String fileName = stringBuilder + "/" + config.getClientType() + "_" + CloudTableUploadService.EXCEPT_FILENAME;
                expectFile = new File(fileName);
            }
            if (!expectFile.exists() || renameFile(config)) {
                file.transferTo(expectFile);
            }
        } catch (IOException e) {
            log.error("save file failed", e);
            throw new ServiceErrorException(ErrorStatus.UNEXPECTED_ERROR);
        }
    }

    protected Boolean renameFile(ClientFeatureConfig config) {
        if (copyFile.getName().endsWith(CloudTableUploadService.COPY_FILENAME)) {
            String[] split = copyFile.getAbsolutePath().split("/");
            StringBuilder stringBuilder = new StringBuilder();
            for (int i = 0; i < split.length - 1; i++) {
                if (i == 0) continue;
                stringBuilder.append("/").append(split[i]);
            }
            String fileName = stringBuilder + "/" + config.getTypeName() + "_" + CloudTableUploadService.COPY_FILENAME;
            copyFile = new File(fileName);
        }
        if (!copyFile.exists() || copyFile.delete()) {
            return expectFile.renameTo(copyFile);
        }
        return false;
    }

}
