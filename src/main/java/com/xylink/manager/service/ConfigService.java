package com.xylink.manager.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.Gson;
import com.xylink.config.Constants;
import com.xylink.config.GmModeEnum;
import com.xylink.config.NetworkConstants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.config.exception.basic.WebException;
import com.xylink.config.mapper.JsonMapper;
import com.xylink.config.util.JsonUtil;
import com.xylink.manager.controller.dto.AudioConfig;
import com.xylink.manager.controller.dto.AudioConfigDto;
import com.xylink.manager.controller.dto.GPUBlacklistDto;
import com.xylink.manager.controller.dto.*;
import com.xylink.manager.controller.dto.servicemanage.ServerManageData;
import com.xylink.manager.controller.vo.HardTerminalConfigVo;
import com.xylink.manager.controller.vo.config.ConfigCheckInfo;
import com.xylink.manager.controller.vo.config.ConfigCheckVo;
import com.xylink.manager.model.ClientFeatureConfig;
import com.xylink.manager.model.Config;
import com.xylink.manager.model.deploy.Container;
import com.xylink.manager.model.deploy.CronJob;
import com.xylink.manager.model.deploy.Node;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.model.em.ConfigCheckEnum;
import com.xylink.manager.model.em.DBType;
import com.xylink.manager.service.api.NoahApiService;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.base.SystemModeConfig;
import com.xylink.manager.service.db.DBCommon;
import com.xylink.manager.service.event.BuffetConfigChangedEvent;
import com.xylink.manager.service.event.source.BuffetConfigChangeObj;
import com.xylink.manager.service.remote.basicmanagement.BasicManagementRemoteClient;
import com.xylink.manager.service.remote.buffet.BuffetRemoteClient;
import com.xylink.manager.service.remote.iauth.IauthRemoteClient;
import com.xylink.manager.service.remote.iauth.dto.PasswordIterationsResponse;
import com.xylink.util.GeneratePwdUtil;
import com.xylink.util.JDBCUtils;
import com.xylink.util.SecurityContextUtil;
import com.xylink.util.SpringBeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.attribute.FileTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;
import java.util.zip.CRC32;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
@Service
public class ConfigService {
    public static final Config DATACENTER_SETTING_CONFIG = new Config("开启数据中心", "DATACENTER_SETTING", "true");

    @Autowired
    private JDBCUtils jdbcUtils;

    @Value("${main.oss.path}")
    private String ossPath;
    @Value("#{'${nemo_l.modal}'.split(',')}")
    private List<String> nemo_l;
    @Value("#{'${nemo_m.modal}'.split(',')}")
    private List<String> nemo_m;
    @Value("#{'${nemo_b.modal}'.split(',')}")
    private List<String> nemo_b;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private K8sService k8sService;
    @Autowired
    private IDeployService deployService;
    @Autowired
    private ServerListService serverListService;
    @Resource
    private BuffetRemoteClient buffetRemoteClient;
    @Resource
    private BasicManagementRemoteClient basicManagementRemoteClient;
    @Autowired
    private CloudMeetingRoomService cloudMeetingRoomService;
    @Resource
    private PrivateDataService privateDataService;
    @Autowired
    private AuditLogService auditLogService;
    @Resource
    private IauthRemoteClient iauthRemoteClient;

    @Value("#{'${main.db.services}'.split(',')}")
    private List<String> mainDbServices;
    @Value("#{'${statis.db.services}'.split(',')}")
    private List<String> statisDbServices;
    @Value("#{'${surv.db.services}'.split(',')}")
    private List<String> survDbServices;
    @Value("#{'${matrix.db.services}'.split(',')}")
    private List<String> matrixDbServices;
    @Value("#{'${uaa.db.services}'.split(',')}")
    private List<String> uaaDbServices;
    @Value("#{'${edu.db.services}'.split(',')}")
    private List<String> eduDbServices;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private ServerGmCertService serverGmCertService;

    @Autowired
    private NoahApiService noahApiService;


    private final Map<String, Integer> recResolutionPriority = new HashMap<String, Integer>() {
        {
            put("1080P", 1);
            put("4K", 2);
            put("8K", 3);
        }
    };

    @PostConstruct
    private void refreshCustomizedKeyToMultiCustomizedKeys() {
        log.info("start config MultiCustomizedKeys...");
        String customizedKey = k8sService.getConfigmap(Constants.CONFIGMAP_PRIVATE_DATA).get("CustomizedKey");
        log.info("refreshCustomizedKeyToMultiCustomizedKeys use ck :{}", customizedKey);
        if (StringUtils.isBlank(customizedKey)) {
            return;
        }
        String customizeIosStore = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP).get("CUSTOMIZE_IOS_STORE");
        log.info("refreshCustomizedKeyToMultiCustomizedKeys use customizeIosStore :{}", customizeIosStore);
        customizeIosStore = Objects.isNull(customizeIosStore) ? "" : customizeIosStore;
        String multiCustomizedKeys = k8sService.getConfigmap(Constants.CONFIGMAP_PRIVATE_DATA).get("MultiCustomizedKeys");
        Map<String, String> cksMap = new HashMap<>();
        if (StringUtils.isNotBlank(multiCustomizedKeys)) {
            cksMap = JsonMapper.nonEmptyMapper().fromJson(multiCustomizedKeys, Map.class);
        }
        cksMap.put(customizedKey, customizeIosStore);
        Map<String, String> multiCKMaps = new HashMap<>();
        multiCKMaps.put("MultiCustomizedKeys", JsonUtil.toJsonStr(cksMap));
        k8sService.editConfigmap(Constants.CONFIGMAP_PRIVATE_DATA, multiCKMaps);
        log.info("config MultiCustomizedKeysv end.");
    }

    public void saveBuffetConfigDict(Config config) {
        jdbcUtils.saveBuffetConfigDict(config.getConfigName(), config.getName());
    }

    public void deleteBuffetConfigDict(String displayName, String configName, String configValue) {
        // 预约会议/会议模板选择参会成员数量上限:meetingParticipantLimit--->  禁止删除
        if ("meetingParticipantLimit".equals(configName)) {
            throw new ServerException(ErrorStatus.CONFIG_FORBID_DELETE);
        }
        if ("maxAllowedRecResolutionE".equals(configName)) {
            throw new ServerException(ErrorStatus.CONFIG_FORBID_DELETE);
        }
        if ("RESOURCE_DEP_SETTING".equals(configName)) {
            throw new ServerException(ErrorStatus.CONFIG_FORBID_DELETE);
        }
        if ("userPasswordIterations".equals(configName)) {
            throw new ServerException(ErrorStatus.CONFIG_FORBID_DELETE);
        }
        if ("userCustomNumberSwitch".equals(configName)) {
            throw new ServerException(ErrorStatus.CONFIG_FORBID_DELETE);
        }
        jdbcUtils.deleteBuffetConfigAndDict(configName);
        buffetRemoteClient.deleteEnterpriseConfig(configName);
        auditLogService.saveEnterpriseAuditLog(displayName, configName, "", configValue, "删除", SecurityContextUtil.currentUser());
        if ("isApprovalUsing".equals(configName)) {
            cleanChargeCache();
        }
        if ("HOT_STANDBY".equals(configName)) {
            basicManagementRemoteClient.clearHotStandbyForEnterpriseConfig();
        }
    }

    public List<Config> listBuffetConfig() {
        List<Config> dicts = jdbcUtils.listBuffetConfigDict();
        Map<String, String> enterpriseConfigMap = filterBuffetEnterpriseConfig(buffetRemoteClient.getEnterpriseConfig());
        Map<String, Config> map = new HashMap<>(10);
        dicts.forEach(d -> map.put(d.getConfigName(), d));
        enterpriseConfigMap.forEach((k, v) -> {
            if (map.containsKey(k)) {
                map.get(k).setConfigValue(v);
            } else if (!k.equalsIgnoreCase("SHOW_HR_RECORD")) {
                map.put(k, new Config(null, k, v));
            }
        });
        Optional<Config> config = configFromIauthServer();
        config.ifPresent(data -> {
            map.put(data.getConfigName(), data);
        });

        Optional<Config> configBm = configFromBmServer();
        configBm.ifPresent(data -> {
            map.put(data.getConfigName(), data);
        });

        return new ArrayList<>(map.values());
    }

    public void addBuffetConfig(Config config) {
         jdbcUtils.saveBuffetConfigDict(config.getConfigName(), config.getName());
         buffetRemoteClient.saveEnterpriseConfig(config.getConfigName(), config.getConfigValue());
    }

    public void saveBuffetConfig(String displayName, String configName, String configValue, String oldConfigValue) {
        log.info("Buffet config request:{\"displayName\":{},\"configName\":{},\"configValue\":{}}", displayName, configName, configValue);
        configValidation(displayName, configName, configValue, oldConfigValue);
        //localRecord修改为false时需要同步pivot控制权限
        if ("localRecord".equalsIgnoreCase(configName) && "false".equalsIgnoreCase(configValue)) {
            boolean exist = jdbcUtils.saveBuffetConfigDict(configName, displayName);
            buffetRemoteClient.saveEnterpriseConfig(configName, configValue);
            jdbcUtils.synchronizePivot();
            auditLogService.saveEnterpriseAuditLog(displayName, configName, configValue, oldConfigValue, exist ? "更新" : "添加", SecurityContextUtil.currentUser());
            return;
        }
        //maxAllowedRecResolutionE降级为低分辨率时需要调用云会议室降级接口
        if ("maxAllowedRecResolutionE".equalsIgnoreCase(configName)) {
            degradeMaxRecResuliton(configValue);
            boolean exist = jdbcUtils.saveBuffetConfigDict(configName, displayName);
            buffetRemoteClient.saveEnterpriseConfig(configName, configValue);
            auditLogService.saveEnterpriseAuditLog(displayName, configName, configValue, oldConfigValue, exist ? "更新" : "添加", SecurityContextUtil.currentUser());
            return;
        }
        //waterModeSetting=false 联动设置云会议室的企业水印格式,默认格式watermarkMode = 0
        if ("waterModeSetting".equals(configName) && "false".equalsIgnoreCase(configValue)) {
            Map<String, Object> configs = new HashMap<>();
            configs.put("watermarkMode", "0");
            cloudMeetingRoomService.addEntMeetingConfig("-1", configs);
        }

        if ("userPasswordIterations".equals(configName)) {
            iauthRemoteClient.passwordIterations(Integer.parseInt(configValue));
            auditLogService.saveEnterpriseAuditLog(displayName, configName, configValue, oldConfigValue, "更新", SecurityContextUtil.currentUser());
            return;
        }

        if ("userCustomNumberSwitch".equals(configName)) {
            basicManagementRemoteClient.editUserCustomNumberSwitch(Boolean.parseBoolean(configValue));
            auditLogService.saveEnterpriseAuditLog(displayName, configName, configValue, oldConfigValue, "更新", SecurityContextUtil.currentUser());
            return;
        }

        boolean exist = jdbcUtils.saveBuffetConfigDict(configName, displayName);
        buffetRemoteClient.saveEnterpriseConfig(configName, configValue);
        auditLogService.saveEnterpriseAuditLog(displayName, configName, configValue, oldConfigValue, exist ? "更新" : "添加", SecurityContextUtil.currentUser());
        if ("isApprovalUsing".equals(configName)) {
            cleanChargeCache();
        }
        if ("forbidMultiSignOn".equalsIgnoreCase(configName)) {
            // 账号单设备登录
            basicManagementRemoteClient.clearRedisCacheForEnterpriseConfig();
        }
        if ("HOT_STANDBY".equals(configName) && "false".equals(configValue)) {
            basicManagementRemoteClient.clearHotStandbyForEnterpriseConfig();
        }
        BuffetConfigChangeObj buffetConfigChangeObj = new BuffetConfigChangeObj(configName, configValue);
        BuffetConfigChangedEvent event = new BuffetConfigChangedEvent(this, buffetConfigChangeObj);
        applicationEventPublisher.publishEvent(event);
    }

    public void preSaveBuffetConfig(String displayName, String configName, String configValue, String oldConfigValue) {
        log.info("Buffet config request:{\"displayName\":{},\"configName\":{},\"configValue\":{}}", displayName, configName, configValue);
        configValidation(displayName, configName, configValue, oldConfigValue);
    }

    public List<ClientFeatureConfig> clientFeatureConfig(ClientFeatureConfig config) {
        List<ClientFeatureConfig> configs = new ArrayList<>();
        String mainIp = serverListService.getMainNodeInternalIP();
        String url = "http://" + mainIp + ":11111/api/rest/clientConfg/internal/config/default/configNameList/v1";
        String resultStr = restTemplate.getForObject(url, String.class);
        //如果配置为空则返回空，不用继续查了
        if (StringUtils.isBlank(resultStr)) {
            return configs;
        }
        List<TerminalConfigDto> terminalConfigs = JsonUtil.parseToList(resultStr, TerminalConfigDto.class);
        if (config == null || StringUtils.isBlank(config.getModelName())) {
            Map<String, List<TerminalConfigDto>> map = terminalConfigs.stream().collect(Collectors.groupingBy(TerminalConfigDto::getClientConfigName));
            configs = map.keySet().stream().map(model ->
                            new ClientFeatureConfig(model, null, null, null, null, null, null))
                    .collect(Collectors.toList());
            return configs;
        }
        //查询sn对应的特定配置 根据模块名称、配置名称、sn
        if (StringUtils.isNotBlank(config.getSn())) {
            return getClientFeatureConfigsBySn(mainIp, config);
        }
        //查询设备类型对应的特定配置
        if (StringUtils.isNotBlank(config.getModelName()) && StringUtils.isNotBlank(config.getConfigName()) && StringUtils.isNotBlank(config.getClientType())) {
            return getClientFeatureConfigsByType(mainIp, config);
        }
        //查询设备类型列表 根据模块名称和配置名称
        if (StringUtils.isNotBlank(config.getModelName()) && StringUtils.isNotBlank(config.getConfigName())) {
           return getDeviceTypeList(mainIp, config);
        }
        //查询配置名称列表 根据模块名称
        if (StringUtils.isNotBlank(config.getModelName())) {
            List<TerminalConfigDto> modelAndConfigNames = terminalConfigs.stream().filter(t -> StringUtils.equals(t.getClientConfigName(), config.getModelName())).collect(Collectors.toList());
            configs = modelAndConfigNames.stream().map(name -> new ClientFeatureConfig(name.getClientConfigName(), name.getConfigName(), null, null, null, null, null)).collect(Collectors.toList());
        }
        return configs;
    }

    /**
     * 根据sn查询特定配置
     * @param mainIp
     * @param config
     * @return
     */
    private List<ClientFeatureConfig> getClientFeatureConfigsBySn (String mainIp, ClientFeatureConfig config) {
        String url = "http://" + mainIp + ":11111/api/rest/clientConfg/internal/enterprise-config/v6?" +
                "enterpriseId=default_enterprise&sn=" + config.getSn();
        return getClientFeatureConfigsByTypeOrSn(config, url);
    }

    private List<ClientFeatureConfig> getClientFeatureConfigsByType (String mainIp, ClientFeatureConfig config) {
        String configUrl = "http://" + mainIp + ":11111/api/rest/clientConfg/internal/enterprise-config/v5?" +
                "enterpriseId=default_enterprise&configType=" + config.getClientType();
        return getClientFeatureConfigsByTypeOrSn(config, configUrl);
    }

    private List<ClientFeatureConfig> getClientFeatureConfigsByTypeOrSn(ClientFeatureConfig config, String configUrl) {
        List<ClientFeatureConfig> configs = new ArrayList<>();
        Map<String, String> param = new HashMap<>(2);
        param.put("configName", config.getConfigName());
        param.put("clientConfigName", config.getModelName());
        Map<String, Map<String, Object>> response;
        Gson gson = new Gson();
        try {
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(configUrl, Collections.singletonList(param), String.class);
            response = gson.fromJson(responseEntity.getBody(), Map.class);
            // 处理成功响应
        } catch (HttpClientErrorException e) {
            // 获取响应状态码和内容
            int statusCode = e.getRawStatusCode();
            String responseBody = e.getResponseBodyAsString();
            log.error("Error status code: " + statusCode);
            log.error("Error response body: " + responseBody);
            ServerException serverException = gson.fromJson(responseBody, ServerException.class);
            throw serverException;
        }

        String value = null;
        if (response != null && !response.isEmpty() && !response.get(config.getModelName()).isEmpty()) {
            Object valueRes = response.get(config.getModelName()).get(config.getConfigName());
            if (valueRes == null) {
                value = null;
            } else if (valueRes instanceof String) {
                value = (String) valueRes;
            } else {
                value = gson.toJson(valueRes);
            }
        }
        ClientFeatureConfig featureConfig = new ClientFeatureConfig(config.getModelName(), config.getConfigName(),
                config.getClientType(), null, value, null, null);
        configs.add(featureConfig);
        return configs;
    }

    private List<ClientFeatureConfig> getDeviceTypeList (String mainIp, ClientFeatureConfig config) {
        String deviceUrl = "http://" + mainIp + ":11111/api/rest/bm/internal/device/property/v1";
        BaseResponseDto<HardTerminalTypeDto> devicesTypeResponse = restTemplate.postForObject(deviceUrl, null, BaseResponseDto.class);
        List<HardTerminalTypeDto> devicesTypeList = JsonUtil.parseToList(JsonUtil.toJsonStr(devicesTypeResponse.getData()), HardTerminalTypeDto.class);
        Map<String, String> specialClientType = listSpecialClientType();
        devicesTypeList = devicesTypeList.stream().map(d -> {
            if (specialClientType.containsKey(d.getSubType() + "")) {
                d.setCategoryDisplay(specialClientType.get(d.getSubType() + ""));
            }
            return d;
        }).collect(Collectors.toList());
        List<ClientFeatureConfig> configs = devicesTypeList.stream().map(c -> new ClientFeatureConfig(config.getModelName(),
                config.getConfigName(), c.getSubType() + "", c.getCategoryDisplay())).collect(Collectors.toList());
        return configs;
    }

    public void addOrEditClientFeatureConfig(ClientFeatureConfig config) {
        if (StringUtils.isNotBlank(config.getSn())) {
            addOrEditClientFeatureConfigForSn(config);
        } else {
            addOrEditClientFeatureConfigForType(config);
        }
    }

    private void addOrEditClientFeatureConfigForType(ClientFeatureConfig config) {
        String mainIp = serverListService.getMainNodeInternalIP();
        String url = "http://" + mainIp + ":11111/api/rest/internal/v1/en/enterprisenemo/profileV2";
        AudioConfig deviceConfig = new AudioConfig(config.getConfigName(), config.getValue(), config.getModelName(), config.getClientType());
        Map<String, Object> reqParams = new HashMap<>();
        reqParams.put("enterpriseId", "default_enterprise");
        reqParams.put("configs", Arrays.asList(deviceConfig));
        restTemplate.put(url, reqParams);
        auditLogService.saveClientAuditLog(config.getConfigName(), config.getTypeName(), config.getValue(), config.getOldValue(), StringUtils.isBlank(config.getOldValue()) ? "增加" : "更新", SecurityContextUtil.currentUser());
    }

    private void addOrEditClientFeatureConfigForSn(ClientFeatureConfig config) {
        String mainIp = serverListService.getMainNodeInternalIP();
        String url = "http://" + mainIp + ":11111/api/rest/clientConfg/internal/device/nemoConfig/update/v1?sn="+config.getSn();
        AudioConfig deviceConfig = new AudioConfig(config.getConfigName(), config.getValue(), config.getModelName(), config.getClientType());
        restTemplate.postForObject(url, deviceConfig, Void.class);
        auditLogService.saveClientAuditLog(config.getConfigName(), config.getSn(), config.getValue(), config.getOldValue(), StringUtils.isBlank(config.getOldValue()) ? "增加" : "更新", SecurityContextUtil.currentUser());
        try {
            restTemplate.postForObject(url, deviceConfig, Void.class);
            // 处理成功响应
        } catch (HttpClientErrorException e) {
            // 获取响应状态码和内容
            int statusCode = e.getRawStatusCode();
            String responseBody = e.getResponseBodyAsString();
            log.error("Error status code: " + statusCode);
            log.error("Error response body: " + responseBody);
            ServerException serverException = new Gson().fromJson(responseBody, ServerException.class);
            throw serverException;
        }

        auditLogService.saveClientAuditLog(config.getConfigName(), config.getSn(), config.getValue(), config.getOldValue(), StringUtils.isBlank(config.getOldValue()) ? "增加" : "更新", SecurityContextUtil.currentUser());
    }

    public void deleteClientFeatureConfig(ClientFeatureConfig config) {
        String mainIp = serverListService.getMainNodeInternalIP();
       if (StringUtils.isNotBlank(config.getSn())) {
           deleteClientFeatureConfigForSn(mainIp, config);
       } else {
           deleteClientFeatureConfigForType(mainIp, config);
       }
    }


    private void deleteClientFeatureConfigForType(String mainIp, ClientFeatureConfig config) {
        String url = "http://" + mainIp + ":11111/api/rest/internal/v1/en/enterprisenemo/profileV2";
        AudioConfig deviceConfig = new AudioConfig(config.getConfigName(), config.getValue(), config.getModelName(), config.getClientType());
        Map<String, Object> reqParams = new HashMap<>();
        reqParams.put("enterpriseId", "default_enterprise");
        reqParams.put("configs", Arrays.asList(deviceConfig));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map> entity = new HttpEntity<>(reqParams, headers);
        restTemplate.exchange(url, HttpMethod.DELETE, entity, Void.class);
        auditLogService.saveClientAuditLog(config.getConfigName(), config.getTypeName(), "", config.getValue(), "删除", SecurityContextUtil.currentUser());
    }


    private void deleteClientFeatureConfigForSn(String mainIp, ClientFeatureConfig config) {
        String url = "http://" + mainIp + ":11111/api/rest/clientConfg/internal/device/nemoConfig/delete/v1?sn="+config.getSn();
        AudioConfig deviceConfig = new AudioConfig(config.getConfigName(), config.getValue(), config.getModelName(), config.getClientType());
        try {
            restTemplate.postForObject(url, deviceConfig, Void.class);
            // 处理成功响应
        } catch (HttpClientErrorException e) {
            // 获取响应状态码和内容
            int statusCode = e.getRawStatusCode();
            String responseBody = e.getResponseBodyAsString();
            log.error("Error status code: " + statusCode);
            log.error("Error response body: " + responseBody);
            ServerException serverException = new Gson().fromJson(responseBody, ServerException.class);
            throw serverException;
        }
        auditLogService.saveClientAuditLog(config.getConfigName(), config.getTypeName(), "", config.getValue(), "删除", SecurityContextUtil.currentUser());
    }

    public void degradeMaxRecResuliton(String configValue) {
        List<Config> configs = listBuffetConfig();
        List<Config> temp = configs.stream().filter(config1 -> config1.getConfigName().equalsIgnoreCase("maxAllowedRecResolutionE")).collect(Collectors.toList());
        Config config = null;
        if (temp.size() > 0) {
            config = temp.get(0);
        } else {
            return;
        }
        if (config != null && !config.getConfigValue().equalsIgnoreCase("1080P")) {
            Integer oldRecResolution = recResolutionPriority.get(config.getConfigValue());
            Integer newRecResolution = recResolutionPriority.get(configValue);
            if (StringUtils.isNotBlank(oldRecResolution.toString()) && oldRecResolution > newRecResolution) {
                //调用云会议室降级接口
                String mainInterIp = serverListService.getMainNodeInternalIP();
                String url = "http://" + mainInterIp + ":11111/api/rest/cloudmeetingroom/internal/enterprise/config/degradeMaxRecResuliton/v1";
                try {
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_JSON);
                    ObjectNode params = JsonNodeFactory.instance.objectNode();
                    params.put("enterpriseId", "default_enterprise");
                    params.put("maxRecResolution", configValue);
                    HttpEntity<String> httpEntity = new HttpEntity<>(params.toString(), headers);
                    restTemplate.postForObject(url, httpEntity, Void.class);
                } catch (Exception e) {
                    throw new ServerException(ErrorStatus.THIRD_INTERFACE_CLOUD_MEETINGROOM_FAILED);
                }
            }
        }
    }

    /**
     * 配置项校验
     *
     * @param displayName
     * @param configName
     * @param configValue
     */
    private void configValidation(String displayName, String configName, String configValue, String oldConfigValue) {
        // 预约会议/会议模板选择参会成员数量上限:meetingParticipantLimit--->整数 ，<= maxValue(ConfigMap:private-manager-data)
        int max = Integer.MAX_VALUE;
        int min = 0;
        if ("meetingParticipantLimit".equals(configName)) {
            try {
                String maxMeetingParticipantLimit = k8sService.getConfigmap(Constants.CONFIGMAP_PRIVATE_DATA).get("MAX_MEETING_PARTICIPANT_LIMIT");
                String minMeetingParticipantLimit = k8sService.getConfigmap(Constants.CONFIGMAP_PRIVATE_DATA).get("MIN_MEETING_PARTICIPANT_LIMIT");
                if (StringUtils.isNotBlank(maxMeetingParticipantLimit)) {
                    max = Integer.parseInt(maxMeetingParticipantLimit);
                }
                if (StringUtils.isNotBlank(minMeetingParticipantLimit)) {
                    min = Integer.parseInt(minMeetingParticipantLimit);
                }
                int value = Integer.parseInt(configValue);
                if (value < min || value > max) {
                    throw new ServerException("配置项值范围为[" + min + "," + max + "]");
                }
            } catch (NumberFormatException e) {
                throw new ServerException("配置项值范围为[" + min + "," + max + "]");
            }
        }
        gmModeChangedCheck(configName, configValue);

        if ("RESOURCE_DEP_SETTING".equals(configName) && "true".equals(oldConfigValue) && !oldConfigValue.equals(configValue)) {
            throw new ServerException(ErrorStatus.CONFIG_FORBID_UPDATE);
        }

        userPasswordIterationsCheck(configName, configValue);
    }

    private void gmModeChangedCheck(String configName, String configValue) {
        if (GmModeEnum.GM.name().equals(configValue) || GmModeEnum.GM_SSL.name().equals(configValue)) {
            log.info("GM_MODE changed to GM or GM_SSL.");
            if (!serverGmCertService.isAllNginxGmCertValid()) {
                throw new ServerException(ErrorStatus.CONFIG_GM_MODE_FORBID_UPDATE);
            }
        }
    }

    private void cleanChargeCache() {
        String mainIp = serverListService.getMainNodeInternalIP();
        String url = "http://" + mainIp + ":11111/api/rest/internal/v1/charge/cache/CHARGE:MEETING_CHECK:ENT:default_enterprise";
        log.info("clean charge cache " + url);
        restTemplate.exchange(url, HttpMethod.DELETE, new HttpEntity(new HashMap<>()), String.class);
    }


    public void configureClientLogo(MultipartFile file, String clientType) throws Exception {
        long time;
        File dir;
        synchronized (ConfigService.class) {
            time = new Date().getTime();
            dir = new File(ossPath + File.separator + time);
        }

        Map<String, String> types = jdbcUtils.clientSubType();
        String filename = file.getOriginalFilename();
        //小鱼 中鱼  zip
        if (nemo_l.contains(types.get(clientType)) || nemo_m.contains(types.get(clientType))) {
            if (filename.endsWith(".zip")) {
                File zip = new File(dir.getAbsolutePath() + ".zip");
                FileUtils.writeByteArrayToFile(zip, file.getBytes());
            } else if (filename.endsWith(".png")) {

                BufferedImage image = ImageIO.read(file.getInputStream());
                int height = nemo_m.contains(types.get(clientType)) ? 1080 : 800;
                int width = nemo_m.contains(types.get(clientType)) ? 1920 : 1280;
                if (image.getHeight() != height || image.getWidth() != width) {
                    throw new WebException("图片尺寸不符合要求(" + width + "x" + height + "),当前图片:" + image.getWidth() + "x" + image.getHeight());
                }

                if (dir.exists()) dir.delete();
                dir.mkdirs();
                File desc = new File(dir, "desc.txt");
                desc.createNewFile();
                String resolution = nemo_m.contains(types.get(clientType)) ? "1920 1080" : "1280 800";

                ArrayList<String> values = new ArrayList<>();
                values.add(resolution + " 1");
                values.add("p 0 0 part0");
                FileUtils.writeLines(desc, values);

                File part0 = new File(dir, "part0");
                part0.mkdir();

                File png = new File(part0, "1.png");
                png.createNewFile();
                FileUtils.writeByteArrayToFile(png, file.getBytes());

                zipLogo(dir.getAbsolutePath());

            } else {
                throw new WebException("当前终端不支持!");
            }
        }
        //大鱼  png
        else if (nemo_b.contains(types.get(clientType))) {
            if (!filename.endsWith(".png")) throw new WebException("大鱼系列终端只支持png格式!");
            File png = new File(ossPath + File.separator + time + ".png");
            FileUtils.writeByteArrayToFile(png, file.getBytes());
        } else {
            throw new ServerException("当前终端不支持!");
        }

        if (nemo_l.contains(types.get(clientType)) || nemo_m.contains(types.get(clientType))) {
            String md5 = DigestUtils.md5Hex(new FileInputStream(new File(dir.getAbsolutePath() + ".zip")));
            ClientFeatureConfig config = new ClientFeatureConfig();
            config.setConfigName("frameworkAnimation");
            config.setValue("http://replace/oss/logo/" + time + ".zip?md5=" + md5);
            config.setClientType(clientType);
            config.setModelName("UIDisplayCustomization");
            config.setBaseConfigType("0");
            config.setTypeName(types.get(clientType));
            addOrEditClientFeatureConfig(config);
        } else if (nemo_b.contains(types.get(clientType))) {
            ClientFeatureConfig config = new ClientFeatureConfig();
            config.setConfigName("splashPicture");
            config.setValue("http://replace/oss/logo/" + time + ".png");
            config.setClientType(clientType);
            config.setModelName("UIDisplayCustomization");
            config.setBaseConfigType("0");
            config.setTypeName(types.get(clientType));
            addOrEditClientFeatureConfig(config);
        }

    }

    public void resetLogo(String type) {
        ClientFeatureConfig config = new ClientFeatureConfig();
        if (StringUtils.isBlank(type)) return;
        Map<String, String> types = jdbcUtils.clientSubType();

        if (nemo_l.contains(types.get(type)) || nemo_m.contains(types.get(type))) {
            config.setConfigName("frameworkAnimation");
            config.setClientType(type);
            config.setModelName("UIDisplayCustomization");
            config.setTypeName(types.get(type));
            deleteClientFeatureConfig(config);
        } else if (nemo_b.contains(types.get(type))) {
            config.setConfigName("splashPicture");
            config.setClientType(type);
            config.setModelName("UIDisplayCustomization");
            config.setTypeName(types.get(type));
            deleteClientFeatureConfig(config);
        }
    }

    public Map<String, String> listSpecialClientType() {
        Map<String, String> types = new HashMap<>();
        types.put("1", "APP");
        types.put("2", "NE系列");
        types.put("3", "浏览器");
        types.put("4", "PSTN");
        types.put("5", "PC端");
        types.put("6", "H323网关");
        types.put("7", "大鱼系列");
        types.put("8", "中鱼系列");

        types.put("70", "ME90(ME80)");
        return types;
    }


    private void zipLogo(String path) {

        ZipOutputStream zipOutputStream = null;
        FileOutputStream fileOutputStream = null;
        try {
            fileOutputStream = new FileOutputStream(path + ".zip");
            zipOutputStream = new ZipOutputStream(fileOutputStream);

            //file desc.txt
            byte[] bytes = getBytes(new File(path + File.separator + "desc.txt"));
            ZipEntry descZipEntry = getZipEntry(bytes, "desc.txt");
            zipOutputStream.putNextEntry(descZipEntry);
            zipOutputStream.setMethod(ZipEntry.STORED);
            zipOutputStream.write(bytes, 0, bytes.length);
            zipOutputStream.closeEntry();

            //dir part0
            ZipEntry part0Entry = getZipEntry(null, "part0/");
            zipOutputStream.putNextEntry(part0Entry);
            zipOutputStream.closeEntry();

            //file 1.png
            bytes = getBytes(new File(path + File.separator + "part0" + File.separator + "1.png"));
            ZipEntry pnngZipEntry = getZipEntry(bytes, "part0/1.png");
            zipOutputStream.putNextEntry(pnngZipEntry);
            zipOutputStream.setMethod(ZipEntry.STORED);
            zipOutputStream.write(bytes, 0, bytes.length);
            zipOutputStream.closeEntry();

            new File(path).delete();
        } catch (Exception e) {
            log.error("fail to zip logo! ", e);
        } finally {
            try {
                if (zipOutputStream != null) zipOutputStream.close();
                if (fileOutputStream != null) fileOutputStream.close();
            } catch (IOException e) {
                log.error("fail to close stream! ", e);
            }
        }

    }

    private ZipEntry getZipEntry(byte[] bytes, String name) {
        ZipEntry zipEntry = new ZipEntry(name);
        if (bytes == null) {
            zipEntry.setCrc(0);
            zipEntry.setSize(0);
        } else {
            CRC32 crc32 = new CRC32();
            crc32.reset();
            crc32.update(bytes);
            zipEntry.setCrc(crc32.getValue());
            zipEntry.setSize(bytes.length);
        }
        Date date = new Date();
        zipEntry.setTime(date.getTime());
        zipEntry.setCreationTime(FileTime.fromMillis(date.getTime()));
        zipEntry.setLastAccessTime(FileTime.fromMillis(date.getTime()));
        zipEntry.setLastModifiedTime(FileTime.fromMillis(date.getTime()));
        zipEntry.setMethod(ZipEntry.STORED);
        return zipEntry;
    }


    private byte[] getBytes(File file) {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(file);
            byte[] b = new byte[1024];
            int n;
            while ((n = fis.read(b)) != -1) {
                bos.write(b, 0, n);
            }
        } catch (FileNotFoundException e) {
            log.error("file error:", e);
        } catch (IOException e) {
            log.error("io error:", e);
        } finally {
            try {
                if (fis != null) fis.close();
                if (bos != null) bos.close();
            } catch (IOException e) {
                log.error("fail to close stream! ", e);
            }
        }
        return bos.toByteArray();
    }

    /**
     * 获取硬终端音频配置
     *
     * @param snOrNumber
     * @param audioType
     * @return
     */
    public HardTerminalConfigVo getHardTerminalAudioConfig(String snOrNumber, String audioType) {
        String mainIp = serverListService.getMainNodeInternalIP();
        String getBySnUrl = "http://" + mainIp + ":11111/api/rest/internal/v1/en/userDevice?sn=" + snOrNumber;
        String getByNumberUrl = "http://" + mainIp + ":11111/api/rest/internal/v1/nemo_number/" + snOrNumber;
        HardTerminalConfigVo configVo = null;
        try {
            if (StringUtils.isNumeric(snOrNumber)) {
                // 通过终端号number获取
                configVo = restTemplate.getForObject(getByNumberUrl, HardTerminalConfigVo.class);
            } else {
                // 通过sn获取
                configVo = restTemplate.getForObject(getBySnUrl, HardTerminalConfigVo.class);
            }
            if (configVo == null) {
                throw new ServerException("无相关配置信息！");
            }

            String getByDeviceIdUrl = "http://" + mainIp + String.format(":11111/api/rest/internal/v1/%s/nemoconfig", configVo.getId());
            String stringConfigs = restTemplate.getForObject(getByDeviceIdUrl, String.class);
            JsonNode jsonArrayConfigs = JsonUtil.parseJson(stringConfigs);
            // 通过音频类型auditType过滤
            String configs = StreamSupport.stream(jsonArrayConfigs.spliterator(), false)
                    .filter(config -> audioType.equalsIgnoreCase(config.path("name").asText()))
                    .findFirst()
                    .map(Object::toString)
                    .orElse(null);
            configVo.setConfigs(configs);
            log.info("[audioType], name = {}", audioType);
        } catch (Exception ex) {
            throw new ServerException("无相关配置信息！");
        }
        return configVo;
    }

    /**
     * 更新硬终端音频配置
     *
     * @param audioConfigDto
     * @return
     */
    public Map<String, Boolean> updateHardTerminalAudioConfig(AudioConfigDto audioConfigDto) {
        String mainIp = serverListService.getMainNodeInternalIP();
        String deviceId = audioConfigDto.getDeviceId();
        String updateUrl = "http://" + mainIp + String.format(":11111/api/rest/internal/v1/device/%s/configs", deviceId);
        HttpEntity<String> httpEntity = new HttpEntity(audioConfigDto.getAudioConfig());

        ResponseEntity<String> response = restTemplate.exchange(updateUrl, HttpMethod.PUT, httpEntity, String.class);
        Map<String, Boolean> responseMap = new HashMap<>();
        responseMap.put("success", response.getStatusCode().is2xxSuccessful());
        log.info("[updateAudioConfig], response = {}", response);
        return responseMap;
    }

    public Map<String, String> getHierarchicalRecordingSwitch() {
        String mainIp = serverListService.getMainNodeInternalIP();
        String getSiteCodeUrl = "http://" + mainIp + ":11111/api/rest/sitecode/internal/ratelimited/enterprise/config/v1?enterpriseid=default_enterprise&configName=HierarchicalRecordingSwitch";
        Map<String, String> response;
        try {
            response = restTemplate.getForObject(getSiteCodeUrl, Map.class);
        } catch (Exception ex) {
            log.error("[getHierarchicalRecordingSwitch], errorMsg = {}", ex.getMessage());
            throw new ServerException("分级录制开关状态获取失败！");
        }
        return response;
    }

    public void updateHierarchicalRecordingSwitch(Map<String, String> switchState) {
        String mainIp = serverListService.getMainNodeInternalIP();
        String updateSiteCodeUrl = "http://" + mainIp + ":11111/api/rest/sitecode/internal/ratelimited/enterprise/config/v1/put?enterpriseid=default_enterprise&configName=HierarchicalRecordingSwitch";
        String updateOceanUrl = "http://" + mainIp + ":11111/api/rest/ocean/enterprise/private/siteCodeConfig";
        try {
            String siteCodeResponse = restTemplate.postForObject(updateSiteCodeUrl, switchState, String.class);
            switchState.put("enterpriseId", "default_enterprise");
            String oceanResponse = restTemplate.postForObject(updateOceanUrl, switchState, String.class);
            log.info("分级录制开关切换-siteCodeResponse, msg = {}", siteCodeResponse);
            log.info("分级录制开关切换-oceanResponse, msg = {}", oceanResponse);
        } catch (Exception ex) {
            log.error("[updateHierarchicalRecordingSwitch], errorMsg = {}", ex.getMessage());
            throw new ServerException("分级录制开关切换失败！");
        }
    }

    public GPUBlacklistDto getSoftTerminalGPUBlacklistConfig(String configType) {
        String mainIp = serverListService.getMainNodeInternalIP();
        String getConfigUrl = "http://" + mainIp + String.format(":11111/api/rest/clientConfg/internal/v1/default/config?clientConfigName=UIDisplayCustomization&configName=virtualBlackList&configType=%s", configType);
        String jsonConfig = restTemplate.getForObject(getConfigUrl, String.class);
        if (StringUtils.isBlank(jsonConfig) || "[]".equals(jsonConfig)) {
            return null;
        }
        JsonNode rootNode = JsonUtil.parseJson(jsonConfig);
        if (rootNode == null || !rootNode.isArray() || rootNode.size() == 0) {
            return null;
        }
        JsonNode firstNode = rootNode.get(0);
        String gpuBlacklistConfig = firstNode.path("configValue").asText();
        String terminateName = null;
        // configType ==1 android/ios,configType == 5 windows/mac
        if ("1".equals(configType)) {
            terminateName = "android";
        }
        if ("5".equals(configType)) {
            terminateName = "windows";
        }
        JsonNode terminateConfig = JsonUtil.parseJson(gpuBlacklistConfig).path(terminateName);

        JsonNode disableGPUsJSONArray = terminateConfig.path("disableGPUs");
        JsonNode disablePlatformJSONArray = terminateConfig.path("disablePlatform");
        JsonNode disableSystemVersionJSONArray = terminateConfig.path("disableSystemVersion");
        JsonNode disableSystemVersionForAIFaceJSONArray = terminateConfig.path("disableSystemVersionForAIFace");
        String disableGPUs = disableGPUsJSONArray == null || disableGPUsJSONArray.isNull() ? "" :
            StringUtils.join(JsonUtil.convertJsonNodeToStringArray(disableGPUsJSONArray), ';').replace("\"", "");
        String disablePlatform = disablePlatformJSONArray == null || disablePlatformJSONArray.isNull() ? "" :
            StringUtils.join(JsonUtil.convertJsonNodeToStringArray(disablePlatformJSONArray), ';').replace("\"", "");
        String disableSystemVersion = disableSystemVersionJSONArray == null || disableSystemVersionJSONArray.isNull() ? "" :
            StringUtils.join(JsonUtil.convertJsonNodeToStringArray(disableSystemVersionJSONArray), ';').replace("\"", "");
        String disableSystemVersionForAIFace = disableSystemVersionForAIFaceJSONArray == null ? "" :
            StringUtils.join(JsonUtil.convertJsonNodeToStringArray(disableSystemVersionForAIFaceJSONArray), ';').replace("\"", "");

        GPUBlacklistDto gpuBlacklistDto = new GPUBlacklistDto();
        gpuBlacklistDto.setDisableGPUs(disableGPUs);
        gpuBlacklistDto.setDisablePlatform(disablePlatform);
        gpuBlacklistDto.setDisableSystemVersion(disableSystemVersion);
        gpuBlacklistDto.setDisableSystemVersionForAIFace(disableSystemVersionForAIFace);
        gpuBlacklistDto.setConfigType(configType);
        log.info("[getSoftTerminalGPUBlacklistConfig], config = {}", gpuBlacklistConfig);
        return gpuBlacklistDto;
    }

    public Map<String, Boolean> updateSoftTerminalGPUBlacklistConfig(GPUBlacklistDto gpuBlacklistDto) {
        String mainIp = serverListService.getMainNodeInternalIP();
        String updateConfigUrl = "http://" + mainIp + ":11111/api/rest/clientConfg/internal/v1/default/config";
        String disableSystemVersion = gpuBlacklistDto.getDisableSystemVersion();
        String disableSystemVersionForAIFace = gpuBlacklistDto.getDisableSystemVersionForAIFace();
        String disableGPUs = gpuBlacklistDto.getDisableGPUs();
        String disablePlatform = gpuBlacklistDto.getDisablePlatform();

        HashMap<String, String> requestBodyMap = new HashMap<>();
        requestBodyMap.put("clientConfigName", "UIDisplayCustomization");
        requestBodyMap.put("configName", "virtualBlackList");
        requestBodyMap.put("configType", gpuBlacklistDto.getConfigType());

        HashMap<String, String[]> terminalConfigMap = new HashMap<>();
        terminalConfigMap.put("disableSystemVersion", StringUtils.isBlank(disableSystemVersion) ? new String[0] : disableSystemVersion.split(";"));
        terminalConfigMap.put("disableSystemVersionForAIFace", StringUtils.isBlank(disableSystemVersionForAIFace) ? new String[0] : disableSystemVersionForAIFace.split(";"));
        terminalConfigMap.put("disableGPUs", StringUtils.isBlank(disableGPUs) ? new String[0] : disableGPUs.split(";"));
        terminalConfigMap.put("disablePlatform", StringUtils.isBlank(disablePlatform) ? new String[0] : disablePlatform.split(";"));

        HashMap<String, HashMap<String, String[]>> configValueMap = new HashMap<>();
        if ("1".equals(gpuBlacklistDto.getConfigType())) {
            configValueMap.put("ios", terminalConfigMap);
            configValueMap.put("android", terminalConfigMap);
        } else if ("5".equals(gpuBlacklistDto.getConfigType())) {
            configValueMap.put("mac", terminalConfigMap);
            configValueMap.put("windows", terminalConfigMap);
        }

        requestBodyMap.put("configValue", JsonUtil.toJson(configValueMap));

        HttpEntity<String> httpEntity = new HttpEntity(Collections.singletonList(requestBodyMap));
        ResponseEntity<String> response = restTemplate.exchange(updateConfigUrl, HttpMethod.POST, httpEntity, String.class);
        Map<String, Boolean> responseMap = new HashMap<>();
        responseMap.put("success", response.getStatusCode().is2xxSuccessful());
        log.info("[updateSoftTerminalGPUBlacklistConfig], response = {}", response);
        return responseMap;
    }


    /**
     * 校验数据库密码
     *
     * @param pwd
     * @return
     */
    public boolean validateDbPassword(String pwd) {
        if (StringUtils.isBlank(pwd) || pwd.length() < 8) {
            return false;
        }
        if (!pwd.matches(".*\\d+.*")) {
            return false;
        }
        if (!pwd.matches(".*[a-z]+.*")) {
            return false;
        }
        if (!pwd.matches(".*[A-Z]+.*")) {
            return false;
        }
        if (!pwd.matches(".*[~!@#$%^&*()_+|<>,.?/:;'\\[\\]{}\"]+.*")) {
            return false;
        }
        return true;
    }

    /**
     * 刷新数据库配置（重启数据库+重启依赖数据库的业务服务）
     *
     * @param isUpdDbPort 是否修改端口
     * @param isUpdPwd    是否修改密码
     * @param dbService   数据库服务类型
     * @param dbPassword  数据库密码（base64后的）
     * @param dbPort      数据库端口号
     */
    public void refreshDbConfig(Boolean isUpdDbPort, Boolean isUpdPwd, String dbService, String dbPassword, String dbPort) {
        log.info("refreshDbConfig invoke start. isUpdDbPort={}, isUpdPwd={}, dbService={}", isUpdDbPort, isUpdPwd, dbService);
        if (isUpdDbPort && StringUtils.isNotBlank(dbPort)) {
            doRefreshDbConfig(dbService, null, dbPort);
        }
        if (isUpdPwd && StringUtils.isNotBlank(dbPassword)) {
            doRefreshDbConfig(dbService, new String(Base64.decodeBase64(dbPassword)), null);
        }
        log.info("refreshDbConfig invoke end. isUpdDbPort={}, isUpdPwd={}, dbService={}", isUpdDbPort, isUpdPwd, dbService);
    }

    public void refreshOceanBasePwd(String dbPassword) {
        log.info("refreshOceanBaseConfig update pwd");
        boolean refreshPwd = updateOceanBasePwd(dbPassword);
        if (!refreshPwd) {
            return;
        }
        //log.info("refreshOceanBaseConfig change config");
        //updateDbConfigToAllAzkabanCM(dbPassword, null);
        //updateDbConfigToAllHiveCM(dbPassword, null);
        //updateDbConfigToCronJobStatisCleanupJob(dbPassword);
        //updateDbConfigToMatrixOdbcCM(dbPassword, null);
        //storeOriginDbPassword("matrix_db_password", dbPassword);
        log.info("refreshOceanBaseConfig restart server");
        deletePods(mainDbServices, 10);
        //deletePods(uaaDbServices, 2);
        //deletePods(statisDbServices, 5);
        //deletePods(survDbServices, 1);
        //deletePods(matrixDbServices, 1);
        //deletePods(eduDbServices, 1);
    }

    private void deletePods(List<String> podNames, long min) {
        podNames.forEach(applabel -> k8sService.deletePodByLabelApp(applabel));
        try {
            Thread.sleep(min * 1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    private boolean updateOceanBasePwd(String dbPassword) {
        int loopCount = 1;
        String labelApp = "private-oceanbase";
        do {
            try {
                List<Pod> podList = deployService.listPodsByAppLabel(labelApp);
                if (!CollectionUtils.isEmpty(podList)) {
                    Pod pod = podList.get(0);
                    if ("Running".equals(pod.getStatusPhase())) {
                        jdbcUtils.updateOceanBasePwd(pod.getIp(), dbPassword);
                        return true;
                    }
                }
            } catch (Exception e) {
                log.warn("Update mysql password failed. labelApp = {}, loopCount = {} ", labelApp, loopCount, e);
            }
            try {
                Thread.sleep(8 * 1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            loopCount++;
        } while (loopCount <= 10);
        return false;
    }

    /**
     * 更新mysql-root密码（重启数据库+重启依赖数据库的业务服务）
     *
     * @param isUpdPwd   是否修改密码
     * @param dbPassword 数据库密码（base64后的）
     */
    public void updateMysqlRootConfig(Boolean isUpdPwd, String dbPassword) {
        log.info("refreshMysqlRootConfig invoke start.  isUpdPwd={}", isUpdPwd);
        if (isUpdPwd && StringUtils.isNotBlank(dbPassword)) {
            doRefreshMysqlConfig(new String(Base64.decodeBase64(dbPassword)));
        }
        log.info("refreshMysqlRootConfig invoke end. isUpdPwd={}", isUpdPwd);
    }

    /**
     * 执行刷新数据库配置
     *
     * @param dbService
     * @param dbPassword 密码明文
     * @param dbPort
     */
    public void doRefreshDbConfig(String dbService, String dbPassword, String dbPort) {
        //修改数据库端口 (针对 main,statis,uaa,surv,matrix均生效)
        if (StringUtils.isNotBlank(dbPort)) {
            //修改mysql配置文件: my-cnf，替换端口号
            Map<String, String> mysqlCnf = k8sService.getConfigmap("mysql-cnf");
            if (!mysqlCnf.isEmpty()) {
                mysqlCnf.put("my-cnf", mysqlCnf.get("my-cnf").replaceAll("port=\\d+", "port=" + dbPort + ""));
                k8sService.editConfigmap("mysql-cnf", mysqlCnf);
                log.info("Update-DB-Config edit mysql-cnf done .");
            }
            Map<String, String> mysqlSlaveCnf = k8sService.getConfigmap("mysqlslave-cnf");
            if (!mysqlSlaveCnf.isEmpty()) {
                mysqlSlaveCnf.put("my-cnf", mysqlSlaveCnf.get("my-cnf").replaceAll("port=\\d+", "port=" + dbPort + ""));
                k8sService.editConfigmap("mysqlslave-cnf", mysqlSlaveCnf);
                log.info("Update-DB-Config edit mysqlslave-cnf done .");
            }

            //重启 private-mysql，private-mysqlslave， 使修改后的端口号生效
            k8sService.deletePodByLabelApp("private-mysql");
            log.info("Update-DB-Config restart private-mysql done .");
            k8sService.deletePodByLabelApp("private-mysqlslave");
            log.info("Update-DB-Config restart private-mysqlslave done .");
            k8sService.deletePodByLabelApp("private-statis-mysql");
            log.info("Update-DB-Config restart private-statis-mysql done .");
            k8sService.deletePodByLabelApp("private-uaa-mysql");
            log.info("Update-DB-Config restart private-uaa-mysql done .");
            k8sService.deletePodByLabelApp("private-surv-mysql");
            log.info("Update-DB-Config restart private-surv-mysql done .");
            k8sService.deletePodByLabelApp("private-matrix-mysql");
            log.info("Update-DB-Config restart private-matrix-mysql done .");
            k8sService.deletePodByLabelApp("private-edu-mysql");
            log.info("Update-DB-Config restart private-edu-mysql done .");

            //更新 iptables， private-consumer-config-1514529486 mysql端口号
            updateDbConfigToIptablesCM(dbPort);
            log.info("Update-DB-Config update iptables mysql port done .");
            updateDbConfigToPrivateConsumerConfigCM(dbPort);
            log.info("Update-DB-Config update private-consumer-config-1514529486 mysql port done .");

            //更新 mysql-rep.yaml mysql端口号，重启main-mysql相关业务服务
            updateDbConfigToMysqlRepCM(dbPort);
            log.info("Update-DB-Config update mysql-rep mysql port done .");
            mainDbServices.forEach(applabel -> k8sService.deletePodByLabelApp(applabel));
            log.info("Update-DB-Config restart services depended on private-mysql done .");

            //更新 all-azkaban.yaml all-hive.yaml mysql密码及端口号，重启statis-mysql相关服务
            updateDbConfigToAllAzkabanCM(dbPassword, dbPort);
            log.info("Update-DB-Config update all-azkaban mysql port done .");
            updateDbConfigToAllHiveCM(dbPassword, dbPort);
            log.info("Update-DB-Config update all-hive mysql port done .");
            statisDbServices.forEach(applabel -> k8sService.deletePodByLabelApp(applabel));
            log.info("Update-DB-Config restart services depended on private-statis-mysql done .");

            //重启 surv-mysql， uaa-mysql相关业务服务
            survDbServices.forEach(applabel -> k8sService.deletePodByLabelApp(applabel));
            log.info("Update-DB-Config restart services depended on private-surv-mysql done .");
            uaaDbServices.forEach(applabel -> k8sService.deletePodByLabelApp(applabel));
            log.info("Update-DB-Config restart services depended on private-uaa-mysql done .");

            //更新 matrix-odbc.yaml mysql端口号，重启matrix-mysql相关业务服务
            updateDbConfigToMatrixOdbcCM(dbPassword, dbPort);
            log.info("Update-DB-Config update matrix-odbc mysql port done .");
            matrixDbServices.forEach(applabel -> k8sService.deletePodByLabelApp(applabel));
            log.info("Update-DB-Config restart services depended on private-matrix-mysql done .");

            //重启 edu-mysql 相关业务服务
            eduDbServices.forEach(applabel -> k8sService.deletePodByLabelApp(applabel));
            log.info("Update-DB-Config restart services depended on private-edu-mysql done .");
        }
        //修改数据库密码（默认修改该账号 $MAIN_DB_USERNAME 的密码）
        if (StringUtils.isNotBlank(dbPassword)) {
            if (DBType.main.name().equals(dbService)) {
                updateMysqlPwd("private-mysql", dbPassword);
                log.info("Update-DB-Config update private-msyql password done .");
                updateMysqlPwd("private-mysqlslave", dbPassword);
                log.info("Update-DB-Config update private-msyqlslave password done .");
                mainDbServices.forEach(applabel -> k8sService.deletePodByLabelApp(applabel));
                log.info("Update-DB-Config restart services depended on private-mysql done .");
            } else if (DBType.statis.name().equals(dbService)) {
                updateMysqlPwd("private-statis-mysql", dbPassword);
                log.info("Update-DB-Config update private-statis-msyql password done .");
                updateDbConfigToAllAzkabanCM(dbPassword, dbPort);
                log.info("Update-DB-Config update all-azkaban mysql password done .");
                updateDbConfigToAllHiveCM(dbPassword, dbPort);
                log.info("Update-DB-Config update all-hive mysql password done .");
                updateDbConfigToCronJobStatisCleanupJob(dbPassword);
                log.info("Update-DB-Config update statis-clean-job mysql password done .");
                statisDbServices.forEach(applabel -> k8sService.deletePodByLabelApp(applabel));
                log.info("Update-DB-Config restart services depended on private-statis-mysql done .");
            } else if (DBType.surv.name().equals(dbService)) {
                updateMysqlPwd("private-surv-mysql", dbPassword);
                log.info("Update-DB-Config update password private-surv-msyql done .");
                survDbServices.forEach(applabel -> k8sService.deletePodByLabelApp(applabel));
                log.info("Update-DB-Config restart services depended on private-surv-mysql done .");
            } else if (DBType.webrtc.name().equals(dbService)) {
                updateMysqlPwd("private-uaa-mysql", dbPassword);
                log.info("Update-DB-Config update password private-uaamsyql done .");
                updateMysqlPwd("private-uaa-mysqlslave", dbPassword);
                log.info("Update-DB-Config update password private-uaa-mysqlslave done .");
                uaaDbServices.forEach(applabel -> k8sService.deletePodByLabelApp(applabel));
                log.info("Update-DB-Config restart services depended on private-uaa-mysql done .");
            } else if (DBType.matrix.name().equals(dbService)) {
                updateMysqlPwd("private-matrix-mysql", dbPassword);
                log.info("Update-DB-Config update password private-matrix-msyql done .");
                updateDbConfigToMatrixOdbcCM(dbPassword, dbPort);
                log.info("Update-DB-Config update matrix-odbc mysql password done .");
                storeOriginDbPassword("matrix_db_password", dbPassword);
                log.info("Update-DB-Config store matrix_db_password done .");
                matrixDbServices.forEach(applabel -> k8sService.deletePodByLabelApp(applabel));
                log.info("Update-DB-Config restart services depended on private-matrix-mysql done .");
            } else if (DBType.edu.name().equals(dbService)) {
                updateMysqlPwd("private-edu-mysql", dbPassword);
                log.info("Update-DB-Config update password private-edu-msyql done .");
                eduDbServices.forEach(applabel -> k8sService.deletePodByLabelApp(applabel));
                log.info("Update-DB-Config restart services depended on private-edu-mysql done .");
            }
        }
    }

    /**
     * 修改mysql-root密码
     *
     * @param dbPassword 密码明文
     */
    public void doRefreshMysqlConfig(String dbPassword) {
        //Main
        updateMysqlRootPwd("private-mysql", dbPassword);
        log.info("Update-DB-Config update private-msyql password done .");
        updateMysqlRootPwd("private-mysqlslave", dbPassword);
        log.info("Update-DB-Config update private-msyqlslave password done .");
        mainDbServices.forEach(applabel -> k8sService.deletePodByLabelApp(applabel));
        log.info("Update-DB-Config restart services depended on private-mysql done .");

        //statis
        updateMysqlRootPwd("private-statis-mysql", dbPassword);
        log.info("Update-DB-Config update private-statis-msyql password done .");
        updateDbConfigToAllAzkabanCM(dbPassword, null);
        log.info("Update-DB-Config update all-azkaban mysql password done .");
        updateDbConfigToAllHiveCM(dbPassword, null);
        log.info("Update-DB-Config update all-hive mysql password done .");
        updateDbConfigToCronJobStatisCleanupJob(dbPassword);
        log.info("Update-DB-Config update statis-clean-job mysql password done .");
        statisDbServices.forEach(applabel -> k8sService.deletePodByLabelApp(applabel));
        log.info("Update-DB-Config restart services depended on private-statis-mysql done .");

        //surv
        updateMysqlRootPwd("private-surv-mysql", dbPassword);
        log.info("Update-DB-Config update password private-surv-msyql done .");
        survDbServices.forEach(applabel -> k8sService.deletePodByLabelApp(applabel));
        log.info("Update-DB-Config restart services depended on private-surv-mysql done .");

        //webrtc
        updateMysqlRootPwd("private-uaa-mysql", dbPassword);
        log.info("Update-DB-Config update password private-uaamsyql done .");
        uaaDbServices.forEach(applabel -> k8sService.deletePodByLabelApp(applabel));
        log.info("Update-DB-Config restart services depended on private-uaa-mysql done .");

        //matrix
        updateMysqlRootPwd("private-matrix-mysql", dbPassword);
        log.info("Update-DB-Config update password private-matrix-msyql done .");
        updateDbConfigToMatrixOdbcCM(dbPassword, null);
        log.info("Update-DB-Config update matrix-odbc mysql password done .");
//        storeOriginDbPassword("matrix_db_password", dbPassword);
        log.info("Update-DB-Config store matrix_db_password done .");
        matrixDbServices.forEach(applabel -> k8sService.deletePodByLabelApp(applabel));
        log.info("Update-DB-Config restart services depended on private-matrix-mysql done .");

        //edu
        updateMysqlRootPwd("private-edu-mysql", dbPassword);
        log.info("Update-DB-Config update password private-edu-msyql done .");
        eduDbServices.forEach(applabel -> k8sService.deletePodByLabelApp(applabel));
        log.info("Update-DB-Config restart services depended on private-edu-mysql done .");

    }

    private void updateMysqlPwd(String labelApp, String newPassword) {
        int loopCount = 1;
        do {
            try {
                List<Pod> podList = deployService.listPodsByAppLabel(labelApp);
                if (!CollectionUtils.isEmpty(podList)) {
                    Pod pod = podList.get(0);
                    if ("Running".equals(pod.getStatusPhase())) {
                        jdbcUtils.updateMysqlPwd(pod.getIp(), newPassword);
                        return;
                    }
                }
            } catch (Exception e) {
                log.warn("Update mysql password failed. labelApp = {}, loopCount = {} ", labelApp, loopCount, e);
            }
            try {
                Thread.sleep(10 * 1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            loopCount++;
        } while (loopCount <= 10);
    }

    private void updateMysqlRootPwd(String labelApp, String newPassword) {
        int loopCount = 1;
        do {
            try {
                List<Pod> podList = deployService.listPodsByAppLabel(labelApp);
                if (!CollectionUtils.isEmpty(podList)) {
                    Pod pod = podList.get(0);
                    if ("Running".equals(pod.getStatusPhase())) {
                        jdbcUtils.updateMysqlRootPwd(pod.getIp(), newPassword);
                        return;
                    }
                }
            } catch (Exception e) {
                log.warn("Update mysql password failed. labelApp = {}, loopCount = {} ", labelApp, loopCount, e);
            }
            try {
                Thread.sleep(10 * 1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            loopCount++;
        } while (loopCount <= 10);
    }

    /**
     * 修改 mysql-rep.yaml 数据库端口号
     * 依赖 main-mysql
     *
     * @param dbPort
     */
    public void updateDbConfigToMysqlRepCM(String dbPort) {
        String configmapName = "mysql-rep";
        String key = "mysql-rep.sh";
        Map<String, String> mysqlCnf = k8sService.getConfigmap(configmapName);
        if (!mysqlCnf.isEmpty()) {
            if (StringUtils.isNotBlank(dbPort)) {
                mysqlCnf.put(key, mysqlCnf.get(key).replaceAll("master_port=\\d+", "master_port=" + dbPort + ""));
            }
            k8sService.editConfigmap(configmapName, mysqlCnf);
        }
    }

    /**
     * 修改 azkaban.properties 数据库密码及端口
     * 依赖 statis-mysql
     *
     * @param dbPwd
     * @param dbPort
     */
    public void updateDbConfigToAllAzkabanCM(String dbPwd, String dbPort) {
        String configmapName = "all-azkaban";
        String key = "azkaban.properties";
        Map<String, String> mysqlCnf = k8sService.getConfigmap(configmapName);
        if (!mysqlCnf.isEmpty()) {
            if (StringUtils.isNotBlank(dbPwd)) {
                mysqlCnf.put(key, mysqlCnf.get(key).replaceAll("mysql.password=.*\n", "mysql.password=" + dbPwd + "\n"));
            }
            if (StringUtils.isNotBlank(dbPort)) {
                mysqlCnf.put(key, mysqlCnf.get(key).replaceAll("mysql.port=\\d+", "mysql.port=" + dbPort + ""));
            }
            k8sService.editConfigmap(configmapName, mysqlCnf);
        }
    }

    /**
     * 修改 all-hive 数据库密码及端口
     * 依赖 statis-mysql
     *
     * @param dbPwd
     * @param dbPort
     */
    public void updateDbConfigToAllHiveCM(String dbPwd, String dbPort) {
        String configmapName = "all-hive";
        String key1 = "HIVE_SITE_CONF_javax_jdo_option_ConnectionURL";
        String key2 = "HIVE_SITE_CONF_javax_jdo_option_ConnectionPassword";
        Map<String, String> mysqlCnf = k8sService.getConfigmap(configmapName);
        if (!mysqlCnf.isEmpty()) {
            if (StringUtils.isNotBlank(dbPort)) {
                mysqlCnf.put(key1, "jdbc:mysql://${STATIS_DATABASE_IP}:" + dbPort + "/hive");
//                mysqlCnf.put(key1, mysqlCnf.get(key1).replaceAll("jdbc:mysql://$\\{STATIS_DATABASE_IP}:\\d/hive", "jdbc:mysql://${STATIS_DATABASE_IP}:"+dbPort+"/hive"));
            }
            if (StringUtils.isNotBlank(dbPwd)) {
                mysqlCnf.put(key2, dbPwd);
            }
            k8sService.editConfigmap(configmapName, mysqlCnf);
        }
    }

    /**
     * 修改 iptables mysql端口号
     *
     * @param dbPort
     */
    public void updateDbConfigToIptablesCM(String dbPort) {
        String key = "ports.txt";
        Map<String, String> mysqlCnf = k8sService.getConfigmap(Constants.CONFIGMAP_IPTABLES);
        if (!mysqlCnf.isEmpty()) {
            if (StringUtils.isNotBlank(dbPort)) {
                mysqlCnf.put(key, mysqlCnf.get(key).replaceAll("\\d+#mysql", "" + dbPort + "#mysql"));
            }
            k8sService.editConfigmap(Constants.CONFIGMAP_IPTABLES, mysqlCnf);
        }
    }

    /**
     * 修改 matrix-odbc 数据库密码及端口
     * 依赖 matrix-mysql
     *
     * @param dbPwd
     * @param dbPort
     */
    public void updateDbConfigToMatrixOdbcCM(String dbPwd, String dbPort) {
        String configmapName = "matrix-odbc";
        String key = "odbc.ini";
        Map<String, String> mysqlCnf = k8sService.getConfigmap(configmapName);
        if (!mysqlCnf.isEmpty()) {
            if (StringUtils.isNotBlank(dbPwd)) {
                mysqlCnf.put(key, mysqlCnf.get(key).replaceAll("Password = .*\n", "Password = " + dbPwd + "\n"));
            }
            if (StringUtils.isNotBlank(dbPort)) {
                mysqlCnf.put(key, mysqlCnf.get(key).replaceAll("Port = \\d+", "Port = " + dbPort + ""));
            }
            k8sService.editConfigmap(configmapName, mysqlCnf);
        }
    }

    /**
     * 修改 private-consumer-config 数据库端口号
     *
     * @param dbPort
     */
    public void updateDbConfigToPrivateConsumerConfigCM(String dbPort) {
        String configmapName = "private-consumer-config-1514529486";
        String key = "consumer.cfg";
        Map<String, String> mysqlCnf = k8sService.getConfigmap(configmapName);
        if (!mysqlCnf.isEmpty()) {
            if (StringUtils.isNotBlank(dbPort)) {
                mysqlCnf.put(key, mysqlCnf.get(key).replaceAll("\"port\": \"\\d\"", "\"port\": \"" + dbPort + "\""));
            }
            k8sService.editConfigmap(configmapName, mysqlCnf);
        }
    }

    /**
     * 存储数据库密码明文到 ip-change
     *
     * @param dbService main-db
     * @param dbPwd
     */
    public void storeOriginDbPassword(String dbService, String dbPwd) {
        String configmapName = "ip-change";
        String key = "jasypt_config";
        Map<String, String> mysqlCnf = k8sService.getConfigmap(configmapName);
        if (!mysqlCnf.isEmpty()) {
            if (StringUtils.isNotBlank(dbPwd)) {
                mysqlCnf.put(key, mysqlCnf.get(key).replaceAll(dbService + ":.*\n", dbService + ":" + dbPwd + "\n"));
            }
            k8sService.editConfigmap(configmapName, mysqlCnf);
        }
    }

    /**
     * 修改 statis-clean-job 数据库密码
     *
     * @param dbPwd
     */
    public void updateDbConfigToCronJobStatisCleanupJob(String dbPwd) {
        try {
            CronJob cronJob = deployService.getCronJobByName("statis-cleanup-job", Constants.NAMESPACE_DEFAULT);
            if (cronJob != null) {
                for (Container container : cronJob.getContainers()) {
                    List<String> commands = container.getCommand();
                    commands = commands.stream().map(command -> command.replaceAll(" -p\".*\" ", " -p\"" + dbPwd + "\" ")).collect(Collectors.toList());
                    container.setCommand(commands);
                }
                deployService.patchCronJobCommand("statis-cleanup-job", Constants.NAMESPACE_DEFAULT, cronJob.getContainers());
            }
        } catch (Exception e) {
            log.error("updateDbConfigToCronJobStatisCleanupJob error. ", e);
        }
    }

    /**
     * 更新ios_app_store链接
     */
    public void updateIosAppStore(String customizedKey) {
        Map<String, String> allIpCM = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        String domain = allIpCM.get(NetworkConstants.MAIN_DOMAIN_NAME);
        String sslPort = allIpCM.get(NetworkConstants.MAIN_NGINX_SSL_PORT);
        if (StringUtils.isNoneBlank(domain, sslPort)) {
            String iosAppStoreLink = "itms-services://?action=download-manifest\\&url=https://" + domain + ":" + sslPort + "/appdownload/ios_office/" + customizedKey + "/manifest.plist";
            allIpCM.put(NetworkConstants.CUSTOMIZE_IOS_STORE, iosAppStoreLink);
            k8sService.editConfigmap(Constants.CONFIGMAP_ALLIP, allIpCM);
        }
    }

    /**
     * 获取检查配置，目前包括数据库和sdk-token
     *
     * @return
     */
    public ConfigCheckVo getCheckConfig() {
        List<ConfigCheckInfo> checkInfoList = getDbCheckInfoList();
        checkInfoList.add(getSdkCheckInfo());
        checkInfoList.add(getMailServerCheckInfo());
        ConfigCheckVo res = new ConfigCheckVo();
        res.setNormal(true);
        checkInfoList.forEach(x -> {
            if (!x.isStatus()) {
                res.setNormal(false);
            }
        });
        res.setCheckInfoList(checkInfoList);
        return res;
    }

    /**
     * 获取sdk-token的检查状态
     *
     * @return
     */
    private ConfigCheckInfo getSdkCheckInfo() {
        ConfigCheckInfo sdkToken = new ConfigCheckInfo(ConfigCheckEnum.SDK_TOKEN);
        if (jdbcUtils.sdkTokenCheck()) {
            sdkToken.setStatus(true);
        } else {
            sdkToken.setStatus(false);
            sdkToken.setStatusDescription("云视讯API-token未更改");
        }
        return sdkToken;
    }

    /**
     * 获取mail-server的检查状态
     *
     * @return
     */
    private ConfigCheckInfo getMailServerCheckInfo() {
        ConfigCheckInfo mailServer = new ConfigCheckInfo(ConfigCheckEnum.MAIL_SERVER);
        boolean isSuccess = privateDataService.getLastTestMailServerResult(true);
        if (isSuccess) {
            mailServer.setStatus(true);
        } else {
            mailServer.setStatus(false);
            mailServer.setStatusDescription("发送测试邮件失败");
        }
        return mailServer;
    }

    /**
     * 获取数据库的检查信息列表
     *
     * @return
     */
    private List<ConfigCheckInfo> getDbCheckInfoList() {
        Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);

        String dbType = allIp.get("DATABASE_TYPE");
        List<ConfigCheckInfo> res = new ArrayList<>();

        //检查数据库
        if ("DM".equals(dbType) || "JC".equals(dbType) || "ST".equals(dbType)) {
            DBCommon dbService = SpringBeanUtil.getBean(dbType + "Service", DBCommon.class);
            res.addAll(dbService.checkLicense());
        }
        if (!"MYSQL".equalsIgnoreCase(dbType)) {
            return res;
        }

        //过滤出mysql相关的pod
        List<Pod> mysqlPod = deployService.listPodsByAppLabel("mysql");

        Map<String, String> privateData = k8sService.getConfigmap(Constants.CONFIGMAP_PRIVATE_DATA);

        res.add(getDbCheckInfo("private-mysql", mysqlPod, privateData.get(NetworkConstants.MAIN_DB_EXPIRATION_TIME), ConfigCheckEnum.MAIN_DB, serverListService.getUsernameOrPwdFromCM(allIp, NetworkConstants.MAIN_DB_SUPER_PASSWORD)));
        res.add(getDbCheckInfo("private-statis-mysql", mysqlPod, privateData.get(NetworkConstants.STATIS_DB_EXPIRATION_TIME), ConfigCheckEnum.STATIS_DB, serverListService.getUsernameOrPwdFromCM(allIp, NetworkConstants.STATIS_DB_PASSWORD)));
        res.add(getDbCheckInfo("private-uaa-mysql", mysqlPod, privateData.get(NetworkConstants.WEBRTC_DB_EXPIRATION_TIME), ConfigCheckEnum.UAA_DB, serverListService.getUsernameOrPwdFromCM(allIp, NetworkConstants.UAA_DB_PASSWORD)));
        res.add(getDbCheckInfo("private-surv-mysql", mysqlPod, privateData.get(NetworkConstants.SURV_DB_EXPIRATION_TIME), ConfigCheckEnum.SURV_DB, serverListService.getUsernameOrPwdFromCM(allIp, NetworkConstants.SURV_DB_PASSWORD)));
        res.add(getDbCheckInfo("private-edu-mysql", mysqlPod, privateData.get(NetworkConstants.EDU_DB_EXPIRATION_TIME), ConfigCheckEnum.EDU_DB, serverListService.getUsernameOrPwdFromCM(allIp, NetworkConstants.EDU_DB_PASSWORD)));
        res.add(getDbCheckInfo("private-matrix-mysql", mysqlPod, privateData.get(NetworkConstants.MATRIX_DB_EXPIRATION_TIME), ConfigCheckEnum.MATRIX_DB, serverListService.getUsernameOrPwdFromCM(allIp, NetworkConstants.MATRIX_DB_PASSWORD)));

        return res.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private ConfigCheckInfo getDbCheckInfo(String label, List<Pod> mysqlPod, String expireTime, ConfigCheckEnum configCheckEnum, String pwd) {
        //未部署
        List<Pod> labelPod = mysqlPod.stream().filter(x -> label.equalsIgnoreCase(x.getAppLabel())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(labelPod)) {
            return null;
        }

        ConfigCheckInfo res = new ConfigCheckInfo(configCheckEnum);

        //为默认密码
        if (StringUtils.isBlank(pwd) || "Da?548!YZ".equals(pwd)) {
            res.setStatus(false);
            res.setStatusDescription("数据库密码为初始密码,请修改密码");
            return res;
        }

        //未设置过期时间
        if (StringUtils.isBlank(expireTime)) {
            res.setStatus(false);
            res.setStatusDescription("数据库密码未设置过期时间,请设置过期时间");
            return res;
        }

        //过期了
        long expireStamp = Long.parseLong(expireTime);
        long currentStamp = System.currentTimeMillis();
        if (currentStamp > expireStamp) {
            res.setStatus(false);
            res.setStatusDescription("数据库密码已过期,请更改数据库密码");
            return res;
        }
        res.setStatus(true);
        return res;
    }

    public String generatePwd() {
        return Base64.encodeBase64String(GeneratePwdUtil.generatePwd().getBytes());
    }

    private void buffetConfigChanged(String configName) {
        if (StringUtils.isBlank(configName)) {
            return;
        }
        try {
            buffetRemoteClient.enterpriseConfigChanged(configName);
        } catch (Exception e) {
            log.error("Notify enterprise config:{} changed error.", configName, e);
        }
    }

    private Optional<Config> configFromIauthServer() {
        try {
            PasswordIterationsResponse response = iauthRemoteClient.passwordIterations();
            if (response != null && StringUtils.isNotBlank(response.getConfigName())) {
                return Optional.of(new Config(response.getConfigDisplayName(), response.getConfigName(), String.valueOf(response.getIterations())));
            }
        } catch (Exception e) {
            log.error("Get config from iauth server error.", e);
        }
        return Optional.empty();
    }

    private static void userPasswordIterationsCheck(String configName, String configValue) {
        if ("userPasswordIterations".equals(configName)) {
            int min = 2, max = 10000;
            try {
                int value = Integer.parseInt(configValue);
                if (value < min || value > max) {
                    throw new ServerException("配置项值范围为[" + min + "," + max + "]");
                }
            } catch (NumberFormatException e) {
                throw new ServerException("配置项值范围为[" + min + "," + max + "]");
            }
        }
    }


    public Map<String, String> getRecordingCrsSwitch() {
        Map<String, String> response = new HashMap<>();
        String data = privateDataService.getRecordingCrsSwitch();
        response.put("configName", "recordingCrsSwitch");
        response.put("configValue", data);
        return response;
    }

    /**
     * 判断开关状态是否变更
     * <br/>开启：<br/>CRS录播模式启用成功后，自动停用CMS的private-recordingserver、private-vodnetwork-proxy、private-vodnetwork-vod、private-vodnetwork-vodedit、private-srs服务，并将这些服务对应的启用按钮置灰不可操作
     * <br/>关闭：<br/>CRS录播模式停用成功后，自动启用CMS的private-recordingserver、private-vodnetwork-proxy、private-vodnetwork-vod、private-vodnetwork-vodedit、private-srs服务，并可正常操作这些服务的停用和启用
     *
     * @param switchState
     */
    public void recordingCrsSwitch(Map<String, String> switchState) {
        String configValue = switchState.get("configValue");
        int update = privateDataService.updateRecordingCrsSwitch(configValue);
        if (update > 0) {
            updateCrsState("1".equals(configValue));
        }
    }

    private void updateCrsState(boolean enable) {
        Map<String, String> crsLabels = crsLabels();
        List<Node> nodeList;
        if (SystemModeConfig.isNewCms()) {
            nodeList = deployService.listNodesByLabels("type", ServerManageData.NODE_TYPE_COMMON);
        } else {
            nodeList = deployService.listNodesByLabels("type", "vod");
        }
        if (enable) {
            nodeList.forEach(nod -> {
                log.info("Remove label:[{}] from node [{}]", crsLabels, nod.getName());
                deployService.removeNodeLabels(nod.getName(), crsLabels.keySet());
            });
            if (SystemModeConfig.isNewCms()) {
                noahApiService.notifyDeployLabelChange();
            }
        } else {
            nodeList.forEach(nod -> {
                log.info("Add label:[{}] to node [{}]", crsLabels, nod.getName());
                deployService.addNodeLabels(nod.getName(), crsLabels);
            });
            if (SystemModeConfig.isNewCms()) {
                noahApiService.notifyDeployLabelChange();
            }
        }
    }

    private Map<String, String> crsLabels() {
        return new HashMap<String, String>() {{
            put("recordingserver", "xylink");
            put("vodnetwork-proxy", "xylink");
            put("vodnetwork-vod", "xylink");
            put("vodnetwork-vodedit", "xylink");
            put("srs", "xylink");
        }};

    }

    private Map<String, String> filterBuffetEnterpriseConfig(Map<String, String> source) {
        if (!CollectionUtils.isEmpty(source)) {
            source.entrySet().removeIf(entry -> "REGISTER_INITROLE".equals(entry.getKey()));
        }
        return source;
    }

    private Optional<Config> configFromBmServer() {
        try {
            Optional<Boolean> response = basicManagementRemoteClient.userCustomNumberSwitch();
            if (response.isPresent()) {
                return Optional.of(new Config("单位级别会议号的生成规则", "userCustomNumberSwitch", String.valueOf(response.get())));
            }
        } catch (Exception e) {
            log.error("Get config:[userCustomNumberSwitch] from bm server error.", e);
        }
        return Optional.empty();
    }
}
