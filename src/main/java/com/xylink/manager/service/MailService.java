package com.xylink.manager.service;

import com.sun.mail.util.MailSSLSocketFactory;
import com.xylink.config.KafkaConstants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.config.mapper.JsonMapper;
import com.xylink.manager.controller.dto.MailConfigDto;
import com.xylink.manager.controller.dto.alert.AlertEventDto;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.rest.dto.Mail;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.manager.service.db.JasyptService;
import com.xylink.util.SpringBeanUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.mail.MailAuthenticationException;
import org.springframework.mail.MailSendException;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.security.GeneralSecurityException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Properties;
import java.util.stream.Collectors;

@Service
public class MailService {
    private static final Logger logger = LoggerFactory.getLogger(MailService.class);
    @Autowired
    private PrivateDataService privateDataService;
    @Lazy
    @Autowired
    private MailTemplateService mailTemplateService;
    @Autowired
    private IDeployService deployService;

    public void sendMail(Mail mail, MailConfigDto mailConfigDto) throws Exception {

        JavaMailSender javaMailSender = generateMailSender(mailConfigDto);

        MimeMessage mimeMessage = javaMailSender.createMimeMessage();
        MimeMessageHelper messageHelper = new MimeMessageHelper(mimeMessage, true, "UTF-8");
        InternetAddress fromAddress = new InternetAddress(mailConfigDto.getUserName(), "小鱼易连", "UTF-8");
        if (mail.getFrom() != null && !mail.getFrom().trim().isEmpty()) {
            fromAddress.setPersonal(mail.getFrom());
        }
        messageHelper.setFrom(fromAddress);
        messageHelper.setCc(fromAddress);
        messageHelper.setTo(mail.getTo().split(";"));
        messageHelper.setSubject(mail.getSubject());
        messageHelper.setText(mail.getContent(), true);
        javaMailSender.send(mimeMessage);
        logger.info("send mail to {} subject {} content {} from {} success!", mail.getTo(), mail.getSubject(), mail.getContent(), mailConfigDto.getUserName());
    }

    public void sendMail(List<Mail> mails, MailConfigDto mailConfigDto) throws Exception {

        JavaMailSender javaMailSender = generateMailSender(mailConfigDto);

        List<MimeMessage> mimeMessages = new ArrayList<>();

        InternetAddress fromAddress = new InternetAddress(mailConfigDto.getUserName(), "小鱼易连", "UTF-8");
        for (Mail mail : mails) {
            MimeMessage mimeMessage = javaMailSender.createMimeMessage();
            MimeMessageHelper messageHelper = new MimeMessageHelper(mimeMessage, true, "UTF-8");
            if (mail.getFrom() != null && !mail.getFrom().trim().isEmpty()) {
                fromAddress.setPersonal(mail.getFrom());
            }
            messageHelper.setFrom(fromAddress);
            messageHelper.setCc(fromAddress);
            messageHelper.setTo(mail.getTo().split(";"));
            messageHelper.setSubject(mail.getSubject());
            messageHelper.setText(mail.getContent(), true);
            mimeMessages.add(mimeMessage);
            logger.info("send mail to {} subject {} content {} from {} success!", mail.getTo(), mail.getSubject(), mail.getContent(), mailConfigDto.getUserName());
        }
        javaMailSender.send(mimeMessages.toArray(new MimeMessage[0]));

    }

    private JavaMailSender generateMailSender(MailConfigDto config) throws GeneralSecurityException {
        JavaMailSenderImpl javaMailSenderImpl = new JavaMailSenderImpl();
        javaMailSenderImpl.setHost(config.getHost());
        javaMailSenderImpl.setPort(config.getPort() == null ? 25 : config.getPort());
        javaMailSenderImpl.setUsername(config.getUserName());
        JasyptService jasyptService = SpringBeanUtil.getBean(JasyptService.class);
        String password = jasyptService.decrypt(config.getPassword());
        javaMailSenderImpl.setPassword(password);
        javaMailSenderImpl.setProtocol(config.getProtocol());
        javaMailSenderImpl.setDefaultEncoding(config.getEncoding());

        Properties javaMailProperties = new Properties();

        if ("smtps".equals(javaMailSenderImpl.getProtocol())) {
            MailSSLSocketFactory sf = new MailSSLSocketFactory();
            sf.setTrustAllHosts(true);
            javaMailProperties.put("mail.smtps.ssl.enable", "true");
            javaMailProperties.put("mail.smtps.ssl.socketFactory", sf);
            javaMailProperties.put("mail.smtps.timeout", 60000);
            javaMailProperties.put("mail.smtps.connectiontimeout", 60000);
            javaMailProperties.put("mail.smtps.writetimeout", 60000);
            javaMailProperties.put("mail.smtps.socketFactory.fallback", false);

        } else {
            javaMailProperties.put("mail.smtp.timeout", 60000);
            javaMailProperties.put("mail.smtp.connectiontimeout", 60000);
            javaMailProperties.put("mail.smtp.writetimeout", 60000);
        }

        javaMailSenderImpl.setJavaMailProperties(javaMailProperties);
        return javaMailSenderImpl;
    }

    public MailConfigDto getMailConfigFromConfigMap() {
        ConfigMap configMap = deployService.getConfigMapManagerData();
        if (null == configMap) {
            logger.error("no config mail configuration.");
            throw new ServerException(ErrorStatus.MAIL_NOT_CONFIG);
        }

        if (configMap.getData().containsKey("mailConfig")) {
            String mailConfigStr = configMap.getData().get("mailConfig");
            MailConfigDto mailConfigDto = JsonMapper.nonEmptyMapper().fromJson(mailConfigStr, MailConfigDto.class);
            if (StringUtils.isBlank(mailConfigDto.getHost()) || Objects.isNull(mailConfigDto.getPort())
                    || StringUtils.isBlank(mailConfigDto.getUserName()) || StringUtils.isBlank(mailConfigDto.getPassword())) {
                throw new ServerException(ErrorStatus.MAIL_NOT_CONFIG);
            }
            return mailConfigDto;
        } else {
            logger.error("no config mail configuration.");
            throw new ServerException(ErrorStatus.MAIL_NOT_CONFIG);
        }

    }


    public void send(Mail mail) throws Exception {
        MailConfigDto mailConfigDto = getMailConfigFromConfigMap();
        sendMail(mail, mailConfigDto);
    }

    public void send(List<Mail> mails) throws Exception {
        MailConfigDto mailConfigDto = getMailConfigFromConfigMap();
        sendMail(mails, mailConfigDto);
    }

    @Async
    public void asyncSend(AlertEventDto eventDto) {
        ConfigMap configMap = deployService.getConfigMapManagerData();
        if (null == configMap) {
            return;
        }
        String hostMailSwitch = configMap.getData().get("hostMailSwitch");
        String busMailSwitch = configMap.getData().get("busMailSwitch");
        if (KafkaConstants.HOST_ALERT_REASON.equals(eventDto.getReason()) && !Boolean.parseBoolean(hostMailSwitch)) {
            return;
        }
        if (KafkaConstants.BUS_ALERT_REASON.equals(eventDto.getReason()) && !Boolean.parseBoolean(busMailSwitch)) {
            return;
        }
        Mail mail = mailTemplateService.generateEventAlertMail(eventDto);
        if (null == mail) {
            logger.error("no mail");
            return;
        }
        try {
            send(mail);
        } catch (Exception e) {
            logger.info("async send mail error", e);
        }
    }

    @Async
    public void asyncSend(Mail mail) throws Exception {
        send(mail);
    }

    public MailConfigDto getMailConfig() {
        MailConfigDto mailConfigDto = privateDataService.getMailConfigCache();
        JasyptService jasyptService = SpringBeanUtil.getBean(JasyptService.class);
        mailConfigDto.setPassword(jasyptService.decrypt(mailConfigDto.getPassword()));
        return mailConfigDto;
    }

    public void updateMailConfig(MailConfigDto mailConfigDto) {
        privateDataService.saveMailConfig(mailConfigDto);
    }


    public void testMail(MailConfigDto mailConfigDto) {
        Mail mail = mailTemplateService.generateTestMail(mailConfigDto);
        try {
            sendMail(mail, mailConfigDto);
            privateDataService.setLastTestMailServerResult(true);
        } catch (MailSendException mailSendException) {
            logger.error("send test email failed.", mailSendException);
            privateDataService.setLastTestMailServerResult(false);
            throw new ServerException(ErrorStatus.TEST_MAIL_SEND_ERROR);
        } catch (MailAuthenticationException mailAuthenticationException) {
            logger.error("send test email failed.", mailAuthenticationException);
            privateDataService.setLastTestMailServerResult(false);
            throw new ServerException(ErrorStatus.TEST_MAIL_AUTHENTICATION_ERROR);
        } catch (Exception e) {
            logger.error("send test email failed.", e);
            privateDataService.setLastTestMailServerResult(false);
            throw new ServerException(ErrorStatus.TEST_MAIL_ERROR);
        }
    }

    public void testMailFromRecordSetting() {
        try {
            MailConfigDto mailConfigDto = privateDataService.getMailConfig();
            if (mailConfigDto != null && StringUtils.isNotBlank(mailConfigDto.getTestMail())) {
                Mail mail = mailTemplateService.generateTestMail(mailConfigDto);
                sendMail(mail, mailConfigDto);
                privateDataService.setLastTestMailServerResult(true);
            }
        } catch (Exception e) {
            logger.error("send test email failed.", e);
            privateDataService.setLastTestMailServerResult(false);
        }
    }

    @Async
    public void asyncSend(List<AlertEventDto> eventDtos) {
        if (CollectionUtils.isEmpty(eventDtos)) {
            return;
        }
        ConfigMap configMap = deployService.getConfigMapManagerData();
        if (null == configMap) {
            return;
        }
        String hostMailSwitch = configMap.getData().get("hostMailSwitch");
        String busMailSwitch = configMap.getData().get("busMailSwitch");
        if (KafkaConstants.HOST_ALERT_REASON.equals(eventDtos.get(0).getReason()) && !Boolean.parseBoolean(hostMailSwitch)) {
            return;
        }
        if (KafkaConstants.BUS_ALERT_REASON.equals(eventDtos.get(0).getReason()) && !Boolean.parseBoolean(busMailSwitch)) {
            return;
        }
        List<Mail> mails = eventDtos.stream().map(eventDto -> mailTemplateService.generateEventAlertMail(eventDto)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mails)) {
            logger.error("no mail");
            return;
        }
        try {
            send(mails);
        } catch (Exception e) {
            logger.info("async send mail error", e);
        }
    }

}
