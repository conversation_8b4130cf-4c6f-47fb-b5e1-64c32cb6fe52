package com.xylink.manager.service.appdownload;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.ProxyConstants;
import com.xylink.config.appdownload.AppPlatFormEnum;
import com.xylink.config.mapper.JsonMapper;
import com.xylink.manager.controller.dto.appdownload.AppDownloadConfig;
import com.xylink.manager.controller.dto.appdownload.AppDownloadInfo;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.remote.css.CssRemoteClient;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-11-09 20:44
 */
@Slf4j
@Service
public class AppDownloadServiceImpl implements AppDownloadService {
    private static final String COMMON_XYLINK_PROTOCOL_TEMPLATE = "xylink-private://";
    private static final String CK_XYLINK_PROTOCOL_TEMPLATE = "xylink-private-{CK}://";
    @Resource
    private CssRemoteClient cssRemoteClient;
    @Resource
    private K8sService k8sService;

    @Override
    public AppDownloadConfig queryConfig(String customizedKey) {
        AppDownloadConfig data = cssRemoteClient.queryAppDownloadConfig(customizedKey);
        data.setCustomizedKey(customizedKey);
        if (StringUtils.isBlank(data.getUpdateServerAddressButton())) {
            data.setUpdateServerAddressButton("0");
        }
        return data;
    }

    @Override
    public void updateConfig(AppDownloadConfig appDownloadConfig) {
        appDownloadConfig.setDefaultUpdateServerAddress(getDefaultUpdateServerAddress(appDownloadConfig.getCustomizedKey()));
        cssRemoteClient.updateAppDownloadConfig(appDownloadConfig);
    }

    @Override
    public List<AppDownloadInfo> updateServerAddressPopAppList(String customizedKey) {
        return UPDATE_SERVER_ADDRESS_POP_APP_LIST_MAPPING.stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<AppDownloadInfo> hideDownloadEntryAppList(String customizedKey) {
        return HIDE_DOWNLOAD_ENTRY_APP_LIST_MAPPING.stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public void refreshDefaultUpdateServerAddress() {
        String multiCustomizedKeys = k8sService.getConfigmap(Constants.CONFIGMAP_PRIVATE_DATA).get("MultiCustomizedKeys");
        if (StringUtils.isNotBlank(multiCustomizedKeys)) {
            Map<String, String> cksMap = JsonMapper.nonEmptyMapper().fromJson(multiCustomizedKeys, Map.class);
            cksMap.forEach((key, value) -> {
                AppDownloadConfig config = queryConfig(key);
                this.updateConfig(config);
            });
        }
        AppDownloadConfig config = queryConfig("");
        this.updateConfig(config);
    }

    private AppDownloadInfo convert(AppPlatFormEnum appPlatFormEnum) {
        return new AppDownloadInfo(appPlatFormEnum.name(), appPlatFormEnum.getClientName());
    }

    private static final List<AppPlatFormEnum> HIDE_DOWNLOAD_ENTRY_APP_LIST_MAPPING = Arrays.asList(
            AppPlatFormEnum.cert,
            AppPlatFormEnum.framework,
            AppPlatFormEnum.outlook,
            AppPlatFormEnum.desktop_pc,
            AppPlatFormEnum.desktop_mac,
            AppPlatFormEnum.desktop_loongarch64,
            AppPlatFormEnum.desktop_amd64,
            AppPlatFormEnum.desktop_mips,
            AppPlatFormEnum.desktop_arm64,
            AppPlatFormEnum.desktop_amd64_kylin,
            AppPlatFormEnum.desktop_arm64_kylin,
            AppPlatFormEnum.desktop_mips_kylin,
            AppPlatFormEnum.desktop_mips_sp1,
            AppPlatFormEnum.desktop_loongarch64_kylin,
            AppPlatFormEnum.android_office,
            AppPlatFormEnum.ios_office,
            AppPlatFormEnum.harmony_app,
            AppPlatFormEnum.harmony_pc,
            AppPlatFormEnum.rooms_android_app
    );


    private static final List<AppPlatFormEnum> UPDATE_SERVER_ADDRESS_POP_APP_LIST_MAPPING = Arrays.asList(
            AppPlatFormEnum.desktop_pc,
            AppPlatFormEnum.desktop_mac,
            AppPlatFormEnum.desktop_loongarch64,
            AppPlatFormEnum.desktop_amd64,
            AppPlatFormEnum.desktop_mips,
            AppPlatFormEnum.desktop_arm64,
            AppPlatFormEnum.desktop_amd64_kylin,
            AppPlatFormEnum.desktop_arm64_kylin,
            AppPlatFormEnum.desktop_mips_kylin,
            AppPlatFormEnum.desktop_mips_sp1,
            AppPlatFormEnum.desktop_loongarch64_kylin,
            AppPlatFormEnum.android_office,
            AppPlatFormEnum.ios_office,
            AppPlatFormEnum.harmony_app,
            AppPlatFormEnum.harmony_pc,
            AppPlatFormEnum.rooms_android_app
    );

    private String getDefaultUpdateServerAddress(String customizedKey) {
        String serverAddress = collectorServer();
        if ("{}".equalsIgnoreCase(serverAddress)) {
            return "";
        }
        String param = Base64.getEncoder().encodeToString(serverAddress.getBytes(StandardCharsets.UTF_8));
        if (StringUtils.isBlank(customizedKey)) {
            return COMMON_XYLINK_PROTOCOL_TEMPLATE + "setservers?param=" + param;
        }
        return CK_XYLINK_PROTOCOL_TEMPLATE.replace("{CK}", customizedKey) + "setservers?param=" + param;
    }

    private String collectorServer() {
        try {
            Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
            final Set<Server> servers = new HashSet<>();
            Server mainServer = new Server(allIp.get(NetworkConstants.MAIN_DOMAIN_NAME), allIp.get(NetworkConstants.MAIN_NGINX_PORT), allIp.get(NetworkConstants.MAIN_NGINX_SSL_PORT));
            servers.add(mainServer);

            Map<String, String> allMainProxy = k8sService.getConfigmap(Constants.CONFIGMAP_MAIN_PROXY);
            List<Pod> mainProxyPods = k8sService.getPodListWithLabelInApp(Constants.POD_NAME_MAIN_PROXY);
            mainProxyPods.forEach(pod -> {
                String nodeName = pod.getNodeName();
                servers.add(new Server(allMainProxy.get(nodeName + NetworkConstants.SUFFIX_DOMAIN), allMainProxy.get(nodeName + ProxyConstants.NGINX_PORT), allMainProxy.get(nodeName + ProxyConstants.NGINX_SSL_PORT)));
            });

            Map<String, String> allHaProxy = k8sService.getConfigmap(Constants.CONFIGMAP_ALL_HAPROXY);
            List<Pod> haProxyPods = k8sService.getPodListWithLabelInApp(Constants.POD_NAME_HAPROXY);
            haProxyPods.forEach(pod -> {
                String nodeName = pod.getNodeName();
                servers.add(new Server(allHaProxy.get(nodeName + NetworkConstants.SUFFIX_DOMAIN), allHaProxy.get(nodeName + "-MAIN-HTTP-PORT"), allHaProxy.get(nodeName + "-MAIN-HTTPS-PORT")));
            });

            Set<Server> finalServers = servers.stream().filter(Server::isLegal).collect(Collectors.toSet());

            Map<String, Set<Map<String, Map<String, Set<Server>>>>> root = new HashMap<>();
            Map<String, Map<String, Set<Server>>> setServers = new HashMap<>();
            Map<String, Set<Server>> serversMap = new HashMap<>();

            if (finalServers.isEmpty()) {
                return "{}";
            } else {
                serversMap.put("servers", finalServers);
            }
            setServers.put("setServers", serversMap);
            root.put("plats", Collections.singleton(setServers));
            return JsonMapper.nonEmptyMapper().toJson(root);
        } catch (Exception e) {
            log.error("Build default server domain error.", e);
            return "{}";
        }
    }

    @Data
    private static class Server {
        private String host;
        private String httpPort;
        private String httpsPort;

        public Server() {
        }

        public Server(String host, String httpPort, String httpsPort) {
            this.host = host;
            this.httpPort = httpPort;
            this.httpsPort = httpsPort;
        }

        @JsonIgnore
        public boolean isLegal() {
            return Objects.nonNull(host) && Objects.nonNull(httpPort) && Objects.nonNull(httpsPort);
        }

        @Override
        public boolean equals(Object object) {
            if (this == object) {
                return true;
            }
            if (object == null || getClass() != object.getClass()) {
                return false;
            }
            Server server = (Server) object;
            return Objects.equals(host, server.host) && Objects.equals(httpPort, server.httpPort) && Objects.equals(httpsPort, server.httpsPort);
        }

        @Override
        public int hashCode() {
            return Objects.hash(host, httpPort, httpsPort);
        }
    }
}
