package com.xylink.manager.service.appdownload;

import com.xylink.manager.controller.dto.appdownload.AppDownloadConfig;
import com.xylink.manager.controller.dto.appdownload.AppDownloadInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-11-09 20:33
 */
public interface AppDownloadService {

    AppDownloadConfig queryConfig(String customizedKey);

    void updateConfig(AppDownloadConfig appDownloadConfig);

    List<AppDownloadInfo> updateServerAddressPopAppList(String customizedKey);

    List<AppDownloadInfo> hideDownloadEntryAppList(String customizedKey);

    void refreshDefaultUpdateServerAddress();

}
