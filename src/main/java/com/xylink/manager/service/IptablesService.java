package com.xylink.manager.service;

import com.google.gson.Gson;
import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.constant.IptablesConstants;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.controller.dto.ClusterIpDto;
import com.xylink.manager.controller.dto.IptablesDto;
import com.xylink.manager.model.IptablesInfo;
import com.xylink.manager.model.IptablesPage;
import com.xylink.manager.model.common.Page;
import com.xylink.manager.model.em.IptablesStatusEnum;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.cache.service.ICacheService;
import com.xylink.manager.service.schedule.IptablesStatusTask;
import com.xylink.util.IptablesUtils;
import com.xylink.util.Ipv6Util;
import com.xylink.util.MemoryPaginationUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class IptablesService {

    @Autowired
    private K8sService k8sService;

    @Autowired
    private ICacheService cacheService;

    @Autowired
    private IptablesStatusTask iptablesStatusTask;

    @Autowired
    private IptablesUtils iptablesUtils;

    private static final Logger logger = LoggerFactory.getLogger(IptablesService.class);

    /**
     * 刷新所有node节点的防火墙配置（如果node节点已开启防火墙配置，则重载配置）
     */
    public void refreshIptables4Node() {
        List<String> nodeIps = k8sService.getAllNodeIP();
        if (CollectionUtils.isEmpty(nodeIps)) {
            return;
        }
        // 请求webhook获取node节点上防火墙状态
        nodeIps.parallelStream().forEach(nodeIp -> {
            boolean isRunning = isRunningIptablesStatus(nodeIp);
            if (isRunning) {
                String url = "http://" + Ipv6Util.handlerIpv6Addr(nodeIp) + ":" + NetworkConstants.WEB_HOOK + "/hooks/iptablesStart";
                iptablesUtils.updateIptablesStatusByPostWebHook(url);
            }
        });
    }

    /**
     * 新增node节点时，更新iptables配置文件，并刷新所有node节点的防火墙配置
     */
    public void updateAndRefreshIptables4Node(String ip) {
        Map<String, String> map = k8sService.getConfigmap(Constants.CONFIGMAP_PRIVATE_DATA);
        String isRefreshIptableStr = map.get("isRefreshIptable");
        if (!Boolean.parseBoolean(isRefreshIptableStr)) {
            return;
        }
        logger.info("updateAndRefreshIptables4Node :{}", ip);
        if (StringUtils.isBlank(ip)) {
            return;
        }
        updateIptables4Node(ip);
        refreshIptables4Node();
    }

    public IptablesInfo getNodeIptablesStatus(String nodeIp) {
        // 请求webhook获取node节点上防火墙状态
        String iptablesInfo = "";
        try {
            Map<String, String> iptablesCmData = cacheService.cacheConfigMapByName(Constants.CONFIGMAP_IPTABLES).getData();
            iptablesInfo = iptablesCmData.get(IptablesConstants.IPTABLES_INFO);
        } catch (Exception e) {
            logger.error("k8sCacheService getConfigMapCache error!");
        }
        Gson gson = new Gson();
        String status = null;
        if (StringUtils.isNotBlank(iptablesInfo)) {
            List<Map<String, String>> iptablesList = gson.fromJson(iptablesInfo, List.class);
            //通过完整nodeIp，至多一个结果
            List<Map<String, String>> iptablesByKey = filterIptablesListByKey(iptablesList, nodeIp);
            if (iptablesByKey != null && iptablesByKey.size() > 0) {
                status = iptablesByKey.get(0).get(IptablesConstants.NODE_STATUS);
            }
        } else {
            status = iptablesUtils.getIptablesStatusByGetWebHookResult(nodeIp);
        }
        return new IptablesInfo(status);
    }

    public void startNodeIptables(String nodeIp) {
        Set<String> ips = new HashSet<>();
        ips.add(nodeIp);
        IptablesDto iptablesDto = new IptablesDto(true, ips);
        updateIptablesStatus(iptablesDto);
    }

    public void stopNodeIptables(String nodeIp) {
        Set<String> ips = new HashSet<>();
        ips.add(nodeIp);
        IptablesDto iptablesDto = new IptablesDto(false, ips);
        updateIptablesStatus(iptablesDto);
    }

    private void updateIptables4Node(String ip) {
        Map<String, String> config = k8sService.getConfigmap(Constants.CONFIGMAP_IPTABLES);
        String ips = config.get("ips.txt");
        if (StringUtils.isBlank(ips)) {
            List<String> allNodeIP = k8sService.getAllNodeIP();
            if (!allNodeIP.contains(ip)) {
                allNodeIP.add(ip);
            }
            ips = StringUtils.join(allNodeIP, '\n');
        } else {
            if (ips.contains(ip)) {
                return;
            }
            if (ips.endsWith("\n")) {
                ips = ips + ip + "\n";
            } else {
                ips = ips + "\n" + ip + "\n";
            }
        }
        config.put("ips.txt", ips);
        k8sService.editConfigmap(Constants.CONFIGMAP_IPTABLES, config);
    }

    public void editCmIptables(ClusterIpDto clusterIpDto) {
        Map<String, String> iptables = k8sService.getConfigmap(Constants.CONFIGMAP_IPTABLES);
        if (Objects.isNull(iptables)) {
            throw new ServerException("获取iptables cm 资源失败！");
        }
        String customClusterIps = clusterIpDto.getCustomClusterIps();
        String innerClusterIps = clusterIpDto.getInnerClusterIps();
        innerClusterIps = innerClusterIps == null ? "" : innerClusterIps.trim();
        customClusterIps = customClusterIps == null ? "" : customClusterIps.trim();
        String ips = (innerClusterIps+ "\n" + customClusterIps).trim();
        iptables.put(IptablesConstants.INNER_CLUSTER_IPS, innerClusterIps);
        iptables.put(IptablesConstants.CUSTOM_CLUSTER_IPS, customClusterIps);
        iptables.put("ips.txt", ips);
        k8sService.editConfigmap(Constants.CONFIGMAP_IPTABLES, iptables);
    }

    public IptablesPage<Map<String, String>> getIptablesStatusList(String key, String status, Long current, Long pageSize) {
        // 如果cm资源中存在则直接返回，否则通过调用webhook接口获取状态
        List<Map<String, String>> iptablesList = getIptablesList();
        IptablesPage<Map<String, String>> iptablesPage = new IptablesPage<>();
        //设置服务器总数
        iptablesPage.setTotalServers(iptablesList.size());
        // 统计已启用总数
        int totalEnabled = getTotalEnabled(iptablesList);
        iptablesPage.setTotalEnabled(totalEnabled);

        //通过key进行过滤
        if (StringUtils.isNotBlank(key)) {
            iptablesList = filterIptablesListByKey(iptablesList, key);
        }
        if (StringUtils.isNotBlank(status)) {
            iptablesList = filterIptablesListByStatus(iptablesList, status);
        }
        Page<Map<String, String>> pagination = MemoryPaginationUtil.pagination(iptablesList, current, pageSize);
        BeanUtils.copyProperties(pagination, iptablesPage);
        return iptablesPage;
    }

    public void updateIptablesStatus(IptablesDto iptablesDto) {
        Enum<IptablesStatusEnum> status = iptablesDto.getUpdateStatus() ? IptablesStatusEnum.opening : IptablesStatusEnum.closing;
        // 先更新cm资源缓存数据
        Map<String, String> iptablesCmData = k8sService.getConfigmap(Constants.CONFIGMAP_IPTABLES);
//        Map<String, String> iptablesCmData = cacheService.getConfigMapCache(Constants.CONFIGMAP_IPTABLES).getData();
        String iptablesInfo = iptablesCmData.get(IptablesConstants.IPTABLES_INFO);
        Gson gson = new Gson();
        if (StringUtils.isNotBlank(iptablesInfo)) {
            List<Map<String, String>> iptablesList = gson.fromJson(iptablesInfo, List.class);
            iptablesList = cleanIptablesList(iptablesList);
            iptablesList.parallelStream()
                    .filter(iptablesVo -> iptablesDto.getNodeIps().contains(iptablesVo.get(IptablesConstants.NODE_IP)))
                    .forEach(iptablesVo -> iptablesVo.put(IptablesConstants.NODE_STATUS, status.name()));
            iptablesCmData.put(IptablesConstants.IPTABLES_INFO, gson.toJson(iptablesList));
            k8sService.editConfigmap(Constants.CONFIGMAP_IPTABLES, iptablesCmData);
        }
        // 异步更新
        iptablesStatusTask.updateIptablesStatus(iptablesDto);
    }

    private boolean isRunningIptablesStatus(String nodeIp) {
        // 请求webhook获取node节点上防火墙状态
        String status = iptablesUtils.getIptablesStatusByGetWebHookResult(nodeIp);
        if (IptablesStatusEnum.abnormal.name().equalsIgnoreCase(status)) {
            logger.error("防火墙状态异常，nodeIp =" + nodeIp);
        }
        return IptablesStatusEnum.enabled.name().equalsIgnoreCase(status);
    }

    /**
     * 统计已启用总数
     */
    private int getTotalEnabled(List<Map<String, String>> iptablesList) {
        return (int) iptablesList.stream()
                .filter(iptablesVo -> iptablesVo.get(IptablesConstants.NODE_STATUS).equals(IptablesStatusEnum.enabled.name()))
                .count();
    }

    private List<Map<String, String>> filterIptablesListByKey(List<Map<String, String>> iptablesList, String key) {
        return iptablesList.stream()
                .filter(iptablesVo -> iptablesVo.get(IptablesConstants.NODE_STATUS).contains(key)
                        || iptablesVo.get(IptablesConstants.NODE_IP).contains(key)
                        || iptablesVo.get(IptablesConstants.NODE_NAME).contains(key))
                .collect(Collectors.toList());
    }

    private List<Map<String, String>> filterIptablesListByStatus(List<Map<String, String>> iptablesList, String status) {
        return iptablesList.stream()
                .filter(iptablesVo -> iptablesVo.get(IptablesConstants.NODE_STATUS).equals(status))
                .collect(Collectors.toList());
    }

    private List<Map<String, String>> getIptablesList() {
        Map<String, String> iptablesCmData = k8sService.getConfigmap(Constants.CONFIGMAP_IPTABLES);
//        Map<String, String> iptablesCmData = cacheService.getConfigMapCache(Constants.CONFIGMAP_IPTABLES).getData();
        String iptablesInfo = iptablesCmData.get(IptablesConstants.IPTABLES_INFO);
        List<Map<String, String>> iptablesList;
        Gson gson = new Gson();
        if (StringUtils.isNotBlank(iptablesInfo)) {
            iptablesList = cleanIptablesList(gson.fromJson(iptablesInfo, List.class));
        } else {
            iptablesList = iptablesUtils.saveIptablesInfoInCm(iptablesCmData);
        }
        return iptablesList;
    }

    public List<Map<String, String>> cleanIptablesList(List<Map<String, String>> iptablesList) {
        List<Map<String, String>> cleanedList = new ArrayList<>(iptablesList);
        Iterator<Map<String, String>> iterator = cleanedList.iterator();

        while (iterator.hasNext()) {
            Map<String, String> map = iterator.next();
            if (map == null) {
                iterator.remove(); // 删除 null 对象
            }
        }
        return cleanedList;
    }
}
