package com.xylink.manager.service;

import com.alibaba.excel.EasyExcelFactory;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import com.xylink.config.K8sSvcConstants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.exception.basic.*;
import com.xylink.config.util.JsonUtil;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.controller.dto.TerminalConfigDto;
import com.xylink.manager.controller.dto.TerminalDto;
import com.xylink.manager.iptables.util.AesUtil;
import com.xylink.manager.model.ClientAccess;
import com.xylink.manager.model.RechargeRequestDto;
import com.xylink.manager.model.deploy.Node;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.model.excel.HeadFor4kExport;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.base.K8sSvcService;
import com.xylink.manager.service.dto.license.AccessTerminalTypeLimitDto;
import com.xylink.manager.service.dto.license.LicenseInfoDto;
import com.xylink.manager.service.dto.license.Module;
import com.xylink.manager.service.dto.license.Tag;
import com.xylink.manager.service.factory.RechargeRequestDtoFactory;
import com.xylink.manager.service.proper.ProperCloudLicenseSupport;
import com.xylink.util.Ipv6Util;
import com.xylink.util.JDBCUtils;
import com.xylink.util.LicenseUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Created by suikelei on 7/12/17.
 */
@Service
public class LicenseInfoService {
    private final static Logger logger = LoggerFactory.getLogger(LicenseInfoService.class);

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private ServerListService serverListService;
    @Autowired
    private JDBCUtils jdbcUtils;
    @Autowired
    private K8sService k8sService;
    @Autowired
    private K8sSvcService k8sSvcService;
    @Autowired
    private ConfigService configService;
    @Autowired
    private IDeployService deployService;

    private static List<String> defaultSeriesWhitelist = Lists.newArrayList("XE","AE", "TP", "GE", "NP");
    private static List<String> allSeries = Lists.newArrayList("XE", "AE", "TP", "GE", "NP", "ES", "ME", "NE");

    private static final String SDK_FUNCTION_UPDATE_URL = "/api/rest/internal/v1/sdk/enterpriseToken/status/change?enterpriseId=default_enterprise&enable=";

    private static final String CHARGE_FP = "CHARGE_FP";

    public String importLicense(String licenseInfo, String chargeIp) {
        Map<String, String> param = new HashMap<>();
        licenseInfo = licenseInfo.trim();
        param.put("license", licenseInfo);
        String svcIp = k8sSvcService.getServiceIpByDefaultNs(K8sSvcConstants.CHARGE_NAME);
        if (StringUtils.isNotBlank(svcIp)) {
            chargeIp = svcIp;
        }
        String ip = StringUtils.isBlank(chargeIp) ? Ipv6Util.handlerIpv6Addr(serverListService.getMainNodeInternalIP()) + ":" + NetworkConstants.INTERNAL_PORT : Ipv6Util.handlerIpv6Addr(chargeIp) + ":" + NetworkConstants.CHARGE_PORT;
        String licenseInfoUrl = "http://" + ip + "/api/rest/internal/v1/charge/license";
        JsonNode object = restTemplate.postForObject(licenseInfoUrl, param, JsonNode.class);
        logger.info("import license response:{}", object);
        Boolean success = object.get("success").asBoolean();
        if (!success) {
            throw new WebException(ErrorStatus.AUTHENTICATION_ERROR);
        }

        LicenseInfoDto license = JsonUtil.parseJson(getLicenseInfo(chargeIp), LicenseInfoDto.class);
        if (ObjectUtils.isEmpty(license)) {
            throw new ServiceErrorException(ErrorStatus.LICENSE_INFO_SET_FAILED);
        }
        // 专有云导入云团ID和分区云区号
        String cloudClusterId = license.getCloudClusterId();
        String areaCode = license.getAreaCode();
        if (StringUtils.isNotBlank(cloudClusterId) && StringUtils.isNotBlank(areaCode)) {
            ProperCloudLicenseSupport licenseSupport = new ProperCloudLicenseSupport(jdbcUtils, k8sService);
            licenseSupport.support(licenseInfo, cloudClusterId, areaCode);
        }
        setTerminalAccessLimit(license);
        setTranscription(license);
        setSdkFunction(license);
        return "success";
    }

    public void setSdkFunction(LicenseInfoDto license) {
        boolean sdkFunction = false;
        if (ObjectUtils.isNotEmpty(license.getModules())) {
            long sdkFunctionNum = Arrays.stream(license.getModules()).filter(x -> "SDK_FUNC".equals(x.getModuleName())).map(Module::getMaxValue).findFirst().orElse(0L);
            sdkFunction = sdkFunctionNum > 0;
        }
        Optional<Pod> externalwebPod = k8sService.getPodWithLabelInApp("private-externalweb");
        //externalweb未部署且需要打开externalweb开关
        if (sdkFunction && !externalwebPod.isPresent()) {
            throw new ServiceErrorException(ErrorStatus.EXTERNALWEB_NOT_DEPLOY_LICENSE_FAILED);
        }
        String internalNginxUrl = serverListService.getInternalNginxUrl();
        String sdkFunctionUrl = internalNginxUrl + SDK_FUNCTION_UPDATE_URL + sdkFunction;
        try {
            restTemplate.postForObject(sdkFunctionUrl, null, Void.class);
            logger.info("update sdkFunction:{} successfully,", sdkFunction);
        } catch (Exception e) {
            logger.error("update sdkFunction:{} failed,e", sdkFunction, e);
            if (sdkFunction) {
                throw new ServiceErrorException(ErrorStatus.EXTERNALWEB_ERROR_LICENSE_FAILED);
            }
        }
    }

    private void setTranscription(LicenseInfoDto licenseInfoDto) {
        Module[] modules = licenseInfoDto.getModules();
        Module transcription = Arrays.stream(modules).filter(x -> "TRANSCRIPTION".equals(x.getModuleName())).findFirst().orElse(null);
        if (ObjectUtils.isEmpty(transcription) || transcription.getMaxValue() <= 0) {
            //关闭开关
            configService.saveBuffetConfig("云会议室会议纪要", "CLOUDMEETING_CONFIG_TRANSCRIPTION", "false", "false");
        } else {
            //打开开关
            configService.saveBuffetConfig("云会议室会议纪要", "CLOUDMEETING_CONFIG_TRANSCRIPTION", "true", "true");
        }
    }

    public void setTerminalAccessLimit(LicenseInfoDto license) {
        logger.info("start set terminal access limit");

        Tag[] tags = license.getTags();
        if (ObjectUtils.isEmpty(tags)) {
            throw new ServiceErrorException(ErrorStatus.TERMINAL_ACCESS_LIMIT_FAILED);
        }
        String series = "";
        String type = "";
        for (Tag tag : tags) {
            if ("SERIES_WHITE_LIST".equals(tag.getTagName())) {
                series = tag.getHexValues();
            }
            if ("DEVICE_WHITE_LIST".equals(tag.getTagName())) {
                type = tag.getHexValues();
            }
        }

        if (StringUtils.isBlank(series)) {
            throw new ServiceErrorException(ErrorStatus.TERMINAL_ACCESS_LIMIT_FAILED);
        }

        // 替换为 Java 原生的十六进制解码逻辑
        series = new String(javax.xml.bind.DatatypeConverter.parseHexBinary(series));

        List<String> seriesWhitelist = new ArrayList<>(Arrays.asList(series.split(",")));
        AccessTerminalTypeLimitDto accessTerminalTypeLimitDto = null;

        if (StringUtils.isNotBlank(type)) {
            // 先用 Java 自带的 DatatypeConverter 将 hex string 转换为 byte[]
            byte[] decodedBytes = javax.xml.bind.DatatypeConverter.parseHexBinary(type);
            List<Integer> terminalTypeWhitelist = LicenseUtil.decodeOfTerminalTypeByte(decodedBytes);

            accessTerminalTypeLimitDto = getTerminalTypeAccessBlackList(terminalTypeWhitelist, seriesWhitelist);
        }
        logger.info("series whitelist:{},terminal type limit:{}", seriesWhitelist, accessTerminalTypeLimitDto);
        jdbcUtils.setTerminalAccessLimit(seriesWhitelist, accessTerminalTypeLimitDto);
    }

    /**
     * 根据终端限制白名单，在各个白名单型号所属系列下，查询 除白名单终端型号外 的所有黑名单
     *
     * @param deviceWhiteList 系列白名单
     * @return
     */
    public AccessTerminalTypeLimitDto getTerminalTypeAccessBlackList(List<Integer> deviceWhiteList, List<String> terminalTypeWhitelist) {
        if (CollectionUtils.isEmpty(deviceWhiteList)) {
            return null;
        }

        logger.info("terminal type whitelist:{}", deviceWhiteList);
        //获取系列
        List<ClientAccess> clientAccesses = jdbcUtils.clientAccessConfig();
        if (CollectionUtils.isEmpty(clientAccesses)) {
            logger.error("search terminal info is null, don`t set terminal type blacklist");
            throw new ServiceErrorException(ErrorStatus.TERMINAL_ACCESS_LIMIT_FAILED);
        }
        Set<String> seriesSet = new HashSet<>(terminalTypeWhitelist);

        //seriesSet.addAll(defaultSeriesWhitelist);
        seriesSet.addAll(allSeries);
        //获取终端型号白名单所属系列 & 默认终端系列白名单 下的所有型号 映射关系
        Map<String, Set<Integer>> seriesToSubtypeList = new HashMap<>();
        //预加载
        seriesSet.forEach(x -> seriesToSubtypeList.put(x, new HashSet<>()));

        for (ClientAccess temp : clientAccesses) {
            String name = temp.getName();
            //防止出现 name为NEMO,实际不是NE系列情况
            if (StringUtils.isBlank(name) || name.length() < 3 || !StringUtils.isNumeric(name.charAt(2) + "")) {
                continue;
            }
            String series = name.substring(0, 2);
            Set<Integer> subtypeList = seriesToSubtypeList.get(series);
            //如果没有该系列则继续下一个,因为之前已经预加载了,只能用null判断!
            if (subtypeList == null) {
                continue;
            }
            subtypeList.add(Integer.parseInt(temp.getSubtype()));
            seriesToSubtypeList.put(series, subtypeList);
        }

        List<Integer> nonDeletedSubtype = new ArrayList<>();
        //所选终端类型白名单 所属系列下 的所有终端类型
        List<Integer> allSubtypeOfSubtypeWhitelistSeries = new ArrayList<>();

        for (Map.Entry<String, Set<Integer>> entry : seriesToSubtypeList.entrySet()) {
            if (defaultSeriesWhitelist.contains(entry.getKey())) {
                //之前的客户限制单个终端的记录，不能针对之前的默认接入终端删除、修改
                nonDeletedSubtype.addAll(entry.getValue());
            } else {
                allSubtypeOfSubtypeWhitelistSeries.addAll(entry.getValue());
            }
        }

        List<Integer> subtypeBlacklist = allSubtypeOfSubtypeWhitelistSeries.stream().filter(x -> !deviceWhiteList.contains(x)).collect(Collectors.toList());

        if (subtypeBlacklist.isEmpty()) {
            logger.info("terminal subtype blacklist is null.");
            jdbcUtils.clearTerminalBlackList(nonDeletedSubtype);
            return null;
        }
        AccessTerminalTypeLimitDto accessTerminalTypeLimitDto = new AccessTerminalTypeLimitDto();
        accessTerminalTypeLimitDto.setNonDeletedSubtypeList(nonDeletedSubtype);
        accessTerminalTypeLimitDto.setNeedUpdatedSubtypeBlacklist(subtypeBlacklist);

        return accessTerminalTypeLimitDto;
    }

    public String getFingerPrint(String chargeIp) {
        Map<String, String> configMap = k8sService.getConfigmapOrCreate("private-manager-data");
        if (configMap.containsKey(CHARGE_FP + "_" + chargeIp)) {
            return AesUtil.decryptCBC(configMap.get(CHARGE_FP + "_" + chargeIp));
        }
        String fp = getChargeFp(chargeIp);
        configMap.put(CHARGE_FP + "_" + chargeIp, AesUtil.encryptCBC(fp));
        k8sService.editConfigmap("private-manager-data", configMap);
        return fp;
    }

    private String getChargeFp(String chargeIp) {
        String svcIp = k8sSvcService.getServiceIpByDefaultNs(K8sSvcConstants.CHARGE_NAME);
        if (StringUtils.isNotBlank(svcIp)) {
            chargeIp = svcIp;
        }
        String ip = StringUtils.isBlank(chargeIp) ? Ipv6Util.handlerIpv6Addr(serverListService.getMainNodeInternalIP()) + ":" + NetworkConstants.INTERNAL_PORT : Ipv6Util.handlerIpv6Addr(chargeIp) + ":" + NetworkConstants.CHARGE_PORT;
        String licenseFpUrl = "http://" + ip + "/api/rest/internal/v1/charge/license/fp";
        return restTemplate.getForObject(licenseFpUrl, String.class);
    }

    public String getLicenseInfo(String chargeIp) {
        String svcIp = k8sSvcService.getServiceIpByDefaultNs(K8sSvcConstants.CHARGE_NAME);
        if (StringUtils.isNotBlank(svcIp)) {
            chargeIp = svcIp;
        }
        String ip = StringUtils.isBlank(chargeIp) ? Ipv6Util.handlerIpv6Addr(serverListService.getMainNodeInternalIP()) + ":" + NetworkConstants.INTERNAL_PORT : Ipv6Util.handlerIpv6Addr(chargeIp) + ":" + NetworkConstants.CHARGE_PORT;
        String licenseInfoUrl = "http://" + ip + "/api/rest/internal/v1/charge/license";
        return restTemplate.getForObject(licenseInfoUrl, String.class);
    }

    public String getLicenseVersion(String chargeIp) {
        String svcIp = k8sSvcService.getServiceIpByDefaultNs(K8sSvcConstants.CHARGE_NAME);
        if (StringUtils.isNotBlank(svcIp)) {
            chargeIp = svcIp;
        }
        String ip = StringUtils.isBlank(chargeIp) ? serverListService.getMainNodeInternalIP() + ":" + NetworkConstants.INTERNAL_PORT : chargeIp + ":" + NetworkConstants.CHARGE_PORT;
        String licenseVersionUrl = "http://" + ip + "/api/rest/internal/v1/charge/license/version";
        return restTemplate.getForObject(licenseVersionUrl, String.class);
    }

    /**
     * 添加终端4K画面能力
     */
    public void addUHD4(String deviceSn, String chargeIp) {
        JsonNode license = JsonUtil.parseJson(getLicenseInfo(chargeIp));
        int maxUhd4Num = 0;
        ArrayNode array = (ArrayNode)license.get("modules");
        for (Iterator<JsonNode> it = array.iterator(); it.hasNext(); ) {
            ObjectNode objectNode = (ObjectNode) it.next();
            if ("UHD".equals(objectNode.get("moduleName").asText())) {
                maxUhd4Num = Integer.parseInt(objectNode.get("maxValue").toString());
            }
        }
        String[] deviceSnAry = deviceSn.split(";");
        String num = jdbcUtils.getUHD4Number();
        if (Integer.parseInt(num) + deviceSnAry.length > maxUhd4Num) {
            throw new WebException("4K终端数量超过license限制!");
        }

        if (license.get("expiredTime").asLong() > 0) {
            long expiredTime = license.get("expiredTime").asLong() * 1000;
            // 1、调用bill接口充值
            rechargeBill(deviceSnAry, expiredTime);
            // 2、终端是否有4k能力/添加4k终端
            addUHD4ByBatch(deviceSnAry);
        }

    }

    public void export(HttpServletResponse response) {
        ServletOutputStream outputStream = null;
        try {
            //设置content—type
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset:utf-8");

            //设置标题
            String fileName = URLEncoder.encode("4k终端列表_" + System.currentTimeMillis(), "UTF-8");
            //Content-disposition是MIME协议的扩展，MIME协议指示MIME用户代理如何显示附加的文件。
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");

            List<Map<String, Object>> resultList = jdbcUtils.getUHD4List();
            if (CollectionUtils.isEmpty(resultList)) {
                resultList = new ArrayList<>(); // 确保即使没有数据也能导出表头
            }

            List<List<Object>> dataList = resultList.stream()
                    .map(map -> Arrays.asList(map.get("deviceSn"),map.get("deviceCategory"),
                            map.get("displayName"), map.get("createTime")))
                    .collect(Collectors.toList());
            outputStream = response.getOutputStream();
            EasyExcelFactory.write(outputStream)
                    .head(HeadFor4kExport.class)
                    .sheet("4k终端列表")
                    .doWrite(dataList);
        } catch (Exception e) {
            logger.error("api permission report export failed", e);
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    logger.error("", e);
                }
            }
        }
    }

    public void rechargeBill(String[] deviceSnAry, long expiredTime) {
        List<RechargeRequestDto> request = Arrays.stream(deviceSnAry).map(item -> RechargeRequestDtoFactory.create4KRecharge(item, expiredTime)).collect(Collectors.toList());
        String url = "http://" + Ipv6Util.handlerIpv6Addr(serverListService.getMainNodeInternalIP()) + ":" + serverListService.getMainNodeInternalPort() + "/api/rest/internal/v1/bill/device/access-permissions";
        logger.info("recharge 4k request:{}", request);
        ResponseEntity<String> data = restTemplate.postForEntity(url, request, String.class);
        logger.info("recharge 4k response:{}", data.getBody());
    }

    public Map<String, Long> getDeviceId(String[] deviceSnAry) {
        String url = "http://" + Ipv6Util.handlerIpv6Addr(serverListService.getMainNodeInternalIP()) + ":" + serverListService.getMainNodeInternalPort() + "/api/rest/internal/v1/en/userDevice";
        ConcurrentHashMap<String, Long> res = new ConcurrentHashMap<String, Long>();
        Arrays.stream(deviceSnAry).parallel().forEach(sn -> {
            TerminalDto terminalDto;
            try {
                terminalDto = restTemplate.getForObject(url + "?sn=" + sn, TerminalDto.class);
            } catch (Exception e) {
                if (e instanceof HttpClientErrorException) {
                    HttpClientErrorException httpClientErrorException = (HttpClientErrorException) e;
                    String responseError = httpClientErrorException.getResponseBodyAsString();
                    ThirdServiceResponseError error = ThirdServiceResponseError.build(responseError);
                    if (error != null && error.getErrorCode() == 8001) {
                        logger.error("终端序列号:{} 无对应deviceId", sn);
                        throw new ClientErrorException(ErrorStatus.NO_DEVICE_ID);
                    }
                }
                throw new ServerException(ErrorStatus.THIRD_INTERFACE_SN_FAILED);
            }
            if (terminalDto != null) {
                res.put(sn, terminalDto.getId());
            } else {
                logger.error("终端序列号:{} 无对应deviceId", sn);
                throw new ClientErrorException(ErrorStatus.NO_DEVICE_ID);
            }

        });
        return res;
    }

    public void addUHD4ByBatch(String[] deviceSnAry) {
        TerminalConfigDto[] terminalConfigDtos = new TerminalConfigDto[]{new TerminalConfigDto("show4kResolution", "true", "UIDisplayCustomization")
                , new TerminalConfigDto("enable4kResolution", "true", "common")};
        Map<String, Long> deviceSnToIds = getDeviceId(deviceSnAry);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<TerminalConfigDto[]> httpEntity = new HttpEntity<>(terminalConfigDtos, httpHeaders);
        deviceSnToIds.entrySet().parallelStream().forEach(entry -> {
            try {
                String url = "http://" + Ipv6Util.handlerIpv6Addr(serverListService.getMainNodeInternalIP()) + ":" + serverListService.getMainNodeInternalPort() + "/api/rest/internal/v1/device/" + entry.getValue().toString() + "/configs";
                restTemplate.exchange(url, HttpMethod.PUT, httpEntity, Void.class);
            } catch (Exception e) {
                String[] messages = e.getMessage().split(",");
                for (String message : messages) {
                    if (message.contains("errorCode")) {
                        String[] split = StringUtils.split(message, ":");
                        String errorCode = split[split.length - 1];
                        if ("4114".equalsIgnoreCase(errorCode)) {
                            logger.error("该终端没有4K能力,无法添加. device id:{},device SN:{}", entry.getValue(), entry.getKey());
                            throw new ClientErrorException(ErrorStatus.DEVICE_NOT_4K);
                        }
                    }
                }
                logger.error("fail add UHD4,", e);
                throw new ClientErrorException(ErrorStatus.THIRD_INTERFACE_ADD_UH4D_FAILED);
            }
        });
    }

    /**
     * 获取部署charge 每个charge对应一个license
     *
     * @return
     */
    public List<NodeDto> getCount() {
        List<NodeDto> nodeDtos = new ArrayList<>();
        List<Node> nodeList = deployService.listNodesByAppLabel(Labels.charge.label());
        for (Node node : nodeList) {
            NodeDto nodeDto = new NodeDto();
            String ip = node.getIp();
            nodeDto.setInternalIp(ip);
            nodeDto.setName(node.getName());
            nodeDtos.add(nodeDto);
        }
        return nodeDtos;
    }

    @Async
    public void syncLicenseInfo() {
        List<NodeDto> nodeDtos = getCount();
        Map<String, String> configMap = k8sService.getConfigmapOrCreate("private-manager-data");
        k8sService.editConfigmap("private-manager-data", configMap);
        for (NodeDto nodeDto : nodeDtos) {
            String chargeIp = nodeDto.getInternalIp();
            String fp = getChargeFp(chargeIp);
            configMap.put(CHARGE_FP + "_" + chargeIp, AesUtil.encryptCBC(fp));
        }
        k8sService.editConfigmap("private-manager-data", configMap);
    }
}
