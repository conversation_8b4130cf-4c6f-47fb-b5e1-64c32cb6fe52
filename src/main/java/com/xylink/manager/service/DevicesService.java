package com.xylink.manager.service;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.controller.Middleware;
import com.xylink.manager.model.Devices;
import com.xylink.manager.service.base.K8sService;
import com.xylink.util.JDBCUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class DevicesService {


    @Autowired
    private JDBCUtils jdbcUtils;

    @Autowired
    private K8sService k8sService;

    public Devices queryDevices(String type, String query) throws Exception{
        Devices devices = jdbcUtils.queryDevices(type, query);

        Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        String domain = "https://" + allIp.get(NetworkConstants.MAIN_DOMAIN_NAME) +
                ("443".equals(allIp.get(NetworkConstants.MAIN_NGINX_SSL_PORT)) ? "" : (":" + allIp.get(NetworkConstants.MAIN_NGINX_SSL_PORT)));

        List<Middleware> urls = new ArrayList<>();
        urls.add(new Middleware("终端卡片配置", domain + "/api/rest/v3/nemo/" + devices.getId() + "/customizedfeatures?securityKey=" + devices.getSk(), null));

        String model = null;
        if (StringUtils.isNotBlank(devices.getSubType()) && StringUtils.isNotBlank(devices.getModel())) {
            model = jdbcUtils.queryDevicesModel(devices.getSubType(), devices.getModel());
        }
        urls.add(new Middleware("终端屏保配置", domain + "/api/rest/v3/en/device/screensaver?sn=" + devices.getSn() + "&model=" + model, null));
//        urls.add(new Middleware("终端信息", domain + "/api/rest/internal/v1/en/nemos?securityKey=" + devices.getSk(), null));
        urls.add(new Middleware("终端功能配置", domain + "/api/rest/v3/nemo/" + devices.getId() + "/nemoconfig?securityKey=" + devices.getSk()+"&hardVersion=&softVersion=&os=&model=", null));
        devices.setUrls(urls);

        return devices;
    }

    public List<Devices> queryHotStandbyDevices() {
        return jdbcUtils.queryHotStandbyDevices();
    }
}

