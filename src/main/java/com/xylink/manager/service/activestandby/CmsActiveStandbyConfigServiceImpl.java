package com.xylink.manager.service.activestandby;

import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.google.common.base.Predicates;
import com.xylink.config.Constants;
import com.xylink.config.constant.CmsActiveStandbyConstants;
import com.xylink.config.constant.ScoutsRoleEnum;
import com.xylink.config.constant.ScoutsRunModeEnum;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.controller.dto.DeploymentServerDto;
import com.xylink.manager.controller.dto.SystemInfo;
import com.xylink.manager.controller.dto.activestandby.ActiveStandbyBaseInfoDto;
import com.xylink.manager.controller.dto.activestandby.ActiveStandbyPreCheckResponseDto;
import com.xylink.manager.controller.dto.activestandby.ActiveStandbyStateInfoDto;
import com.xylink.manager.controller.dto.activestandby.PeerContactAddOrUpdateDto;
import com.xylink.manager.controller.dto.alert.AlertApiReq;
import com.xylink.manager.iptables.enums.NodeType;
import com.xylink.manager.iptables.service.IptablesService;
import com.xylink.manager.model.em.SystemMode;
import com.xylink.manager.service.ServerStatusService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.cache.bean.NodeCache;
import com.xylink.manager.service.remote.scouts.ScoutsRemoteClient;
import com.xylink.manager.service.remote.scouts.dto.ScoutsResultResponse;
import com.xylink.manager.validate.AlertApiReqValidate;
import com.xylink.util.CommandUtils;
import com.xylink.util.IpUtils;
import com.xylink.util.activestandby.ScoutsConfigYamlUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2025-03-11 15:11
 */
@Slf4j
@Service
public class CmsActiveStandbyConfigServiceImpl implements ICmsActiveStandbyConfigService {

    private static final int DEFAULT_RETRY_COUNT = 150;

    private static final String DEFAULT_SCOUTS_CONFIG_PATH = "/mnt/xylink/scouts/config/settings.yaml";
    private static final String DEFAULT_CMS_HA_VARIABLES_PATH = "/mnt/xylink/cmsHaVariables";
    private static final String DEFAULT_CMS_HA_REFRESHENV_PATH = "/mnt/script/cluster/deploy/refreshEnv.sh";
    private static final String DEFAULT_CMS_INIT_HAUTILS_PATH = "/mnt/script/cluster/deploy/init_hautils.sh";
    private static final String DEFAULT_CMS_CANCEL_PATH = "/mnt/script/cluster/deploy/changeHautilsToStandalone.sh";

    private static final List<String> CONTACT_RESTART_SERVICE = Arrays.asList("mc", "rmserver", "signal");

    @Resource
    private ScoutsRemoteClient scoutsRemoteClient;
    @Resource
    private K8sService k8sService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private ServerStatusService serverStatusService;
    @Autowired
    private IptablesService iptablesService;

    /**
     * 重试条件：异常 、false
     * 重试策略：每次间隔3s 重试10次
     */
    Retryer<Boolean> retryer = RetryerBuilder.<Boolean>newBuilder()
            .retryIfException()
            .retryIfResult(Predicates.equalTo(false))
            .withStopStrategy(StopStrategies.stopAfterAttempt(150))
            .withWaitStrategy(WaitStrategies.incrementingWait(0, TimeUnit.SECONDS, 2, TimeUnit.SECONDS))
            .build();

    @Override
    public void addContact(ActiveStandbyBaseInfoDto activeStandbyBaseInfoDto) {
        // 校验并设置member
        preCheck(activeStandbyBaseInfoDto);
        setDefaultInfo(activeStandbyBaseInfoDto);
        // 处理本节点
        selfContact(activeStandbyBaseInfoDto);
        // 处理对端
        peerContact(activeStandbyBaseInfoDto);
        // 处理仲裁机
        arbiterContact(activeStandbyBaseInfoDto);
        //等待scouts启动完成
        boolean scoutsStatus = checkScoutsStatus(activeStandbyBaseInfoDto);
        if (!scoutsStatus) {
            log.error("==>尝试调用本机器scouts为集群运行模式error.");
            throw new ServerException(ErrorStatus.SCOUTS_RUN_MODE_ERROR);
        }
        // 切换成集群模式
        try {
            retryer.call(() -> {
                log.info("==>尝试调用scouts为集群运行模式");
                Optional<ActiveStandbyStateInfoDto.NodeState> masterNodeCheck = masterScoutsNode();
                if (masterNodeCheck.isPresent() && masterNodeCheck.get().isAlive()) {
                    runModChange(masterNodeCheck.get(), ScoutsRunModeEnum.replicaset);
                    return checkScoutsReplicaStatus(activeStandbyBaseInfoDto);
                }
                return false;
            });
        } catch (Exception e) {
            log.error("==>尝试调用本机器scouts为集群运行模式error.", e);
        }
    }

    private boolean checkScoutsStatus(ActiveStandbyBaseInfoDto activeStandbyBaseInfoDto) {
        boolean status = false;
        int count = 0;
        while (!status && count < DEFAULT_RETRY_COUNT) {
            try {
                Thread.sleep(1000L);
                scoutsRemoteClient.nodeState(false);
                scoutsRemoteClient.nodeState(false, activeStandbyBaseInfoDto.getPeerIp() + ":"
                        + CmsActiveStandbyConstants.DEFAULT_SCOUTS_HTTP_PORT);
                List<String> list = activeStandbyBaseInfoDto.getArbitrationIp();
                list.forEach(ip -> scoutsRemoteClient.nodeState(false, ip + ":"
                        + CmsActiveStandbyConstants.DEFAULT_SCOUTS_HTTP_PORT));
                status = true;
                count++;
            } catch (Exception e) {
                log.warn("scouts 服务可能还没启动");
                count++;
            }
        }
        return status;
    }


    private boolean checkScoutsReplicaStatus(ActiveStandbyBaseInfoDto activeStandbyBaseInfoDto) {
        boolean replicasetStatus = false;
        int count = 0;
        while (!replicasetStatus && count < DEFAULT_RETRY_COUNT) {
            try {
                Thread.sleep(1000L);
                List<ActiveStandbyStateInfoDto.NodeState> masterNodeState = scoutsRemoteClient
                        .nodeState(false, activeStandbyBaseInfoDto.getLocalIp() + ":"
                                + CmsActiveStandbyConstants.DEFAULT_SCOUTS_HTTP_PORT);
                Optional<ActiveStandbyStateInfoDto.NodeState> masterNodeCheck = masterNodeState.stream().filter(o -> o.isLocal()).findFirst();
                List<ActiveStandbyStateInfoDto.NodeState> slaveNodeState = scoutsRemoteClient
                        .nodeState(false, activeStandbyBaseInfoDto.getPeerIp() + ":"
                                + CmsActiveStandbyConstants.DEFAULT_SCOUTS_HTTP_PORT);
                Optional<ActiveStandbyStateInfoDto.NodeState> slaveScoutsNodeCheck = slaveNodeState.stream().filter(o -> o.isLocal()).findFirst();
                List<String> list = activeStandbyBaseInfoDto.getArbitrationIp();
                boolean arbiterIsReplicasetMod = true;
                for (String arbiterIp : list) {
                    List<ActiveStandbyStateInfoDto.NodeState> arbiterNodeState = scoutsRemoteClient
                            .nodeState(false, arbiterIp + ":"
                                    + CmsActiveStandbyConstants.DEFAULT_SCOUTS_HTTP_PORT);
                    Optional<ActiveStandbyStateInfoDto.NodeState> arbiterScoutsNodeCheck = arbiterNodeState.stream().filter(o -> o.isLocal()).findFirst();
                    if (arbiterScoutsNodeCheck.isPresent() && !arbiterScoutsNodeCheck.get().isReplicasetMod()) {
                        arbiterIsReplicasetMod &= false;
                    }
                }
                replicasetStatus = masterNodeCheck.isPresent() && masterNodeCheck.get().isReplicasetMod()
                        && slaveScoutsNodeCheck.isPresent() && slaveScoutsNodeCheck.get().isReplicasetMod()
                        && arbiterIsReplicasetMod;
                count++;
            } catch (Exception e) {
                log.warn("scouts 服务可能还没启动");
                count++;
            }
        }
        return replicasetStatus;
    }

    @Override
    public ActiveStandbyPreCheckResponseDto addOrUpdatePre(ActiveStandbyBaseInfoDto activeStandbyBaseInfoDto) {
        preCheck(activeStandbyBaseInfoDto);
        ActiveStandbyPreCheckResponseDto activeStandbyPreCheckResponseDto = new ActiveStandbyPreCheckResponseDto();
        ActiveStandbyPreCheckResponseDto.NodeInfo master = new ActiveStandbyPreCheckResponseDto.NodeInfo();
        master.setProductName(getProductName());
        master.setLocalIp(activeStandbyBaseInfoDto.getLocalIp());
        ActiveStandbyPreCheckResponseDto.NodeInfo slave = new ActiveStandbyPreCheckResponseDto.NodeInfo();
        slave.setLocalIp(activeStandbyBaseInfoDto.getPeerIp());
        slave.setProductName(_getPeerProductName(activeStandbyBaseInfoDto.getPeerIp()));
        activeStandbyPreCheckResponseDto.setMaster(master);
        activeStandbyPreCheckResponseDto.setSlave(slave);
        return activeStandbyPreCheckResponseDto;
    }

    @Override
    public void selfContact(ActiveStandbyBaseInfoDto activeStandbyBaseInfoDto) {
        // 保存信息
        log.info("==>开始处理本机关联配置");
        activeStandbyBaseInfoDto.setLocalIp(IpUtils.getCmsIp().getIpv4());
        Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        allIp.put(CmsActiveStandbyConstants.ENV_CLUSTER_SCOUT_IP, activeStandbyBaseInfoDto.getLocalIp() + ":" + CmsActiveStandbyConstants.DEFAULT_SCOUTS_HTTP_PORT);
        allIp.put(CmsActiveStandbyConstants.ENV_CURRENT_PRODUCT_NAME, getProductName());
        allIp.put(CmsActiveStandbyConstants.ENV_CURRENT_SERVER_IP, activeStandbyBaseInfoDto.getLocalIp());
        allIp.put(CmsActiveStandbyConstants.ENV_PEER_IP, activeStandbyBaseInfoDto.getPeerIp());
        allIp.put(CmsActiveStandbyConstants.ENV_ARBITRATION_IP, String.join(",", activeStandbyBaseInfoDto.getArbitrationIp()));
        allIp.put(CmsActiveStandbyConstants.ENV_VIP, activeStandbyBaseInfoDto.getVip());
        allIp.put(CmsActiveStandbyConstants.ENV_CURRENT_HEARTBEAT_INTERVALS, String.valueOf(activeStandbyBaseInfoDto.getHeartbeatIntervals()));
        allIp.put(CmsActiveStandbyConstants.ENV_CURRENT_HEARTBEAT_TIMEOUT, String.valueOf(activeStandbyBaseInfoDto.getHeartbeatTimeout()));
        allIp.put(CmsActiveStandbyConstants.ENV_CURRENT_HEARTBEAT_DEAD_COUNT, String.valueOf(activeStandbyBaseInfoDto.getHeartbeatDeadCount()));
        allIp.put(CmsActiveStandbyConstants.ENV_PEER_ZK_PORT, "3181");
        allIp.put(CmsActiveStandbyConstants.ENV_PEER_SIGSERVER_ID, activeStandbyBaseInfoDto.getPeerSigserverId());
        allIp.put(CmsActiveStandbyConstants.ENV_SYSTEM_HA_ENABLE, "true");
        k8sService.editConfigmap(Constants.CONFIGMAP_ALLIP, allIp);
        //下发iptables规则
        iptablesService.checkAndUpdateRules("activeStandby", NodeType.ALL);
        try {
            Thread.sleep(10 * 1000L);
        } catch (InterruptedException e) {
            log.error("sleep error", e);
        }
        String hostName = getHostName();

        // all-sigserver-ha
        Map<String, String> sigserver = k8sService.getConfigmap(Constants.CONFIGMAP_ALL_SIGSERVER_HA);
        sigserver.put(CmsActiveStandbyConstants.SIG_SERVER_INTER_CLOUD_MASTER_SLAVE, "true");
        sigserver.put(CmsActiveStandbyConstants.SIG_MASTER_SLAVE_ENABLE, "true");
        String currentSigServerIdKey = hostName + "-SIGSERVER-ID";
        sigserver.put(currentSigServerIdKey, activeStandbyBaseInfoDto.getCurrentSigserverId());
        k8sService.editConfigmap(Constants.CONFIGMAP_ALL_SIGSERVER_HA, sigserver);

        // all-mc
        Map<String, String> mcserver = k8sService.getConfigmap(Constants.CONFIGMAP_MC);
        String currentMcNameKey = hostName + "-MC_NAME";
        mcserver.put(currentMcNameKey, activeStandbyBaseInfoDto.getCurrentMcName());
        k8sService.editConfigmap(Constants.CONFIGMAP_MC, mcserver);

        // all-rmserver
        Map<String, String> rmserver = k8sService.getConfigmap("all-rmserver");
        String currentRmServerNameKey = hostName + "-RM_SERVER_NAME";
        rmserver.put(currentRmServerNameKey, activeStandbyBaseInfoDto.getCurrentRmServerName());
        k8sService.editConfigmap("all-rmserver", rmserver);

        // /mnt/xylink/cmsHaVariables
        add_contact_cmsHaVariablesFile(allIp, currentSigServerIdKey, sigserver);

        // 修改scounts 配置文件，不改配置重启
        try {
            ScoutsConfigYamlUtils.updateContentInLocal(DEFAULT_SCOUTS_CONFIG_PATH, activeStandbyBaseInfoDto.getMembers(), activeStandbyBaseInfoDto.getHeartbeatIntervals(), activeStandbyBaseInfoDto.getHeartbeatTimeout(), activeStandbyBaseInfoDto.getHeartbeatDeadCount());
            scoutsRemoteClient.updateConfig("");
        } catch (Exception e) {
            log.error("修改scounts 配置文件,重启异常", e);
        }

        // 重启服务
        contact_restart();
    }

    private String getHostName() {
        Optional<NodeCache> node = k8sService.getNode("common-main");
        String hostName = "private-docker-main";
        if (node.isPresent()) {
            hostName = node.get().getMetadata().getName();
        }
        return hostName;
    }

    private void contact_restart() {
        log.info("==>开始处理本机相关服务：{}", CONTACT_RESTART_SERVICE);
        try {
            CONTACT_RESTART_SERVICE.forEach(service -> {
                k8sService.restartAllPodByNodeAppLabel(service);
            });
        } catch (Exception e) {
            log.error("重启服务异常", e);
        }

    }

    private void add_contact_cmsHaVariablesFile(Map<String, String> allIp, String currentSigServerIdKey, Map<String, String> sigserver) {
        Map<String, String> keyValueMap = new HashMap<>();
        keyValueMap.put(CmsActiveStandbyConstants.ENV_VIP, allIp.get(CmsActiveStandbyConstants.ENV_VIP));
        keyValueMap.put(CmsActiveStandbyConstants.DATABASE_TYPE, allIp.get("DATABASE_TYPE"));
        keyValueMap.put(CmsActiveStandbyConstants.ENV_PEER_IP, allIp.get(CmsActiveStandbyConstants.ENV_PEER_IP));
        keyValueMap.put(CmsActiveStandbyConstants.ENV_PEER_SIGSERVER_ID, allIp.get(CmsActiveStandbyConstants.ENV_PEER_SIGSERVER_ID));
        keyValueMap.put(CmsActiveStandbyConstants.ENV_CLUSTER_SCOUT_IP, allIp.get(CmsActiveStandbyConstants.ENV_CLUSTER_SCOUT_IP));
        keyValueMap.put(CmsActiveStandbyConstants.ENV_CURRENT_SERVER_IP, allIp.get(CmsActiveStandbyConstants.ENV_CURRENT_SERVER_IP));
        keyValueMap.put(currentSigServerIdKey, sigserver.get(currentSigServerIdKey));

        StringBuilder content = new StringBuilder();
        for (Map.Entry<String, String> entry : keyValueMap.entrySet()) {
            content.append(entry.getKey()).append("=").append(entry.getValue()).append("\n");
        }

        // 写入文件（覆盖原文件）
        try {
            Files.createDirectories(Paths.get(DEFAULT_CMS_HA_VARIABLES_PATH).getParent());
            Files.write(Paths.get(DEFAULT_CMS_HA_VARIABLES_PATH), content.toString().getBytes(), StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
        } catch (IOException e) {
            log.error("{} 更新失败.", DEFAULT_CMS_HA_VARIABLES_PATH, e);
        }

        try {
            List<String> result = CommandUtils.execCommand(new String[]{"/bin/sh", "-c", DEFAULT_CMS_HA_REFRESHENV_PATH});
            log.info("refreshEnv.sh response:[{}]", result);
        } catch (Exception e) {
            log.error("execCommand error", e);
        }

        try {
            List<String> result = CommandUtils.execCommand(new String[]{"/bin/sh", "-c", DEFAULT_CMS_INIT_HAUTILS_PATH});
            log.info("init_hautils.sh response:[{}]", result);
        } catch (Exception e) {
            log.error("execCommand error", e);
        }

    }

    private void peerContact(ActiveStandbyBaseInfoDto activeStandbyBaseInfoDto) {
        log.info("==>开始调用对端处理关联配置");
        ActiveStandbyBaseInfoDto activeStandbyBaseInfoDtoPeer = new ActiveStandbyBaseInfoDto();
        BeanUtils.copyProperties(activeStandbyBaseInfoDto, activeStandbyBaseInfoDtoPeer);
        activeStandbyBaseInfoDtoPeer.setPeerIp(activeStandbyBaseInfoDto.getLocalIp());
        activeStandbyBaseInfoDtoPeer.swapProperties("peerRmServerName", "currentRmServerName");
        activeStandbyBaseInfoDtoPeer.swapProperties("peerSigserverId", "currentSigserverId");
        activeStandbyBaseInfoDtoPeer.swapProperties("peerMcName", "currentMcName");
        activeStandbyBaseInfoDtoPeer.setLocalIp(null);

        String peerManagerIp = activeStandbyBaseInfoDto.getPeerIp();
        String url = "http://" + peerManagerIp + ":18028" + "/manager/peer/scouts/node/contact/addOrUpdate";
        PeerContactAddOrUpdateDto peerContactAddOrUpdateDto = new PeerContactAddOrUpdateDto();
        peerContactAddOrUpdateDto.setActiveStandbyBaseInfoDto(activeStandbyBaseInfoDtoPeer);
        peerContactAddOrUpdateDto.setAlertApiReq(getAlertApiReq(AlertApiReqValidate.PEER_SCOUTS_CONTACT_ADD));

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<PeerContactAddOrUpdateDto> requestEntity = new HttpEntity<>(peerContactAddOrUpdateDto, headers);
        log.info("Request peer request:{}", requestEntity);
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
        log.info("Request peer:{} response is:{}", url, responseEntity);
    }

    private void arbiterContact(ActiveStandbyBaseInfoDto activeStandbyBaseInfoDto) {
        List<String> arbitrationIps = activeStandbyBaseInfoDto.getArbitrationIp();
        for (String arbitrationIp : arbitrationIps) {
            log.info("==>开始调用仲裁机:[{}]处理关联配置", arbitrationIp);
            _arbiterUpdateByLocalYaml(arbitrationIp);
        }
    }

    private void arbiterUpdateContact(ActiveStandbyBaseInfoDto activeStandbyBaseInfoDto) {
        List<String> arbitrationIps = activeStandbyBaseInfoDto.getArbitrationIp();
        for (String arbitrationIp : arbitrationIps) {
            log.info("==>更新：仲裁机器处理:{}", arbitrationIp);
            _arbiterUpdateByLocalYaml(arbitrationIp);
        }
    }

    private void _arbiterUpdateByLocalYaml(String arbitrationIp) {
        String yaml = ScoutsConfigYamlUtils.currentContent(DEFAULT_SCOUTS_CONFIG_PATH);
        scoutsRemoteClient.updateConfig(yaml, arbitrationIp + ":" + CmsActiveStandbyConstants.DEFAULT_SCOUTS_HTTP_PORT);
    }

    /**
     * <pre>
     *     浮动IP与本机IP不在同一局域网！
     *     浮动IP不可与本机IP设为相同地址！
     *
     * </pre>
     *
     * @param activeStandbyBaseInfoDto
     */
    private void vipInfoValidation(ActiveStandbyBaseInfoDto activeStandbyBaseInfoDto) {
        if (activeStandbyBaseInfoDto.getVip().equalsIgnoreCase(activeStandbyBaseInfoDto.getLocalIp())) {
            throw new ServerException(ErrorStatus.CMS_ACTIVE_STANDBY_VIP_EQUAL_LOCALIP);
        }

        String subnetMask = IpUtils.subnetMask();
        log.info("subnetMask is:{}", subnetMask);
        if (!IpUtils.isInSameSubnet(activeStandbyBaseInfoDto.getVip(), activeStandbyBaseInfoDto.getLocalIp(), subnetMask)) {
            throw new ServerException(ErrorStatus.CMS_ACTIVE_STANDBY_VIP_LOCALIP_NOT_SAME_LAN);
        }

    }

    /**
     * <pre>
     *    仲裁机设备类型非AMS或与该设备无法网络互通toast提示：仲裁机系统异常！  manager调仲裁机scouts，scouts 通过dmidecode  -t system查
     *    仲裁机（ip信息）已离线！（选择完仲裁节点后，突然下线，scouts失联）manager调仲裁机scouts接口
     * </pre>
     *
     * @param activeStandbyBaseInfoDto
     */
    private void arbiterInfoValidation(ActiveStandbyBaseInfoDto activeStandbyBaseInfoDto) {
        List<String> arbitrationIps = activeStandbyBaseInfoDto.getArbitrationIp();
        if (CollectionUtils.isEmpty(arbitrationIps)) {
            throw new ServerException(ErrorStatus.CMS_ACTIVE_STANDBY_ARBITER_EXCEPTION);
        }


        for (String arbitrationIp : arbitrationIps) {
            try {
                List<ActiveStandbyStateInfoDto.NodeState> nodeStates = scoutsRemoteClient.nodeState(false, arbitrationIp + ":" + CmsActiveStandbyConstants.DEFAULT_SCOUTS_HTTP_PORT);
                nodeStates.forEach(nodeState -> {
                    if (nodeState.isLocal() && !nodeState.isAlive()) {
                        log.error("arbiter node info:[{}]", nodeState);
                        throw new ServerException(ErrorStatus.CMS_ACTIVE_STANDBY_ARBITER_NOT_READY);
                    }

                    if (nodeState.isLocal() && !isAms(nodeState)) {
                        log.error("arbiter node info:[{}]", nodeState);
                        throw new ServerException(ErrorStatus.CMS_ACTIVE_STANDBY_ARBITER_EXCEPTION);
                    }
                });
            } catch (Exception e) {
                throw new ServerException(e, ErrorStatus.CMS_ACTIVE_STANDBY_ARBITER_EXCEPTION);
            }
        }
    }

    /**
     * <pre>
     *     对端设备类型非CMS或与该设备无法网络互通toast提示：对端系统异常！
     *     对端系统版本与本机不一致！  德成确认哪些服务（服务管理版本号）
     *     对端已离线！（选择完对端IP后，对端设备突然下线，scouts失联） manager调对端manager,  对端manager调本地scouts接口
     *      对端已与其他设备建立主备关系！manager调对端manager,  对端manager调本地scouts接口
     * </pre>
     *
     * @param activeStandbyBaseInfoDto
     */

    private void peerInfoValidation(ActiveStandbyBaseInfoDto activeStandbyBaseInfoDto) {
        List<DeploymentServerDto> self = getCheckServiceVersion();
        List<DeploymentServerDto> peer = getPeerCheckServiceVersion(activeStandbyBaseInfoDto.getPeerIp());
        // 将列表转换为 Map，key 为 labelAppName，value 为 version
        Map<String, String> map1 = toMap(self);
        Map<String, String> map2 = toMap(peer);
        // 遍历 map1，检查 map2 中是否存在相同的 labelAppName，并比较 version
        for (Map.Entry<String, String> entry : map1.entrySet()) {
            String labelAppName = entry.getKey();
            String version1 = entry.getValue();
            String version2 = map2.get(labelAppName);
            // 如果 map2 中存在相同的 labelAppName，但 version 不同，则返回 false
            if (!version1.equals(version2)) {
                throw new ServerException(ErrorStatus.CMS_ACTIVE_STANDBY_PEER_SERVICE_VERSION_NOT_SAME);
            }
        }

        List<ActiveStandbyStateInfoDto.NodeState> nodeStates;

        try {
            nodeStates = scoutsRemoteClient.nodeState(false, activeStandbyBaseInfoDto.getPeerIp() + ":" + CmsActiveStandbyConstants.DEFAULT_SCOUTS_HTTP_PORT);
        } catch (Exception e) {
            throw new ServerException(e, ErrorStatus.CMS_ACTIVE_STANDBY_PEER_EXCEPTION);
        }

        if (nodeStates == null || nodeStates.isEmpty()) {
            throw new ServerException(ErrorStatus.CMS_ACTIVE_STANDBY_PEER_EXCEPTION);
        }

        nodeStates.forEach(nodeState -> {
            if (nodeState.isLocal() && !nodeState.isAlive()) {
                log.error("peer node info:[{}]", nodeState);
                throw new ServerException(ErrorStatus.CMS_ACTIVE_STANDBY_PEER_NOT_READY);
            }

            if (nodeState.isLocal() && !isCms(nodeState)) {
                log.error("peer node info:[{}]", nodeState);
                throw new ServerException(ErrorStatus.CMS_ACTIVE_STANDBY_PEER_EXCEPTION);
            }

            if (!nodeState.isLocal() && !ScoutsRoleEnum.arbiter.name().equals(nodeState.getRole()) && !nodeState.getLocalIp().equals(activeStandbyBaseInfoDto.getLocalIp())) {
                log.error("peer node info:[{}]", nodeState);
                throw new ServerException(ErrorStatus.CMS_ACTIVE_STANDBY_PEER_BUSY);
            }

        });


    }

    private Map<String, String> toMap(List<DeploymentServerDto> list) {
        Map<String, String> map = new HashMap<>();
        for (DeploymentServerDto dto : list) {
            map.put(dto.getLabelAppName(), dto.getVersion());
        }
        return map;
    }


    @Override
    public void updateContact(ActiveStandbyBaseInfoDto activeStandbyBaseInfoDto) {
        Optional<ActiveStandbyBaseInfoDto> before = baseInfo();
        log.info("before is:[{}]", before);

        preCheck(activeStandbyBaseInfoDto);

        Optional<ActiveStandbyStateInfoDto.NodeState> masterNode = masterScoutsNode();
        masterNode.ifPresent(master -> runModChange(master, ScoutsRunModeEnum.ops));
        // 检查所有节点状态
        try {
            retryer.call(() -> {
                log.info("==>检查scouts是否为运维运行模式");
                List<ActiveStandbyStateInfoDto.NodeState> nodeStates = getScoutsNodeStatesWithNoServices();
                return !CollectionUtils.isEmpty(nodeStates) && nodeStates.stream().allMatch(nodeState -> ScoutsRunModeEnum.ops.name().equals(nodeState.getRunMod()));
            });
        } catch (Exception e) {
            log.error("==>检查scouts是否为运维运行模式error.", e);
        }

        // 更新配置
        selfUpdateContact(activeStandbyBaseInfoDto);
        // 处理对端
        peerUpdateContact(activeStandbyBaseInfoDto);
        // 处理仲裁机
        arbiterUpdateContact(activeStandbyBaseInfoDto);

        try {
            retryer.call(() -> {
                log.info("==>尝试调用master机器scouts为集群运行模式");
                Optional<ActiveStandbyStateInfoDto.NodeState> masterNodeCheck = masterScoutsNode();
                if (masterNodeCheck.isPresent() && masterNodeCheck.get().isAlive()) {
                    runModChange(masterNodeCheck.get(), ScoutsRunModeEnum.replicaset);
                    return checkScoutsReplicaStatus(activeStandbyBaseInfoDto);
                }
                return false;
            });
        } catch (Exception e) {
            log.error("==>尝试调用master机器scouts为集群运行模式error.", e);
        }

    }

    @Override
    public void selfUpdateContact(ActiveStandbyBaseInfoDto activeStandbyBaseInfoDto) {
        log.info("==>更新：本机处理");
        Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        allIp.put(CmsActiveStandbyConstants.ENV_CURRENT_HEARTBEAT_INTERVALS, String.valueOf(activeStandbyBaseInfoDto.getHeartbeatIntervals()));
        allIp.put(CmsActiveStandbyConstants.ENV_CURRENT_HEARTBEAT_TIMEOUT, String.valueOf(activeStandbyBaseInfoDto.getHeartbeatTimeout()));
        allIp.put(CmsActiveStandbyConstants.ENV_CURRENT_HEARTBEAT_DEAD_COUNT, String.valueOf(activeStandbyBaseInfoDto.getHeartbeatDeadCount()));
        k8sService.editConfigmap(Constants.CONFIGMAP_ALLIP, allIp);
        // 修改scounts 配置文件，不改配置重启
        try {
            ScoutsConfigYamlUtils.updateContentInLocal(DEFAULT_SCOUTS_CONFIG_PATH, activeStandbyBaseInfoDto.getHeartbeatIntervals(), activeStandbyBaseInfoDto.getHeartbeatTimeout(), activeStandbyBaseInfoDto.getHeartbeatDeadCount());
            scoutsRemoteClient.updateConfig("");
        } catch (Exception e) {
            log.error("修改scounts 配置文件,重启异常", e);
        }
    }

    private void peerUpdateContact(ActiveStandbyBaseInfoDto activeStandbyBaseInfoDto) {
        String peerManagerIp = activeStandbyBaseInfoDto.getPeerIp();
        log.info("==>更新：对端处理:{}", peerManagerIp);
        String url = "http://" + peerManagerIp + ":18028" + "/manager/peer/scouts/node/contact/update";
        PeerContactAddOrUpdateDto peerContactAddOrUpdateDto = new PeerContactAddOrUpdateDto();
        peerContactAddOrUpdateDto.setActiveStandbyBaseInfoDto(activeStandbyBaseInfoDto);
        peerContactAddOrUpdateDto.setAlertApiReq(getAlertApiReq(AlertApiReqValidate.PEER_SCOUTS_CONTACT_UPDATE));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<PeerContactAddOrUpdateDto> requestEntity = new HttpEntity<>(peerContactAddOrUpdateDto, headers);
        log.info("Request peer request:{}", requestEntity);
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
        log.info("Request peer:{} response is:{}", url, responseEntity);
    }

    private void preCheck(ActiveStandbyBaseInfoDto activeStandbyBaseInfoDto) {
        activeStandbyBaseInfoDto.setLocalIp(IpUtils.getCmsIp().getIpv4());
        peerInfoValidation(activeStandbyBaseInfoDto);
        arbiterInfoValidation(activeStandbyBaseInfoDto);
        vipInfoValidation(activeStandbyBaseInfoDto);
    }

    private void setDefaultInfo(ActiveStandbyBaseInfoDto activeStandbyBaseInfoDto) {
        activeStandbyBaseInfoDto.setLocalIp(IpUtils.getCmsIp().getIpv4());

        List<String> members = new ArrayList<>();
        members.add(masterMember(activeStandbyBaseInfoDto.getLocalIp()));
        members.add(slaveMember(activeStandbyBaseInfoDto.getPeerIp()));
        List<String> arbitrationIps = activeStandbyBaseInfoDto.getArbitrationIp();
        arbitrationIps.forEach(arbitrationIp -> {
            members.add(arbiterMember(arbitrationIp));
        });
        activeStandbyBaseInfoDto.setMembers(members);
        // id 的分配 尽量保持不变，优先取本地的
        setServerIdInfo(activeStandbyBaseInfoDto);
    }


    @Override
    public Optional<ActiveStandbyBaseInfoDto> baseInfo() {
        Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);

        String vip = allIp.get(CmsActiveStandbyConstants.ENV_VIP);
        String peerIp = allIp.get(CmsActiveStandbyConstants.ENV_PEER_IP);
        String arbitrationIp = allIp.get(CmsActiveStandbyConstants.ENV_ARBITRATION_IP);
        if (StringUtils.isBlank(vip) || StringUtils.isBlank(peerIp) || StringUtils.isBlank(arbitrationIp)) {
            return Optional.empty();
        }
        ActiveStandbyBaseInfoDto activeStandbyBaseInfoDto = new ActiveStandbyBaseInfoDto();
        activeStandbyBaseInfoDto.setVip(vip);
        activeStandbyBaseInfoDto.setPeerIp(peerIp);
        activeStandbyBaseInfoDto.setArbitrationIp(Stream.of(arbitrationIp.split(",")).collect(Collectors.toList()));
        activeStandbyBaseInfoDto.setHeartbeatDeadCount(Integer.parseInt(allIp.get(CmsActiveStandbyConstants.ENV_CURRENT_HEARTBEAT_DEAD_COUNT)));
        activeStandbyBaseInfoDto.setHeartbeatTimeout(Integer.parseInt(allIp.get(CmsActiveStandbyConstants.ENV_CURRENT_HEARTBEAT_TIMEOUT)));
        activeStandbyBaseInfoDto.setHeartbeatIntervals(Integer.parseInt(allIp.get(CmsActiveStandbyConstants.ENV_CURRENT_HEARTBEAT_INTERVALS)));
        SystemInfo.ActiveStandbyInfo activeStandbyInfo = getActiveStandbyInfo();
        activeStandbyBaseInfoDto.setRole(activeStandbyInfo.getRole());
        return Optional.of(activeStandbyBaseInfoDto);
    }

    @Override
    public ActiveStandbyStateInfoDto nodeState(boolean needService) {
        ActiveStandbyStateInfoDto activeStandbyStateInfoDto = new ActiveStandbyStateInfoDto();
        List<ActiveStandbyStateInfoDto.NodeState> nodeStates = scoutsRemoteClient.nodeState(needService);
        // 设置ProductName
        nodeStates = nodeStates.stream().filter(nodeState -> !isArbiter(nodeState)).collect(Collectors.toList());

        nodeStates = nodeStates.stream().filter(nodeState -> {
            if (ScoutsRunModeEnum.standalone.name().equals(nodeState.getRunMod())) {
                return nodeState.isLocal();
            }
            return true;
        }).collect(Collectors.toList());

        nodeStates.forEach(node -> {
            if (node.isLocal()) {
                node.setProductName(getProductName());
                activeStandbyStateInfoDto.setLocalIp(node.getLocalIp());
            } else {
                node.setProductName(getPeerProductName(node));
            }
        });
        Optional<ActiveStandbyBaseInfoDto> optionalActiveStandbyBaseInfoDto = this.baseInfo();
        if (optionalActiveStandbyBaseInfoDto.isPresent()) {
            ActiveStandbyBaseInfoDto activeStandbyBaseInfoDto = optionalActiveStandbyBaseInfoDto.get();
            activeStandbyStateInfoDto.setVip(activeStandbyBaseInfoDto.getVip());
            activeStandbyStateInfoDto.setHasPeer(true);
        }
        activeStandbyStateInfoDto.setNodeState(nodeStates);
        Optional<ActiveStandbyStateInfoDto.NodeState> master = masterScoutsNode(nodeStates);
        master.ifPresent(state -> activeStandbyStateInfoDto.setOps(state.isOpsRunMod()));
        return activeStandbyStateInfoDto;
    }

    @Override
    public String getProductName() {
        Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        return allIp.getOrDefault(CmsActiveStandbyConstants.ENV_CURRENT_PRODUCT_NAME, getProductNameByDmidecode());
    }

    @Override
    public void updateProductName(String productName) {
        Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        allIp.put(CmsActiveStandbyConstants.ENV_CURRENT_PRODUCT_NAME, productName);
        k8sService.editConfigmap(Constants.CONFIGMAP_ALLIP, allIp);
    }

    @Override
    public void cancel() {
        // 修改主scouts到standalone
        log.info("==>开始处理取消配置");
        Optional<ActiveStandbyBaseInfoDto> baseInfo = baseInfo();
        List<ActiveStandbyStateInfoDto.NodeState> nodeStates = getScoutsNodeStatesWithNoServices();
        Optional<ActiveStandbyStateInfoDto.NodeState> masterNode = masterScoutsNode(nodeStates);
        if (masterNode.isPresent()) {
            log.info("==>取消：切换standalone");
            runModChange(masterNode.get(), ScoutsRunModeEnum.standalone);
        } else {
            log.info("No master node.");
        }
        // 查询集群状态 standalone
        checkAllScoutsNodeRunMode(ScoutsRunModeEnum.standalone);
        // 本机处理
        selfCancel();
        // 检查本机器scouts状态
        try {
            retryer.call(() -> {
                log.info("==>检查本机scouts状态");
                Optional<ActiveStandbyStateInfoDto.NodeState> selfNodeCheck = selfScoutsNode();
                return selfNodeCheck.isPresent() && selfNodeCheck.get().isAlive();
            });
        } catch (Exception e) {
            log.error("==>检查本机scouts状态error.", e);
        }

        // 对端处理
        baseInfo.ifPresent(activeStandbyBaseInfoDto -> peerCancel(activeStandbyBaseInfoDto.getPeerIp()));

        // 仲裁机处理
        try {
            log.info("==>取消：仲裁机处理");
            Optional<List<ActiveStandbyStateInfoDto.NodeState>> arbiters = arbiterScoutsAddrs(nodeStates);
            arbiters.ifPresent(arbiter -> arbiter.forEach(this::arbiterCancel));
        } catch (Exception e) {
            log.error("仲裁机器处理异常", e);
        }
        checkScoutsStatus(baseInfo.get());
    }

    private void checkAllScoutsNodeRunMode(ScoutsRunModeEnum scoutsRunModeEnum) {
        try {
            retryer.call(() -> {
                log.info("==>检查scouts runMode is [{}] ?", scoutsRunModeEnum.name());
                List<ActiveStandbyStateInfoDto.NodeState> nodeStates = getScoutsNodeStatesWithNoServices();
                return nodeStates.stream().allMatch(nodeState -> scoutsRunModeEnum.name().equals(nodeState.getRunMod()));
            });
        } catch (Exception e) {
            log.error("==>检查scouts runMode is [{}] ?", scoutsRunModeEnum.name(), e);
        }
    }

    @Override
    public void selfCancel() {
        log.info("==>取消：本机处理");
        //  删除相关数据
        clearBaseInfo();
        // 重启服务
        contact_restart();
        // scouts配置文件修改 *************:60001:6001:master
        List<String> members = new ArrayList<>();
        String sb = IpUtils.getCmsIp().getIpv4() + ":" + CmsActiveStandbyConstants.DEFAULT_SCOUTS_NODE_PORT + ":" + CmsActiveStandbyConstants.DEFAULT_SCOUTS_HTTP_PORT + ":" + ScoutsRoleEnum.master.name();
        members.add(sb);
        String yaml = ScoutsConfigYamlUtils.updateContent(DEFAULT_SCOUTS_CONFIG_PATH, members);
        scoutsRemoteClient.updateConfig(yaml);
    }

    @Override
    public void runModChange(ScoutsRunModeEnum scoutsRunModeEnum) {
        log.info("==>开始切换成{}模式", scoutsRunModeEnum.name());
        Optional<ActiveStandbyStateInfoDto.NodeState> masterNode = masterScoutsNode();
        masterNode.ifPresent(nodeState -> runModChange(nodeState, scoutsRunModeEnum));

        try {
            retryer.call(() -> {
                log.info("==>检查scouts状态");
                Optional<ActiveStandbyStateInfoDto.NodeState> masterNodeCheck = masterScoutsNode();
                return masterNodeCheck.isPresent() && masterNodeCheck.get().isAlive() && scoutsRunModeEnum.name().equals(masterNodeCheck.get().getRunMod());
            });
        } catch (Exception e) {
            log.error("==>检查scouts状态error.", e);
        }
    }

    @Override
    public void runModChange(ActiveStandbyStateInfoDto.NodeState nodeState, ScoutsRunModeEnum scoutsRunModeEnum) {
        scoutsRemoteClient.runModeChange(scoutsRunModeEnum, getScoutsHttpAddress(nodeState));
    }

    @Override
    public void failover(String targetMasterAddr, String targetSlaveAddr) {
        ScoutsResultResponse response;
        try {
            response = scoutsRemoteClient.failover(getScoutsHttpAddress(targetMasterAddr), targetMasterAddr, targetSlaveAddr);
        } catch (Exception e) {
            log.error("==>切换失败，重试一次", e);
            response = scoutsRemoteClient.failover(getScoutsHttpAddress(targetMasterAddr), targetMasterAddr, targetSlaveAddr);
        }
        if (!response.isSuccess()) {
            throw new ServerException(response.getMsg());
        }
        // 检查scouts 是否在切换 或者 master已经是目标
        try {
            retryer.call(() -> {
                log.info("==>检查scouts状态");

                List<ActiveStandbyStateInfoDto.NodeState> nodeStates = getScoutsNodeStatesWithNoServices();

                if (nodeStates.stream().anyMatch(nodeState -> "switching".equals(nodeState.getChangeStatus()))) {
                    return true;
                }

                Optional<ActiveStandbyStateInfoDto.NodeState> masterNodeCheck = masterScoutsNode(nodeStates);
                if (masterNodeCheck.isPresent()) {
                    ActiveStandbyStateInfoDto.NodeState nodeState = masterNodeCheck.get();
                    return nodeState.getAddr().equals(targetMasterAddr);
                }
                return false;
            });
        } catch (Exception e) {
            log.error("==>检查scouts状态error.", e);
        }
    }

    @Override
    public SystemInfo.ActiveStandbyInfo getActiveStandbyInfo() {
        SystemInfo.ActiveStandbyInfo activeStandbyInfo = new SystemInfo.ActiveStandbyInfo();
        Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        String vip = allIp.get(CmsActiveStandbyConstants.ENV_VIP);
        String peerIp = allIp.get(CmsActiveStandbyConstants.ENV_PEER_IP);
        String arbitrationIp = allIp.get(CmsActiveStandbyConstants.ENV_ARBITRATION_IP);
        if (StringUtils.isBlank(vip) || StringUtils.isBlank(peerIp) || StringUtils.isBlank(arbitrationIp)) {
            return activeStandbyInfo;
        }
        List<ActiveStandbyStateInfoDto.NodeState> nodes = getScoutsNodeStatesWithNoServices();
        for (ActiveStandbyStateInfoDto.NodeState node : nodes) {
            if (node.isLocal()) {
                activeStandbyInfo.setRole(node.getRole());
            }

            if (ScoutsRoleEnum.master.name().equalsIgnoreCase(node.getRole())) {
                activeStandbyInfo.setOps(node.isOpsRunMod());
            }
        }

        return activeStandbyInfo;
    }

    @Override
    public List<DeploymentServerDto> getCheckServiceVersion() {
        List<DeploymentServerDto> allDameonSets = serverStatusService.allDameonSets();
        return allDameonSets.stream().filter(daemonSet ->
                CmsActiveStandbyConstants.CHECK_SERVICE_VERSION_LIST.contains(daemonSet.getLabelAppName())
        ).collect(Collectors.toList());
    }

    private List<DeploymentServerDto> getPeerCheckServiceVersion(String peerManagerIp) {
        String url = "http://" + peerManagerIp + ":18028" + "/manager/peer/scouts/node/server/version";
        AlertApiReq alertApiReq = getAlertApiReq(AlertApiReqValidate.PEER_SCOUTS_SERVICES_VERSION);
        ResponseEntity<DeploymentServerDto[]> responseEntity = restTemplate.postForEntity(url, alertApiReq, DeploymentServerDto[].class);
        if (responseEntity.getBody() == null) {
            return Collections.emptyList();
        }
        return Arrays.asList(responseEntity.getBody());
    }

    private String getProductNameByDmidecode() {
        String productName = null;
        try {
            List<String> command = Arrays.asList("dmidecode", "-t", "system");
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            Process process = processBuilder.start();
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            boolean productNameFound = false;

            while ((line = reader.readLine()) != null) {
                if (line.contains("Product Name:")) {
                    productNameFound = true;
                    productName = line.split("\\s*:\\s*", 2)[1];
                    break;
                }
            }
            if (!productNameFound) {
                log.info("Product Name not found.");
            }
            reader.close();
            process.waitFor();
        } catch (IOException | InterruptedException e) {
            log.error("dmidecode error.", e);
        }
        return StringUtils.isBlank(productName) ? "CMS1000" : productName;
    }

    private String getPeerProductName(ActiveStandbyStateInfoDto.NodeState node) {
        String peerManagerIp = node.getLocalIp();
        try {
            return _getPeerProductName(peerManagerIp);
        } catch (Exception e) {
            log.error("Error", e);
        }
        return "-";
    }

    private String _getPeerProductName(String peerManagerIp) {
        String url = "http://" + peerManagerIp + ":18028" + "/manager/peer/scouts/node/productName";
        log.info("Request peer:{}", url);
        AlertApiReq alertApiReq = getAlertApiReq(AlertApiReqValidate.PEER_SCOUTS_PRODUCTNAME);
        ResponseEntity<String> peerProductName = restTemplate.postForEntity(url, alertApiReq, String.class);
        log.info("Request peer:{} response is:{}", url, peerProductName);
        return peerProductName.getBody();
    }

    private static AlertApiReq getAlertApiReq(String eventType) {
        AlertApiReq alertApiReq = new AlertApiReq();
        String key = "inner";
        long timestamp = System.currentTimeMillis();
        alertApiReq.setKey(key);
        alertApiReq.setTimestamp(timestamp);
        alertApiReq.setSign(AlertApiReqValidate.sign(key, eventType, timestamp));
        return alertApiReq;
    }

    private void clearBaseInfo() {
        Optional<ActiveStandbyBaseInfoDto> optionalActiveStandbyBaseInfoDto = baseInfo();
        if (optionalActiveStandbyBaseInfoDto.isPresent()) {
            log.info("Clear base info:[{}]", optionalActiveStandbyBaseInfoDto.get());
            Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
            allIp.remove(CmsActiveStandbyConstants.ENV_VIP);
            allIp.remove(CmsActiveStandbyConstants.ENV_PEER_IP);
            allIp.remove(CmsActiveStandbyConstants.ENV_ARBITRATION_IP);
            allIp.remove(CmsActiveStandbyConstants.ENV_CURRENT_HEARTBEAT_DEAD_COUNT);
            allIp.remove(CmsActiveStandbyConstants.ENV_CURRENT_HEARTBEAT_TIMEOUT);
            allIp.remove(CmsActiveStandbyConstants.ENV_CURRENT_HEARTBEAT_INTERVALS);
            allIp.remove(CmsActiveStandbyConstants.ENV_CURRENT_SERVER_IP);
            allIp.remove(CmsActiveStandbyConstants.ENV_CLUSTER_SCOUT_IP);
            allIp.put(CmsActiveStandbyConstants.ENV_SYSTEM_HA_ENABLE, "false");
            k8sService.replaceConfigmap(Constants.CONFIGMAP_ALLIP, allIp);
        }

        Map<String, String> sigserver = k8sService.getConfigmap(Constants.CONFIGMAP_ALL_SIGSERVER_HA);
        sigserver.put(CmsActiveStandbyConstants.SIG_SERVER_INTER_CLOUD_MASTER_SLAVE, "false");
        sigserver.put(CmsActiveStandbyConstants.SIG_MASTER_SLAVE_ENABLE, "false");
        k8sService.editConfigmap(Constants.CONFIGMAP_ALL_SIGSERVER_HA, sigserver);

        List<ActiveStandbyStateInfoDto.NodeState> nodeStates = getScoutsNodeStatesWithNoServices();
        Optional<ActiveStandbyStateInfoDto.NodeState> master = masterScoutsNode(nodeStates);
        Optional<ActiveStandbyStateInfoDto.NodeState> slave = slaveScoutsNode(nodeStates);
        String masterIp = "127.0.0.1";
        String slaveIp = "127.0.0.1";
        if (master.isPresent()) {
            masterIp = master.get().getLocalIp();
        }

        if (slave.isPresent()) {
            slaveIp = slave.get().getLocalIp();
        }
        cancel_contact_cmsHaVariablesFile(masterIp, slaveIp);
    }

    private void cancel_contact_cmsHaVariablesFile(String masterIp, String slaveIp) {
        try {
            List<String> result = CommandUtils.execCommand(new String[]{"/bin/sh", "-c", DEFAULT_CMS_CANCEL_PATH, masterIp, slaveIp});
            log.info("refreshEnv.sh response:[{}]", result);
        } catch (Exception e) {
            log.error("execCommand error", e);
        }
    }

    private void peerCancel(String peerManagerIp) {
        log.info("==>取消：对端处理:{}", peerManagerIp);
        String url = "http://" + peerManagerIp + ":18028" + "/manager/peer/scouts/node/contact/cancel";
        AlertApiReq alertApiReq = getAlertApiReq(AlertApiReqValidate.PEER_SCOUTS_CONTACT_CANCEL);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, alertApiReq, String.class);
        log.info("Request peer:{} response is:{}", url, responseEntity);
    }

    private void arbiterCancel(ActiveStandbyStateInfoDto.NodeState arbiterNodeStates) {
        log.info("==>取消：仲裁机:[{}]处理", arbiterNodeStates.getLocalIp());
        List<String> members = new ArrayList<>();
        members.add(arbiterMember(arbiterNodeStates.getLocalIp()));
        String yaml = ScoutsConfigYamlUtils.updateContent(DEFAULT_SCOUTS_CONFIG_PATH, members);
        scoutsRemoteClient.updateConfig(yaml, arbiterNodeStates.getLocalIp() + ":" + CmsActiveStandbyConstants.DEFAULT_SCOUTS_HTTP_PORT);
    }

    private Optional<ActiveStandbyStateInfoDto.NodeState> masterScoutsNode(List<ActiveStandbyStateInfoDto.NodeState> nodeStates) {
        if (!CollectionUtils.isEmpty(nodeStates)) {
            return nodeStates.stream().filter(nodeState -> ScoutsRoleEnum.master.name().equalsIgnoreCase(nodeState.getRole())).findFirst();
        }
        return Optional.empty();
    }

    private Optional<ActiveStandbyStateInfoDto.NodeState> slaveScoutsNode(List<ActiveStandbyStateInfoDto.NodeState> nodeStates) {
        if (!CollectionUtils.isEmpty(nodeStates)) {
            return nodeStates.stream().filter(nodeState -> ScoutsRoleEnum.slave.name().equalsIgnoreCase(nodeState.getRole())).findFirst();
        }
        return Optional.empty();
    }

    private Optional<ActiveStandbyStateInfoDto.NodeState> arbiterScoutsNode(List<ActiveStandbyStateInfoDto.NodeState> nodeStates) {
        if (!CollectionUtils.isEmpty(nodeStates)) {
            return nodeStates.stream().filter(nodeState -> ScoutsRoleEnum.arbiter.name().equalsIgnoreCase(nodeState.getRole())).findFirst();
        }
        return Optional.empty();
    }

    private Optional<ActiveStandbyStateInfoDto.NodeState> selfScoutsNode(List<ActiveStandbyStateInfoDto.NodeState> nodeStates) {
        if (!CollectionUtils.isEmpty(nodeStates)) {
            return nodeStates.stream().filter(ActiveStandbyStateInfoDto.NodeState::isLocal).findFirst();
        }
        return Optional.empty();
    }

    private Optional<ActiveStandbyStateInfoDto.NodeState> masterScoutsNode() {
        List<ActiveStandbyStateInfoDto.NodeState> nodeStates;
        try {
            nodeStates = getScoutsNodeStatesWithNoServices();
        } catch (Exception e) {
            log.error("get node info error.", e);
            return Optional.empty();
        }
        return masterScoutsNode(nodeStates);
    }

    private Optional<ActiveStandbyStateInfoDto.NodeState> slaveScoutsNode() {
        List<ActiveStandbyStateInfoDto.NodeState> nodeStates;
        try {
            nodeStates = getScoutsNodeStatesWithNoServices();
        } catch (Exception e) {
            log.error("get node info error.", e);
            return Optional.empty();
        }
        return slaveScoutsNode(nodeStates);
    }

    private Optional<ActiveStandbyStateInfoDto.NodeState> arbiterScoutsNode() {
        List<ActiveStandbyStateInfoDto.NodeState> nodeStates;
        try {
            nodeStates = getScoutsNodeStatesWithNoServices();
        } catch (Exception e) {
            log.error("get node info error.", e);
            return Optional.empty();
        }
        return arbiterScoutsNode(nodeStates);
    }

    private Optional<ActiveStandbyStateInfoDto.NodeState> selfScoutsNode() {
        List<ActiveStandbyStateInfoDto.NodeState> nodeStates;
        try {
            nodeStates = getScoutsNodeStatesWithNoServices();
        } catch (Exception e) {
            log.error("get node info error.", e);
            return Optional.empty();
        }
        return selfScoutsNode(nodeStates);
    }

    private Optional<List<ActiveStandbyStateInfoDto.NodeState>> arbiterScoutsAddrs(List<ActiveStandbyStateInfoDto.NodeState> nodeStates) {
        if (!CollectionUtils.isEmpty(nodeStates)) {
            return Optional.of(nodeStates.stream().filter(nodeState -> ScoutsRoleEnum.arbiter.name().equalsIgnoreCase(nodeState.getRole())).collect(Collectors.toList()));
        }
        return Optional.empty();
    }

    private boolean isArbiter(ActiveStandbyStateInfoDto.NodeState nodeState) {
        return ScoutsRoleEnum.arbiter.name().equals(nodeState.getRole());
    }

    private String masterMember(String masterIp) {
        return masterIp + ":" + CmsActiveStandbyConstants.DEFAULT_SCOUTS_NODE_PORT + ":" + CmsActiveStandbyConstants.DEFAULT_SCOUTS_HTTP_PORT + ":" + ScoutsRoleEnum.master.name();
    }

    private String slaveMember(String slaveIp) {
        return slaveIp + ":" + CmsActiveStandbyConstants.DEFAULT_SCOUTS_NODE_PORT + ":" + CmsActiveStandbyConstants.DEFAULT_SCOUTS_HTTP_PORT + ":" + ScoutsRoleEnum.slave.name();
    }

    private String arbiterMember(String arbiterIp) {
        return arbiterIp + ":" + CmsActiveStandbyConstants.DEFAULT_SCOUTS_NODE_PORT + ":" + CmsActiveStandbyConstants.DEFAULT_SCOUTS_HTTP_PORT + ":" + ScoutsRoleEnum.arbiter.name();
    }

    private List<ActiveStandbyStateInfoDto.NodeState> getScoutsNodeStatesWithNoServices() {
        return scoutsRemoteClient.nodeState(false);
    }

    private String getScoutsHttpAddress(ActiveStandbyStateInfoDto.NodeState nodeState) {
        return nodeState.getLocalIp() + ":" + CmsActiveStandbyConstants.DEFAULT_SCOUTS_HTTP_PORT;
    }

    private String getScoutsHttpAddress(String nodeAddress) {
        return nodeAddress.substring(0, nodeAddress.indexOf(":")) + ":" + CmsActiveStandbyConstants.DEFAULT_SCOUTS_HTTP_PORT;
    }

    private boolean isCms(ActiveStandbyStateInfoDto.NodeState nodeState) {
        String peerManagerIp = nodeState.getLocalIp();
        String url = "http://" + peerManagerIp + ":18028" + "/manager/peer/scouts/node/system/info";
        AlertApiReq alertApiReq = getAlertApiReq(AlertApiReqValidate.PEER_SCOUTS_SYSTEM_INFO);
        ResponseEntity<SystemInfo> responseEntity = restTemplate.postForEntity(url, alertApiReq, SystemInfo.class);
        return responseEntity.getBody() != null && SystemMode.cms.name().equals(responseEntity.getBody().getMode());
    }

    private boolean isAms(ActiveStandbyStateInfoDto.NodeState nodeState) {
        return StringUtils.isNotBlank(nodeState.getDmidecodeName()) && nodeState.getDmidecodeName().startsWith("AMS");
    }

    private void setServerIdInfo(ActiveStandbyBaseInfoDto activeStandbyBaseInfoDto) {
        String hostName = getHostName();
        // all-sigserver-ha
        Map<String, String> sigserver = k8sService.getConfigmap(Constants.CONFIGMAP_ALL_SIGSERVER_HA);
        sigserver.put(CmsActiveStandbyConstants.SIG_SERVER_INTER_CLOUD_MASTER_SLAVE, "true");
        sigserver.put(CmsActiveStandbyConstants.SIG_MASTER_SLAVE_ENABLE, "true");
        String sigServerIdKey = hostName + "-SIGSERVER-ID";
        String currentPrivateSigId = sigserver.getOrDefault(sigServerIdKey, "privateSig1");
        activeStandbyBaseInfoDto.setCurrentSigserverId(currentPrivateSigId);
        activeStandbyBaseInfoDto.setPeerSigserverId("privateSig" + getPeerNumber(currentPrivateSigId));

        // all-mc
        Map<String, String> mcserver = k8sService.getConfigmap(Constants.CONFIGMAP_MC);
        String mcNameKey = hostName + "-MC_NAME";
        String currentMcName = mcserver.getOrDefault(mcNameKey, "mc1");
        activeStandbyBaseInfoDto.setCurrentMcName(currentMcName);
        activeStandbyBaseInfoDto.setPeerMcName("mc" + getPeerNumber(currentMcName));

        // all-rmserver
        Map<String, String> rmserver = k8sService.getConfigmap("all-rmserver");
        String rmServerNameKey = hostName + "-RM_SERVER_NAME";
        String currentRmName = rmserver.getOrDefault(rmServerNameKey, "rm1");
        activeStandbyBaseInfoDto.setCurrentRmServerName(currentRmName);
        activeStandbyBaseInfoDto.setPeerRmServerName("rm" + getPeerNumber(currentRmName));
    }

    private String getPeerNumber(String currentId) {
        return currentId.endsWith("1") ? "2" : "1";
    }

}
