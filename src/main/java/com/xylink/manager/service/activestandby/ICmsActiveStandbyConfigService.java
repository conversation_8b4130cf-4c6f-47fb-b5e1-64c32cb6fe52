package com.xylink.manager.service.activestandby;

import com.xylink.config.constant.ScoutsRunModeEnum;
import com.xylink.manager.controller.dto.DeploymentServerDto;
import com.xylink.manager.controller.dto.SystemInfo;
import com.xylink.manager.controller.dto.activestandby.ActiveStandbyBaseInfoDto;
import com.xylink.manager.controller.dto.activestandby.ActiveStandbyPreCheckResponseDto;
import com.xylink.manager.controller.dto.activestandby.ActiveStandbyStateInfoDto;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2025-03-11 15:10
 */
public interface ICmsActiveStandbyConfigService {
    /**
     * 添加
     *
     * @param activeStandbyBaseInfoDto
     */
    void addContact(ActiveStandbyBaseInfoDto activeStandbyBaseInfoDto);

    /**
     * 添加前的校验
     *
     * @param activeStandbyBaseInfoDto
     */
    ActiveStandbyPreCheckResponseDto addOrUpdatePre(ActiveStandbyBaseInfoDto activeStandbyBaseInfoDto);

    void selfContact(ActiveStandbyBaseInfoDto activeStandbyBaseInfoDto);

    /**
     * 修改 只能更新 scouts配置，不能修改ip相关（如需修改需要解除当前，再配置）
     *
     * @param activeStandbyBaseInfoDto
     */
    void updateContact(ActiveStandbyBaseInfoDto activeStandbyBaseInfoDto);

    void selfUpdateContact(ActiveStandbyBaseInfoDto activeStandbyBaseInfoDto);

    /**
     * 主备配置信息
     *
     * @return
     */
    Optional<ActiveStandbyBaseInfoDto> baseInfo();

    /**
     * 查询基础信息
     *
     * @param needService
     * @return
     */
    ActiveStandbyStateInfoDto nodeState(boolean needService);

    /**
     * 获取 产品名称
     *
     * @return
     */
    String getProductName();

    /**
     * 更新 产品名称
     */
    void updateProductName(String productName);

    /**
     * 取消主备
     */
    void cancel();

    void selfCancel();

    /**
     * 模式切换
     */
    void runModChange(ScoutsRunModeEnum scoutsRunModeEnum);

    /**
     * 模式切换
     *
     * @param nodeState
     * @param scoutsRunModeEnum
     */
    void runModChange(ActiveStandbyStateInfoDto.NodeState nodeState, ScoutsRunModeEnum scoutsRunModeEnum);

    /**
     * 主备切换
     *
     * @param targetMasterAddr
     * @param targetSlaveAddr
     */
    void failover(String targetMasterAddr, String targetSlaveAddr);

    /**
     * 主备
     *
     * @return
     */
    SystemInfo.ActiveStandbyInfo getActiveStandbyInfo();

    /**
     * 关键服务版本
     *
     * @return
     */
    List<DeploymentServerDto> getCheckServiceVersion();

}
