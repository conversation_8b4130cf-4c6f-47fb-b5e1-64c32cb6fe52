package com.xylink.manager.service.multiregion;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.ProxyConstants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.config.exception.basic.WebException;
import com.xylink.config.mapper.JsonMapper;
import com.xylink.config.util.JsonUtil;
import com.xylink.manager.controller.dto.multiregion.req.RegionConfigReq;
import com.xylink.manager.controller.dto.multiregion.resp.RegionConfigTypeResp;
import com.xylink.manager.model.deploy.Node;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.multiregion.configmap.RegionConfigDataConfigMap;
import com.xylink.manager.service.multiregion.configmap.RegionDataConfigMap;
import com.xylink.manager.service.watch.processor.impl.AllIpProcessor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/10/28 11:07 上午
 * 多区域配置controller 支持针对不同区域的域名配置
 */

@Service
public class MultiRegionConfigService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private K8sService k8sService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private MultiRegionService multiRegionService;
    @Autowired
    private IDeployService deployService;

    /**
     * 获取区域接入数据
     *
     * @return 区域数据
     */
    public List<RegionConfigDataConfigMap> getRegionConfigDataConfigMap() {
        String json = k8sService.getConfigmap(Constants.CONFIGMAP_MULTIREGION)
                .get(Constants.CONFIGMAP_KEY_MULTIREGION_CONFIG);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        List<RegionConfigDataConfigMap> regionDataConfigMaps;
        try {
            regionDataConfigMaps = objectMapper.readValue(json, objectMapper.getTypeFactory()
                    .constructCollectionType(List.class, RegionConfigDataConfigMap.class));
        } catch (JsonProcessingException e) {
            throw new WebException("configmap 区域接入数据解析出错" + e.getMessage());
        }

        return regionDataConfigMaps;
    }

    // 将最后一个:80和最后一个:443去掉
    private String removePort(String host) {
        if (StringUtils.isBlank(host)) {
            return host;
        }
        return isEncrypt() ? host.replaceAll(":443$", "") : host.replaceAll(":80$", "");
    }

    /**
     * 更新区域接入数据
     */
    private void updateDefaultRegionConfigDataConfigMap(List<RegionConfigDataConfigMap> data) {
        //做一个特殊处理, DEFAULT中的端口号如果是80或者443，全部去掉
        Optional<RegionConfigDataConfigMap> aDefault = data.stream()
                .filter(x -> x.getId().equals(MultiRegionService.DEFAULT))
                .findFirst();
        if (aDefault.isPresent()) {
            RegionConfigDataConfigMap defaultData = aDefault.get();
            //去除80 443默认端口，如果去除，记录下标记
            String mainHttp = removePort(defaultData.getMainHttp());
            if (mainHttp != null) {
                defaultData.setHiddenHttpPort(!mainHttp.equals(defaultData.getMainHttp()));
            }
            defaultData.setMainHttp(mainHttp);

            String mainWs = removePort(defaultData.getMainWs());
            if (mainWs != null) {
                defaultData.setHiddenWsPort(!mainWs.equals(defaultData.getMainWs()));
            }
            defaultData.setMainWs(mainWs);

            String mainWebrtc = removePort(defaultData.getWebrtc());
            if (mainWebrtc != null) {
                defaultData.setHiddenWebrtcPort(!mainWebrtc.equals(defaultData.getWebrtc()));
            }
            defaultData.setWebrtc(mainWebrtc);

            String mainvod = removePort(defaultData.getVod());
            if (mainvod != null) {
                defaultData.setHiddenVodPort(!mainvod.equals(defaultData.getVod()));
            }
            defaultData.setVod(mainvod);
        }
        updateK8sRegionConfigDataConfigmap(data);
    }

    public void updateK8sRegionConfigDataConfigmap(List<RegionConfigDataConfigMap> data) {
        //所有数据添加时间戳
        long timestamp = System.currentTimeMillis();
        data.forEach(x -> x.setTimestamp(timestamp));
        String json = JsonMapper.nonEmptyMapper().toJson(data);
        deployService.patchConfigMap(Constants.CONFIGMAP_MULTIREGION, Constants.NAMESPACE_DEFAULT, d -> {
            d.put(Constants.CONFIGMAP_KEY_MULTIREGION_CONFIG, json);
        });
        logger.info("update multi region data successfully,data is {}", json);
    }

    /**
     * 创建默认接入数据
     */
    public void createDefaultRegionConfig() {
        boolean isForceUpdate = false;
        //创建configmap 如果为空，就必须更新一个空值进去
        Map<String, String> configmapOrCreate = k8sService.getConfigmapOrCreate(Constants.CONFIGMAP_MULTIREGION);
        if (configmapOrCreate == null || configmapOrCreate.isEmpty() || !configmapOrCreate.containsKey(Constants.CONFIGMAP_KEY_MULTIREGION_CONFIG)) {
            isForceUpdate = true;
        }
        //创建数据
        List<RegionConfigDataConfigMap> maps = new ArrayList<>();
        RegionConfigDataConfigMap map = new RegionConfigDataConfigMap();
        maps.add(map);
        map.setId(MultiRegionService.DEFAULT);
        map.setManagerId(MultiRegionService.DEFAULT);
        updateDefaultRegionConfig(map, maps, isForceUpdate);
    }

    private void updateDefaultRegionConfig(RegionConfigDataConfigMap defaultData, List<RegionConfigDataConfigMap> allDatas, boolean isForceUpdate) {
        //原始数据端口号可能被隐藏了，需要恢复
        if (defaultData.isHiddenHttpPort() && StringUtils.isNotBlank(defaultData.getMainHttp())) {
            defaultData.setMainHttp(defaultData.getMainHttp() + (isEncrypt() ? ":443" : ":80"));
        }
        if (defaultData.isHiddenWsPort() && StringUtils.isNotBlank(defaultData.getMainWs())) {
            defaultData.setMainWs(defaultData.getMainWs() + (isEncrypt() ? ":443" : ":80"));
        }
        if (defaultData.isHiddenWebrtcPort() && StringUtils.isNotBlank(defaultData.getWebrtc())) {
            defaultData.setWebrtc(defaultData.getWebrtc() + (isEncrypt() ? ":443" : ":80"));
        }
        if (defaultData.isHiddenVodPort() && StringUtils.isNotBlank(defaultData.getVod())) {
            defaultData.setVod(defaultData.getVod() + (isEncrypt() ? ":443" : ":80"));
        }
        //保存下原始数据
        String oldData = JsonUtil.toJson(defaultData);

        //处理main的数据
        Map<String, String> allIpMap = deployService.getConfigMapAllIp().getData();
        String mainDomain = allIpMap.get(NetworkConstants.MAIN_DOMAIN_NAME);
        if (StringUtils.isNotBlank(mainDomain)) {
            String mainHttpPort = allIpMap.get(NetworkConstants.MAIN_NGINX_PORT);
            String mainHttpsPort = allIpMap.get(NetworkConstants.MAIN_NGINX_SSL_PORT);
            defaultData.setMainHttpPort(mainHttpPort);
            defaultData.setMainHttpsPort(mainHttpsPort);
            if (isEncrypt()) {
                if (StringUtils.isNotBlank(mainHttpsPort)) {
                    defaultData.setMainHttp("https://" + mainDomain + ":" + mainHttpsPort);
                    defaultData.setMainWs("wss://" + mainDomain + ":" + mainHttpsPort);
                }
            } else {
                if (StringUtils.isNotBlank(mainHttpPort)) {
                    defaultData.setMainHttp("http://" + mainDomain + ":" + mainHttpPort);
                    defaultData.setMainWs("ws://" + mainDomain + ":" + mainHttpPort);
                }
            }
        }

        //webrtc只有为空的时候，才处理
        if (StringUtils.isBlank(defaultData.getWebrtc())) {
            //新部署模式
            boolean b = updateDefaultWebrtcProxyData(defaultData);
            //新部署模式webrtc-proxy未发现，走老部署模式
            if (!b) {
                updateDefaultOpenRestyWebrtcData(defaultData);
            }
        }

        //处理vod的数据
        String vodDomain = allIpMap.get(NetworkConstants.VOD_DOMAIN_NAME);
        if (StringUtils.isNotBlank(vodDomain)) {
            String vodHttpPort = allIpMap.get(NetworkConstants.VOD_NGINX_PORT);
            String vodHttpsPort = allIpMap.get(NetworkConstants.VOD_NGINX_SSL_PORT);
            defaultData.setVodHttpPort(vodHttpPort);
            defaultData.setVodHttpsPort(vodHttpsPort);
            if (isEncrypt()) {
                if (StringUtils.isNotBlank(vodHttpsPort)) {
                    defaultData.setVod("https://" + vodDomain + ":" + vodHttpsPort);
                }
            } else {
                if (StringUtils.isNotBlank(vodHttpPort)) {
                    defaultData.setVod("http://" + vodDomain + ":" + vodHttpPort);
                }
            }
        }

        String newData = JsonUtil.toJson(defaultData);
        //判断是否需要更新
        if (!oldData.equals(newData) || isForceUpdate) {
            updateDefaultRegionConfigDataConfigMap(allDatas);
        }
    }

    /**
     * 更新默认数据中的Webrtc-proxy数据，如果无法找到，再找openresty-webrtc
     *
     * @param defaultData 默认数据
     * @return 是否更新
     */
    private boolean updateDefaultWebrtcProxyData(RegionConfigDataConfigMap defaultData) {
        // 获取带有openresty-webrtc和webrtc-proxy的webrtc节点
        List<Node> nodes = deployService.listNodesByLabels(Constants.TYPE, new String[]{Labels.webrtc.label(),
                Labels.webrtc_arm.label(),
                Labels.webrtc_x86.label()});

        for (Node node : nodes) {
            Map<String, String> labels = node.getLabels();
            if (!CollectionUtils.isEmpty(labels) && (
                    labels.containsKey(Labels.webrtc_proxy.label()) ||
                            labels.containsKey(Labels.webrtc_proxy_x86.label()) ||
                            labels.containsKey(Labels.webrtc_proxy_arm.label())) &&

                    (labels.containsKey(Labels.openresty_webrtc.label()) ||
                            labels.containsKey(Labels.openresty_webrtc_x86.label()) ||
                            labels.containsKey(Labels.openresty_webrtc_arm.label()))) {
                //获取Node名称
                String name = node.getName();
                // 获取域名
                String nodeDomainKey = name + NetworkConstants.SUFFIX_DOMAIN;
                Map<String, String> allWebrtcProxyMap = k8sService.getConfigmap(Constants.CONFIGMAP_ALL_WEBRTC_PROXY);
                String domain = allWebrtcProxyMap.get(nodeDomainKey);
                if (StringUtils.isNotBlank(domain)) {
                    RegionConfigTypeResp.DataDTO dataDTO = new RegionConfigTypeResp.DataDTO();
                    dataDTO.setDomain(domain);
                    // 获取端口
                    String webrtcHttpPort = allWebrtcProxyMap.get(name + "-WEBRTC_PROXY_NGINX_PORT");
                    String webrtcHttpsPort = allWebrtcProxyMap.get(name + "-WEBRTC_PROXY_NGINX_SSL_PORT");
                    String webrtcPort = isEncrypt() ? webrtcHttpsPort : webrtcHttpPort;
                    defaultData.setWebrtcHttpPort(webrtcHttpPort);
                    defaultData.setWebrtcHttpsPort(webrtcHttpsPort);
                    if (StringUtils.isNotBlank(webrtcPort)) {
                        defaultData.setWebrtc("https://" + domain + ":" + webrtcPort);
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private void updateDefaultOpenRestyWebrtcData(RegionConfigDataConfigMap defaultData) {
        // 获取所有webrtc节点
        List<Pod> pods = getPodsByLables(new String[]{Labels.openresty_webrtc.label(),
                Labels.openresty_webrtc_x86.label(),
                Labels.openresty_webrtc_arm.label()});

        if (!CollectionUtils.isEmpty(pods)) {
            for (Pod pod : pods) {
                //通过pod名称获取域名
                String name = pod.getNodeName();
                // 获取域名
                String nodeDomainKey = name + "-" + NetworkConstants.WEBRTC_DOMAIN_NAME;
                String domain = k8sService.getConfigmap(Constants.CONFIGMAP_WEBRTC).get(nodeDomainKey);
                //打印webrtc端口
                if (StringUtils.isNotBlank(domain)) {
                    Map<String, String> allOpenrestyWebrtcMap = k8sService.getConfigmap("all-openresty-webrtc");
                    String webrtcHttpPort = allOpenrestyWebrtcMap.get("WEBRTC_NGINX_PORT");
                    String webrtcHttpsPort = allOpenrestyWebrtcMap.get("WEBRTC_NGINX_SSL_PORT");
                    String webrtcPort = isEncrypt() ? webrtcHttpsPort : webrtcHttpPort;
                    if (StringUtils.isNotBlank(webrtcPort)) {
                        defaultData.setWebrtc("https://" + domain + ":" + webrtcPort);
                        return;
                    }
                }
            }
        }
    }

    /**
     * 校验default数据是否正常
     */
    public boolean checkDefaultRegionConfigExist() {
        List<RegionConfigDataConfigMap> datas = getRegionConfigDataConfigMap();
        if (datas == null) {
            logger.error("multiRegionData - all config data is not exist,need to init...");
            return false;
        }
        Optional<RegionConfigDataConfigMap> defaultData = datas.stream()
                .filter(data -> data.getId().equals(MultiRegionService.DEFAULT))
                .findFirst();

        if (!defaultData.isPresent()) {
            logger.error("multiRegionData - default  is not exist,need to init...");
            return false;
        }

        RegionConfigDataConfigMap regionConfigDataConfigMap = defaultData.get();
        String mainHttp = regionConfigDataConfigMap.getMainHttp();
        String mainWs = regionConfigDataConfigMap.getMainWs();
        if (StringUtils.isBlank(mainHttp) || StringUtils.isBlank(mainWs)) {
            logger.error("multiRegionData - default main  is not exist,need to init...");
            return false;
        }

        return true;
    }

    /**
     * 刷新默认配置
     */
    public void refreshDefaultConfig() {
        List<RegionConfigDataConfigMap> datas = getRegionConfigDataConfigMap();
        if (datas == null) {
            //还没进入过页面，不处理刷新
            return;
        }
        logger.info("[multi region] refreshDefaultConfig starting...");
        Optional<RegionConfigDataConfigMap> defaultData = datas.stream()
                .filter(data -> data.getId().equals(MultiRegionService.DEFAULT))
                .findFirst();
        defaultData.ifPresent(regionConfigDataConfigMap -> updateDefaultRegionConfig(regionConfigDataConfigMap, datas, false));
    }


    /**
     * 事件触发，刷新默认配置
     */
    public void refreshDefaultConfig(List<AllIpProcessor.ChangeData> changes) {
        Set<String> set = changes.stream()
                .map(AllIpProcessor.ChangeData::getKey)
                .collect(Collectors.toSet());
        if (set.contains(NetworkConstants.MAIN_DOMAIN_NAME) ||
                set.contains(NetworkConstants.MAIN_NGINX_SSL_PORT) ||
                set.contains(NetworkConstants.MAIN_NGINX_PORT) ||
                set.contains(NetworkConstants.VOD_NGINX_SSL_PORT) ||
                set.contains(NetworkConstants.VOD_NGINX_PORT) ||
                set.contains(NetworkConstants.VOD_DOMAIN_NAME)
        ) {
            refreshDefaultConfig();
        }
    }

    // 获取协议
    private String getScheme() {
        return k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP).get("SCHEME");
    }

    // 是否加密
    public boolean isEncrypt() {
        return getScheme().equals("https/wss");
    }

    //判断URL是域名还是IP地址
    private static boolean isIPAddress(String host) {
        // 正则表达式匹配 IPv4 地址
        String ipv4Pattern = "^([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\.([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\.([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\.([01]?\\d\\d?|2[0-4]\\d|25[0-5])$";

        //无全0块，标准IPv6地址的正则表达式
        Pattern IPV6_STD_REGEX =
                Pattern.compile(
                        "^\\[([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}\\]$");
        //压缩正则表达式
        Pattern IPV6_COMPRESS_REGEX =
                Pattern.compile(
                        "^\\[(([0-9A-Fa-f]{1,4}(:[0-9A-Fa-f]{1,4})*)?)::((([0-9A-Fa-f]{1,4}:)*[0-9A-Fa-f]{1,4})?)\\]$");

        Pattern ipv4Regex = Pattern.compile(ipv4Pattern);

        // 检查主机是否匹配 IPv4 或 IPv6 地址
        return ipv4Regex.matcher(host).matches() ||
                IPV6_STD_REGEX.matcher(host).matches() ||
                IPV6_COMPRESS_REGEX.matcher(host).matches();
    }

    /**
     * 获取已经使用的IP端口信息
     */
    private Set<String> getUsedIpPort(String type) {
        HashSet<String> used = new HashSet<>();
        List<RegionConfigDataConfigMap> data = getRegionConfigDataConfigMap();
        if (data != null && !data.isEmpty()) {
            // 获取已经使用的main的域名
            Set<String> collect = data.stream().map(x -> {
                String use;
                if ("webrtc".equals(type)) {
                    use = x.getWebrtc();
                } else if ("vod".equals(type)) {
                    use = x.getVod();
                } else {
                    use = x.getMainHttp();
                }
                if (use != null) {
                    use = use.replace("http://", "");
                    use = use.replace("https://", "");
                }
                return use;
            }).filter(Objects::nonNull).collect(Collectors.toSet());
            used.addAll(collect);
        }
        return used;
    }

    private List<Pod> getPodsByLables(String[] labels) {
        //给数组中添加上"private-"前缀
        String[] labelsWithPrefix = Arrays.stream(labels).map(label -> "private-" + label).toArray(String[]::new);
        return k8sService.getPodListWithLabelsInApp(labelsWithPrefix);
    }


    /**
     * 获取main相关的配置信息
     * 数据来源：all-main-proxy 下的 private-xylink-main-proxy-1647503713-new-DOMAIN
     * （item.getMetadata().getName() + NetworkConstants.SUFFIX_DOMAIN）
     *
     * @return
     */
    public List<RegionConfigTypeResp.DataDTO> getMainList(boolean checkedUsed) {
        Set<String> used = getUsedIpPort("main");
        List<RegionConfigTypeResp.DataDTO> res = new ArrayList<>();

        // 获取所有main节点
        List<Pod> pods = getPodsByLables(new String[]{Labels.main_proxy.label(),
                Labels.main_proxy_x86.label(),
                Labels.main_proxy_arm.label()});

        if (!CollectionUtils.isEmpty(pods)) {
            Map<String, String> mainProxyMap = k8sService.getConfigmap("all-main-proxy");
            pods.forEach(pod -> {
                //通过pod名称获取域名
                String name = pod.getNodeName();
                // 获取域名
                String nodeDomainKey = name + NetworkConstants.SUFFIX_DOMAIN;
                String domain = mainProxyMap.get(nodeDomainKey);
                if (StringUtils.isNotBlank(domain)) {
                    RegionConfigTypeResp.DataDTO dataDTO = new RegionConfigTypeResp.DataDTO();
                    dataDTO.setDomain(domain);
                    // 获取端口
                    String httpPort = mainProxyMap.get(name + ProxyConstants.NGINX_PORT);
                    String httpsPort = mainProxyMap.get(name + ProxyConstants.NGINX_SSL_PORT);
                    dataDTO.setPort(httpPort + ":" + httpsPort);
                    dataDTO.setHttpPort(httpPort);
                    dataDTO.setHttpsPort(httpsPort);
                    if (checkedUsed) {
                        if (!used.contains(domain + ":" + dataDTO.getPort())) {
                            res.add(dataDTO);
                        }
                    } else {
                        res.add(dataDTO);
                    }
                }
            });
        }

        //排序
        return sort(res);
    }


    /**
     * 获取webrtc相关的配置信息
     * 数据来源：all-main-proxy 下的 private-xylink-main-proxy-1647503713-new-DOMAIN
     * （item.getMetadata().getName() + NetworkConstants.SUFFIX_DOMAIN）
     */
    public List<RegionConfigTypeResp.DataDTO> getWebrtcList() {
        List<RegionConfigTypeResp.DataDTO> res = new ArrayList<>();
        //如果webrtc proxy list存在，就使用proxy，否则使用openresty webrtc
        getWebrtcProxyList(res);
        if (res.isEmpty()) {
            getOpenrestyWebrtcList(res);
        }
        //对list进行去重，仅使用domain和port来判断重复
        Map<String, RegionConfigTypeResp.DataDTO> distinctMap = new HashMap<>();
        res.forEach(x -> distinctMap.put(x.getDomain() + ":" + x.getPort(), x));
        //排序
        return sort(new ArrayList<>(distinctMap.values()));
    }

    //新部署方案，获取webrtc-proxy列表
    private void getWebrtcProxyList(List<RegionConfigTypeResp.DataDTO> res) {
        // 获取所有webrtc-proxy节点
        List<Pod> pods = getPodsByLables(new String[]{Labels.webrtc_proxy.label(),
                Labels.webrtc_proxy_arm.label(),
                Labels.webrtc_proxy_x86.label()});

        if (!CollectionUtils.isEmpty(pods)) {
            Map<String, String> webrtcProxyMap = k8sService.getConfigmap(Constants.CONFIGMAP_ALL_WEBRTC_PROXY);
            pods.forEach(pod -> {
                //通过hostname获取域名
                String name = pod.getNodeName();
                // 获取域名
                String nodeDomainKey = name + NetworkConstants.SUFFIX_DOMAIN;
                String domain = webrtcProxyMap.get(nodeDomainKey);
                if (StringUtils.isNotBlank(domain)) {
                    RegionConfigTypeResp.DataDTO dataDTO = new RegionConfigTypeResp.DataDTO();
                    dataDTO.setDomain(domain);
                    // 获取端口
                    String httpPort = webrtcProxyMap.get(name + "-WEBRTC_PROXY_NGINX_PORT");
                    String httpsPort = webrtcProxyMap.get(name + "-WEBRTC_PROXY_NGINX_SSL_PORT");
                    dataDTO.setPort(httpPort + ":" + httpsPort);
                    dataDTO.setHttpPort(httpPort);
                    dataDTO.setHttpsPort(httpsPort);
                    res.add(dataDTO);
                }
            });
        }
    }

    //无webrtc proxy，兼容老方案
    private void getOpenrestyWebrtcList(List<RegionConfigTypeResp.DataDTO> res) {
        // 获取所有webrtc节点
        List<Pod> pods = getPodsByLables(new String[]{Labels.openresty_webrtc.label(),
                Labels.openresty_webrtc_x86.label(),
                Labels.openresty_webrtc_arm.label()});
        if (!CollectionUtils.isEmpty(pods)) {
            pods.forEach(pod -> {
                //通过pod名称获取域名
                String name = pod.getNodeName();
                // 获取域名
                String nodeDomainKey = name + "-" + NetworkConstants.WEBRTC_DOMAIN_NAME;
                String domain = k8sService.getConfigmap("all-webrtc").get(nodeDomainKey);
                if (StringUtils.isNotBlank(domain)) {
                    RegionConfigTypeResp.DataDTO dataDTO = new RegionConfigTypeResp.DataDTO();
                    dataDTO.setDomain(domain);
                    // 获取端口
                    Map<String, String> map = k8sService.getConfigmap("all-openresty-webrtc");
                    String httpPort = map.get("WEBRTC_NGINX_PORT");
                    String httpsPort = map.get("WEBRTC_NGINX_SSL_PORT");
                    dataDTO.setPort(httpPort + ":" + httpsPort);
                    dataDTO.setHttpPort(httpPort);
                    dataDTO.setHttpsPort(httpsPort);
                    res.add(dataDTO);
                }
            });
        }
    }

    //域名排前面，IP排后面
    private List<RegionConfigTypeResp.DataDTO> sort(List<RegionConfigTypeResp.DataDTO> list) {
        List<RegionConfigTypeResp.DataDTO> res = new ArrayList<>();
        List<RegionConfigTypeResp.DataDTO> domain = list.stream().filter(x -> !isIPAddress(x.getDomain()))
                .sorted(Comparator.comparing(RegionConfigTypeResp.DataDTO::getDomain))
                .collect(Collectors.toList());
        res.addAll(domain);
        List<RegionConfigTypeResp.DataDTO> ip = list.stream().filter(x -> isIPAddress(x.getDomain()))
                .sorted(Comparator.comparing(RegionConfigTypeResp.DataDTO::getDomain))
                .collect(Collectors.toList());
        res.addAll(ip);
        return res;
    }

    /**
     * 判断是否重复
     */
    public void checkIfExist(RegionConfigReq.DataDTO main) {
        // 获取所有数据
        List<RegionConfigDataConfigMap> datas = getRegionConfigDataConfigMap();
        if (datas != null) {
            // 新增 判断是否已存在,main不允许存在
            boolean exist = datas.stream()
                    .anyMatch(data -> data.getManagerId().equals(main.getDomain() + ":" + main.getPort()));
            if (exist) {
                throw new ServerException(ErrorStatus.REGIONCONFIG_DATA_EXISTED);
            }
        }
    }

    /**
     * 添加区域接入配置
     */
    public void add(RegionConfigReq.DataDTO main, RegionConfigReq.DataDTO webrtc, RegionConfigReq.DataDTO vod) {
        // 获取所有数据
        List<RegionConfigDataConfigMap> datas = getRegionConfigDataConfigMap();
        addNewRegionConfigData(main, webrtc, vod, datas, false);
    }

    /**
     * 新增接入数据
     */
    private void addNewRegionConfigData(RegionConfigReq.DataDTO main,
                                        RegionConfigReq.DataDTO webrtc,
                                        RegionConfigReq.DataDTO vod,
                                        List<RegionConfigDataConfigMap> datas,
                                        boolean isDefault) {
        boolean encrypt = isEncrypt();
        //查询可用区域列表
        List<RegionConfigTypeResp.DataDTO> mainList = getMainList(false);
        List<RegionConfigTypeResp.DataDTO> vodList = getVodList(false);
        List<RegionConfigTypeResp.DataDTO> webrtcList = getWebrtcList();
        Map<String, RegionConfigTypeResp.DataDTO> mainMap = mainList.stream()
                .collect(Collectors.toMap(it -> it.getDomain() + ":" + it.getPort(), it -> it));
        Map<String, RegionConfigTypeResp.DataDTO> vodMap = vodList.stream()
                .collect(Collectors.toMap(it -> it.getDomain() + ":" + it.getPort(), it -> it));
        Map<String, RegionConfigTypeResp.DataDTO> webrtcMap = webrtcList.stream()
                .collect(Collectors.toMap(it -> it.getDomain() + ":" + it.getPort(), it -> it));
        // 创建数据
        RegionConfigDataConfigMap newData = new RegionConfigDataConfigMap();
        if (isDefault) {
            newData.setId(MultiRegionService.DEFAULT);
            newData.setManagerId(MultiRegionService.DEFAULT);
        } else {
            String[] ports = main.getPort().split(":");
            newData.setId(main.getDomain() + ":" + (encrypt ? ports[1] : ports[0]));
            newData.setManagerId(main.getDomain() + ":" + main.getPort());
        }
        //判断是从哪个可用区域选择出来的，然后把两个端口的默认值放进去
        String mainDomainWithPort = main.getDomain() + ":" + main.getPort();
        String vodDomainWithPort = vod == null ? null : vod.getDomain() + ":" + vod.getPort();
        String webrtcDomainWithPort = webrtc == null ? null : webrtc.getDomain() + ":" + webrtc.getPort();
        if (mainMap.containsKey(mainDomainWithPort)) {
            newData.setMainHttpPort(mainMap.get(mainDomainWithPort).getHttpPort());
            newData.setMainHttpsPort(mainMap.get(mainDomainWithPort).getHttpsPort());
        } else {
            String[] ports = main.getPort().split(":");
            newData.setMainHttpPort(ports[0]);
            newData.setMainHttpsPort(ports[1]);
        }
        if (vodMap.containsKey(vodDomainWithPort)) {
            newData.setVodHttpPort(vodMap.get(vodDomainWithPort).getHttpPort());
            newData.setVodHttpsPort(vodMap.get(vodDomainWithPort).getHttpsPort());
        } else {
            String[] ports = vod == null ? null : vod.getPort().split(":");
            newData.setVodHttpPort(ports == null ? null : ports[0]);
            newData.setVodHttpsPort(ports == null ? null : ports[1]);
        }
        if (webrtcMap.containsKey(webrtcDomainWithPort)) {
            newData.setWebrtcHttpPort(webrtcMap.get(webrtcDomainWithPort).getHttpPort());
            newData.setWebrtcHttpsPort(webrtcMap.get(webrtcDomainWithPort).getHttpsPort());
        } else {
            String[] ports = webrtc == null ? null : webrtc.getPort().split(":");
            newData.setWebrtcHttpPort(ports == null ? null : ports[0]);
            newData.setWebrtcHttpsPort(ports == null ? null : ports[1]);
        }
        //如果是IPV6地址，加上[]，直接使用最简单的判断方式，判断是否有超过1个冒号
        if (main.getDomain().split(":").length > 1 && !main.getDomain().startsWith("[")) {
            main.setDomain("[" + main.getDomain() + "]");
        }
        newData.setMainHttp((encrypt ? "https" : "http") + "://" + main.getDomain() + ":" + (encrypt ? newData.getMainHttpsPort() : newData.getMainHttpPort()));
        newData.setMainWs((encrypt ? "wss" : "ws") + "://" + main.getDomain() + ":" + main.getPort());
        if (webrtc != null && webrtc.getDomain() != null) {
            //如果是IPV6地址，加上[]，直接使用最简单的判断方式，判断是否有超过1个冒号
            if (webrtc.getDomain().split(":").length > 1 && !webrtc.getDomain().startsWith("[")) {
                webrtc.setDomain("[" + webrtc.getDomain() + "]");
            }
            newData.setWebrtc((encrypt ? "https" : "http") + "://" + webrtc.getDomain() + ":" + (encrypt ? newData.getWebrtcHttpsPort() : newData.getWebrtcHttpPort()));
        }
        if (vod != null && vod.getDomain() != null) {
            //如果是IPV6地址，加上[]，直接使用最简单的判断方式，判断是否有超过1个冒号
            if (vod.getDomain().split(":").length > 1 && !vod.getDomain().startsWith("[")) {
                vod.setDomain("[" + vod.getDomain() + "]");
            }
            newData.setVod((encrypt ? "https" : "http") + "://" + vod.getDomain() + ":" + (encrypt ? newData.getVodHttpsPort() : newData.getVodHttpPort()));
        }
        datas.add(newData);
        // 更新数据
        if (isDefault) {
            logger.info("update default region config data configmap");
            updateDefaultRegionConfigDataConfigMap(datas);
        } else {
            logger.info("update region config data configmap");
            updateK8sRegionConfigDataConfigmap(datas);
        }
    }

    /**
     * 删除区域接入配置
     *
     * @param ids 接入地址ID，批量删除
     */
    public void deleteConfigByManagerIds(List<String> ids) {
        // 获取所有数据
        List<RegionConfigDataConfigMap> configs = getRegionConfigDataConfigMap();
        if (configs == null) {
            return;
        }
        // 过滤掉要删除的数据
        List<RegionConfigDataConfigMap> collect = configs.stream()
                .filter(data -> !ids.contains(data.getManagerId()))
                .collect(Collectors.toList());
        // 更新数据
        updateK8sRegionConfigDataConfigmap(collect);
    }

    /**
     * 获取区域接入配置
     */
    public List<RegionConfigDataConfigMap> getRegionConfigsByManagerIds(List<String> ids) {
        // 获取所有数据
        List<RegionConfigDataConfigMap> configs = getRegionConfigDataConfigMap();
        if (configs == null) {
            return null;
        }
        return configs.stream()
                .filter(data -> ids.contains(data.getManagerId()))
                .collect(Collectors.toList());
    }

    /**
     * 检查修改的域名是否已存在
     *
     * @param oldManagerId 老域名端口
     * @param main         新域名端口
     */
    public void checkUpdateDomainIsExist(String oldManagerId, RegionConfigReq.DataDTO main) {
        String newId = main.getDomain() + ":" + main.getPort();
        if (oldManagerId.equals(newId)) {
            //没有修改，略过
            return;
        }
        // 获取所有数据
        List<RegionConfigDataConfigMap> datas = getRegionConfigDataConfigMap();
        if (datas == null) {
            return;
        }
        //判断新修改的main地址是否存在，如果存在，也是不能修改的
        // 新增 判断是否已存在,main不允许存在
        boolean exist = datas.stream()
                .anyMatch(data -> !data.getManagerId().equals(oldManagerId) && data.getManagerId().equals(newId));

        if (exist) {
            throw new ServerException(ErrorStatus.REGIONCONFIG_DATA_EXISTED);
        }
    }

    /**
     * 更新区域接入配置
     */
    public void update(String oldManagerId, RegionConfigReq.DataDTO main, RegionConfigReq.DataDTO webrtc, RegionConfigReq.DataDTO vod) {
        // 获取所有数据
        List<RegionConfigDataConfigMap> datas = getRegionConfigDataConfigMap();
        if (datas == null) {
            datas = new ArrayList<>();
        }
        //删除老数据
        datas = datas.stream()
                .filter(data -> !data.getManagerId().equals(oldManagerId))
                .collect(Collectors.toList());
        addNewRegionConfigData(main, webrtc, vod, datas, MultiRegionService.DEFAULT.equals(oldManagerId));
    }

    /**
     * 获取VOD接入配置
     */
    public List<RegionConfigTypeResp.DataDTO> getVodList(boolean checkedUsed) {
        Set<String> used = getUsedIpPort("vod");

        List<RegionConfigTypeResp.DataDTO> res = new ArrayList<>();
        // 获取所有webrtc节点
        List<Pod> pods = getPodsByLables(new String[]{Labels.vod_proxy.label(),
                Labels.vod_proxy_x86.label(),
                Labels.vod_proxy_arm.label()});

        if (!CollectionUtils.isEmpty(pods)) {
            Map<String, String> vodProxyMap = k8sService.getConfigmap("all-vod-proxy");
            pods.forEach(pod -> {
                //通过pod名称获取域名
                String name = pod.getNodeName();
                // 获取域名
                String nodeDomainKey = name + NetworkConstants.SUFFIX_DOMAIN;
                String domain = vodProxyMap.get(nodeDomainKey);
                if (StringUtils.isNotBlank(domain)) {
                    RegionConfigTypeResp.DataDTO dataDTO = new RegionConfigTypeResp.DataDTO();
                    dataDTO.setDomain(domain);
                    // 获取端口
                    String httpPort = vodProxyMap.get(name + "-VOD-NGINX-PORT");
                    String httpsPort = vodProxyMap.get(name + "-VOD-NGINX-SSL-PORT");
                    dataDTO.setPort(httpPort + ":" + httpsPort);
                    dataDTO.setHttpPort(httpPort);
                    dataDTO.setHttpsPort(httpsPort);
                    if (checkedUsed) {
                        if (!used.contains(domain + ":" + dataDTO.getPort())) {
                            res.add(dataDTO);
                        }
                    } else {
                        res.add(dataDTO);
                    }
                }
            });
        }
        //排序
        return sort(res);
    }

    /**
     * 检测默认数据是否发生变更
     */
    public void checkDefaultRegionConfig() {
        refreshDefaultConfig();
    }

    /**
     * 调整schema时需要刷新数据
     */
    public void refreshSchema() {
        //区域中的id需要处理端口，处理之后更新到接入地址中
        List<RegionDataConfigMap> regionDataConfigMap = multiRegionService.getRegionDataConfigMap();
        if (regionDataConfigMap == null || regionDataConfigMap.isEmpty()) {
            return;
        }

        List<RegionConfigDataConfigMap> datas = getRegionConfigDataConfigMap();
        if (datas == null || datas.isEmpty()) {
            return;
        }

        logger.info("[multi region] refreshSchema starting...");

        //处理协议
        String from, to, from2, to2, port1, port2;
        if (isEncrypt()) {
            from = "http://";
            to = "https://";
            from2 = "ws://";
            to2 = "wss://";
            port1 = ":80$";
            port2 = ":443";
        } else {
            from = "https://";
            to = "http://";
            from2 = "wss://";
            to2 = "ws://";
            port1 = ":443$";
            port2 = ":80";
        }

        //做一个特殊处理, DEFAULT中的端口号如果被隐藏，不需要处理
        Optional<RegionConfigDataConfigMap> aDefault = datas.stream()
                .filter(x -> x.getId().equals(MultiRegionService.DEFAULT))
                .findFirst();
        if (aDefault.isPresent()) {
            RegionConfigDataConfigMap defaultData = aDefault.get();
            //替换协议和端口
            defaultData.setMainHttp(defaultData.getMainHttp().replaceAll(from, to));
            if (!defaultData.isHiddenHttpPort()) {
                defaultData.setMainHttp(defaultData.getMainHttp().replaceAll(port1, port2));
            }
            defaultData.setMainWs(defaultData.getMainWs().replaceAll(from2, to2));
            if (!defaultData.isHiddenWsPort()) {
                defaultData.setMainWs(defaultData.getMainWs().replaceAll(port1, port2));
            }
            if (StringUtils.isNotBlank(defaultData.getWebrtc())) {
                defaultData.setWebrtc(defaultData.getWebrtc().replaceAll(from, to));
                if (!defaultData.isHiddenWebrtcPort()) {
                    defaultData.setWebrtc(defaultData.getWebrtc().replaceAll(port1, port2));
                }
            }
            if (StringUtils.isNotBlank(defaultData.getVod())) {
                defaultData.setVod(defaultData.getVod().replaceAll(from, to));
                if (!defaultData.isHiddenVodPort()) {
                    defaultData.setVod(defaultData.getVod().replaceAll(port1, port2));
                }
            }
        }

        HashMap<String, String> idReplace = new HashMap<>();
        //非默认，直接处理
        datas.stream().filter(x -> !x.getId().equals(MultiRegionService.DEFAULT))
                .forEach(data -> {
                    String id = data.getId();
                    String s = id.replaceAll(port1, port2);
                    if (!id.equals(s)) {
                        //端口有变化，记录一下，替换
                        data.setId(s);
                        //managerId不需要改
                        idReplace.put(id, s);
                    }
                    data.setMainHttp(data.getMainHttp().replaceAll(from, to).replaceAll(port1, port2));
                    data.setMainWs(data.getMainWs().replaceAll(from2, to2).replaceAll(port1, port2));
                    if (StringUtils.isNotBlank(data.getWebrtc())) {
                        data.setWebrtc(data.getWebrtc().replaceAll(from, to).replaceAll(port1, port2));
                    }
                    if (StringUtils.isNotBlank(data.getVod())) {
                        data.setVod(data.getVod().replaceAll(from, to).replaceAll(port1, port2));
                    }
                });

        updateK8sRegionConfigDataConfigmap(datas);

        //更新Id
        regionDataConfigMap.forEach(region -> {
            List<RegionDataConfigMap.DataDTO> data = region.getData();
            if (data != null) {
                data.forEach(dataDTO -> {
                    String id = dataDTO.getAddress();
                    if (idReplace.containsKey(id)) {
                        dataDTO.setAddress(idReplace.get(id));
                    }
                });
            }
        });
        multiRegionService.updateRegionDataConfigMap(regionDataConfigMap);
    }

    /**
     * 更新webrtc数据
     */
    public void refreshWebrtcData(String webrtc, String webrtcNginxSslPort, String webrtcNginxPort) {
        List<RegionConfigDataConfigMap> datas = getRegionConfigDataConfigMap();
        if (datas == null) {
            //还没进入过页面，不处理刷新
            return;
        }
        Optional<RegionConfigDataConfigMap> defaultData = datas.stream()
                .filter(data -> data.getId().equals(MultiRegionService.DEFAULT)).findFirst();

        defaultData.ifPresent(regionConfigDataConfigMap -> {
            String old = regionConfigDataConfigMap.getWebrtc();
            //为空才放进去
            if (StringUtils.isBlank(old)) {
                if (isEncrypt()) {
                    regionConfigDataConfigMap.setWebrtc("https://" + webrtc + ":" + (StringUtils.isBlank(webrtcNginxSslPort) ? "443" : webrtcNginxSslPort));
                } else {
                    regionConfigDataConfigMap.setWebrtc("http://" + webrtc + ":" + (StringUtils.isBlank(webrtcNginxPort) ? "80" : webrtcNginxPort));
                }
                //去除默认端口号并记录
                String mainWebrtc = removePort(regionConfigDataConfigMap.getWebrtc());
                if (mainWebrtc != null) {
                    regionConfigDataConfigMap.setHiddenWebrtcPort(!mainWebrtc.equals(regionConfigDataConfigMap.getWebrtc()));
                }
                regionConfigDataConfigMap.setWebrtc(mainWebrtc);
                updateK8sRegionConfigDataConfigmap(datas);
            }
        });
    }
}
