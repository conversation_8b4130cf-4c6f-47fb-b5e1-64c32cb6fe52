package com.xylink.manager.service.basic;

import com.xylink.config.Constants;
import com.xylink.manager.controller.basic.ClientAgreementController;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.service.base.IDeployService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class ClientAgreementService {

    private static final String DEFAULT_TEMPLATE_HTML = "<!doctype html><html lang=\"en\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"><meta name=\"viewport\" content=\"width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0\"><meta http-equiv=\"X-UA-Compatible\" content=\"ie=edge\"><title>{title}</title><style> body {            font-size: 14px;  font-family: simsun;   }  @media (prefers-color-scheme: dark) {  body {   background: #161616; color: rgba(255, 255, 255, 0.9);} }</style><script> function NAPIControl() {  return \"success\";  }</script></head><body><h1>{title}</h1><div>{content}</div></body></html>";

    @Autowired
    private IDeployService deployService;

    public void addPrivacy(ClientAgreementController.AgreementDto agreementDto) {
        add(agreementDto, "privacy-agreement.html");
    }

    public void addPrivacyEN(ClientAgreementController.AgreementDto agreementDto) {
        add(agreementDto, "privacy-agreement-en.html");
    }

    public void addService(ClientAgreementController.AgreementDto agreementDto) {
        add(agreementDto, "xylink-agreement.html");
    }

    public void addServiceEN(ClientAgreementController.AgreementDto agreementDto) {
        add(agreementDto, "xylink-agreement-en.html");
    }

    public ClientAgreementController.AgreementDto getPrivacy() {
        return get("privacy-agreement.html");
    }

    public ClientAgreementController.AgreementDto getPrivacyEN() {
        return get("privacy-agreement-en.html");
    }

    public ClientAgreementController.AgreementDto getService() {
        return get("xylink-agreement.html");
    }

    public ClientAgreementController.AgreementDto getServiceEN() {
        return get("xylink-agreement-en.html");
    }

    private ClientAgreementController.AgreementDto get(String key) {
        Map<String, String> data = getClientAgreementMap();
        String html = data.get(key);

        ClientAgreementController.AgreementDto agreementDto = new ClientAgreementController.AgreementDto();
        if (StringUtils.isNotBlank(html)) {
            agreementDto.setContent(html.substring(html.indexOf("<div>") + 5, html.indexOf("</div>")));
            agreementDto.setTitle(html.substring(html.indexOf("<h1>") + 4, html.indexOf("</h1>")));
        }
        return agreementDto;
    }

    private void add(ClientAgreementController.AgreementDto agreementDto, String key) {
        String htmlContent;
        if (StringUtils.isBlank(agreementDto.getTemplate())) {
            htmlContent = DEFAULT_TEMPLATE_HTML.replaceAll("\\{title}", agreementDto.getTitle());
            htmlContent = htmlContent.replaceFirst("\\{content}", agreementDto.getContent());
        } else {
            htmlContent = agreementDto.getTemplate().replaceAll("\\{title}", agreementDto.getTitle());
            htmlContent = htmlContent.replaceFirst("\\{content}", agreementDto.getContent());
        }

        String finalHtmlContent = htmlContent;
        deployService.patchConfigMap("private-client-agreement", Constants.NAMESPACE_DEFAULT, d -> {
            d.put(key, finalHtmlContent);
        });
    }

    private Map<String, String> getClientAgreementMap() {
        ConfigMap configMap = deployService.getConfigMapByName("private-client-agreement", Constants.NAMESPACE_DEFAULT);
        if (configMap == null) {
            return new HashMap<>(0);
        }
        return configMap.getData();
    }

    public void completeContent(String key, String soureceKey) {
        deployService.patchConfigMap("private-client-agreement", Constants.NAMESPACE_DEFAULT, d -> {
            d.put(key, d.get(soureceKey));
        });
    }
}
