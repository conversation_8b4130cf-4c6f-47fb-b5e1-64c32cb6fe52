package com.xylink.manager.service.basic;

import com.xylink.config.Constants;
import com.xylink.config.ProxyConstants;
import com.xylink.config.exception.basic.ClientErrorException;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServiceErrorException;
import com.xylink.manager.controller.dto.basic.MultiNetDomainConversionDto;
import com.xylink.manager.controller.dto.basic.PodHostInfoDTO;
import com.xylink.manager.service.ServerStatusService;
import com.xylink.manager.service.base.K8sService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023/11/20 14:37
 */
@Service
@Slf4j
public class MultiNetDomainConversionService {
    @Autowired
    private K8sService k8sService;

    @Autowired
    private ServerStatusService serverStatusService;

    public List<MultiNetDomainConversionDto> list() {
        Map<String, String> mainProxyCM = k8sService.getConfigmap(Constants.CONFIGMAP_MAIN_PROXY);
        List<PodHostInfoDTO> mainProxyPodHostInfoList = k8sService.getPodHostInfoList(Constants.POD_NAME_MAIN_PROXY);

        List<MultiNetDomainConversionDto> res = new ArrayList<>();
        for (PodHostInfoDTO temp : mainProxyPodHostInfoList) {
            String ruleKey = temp.getHostname() + ProxyConstants.DOMAIN_CONVERSION_RULER;
            String currentRule = mainProxyCM.get(ruleKey);
            if (StringUtils.isBlank(currentRule)) {
                continue;
            }

            String[] split = currentRule.split("@");

            Arrays.stream(split).forEach(x -> {
                String[] tempRule = x.split(",");
                MultiNetDomainConversionDto conversionDto = new MultiNetDomainConversionDto();
                conversionDto.setPod(temp.getPod());
                conversionDto.setHostname(temp.getHostname());
                conversionDto.setServerIp(temp.getServerIp());
                conversionDto.setSourceIp(tempRule[0]);
                conversionDto.setTargetIp(tempRule[1]);
                res.add(conversionDto);
            });
        }
        return res;
    }

    public List<PodHostInfoDTO> getProxyList() {
        return k8sService.getPodHostInfoList(Constants.POD_NAME_MAIN_PROXY);
    }

    public void add(MultiNetDomainConversionDto conversionDto) {
        Map<String, String> mainProxyCM = k8sService.getConfigmap(Constants.CONFIGMAP_MAIN_PROXY);
        String ruleKey = conversionDto.getHostname() + ProxyConstants.DOMAIN_CONVERSION_RULER;
        String currentRule = mainProxyCM.get(ruleKey);
        String resRule;
        if (StringUtils.isBlank(currentRule)) {
            resRule = conversionDto.getSourceIp() + "," + conversionDto.getTargetIp();
        } else {
            String[] split = currentRule.split("@");
            List<String> ruleListBySourceIp = Arrays.stream(split).filter(x -> x.startsWith(conversionDto.getSourceIp())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ruleListBySourceIp)) {
                throw new ClientErrorException(ErrorStatus.DATA_EXISTED);
            }
            resRule = currentRule + "@" + conversionDto.getSourceIp() + "," + conversionDto.getTargetIp();
        }
        HashMap<String, String> map = new HashMap<>();
        map.put(ruleKey, resRule);
        k8sService.patchConfigMap(Constants.CONFIGMAP_MAIN_PROXY, map);
    }

    public void update(MultiNetDomainConversionDto conversionDto) {
        List<String> ruleListWithoutSourceIp = getRuleListWithoutDtoRule(conversionDto);
        ruleListWithoutSourceIp.add(conversionDto.getSourceIp() + "," + conversionDto.getTargetIp());
        String resRule = StringUtils.join(ruleListWithoutSourceIp, "@");

        HashMap<String, String> map = new HashMap<>();
        map.put(conversionDto.getHostname() + ProxyConstants.DOMAIN_CONVERSION_RULER, resRule);
        k8sService.patchConfigMap(Constants.CONFIGMAP_MAIN_PROXY, map);
    }

    /**
     * 删除当前域名转换规则
     * @param conversionDto
     */
    public void delete(MultiNetDomainConversionDto conversionDto) {
        List<String> ruleListWithoutSourceIp = getRuleListWithoutDtoRule(conversionDto);
        if (CollectionUtils.isEmpty(ruleListWithoutSourceIp)) {
            k8sService.removeDataFromCM(Constants.CONFIGMAP_MAIN_PROXY, conversionDto.getHostname() + ProxyConstants.DOMAIN_CONVERSION_RULER);
            List<PodHostInfoDTO> podHostInfoList = k8sService.getPodHostInfoList(Constants.POD_NAME_MAIN_PROXY);
            String podName = podHostInfoList.stream().filter(x -> x.getHostname().equals(conversionDto.getHostname())).map(PodHostInfoDTO::getPod).findFirst().orElseThrow(() -> new ServiceErrorException(ErrorStatus.UNEXPECTED_ERROR));
            serverStatusService.restartPod(podName, "name");
        } else {
            String resRule = StringUtils.join(ruleListWithoutSourceIp, "@");
            HashMap<String, String> map = new HashMap<>();
            map.put(conversionDto.getHostname() + ProxyConstants.DOMAIN_CONVERSION_RULER, resRule);
            k8sService.patchConfigMap(Constants.CONFIGMAP_MAIN_PROXY, map);
        }
    }

    /**
     * 获取当前main-proxy的域名转换规则列表（排除当前conversionDto的域名规则）
     * 未找到当前main-proxy的currentRule,直接抛出异常ErrorStatus.DATA_NOT_EXIST
     * @param conversionDto
     * @return
     */
    private List<String> getRuleListWithoutDtoRule(MultiNetDomainConversionDto conversionDto) {
        Map<String, String> mainProxyCM = k8sService.getConfigmap(Constants.CONFIGMAP_MAIN_PROXY);
        String ruleKey = conversionDto.getHostname() + ProxyConstants.DOMAIN_CONVERSION_RULER;
        String currentRule = mainProxyCM.get(ruleKey);

        if (StringUtils.isBlank(currentRule)) {
            throw new ClientErrorException(ErrorStatus.DATA_NOT_EXIST);
        }
        String[] split = currentRule.split("@");
        return Arrays.stream(split).filter(x -> !x.startsWith(conversionDto.getSourceIp())).collect(Collectors.toList());
    }
}
