package com.xylink.manager.service;


import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.StatisItem;
import com.xylink.manager.controller.dto.HybridStaticDto;
import com.xylink.manager.controller.dto.NodeMemoryDTO;
import com.xylink.manager.controller.dto.moitor.PodMonitor;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.manager.service.base.SystemModeConfig;
import com.xylink.manager.service.influxdb.AlertInfoInfluxdbDao;
import org.apache.commons.lang3.StringUtils;
import org.influxdb.BatchOptions;
import org.influxdb.InfluxDB;
import org.influxdb.InfluxDBFactory;
import org.influxdb.dto.Point;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Created by haijiao on 2017/11/11.
 */
@Service
@DependsOn("systemModeConfig")
public class InfluxDBService {
    private static final Logger logger = LoggerFactory.getLogger(InfluxDBService.class);

    @Value("${influxdb.url}")
    private String dbUrl;
    @Value("${influxdb.name:k8s}")
    private String dbName;
    @Value("${influxdb.userName:root}")
    private String dbUserName;
    @Value("${influxdb.passWord:root}")
    private String dbPassWord;

    /**
     * InfluxDB客户端是线程安全的,官方建议每个应用程序只有一个实例并在可能的情况下重用它
     */
    private InfluxDB influxDB;


    private static final String query_max_value_prefix = "select max(\"value\") from \"";
    private static final String query_condition_nodename = "\" where nodename='";
    private static final String query_condition_last30minute_groupby1minute = "' and time > now() - 30m group by time(1m)";
    private static final String query_condition_last180minute_groupby1minute = "' and time > now() - 180m group by time(1m)";
    private static final String query_condition_last1day_groupby10minute = "' and time > now() - 24h group by time(10m)";
    private static final String query_condition_last3day_groupby1hour = "' and time > now() - 72h group by time(1h)";
    private static final String query_condition_last14day_groupby1day = "' and time > now() - 30d group by time(1d)";
    private static final String query_condition_last2minute = "' and time > now() - 2m";

    private static final String query_condition_time_tpl = "' and time > now() - {PERIOD} group by time({GROUP})";

    private static final String query_pod_time_tpl = "select max(\"value\") from \"{MEASUREMENT}\" where pod_name='{POD}' and  type='pod' and time > now() - {PERIOD} group by time({GROUP})";
    private static final String query_pod_1m_tpl = "select value from \"{MEASUREMENT}\" where pod_name='{POD}' and  type='pod' and time > now() - {PERIOD} ";
    private static final String query_pod_by_node_tpl = "select pod_name, value  from \"{MEASUREMENT}\" where nodename='{NODENAME}' and  type='pod'  and time > now() - 2m";

    private static final String cpu_capacity = "cpu/node_capacity";
    private static final String memory_capacity = "memory/node_capacity";
    private static final String filesystem_limit = "filesystem/limit";

    private static final String cpu_usage = "cpu/usage_rate";
    private static final String memory_usage = "memory/rss";
    private static final String network_rx = "network/rx_rate";
    private static final String network_tx = "network/tx_rate";
    private static final String filesystem_usage = "filesystem/usage";

    private SimpleDateFormat sdf_Hm = new SimpleDateFormat("HH:mm");
    private SimpleDateFormat sdf_Md = new SimpleDateFormat("MM.dd");
    private SimpleDateFormat sdf_MdHm = new SimpleDateFormat("MM.dd HH:mm");

    @Autowired
    private IDeployService deployService;

    @PostConstruct
    public void init() {
        if (SystemModeConfig.isCmsOrXms()) {
            logger.info("SystemModeConfig is cms, so not init influxdb");
            return;
        }
        //不调用集成的方法，为了作 为空判断
        ConfigMap configMap = deployService.getConfigMapByName(Constants.CONFIGMAP_ALLIP, Constants.NAMESPACE_DEFAULT);
        if (configMap != null) {
            String n9eSwitch = configMap.getData().getOrDefault(NetworkConstants.N9E_SWITCH, "false");
            //influxdb与夜莺互斥
            if (StringUtils.equals(n9eSwitch, "true")) {
                logger.info("n9e enabled, disable influxdb");
                return;
            }
        }
        influxDB = InfluxDBFactory.connect(dbUrl, dbUserName, dbPassWord);

        //启动批量插入
        influxDB.enableBatch(BatchOptions.DEFAULTS.exceptionHandler((failedPoints, throwable) -> {
            List<Point> list = new ArrayList<>();
            failedPoints.forEach(list::add);
            logger.error("insert influxdb failed,failedPoints:{}", list, throwable);
        }));

        AlertInfoInfluxdbDao.checkDatabase(influxDB);
    }

    public InfluxDB getInfluxDB() {
        return influxDB;
    }

    public static void checkDatabase(InfluxDB influxDB, String database, String retentionPolicyName, String duration, boolean isDefault) {
        if (StringUtils.isBlank(database)) {
            logger.warn("database don't blank !!!");
            return;
        }

        //没有数据库需要创建数据库，并创建默认的保留策略
        try {
            if (!influxDB.databaseExists(database) && StringUtils.isNoneBlank(retentionPolicyName, duration)) {
                influxDB.createDatabase(database);
                influxDB.createRetentionPolicy(retentionPolicyName, database, duration, 1, isDefault);
            }
        } catch (Exception e) {
            logger.error("check influxdb database failed!!!,", e);
        }
    }

    private QueryResult query(String command) {
        logger.info(command);
        return influxDB.query(new Query(command, dbName), TimeUnit.MICROSECONDS);
    }


    /**
     * get the last 30 minutes usage that the max value for every minute,
     *
     * @param item
     * @param nodeOrPod
     * @return
     */
    private String generateUsageRequestUrl(StatisItem item, String nodeOrPod, String period, String type) {
        String table = null;
        switch (item) {
            case NODE_CPU:
                table = cpu_usage;
                break;
            case NODE_MEMORY:
                table = memory_usage;
                break;
            case NODE_NETWORK_RX:
                table = network_rx;
                break;
            case NODE_NETWORK_TX:
                table = network_tx;
                break;
            case NODE_FILESYSTEM:
                table = filesystem_usage;
                break;
            case POD_CPU:
                table = cpu_usage;
                break;
            case POD_MEMORY:
                table = memory_usage;
                break;
            default:
                logger.warn("Mismatch item: " + item.toString());
                return null;

        }


        String query_condition_time;
        String group;
        if (period.endsWith("m")) {
            group = "1m";
        } else if (period.endsWith("h")) {
            group = "1h";
            int num = Integer.parseInt(period.replaceAll("h", ""));
            if (num <= 7) {
                period = num * 60 + "m";
                group = "1m";
            }
        } else if (period.endsWith("d")) {
            group = "1d";
            int num = Integer.parseInt(period.replaceAll("d", ""));
            if (num <= 7) {
                period = num * 24 + "h";
                group = "1h";
            }
        } else {
            period = "30m";
            group = "1m";
        }

        String query = null;
        if ("NODE".equals(type)) {
            query_condition_time = query_condition_time_tpl.replace("{PERIOD}", period).replace("{GROUP}", group);
            query = query_max_value_prefix + table + query_condition_nodename + nodeOrPod + query_condition_time;
        } else if ("POD".equals(type)) {
            query = "1m".equals(group) ? query_pod_1m_tpl : query_pod_time_tpl;
            query = query
                    .replace("{MEASUREMENT}", table)
                    .replace("{POD}", nodeOrPod)
                    .replace("{PERIOD}", period)
                    .replace("{GROUP}", group);
        }

        return query;
    }

    /**
     * get the max value for the last 2 minutes. In case last 1 minute no value, so we select 2 minutes
     *
     * @param item
     * @param nodeName
     * @return
     */
    private String generateCapacityRequestUrl(StatisItem item, String nodeName) {
        String table = null;
        switch (item) {
            case NODE_CPU:
                table = cpu_capacity;
                break;
            case NODE_MEMORY:
                table = memory_capacity;
                break;
            case NODE_FILESYSTEM:
                table = filesystem_limit;
                break;
            default:
//                logger.warn("Mismatch item: " + item.toString());
                return null;

        }

        String query = query_max_value_prefix + table + query_condition_nodename + nodeName + query_condition_last2minute;
        return query;
    }


    public Long getValueByTableAndNode(StatisItem item, String nodeName) {
        String request = generateCapacityRequestUrl(item, nodeName);
        if (StringUtils.isNotEmpty(request)) {
            String value = null;
            QueryResult queryResult = query(request);
            List<QueryResult.Result> results = queryResult.getResults();
            if (!CollectionUtils.isEmpty(results)) {
                QueryResult.Result result = results.get(0);
                //logger.info("getValueByTableAndNode - item: " + item.toString() + ", nodeName: " + nodeName + ", result: " + result.toString());
                List<QueryResult.Series> seriesList = result.getSeries();
                if (!CollectionUtils.isEmpty(seriesList)) {
                    QueryResult.Series series = seriesList.get(0);
                    List<List<Object>> valuesList = series.getValues();
                    if (!CollectionUtils.isEmpty(valuesList)) {
                        List<Object> valueList = valuesList.get(0);
                        if (!CollectionUtils.isEmpty(valueList) && valueList.size() == 2) {
                            Double dValue = (Double) valueList.get(1);
                            if (null != dValue) {
                                //logger.debug("getValueByTableAndNode - item: " + item.toString() + ", nodeName: " + nodeName + ", capacity: " + dValue);
                                return dValue.longValue();
                            }
                        }
                    }
                }
            }

            if (StringUtils.isNotEmpty(value)) {
                //logger.debug("getValueByTableAndNode - item: " + item.toString() + ", nodeName: " + nodeName + ", capacity: " + value);
                return Long.parseLong(value);
            }
        }
        //logger.debug("getValueByTableAndNode - item: " + item.toString() + ", nodeName: " + nodeName + ", capacity is null.");

        return null;
    }

    /**
     * 获取所有node节点的统计信息
     */
    public Map<String, HybridStaticDto> getLatestStaticsByType(StatisItem item) {
        long begin = new Date().getTime();
        Map<String, HybridStaticDto> data = new HashMap<>();
        //获取所有node统计信息对应的容量
        String capacityQuery = null;
        String statisQuery;
        switch (item) {
            case NODE_CPU:
                capacityQuery = "select max(\"value\"), \"nodename\" from \"cpu/node_capacity\" where time > now() - 2m group by nodename";
                statisQuery = "select value, nodename from \"cpu/usage_rate\" where time > now() - 2m and type='node' ";
                break;
            case NODE_MEMORY:
                capacityQuery = "select max(\"value\"), \"nodename\" from \"memory/node_capacity\" where time > now() - 2m group by nodename";
                statisQuery = "select value, nodename from \"memory/rss\" where time > now() - 2m and type='node'";
                break;
            case NODE_FILESYSTEM:
                capacityQuery = "select max(\"value\"), \"nodename\" from \"filesystem/limit\" where time > now() - 2m group by nodename";
                statisQuery = "select value, nodename from \"filesystem/usage\" where time > now() - 2m and type='node'";
                break;
            case NODE_NETWORK_RX:
                statisQuery = "select value, nodename from \"network/rx_rate\" where time > now() - 2m and type='node'";
                break;
            case NODE_NETWORK_TX:
                statisQuery = "select value, nodename from \"network/tx_rate\" where time > now() - 2m and type='node'";
                break;
            default:
                capacityQuery = null;
                statisQuery = null;
                break;
        }
        Map<String, Double> nodeCapacityMap = new HashMap<>();
        List<QueryResult.Result> results;
        if (StringUtils.isNotBlank(capacityQuery)) {
            results = query(capacityQuery).getResults();
            if (!CollectionUtils.isEmpty(results) && !CollectionUtils.isEmpty(results.get(0).getSeries())) {
                List<QueryResult.Series> series = results.get(0).getSeries();

                series.stream().filter(s -> !CollectionUtils.isEmpty(s.getValues()) && !CollectionUtils.isEmpty(s.getValues().get(0)) && s.getValues().get(0).size() == 3)
                        .forEach(s -> nodeCapacityMap.put(s.getValues().get(0).get(2).toString(), (Double) (s.getValues().get(0).get(1))));
            }
        }


        //获取所有node最近2分钟的统计信息
        results = query(statisQuery).getResults();
        Map<String, List<Object>> nodeMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(results) && !CollectionUtils.isEmpty(results.get(0).getSeries())) {
            List<QueryResult.Series> nodeSeries = results.get(0).getSeries();

            nodeSeries.get(0).getValues().stream().filter(s -> !CollectionUtils.isEmpty(s) && s.size() == 3)
                    .forEach(s -> {
                        String nodeName = s.get(2).toString();
                        if (!nodeMap.containsKey(nodeName)) {
                            nodeMap.put(nodeName, s);
                        } else {
                            if (((Double) (s.get(0))).doubleValue() > ((Double) (nodeMap.get(nodeName).get(0))).doubleValue()) {
                                nodeMap.put(nodeName, s);
                            }
                        }
                    });
        }
        nodeMap.values().stream().forEach(node -> {
            String nodeName = node.get(2).toString();
            HybridStaticDto statisDto = new HybridStaticDto();
            List<String> timestamps = new ArrayList<>();
            List<Float> values = new ArrayList<>();

            Double usage = (Double) node.get(1);
            Date time = new Date(Math.round((Double) node.get(0)) / 1000);

            if (item == StatisItem.NODE_CPU || item == StatisItem.NODE_MEMORY) {
                if (null != nodeCapacityMap.get(nodeName) && 0 != nodeCapacityMap.get(nodeName)) {
                    Float fusage = 100 * usage.floatValue() / nodeCapacityMap.get(nodeName).floatValue();
                    values.add(((float) Math.round(fusage * 100)) / 100);
                    timestamps.add(sdf_Hm.format(time));
                }
            } else {
                values.add(usage.floatValue());
                timestamps.add(sdf_Hm.format(time));
            }

            statisDto.setSeries(values);
            statisDto.setxAxis(timestamps);
            data.put(nodeName, statisDto);


        });

        logger.info("query all node " + item.name() + ": " + (new Date().getTime() - begin));
        return data;
    }


    /**
     * 获取单个node节点的统计信息
     */
    public HybridStaticDto getStaticsByTypeAndNode(StatisItem item, String nodeName, String period) {
        HybridStaticDto statisDto = new HybridStaticDto();
        List<String> timestamps = new ArrayList<>();
        List<Float> values = new ArrayList<>();
        Long capacity = getValueByTableAndNode(item, nodeName);

        String request = generateUsageRequestUrl(item, nodeName, period, "NODE");
        QueryResult queryResult = query(request);
        List<QueryResult.Result> results = queryResult.getResults();
        if (!CollectionUtils.isEmpty(results)) {
            QueryResult.Result result = results.get(0);
            List<QueryResult.Series> seriesList = result.getSeries();
            if (!CollectionUtils.isEmpty(seriesList)) {
                QueryResult.Series series = seriesList.get(0);
                List<List<Object>> valuesList = series.getValues();
                if (!CollectionUtils.isEmpty(valuesList)) {
                    SimpleDateFormat format = sdf_Hm;
                    if (period.endsWith("d") && Integer.parseInt(period.replaceAll("d", "")) <= 7) format = sdf_MdHm;
                    else if (period.endsWith("d") && Integer.parseInt(period.replaceAll("d", "")) > 7) format = sdf_Md;

                    for (List<Object> valueList : valuesList) {
                        if (!CollectionUtils.isEmpty(valueList) && valueList.size() == 2) {
                            Double timestamp = (Double) valueList.get(0);
                            Double usage = (Double) valueList.get(1);

                            if (timestamp == null || usage == null) {
                                continue;
                            }

                            Long lTimestamp = Math.round(timestamp) / 1000;
                            Date time = new Date(lTimestamp);

                            if (item == StatisItem.NODE_CPU || item == StatisItem.NODE_MEMORY) {
                                if (null != capacity && 0 != capacity) {
                                    Float fusage = 100 * usage.floatValue() / capacity;
                                    values.add(((float) Math.round(fusage * 100)) / 100);
                                    timestamps.add(format.format(time));
                                }
                            } else {
                                values.add(usage.floatValue());
                                timestamps.add(format.format(time));
                            }
                        }
                    }
                }
            }
        }

        statisDto.setSeries(values);
        statisDto.setxAxis(timestamps);
        statisDto.setCapability(capacity);

        return statisDto;
    }

    public HybridStaticDto getStaticsByTypeAndPod(StatisItem item, String podName, String nodeName, String period) {

        HybridStaticDto statisDto = new HybridStaticDto();
        List<String> timestamps = new ArrayList<>();
        List<Float> values = new ArrayList<>();
        Long capacity = null;

        //查询pod所在node最大内存/cpu
        switch (item) {
            case POD_CPU:
                capacity = getValueByTableAndNode(StatisItem.NODE_CPU, nodeName);
                break;
            case POD_MEMORY:
                capacity = getValueByTableAndNode(StatisItem.NODE_MEMORY, nodeName);
                break;
            default:
                return null;
        }

        SimpleDateFormat format = sdf_Hm;
        if (period.endsWith("d") && Integer.parseInt(period.replaceAll("d", "")) <= 7) format = sdf_MdHm;
        else if (period.endsWith("d") && Integer.parseInt(period.replaceAll("d", "")) > 7) format = sdf_Md;


        // 根据pod查询influxDb中的监控数据
        // 当前pod没有设置内存 cpu限制，以node的内存和cpu为基数计算
        String request = generateUsageRequestUrl(item, podName, period, "POD");
        QueryResult queryResult = query(request);
        List<List<Object>> data = new ArrayList<>();

        try {
            data = queryResult.getResults().get(0).getSeries().get(0).getValues();
        } catch (Exception e) {
            logger.error("pod monitor info error, query: " + request, e);
        }
        SimpleDateFormat finalFormat = format;
        Long finalCapacity = capacity;
        data.stream().forEachOrdered(v -> {
            if (v != null && v.size() == 2) {
                timestamps.add(finalFormat.format(new Date(Math.round((Double) v.get(0)) / 1000)));

                Float fusage = v.get(1) == null ? 0 : 100 * ((Double) v.get(1)).floatValue() / finalCapacity;
                values.add(((float) Math.round(fusage * 100)) / 100);
            }
        });
        statisDto.setSeries(values);
        statisDto.setxAxis(timestamps);
        statisDto.setCapability(capacity);
        return statisDto;
    }

    public NodeMemoryDTO getMemoryNode(List<String> nodeList) {
        double min = -1d;
        String nameName = "";
        Float fusage = 0f;
        String capacityQuery = "select max(\"value\"), \"nodename\" from \"memory/node_capacity\" where time > now() - 2m group by nodename";
        String statisQuery = "select value, nodename from \"memory/rss\" where time > now() - 2m and type='node'";
        Map<String, Double> nodeCapacityMap = new HashMap<>();
        List<QueryResult.Result> results = influxDB.query(new Query(capacityQuery, "k8s"), TimeUnit.MICROSECONDS).getResults();
        if (!CollectionUtils.isEmpty(results) && !CollectionUtils.isEmpty(results.get(0).getSeries())) {
            List<QueryResult.Series> series = results.get(0).getSeries();
            series.stream().filter(s -> !CollectionUtils.isEmpty(s.getValues()) && !CollectionUtils.isEmpty(s.getValues().get(0)) && s.getValues().get(0).size() == 3 && nodeList.contains(s.getValues().get(0).get(2).toString()))
                    .forEach(s -> nodeCapacityMap.put(s.getValues().get(0).get(2).toString(), (Double) (s.getValues().get(0).get(1))));
        }
        //获取所有node最近2分钟的统计信息
        results = influxDB.query(new Query(statisQuery, "k8s"), TimeUnit.MICROSECONDS).getResults();
        if (!CollectionUtils.isEmpty(results)
                && !CollectionUtils.isEmpty(results.get(0).getSeries())
                && null != results.get(0).getSeries().get(0)
                && !CollectionUtils.isEmpty(results.get(0).getSeries().get(0).getValues())) {
            for (List<Object> value : results.get(0).getSeries().get(0).getValues()) {
                if (value.size() != 3 || !nodeList.contains(value.get(2).toString())) {
                    continue;
                }
                Double maxValue = nodeCapacityMap.get(value.get(2).toString()) == null ? 0d : nodeCapacityMap.get(value.get(2).toString());
                Double usage = (Double) value.get(1);
                double end = maxValue - usage;
                if (end >= min) {
                    nameName = value.get(2).toString();
                    min = end;
                    fusage = 100 * usage.floatValue() / maxValue.floatValue();
                }
            }
        }
        NodeMemoryDTO nodeMemory = new NodeMemoryDTO();
        nodeMemory.setNameName(nameName);
        nodeMemory.setFusage(fusage);
        return nodeMemory;
    }

    public List<PodMonitor> getPodInfoByNode(StatisItem item, String nodeName) {
        String measurement = null;
        switch (item) {
            case POD_CPU:
                measurement = cpu_usage;
                break;
            case POD_MEMORY:
                measurement = memory_usage;
                break;
            default:
                logger.warn("Mismatch item: " + item.toString());
                return null;

        }
        List<List<Object>> data = new ArrayList<>();
        String query = query_pod_by_node_tpl
                .replace("{MEASUREMENT}", measurement)
                .replace("{NODENAME}", nodeName);
        QueryResult queryResult = query(query);

        try {
            data = queryResult.getResults().get(0).getSeries().get(0).getValues();
        } catch (Exception e) {
            logger.error("pod monitor info error, query: " + query, e);
        }
        Map<String, PodMonitor> map = new HashMap<>();
        data.stream().forEachOrdered(v -> {
            if (v != null && v.size() == 3) {
                String podName = v.get(1).toString();
                PodMonitor pod = new PodMonitor();

                if (map.containsKey(podName)) pod = map.get(podName);

                if (pod.getStartTime() == null || pod.getStartTime() < (Double) v.get(0)) {
                    pod.setName(podName);
                    pod.setNodeName(nodeName);
                    pod.setStartTime(((Double) v.get(0)).longValue());
                    if (item == StatisItem.POD_CPU) pod.setCpuCores((Double) v.get(2));
                    if (item == StatisItem.POD_MEMORY) pod.setMemRss((Double) v.get(2));
                    map.put(podName, pod);
                }
            }
        });
        return new ArrayList<>(map.values());
    }

}
