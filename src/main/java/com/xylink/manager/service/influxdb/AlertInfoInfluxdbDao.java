package com.xylink.manager.service.influxdb;

import com.xylink.manager.controller.dto.alert.AlertConstant;
import com.xylink.manager.controller.dto.alert.AlertEventDto;
import com.xylink.manager.controller.dto.alert.AlertEventTotalDto;
import com.xylink.manager.model.common.Pageable;
import com.xylink.manager.service.InfluxDBService;
import com.xylink.manager.service.nightingale.MonitorN9eService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.influxdb.InfluxDB;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.BoundParameterQuery;
import org.influxdb.dto.Point;
import org.influxdb.impl.InfluxDBMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2023/2/15 10:41 AM
 */
@Service
@Slf4j
public class AlertInfoInfluxdbDao {

    @Autowired
    private InfluxDBService influxDBService;
    @Resource
    private MonitorN9eService monitorN9eService;

    /**
     * 获取某类告警总条数
     */
    public List<AlertEventTotalDto> getTotalByInfluxdb(boolean isRemoveRead, String measurement, List<String> observableNodeList) {
        Instant endTime = Instant.now().minus(7, ChronoUnit.DAYS);
        StringBuilder command = new StringBuilder();
        command.append("SELECT COUNT(detail) as total FROM ").append(measurement).append(" WHERE 1=1");
        if (isRemoveRead) {
            command.append(" AND readed = false");
        }
        command.append(" AND time >= '").append(endTime).append("'");
        if (observableNodeList != null) {
            if (observableNodeList.isEmpty()) {
                return Collections.emptyList();
            } else {
                command.append(" AND (");
                for (int i = 0; i < observableNodeList.size(); i++) {
                    String nodeName = observableNodeList.get(i);
                    command.append("node_name = '").append(nodeName).append("'");
                    if (i < observableNodeList.size() - 1) {
                        command.append(" OR ");
                    }
                }
                command.append(")");
            }
        }
        log.info("getTotalByInfluxdb sql = {}", command.toString());
//        String command = isRemoveRead ? "SELECT COUNT(detail) as total FROM " + measurement + " where readed = false and time >= '" + endTime + "'"
//                : "SELECT COUNT(detail) as total FROM " + measurement + " where time >= '" + endTime + "'";
        return selectByInfluxdb(measurement, command.toString(), AlertEventTotalDto.class);
    }

    /**
     * 获取某类告警列表
     */
    public List<AlertEventDto> getAlertListByInfluxdb(boolean isRemoveRead, String measurement, Pageable pageable, List<String> observableNodeList) {
         return getAlertListByInfluxdb(isRemoveRead, measurement, pageable.getPageSize(), (pageable.getPageNumber() - 1) * pageable.getPageSize(), observableNodeList);
    }

    /**
     * 获取某类告警列表
     */
    public List<AlertEventDto> getAlertListByInfluxdb(boolean isRemoveRead, String measurement, long limit, long offset, List<String> observableNodeList) {
        Instant endTime = Instant.now().minus(7, ChronoUnit.DAYS);
        StringBuilder command = new StringBuilder();
        command.append("SELECT * FROM ").append(measurement).append(" WHERE 1=1");
        if (isRemoveRead) {
            command.append(" AND readed = false");
        }
        command.append(" AND time >= '").append(endTime).append("'");
        if (observableNodeList != null) {
            if (observableNodeList.isEmpty()) {
                return Collections.emptyList();
            } else {
                command.append(" AND (");
                for (int i = 0; i < observableNodeList.size(); i++) {
                    String nodeName = observableNodeList.get(i);
                    command.append("node_name = '").append(nodeName).append("'");
                    if (i < observableNodeList.size() - 1) {
                        command.append(" OR ");
                    }
                }
                command.append(")");
            }
        }
        command.append(" order by time desc limit ").append(limit)
                .append(" OFFSET ").append(offset);
        log.info("getAlertListByInfluxdb sql = {}", command.toString());
//        String command = isRemoveRead ? "SELECT * FROM "+ measurement +" where readed = false and time >= '" + endTime + "' order by time desc limit " + limit
//                + " OFFSET " + offset
//                : "SELECT * FROM "+ measurement +" where time >= '" + endTime + "' order by time desc limit " + limit
//                + " OFFSET " + offset;
        return selectByInfluxdb(measurement, command.toString(), AlertEventDto.class);
    }

    /**
     * 根据条件查询告警列表
     */
    public List<AlertEventDto> getAlertListByInfluxdb(String measurement, String time) {
        String command = "select * from " + measurement + " where time = '" + time + "'";
        return selectByInfluxdb(measurement, command, AlertEventDto.class);
    }

    /**
     * 查询数据
     */
    public <x> List<x> selectByInfluxdb(String measurement, String command, Class<x> xClass) {
        BoundParameterQuery query = BoundParameterQuery.QueryBuilder
                .newQuery(command)
                .forDatabase(AlertConstant.ALERT_DATABASE)
                .create();
        log.info("sql:{}", query.getCommand());
        InfluxDBMapper influxDBMapper = new InfluxDBMapper(influxDBService.getInfluxDB());
        return influxDBMapper.query(query, xClass, measurement);
    }

    /**
     * 批量添加告警信息
     */
    public void insertAlertInfo(Map<String,List<AlertEventDto>> measurementToData) {
        if (CollectionUtils.isEmpty(measurementToData)) {
            return;
        }
        if (monitorN9eService.nightingaleSwitch()){
            return;
        }

        BatchPoints batchPoints = BatchPoints.database(AlertConstant.ALERT_DATABASE).retentionPolicy(AlertConstant.ALERT_RETENTION_POLICY).build();
        for (Map.Entry<String, List<AlertEventDto>> temp : measurementToData.entrySet()) {
            for (AlertEventDto dto : temp.getValue()) {
                Point point = Point.measurement(temp.getKey()).addFieldsFromPOJO(dto).build();
                batchPoints.point(point);
            }
        }

        influxDBService.getInfluxDB().write(batchPoints);
    }

    /**
     * 批量添加告警信息
     */
    public void insertAlertInfo(String measurement, List<AlertEventDto> list) {
        if (StringUtils.isBlank(measurement) || CollectionUtils.isEmpty(list)) {
            return;
        }

        BatchPoints batchPoints = BatchPoints.database(AlertConstant.ALERT_DATABASE).retentionPolicy(AlertConstant.ALERT_RETENTION_POLICY).build();
        for (AlertEventDto dto : list) {
            Point point = Point.measurement(measurement).addFieldsFromPOJO(dto).build();
            batchPoints.point(point);
        }

        influxDBService.getInfluxDB().write(batchPoints);
    }

    /**
     * 添加告警信息
     */
    public void insertAlertInfo(String measurement, AlertEventDto dto) {
        if (StringUtils.isBlank(measurement) || Objects.isNull(dto)) {
            return;
        }

        BatchPoints batchPoints = BatchPoints.database(AlertConstant.ALERT_DATABASE).retentionPolicy(AlertConstant.ALERT_RETENTION_POLICY).build();
        Point point = Point.measurement(measurement).addFieldsFromPOJO(dto).build();
        batchPoints.point(point);

        influxDBService.getInfluxDB().write(batchPoints);
    }

    public static void checkDatabase(InfluxDB influxDB) {
        InfluxDBService.checkDatabase(influxDB, AlertConstant.ALERT_DATABASE, AlertConstant.ALERT_RETENTION_POLICY, "1w", true);
    }
}
