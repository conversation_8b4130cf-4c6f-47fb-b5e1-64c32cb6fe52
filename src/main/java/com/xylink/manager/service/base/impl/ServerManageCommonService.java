package com.xylink.manager.service.base.impl;

import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.controller.dto.FeatureDto;
import com.xylink.manager.controller.dto.servicemanage.*;
import com.xylink.manager.inspection.entity.Constant;
import com.xylink.manager.model.Pair2;
import com.xylink.manager.model.common.Page;
import com.xylink.manager.model.em.DefaultConfigmapDataEnum;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.ServerListService;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.base.PlatformConfig;
import com.xylink.manager.service.base.SystemModeConfig;
import com.xylink.manager.service.cache.bean.ConfigMapCache;
import com.xylink.manager.service.cache.bean.ContainerStatusCache;
import com.xylink.manager.service.cache.bean.OwnerReferenceCache;
import com.xylink.manager.service.cache.bean.PodCache;
import com.xylink.manager.service.cache.service.ICacheService;
import com.xylink.manager.service.servermanage.ServerAdvanceConfigStrategy;
import com.xylink.manager.service.servermanage.ServerAdvanceConfigStrategyContext;
import com.xylink.manager.service.servermanage.ServerAdvanceConfigStrategyFactory;
import com.xylink.util.DateTimeUtil;
import com.xylink.util.Ipv6Util;
import com.xylink.util.LocalCacheUtil;
import com.xylink.util.MemoryPaginationUtil;
import io.fabric8.kubernetes.api.model.*;
import io.fabric8.kubernetes.api.model.apps.DaemonSet;
import io.fabric8.kubernetes.api.model.apps.Deployment;
import io.fabric8.kubernetes.api.model.apps.ReplicaSet;
import io.fabric8.kubernetes.api.model.apps.StatefulSet;
import io.fabric8.kubernetes.client.dsl.PodResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR> create on 2025/5/9
 */
@Service
@Slf4j
public class ServerManageCommonService {


    @Autowired
    private K8sDeployService k8sDeployService;

    @Autowired
    private ServerListService serverListService;

    @Autowired
    private IDeployService deployService;

    @Autowired
    private ServerAdvanceConfigStrategyFactory serverAdvanceConfigStrategyFactory;

    @Autowired
    private ICacheService cacheService;

    @Autowired
    private K8sService k8sService;

    private static final String STATUS_ERROR = "status_error";
    private static final String STATUS_OK = "status_ok";


    /**
     * 需要排除的containerName如 init-private-jar
     */
    private static final List<String> EXCLUDE_CONTAINER_NAME_LIST = Arrays.asList("init-private-jar", "init-chmod-dir", "init-shared", "init-shared-nrestart", "init-shared-static-source", "init-libstdc", "init-change-core-pattern", "init-echo-keepalive", "init-chmod-keepalive", "init-traceid-agent");
    /**
     * 需要特殊处理的服务，比如rmserver
     */
    private static final List<String> SPECIAL_SERVER = Arrays.asList("private-rmserver-lib");

    public Page<ServerInfoVO> buildServerList(List<ServerInfoVO> resultList, ServerListParam param) {
        //设置是否支持部署
        setDeploySupport(resultList);

        String keywords = param.getKeywords();
        String status = param.getStatus();
        String deployType = param.getDeployType();
        String controllerType = param.getControllerType();
        List<ServerInfoVO> records = resultList.stream()
                .filter(x -> StringUtils.isBlank(keywords) || x.getServerName().contains(keywords))
                .filter(x -> StringUtils.isBlank(status) || status.equals(x.getStatus()))
                .filter(x -> StringUtils.isBlank(deployType) || deployType.equals(x.getDeployType()))
                .filter(x -> StringUtils.isBlank(controllerType) || controllerType.equals(x.getControllerType()))
                .collect(Collectors.toList());
        String orderByField = param.getOrderByField();
        if (StringUtils.isBlank(orderByField) || "serverName".equals(orderByField)) {
            records.sort(Comparator.comparing(ServerInfoVO::getServerName, Comparator.nullsLast(String::compareTo)));
        } else {
            records.sort(Comparator.comparing(ServerInfoVO::getReplica, Comparator.nullsLast(Integer::compareTo)));
        }
        if (param.getOrderDesc()) {
            Collections.reverse(records);
        }
        return MemoryPaginationUtil.pagination(records, param.getCurrent(), param.getPageSize());
    }

    private void setDeploySupport(List<ServerInfoVO> resultList) {
        if (SystemModeConfig.isPrivate56()) {
            //5.6 都是 private-xxx的标准格式。对应 xxx:xylink的标准格式。  有个别限制无法操作，如logagent
            resultList.forEach(x -> x.setDeploySupport(!ServerManageNotSupport.SERVERS_56.contains(x.getServerName())));
            return;
        }
        //5.2
        if (PlatformConfig.isAnKe()) {
            resultList.forEach(x -> x.setDeploySupport(!ServerManageNotSupport.SERVERS_52_ANKE.contains(x.getServerName())));
        } else {
            resultList.forEach(x -> x.setDeploySupport(!ServerManageNotSupport.SERVERS_52.contains(x.getServerName())));
        }
    }


    public String getServerStatus(String controllerName, List<Pod> pods) {
        List<Pod> controllerPods = getControllerPods(controllerName, pods);
        if (CollectionUtils.isEmpty(controllerPods)) {
            return STATUS_OK;
        }
        for (Pod targetPod : controllerPods) {
            String phase = targetPod.getStatus().getPhase();
            if (!"Running".equalsIgnoreCase(phase)) {
                return STATUS_ERROR;
            }
        }
        return STATUS_OK;
    }


    public List<Pod> getControllerPods(String controllerName, List<Pod> pods) {
        List<Pod> targetPods = pods.stream().filter(x -> x.getMetadata().getName().contains(controllerName))
                .collect(Collectors.toList());
        Predicate<Pod> predicate = x -> {
            OwnerReference finalOwnerReference = getFinalOwnerReference(x);
            if (Objects.isNull(finalOwnerReference)) {
                return false;
            }
            return StringUtils.equals(finalOwnerReference.getName(), controllerName);
        };
        return targetPods.stream().filter(predicate).collect(Collectors.toList());
    }

    private Integer getRestartCount(PodCache pod) {
        int sum = 0;
        List<ContainerStatusCache> containerStatuses = pod.getStatus().getContainerStatuses();
        if (CollectionUtils.isEmpty(containerStatuses)) {
            return 0;
        }
        for (ContainerStatusCache containerStatus : containerStatuses) {
            Integer restartCount = containerStatus.getRestartCount();
            if (Objects.nonNull(restartCount)) {
                sum += restartCount;
            }
        }
        return sum;
    }

    private Pair2<String, String> getFinalOwnerReferenceCacheInfo(PodCache podCache) {
        List<OwnerReferenceCache> ownerReferenceCaches = podCache.getMetadata().getOwnerReferences();
        if (CollectionUtils.isEmpty(ownerReferenceCaches)) {
            log.warn("{} pod not found the owner reference!", podCache.getMetadata().getName());
            return new Pair2<>(null, null);
        }
        OwnerReferenceCache ownerReferenceCache = ownerReferenceCaches.get(0);
        String kind = ownerReferenceCache.getKind();
        if (!ServerManageData.ControllerType.DEPLOYMENT_REPLICA_SET.equals(kind)) {
            String serverName = ownerReferenceCache.getName();
            String controllerType = ownerReferenceCache.getKind();
            return new Pair2<>(serverName, controllerType);
        }
        ReplicaSet replicaSet = k8sDeployService.client().apps().replicaSets()
                .inNamespace(Constants.NAMESPACE_DEFAULT).withName(ownerReferenceCache.getName()).get();
        if (Objects.isNull(replicaSet)) {
            log.warn("{} pod not found the owner replicaSet!", podCache.getMetadata().getName());
            return new Pair2<>(null, null);
        }
        List<OwnerReference> ownerReferences = replicaSet.getMetadata().getOwnerReferences();
        if (CollectionUtils.isEmpty(ownerReferences)) {
            log.warn("{} pod not found the final owner reference!", podCache.getMetadata().getName());
            return new Pair2<>(null, null);
        }
        OwnerReference ownerReference = ownerReferences.get(0);
        return new Pair2<>(ownerReference.getName(), ownerReference.getKind());
    }

    private OwnerReference getFinalOwnerReference(Pod pod) {
        List<OwnerReference> ownerReferences = pod.getMetadata().getOwnerReferences();
        if (CollectionUtils.isEmpty(ownerReferences)) {
            log.warn("{} pod not found the owner reference!", pod.getMetadata().getName());
            return null;
        }
        OwnerReference ownerReference = ownerReferences.get(0);
        String kind = ownerReference.getKind();
        if (ServerManageData.ControllerType.DEPLOYMENT_REPLICA_SET.equals(kind)) {
            ReplicaSet replicaSet = k8sDeployService.client().apps().replicaSets()
                    .inNamespace(Constants.NAMESPACE_DEFAULT).withName(ownerReference.getName()).get();
            if (Objects.isNull(replicaSet)) {
                log.warn("{} pod not found the owner replicaSet!", pod.getMetadata().getName());
                return null;
            }
            ownerReferences = replicaSet.getMetadata().getOwnerReferences();
            if (CollectionUtils.isEmpty(ownerReferences)) {
                log.warn("{} pod not found the final owner reference!", pod.getMetadata().getName());
                return null;
            }
            return ownerReferences.get(0);
        }
        return ownerReference;
    }


    public Page<ServerInstanceInfoVO> serverInstanceList(ServerInstanceListParam param) {
        List<ServerInstanceInfoVO> resultList = (List<ServerInstanceInfoVO>) LocalCacheUtil.get(ServerManageData.SERVER_INSTANCE_LIST_CACHE_KEY);
        if (Objects.nonNull(resultList)) {
            log.info("get serverInstanceList from cache data");
            return buildServerInstanceList(resultList, param);
        } else {
            resultList = new ArrayList<>();
        }
        Stopwatch stopwatch = Stopwatch.createUnstarted();
        stopwatch.start();
        List<PodCache> podCaches = cacheService.cachePodList();
        stopwatch.stop();
        long costMills = stopwatch.elapsed(TimeUnit.MILLISECONDS);
        log.info("serverInstanceList time get pods: {}", costMills);
        stopwatch.reset().start();
        List<String> notSupportServices = isNotDeploySupportServices();
        List<com.xylink.manager.model.deploy.Node> nodes = deployService.listAllNodes();
        for (PodCache podCache : podCaches) {
            ServerInstanceInfoVO vo = new ServerInstanceInfoVO();
            vo.setInstanceName(podCache.getMetadata().getName());
            Pair2<String, String> pari2 = getFinalOwnerReferenceCacheInfo(podCache);
            String serverName = pari2.getFirst();
            vo.setServerName(serverName);
            if (StringUtils.isBlank(serverName) || notSupportServices.contains(serverName)) {
                //不支持的服务
                continue;
            }
            String controllerType = pari2.getSecond();
            if ("Job".equals(controllerType)) {
                continue;
            }
            if ("Node".equals(controllerType)) {
                continue;
            }
            vo.setControllerType(controllerType);
            String nodeName = podCache.getSpec().getNodeName();
            vo.setK8sNodeName(nodeName);
            com.xylink.manager.model.deploy.Node node;
            if (StringUtils.isBlank(nodeName)) {
                log.warn("{} pod has no nodeName", podCache.getMetadata().getName());
                node = null;
            } else {
                node = getNodeByName(nodeName, nodes);
            }
            if (Objects.isNull(node)) {
                log.warn("{} pod not has node", podCache.getMetadata().getName());
            }
            vo.setK8sNodeInternalIp(Objects.isNull(node) ? "" : node.getIp());
            vo.setK8sNodeExternalIp(getNodeExternalIp(node));
            vo.setExistAdvance(getExistAdvance(serverName));
            vo.setAdvanceFeature(serverName.replace(ServerManageData.NAME_PREFIX, ""));
            vo.setNodeType(Objects.isNull(node) ? null : node.getType());
            vo.setStatus(getPodStatus(podCache, controllerType));
            vo.setRestartCount(getRestartCount(podCache));
            vo.setRunningSeconds(getRunningSecondsCache(podCache));
            resultList.add(vo);
        }
        stopwatch.stop();
        costMills = stopwatch.elapsed(TimeUnit.MILLISECONDS);
        log.info("serverInstanceList build resultList: {}", costMills);
        //设置到缓存
        LocalCacheUtil.put(ServerManageData.SERVER_INSTANCE_LIST_CACHE_KEY, resultList);
        stopwatch.reset().start();
        Page<ServerInstanceInfoVO> returnList = buildServerInstanceList(resultList, param);
        stopwatch.stop();
        costMills = stopwatch.elapsed(TimeUnit.MILLISECONDS);
        log.info("serverInstanceList build returnList: {}", costMills);
        return returnList;
    }

    private String getPodStatus(PodCache podCache, String controllerType) {
        String status;
        if (podCache.getStatus().getPhase().equalsIgnoreCase("Running")) {
            status = "Running";
            if (podCache.getStatus().getContainerStatuses().get(0).getState().getRunning() == null
                    && podCache.getStatus().getContainerStatuses().get(0).getState().getWaiting() != null) {
                status = podCache.getStatus().getContainerStatuses().get(0).getState().getWaiting().getReason();
            }
            if (StringUtils.isNotBlank(podCache.getMetadata().getDeletionTimestamp())) {
                status = "Terminating";
            }
        } else {
            status = podCache.getStatus().getPhase();
            if (!CollectionUtils.isEmpty(podCache.getStatus().getInitContainerStatuses())) {
                int readyCount = (int) podCache.getStatus().getInitContainerStatuses().stream()
                        .filter(ContainerStatusCache::getReady).count();
                int initCount = podCache.getStatus().getInitContainerStatuses().size();
                if (readyCount != initCount) {
                    status = "Init:" + readyCount + "/" + initCount;
                }
            }
        }
        return status;
    }


    private Long getRunningSecondsCache(PodCache podCache) {
        String startTime = podCache.getStatus().getStartTime();
        if (StringUtils.isBlank(startTime)) {
            return null;
        }
        Long startMills = DateTimeUtil.strTime(startTime.replace("T", " "), "yyyy-MM-dd hh:mm:ssZ");
        if (Objects.isNull(startMills)) {
            log.warn("getRunningSeconds failed of {}", startTime);
            return null;
        }
        return (System.currentTimeMillis() - startMills) / 1000;
    }

    private String getNodeExternalIp(com.xylink.manager.model.deploy.Node node) {
        if (Objects.isNull(node)) {
            return null;
        }
        Map<String, String> labels = node.getLabels();
        if (Objects.isNull(labels)) {
            return null;
        }
        return labels.get(ServerManageData.LABEL_KEY_NODE_EXTERNAL_IP);
    }

    private Boolean getExistAdvance(String serverName) {
        String label = serverName.replace(ServerManageData.NAME_PREFIX, "");
        return Labels.advanceLabels().contains(label);
    }

    private com.xylink.manager.model.deploy.Node getNodeByName(String nodeName, List<com.xylink.manager.model.deploy.Node> nodes) {
        if (StringUtils.isBlank(nodeName)) {
            return null;
        }
        Optional<com.xylink.manager.model.deploy.Node> first = nodes.stream().filter(x -> nodeName.equals(x.getName())).findFirst();
        return first.orElse(null);
    }

    public boolean isNotDeploySupport(String serverName) {
        if (SystemModeConfig.isPrivate56()) {
            //5.6 都是 private-xxx 对应 xxx:xylink的标准格式，但也有个别限制无法操作
            return ServerManageNotSupport.SERVERS_56.contains(serverName);
        }
        //5.2
        if (PlatformConfig.isAnKe()) {
            return ServerManageNotSupport.SERVERS_52_ANKE.contains(serverName);
        } else {
            return ServerManageNotSupport.SERVERS_52.contains(serverName);
        }
    }

    public List<String> isNotDeploySupportServices() {
        if (SystemModeConfig.isPrivate56()) {
            //5.6 都是 private-xxx 对应 xxx:xylink的标准格式，但也有个别限制无法操作
            return ServerManageNotSupport.SERVERS_56;
        }
        //5.2
        if (PlatformConfig.isAnKe()) {
            return ServerManageNotSupport.SERVERS_52_ANKE;
        } else {
            return ServerManageNotSupport.SERVERS_52;
        }
    }

    private Page<ServerInstanceInfoVO> buildServerInstanceList(List<ServerInstanceInfoVO> resultList, ServerInstanceListParam param) {
        String keywords = param.getKeywords();
        String serverName = param.getServerName();
        String status = param.getStatus();
        String controllerType = param.getControllerType();
        List<String> ensureStatus = Lists.newArrayList("Pending", "Running", "Succeeded", "Failed");
        List<ServerInstanceInfoVO> records = resultList.stream()
                .filter(x -> StringUtils.isBlank(keywords) || x.getServerName().contains(keywords)
                        || x.getInstanceName().contains(keywords)
                        || x.getK8sNodeInternalIp().contains(keywords))
                .filter(x -> StringUtils.isBlank(status) || status.equals(x.getStatus()) ||
                        //筛选Unknown时匹配其他未知的
                        ("Unknown".equals(status) && !ensureStatus.contains(x.getStatus()))
                )
                .filter(x -> StringUtils.isBlank(controllerType) || controllerType.equals(x.getControllerType()))
                .filter(x -> StringUtils.isBlank(serverName) || serverName.equals(x.getServerName()))
                .collect(Collectors.toList());
        String orderByField = param.getOrderByField();
        if (StringUtils.isBlank(orderByField) || "instanceName".equals(orderByField)) {
            records.sort(Comparator.comparing(ServerInstanceInfoVO::getServerName, Comparator.nullsLast(String::compareTo)));
        } else if ("runningSeconds".equals(orderByField)) {
            records.sort(Comparator.comparing(ServerInstanceInfoVO::getRunningSeconds, Comparator.nullsLast(Long::compareTo)));
        } else if ("restartCount".equals(orderByField)) {
            records.sort(Comparator.comparing(ServerInstanceInfoVO::getRestartCount, Comparator.nullsLast(Integer::compareTo)));
        }
        if (param.getOrderDesc()) {
            Collections.reverse(records);
        }
        return MemoryPaginationUtil.pagination(records, param.getCurrent(), param.getPageSize());
    }

    private void excludeNewContainer(List<Container> containers) {
        //针对新增的jar-change等container，为了不影响原先获取镜像版本逻辑，需要remove新加container
        if (CollectionUtils.isEmpty(containers)) {
            return;
        }
        containers.removeIf(c -> EXCLUDE_CONTAINER_NAME_LIST.contains(c.getName()));
    }

    public String getCodeVersion(PodTemplateSpec podTemplateSpec) {
        if (Objects.isNull(podTemplateSpec)) {
            return "";
        }
        List<Container> containers = podTemplateSpec.getSpec().getContainers();
        List<Container> initContainers = podTemplateSpec.getSpec().getInitContainers();
        excludeNewContainer(initContainers);
        excludeNewContainer(containers);
        Container container = null;
        boolean findContain = false;
        //1、如果是private-rmserver-lib等特殊服务，直接取initContainer的第一个
        if (!CollectionUtils.isEmpty(initContainers)) {
            container = initContainers.stream().filter(x -> SPECIAL_SERVER.contains(x.getName())).findFirst().orElse(null);
            if (Objects.nonNull(container)) {
                findContain = true;
            }
        }
        //2、取initContainer的最后一个（init-chmod-dir等已经被排除了）
        if (!findContain && !CollectionUtils.isEmpty(initContainers)) {
            container = initContainers.get(initContainers.size() - 1);
            if (container.getName().contains(Constants.PRIVATE_PREFIX)) {
                findContain = true;
            }
        }
        //3、取容器private-xxx的
        if (!findContain && containers.size() > 0) {
            for (Container c : containers) {
                container = c;
                if (container.getName().contains(Constants.PRIVATE_PREFIX) || "runcupdate".equalsIgnoreCase(container.getName())
                        || "oceanbase".equalsIgnoreCase(container.getName())) {
                    findContain = true;
                    break;
                }
            }
        }
        if (findContain) {
            List<String> imageContents = Arrays.asList(container.getImage().split(":"));
            return imageContents.get(imageContents.size() - 1);
        }
        return "";
    }

    public void restartPods(List<String> instanceNames) {
        log.info("restart instances: [{}]", instanceNames);
        if (CollectionUtils.isEmpty(instanceNames)) {
            return;
        }
        for (String instanceName : instanceNames) {
            PodResource<Pod> podPodResource = k8sDeployService.client().pods()
                    .inNamespace(Constants.NAMESPACE_DEFAULT).withName(instanceName);
            Pod pod = podPodResource.get();
            if (Objects.isNull(pod)) {
                log.warn("not found pod:{}", instanceName);
            } else {
                log.info("delete instance pod:{}", instanceName);
                podPodResource.delete();
            }
        }
        LocalCacheUtil.remove(ServerManageData.SERVER_LIST_CACHE_KEY);
        LocalCacheUtil.remove(ServerManageData.SERVER_INSTANCE_LIST_CACHE_KEY);
    }

    public void restartServers(List<String> serverNames) {
        log.info("restart servers: [{}]", serverNames);
        if (CollectionUtils.isEmpty(serverNames)) {
            return;
        }
        serverNames.forEach(this::deleteServerPods);
        LocalCacheUtil.remove(ServerManageData.SERVER_LIST_CACHE_KEY);
        LocalCacheUtil.remove(ServerManageData.SERVER_INSTANCE_LIST_CACHE_KEY);
    }

    private void deleteServerPods(String serverName) {
        if (isNotDeploySupport(serverName)) {
            log.info("{} not support by {} and {}", serverName, SystemModeConfig.current(), PlatformConfig.current());
            return;
        }
        List<Pod> pods = k8sDeployService.client().pods().list().getItems();
        if (CollectionUtils.isEmpty(pods)) {
            return;
        }
        List<Pod> controllerPods = getControllerPods(serverName, pods);
        if (CollectionUtils.isEmpty(controllerPods)) {
            log.info("{} found instances: []", serverName);
            return;
        }
        List<String> instanceNames = controllerPods.stream()
                .map(x -> x.getMetadata().getName()).collect(Collectors.toList());
        log.info("{} found instances: [{}]", serverName, instanceNames);
        for (String instanceName : instanceNames) {
            PodResource<Pod> podPodResource = k8sDeployService.client().pods()
                    .inNamespace(Constants.NAMESPACE_DEFAULT).withName(instanceName);
            Pod pod = podPodResource.get();
            if (Objects.isNull(pod)) {
                log.warn("not found pod:{}", instanceName);
            } else {
                log.info("delete instance pod:{}", instanceName);
                podPodResource.delete();
            }
        }
    }

    private void removeOriginNodeLabelAndTaint(String serverName, List<Node> skipNodes, Map<String, Node> clusterNodes) {
        log.info("{} start remove origin node labels and taints", serverName);
        Set<String> skipNodeNames = skipNodes.stream().map(x -> x.getMetadata().getName()).collect(Collectors.toSet());
        for (Map.Entry<String, Node> entry : clusterNodes.entrySet()) {
            //跳过当前需要部署的节点
            String nodeName = entry.getKey();
            if (skipNodeNames.contains(nodeName)) {
                continue;
            }
            Node node = entry.getValue();
            boolean hasChange = removeLabelAndTaintNode(serverName, node);
            if (hasChange) {
                k8sDeployService.client().nodes().withName(nodeName).replace(node);
                log.info("{} remove origin label and taints changed node {}", serverName, nodeName);
            }
        }
        log.info("{} end remove origin node labels and taints", serverName);
    }

    private boolean removeLabelAndTaintNode(String serverName, Node node) {
        //移除 [服务名:xylink] 标签
        boolean hasChange = false;
        String nodeName = node.getMetadata().getName();
        Map<String, String> labels = node.getMetadata().getLabels();
        if (Objects.isNull(labels)) {
            labels = new HashMap<>();
        }
        Iterator<Map.Entry<String, String>> iterator = labels.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, String> label = iterator.next();
            String labelKey = label.getKey();
            String labelValue = label.getValue();
            String deployKey = serverName.replace(ServerManageData.NAME_PREFIX, "");
            String deployValue = ServerManageData.DeployConfig.NODE_SELECTOR_VALUE;
            if (StringUtils.equals(labelKey, deployKey) && StringUtils.equals(labelValue, deployValue)) {
                log.info("{} node will remove label {}:{}", nodeName, deployKey, deployValue);
                iterator.remove();
                hasChange = true;
            }
        }
        //移除 污点
        List<Taint> taints = node.getSpec().getTaints();
        if (Objects.isNull(taints)) {
            taints = new ArrayList<>();
        }
        Iterator<Taint> iter = taints.iterator();
        while (iter.hasNext()) {
            Taint taint = iter.next();
            String taintKey = taint.getKey();
            String taintValue = taint.getValue();
            String taintEffect = taint.getEffect();
            String deployTaintKey = ServerManageData.DeployConfig.TAINT_KEY;
            String deployTaintValue = serverName.replace(ServerManageData.NAME_PREFIX, "");
            String deployTaintEffect = ServerManageData.DeployConfig.TAINT_EFFECT_NO_EXECUTE;
            if (StringUtils.equals(taintKey, deployTaintKey) && StringUtils.equals(taintValue, deployTaintValue)
                    && StringUtils.equals(taintEffect, deployTaintEffect)) {
                log.info("{} node will remove taint {}={}:{}", nodeName, taintKey, taintValue, taintEffect);
                iter.remove();
                hasChange = true;
            }
        }
        return hasChange;
    }

    private void addNodeLabelAntTaint(String serverName, Node node, String labelKey, String labelValue,
                                      String taintKey, String taintValue, String taintEffect) {
        String nodeName = node.getMetadata().getName();
        log.info("{} node will put label {}:{}", nodeName, labelKey, labelValue);
        Map<String, String> labels = node.getMetadata().getLabels();
        if (Objects.isNull(labels)) {
            labels = new HashMap<>();
            node.getMetadata().setLabels(labels);
        }
        labels.put(labelKey, labelValue);
        Taint taint = new TaintBuilder()
                .withKey(taintKey)
                .withValue(taintValue)
                .withEffect(taintEffect)
                .build();
        List<Taint> taints = node.getSpec().getTaints();
        if (Objects.isNull(taints)) {
            taints = new ArrayList<>();
            node.getSpec().setTaints(taints);
        }
        Iterator<Taint> iterator = taints.iterator();
        while (iterator.hasNext()) {
            Taint t = iterator.next();
            if (taintKey.equals(t.getKey()) && taintEffect.equals(t.getEffect())) {
                log.info("{} node will remove taint {}:{}={}", nodeName, t.getKey(), t.getValue(), t.getEffect());
                iterator.remove();
            }
        }
        log.info("{} node will add taint {}:{}={}", nodeName, taintKey, taintValue, taintEffect);
        taints.add(taint);
        k8sDeployService.client().nodes().withName(nodeName).replace(node);
    }

    private void addNodeLabel(Node node, String key, String value) {
        String nodeName = node.getMetadata().getName();
        log.info("{} node will put label {}:{}", nodeName, key, value);
        Map<String, String> labels = node.getMetadata().getLabels();
        if (Objects.isNull(labels)) {
            labels = new HashMap<>();
            node.getMetadata().setLabels(labels);
        }
        labels.put(key, value);
        k8sDeployService.client().nodes().withName(nodeName).replace(node);
    }

    public List<DeployNode> deployNodes(String serverName) {
        List<com.xylink.manager.model.deploy.Node> nodes = k8sDeployService.listAllNodes();
        if (CollectionUtils.isEmpty(nodes)) {
            return Lists.newArrayList();
        }
        List<DeployNode> resultList = new ArrayList<>();
        String key = serverName.replace(ServerManageData.NAME_PREFIX, "");
        String value = ServerManageData.DeployConfig.NODE_SELECTOR_VALUE;
        for (com.xylink.manager.model.deploy.Node node : nodes) {
            Map<String, String> labels = node.getLabels();
            if (Objects.isNull(labels)) {
                continue;
            }
            String val = labels.get(key);
            if (Objects.isNull(val)) {
                continue;
            }
            if (val.equals(value)) {
                DeployNode dto = new DeployNode();
                dto.setHostName(node.getHostName());
                dto.setNodeId(node.getName());
                dto.setInternalIp(node.getIp());
                resultList.add(dto);
            }
        }
        return resultList;
    }

    public boolean validDeployAndControllerType(String deployType, String controllerType) {
        if (StringUtils.isBlank(deployType)) {
            return false;
        }
        if (StringUtils.isBlank(controllerType)) {
            return false;
        }
        boolean validDeployType = false;
        switch (deployType) {
            case ServerManageData.DeployType.RANDOM_DRIFT_NODE:
            case ServerManageData.DeployType.FIXED_NODE:
            case ServerManageData.DeployType.MONOPOLIZE_NODE:
                validDeployType = true;
                break;
            default:
                break;
        }
        boolean validControllerType = false;
        switch (controllerType) {
            case ServerManageData.ControllerType.DAEMON_SET:
            case ServerManageData.ControllerType.DEPLOYMENT:
            case ServerManageData.ControllerType.STATEFUL_SET:
                validControllerType = true;
                break;
            default:
                break;
        }
        return validDeployType && validControllerType;
    }

    public List<Node> parseDeployNodes(String deployType, Map<String, Node> clusterNodes, List<String> k8sNodeNames) {
        List<Node> nodes = new ArrayList<>();
        if (!ServerManageData.DeployType.RANDOM_DRIFT_NODE.equals(deployType)) {
            //非随机漂移的要求必须选择node
            if (CollectionUtils.isEmpty(k8sNodeNames)) {
                throw new ServerException("参数有误[未选择节点]！");
            }
            for (String k8sNodeName : k8sNodeNames) {
                Node node = clusterNodes.get(k8sNodeName);
                if (Objects.isNull(node)) {
                    throw new ServerException("部署异常，请检查[" + k8sNodeName + "]node节点！");
                }
                nodes.add(node);
            }
        }
        return nodes;
    }

    public Map<String, Node> getClusterNodes() {
        Map<String, Node> nodeMap = new HashMap<>();
        List<Node> nodes = k8sDeployService.client().nodes().list().getItems();
        if (CollectionUtils.isEmpty(nodes)) {
            return nodeMap;
        }
        for (Node node : nodes) {
            nodeMap.put(node.getMetadata().getName(), node);
        }
        return nodeMap;
    }

    public void deployForNode(String serverName, String deployType, Map<String, Node> clusterNodes, List<Node> deployNodes) {
        String labelKey = serverName.replace(ServerManageData.NAME_PREFIX, "");
        String labelValue = ServerManageData.DeployConfig.NODE_SELECTOR_VALUE;
        String taintKey = ServerManageData.DeployConfig.TAINT_KEY;
        String taintValue = serverName.replace(ServerManageData.NAME_PREFIX, "");
        String taintEffect = ServerManageData.DeployConfig.TAINT_EFFECT_NO_EXECUTE;

        //1、清理原node标签、污点，并且跳过已经部署的节点
        removeOriginNodeLabelAndTaint(serverName, deployNodes, clusterNodes);

        if (ServerManageData.DeployType.FIXED_NODE.equals(deployType)) {
            //2、为node打标签
            for (Node node : deployNodes) {
                addNodeLabel(node, labelKey, labelValue);
            }
            return;
        }
        if (ServerManageData.DeployType.MONOPOLIZE_NODE.equals(deployType)) {
            //2、为node打标签、污点
            for (Node node : deployNodes) {
                addNodeLabelAntTaint(serverName, node, labelKey, labelValue, taintKey, taintValue, taintEffect);
            }
        }
    }

    public void cancelDeployForNode(String serverName, Map<String, Node> clusterNodes) {
        //1、清理原node标签、污点，跳过节点参数传空
        removeOriginNodeLabelAndTaint(serverName, Lists.newArrayList(), clusterNodes);
    }

    public void handleDomainAndHostIp(List<DeployNode> deployNodes, String serverName) {
        log.info("start handle deploy handleDomainAndHostIp of {}", serverName);
        String label = serverName.replace(ServerManageData.NAME_PREFIX, "");
        String configmapName = getServerCm(serverName);
        log.info("get handleDomainAndHostIp cm name: {}", serverName);
        com.xylink.manager.model.deploy.ConfigMap configMap = deployService.getConfigMapByName(configmapName, Constants.NAMESPACE_DEFAULT);
        //没有该all-* configmap 则自动创建
        Map<String, String> cmData = configMap == null ? DefaultConfigmapDataEnum.initDefault(label) : configMap.getData();

        for (DeployNode deployNode : deployNodes) {
            String hostName = deployNode.getHostName();
            if (SystemModeConfig.isPrivate56()) {
                log.info("handleDomainAndHostIp check env is 5.6 so use nodeId as hostname");
                hostName = deployNode.getNodeId();
            }
            String nodeInterIpKey = hostName + NetworkConstants.SUFFIX_INTERNAL_IP;
            String nodePubIpKey = hostName + NetworkConstants.SUFFIX_PUBLIC_IP;
            String nodeDomainKey = hostName + NetworkConstants.SUFFIX_DOMAIN;
            //清除脏数据
            Iterator<Map.Entry<String, String>> iterator = cmData.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, String> next = iterator.next();
                if (next.getKey() == null || next.getValue() == null) {
                    break;
                }
                if (next.getKey().endsWith(NetworkConstants.SUFFIX_INTERNAL_IP) && next.getValue().equalsIgnoreCase(deployNode.getInternalIp())) {
                    iterator.remove();
                }
            }
            cmData.put(nodeInterIpKey, deployNode.getInternalIp());
            cmData.put(nodePubIpKey, deployNode.getExternalIp());
            cmData.put(nodeDomainKey, deployNode.getInternalIp());
            if ("mc".equalsIgnoreCase(label) && StringUtils.isBlank(deployNode.getExternalIp())) {
                cmData.put(nodePubIpKey, deployNode.getInternalIp());
            }
        }
        log.info("{} host info start patch configmap {}, write:[{}]", serverName, configmapName, cmData);
        deployService.patchConfigMap(configmapName, Constants.NAMESPACE_DEFAULT, d -> d.putAll(cmData));

        //向all-ip中写入负载均衡多活负载均衡IP，供nginx渲染使用
        StringBuilder distributeIp = new StringBuilder();
        for (DeployNode deployNode : deployNodes) {
            distributeIp.append(Ipv6Util.getHost(deployNode.getInternalIp(), deployNode.getNodeId())).append(",");
        }
        if (distributeIp.length() > 0) {
            distributeIp = new StringBuilder(distributeIp.substring(0, distributeIp.length() - 1));
        }
        if (Labels.nightingale.label().equals(label)) {
            log.info("nightingale not need write so return");
            return;
        }
        String loadBalanceKey;
        try {
            loadBalanceKey = Labels.labelOf(label).loadblanceKey();
        } catch (Exception ex) {
            log.info("{} loadBalanceKey error: {}, so return", serverName, ex.getMessage());
            return;
        }
        if (StringUtils.isBlank(loadBalanceKey)) {
            log.info("{} loadBalanceKey start , key is null, so return", serverName);
            return;
        }
        String loadBalanceVal = distributeIp.toString();
        configmapName = "all-ip";
        log.info("{} loadBalanceKey start patch configmap {}, write :[{}], val:[{}]", serverName, configmapName, loadBalanceKey, loadBalanceVal);
        deployService.patchConfigMap(configmapName, Constants.NAMESPACE_DEFAULT, d -> {
            d.put(loadBalanceKey, loadBalanceVal);
        });
    }

    public String getServerCm(String serverName) {
        String label = serverName.replace(ServerManageData.NAME_PREFIX, "");
        String configmapName = "all-" + label;
        if (label.endsWith("-x86")) {
            configmapName = "all-" + label.replace("-x86", "");
        }
        if (label.endsWith(Constants.ARM)) {
            configmapName = "all-" + label.replace(Constants.ARM, "");
        }
        configmapName = configmapName.replace("_", "-");
        if ("h323-sig".equalsIgnoreCase(label) || "h323-sig-x86".equalsIgnoreCase(label) || "h323-sig-arm".equalsIgnoreCase(label)) {
            configmapName = Constants.CONFIGMAP_H323_GATEWAY;
        }
        if ("mcserver".equals(label)) {
            configmapName = Constants.CONFIGMAP_MC;
        }
        if ("allocatorserver".equals(label)) {
            configmapName = Constants.CONFIGMAP_ALL_ALLOCATOR;
        }
        if ("cascademgr".equals(label)) {
            configmapName = Constants.CONFIGMAP_CASCADE_MGR;
        }
        return configmapName;
    }

    public void editCm(EditCmParam param) {
        String configmapName = param.getConfigmapName();
        if (StringUtils.isBlank(configmapName)) {
            throw new ServerException("参数有误");
        }
        String operation = param.getOperation();
        Map<String, String> datas = param.getDatas();
        if (ServerManageData.ConfigMapOperation.PATCH.equals(operation)) {
            k8sDeployService.patchConfigMap(configmapName, Constants.NAMESPACE_DEFAULT, c -> c.putAll(datas));
        } else if (ServerManageData.ConfigMapOperation.REPLACE.equals(operation)) {
            k8sDeployService.patchConfigMap(configmapName, Constants.NAMESPACE_DEFAULT, c -> {
                c.clear();
                c.putAll(datas);
            });
        } else {
            throw new ServerException("不支持的操作方式");
        }
    }

    public void handleAdvanceConfig(List<DeployNode> deployNodes, String serverName) {
        List<ServerAdvanceConfigStrategy> strategies = serverAdvanceConfigStrategyFactory.create(serverName);
        for (ServerAdvanceConfigStrategy strategy : strategies) {
            new ServerAdvanceConfigStrategyContext().advanceConfig(strategy, deployNodes, serverName);
        }
    }

    public void labelToNode(NodeToLabelDTO param) {
        String nodeId = param.getNodeId();
        if (Objects.isNull(nodeId)) {
            throw new ServerException("nodeId不能为空！");
        }
        Node node = k8sDeployService.client().nodes().withName(nodeId).get();
        if (Objects.isNull(node)) {
            log.warn("labelToNode not found node {} and return", nodeId);
            return;
        }
        Map<String, String> labels = node.getMetadata().getLabels();
        if (Objects.isNull(labels)) {
            labels = new HashMap<>();
            node.getMetadata().setLabels(labels);
        }
        Map<String, String> datas = param.getDatas();
        log.info("{} label to node data:[{}]", nodeId, datas);
        labels.putAll(datas);
        k8sDeployService.client().nodes().withName(nodeId).replace(node);
    }

    public Page<DeployNodeExtend> nodeList(Integer current, Integer pageSize, String keywords) {
        List<Node> nodes = k8sDeployService.client().nodes().list().getItems();
        if (CollectionUtils.isEmpty(nodes)) {
            return new Page<>(current, pageSize);
        }
        List<DeployNodeExtend> resultList = new ArrayList<>();
        for (Node node : nodes) {
            Map<String, String> labels = node.getMetadata().getLabels();
            if (Objects.nonNull(labels) && "common".equals(labels.get(Constants.TYPE))) {
                DeployNodeExtend dto = new DeployNodeExtend();
                String nodeId = node.getMetadata().getName();
                dto.setNodeId(nodeId);
                //hostname
                dto.setHostName(getNodeHostName(node));
                //internalIp
                dto.setInternalIp(getNodeInternalIp(node));
                dto.setExternalIp(labels.get(ServerManageData.LABEL_KEY_NODE_EXTERNAL_IP));
                dto.setMonopolizeNode(false);
                List<Taint> taints = node.getSpec().getTaints();
                if (!CollectionUtils.isEmpty(taints)) {
                    for (Taint taint : taints) {
                        if (ServerManageData.DeployConfig.TAINT_KEY.equals(taint.getKey())
                                && ServerManageData.DeployConfig.TAINT_EFFECT_NO_EXECUTE.equals(taint.getEffect())) {
                            dto.setMonopolizeNode(true);
                            dto.setMonopolizeServer(ServerManageData.NAME_PREFIX + taint.getValue());
                        }
                    }
                }
                resultList.add(dto);
            }
        }
        if (StringUtils.isNotBlank(keywords)) {
            resultList = resultList.stream().filter(x ->
                    (Objects.nonNull(x.getNodeId()) && x.getNodeId().contains(keywords)) ||
                            (Objects.nonNull(x.getExternalIp()) && x.getExternalIp().contains(keywords)) ||
                            (Objects.nonNull(x.getInternalIp()) && x.getInternalIp().contains(keywords))
            ).collect(Collectors.toList());
        }
        return MemoryPaginationUtil.pagination(resultList, current, pageSize);
    }

    private String getNodeInternalIp(Node node) {
        List<NodeAddress> addresses = node.getStatus().getAddresses();
        if (CollectionUtils.isEmpty(addresses)) {
            return "";
        }
        return addresses.stream()
                .filter(x -> x.getType().equalsIgnoreCase(Constants.INTERNAL_IP))
                .findFirst()
                .map(NodeAddress::getAddress)
                .orElse("");
    }

    private String getNodeHostName(Node node) {
        String hostname;
        Map<String, String> labels = node.getMetadata().getLabels();
        if (CollectionUtils.isEmpty(labels)) {
            hostname = "";
        } else {
            hostname = labels.getOrDefault(Constant.HOST_NAME, "");
        }
        if (StringUtils.isBlank(hostname)) {
            hostname = labels.getOrDefault("kubernetes.io/hostname", "");
        }
        if (StringUtils.isBlank(hostname)) {
            List<NodeAddress> addresses = node.getStatus().getAddresses();
            if (CollectionUtils.isEmpty(addresses)) {
                return hostname;
            }
            hostname = addresses.stream()
                    .filter(x -> x.getType().equalsIgnoreCase(Constants.HOSTNAME))
                    .findFirst()
                    .map(NodeAddress::getAddress)
                    .orElse("");
        }
        return hostname;
    }

    public List<FeatureDto> getFeatureList(String nodeName) {
        ConfigMapCache configMapCache = k8sService.getConfigMapCacheOrCreateIfNotExist(Constants.CONFIGMAP_SERVER_DESCRIPTION);
        Map<String, String> allServerDescription = configMapCache.getData();
        if (Objects.isNull(allServerDescription)) {
            allServerDescription = new HashMap<>();
        }
        com.xylink.manager.model.deploy.Node node = k8sDeployService.getNodeByName(nodeName);
        if (Objects.isNull(node)) {
            return Lists.newArrayList();
        }
        Map<String, String> labels = node.getLabels();
        if (Objects.isNull(labels)) {
            return Lists.newArrayList();
        }
        List<FeatureDto> resultList = new ArrayList<>();
        for (Map.Entry<String, String> entry : labels.entrySet()) {
            if (ServerManageData.DeployConfig.NODE_SELECTOR_VALUE.equals(entry.getValue())) {
                FeatureDto dto = new FeatureDto();
                String label = entry.getKey();
                dto.setFeature(label);
                dto.setExistAdvance(Labels.advanceLabels().contains(label));
                String displayNameKey = label + Constants.DISPLAY_NAME;
                String descriptionKey = label + Constants.DESCRIPTION;
                dto.setDisplayName(allServerDescription.get(displayNameKey));
                dto.setDescription(allServerDescription.get(descriptionKey));
                resultList.add(dto);
            }
        }
        return resultList;
    }

    public String getDeployType(String controllerType, String serverName) {
        Map<String, String> labels = null;
        if (ServerManageData.ControllerType.DAEMON_SET.equals(controllerType)) {
            DaemonSet ds = k8sDeployService.client().apps().daemonSets().inNamespace(Constants.NAMESPACE_DEFAULT).withName(serverName).get();
            if (Objects.isNull(ds)) {
                throw new ServerException("未找到控制器资源，请检查服务名称是否正确!");
            }
            labels = ds.getMetadata().getLabels();
        } else if (ServerManageData.ControllerType.DEPLOYMENT.equals(controllerType)) {
            Deployment dp = k8sDeployService.client().apps().deployments().inNamespace(Constants.NAMESPACE_DEFAULT).withName(serverName).get();
            if (Objects.isNull(dp)) {
                throw new ServerException("未找到控制器资源，请检查服务名称是否正确!");
            }
            labels = dp.getMetadata().getLabels();

        } else if (ServerManageData.ControllerType.STATEFUL_SET.equals(controllerType)) {
            StatefulSet sts = k8sDeployService.client().apps().statefulSets().inNamespace(Constants.NAMESPACE_DEFAULT).withName(serverName).get();
            if (Objects.isNull(sts)) {
                throw new ServerException("未找到控制器资源，请检查服务名称是否正确!");
            }
            labels = sts.getMetadata().getLabels();
        }
        if (Objects.isNull(labels)) {
            return null;
        }
        return labels.get(ServerManageData.DEPLOY_TYPE);
    }


    final protected String getDistributeIp(String label) {
        StringBuilder distributeIp = new StringBuilder();
        List<com.xylink.manager.model.deploy.Node> nodeList = deployService.listNodesByAppLabel(label);
        for (com.xylink.manager.model.deploy.Node node : nodeList) {
            String ip = node.getIp();
            distributeIp.append(Ipv6Util.getHost(ip, node.getName())).append(",");
        }

        if (distributeIp.length() > 0) {
            distributeIp = new StringBuilder(distributeIp.substring(0, distributeIp.length() - 1));
        }

        return distributeIp.toString();
    }

    public Map<String, String> queryCm(String configmapName) {
        com.xylink.manager.model.deploy.ConfigMap data = deployService.getConfigMapByName(configmapName);
        if (Objects.isNull(data)) {
            log.info("queryCm not has cm:{}", configmapName);
            return Maps.newHashMap();
        }
        return data.getData();
    }

    public List<NodeToLabelDTO> queryNodeLabels(List<String> nodeIds) {
        List<NodeToLabelDTO> resultList = new ArrayList<>();
        List<com.xylink.manager.model.deploy.Node> nodes = k8sDeployService.listAllNodes();
        if (CollectionUtils.isEmpty(nodes)) {
            return Lists.newArrayList();
        }
        Map<String, Map<String, String>> nodeLabelMap = nodes.stream().collect(Collectors.toMap(x -> x.getName(), x -> x.getLabels()));
        for (String nodeId : nodeIds) {
            NodeToLabelDTO dto = new NodeToLabelDTO();
            dto.setNodeId(nodeId);
            dto.setDatas(nodeLabelMap.get(nodeId));
            resultList.add(dto);
        }
        return resultList;
    }
}