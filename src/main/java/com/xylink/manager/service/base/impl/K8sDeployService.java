package com.xylink.manager.service.base.impl;

import com.xylink.manager.controller.dto.inspect.SimpleListener;
import com.xylink.manager.model.deploy.*;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.model.deploy.Container;
import com.xylink.manager.model.deploy.Namespace;
import com.xylink.manager.model.deploy.Node;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.manager.service.base.K8sClientBuilder;
import com.xylink.util.MapDiffUtil;
import io.fabric8.kubernetes.api.model.*;
import io.fabric8.kubernetes.api.model.apps.DaemonSetBuilder;
import io.fabric8.kubernetes.api.model.apps.DeploymentBuilder;
import io.fabric8.kubernetes.api.model.batch.v1.JobBuilder;
import io.fabric8.kubernetes.api.model.batch.v1.JobStatus;
import io.fabric8.kubernetes.api.model.batch.v1beta1.CronJobBuilder;
import io.fabric8.kubernetes.client.KubernetesClient;
import io.fabric8.kubernetes.client.dsl.ExecWatch;
import io.fabric8.kubernetes.client.dsl.ScalableResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.yaml.snakeyaml.Yaml;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.io.OutputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Slf4j
public class K8sDeployService implements IDeployService {
    private static final Logger monitorLogger = LoggerFactory.getLogger("monitor");
    private final KubernetesClient client;

    public K8sDeployService(K8sClientBuilder clientBuilder) {
        this.client = clientBuilder.getClient();
    }

    /**
     * 包级别可见，对外不可见
     */
    KubernetesClient client() {
        return client;
    }

    @Nonnull
    @Override
    public List<Node> listAllNodes() {
        return client.nodes()
                .list()
                .getItems()
                .stream()
                .map(Node::buildNode)
                .collect(Collectors.toList());
    }

    @Nullable
    @Override
    public Node getNodeByName(@Nonnull String name) {
        io.fabric8.kubernetes.api.model.Node k8sNode = client.nodes().withName(name).get();
        if (k8sNode == null) {
            return null;
        }
        return Node.buildNode(k8sNode);
    }

    @Nonnull
    @Override
    public List<Node> listNodesByLabels(@Nonnull String key, @Nonnull String[] labels) {
        if (labels.length == 0) {
            return new ArrayList<>(0);
        }
        NodeList list;
        if (labels.length == 1) {
            list = client.nodes().withLabel(key, labels[0]).list();
        } else {
            list = client.nodes().withLabelIn(key, labels).list();
        }
        if (list == null || CollectionUtils.isEmpty(list.getItems())) {
            return new ArrayList<>(0);
        }
        return list.getItems().stream().map(Node::buildNode).collect(Collectors.toList());
    }

    @Override
    public void patchNodeLabels(@Nonnull String nodeName, @Nonnull Consumer<Map<String, String>> consumer) {
        client.nodes()
                .withName(nodeName)
                .edit(n -> {
                    Map<String, String> labels = n.getMetadata().getLabels();
                    if (labels == null) {
                        labels = new HashMap<>();
                    }
                    Map<String, String> oldMap = null;
                    if (monitorLogger.isDebugEnabled()) {
                        oldMap = new HashMap<>(labels);
                    }
                    consumer.accept(labels);
                    if (monitorLogger.isDebugEnabled()) {
                        monitorLogger.debug("patch node [{}] labels: {}", nodeName, MapDiffUtil.diff(oldMap, labels));
                    }
                    return new NodeBuilder(n).editMetadata().withLabels(labels).endMetadata().build();
                });
    }

    @Override
    public void addNodeLabels(@Nonnull String nodeName, @Nonnull Map<String, String> labels) {
        if (monitorLogger.isDebugEnabled()) {
            monitorLogger.debug("add node [{}] labels: {}", nodeName, labels);
        }
        client.nodes()
                .withName(nodeName)
                .edit(n -> new NodeBuilder(n)
                        .editMetadata()
                        .addToLabels(labels)
                        .endMetadata().build()
                );
    }

    @Override
    public void removeNodeLabels(@Nonnull String nodeName, @Nonnull String[] labelKeys) {
        if (monitorLogger.isDebugEnabled()) {
            monitorLogger.debug("remove node [{}] labelKeys: {}", nodeName, Arrays.toString(labelKeys));
        }
        client.nodes()
                .withName(nodeName)
                .edit(n -> {
                    NodeFluent.MetadataNested<NodeBuilder> editMetadata = new NodeBuilder(n).editMetadata();
                    for (String labelKey : labelKeys) {
                        editMetadata.removeFromLabels(labelKey);
                    }
                    return editMetadata.endMetadata().build();
                });
    }

    @Override
    public void removeNodeAppLabel(@Nullable String nodeName, @Nonnull String appName) {
        if (monitorLogger.isDebugEnabled()) {
            monitorLogger.debug("remove node [{}] labelKeys: {}", nodeName, appName);
        }
        listNodesByAppLabel(appName).stream()
                .filter(node -> !StringUtils.isNotBlank(nodeName) || StringUtils.equals(nodeName, node.getName()))
                .forEach(node -> client.nodes()
                        .withName(node.getName())
                        .edit(n -> new NodeBuilder(n)
                                .editMetadata()
                                .removeFromLabels(appName)
                                .endMetadata()
                                .build()));
    }

    @Override
    public void deleteNode(@Nonnull String nodeName) {
        if (monitorLogger.isDebugEnabled()) {
            monitorLogger.debug("delete node [{}]", nodeName);
        }
        client.nodes()
                .withName(nodeName)
                .delete();
    }

    @Override
    public void approveNodeCertificate(@Nonnull String nodeName) {
        String approveUsername = "system:node:" + nodeName;
        client.certificates()
                .v1()
                .certificateSigningRequests()
                .list()
                .getItems()
                .stream()
                .filter(it -> StringUtils.equals(it.getSpec().getUsername(), approveUsername) && StringUtils.isBlank(it.getStatus().getCertificate()))
                .forEach(request -> {
                    String name = request.getMetadata().getName();
                    log.info("approve certificate request: {}", name);
                    client.certificates().v1().certificateSigningRequests().withName(name).approve();
                });
    }

    @Nonnull
    @Override
    public List<Pod> listAllPod() {
        return client.pods()
                .list()
                .getItems()
                .stream()
                .map(Pod::buildPod)
                .collect(Collectors.toList());
    }

    @Nonnull
    @Override
    public List<Pod> listAllPodByNamespace(@Nonnull String namespace) {
        return client.pods()
                .inNamespace(namespace)
                .list()
                .getItems()
                .stream()
                .map(Pod::buildPod)
                .collect(Collectors.toList());
    }

    @Nonnull
    @Override
    public List<Pod> listPodsByNamespaceAndLabels(@Nonnull String namespace, @Nonnull String key, @Nonnull String[] labels) {
        if (labels.length == 0) {
            return new ArrayList<>(0);
        }
        if (labels.length == 1) {
            return client.pods()
                    .inNamespace(namespace)
                    .withLabel(key, labels[0])
                    .list()
                    .getItems()
                    .stream()
                    .map(Pod::buildPod)
                    .collect(Collectors.toList());
        } else {
            return client.pods()
                    .inNamespace(namespace)
                    .withLabelIn(key, labels)
                    .list()
                    .getItems()
                    .stream()
                    .map(Pod::buildPod)
                    .collect(Collectors.toList());
        }
    }

    @Nonnull
    @Override
    public List<Pod> listPodsByLabels(@Nonnull String key, @Nonnull String[] labels) {
        if (labels.length == 0) {
            return new ArrayList<>(0);
        }
        if (labels.length == 1) {
            return client.pods()
                    .withLabel(key, labels[0])
                    .list()
                    .getItems()
                    .stream()
                    .map(Pod::buildPod)
                    .collect(Collectors.toList());
        } else {
            return client.pods()
                    .withLabelIn(key, labels)
                    .list()
                    .getItems()
                    .stream()
                    .map(Pod::buildPod)
                    .collect(Collectors.toList());
        }
    }

    @Override
    public void deletePod(@Nonnull Pod pod) {
        if (monitorLogger.isDebugEnabled()) {
            monitorLogger.debug("delete pod [{}/{}]", pod.getNamespace(), pod.getPodName());
        }
        client.pods()
                .inNamespace(pod.getNamespace())
                .withName(pod.getPodName())
                .withGracePeriod(0)
                .delete();
    }

    @Override
    public void deletePodByName(@Nonnull String podName, @Nonnull String namespace) {
        if (monitorLogger.isDebugEnabled()) {
            monitorLogger.debug("delete pod [{}/{}]", namespace, podName);
        }
        client.pods()
                .inNamespace(namespace)
                .withName(podName)
                .delete();
    }

    @Nonnull
    @Override
    public Future<Void> executeCommandForPod(@Nonnull String podName,
                                             @Nonnull String namespace,
                                             @Nullable String containerName,
                                             @Nonnull OutputStream outputStream,
                                             @Nonnull OutputStream errorStream,
                                             @Nonnull String... command) {
        if (monitorLogger.isDebugEnabled()) {
            monitorLogger.debug("execute command for pod [{}/{}] in container [{}]: {}", namespace, podName, containerName, Arrays.toString(command));
        }
        CompletableFuture<Void> future = new CompletableFuture<>();
        ExecWatch watch = client.pods()
                .inNamespace(namespace)
                .withName(podName)
                .inContainer(containerName)
                .writingOutput(outputStream)
                .writingError(errorStream)
                .usingListener(new SimpleListener(future))
                .exec(command);
        //thenRun不管会不会报错都会执行，所以 watch 会被正常 close
        return future.thenRun(watch::close);
    }

    @Nullable
    @Override
    public ConfigMap getConfigMapByName(@Nonnull String name, @Nonnull String namespace) {
        io.fabric8.kubernetes.api.model.ConfigMap configMap = client.configMaps()
                .inNamespace(namespace)
                .withName(name)
                .get();
        if (configMap == null) {
            return null;
        }
        return ConfigMap.buildConfigMap(configMap);
    }

    @Override
    public void createEmptyConfigMap(@Nonnull String name, @Nonnull String namespace) {
        if (monitorLogger.isDebugEnabled()) {
            monitorLogger.debug("create empty configMap [{}/{}]", namespace, name);
        }
        client.configMaps()
                .inNamespace(namespace)
                .create(new ConfigMapBuilder()
                        .withNewMetadata()
                        .withName(name)
                        .withNamespace(namespace)
                        .endMetadata()
                        .build());
    }

    @Nonnull
    @Override
    public Map<String, String> patchConfigMap(@Nonnull String name,
                                              @Nonnull String namespace,
                                              @Nonnull Consumer<Map<String, String>> consumer) {
        ConfigMap configMap = getConfigMapByName(name, namespace);
        Map<String, String> map;
        if (configMap == null) {
            map = new LinkedHashMap<>();
        } else {
            map = configMap.getData();
        }
        Map<String, String> oldMap = null;
        if (monitorLogger.isDebugEnabled()) {
            oldMap = new HashMap<>(map);
        }
        consumer.accept(map);
        if (monitorLogger.isDebugEnabled()) {
            monitorLogger.debug("patch config map [{}/{}]: {}", namespace, name, MapDiffUtil.diff(oldMap, map));
        }
        if (configMap == null) {
            client.configMaps()
                    .create(new ConfigMapBuilder()
                            .withNewMetadata()
                            .withName(name)
                            .withNamespace(namespace)
                            .endMetadata()
                            .withData(CollectionUtils.isEmpty(map) ? null : map)
                            .build());
        } else {
            client.configMaps()
                    .inNamespace(namespace)
                    .withName(name)
                    .edit(c -> new ConfigMapBuilder(c)
                            .withData(CollectionUtils.isEmpty(map) ? null : map)
                            .build());
        }
        return map;
    }

    /**
     * 重新构建configmap,覆盖原数据
     *
     * @param namespace
     * @param name
     * @param data
     */
    public void rebuildConfigMap(String namespace, String name, Map<String, String> data) {
        try {
            client.resource(new ConfigMapBuilder()
                    .withNewMetadata()
                    .withName(name)
                    .withNamespace(namespace)
                    .endMetadata()
                    .addToData(data)
                    .build()).createOrReplace();
        } catch (Exception e) {
            client.configMaps()
                    .inNamespace(namespace)
                    .withName(name).edit(c ->
                    new ConfigMapBuilder()
                            .withNewMetadata().withName(name).endMetadata().addToData(data).build()
            );
        }
    }

    @Nonnull
    @Override
    public Map<String, String> patchConfigMapByBinaryData(@Nonnull String name,
                                                          @Nonnull String namespace,
                                                          @Nonnull Consumer<Map<String, String>> consumer) {
        if (monitorLogger.isDebugEnabled()) {
            monitorLogger.debug("patch config map [{}/{}] by binary data", namespace, name);
        }
        ConfigMap configMap = getConfigMapByName(name, namespace);
        Map<String, String> map;
        if (configMap == null) {
            map = new LinkedHashMap<>();
        } else {
            map = configMap.getData();
        }
        Map<String, String> oldMap = null;
        if (monitorLogger.isDebugEnabled()) {
            oldMap = new HashMap<>(map);
        }
        consumer.accept(map);
        if (monitorLogger.isDebugEnabled()) {
            monitorLogger.debug("patch config map [{}/{}] with binary data: {}", namespace, name, MapDiffUtil.diff(oldMap, map).toStringWithoutValue());
        }
        if (configMap == null) {
            client.configMaps()
                    .create(new ConfigMapBuilder()
                            .withNewMetadata()
                            .withName(name)
                            .withNamespace(namespace)
                            .endMetadata()
                            .withBinaryData(CollectionUtils.isEmpty(map) ? null : map)
                            .build());
        } else {
            client.configMaps()
                    .inNamespace(namespace)
                    .withName(name)
                    .edit(c -> new ConfigMapBuilder(c)
                            .withBinaryData(CollectionUtils.isEmpty(map) ? null : map)
                            .build());
        }
        return map;
    }

    @Override
    public void deleteConfigMap(@Nonnull String name, @Nonnull String namespace) {
        if (monitorLogger.isDebugEnabled()) {
            monitorLogger.debug("delete configMap [{}/{}]", namespace, name);
        }
        client.configMaps()
                .inNamespace(namespace)
                .withName(name)
                .delete();
    }

    @NotNull
    @Override
    public List<Job> listAllJob(@Nonnull String namespace) {
        return client.batch().v1().jobs().inNamespace(namespace).list().getItems().stream().map(Job::buildJob).collect(Collectors.toList());
    }

    @Nonnull
    @Override
    public Job createOrReplaceJobByYaml(@Nonnull String yaml) {
        Yaml y = new Yaml();
        io.fabric8.kubernetes.api.model.batch.v1.Job job = y.loadAs(yaml, io.fabric8.kubernetes.api.model.batch.v1.Job.class);
        job = client.batch().v1().jobs().createOrReplace(new JobBuilder(job).build());
        return Job.buildJob(job);
    }

    @Nullable
    @Override
    public Job createJobFromCronJob(@Nonnull String cronJobName, @Nonnull String jobName, @Nonnull String namespace) {
        // First try v1beta1 API
        io.fabric8.kubernetes.api.model.batch.v1beta1.CronJob cronJobV1Beta1 = client.batch().v1beta1().cronjobs().inNamespace(namespace).withName(cronJobName).get();
        io.fabric8.kubernetes.api.model.batch.v1.Job manualJob;

        if (cronJobV1Beta1 != null) {
            manualJob = new JobBuilder()
                    .withNewMetadata()
                    .withName(jobName)
                    .addToLabels("triggered-by", "manual")
                    .endMetadata()
                    .withSpec(cronJobV1Beta1.getSpec().getJobTemplate().getSpec())
                    .build();
        } else {
            // If not found, try v1 API as fallback
            if (monitorLogger.isDebugEnabled()) {
                monitorLogger.debug("CronJob [{}] not found in v1beta1 API, trying v1 API for job creation", cronJobName);
            }
            io.fabric8.kubernetes.api.model.batch.v1.CronJob cronJobV1 = client.batch().v1().cronjobs().inNamespace(namespace).withName(cronJobName).get();
            if (cronJobV1 != null) {
                if (monitorLogger.isDebugEnabled()) {
                    monitorLogger.debug("CronJob [{}] found in v1 API for job creation", cronJobName);
                }
                manualJob = new JobBuilder()
                        .withNewMetadata()
                        .withName(jobName)
                        .addToLabels("triggered-by", "manual")
                        .endMetadata()
                        .withSpec(cronJobV1.getSpec().getJobTemplate().getSpec())
                        .build();
            } else {
                return null;
            }
        }

        client.batch().v1().jobs()
                .inNamespace(namespace)
                .createOrReplace(manualJob);

        return getJobByName(jobName, namespace);
    }

    @Nullable
    @Override
    public Job getJobByName(@Nonnull String name, @Nonnull String namespace) {
        io.fabric8.kubernetes.api.model.batch.v1.Job job = client.batch().v1().jobs().inNamespace(namespace).withName(name).get();
        if (job == null) {
            return null;
        }
        return Job.buildJob(job);
    }

    @Nonnull
    @Override
    public List<Job> listJobsByNamespace(@Nonnull String namespace) {
        return client.batch().v1()
                .jobs()
                .inNamespace(namespace)
                .list()
                .getItems()
                .stream()
                .map(Job::buildJob)
                .collect(Collectors.toList());
    }

    @Override
    public void deleteJob(@Nonnull String jobName, @Nonnull String namespace) {
        if (monitorLogger.isDebugEnabled()) {
            monitorLogger.debug("delete job [{}/{}]", namespace, jobName);
        }
        client.batch().v1().jobs().inNamespace(namespace).withName(jobName).delete();
    }

    @Override
    public void restartJob(@Nonnull String jobName, @Nonnull String namespace) {
        ScalableResource<io.fabric8.kubernetes.api.model.batch.v1.Job> resource = client.batch().v1().jobs().inNamespace(namespace).withName(jobName);
        io.fabric8.kubernetes.api.model.batch.v1.Job job = resource.get();
        resource.delete();

        ObjectMeta meta = new ObjectMeta();
        meta.setName(jobName);
        job.setMetadata(meta);
        job.setStatus(new JobStatus());

        job.getSpec().getSelector().setMatchLabels(new HashMap<>());
        job.getSpec().getTemplate().getMetadata().getLabels().remove("controller-uid");
        job.getSpec().getTemplate().getMetadata().getLabels().remove("batch.kubernetes.io/controller-uid");
        job.getSpec().getTemplate().getMetadata().getLabels().remove("batch.kubernetes.io/job-name");
        resource.create(job);
    }

    @Nullable
    @Override
    public CronJob getCronJobByName(@Nonnull String name, @Nonnull String namespace) {
        // First try v1beta1 API
        io.fabric8.kubernetes.api.model.batch.v1beta1.CronJob cronJobV1Beta1 = client.batch().v1beta1().cronjobs().inNamespace(namespace).withName(name).get();
        if (cronJobV1Beta1 != null) {
            return CronJob.buildJob(cronJobV1Beta1);
        }

        // If not found, try v1 API as fallback
        if (monitorLogger.isDebugEnabled()) {
            monitorLogger.debug("CronJob [{}] not found in v1beta1 API, trying v1 API", name);
        }
        io.fabric8.kubernetes.api.model.batch.v1.CronJob cronJobV1 = client.batch().v1().cronjobs().inNamespace(namespace).withName(name).get();
        if (cronJobV1 != null) {
            if (monitorLogger.isDebugEnabled()) {
                monitorLogger.debug("CronJob [{}] found in v1 API", name);
            }
            return CronJob.buildJob(cronJobV1);
        }

        return null;
    }

    @Override
    public void patchCronJobCommand(@Nonnull String cronJobName, @Nonnull String namespace, @Nonnull List<Container> containers) {
        if (monitorLogger.isDebugEnabled()) {
            Map<String, String> containerCommandMap = new HashMap<>(containers.size());
            for (Container container : containers) {
                containerCommandMap.put(container.getName(), StringUtils.join(container.getCommand(), " "));
            }
            monitorLogger.debug("patch cron job [{}/{}] command: {}", namespace, cronJobName, containerCommandMap);
        }
        Map<String, List<String>> containerCommandMap = new HashMap<>(containers.size());
        for (Container container : containers) {
            containerCommandMap.put(container.getName(), container.getCommand());
        }

        // First try v1beta1 API
        try {
            client.batch()
                    .v1beta1()
                    .cronjobs()
                    .inNamespace(namespace)
                    .withName(cronJobName)
                    .edit(cj -> {
                                cj.getSpec()
                                        .getJobTemplate()
                                        .getSpec()
                                        .getTemplate()
                                        .getSpec()
                                        .getContainers()
                                        .forEach(c -> {
                                            List<String> command = containerCommandMap.get(c.getName());
                                            if (command != null) {
                                                c.setCommand(command);
                                            }
                                        });
                                return new CronJobBuilder(cj).build();
                            }
                    );
        } catch (Exception e) {
            // If v1beta1 fails, try v1 API as fallback
            if (monitorLogger.isDebugEnabled()) {
                monitorLogger.debug("Failed to patch CronJob [{}] using v1beta1 API, trying v1 API: {}", cronJobName, e.getMessage());
            }
            client.batch()
                    .v1()
                    .cronjobs()
                    .inNamespace(namespace)
                    .withName(cronJobName)
                    .edit(cj -> {
                                cj.getSpec()
                                        .getJobTemplate()
                                        .getSpec()
                                        .getTemplate()
                                        .getSpec()
                                        .getContainers()
                                        .forEach(c -> {
                                            List<String> command = containerCommandMap.get(c.getName());
                                            if (command != null) {
                                                c.setCommand(command);
                                            }
                                        });
                                return cj;
                            }
                    );
        }
    }

    @Nullable
    @Override
    public SVC getSvcByName(@Nonnull String name, @Nonnull String namespace) {
        io.fabric8.kubernetes.api.model.Service service = client.services()
                .inNamespace(namespace)
                .withName(name)
                .get();
        if (service == null) {
            return null;
        }
        return SVC.buildSVC(service);
    }

    @Nonnull
    @Override
    public List<DaemonSet> listDaemonSetByNamespace(@Nonnull String namespace) {
        return client.apps()
                .daemonSets()
                .inNamespace(namespace)
                .list()
                .getItems()
                .stream()
                .map(DaemonSet::buildDaemonSet)
                .collect(Collectors.toList());
    }

    @Nonnull
    @Override
    public List<DaemonSet> listDaemonSetByNamespaceAndLabels(@Nonnull String namespace, @Nonnull String key, @Nonnull String[] labels) {
        if (labels.length == 0) {
            return new ArrayList<>(0);
        }
        if (labels.length == 1) {
            return client.apps()
                    .daemonSets()
                    .inNamespace(namespace)
                    .withLabel(key, labels[0])
                    .list()
                    .getItems()
                    .stream()
                    .map(DaemonSet::buildDaemonSet)
                    .collect(Collectors.toList());
        } else {
            return client.apps()
                    .daemonSets()
                    .inNamespace(namespace)
                    .withLabelIn(key, labels)
                    .list()
                    .getItems()
                    .stream()
                    .map(DaemonSet::buildDaemonSet)
                    .collect(Collectors.toList());
        }
    }

    @Nullable
    @Override
    public DaemonSet getDaemonSetByName(@Nonnull String name, @Nonnull String namespace) {
        io.fabric8.kubernetes.api.model.apps.DaemonSet daemonSet = client.apps().daemonSets()
                .inNamespace(namespace)
                .withName(name)
                .get();
        if (daemonSet == null) {
            return null;
        }
        return DaemonSet.buildDaemonSet(daemonSet);
    }

    @Override
    public void patchDaemonSetEnv(@Nonnull String name, @Nonnull String namespace, @Nonnull List<Container> containers) {
        Map<String, Map<String, String>> containerEnvMap = new HashMap<>(containers.size());
        for (Container container : containers) {
            containerEnvMap.put(container.getName(), container.getEnv());
        }
        if (monitorLogger.isDebugEnabled()) {
            monitorLogger.debug("patch daemonset [{}/{}] env: {}", namespace, name, containerEnvMap);
        }
        client.apps()
                .daemonSets()
                .inNamespace(namespace)
                .withName(name)
                .edit(ds -> {
                    ds.getSpec()
                            .getTemplate()
                            .getSpec()
                            .getContainers()
                            .forEach(c -> {
                                Map<String, String> envVarMap = containerEnvMap.get(c.getName());
                                if (envVarMap != null) {
                                    for (EnvVar envVar : c.getEnv()) {
                                        String key = envVar.getName();
                                        if (envVarMap.containsKey(key)) {
                                            envVar.setValue(envVarMap.get(key));
                                        }
                                    }
                                }
                            });
                    return new DaemonSetBuilder(ds).build();
                });
    }

    @Override
    public void patchDaemonSetImage(@Nonnull String name,
                                    @Nonnull String namespace,
                                    @Nonnull List<Container> containers,
                                    @Nonnull List<Container> initContainers) {
        Map<String, String> containerImageMap = new HashMap<>(containers.size());
        Map<String, String> initContainerImageMap = new HashMap<>(initContainers.size());
        for (Container c : containers) {
            containerImageMap.put(c.getName(), c.getImage());
        }
        for (Container c : initContainers) {
            initContainerImageMap.put(c.getName(), c.getImage());
        }
        if (monitorLogger.isDebugEnabled()) {
            monitorLogger.debug("patch daemonset [{}/{}] image: container: {}, initContainer: {}", namespace, name, containerImageMap, initContainerImageMap);
        }
        client.apps()
                .daemonSets()
                .inNamespace(namespace)
                .withName(name)
                .edit(ds -> {
                    ds.getSpec()
                            .getTemplate()
                            .getSpec()
                            .getContainers()
                            .forEach(c -> {
                                String image = containerImageMap.get(c.getName());
                                if (image != null) {
                                    c.setImage(image);
                                }
                            });
                    ds.getSpec()
                            .getTemplate()
                            .getSpec()
                            .getInitContainers()
                            .forEach(c -> {
                                String image = initContainerImageMap.get(c.getName());
                                if (image != null) {
                                    c.setImage(image);
                                }
                            });
                    return new DaemonSetBuilder(ds).build();
                });
    }

    @Override
    public void deleteDaemonSet(@Nonnull String name, @Nonnull String namespace) {
        if (monitorLogger.isDebugEnabled()) {
            monitorLogger.debug("delete daemonset [{}/{}]", namespace, name);
        }
        client.apps().daemonSets().inNamespace(namespace).withName(name).delete();
    }

    @Nonnull
    @Override
    public List<StatefulSet> listStatefulSetByNamespace(@Nonnull String namespace) {
        return client.apps()
                .statefulSets()
                .inNamespace(namespace)
                .list()
                .getItems()
                .stream()
                .map(StatefulSet::buildStatefulSet)
                .collect(Collectors.toList());
    }

    @Nonnull
    @Override
    public List<Deployment> listDeploymentByNamespace(@Nonnull String namespace) {
        return client.apps()
                .deployments()
                .inNamespace(namespace)
                .list()
                .getItems()
                .stream()
                .map(Deployment::buildDeployment)
                .collect(Collectors.toList());
    }

    @Nonnull
    @Override
    public List<Deployment> listDeploymentByNamespaceAndLabels(@Nonnull String namespace, @Nonnull String key, @Nonnull String[] labels) {
        if (labels.length == 0) {
            return new ArrayList<>(0);
        }
        if (labels.length == 1) {
            return client.apps()
                    .deployments()
                    .inNamespace(namespace)
                    .withLabel(key, labels[0])
                    .list()
                    .getItems()
                    .stream()
                    .map(Deployment::buildDeployment)
                    .collect(Collectors.toList());
        } else {
            return client.apps()
                    .deployments()
                    .inNamespace(namespace)
                    .withLabelIn(key, labels)
                    .list()
                    .getItems()
                    .stream()
                    .map(Deployment::buildDeployment)
                    .collect(Collectors.toList());
        }
    }

    @Nullable
    @Override
    public Deployment getDeploymentByName(@Nonnull String name, @Nonnull String namespace) {
        io.fabric8.kubernetes.api.model.apps.Deployment deployment = client.apps()
                .deployments()
                .inNamespace(namespace)
                .withName(name)
                .get();
        if (deployment == null) {
            return null;
        }
        return Deployment.buildDeployment(deployment);
    }

    @Override
    public void scaleDeployment(@Nonnull String deploymentName, @Nonnull String namespace, int replicas) {
        if (monitorLogger.isDebugEnabled()) {
            monitorLogger.debug("scale deployment [{}/{}] to {} replicas", namespace, deploymentName, replicas);
        }
        client.apps()
                .deployments()
                .inNamespace(namespace)
                .withName(deploymentName)
                .scale(replicas, true);
    }

    @Override
    public void patchDeploymentImage(@Nonnull String name, @Nonnull String namespace, @Nonnull List<Container> containers, @Nonnull List<Container> initContainers) {
        Map<String, String> containerImageMap = new HashMap<>(containers.size());
        Map<String, String> initContainerImageMap = new HashMap<>(initContainers.size());
        for (Container c : containers) {
            containerImageMap.put(c.getName(), c.getImage());
        }
        for (Container c : initContainers) {
            initContainerImageMap.put(c.getName(), c.getImage());
        }
        if (monitorLogger.isDebugEnabled()) {
            monitorLogger.debug("patch deployment [{}/{}] image: container: {}, initContainer: {}", namespace, name, containerImageMap, initContainerImageMap);
        }
        client.apps()
                .deployments()
                .inNamespace(namespace)
                .withName(name)
                .edit(ds -> {
                    ds.getSpec()
                            .getTemplate()
                            .getSpec()
                            .getContainers()
                            .forEach(c -> {
                                String image = containerImageMap.get(c.getName());
                                if (image != null) {
                                    c.setImage(image);
                                }
                            });
                    ds.getSpec()
                            .getTemplate()
                            .getSpec()
                            .getInitContainers()
                            .forEach(c -> {
                                String image = initContainerImageMap.get(c.getName());
                                if (image != null) {
                                    c.setImage(image);
                                }
                            });
                    return new DeploymentBuilder(ds).build();
                });
    }

    @Nullable
    @Override
    public Namespace getNamespaceByName(@Nonnull String name) {
        io.fabric8.kubernetes.api.model.Namespace namespace = client.namespaces()
                .withName(name)
                .get();
        return Namespace.buildNamespace(namespace);
    }

    @Override
    public void watchPodLog(@Nonnull String podName,
                            @Nonnull String namespace,
                            @Nullable String containerName,
                            @Nonnull OutputStream outputStream) {
        if (containerName == null) {
            client.pods()
                    .inNamespace(namespace)
                    .withName(podName)
                    .watchLog(outputStream);
        } else {
            client.pods()
                    .inNamespace(namespace)
                    .withName(podName)
                    .inContainer(containerName)
                    .watchLog(outputStream);
        }
    }
}
