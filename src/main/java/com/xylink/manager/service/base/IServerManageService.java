package com.xylink.manager.service.base;

import com.xylink.manager.controller.dto.servicemanage.*;
import com.xylink.manager.model.common.Page;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> create on 2025/4/30
 */
public interface IServerManageService {

    /**
     * 查询服务部署列表
     */
    Page<ServerInfoVO> serverList(ServerListParam param);

    /**
     * 服务实例列表
     */
    Page<ServerInstanceInfoVO> serverInstanceList(ServerInstanceListParam param);

    /**
     * 重启服务实例
     */
    void restartInstances(List<String> instanceNames);

    /**
     * 重启服务
     */
    void restartServers(List<String> serverNames);

    /**
     * 取消部署
     */
    void cancelDeploy(String serverName, String controllerType);

    /**
     * 服务部署
     */
    void deploy(ServerDeployInfo param);

    /**
     * 查询部署信息
     */
    List<DeployNode> deployNodes(String serverName);

    /**
     * 服务部署-外部工具调用
     */
    void deployExternal(ServerDeployExternalParam param);

    /**
     * 编辑configmap
     */
    void editCm(EditCmParam param);


    /**
     * 查询configmap
     */
    Map<String, String> queryCm(String configmapName);

    /**
     * 为node打标签
     */
    void labelToNode(NodeToLabelDTO param);

    /**
     * 查询部署时选择的node列表
     */
    Page<DeployNodeExtend> nodeList(Integer current, Integer pageSize, String keywords);

    /**
     * 部署工具-增量部署（不清理原节点部署）
     */
    void incrementDeploy(IncrementDeployParam param);

    /**
     * 部署工具-查询node对应的标签数据
     */
    List<NodeToLabelDTO> queryNodeLabels(List<String> nodeIds);
}
