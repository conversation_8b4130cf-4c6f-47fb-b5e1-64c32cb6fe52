package com.xylink.manager.service.base.impl;

import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.service.api.NoahApiService;
import com.xylink.manager.service.base.K8sClientBuilder;
import com.xylink.util.SpringBeanUtil;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Nonnull;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.function.Consumer;

@Slf4j
public class K8sWithNoahDeployService extends K8sDeployService {
//    private static final List<String> NOAH_CONFIG_NAMES = Lists.newArrayList(
//            "cloudproxy-config",
//            "dmcu-dispatch",
//            "domain-filter",
//            "mc-client-score",
//            "mc-server-score",
//            "mcaccess-config",
//            "ntp-cnf",
//            "private-client-agreement"
//    );

    public K8sWithNoahDeployService(K8sClientBuilder clientBuilder) {
        super(clientBuilder);
    }

    private NoahApiService noahApiService() {
        return SpringBeanUtil.getBean(NoahApiService.class);
    }

    @Override
    @Nonnull
    public Map<String, String> patchConfigMap(@Nonnull String name, @Nonnull String namespace, @Nonnull Consumer<Map<String, String>> consumer) {
//        if (!NOAH_CONFIG_NAMES.contains(name)) {
//            log.info("patch config map by k8s, name: {}, namespace: {}", name, namespace);
//            return super.patchConfigMap(name, namespace, consumer);
//        }
        log.info("patch config map by noah, name: {}, namespace: {}", name, namespace);
        ConfigMap configMap = getConfigMapByName(name, namespace);
        Map<String, String> map;
        if (configMap == null) {
            map = new LinkedHashMap<>();
        } else {
            map = configMap.getData();
        }
        consumer.accept(map);
        NoahApiService.ProxyMetadata metadata = new NoahApiService.ProxyMetadata();
        metadata.setName(name);
        metadata.setNamespace(namespace);
        NoahApiService.ProxyConfigMap proxyConfigMap = new NoahApiService.ProxyConfigMap();
        proxyConfigMap.setMetadata(metadata);
        proxyConfigMap.setData(map);
        noahApiService().proxyUpdateConfigMap(proxyConfigMap);
        return map;
    }
}
