package com.xylink.manager.service.base;

import com.xylink.config.Constants;
import com.xylink.manager.model.em.SystemMode;
import io.fabric8.kubernetes.api.model.ConfigMap;
import io.fabric8.kubernetes.client.KubernetesClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/9/17 2:30 下午
 */
@Component
@Slf4j
public class SystemModeConfig {
    //必须获取这个对象，IDeployService也依赖该类进行环境判断
    private static KubernetesClient client;

    @Resource
    private K8sClientBuilder clientBuilder;

    @PostConstruct
    public void init() {
        client = clientBuilder.getClient();
    }

    private static final String MODE = System.getProperty("systemMode");

    /**
     * 获取当前系统模式，根据配置的 `systemMode` 系统属性或 Kubernetes ConfigMap 配置获取系统运行模式。
     * 当未配置或无法解析时，将返回默认模式 `SystemMode.classic`。
     * <p>
     * 备注：如果需要支持无k8s的环境，则该方法需要try catch用于异常处理
     *
     * @return 当前系统运行模式，返回值可能为 `SystemMode.classic`、`SystemMode.cms` 或其他已定义的 `SystemMode` 枚举值
     */
    public static SystemMode current() {
        ConfigMap configMap = client.configMaps().inNamespace(Constants.NAMESPACE_DEFAULT).withName(Constants.CONFIGMAP_CUSTOMIZE_INSTALL).get();
        Map<String, String> installMap = configMap == null ? new HashMap<>(0) : configMap.getData();
        String deployType = installMap.get(Constants.DEPLOY_TYPE);
        if (deployType != null && SystemMode.cms.name().equalsIgnoreCase(deployType.trim())) {
            return SystemMode.cms;
        }

        if (productNameIsCmsOrXms() && isPrivate56()) {
            return SystemMode.cms;
        }

        SystemMode systemMode = EnumUtils.getEnum(SystemMode.class, MODE);
        return systemMode == null ? SystemMode.classic : systemMode;
    }

    public static boolean isCmsOrXms() {
        return SystemMode.cms.equals(current()) || productNameIsCmsOrXms();
    }

    public static boolean productNameIsCmsOrXms() {
        String productName = productName();
        return !StringUtils.isBlank(productName) && (productName.contains("CMS") || productName.contains("XMS"));
    }

    public static String productName() {
        String productName = null;
        try {
            // 先检查是否存在 dmidecode 命令
            ProcessBuilder whichPb = new ProcessBuilder("which", "dmidecode");
            Process whichProcess = whichPb.start();
            int exitCode = whichProcess.waitFor();
            if (exitCode != 0) {
                log.info("dmidecode command not found.");
                return "";
            }
            List<String> command = Arrays.asList("dmidecode", "-t", "system");
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            Process process = processBuilder.start();
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            boolean productNameFound = false;

            while ((line = reader.readLine()) != null) {
                if (line.contains("Product Name:")) {
                    productNameFound = true;
                    productName = line.split("\\s*:\\s*", 2)[1];
                    break;
                }
            }
            if (!productNameFound) {
                log.info("Product Name not found.");
            }
            reader.close();
            process.waitFor();
        } catch (IOException | InterruptedException e) {
            log.error("dmidecode error.", e);
        }
        return productName;
    }

    public static boolean isOldCms() {
        SystemMode systemMode = EnumUtils.getEnum(SystemMode.class, MODE);
        SystemMode model = systemMode == null ? SystemMode.classic : systemMode;
        return SystemMode.cms.equals(model);
    }

    public static SystemMode currentNew() {
        //1220去kvm版本
        ConfigMap configMap = client.configMaps().inNamespace(Constants.NAMESPACE_DEFAULT).withName(Constants.CONFIGMAP_CUSTOMIZE_INSTALL).get();
        Map<String, String> installMap = configMap == null ? new HashMap<>(0) : configMap.getData();
        String deployType = installMap.get(Constants.DEPLOY_TYPE);
        if (deployType != null && SystemMode.cms.name().equalsIgnoreCase(deployType.trim())) {
            return SystemMode.cms;
        }
        if (deployType != null && SystemMode.private_56.name().equalsIgnoreCase(deployType.trim())) {
            return SystemMode.private_56;
        }
        return SystemMode.classic;
    }

    public static boolean isNewCms() {
        //1220去kvm版本
        return SystemMode.cms.equals(currentNew())
                //5.6的cms或xms
                || (SystemMode.private_56.equals(currentNew()) && productNameIsCmsOrXms());
    }

    public static boolean isPrivate56() {
        //1220去kvm版本
        return SystemMode.private_56.equals(currentNew());
    }
}
