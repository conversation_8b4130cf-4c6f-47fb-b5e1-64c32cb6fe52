package com.xylink.manager.service.base.impl;

import com.google.common.base.Stopwatch;
import com.xylink.config.Constants;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.controller.dto.servicemanage.*;
import com.xylink.manager.model.common.Page;
import com.xylink.manager.service.base.IServerManageService;
import com.xylink.util.LocalCacheUtil;
import io.fabric8.kubernetes.api.model.*;
import io.fabric8.kubernetes.api.model.apps.DaemonSet;
import io.fabric8.kubernetes.api.model.apps.Deployment;
import io.fabric8.kubernetes.api.model.apps.StatefulSet;
import io.fabric8.kubernetes.client.KubernetesClient;
import io.fabric8.kubernetes.client.dsl.AppsAPIGroupDSL;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> create on 2025/4/30
 */
@Slf4j
public class K8sServerManageServiceImpl implements IServerManageService {

    @Autowired
    private K8sDeployService k8sDeployService;

    @Autowired
    private ServerManageCommonService commonService;

    @Override
    public Page<ServerInfoVO> serverList(ServerListParam param) {
        List<ServerInfoVO> resultList = (List<ServerInfoVO>) LocalCacheUtil.get(ServerManageData.SERVER_LIST_CACHE_KEY);
        if (Objects.nonNull(resultList)) {
            log.info("get serverList from cache data");
            return commonService.buildServerList(resultList, param);
        } else {
            resultList = new ArrayList<>();
        }
        Stopwatch stopwatch = Stopwatch.createUnstarted();
        stopwatch.start();
        KubernetesClient client = k8sDeployService.client();
        AppsAPIGroupDSL apps = client.apps();
        List<Deployment> deployments = apps.deployments().list().getItems();
        List<DaemonSet> daemonSets = apps.daemonSets().list().getItems();
        List<StatefulSet> statefulSets = apps.statefulSets().list().getItems();
        List<io.fabric8.kubernetes.api.model.Pod> pods = k8sDeployService.client().pods().list().getItems();
        stopwatch.stop();
        long costMills = stopwatch.elapsed(TimeUnit.MILLISECONDS);
        log.info("serverList time resource and pods cost mills:{}", costMills);
        stopwatch.reset().start();
        for (Deployment dp : deployments) {
            ServerInfoVO vo = new ServerInfoVO();
            String controllerName = dp.getMetadata().getName();
            vo.setServerName(controllerName);
            vo.setStatus(commonService.getServerStatus(controllerName, pods));
            vo.setReplica(commonService.getControllerPods(controllerName, pods).size());
            vo.setDeployType(getDeployType(dp));
            vo.setCodeVersion(commonService.getCodeVersion(dp.getSpec().getTemplate()));
            vo.setControllerType(ServerManageData.ControllerType.DEPLOYMENT);
            resultList.add(vo);
        }
        for (DaemonSet ds : daemonSets) {
            ServerInfoVO vo = new ServerInfoVO();
            String controllerName = ds.getMetadata().getName();
            vo.setServerName(controllerName);
            vo.setStatus(commonService.getServerStatus(controllerName, pods));
            vo.setReplica(commonService.getControllerPods(controllerName, pods).size());
            vo.setDeployType(getDeployType(ds));
            vo.setCodeVersion(commonService.getCodeVersion(ds.getSpec().getTemplate()));
            vo.setControllerType(ServerManageData.ControllerType.DAEMON_SET);
            resultList.add(vo);
        }
        for (StatefulSet sts : statefulSets) {
            ServerInfoVO vo = new ServerInfoVO();
            String controllerName = sts.getMetadata().getName();
            vo.setServerName(controllerName);
            vo.setStatus(commonService.getServerStatus(controllerName, pods));
            vo.setReplica(commonService.getControllerPods(controllerName, pods).size());
            vo.setCodeVersion(commonService.getCodeVersion(sts.getSpec().getTemplate()));
            vo.setControllerType(ServerManageData.ControllerType.STATEFUL_SET);
            resultList.add(vo);
        }
        stopwatch.stop();
        costMills = stopwatch.elapsed(TimeUnit.MILLISECONDS);
        log.info("serverList time client get pods cost mills:{}", costMills);
        //设置到缓存
        LocalCacheUtil.put(ServerManageData.SERVER_LIST_CACHE_KEY, resultList);
        stopwatch.reset().start();
        Page<ServerInfoVO> r = commonService.buildServerList(resultList, param);
        stopwatch.stop();
        costMills = stopwatch.elapsed(TimeUnit.MILLISECONDS);
        log.info("serverList time build return List cost mills:{}", costMills);
        return r;
    }

    @Override
    public Page<ServerInstanceInfoVO> serverInstanceList(ServerInstanceListParam param) {
        return commonService.serverInstanceList(param);
    }


    /**
     * 获取部署类型
     * random_drift_node-随机漂移
     * fixed_node-固定节点
     * monopolize_node-独占节点
     */
    private String getDeployType(HasMetadata resource) {
        final ObjectMeta metadata = resource.getMetadata();
        final Map<String, String> labels = metadata.getLabels();
        if (Objects.isNull(labels)) {
            return null;
        }
        return labels.get(ServerManageData.DEPLOY_TYPE);
    }

    @Override
    public void restartInstances(List<String> instanceNames) {
        commonService.restartPods(instanceNames);
    }

    @Override
    public void restartServers(List<String> serverNames) {
        commonService.restartServers(serverNames);
    }

    @Override
    public void cancelDeploy(String serverName, String controllerType) {
        if (StringUtils.isBlank(serverName) || StringUtils.isBlank(controllerType)) {
            throw new ServerException("参数有误");
        }
        if (commonService.isNotDeploySupport(serverName)) {
            throw new ServerException("不允许操作服务[" + serverName + "]");
        }
        //修改控制器的deploy_type
        switch (controllerType) {
            case ServerManageData.ControllerType.DAEMON_SET:
                DaemonSet ds = k8sDeployService.client().apps().daemonSets().inNamespace(Constants.NAMESPACE_DEFAULT).withName(serverName).get();
                if (Objects.isNull(ds)) {
                    throw new ServerException("参数有误，请检查[DaemonSet]是否存在");
                }
                buildCancelDeploy(serverName, ds);
                k8sDeployService.client().apps().daemonSets().inNamespace(Constants.NAMESPACE_DEFAULT).withName(serverName).replace(ds);
                break;
            case ServerManageData.ControllerType.DEPLOYMENT:
                Deployment dm = k8sDeployService.client().apps().deployments().inNamespace(Constants.NAMESPACE_DEFAULT).withName(serverName).get();
                if (Objects.isNull(dm)) {
                    throw new ServerException("参数有误，请检查[Deployment]是否存在");
                }
                buildCancelDeploy(serverName, dm);
                k8sDeployService.client().apps().deployments().inNamespace(Constants.NAMESPACE_DEFAULT).withName(serverName).replace(dm);
                break;
            case ServerManageData.ControllerType.STATEFUL_SET:
                StatefulSet sts = k8sDeployService.client().apps().statefulSets().inNamespace(Constants.NAMESPACE_DEFAULT).withName(serverName).get();
                if (Objects.isNull(sts)) {
                    throw new ServerException("参数有误，请检查[StatefulSet]是否存在");
                }
                buildCancelDeploy(serverName, sts);
                k8sDeployService.client().apps().statefulSets().inNamespace(Constants.NAMESPACE_DEFAULT).withName(serverName).replace(sts);
                break;
            default:
                throw new ServerException("参数有误");
        }
        Map<String, Node> clusterNodes = commonService.getClusterNodes();
        //移除node的标签、污点
        commonService.cancelDeployForNode(serverName, clusterNodes);
        LocalCacheUtil.remove(ServerManageData.SERVER_LIST_CACHE_KEY);
        LocalCacheUtil.remove(ServerManageData.SERVER_INSTANCE_LIST_CACHE_KEY);
    }

    private void buildCancelDeploy(String serverName, HasMetadata hasMetadata) {
        Map<String, String> labels = hasMetadata.getMetadata().getLabels();
        if (Objects.isNull(labels)) {
            labels = new HashMap<>();
            hasMetadata.getMetadata().setLabels(labels);
        }
        labels.put(ServerManageData.DEPLOY_TYPE, ServerManageData.DeployType.CANCEL_DEPLOY);
        log.info("{} will set label {}:{}", serverName, ServerManageData.DEPLOY_TYPE, ServerManageData.DeployType.CANCEL_DEPLOY);
    }

    @Override
    public void deploy(ServerDeployInfo param) {
        String serverName = param.getServerName();
        log.info("start k8s deploy {}, param: [{}]", serverName, param);
        List<DeployNode> deployNodes = param.getDeployNodes();
        String deployType = param.getDeployType();
        String controllerType = param.getControllerType();
        Integer replica = param.getReplica();
        if (StringUtils.isBlank(serverName) || !commonService.validDeployAndControllerType(deployType, controllerType)) {
            throw new ServerException("部署参数有误！");
        }
        if (commonService.isNotDeploySupport(serverName)) {
            throw new ServerException("不支持操作服务！");
        }
        doDeploy(serverName, controllerType, deployType, replica, deployNodes);
        LocalCacheUtil.remove(ServerManageData.SERVER_LIST_CACHE_KEY);
        LocalCacheUtil.remove(ServerManageData.SERVER_INSTANCE_LIST_CACHE_KEY);
    }

    private void setControllerDeployLabel(HasMetadata resource, String resourceName, String deployType) {
        Map<String, String> labels = resource.getMetadata().getLabels();
        if (Objects.isNull(labels)) {
            labels = new HashMap<>();
            resource.getMetadata().setLabels(labels);
        }
        log.info("{} controller will set deployType label: {}", resourceName, deployType);
        labels.put(ServerManageData.DEPLOY_TYPE, deployType);
    }

    private void setDsInfo(DaemonSet ds, String dsName, String deployType) {

        String labelKey = dsName.replace(ServerManageData.NAME_PREFIX, "");
        String labelValue = ServerManageData.DeployConfig.NODE_SELECTOR_VALUE;
        String taintKey = ServerManageData.DeployConfig.TAINT_KEY;
        String taintValue = dsName.replace(ServerManageData.NAME_PREFIX, "");
        String taintEffect = ServerManageData.DeployConfig.TAINT_EFFECT_NO_EXECUTE;
        //1、设置部署方式
        setControllerDeployLabel(ds, dsName, deployType);
        //2、设置nodeSelector
        Map<String, String> nodeSelector = ds.getSpec().getTemplate().getSpec().getNodeSelector();
        if (Objects.isNull(nodeSelector)) {
            nodeSelector = new HashMap<>();
            ds.getSpec().getTemplate().getSpec().setNodeSelector(nodeSelector);
        }
        nodeSelector.put(labelKey, labelValue);
        log.info("{} ds will set nodeSelector {}:{}", dsName, labelKey, labelValue);
        //3、设置容忍
        List<Toleration> tolerations = ds.getSpec().getTemplate().getSpec().getTolerations();
        if (Objects.isNull(tolerations)) {
            tolerations = new ArrayList<>();
            ds.getSpec().getTemplate().getSpec().setTolerations(tolerations);
        }
        if (ServerManageData.DeployType.FIXED_NODE.equals(deployType)) {
            log.info("{} ds will remove taint [{}:{}={}]", dsName, taintKey, taintValue, taintEffect);
            tolerations.removeIf(t -> taintKey.equals(t.getKey()) &&
                    taintValue.equals(t.getValue()) &&
                    taintEffect.equals(t.getEffect()) &&
                    ServerManageData.DeployConfig.TAINT_OPERATOR.equals(t.getOperator()));
        } else {
            tolerations.removeIf(t -> taintKey.equals(t.getKey()) &&
                    taintValue.equals(t.getValue()) &&
                    taintEffect.equals(t.getEffect()) &&
                    ServerManageData.DeployConfig.TAINT_OPERATOR.equals(t.getOperator()));
            Toleration toleration = new TolerationBuilder()
                    .withKey(taintKey)
                    .withOperator(ServerManageData.DeployConfig.TAINT_OPERATOR)
                    .withValue(taintValue)
                    .withEffect(taintEffect)
                    .build();
            log.info("{} ds will set taint {}:{}={}", dsName, taintKey, taintValue, taintEffect);
            tolerations.add(toleration);
        }
        k8sDeployService.client().apps().daemonSets()
                .inNamespace(Constants.NAMESPACE_DEFAULT)
                .withName(dsName)
                .replace(ds);
    }

    private void deployDaemonSet(String serverName, String deployType, List<String> k8sNodeNames, Map<String, Node> clusterNodes) {
        if (ServerManageData.DeployType.RANDOM_DRIFT_NODE.equals(deployType)) {
            throw new ServerException("不支持的部署方式");
        }
        List<Node> deployNodes = commonService.parseDeployNodes(deployType, clusterNodes, k8sNodeNames);
        DaemonSet ds = k8sDeployService.client().apps().daemonSets()
                .inNamespace(Constants.NAMESPACE_DEFAULT).withName(serverName).get();
        if (Objects.isNull(ds)) {
            throw new ServerException("部署异常，请检查[" + serverName + "]DaemonSet是否正常");
        }
        commonService.deployForNode(serverName, deployType, clusterNodes, deployNodes);
        //控制器调整
        setDsInfo(ds, serverName, deployType);
    }

    private void deployDeployment(String serverName, String deployType, List<String> k8sNodeNames, Map<String, Node> clusterNodes, Integer replica) {
        List<Node> deployNodes = commonService.parseDeployNodes(deployType, clusterNodes, k8sNodeNames);
        Deployment deployment = k8sDeployService.client().apps().deployments()
                .inNamespace(Constants.NAMESPACE_DEFAULT).withName(serverName).get();
        if (Objects.isNull(deployment)) {
            throw new ServerException("部署异常，请检查[" + serverName + "]Deployment是否正常");
        }
        if (!ServerManageData.DeployType.RANDOM_DRIFT_NODE.equals(deployType)) {
            replica = k8sNodeNames.size();
        }
        commonService.deployForNode(serverName, deployType, clusterNodes, deployNodes);
        setDmInfo(deployment, serverName, deployType, replica);
    }

    private void deployStatefulSet(String serverName, String deployType, List<String> k8sNodeNames, Map<String, Node> clusterNodes, Integer replica) {
        List<Node> deployNodes = commonService.parseDeployNodes(deployType, clusterNodes, k8sNodeNames);

        StatefulSet statefulSet = k8sDeployService.client().apps().statefulSets()
                .inNamespace(Constants.NAMESPACE_DEFAULT).withName(serverName).get();
        if (Objects.isNull(statefulSet)) {
            throw new ServerException("部署异常，请检查[" + serverName + "]StatefulSet是否正常");
        }
        if (!ServerManageData.DeployType.RANDOM_DRIFT_NODE.equals(deployType)) {
            replica = k8sNodeNames.size();
        }
        commonService.deployForNode(serverName, deployType, clusterNodes, deployNodes);
        setStsInfo(statefulSet, serverName, deployType, replica);
    }

    private void setDmInfo(Deployment deployment, String dmName, String deployType, Integer replica) {
        String labelKey = dmName.replace(ServerManageData.NAME_PREFIX, "");
        String labelValue = ServerManageData.DeployConfig.NODE_SELECTOR_VALUE;
        String taintKey = ServerManageData.DeployConfig.TAINT_KEY;
        String taintValue = dmName.replace(ServerManageData.NAME_PREFIX, "");
        String taintEffect = ServerManageData.DeployConfig.TAINT_EFFECT_NO_EXECUTE;
        //1、设置部署方式
        setControllerDeployLabel(deployment, dmName, deployType);
        //2、设置nodeSelector、副本数
        Map<String, String> nodeSelector = deployment.getSpec().getTemplate().getSpec().getNodeSelector();
        if (Objects.isNull(nodeSelector)) {
            nodeSelector = new HashMap<>();
            deployment.getSpec().getTemplate().getSpec().setNodeSelector(nodeSelector);
        }
        if (ServerManageData.DeployType.RANDOM_DRIFT_NODE.equals(deployType)) {
            //随机漂移 移除nodeSelector
            log.info("{} deployment will remove nodeSelector {}:{}", dmName, labelKey, labelValue);
            nodeSelector.entrySet().removeIf(entry -> labelKey.equals(entry.getKey()) && labelValue.equals(entry.getValue()));
        } else {
            nodeSelector.put(labelKey, labelValue);
            log.info("{} deployment will set nodeSelector {}:{}", dmName, labelKey, labelValue);
        }
        //3、设置容忍
        List<Toleration> tolerations = deployment.getSpec().getTemplate().getSpec().getTolerations();
        if (Objects.isNull(tolerations)) {
            tolerations = new ArrayList<>();
            deployment.getSpec().getTemplate().getSpec().setTolerations(tolerations);
        }
        if (ServerManageData.DeployType.FIXED_NODE.equals(deployType) || ServerManageData.DeployType.RANDOM_DRIFT_NODE.equals(deployType)) {
            //随机漂移、固定节点 移除容忍
            log.info("{} deployment will remove taint [{}:{}={}]", dmName, taintKey, taintValue, taintEffect);
            tolerations.removeIf(t -> taintKey.equals(t.getKey()) &&
                    taintValue.equals(t.getValue()) &&
                    taintEffect.equals(t.getEffect()) &&
                    ServerManageData.DeployConfig.TAINT_OPERATOR.equals(t.getOperator()));
        } else {
            tolerations.removeIf(t -> taintKey.equals(t.getKey()) &&
                    taintValue.equals(t.getValue()) &&
                    taintEffect.equals(t.getEffect()) &&
                    ServerManageData.DeployConfig.TAINT_OPERATOR.equals(t.getOperator()));
            Toleration toleration = new TolerationBuilder()
                    .withKey(taintKey)
                    .withOperator(ServerManageData.DeployConfig.TAINT_OPERATOR)
                    .withValue(taintValue)
                    .withEffect(taintEffect)
                    .build();
            log.info("{} deployment will set taint {}:{}={}", dmName, taintKey, taintValue, taintEffect);
            tolerations.add(toleration);
        }
        //设置副本数
        log.info("{} deployment will set replica : {}", dmName, replica);
        deployment.getSpec().setReplicas(replica);
        k8sDeployService.client().apps().deployments()
                .inNamespace(Constants.NAMESPACE_DEFAULT)
                .withName(dmName)
                .replace(deployment);
    }

    private void setStsInfo(StatefulSet statefulSet, String stsName, String deployType, Integer replica) {
        String labelKey = stsName.replace(ServerManageData.NAME_PREFIX, "");
        String labelValue = ServerManageData.DeployConfig.NODE_SELECTOR_VALUE;
        String taintKey = ServerManageData.DeployConfig.TAINT_KEY;
        String taintValue = stsName.replace(ServerManageData.NAME_PREFIX, "");
        String taintEffect = ServerManageData.DeployConfig.TAINT_EFFECT_NO_EXECUTE;
        //1、设置部署方式
        setControllerDeployLabel(statefulSet, stsName, deployType);
        //2、设置nodeSelector、副本数
        Map<String, String> nodeSelector = statefulSet.getSpec().getTemplate().getSpec().getNodeSelector();
        if (Objects.isNull(nodeSelector)) {
            nodeSelector = new HashMap<>();
            statefulSet.getSpec().getTemplate().getSpec().setNodeSelector(nodeSelector);
        }
        if (ServerManageData.DeployType.RANDOM_DRIFT_NODE.equals(deployType)) {
            //随机漂移 移除nodeSelector
            log.info("{} sts will remove nodeSelector {}:{}", stsName, labelKey, labelValue);
            nodeSelector.entrySet().removeIf(entry -> labelKey.equals(entry.getKey()) && labelValue.equals(entry.getValue()));
        } else {
            nodeSelector.put(labelKey, labelValue);
            log.info("{} sts will set nodeSelector {}:{}", stsName, labelKey, labelValue);
        }
        //3、设置容忍
        List<Toleration> tolerations = statefulSet.getSpec().getTemplate().getSpec().getTolerations();
        if (Objects.isNull(tolerations)) {
            tolerations = new ArrayList<>();
            statefulSet.getSpec().getTemplate().getSpec().setTolerations(tolerations);
        }
        if (ServerManageData.DeployType.FIXED_NODE.equals(deployType) || ServerManageData.DeployType.RANDOM_DRIFT_NODE.equals(deployType)) {
            //随机漂移、固定节点 移除容忍
            log.info("{} sts will remove taint [{}:{}={}]", stsName, taintKey, taintValue, taintEffect);
            tolerations.removeIf(t -> taintKey.equals(t.getKey()) &&
                    taintValue.equals(t.getValue()) &&
                    taintEffect.equals(t.getEffect()) &&
                    ServerManageData.DeployConfig.TAINT_OPERATOR.equals(t.getOperator()));
        } else {
            tolerations.removeIf(t -> taintKey.equals(t.getKey()) &&
                    taintValue.equals(t.getValue()) &&
                    taintEffect.equals(t.getEffect()) &&
                    ServerManageData.DeployConfig.TAINT_OPERATOR.equals(t.getOperator()));
            Toleration toleration = new TolerationBuilder()
                    .withKey(taintKey)
                    .withOperator(ServerManageData.DeployConfig.TAINT_OPERATOR)
                    .withValue(taintValue)
                    .withEffect(taintEffect)
                    .build();
            log.info("{} sts will set taint {}:{}={}", stsName, taintKey, taintValue, taintEffect);
            tolerations.add(toleration);
        }
        //设置副本数
        log.info("{} sts will set replica : {}", statefulSet, replica);
        statefulSet.getSpec().setReplicas(replica);
        k8sDeployService.client().apps().statefulSets()
                .inNamespace(Constants.NAMESPACE_DEFAULT)
                .withName(stsName)
                .replace(statefulSet);
    }


    @Override
    public List<DeployNode> deployNodes(String serverName) {
        return commonService.deployNodes(serverName);
    }

    public String getControllerType(String serverName) {
        DaemonSet daemonSet = k8sDeployService.client().apps().daemonSets().inNamespace(Constants.NAMESPACE_DEFAULT).withName(serverName).get();
        if (Objects.nonNull(daemonSet)) {
            return ServerManageData.ControllerType.DAEMON_SET;
        }
        Deployment deployment = k8sDeployService.client().apps().deployments().inNamespace(Constants.NAMESPACE_DEFAULT).withName(serverName).get();
        if (Objects.nonNull(deployment)) {
            return ServerManageData.ControllerType.DEPLOYMENT;
        }

        StatefulSet statefulSet = k8sDeployService.client().apps().statefulSets().inNamespace(Constants.NAMESPACE_DEFAULT).withName(serverName).get();
        if (Objects.nonNull(statefulSet)) {
            return ServerManageData.ControllerType.STATEFUL_SET;
        }
        throw new ServerException("服务名称有误[未找到控制器]");
    }

    @Override
    public void deployExternal(ServerDeployExternalParam param) {
        String serverName = param.getServerName();
        log.info("start k8s external deploy {}, param: [{}]", serverName, param);
        String controllerType = getControllerType(serverName);
        String deployType = param.getDeployType();
        List<DeployNode> deployNodes = param.getDeployNodes();
        Integer replica = param.getReplica();
        if (StringUtils.isBlank(serverName) || !commonService.validDeployAndControllerType(deployType, controllerType)) {
            throw new ServerException("部署参数有误！");
        }
        doDeploy(serverName, controllerType, deployType, replica, deployNodes);
        LocalCacheUtil.remove(ServerManageData.SERVER_LIST_CACHE_KEY);
        LocalCacheUtil.remove(ServerManageData.SERVER_INSTANCE_LIST_CACHE_KEY);
    }

    private void doDeploy(String serverName, String controllerType, String deployType, Integer replica, List<DeployNode> deployNodes) {
        if (Objects.isNull(deployNodes)) {
            deployNodes = new ArrayList<>();
        }
        List<String> k8sNodeNames;
        if (CollectionUtils.isEmpty(deployNodes)) {
            k8sNodeNames = new ArrayList<>();
        } else {
            k8sNodeNames = deployNodes.stream().map(DeployNode::getNodeId).collect(Collectors.toList());
        }
        Map<String, Node> clusterNodes = commonService.getClusterNodes();
        switch (controllerType) {
            case ServerManageData.ControllerType.DAEMON_SET:
                log.info("{} ds start server deploy, controllerType: {}, deployType: {}, nodes: {}", serverName, controllerType, deployType, k8sNodeNames);
                deployDaemonSet(serverName, deployType, k8sNodeNames, clusterNodes);
                //写高级配置基础信息：内外网IP
                commonService.handleDomainAndHostIp(deployNodes, serverName);
                log.info("{} ds start handleAdvanceConfig", serverName);
                commonService.handleAdvanceConfig(deployNodes, serverName);
                break;
            case ServerManageData.ControllerType.DEPLOYMENT:
                log.info("{} deployment start server deploy, controllerType: {}, deployType: {}, nodes: {}, replica:{}", serverName,
                        controllerType, deployType, k8sNodeNames, replica);
                deployDeployment(serverName, deployType, k8sNodeNames, clusterNodes, replica);
                commonService.handleDomainAndHostIp(deployNodes, serverName);
                log.info("{} deployment start handleAdvanceConfig", serverName);
                commonService.handleAdvanceConfig(deployNodes, serverName);
                break;
            case ServerManageData.ControllerType.STATEFUL_SET:
                log.info("{} sts start server deploy, controllerType: {}, deployType: {}, nodes: {}, replica:{}", serverName,
                        controllerType, deployType, k8sNodeNames, replica);
                deployStatefulSet(serverName, deployType, k8sNodeNames, clusterNodes, replica);
                commonService.handleDomainAndHostIp(deployNodes, serverName);
                log.info("{} sts start handleAdvanceConfig", serverName);
                commonService.handleAdvanceConfig(deployNodes, serverName);
                break;
            default:
        }
    }


    @Override
    public void editCm(EditCmParam param) {
        commonService.editCm(param);
    }

    @Override
    public Map<String, String> queryCm(String configmapName) {
        return commonService.queryCm(configmapName);
    }

    @Override
    public void labelToNode(NodeToLabelDTO param) {
        commonService.labelToNode(param);
    }

    @Override
    public Page<DeployNodeExtend> nodeList(Integer current, Integer pageSize, String keywords) {
        return commonService.nodeList(current, pageSize, keywords);
    }

    @Override
    public void incrementDeploy(IncrementDeployParam param) {
        String serverName = param.getServerName();
        log.info("start k8s external increment deploy {}, param:[{}]", serverName, param);
        List<DeployNode> incrementNodes = param.getIncrementNodes();
        if (CollectionUtils.isEmpty(incrementNodes) || StringUtils.isBlank(serverName)) {
            throw new ServerException("参数有误！");
        }
        String deployType = param.getDeployType();
        if (!ServerManageData.DeployType.FIXED_NODE.equals(deployType)
                && !ServerManageData.DeployType.MONOPOLIZE_NODE.equals(deployType)) {
            throw new ServerException("参数有误，不支持的部署类型！");
        }
        String controllerType = getControllerType(serverName);
        String existDeployType = commonService.getDeployType(controllerType, serverName);
        if (StringUtils.isNotBlank(existDeployType) && !existDeployType.equals(deployType)
                && !ServerManageData.DeployType.CANCEL_DEPLOY.equals(existDeployType)) {
            //原来 有deployType 且 和现在不一致
            throw new ServerException("参数有误，请保持deploy_type一致");
        }
        //查询原有的deployNodes
        List<DeployNode> existsDeployNodes = commonService.deployNodes(serverName);
        List<String> existsNodeIds = existsDeployNodes.stream().map(DeployNode::getNodeId).collect(Collectors.toList());
        log.info("incrementDeploy exists deployNodes size:{}, nodeIds:[{}]", existsDeployNodes.size(), existsNodeIds);
        List<String> incrementNodeIds = incrementNodes.stream().map(DeployNode::getNodeId).collect(Collectors.toList());
        log.info("incrementDeploy add incrementNodes size:{}, nodeIds:[{}]", incrementNodes.size(), incrementNodeIds);
        List<DeployNode> deployNodes = new ArrayList<>(incrementNodes);
        deployNodes.addAll(existsDeployNodes);
        doDeploy(serverName, controllerType, deployType, deployNodes.size(), deployNodes);
        LocalCacheUtil.remove(ServerManageData.SERVER_LIST_CACHE_KEY);
        LocalCacheUtil.remove(ServerManageData.SERVER_INSTANCE_LIST_CACHE_KEY);
    }

    @Override
    public List<NodeToLabelDTO> queryNodeLabels(List<String> nodeIds) {
        return commonService.queryNodeLabels(nodeIds);
    }


}