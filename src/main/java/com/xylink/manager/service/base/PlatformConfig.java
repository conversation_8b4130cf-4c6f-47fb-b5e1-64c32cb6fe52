package com.xylink.manager.service.base;

import com.xylink.manager.model.em.Platform;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;

/**
 * <AUTHOR>
 * @since 2021/9/17 2:30 下午
 */
@Slf4j
public class PlatformConfig {
    private static final String PLATFORM = System.getProperty("platform");

    public static Platform current() {
        Platform platformEnum = EnumUtils.getEnum(Platform.class, PLATFORM);
        return platformEnum == null ? Platform.X86 : platformEnum;
    }

    public static boolean isAnKe() {
        return current() == Platform.ANKE;
    }
}
