package com.xylink.manager.service.base;

import com.xylink.manager.service.base.impl.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

/**
 * <AUTHOR> create on 2025/4/30
 */
@Configuration
@DependsOn("systemModeConfig")
@Slf4j
public class ServerManageConfiguration {

    //配置文件 third.k8s.switch
    @Value("${third.k8s.switch}")
    private boolean thirdK8sSwitch;

    @Bean
    public IServerManageService serverManageService() {
        if (SystemModeConfig.isNewCms() || SystemModeConfig.isPrivate56()) {
            log.info("ServerManageConfiguration mode is {}, use [{}]", SystemModeConfig.current(), NoahServerManageServiceImpl.class);
            return new NoahServerManageServiceImpl();
        } else {
            log.info("ServerManageConfiguration mode is {}, use [{}]", SystemModeConfig.current(), K8sServerManageServiceImpl.class);
            return new K8sServerManageServiceImpl();
        }
    }

    @Bean
    public IDeployService deployService(K8sClientBuilder clientBuilder) {
        if (thirdK8sSwitch){
            return new K8sThirdDeployService(clientBuilder);
        }
        if (SystemModeConfig.isPrivate56()) {
            return new K8sWithNoahDeployService(clientBuilder);
        } else {
            return new K8sDeployService(clientBuilder);
        }
    }
}
