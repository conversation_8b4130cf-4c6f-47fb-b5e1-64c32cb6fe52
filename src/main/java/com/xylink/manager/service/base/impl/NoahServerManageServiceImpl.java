package com.xylink.manager.service.base.impl;

import com.google.common.base.Stopwatch;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.controller.dto.servicemanage.*;
import com.xylink.manager.model.common.Page;
import com.xylink.manager.service.api.NoahApiService;
import com.xylink.manager.service.base.IServerManageService;
import com.xylink.manager.service.base.SystemModeConfig;
import com.xylink.util.LocalCacheUtil;
import io.fabric8.kubernetes.api.model.Node;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <AUTHOR> create on 2025/4/30
 */
@Slf4j
public class NoahServerManageServiceImpl implements IServerManageService {

    @Autowired
    private NoahApiService noahApiService;

    @Autowired
    private K8sDeployService k8sDeployService;

    @Autowired
    private ServerManageCommonService commonService;

    @Autowired
    private NoahServerManagerInitService noahServerManagerInitService;

    @PostConstruct
    public void initDeployServers(){
        noahServerManagerInitService.initDeployServers();
    }
    @Override
    public Page<ServerInfoVO> serverList(ServerListParam param) {
        List<ServerInfoVO> resultList = (List<ServerInfoVO>) LocalCacheUtil.get(ServerManageData.SERVER_LIST_CACHE_KEY);
        if (Objects.nonNull(resultList)) {
            log.info("get serverList from cache data");
            return commonService.buildServerList(resultList, param);
        } else {
            resultList = new ArrayList<>();
        }
        Stopwatch stopwatch = Stopwatch.createUnstarted();
        stopwatch.start();
        List<NoahApiService.NoahServerInfo> serverList = noahApiService.serverList();
        stopwatch.stop();
        long costMills = stopwatch.elapsed(TimeUnit.MILLISECONDS);
        log.info("serverList time noahApi cost mills:{}", costMills);
        if (CollectionUtils.isEmpty(serverList)) {
            return new Page<>(param.getCurrent(), param.getPageSize());
        }
        stopwatch.reset().start();
        List<io.fabric8.kubernetes.api.model.Pod> pods = k8sDeployService.client().pods().list().getItems();
        stopwatch.stop();
        costMills = stopwatch.elapsed(TimeUnit.MILLISECONDS);
        log.info("serverList time client get pods cost mills:{}", costMills);
        stopwatch.reset().start();
        for (NoahApiService.NoahServerInfo server : serverList) {
            String resType = server.getResType();
            if (ServerManageData.ControllerType.NOAH_DEPLOYMENT.equals(resType)) {
                ServerInfoVO vo = new ServerInfoVO();
                String controllerName = server.getServerName();
                vo.setServerName(controllerName);
                vo.setStatus(commonService.getServerStatus(controllerName, pods));
                vo.setReplica(commonService.getControllerPods(controllerName, pods).size());
                vo.setDeployType(server.getDeployType());
                vo.setCodeVersion(server.getImageTag());
                vo.setControllerType(ServerManageData.ControllerType.DEPLOYMENT);
                resultList.add(vo);
            }
            if (ServerManageData.ControllerType.NOAH_DAEMON_SET.equals(resType)) {
                ServerInfoVO vo = new ServerInfoVO();
                String controllerName = server.getServerName();
                vo.setServerName(controllerName);
                vo.setStatus(commonService.getServerStatus(controllerName, pods));
                vo.setReplica(commonService.getControllerPods(controllerName, pods).size());
                vo.setDeployType(server.getDeployType());
                vo.setCodeVersion(server.getImageTag());
                vo.setControllerType(ServerManageData.ControllerType.DAEMON_SET);
                resultList.add(vo);
            }
            if (ServerManageData.ControllerType.NOAH_STATEFUL_SET.equals(resType)) {
                ServerInfoVO vo = new ServerInfoVO();
                String controllerName = server.getServerName();
                vo.setServerName(controllerName);
                vo.setStatus(commonService.getServerStatus(controllerName, pods));
                vo.setReplica(commonService.getControllerPods(controllerName, pods).size());
                vo.setDeployType(server.getDeployType());
                vo.setCodeVersion(server.getImageTag());
                vo.setControllerType(ServerManageData.ControllerType.STATEFUL_SET);
                resultList.add(vo);
            }
        }
        stopwatch.stop();
        costMills = stopwatch.elapsed(TimeUnit.MILLISECONDS);
        log.info("serverList time build resultList cost mills:{}", costMills);
        //设置到缓存
        LocalCacheUtil.put(ServerManageData.SERVER_LIST_CACHE_KEY, resultList);
        stopwatch.reset().start();
        Page<ServerInfoVO> r = commonService.buildServerList(resultList, param);
        stopwatch.stop();
        costMills = stopwatch.elapsed(TimeUnit.MILLISECONDS);
        log.info("serverList time build return List cost mills:{}", costMills);
        return r;
    }

    @Override
    public Page<ServerInstanceInfoVO> serverInstanceList(ServerInstanceListParam param) {
        return commonService.serverInstanceList(param);

    }

    @Override
    public void restartInstances(List<String> instanceNames) {
        commonService.restartPods(instanceNames);
    }

    @Override
    public void restartServers(List<String> serverNames) {
        commonService.restartServers(serverNames);
    }

    @Override
    public void cancelDeploy(String serverName, String controllerType) {
        if (StringUtils.isBlank(serverName) || StringUtils.isBlank(controllerType)) {
            throw new ServerException("参数有误");
        }
        if (commonService.isNotDeploySupport(serverName)) {
            throw new ServerException("不允许操作服务[" + serverName + "]");
        }
        Map<String, Node> clusterNodes = commonService.getClusterNodes();
        //移除node的标签、污点
        commonService.cancelDeployForNode(serverName, clusterNodes);

        //通知配置中心更新控制器deploy_type
        noahApiService.notifyDeployLabelChange();

        LocalCacheUtil.remove(ServerManageData.SERVER_LIST_CACHE_KEY);
        LocalCacheUtil.remove(ServerManageData.SERVER_INSTANCE_LIST_CACHE_KEY);
    }

    @Override
    public void deploy(ServerDeployInfo param) {
        String controllerType = param.getControllerType();
        String serverName = param.getServerName();
        log.info("start noah deploy {}, param:[{}]", serverName, param);
        String deployType = param.getDeployType();
        List<DeployNode> deployNodes = param.getDeployNodes();
        Integer replica = param.getReplica();
        if (StringUtils.isBlank(serverName) || !commonService.validDeployAndControllerType(deployType, controllerType)) {
            throw new ServerException("部署参数有误！");
        }
        if (commonService.isNotDeploySupport(serverName)) {
            throw new ServerException("不支持操作服务！");
        }
        doDeploy(serverName, controllerType, deployType, replica, deployNodes);
        LocalCacheUtil.remove(ServerManageData.SERVER_LIST_CACHE_KEY);
        LocalCacheUtil.remove(ServerManageData.SERVER_INSTANCE_LIST_CACHE_KEY);
    }

    private void doDeploy(String serverName, String controllerType, String deployType, Integer replica, List<DeployNode> deployNodes) {
        List<String> k8sNodeNames;
        if (CollectionUtils.isEmpty(deployNodes)) {
            k8sNodeNames = new ArrayList<>();
        } else {
            k8sNodeNames = deployNodes.stream().map(DeployNode::getNodeId).collect(Collectors.toList());
        }
        Map<String, Node> clusterNodes = commonService.getClusterNodes();
        switch (controllerType) {
            case ServerManageData.ControllerType.DAEMON_SET:
                log.info("{} ds start server deploy, controllerType: {}, deployType: {}, nodes: {}", serverName, controllerType, deployType, k8sNodeNames);
                deployDaemonSet(serverName, deployType, k8sNodeNames, clusterNodes);
                commonService.handleDomainAndHostIp(deployNodes, serverName);
                log.info("{} ds start handleAdvanceConfig", serverName);
                commonService.handleAdvanceConfig(deployNodes, serverName);
                break;
            case ServerManageData.ControllerType.DEPLOYMENT:
                log.info("{} deployment start server deploy, controllerType: {}, deployType: {}, nodes: {}, replica:{}", serverName,
                        controllerType, deployType, k8sNodeNames, replica);
                deployDeployment(serverName, deployType, k8sNodeNames, clusterNodes, replica);
                commonService.handleDomainAndHostIp(deployNodes, serverName);
                log.info("{} deployment start handleAdvanceConfig", serverName);
                commonService.handleAdvanceConfig(deployNodes, serverName);
                break;
            case ServerManageData.ControllerType.STATEFUL_SET:
                log.info("{} sts start server deploy, controllerType: {}, deployType: {}, nodes: {}, replica:{}", serverName,
                        controllerType, deployType, k8sNodeNames, replica);
                deployStatefulSet(serverName, deployType, k8sNodeNames, clusterNodes, replica);
                commonService.handleDomainAndHostIp(deployNodes, serverName);
                log.info("{} sts start handleAdvanceConfig", serverName);
                commonService.handleAdvanceConfig(deployNodes, serverName);
                break;
            default:
        }
    }

    private void deployDaemonSet(String serverName, String deployType, List<String> k8sNodeNames, Map<String, Node> clusterNodes) {
        if (ServerManageData.DeployType.RANDOM_DRIFT_NODE.equals(deployType)) {
            throw new ServerException("不支持的部署方式");
        }
        List<Node> deployNodes = commonService.parseDeployNodes(deployType, clusterNodes, k8sNodeNames);
        commonService.deployForNode(serverName, deployType, clusterNodes, deployNodes);
        //控制器调整
        noahApiService.changeDeployMode(serverName, deployType, null);
    }

    private void deployDeployment(String serverName, String deployType, List<String> k8sNodeNames, Map<String, Node> clusterNodes, Integer replica) {
        List<Node> deployNodes = commonService.parseDeployNodes(deployType, clusterNodes, k8sNodeNames);
        if (!ServerManageData.DeployType.RANDOM_DRIFT_NODE.equals(deployType)) {
            replica = k8sNodeNames.size();
        }
        commonService.deployForNode(serverName, deployType, clusterNodes, deployNodes);
        //控制器调整
        noahApiService.changeDeployMode(serverName, deployType, replica);
    }

    private void deployStatefulSet(String serverName, String deployType, List<String> k8sNodeNames, Map<String, Node> clusterNodes, Integer replica) {
        List<Node> deployNodes = commonService.parseDeployNodes(deployType, clusterNodes, k8sNodeNames);
        if (!ServerManageData.DeployType.RANDOM_DRIFT_NODE.equals(deployType)) {
            replica = k8sNodeNames.size();
        }
        commonService.deployForNode(serverName, deployType, clusterNodes, deployNodes);
        //控制器调整
        noahApiService.changeDeployMode(serverName, deployType, replica);
    }

    @Override
    public List<DeployNode> deployNodes(String serverName) {
        return commonService.deployNodes(serverName);
    }

    @Override
    public void deployExternal(ServerDeployExternalParam param) {
        String serverName = param.getServerName();
        log.info("start noah external deploy {}, param:[{}]", serverName, param);
        String controllerType = convertNoahControllerType(noahApiService.getControllerType(serverName));
        String deployType = param.getDeployType();
        List<DeployNode> deployNodes = param.getDeployNodes();
        Integer replica = param.getReplica();
        if (StringUtils.isBlank(serverName) || !commonService.validDeployAndControllerType(deployType, controllerType)) {
            throw new ServerException("部署参数有误！");
        }
        doDeploy(serverName, controllerType, deployType, replica, deployNodes);
        LocalCacheUtil.remove(ServerManageData.SERVER_LIST_CACHE_KEY);
        LocalCacheUtil.remove(ServerManageData.SERVER_INSTANCE_LIST_CACHE_KEY);
    }

    private static String convertNoahControllerType(String noahControllerType) {
        switch (noahControllerType) {
            case ServerManageData.ControllerType.NOAH_DAEMON_SET:
                return ServerManageData.ControllerType.DAEMON_SET;
            case ServerManageData.ControllerType.NOAH_DEPLOYMENT:
                return ServerManageData.ControllerType.DEPLOYMENT;
            case ServerManageData.ControllerType.NOAH_STATEFUL_SET:
                return ServerManageData.ControllerType.STATEFUL_SET;
            default:
                throw new ServerException("不支持的控制器类型:[" + noahControllerType + "]");
        }
    }


    @Override
    public void editCm(EditCmParam param) {
        commonService.editCm(param);
    }

    @Override
    public Map<String, String> queryCm(String configmapName) {
        return commonService.queryCm(configmapName);
    }

    @Override
    public void labelToNode(NodeToLabelDTO param) {
        commonService.labelToNode(param);
    }

    @Override
    public Page<DeployNodeExtend> nodeList(Integer current, Integer pageSize, String keywords) {
        return commonService.nodeList(current, pageSize, keywords);
    }

    @Override
    public void incrementDeploy(IncrementDeployParam param) {
        String serverName = param.getServerName();
        log.info("start noah external increment deploy {}, param:[{}]", serverName, param);
        List<DeployNode> incrementNodes = param.getIncrementNodes();
        if (CollectionUtils.isEmpty(incrementNodes) || StringUtils.isBlank(serverName)) {
            throw new ServerException("参数有误！");
        }
        String deployType = param.getDeployType();
        if (!ServerManageData.DeployType.FIXED_NODE.equals(deployType)
                && !ServerManageData.DeployType.MONOPOLIZE_NODE.equals(deployType)) {
            throw new ServerException("参数有误，不支持的部署类型！");
        }
        String controllerType = convertNoahControllerType(noahApiService.getControllerType(serverName));
        String existDeployType = commonService.getDeployType(controllerType, serverName);
        if (StringUtils.isNotBlank(existDeployType) && !existDeployType.equals(deployType)
                && !ServerManageData.DeployType.CANCEL_DEPLOY.equals(existDeployType)) {
            //原来 有deployType 且 和现在不一致
            throw new ServerException("参数有误，请保持deploy_type一致");
        }
        //查询原有的deployNodes
        List<DeployNode> existsDeployNodes = commonService.deployNodes(serverName);
        List<String> existsNodeIds = existsDeployNodes.stream().map(DeployNode::getNodeId).collect(Collectors.toList());
        log.info("incrementDeploy exists deployNodes size:{}, nodeIds:[{}]", existsDeployNodes.size(), existsNodeIds);
        List<String> incrementNodeIds = incrementNodes.stream().map(DeployNode::getNodeId).collect(Collectors.toList());
        log.info("incrementDeploy add incrementNodes size:{}, nodeIds:[{}]", incrementNodes.size(), incrementNodeIds);
        List<DeployNode> deployNodes = new ArrayList<>(incrementNodes);
        deployNodes.addAll(existsDeployNodes);
        doDeploy(serverName, controllerType, deployType, deployNodes.size(), deployNodes);
        LocalCacheUtil.remove(ServerManageData.SERVER_LIST_CACHE_KEY);
        LocalCacheUtil.remove(ServerManageData.SERVER_INSTANCE_LIST_CACHE_KEY);
    }

    @Override
    public List<NodeToLabelDTO> queryNodeLabels(List<String> nodeIds) {
        return commonService.queryNodeLabels(nodeIds);
    }


}

