package com.xylink.manager.service.base.impl;

import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.service.api.NoahApiService;
import com.xylink.manager.service.base.K8sClientBuilder;
import com.xylink.util.SpringBeanUtil;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * ClassName:K8sThirdDeployService
 * Package:com.xylink.manager.service.base.impl
 * Description:
 *
 * <AUTHOR>
 * @Date 2025/7/28-16:17
 * @Version: v1.0
 */
public class K8sThirdDeployService extends K8sWithNoahDeployService {

    @Resource
    private RestTemplate restTemplate;

    public K8sThirdDeployService(K8sClientBuilder clientBuilder) {
        super(clientBuilder);
    }


    @Override
    @Nullable
    public ConfigMap getConfigMapByName(@NotNull String name, @NotNull String namespace) {
        NoahApiService.ProxyConfigMap proxyConfigMap = noahApiService().proxyQueryConfigMap(name, namespace);
        if (proxyConfigMap == null) {
            return null;
        }
        return ConfigMap.buildConfigMap(proxyConfigMap);
    }


    private NoahApiService noahApiService() {
        return SpringBeanUtil.getBean(NoahApiService.class);
    }
}
