package com.xylink.manager.service.base.impl;

import com.xylink.config.Constants;
import com.xylink.config.mapper.JsonMapper;
import com.xylink.manager.model.deploy.Event;
import com.xylink.manager.service.base.IWatchService;
import com.xylink.manager.service.watch.k8s.K8sConfigMapWatcherDataLoader;
import com.xylink.manager.service.watch.k8s.K8sDeploymentWatcherDataLoader;
import com.xylink.manager.service.watch.k8s.K8sNodeWatcherDataLoader;
import com.xylink.manager.service.watch.k8s.K8sPodWatcherDataLoader;
import io.fabric8.kubernetes.client.Watch;
import io.fabric8.kubernetes.client.Watcher;
import io.fabric8.kubernetes.client.WatcherException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;

@Component
@Slf4j
public class K8sWatcherService implements IWatchService {
    @Autowired
    private K8sDeployService k8sDeployService;

    @Override
    public void watchEvent(@Nonnull Function<Event, Boolean> handleCheck, @Nonnull Consumer<Event> consumer) {
        final CountDownLatch closeLatch = new CountDownLatch(1);
        try (Watch ignored = k8sDeployService.client()
                .v1()
                .events()
                .inNamespace(Constants.NAMESPACE_DEFAULT)
                .watch(new Watcher<io.fabric8.kubernetes.api.model.Event>() {
                    @Override
                    public void eventReceived(Action action, io.fabric8.kubernetes.api.model.Event event) {
                        Event handleEvent = Event.buildEvent(event);
                        handleEvent.setAction(action.name());
                        if (handleCheck.apply(handleEvent)) {
                            handleEvent.setJsonContent(JsonMapper.nonEmptyMapper().toJson(event));
                            consumer.accept(handleEvent);
                        }
                    }

                    @Override
                    public void onClose(WatcherException e) {
                        closeLatch.countDown();
                    }
                })) {
            closeLatch.await(10, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("watcher event error", e);
        }
    }

    @Configuration
    public static class K8sWatcherServiceConfig {
        @Autowired
        private K8sDeployService k8sDeployService;

        @Bean
        public K8sNodeWatcherDataLoader k8sNodeWatcherDataLoader() {
            return new K8sNodeWatcherDataLoader(k8sDeployService.client());
        }

        @Bean
        public K8sDeploymentWatcherDataLoader k8sDeploymentWatcherDataLoader() {
            return new K8sDeploymentWatcherDataLoader(k8sDeployService.client());
        }

        @Bean
        public K8sPodWatcherDataLoader k8sPodWatcherDataLoader() {
            return new K8sPodWatcherDataLoader(k8sDeployService.client());
        }

        @Bean
        public K8sConfigMapWatcherDataLoader k8sConfigMapWatcherDataLoader() {
            return new K8sConfigMapWatcherDataLoader(k8sDeployService.client());
        }
    }
}
