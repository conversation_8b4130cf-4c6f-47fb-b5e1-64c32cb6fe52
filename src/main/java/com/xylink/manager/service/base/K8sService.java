package com.xylink.manager.service.base;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.constant.IptablesConstants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.config.exception.basic.ServiceErrorException;
import com.xylink.config.ssl.TLSClientHttpsRequestFactory;
import com.xylink.manager.controller.dto.basic.PodHostInfoDTO;
import com.xylink.manager.model.cm.KafkaCM;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.model.deploy.Container;
import com.xylink.manager.model.deploy.DaemonSet;
import com.xylink.manager.model.deploy.Deployment;
import com.xylink.manager.model.em.ConfigmapCtrlEnum;
import com.xylink.manager.model.em.DBType;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.cache.bean.ConfigMapCache;
import com.xylink.manager.service.cache.bean.NodeAddressCache;
import com.xylink.manager.service.cache.bean.NodeCache;
import com.xylink.manager.service.cache.service.ICacheService;
import com.xylink.util.Ipv6Util;
import io.fabric8.kubernetes.api.model.HasMetadata;
import io.fabric8.kubernetes.api.model.IntOrString;
import io.fabric8.kubernetes.api.model.ObjectMeta;
import io.lettuce.core.ReadFrom;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.logging.log4j.util.Strings;
import org.apache.tomcat.util.net.IPv6Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.yaml.snakeyaml.Yaml;

import java.io.*;
import java.net.URL;
import java.nio.file.Files;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * Created by liujian on 2017/9/12.
 * k8s管理公共类,负责相关的基础操作
 */
@Service
public class K8sService {
    private final static Logger logger = LoggerFactory.getLogger(K8sService.class);

    /**
     * 专有云业务需区分公有云和私有云
     * 公有云数据需写入到多个k8s对应ns的cm中，而私有云ns只有default
     * 此开关作用是针对公有云，私有云无需关注其他ns的cm操作
     */
    @Value("${deploy-platform:privateCloud}")
    private String deployPlatform;

    @Value("${k8s.protocol.default.http}")
    private boolean k8sProtocolHttp;

    private final K8sClientBuilder clientBuilder;
    private final IDeployService deployService;
    private final ICacheService cacheService;
    @Getter
    private final RestTemplate restTemplate;

    private static final String STATS_SUMMARY = "/stats/summary";

    @Value("${base.dir}")
    private String baseDir;

    public K8sService(K8sClientBuilder clientBuilder, IDeployService deployService, ICacheService cacheService) {
        this.clientBuilder = clientBuilder;
        this.deployService = deployService;
        this.cacheService = cacheService;
        HttpComponentsClientHttpRequestFactory factory = TLSClientHttpsRequestFactory.getInstance(clientBuilder.getConfig());
        factory.setReadTimeout(5000);
        factory.setConnectTimeout(15000);
        this.restTemplate = new RestTemplate(factory);
    }

    /**
     * 校验是否为公有云环境
     */
    public boolean isPublicPlatform() {
        return StringUtils.isNotEmpty(deployPlatform)
                && Constants.PUBLIC_CLOUD.equals(deployPlatform);
    }

    public String getHostNameByNodeIp(String nodeIp) {
        if (StringUtils.isEmpty(nodeIp)) {
            return null;
        }
        com.xylink.manager.model.deploy.Node node = deployService.getNodeByName(nodeIp);
        if (node == null) {
            return null;
        }
        return node.getHostName();
    }

    public List<com.xylink.manager.model.deploy.Node> listFitNodeByLabelIn(String key, String[] labels) {
        return deployService.listNodesByLabels(key, labels);
    }

    public List<com.xylink.manager.model.deploy.Node> listNode() {
        return deployService.listAllNodes();
    }

    public String getHBaseNodeIp() {
        return getNodeIpByApp("hbase");
    }

    public String getHadoopNodeIp() {
        return deployService.listNodesByLabels("type", "hadoop")
                .stream()
                .map(com.xylink.manager.model.deploy.Node::getIp)
                .findFirst()
                .orElse(null);
    }

    public String getEsNodeIp() {
        return getNodeIpByApp("es");
    }

    public String getRedisNodeIp() {
        return getNodeIpByApp("redis");
    }

    public List<String> getMcNodeIp() {
        return getNodeIpsByApp("mc");
    }

    public String getZkNodeIp() {
        return getNodeIpByApp("zookeeper");
    }

    private List<String> getNodeIpsByApp(String appName) {
        return deployService.listNodesByAppLabel(appName)
                .stream()
                .map(com.xylink.manager.model.deploy.Node::getIp)
                .collect(Collectors.toList());
    }

    public String getNodeIpByApp(String appName) {
        return getNodeIpsByApp(appName)
                .stream()
                .findFirst()
                .orElse("");
    }

    public Optional<com.xylink.manager.model.deploy.Pod> getEsPod() {
        return deployService.listPodsByAppLabel(Constants.POD_NAME_ES).stream().findFirst();
    }

    public Optional<com.xylink.manager.model.deploy.Pod> getHBasePod() {
        return deployService.listPodsByAppLabel(Constants.POD_NAME_HBASE).stream().findFirst();
    }

    public List<String> getAllNodeIP() {
        return deployService.listAllNodes()
                .stream()
                .map(com.xylink.manager.model.deploy.Node::getIp)
                .distinct()
                .collect(Collectors.toList());
    }

    public List<String> getNodeIPWithOutMain() {
        List<String> filterNodeTypes = new ArrayList<>();
        filterNodeTypes.add(Constants.NODETYPE_MAIN);
        filterNodeTypes.add(Constants.NODE_TYPE_COMMON_MAIN);
        return deployService.listAllNodes()
                .stream()
                .filter(it -> !filterNodeTypes.contains(it.getType()))
                .map(com.xylink.manager.model.deploy.Node::getIp)
                .distinct()
                .collect(Collectors.toList());
    }

    public boolean getDbPodStatus(String type, String dbType) {
        return getDbPodStatus(type, "master", dbType);
    }

    public boolean getDbPodStatus(String type, String db, String dbType) {
        String label = type;
        try {
            if (DBType.main.name().equalsIgnoreCase(type)) {
                if (!"master".equals(db)) {
                    label = Constants.POD_NAME_MYSQLSLAVE;
                } else {
                    if ("ST".equalsIgnoreCase(dbType)) {
                        label = Constants.POD_NAME_ST;
                    } else if ("DM".equalsIgnoreCase(dbType)) {
                        label = Constants.POD_NAME_DM;
                    } else if ("OB".equalsIgnoreCase(dbType)) {
                        label = Constants.POD_NAME_OB;
                    } else {
                        label = Constants.POD_NAME_MYSQL;
                    }
                }
            } else if (DBType.surv.name().equalsIgnoreCase(type)) {
                label = Constants.POD_NAME_SURV_MYSQL;
            } else if (DBType.statis.name().equalsIgnoreCase(type)) {
                label = Constants.POD_NAME_STATIS_MYSQL;
            } else if (DBType.hbase.name().equalsIgnoreCase(type)) {
                label = Constants.POD_NAME_HBASE;
            } else if (DBType.edu.name().equalsIgnoreCase(type)) {
                label = Constants.POD_NAME_EDU_MYSQL;
            } else if (DBType.webrtc.name().equalsIgnoreCase(type)) {
                label = Constants.POD_NAME_WEBRTC_MYSQL;
            } else if (DBType.matrix.name().equalsIgnoreCase(type)) {
                label = Constants.POD_NAME_MATRIX_MYSQL;
            } else if (DBType.es.name().equalsIgnoreCase(type)) {
                label = Constants.POD_NAME_ES;
            } else if (DBType.nightingale.name().equalsIgnoreCase(type)) {
                label = Constants.POD_NAME_NIGHTINGALE_MID;
            }
            List<com.xylink.manager.model.deploy.Pod> pods = deployService.listPodsByAppLabel(label);
            if (CollectionUtils.isEmpty(pods)) {
                logger.warn("db pod not exist, db: {}, dbType: {},type: {}", db, dbType, type);
                return false;
            }
            return StringUtils.equalsIgnoreCase(pods.get(0).getStatusPhase(), "Running");
        } catch (Exception e) {
            logger.warn("getDbPodStatus type:{}, db:{}, dbType:{},exception : {}", type, db, dbType, e.getMessage());
        }
        return false;
    }


    /**
     * 根据配置获取configmap指定数据,主要针对部分configmap存储数据较多,避免返回大量无用数据（前段使用较多）
     *
     * @param limit 参见 ConfigmapCtrlEnum
     */
    public Map<String, String> getConfigmapWithLimit(String limit) {
        if (ConfigmapCtrlEnum.hasOne(limit)) {
            ConfigmapCtrlEnum ctrlEnum = ConfigmapCtrlEnum.valueOf(limit);
            com.xylink.manager.model.deploy.ConfigMap configMap = deployService.getConfigMapByName(ctrlEnum.getConfigmap());
            if (configMap == null) {
                return null;
            }
            Map<String, String> data = configMap.getData();
            if (ctrlEnum.getEditableKeys() == null) {
                return data;
            } else {
                Map<String, String> result = new HashMap<>();
                ctrlEnum.getEditableKeys().forEach(key -> result.put(key, StringUtils.isBlank(data.get(key)) ? ctrlEnum.getDefaultKeyValues().get(key) : data.get(key)));
                return result;
            }
        } else {
            return null;
        }
    }

    /**
     * 根据ConfigmapCtrlEnum修改configMap时，替换空值为默认值
     */
    public void editConfigmapWithLimit(Map<String, String> data, String limit) {
        Map<String, String> defaultKeyValues = ConfigmapCtrlEnum.valueOf(limit).getDefaultKeyValues();
        data.entrySet().stream().filter(entry -> StringUtils.isBlank(entry.getValue()))
                .forEach(defaultKV -> data.put(defaultKV.getKey(), defaultKeyValues.get(defaultKV.getKey())));
        this.editConfigmap(ConfigmapCtrlEnum.valueOf(limit).getConfigmap(), data);
    }


    /**
     * 根据name查询configmap全量数据
     */
    public Map<String, String> getConfigmap(String name) {
        com.xylink.manager.model.deploy.ConfigMap configMap = deployService.getConfigMapByName(name);
        Map<String, String> configMapData = configMap == null ? new HashMap<>() : configMap.getData();
        if (Constants.CONFIGMAP_IPTABLES.equals(name)) {
            getInnerAndCustomIpsInCm(configMapData);
        }
        return configMapData;
    }

    public void editConfigmap(String namespace, String name, Consumer<Map<String, String>> consumer) {
        deployService.patchConfigMap(name, namespace, d -> {
            consumer.accept(d);
            logger.info("edit configmap {} as: {}", name, d);
        });
    }

    public Map<String, String> getConfigmapOrCreate(String namespace, String name) {
        ConfigMap configMap = deployService.getConfigMapByName(name, namespace);
        if (configMap != null) {
            return configMap.getData();
        }
        deployService.createEmptyConfigMap(name, namespace);
        return new HashMap<>();
    }

    /**
     * 根据name查询configmap全量数据,如当前configmap不存在则创建
     */
    public Map<String, String> getConfigmapOrCreate(String name) {
        return getConfigmapOrCreate(Constants.NAMESPACE_DEFAULT, name);
    }


    /**
     * 根据name修改configmap,在原有数据上追加
     */
    public void editConfigmap(String name, Map<String, String> data) {
        this.editConfigmap(Constants.NAMESPACE_DEFAULT, name, data);
    }

    /**
     * 在原有configmap上追加
     * 原数据：key1: value1
     * 新增数据：key2: value2
     * <p>
     * 新增之后configmap结果：
     * key1: value1
     * key2: value2
     *
     * @param name configmap名字
     * @param data 数据
     */
    public void patchConfigMap(String name, Map<String, String> data) {
        logger.info("config map name: {}, patch data: {}", name, data);
        deployService.patchConfigMap(name, Constants.NAMESPACE_DEFAULT, d -> d.putAll(data));
    }

    public void removeDataFromCM(String name, String... keys) {
        if (keys == null || keys.length == 0) {
            return;
        }
        logger.info("config map name:{},keys to remove:{}", name, keys);
        deployService.patchConfigMap(name, Constants.NAMESPACE_DEFAULT, d -> {
            for (String key : keys) {
                d.remove(key);
            }
        });
    }

    public void editConfigmap(String namespace, String name, Map<String, String> data) {
        editConfigmap(namespace, name, d -> d.putAll(data));
    }

    /**
     * 根据name修改configmap,清空原有数据
     */
    public void replaceConfigmap(String name, Map<String, String> data) {
        deployService.patchConfigMap(name, Constants.NAMESPACE_DEFAULT, d -> {
            d.clear();
            if (Objects.nonNull(data)) {
                d.putAll(data);
            }
        });
        logger.info("replace configmap {} as: {}", name, data);
    }


    /**
     * 通过调用kubelet 接口, 实现nginx reload
     */
    public void reloadNginxByKubeletRest(String label) {
        if (StringUtils.isBlank(label)) return;
        deployService.listPodsByAppLabel(label)
                .stream()
                .filter(it -> StringUtils.equalsIgnoreCase(it.getStatusPhase(), "Running"))
                .forEach(pod -> {
                    boolean ipChange = deployService.executeCommandForPodGotSuccess(pod.getPodName(), pod.getNamespace(), new String[]{"sh", "/usr/libra/ip_change/ip_change.sh"});
                    if (!ipChange) {
                        logger.warn("nginx [{}] reload failed", pod.getPodName());
                        return;
                    }
                    logger.info("nginx reload, request: ip {}, pod: {}", pod.getHostIp(), pod.getPodName());
                    try {
                        boolean reload = deployService.executeCommandForPodGotSuccess(pod.getPodName(), pod.getNamespace(), new String[]{"/usr/local/openresty/nginx/sbin/nginx", "-s", "reload"});
                        if (!reload) {
                            throw new ServerException(String.format("%s reload失败!", pod.getHostIp()));
                        }
                    } catch (RestClientException e) {
                        if (e instanceof HttpServerErrorException) {
                            HttpServerErrorException httpError = (HttpServerErrorException) e;
                            logger.error("reload nginx error: code {}, result: {}", httpError.getRawStatusCode(), httpError.getResponseBodyAsString(), e);
                        } else {
                            logger.error("reload nginx error ", e);
                        }
                        throw new ServerException(String.format("%s reload失败!", pod.getHostIp()));
                    }
                });
    }

    public void restartJob(String name) {
        deployService.restartJob(name, Constants.NAMESPACE_DEFAULT);
    }

    public boolean isRunning(String key, String label) {
        return deployService.listPodsByLabels(key, label)
                .stream()
                .anyMatch(it -> StringUtils.equalsIgnoreCase(it.getStatusPhase(), "Running"));
    }

    /**
     * 删除node标签,格式 service label:xylink
     */
    public void removeNodeLabel(Labels label) {
        deployService.removeNodeAppLabel(null, label.label());
    }

    /**
     * 获取数据库类型（MYSQL、ST、DM）
     */
    public String getDataBaseType() {
        return getConfigmap(Constants.CONFIGMAP_ALLIP).getOrDefault(NetworkConstants.DATABASE_TYPE, "MYSQL");
    }


    public List<com.xylink.manager.model.deploy.Node> getNodeByLabel(String key, String value) {
        return deployService.listNodesByLabels(key, value);
    }

    public void updateNodeLabel(String nodeName, String key, String value) {
        deployService.addNodeLabel(nodeName, key, value);
    }

    /**
     * 获取开关配置
     */
    public boolean getSwitch(String configMapKey) {
        Map<String, String> allIp = getConfigmap(Constants.CONFIGMAP_ALLIP);
        String switchConfig = allIp.get(configMapKey);
        return !StringUtils.isBlank(switchConfig) && Boolean.parseBoolean(switchConfig);
    }

    /**
     * 根据label app删除pod
     */
    public void deletePodByLabelApp(String appValue) {
        try {
            deployService.deletePodByAppLabel(appValue);
        } catch (Exception e) {
            logger.error("deletePodByLabelApp failed. appValue = {}", appValue, e);
        }
    }

    public void deletePod(String podName) {
        if (StringUtils.isBlank(podName)) {
            return;
        }
        deployService.deletePodByName(podName, Constants.NAMESPACE_DEFAULT);
    }

    /**
     * left : DATABASE_IP Middle:MANAGER_ADDRESS  right:DATABASE_PORT
     */
    public Triple<String, String, String> getStandbyMainDataBaseInfo() {
        Map<String, String> allIps = getConfigmap(Constants.CONFIGMAP_ALLIP);
        return Triple.of(allIps.get(NetworkConstants.STANDBY_SYSTEM_DATABASE_IP), allIps.get(NetworkConstants.STANDBY_SYSTEM_MANAGER_ADDRESS), allIps.get(NetworkConstants.STANDBY_SYSTEM_DATABASE_PORT));
    }

    /**
     * left : DATABASE_IP Middle:MANAGER_ADDRESS  right:DATABASE_PORT
     */
    public Triple<String, String, String> getMainDataBaseInfo() {
        Map<String, String> allIps = getConfigmap(Constants.CONFIGMAP_ALLIP);
        return Triple.of(allIps.get(NetworkConstants.MASTER_SYSTEM_DATABASE_IP), allIps.get(NetworkConstants.MASTER_SYSTEM_MANAGER_ADDRESS), allIps.get(NetworkConstants.MASTER_SYSTEM_DATABASE_PORT));
    }

    private String getMasterUrl() {
        URL masterUrl = this.clientBuilder.getClient().getMasterUrl();
        return String.valueOf(masterUrl);
    }

    public String summaryUrl(String nodeIp) {
        return k8sProtocolHttp ? ("http://" + Ipv6Util.handlerIpv6Addr(nodeIp) + ":10255" + STATS_SUMMARY) : getNodeProxyPre(nodeIp) + STATS_SUMMARY;
    }

    public String getNodeProxyPre(String nodeIp) {
        try {
            List<NodeCache> nodeCacheList = cacheService.cacheNodeList();
            NodeCache node = nodeCacheList.stream().filter(nodeCache -> {
                List<NodeAddressCache> nodeAddressCacheList = nodeCache.getStatus().getAddresses();
                NodeAddressCache nodeAddress = nodeAddressCacheList.stream().filter(nodeAddressCache -> "InternalIP".equalsIgnoreCase(nodeAddressCache.getType())).findFirst().get();
                return nodeIp.equals(nodeAddress.getAddress());
            }).findFirst().get();
            return getMasterUrl() + "api/v1/nodes/" + node.getMetadata().getName() + "/proxy";
        } catch (Exception e) {
            logger.error("getNodeProxyPre failed. nodeIp = {} ", nodeIp, e);
            return getMasterUrl() + "api/v1/nodes/" + nodeIp + "/proxy";
        }
    }

    public String getExportKubeConfigExec() {
        return "export KUBECONFIG=" + baseDir + "/kubeconfig";
    }

    private void getInnerAndCustomIpsInCm(Map<String, String> configMapData) {
        String innerClusterIps = configMapData.get(IptablesConstants.INNER_CLUSTER_IPS);
        String customClusterIps = configMapData.get(IptablesConstants.CUSTOM_CLUSTER_IPS);
        innerClusterIps = innerClusterIps == null ? "" : innerClusterIps;
        customClusterIps = customClusterIps == null ? "" : customClusterIps;
        String ips = configMapData.get("ips.txt");
        if (StringUtils.isBlank(customClusterIps) && StringUtils.isBlank(innerClusterIps)) {
            Set<String> internalIps = getInnerIps();
            if (StringUtils.isNotBlank(ips)) {
                updateInnerIpsAndCustomIps(ips, internalIps, configMapData);
            } else {
                configMapData.put("ips.txt", String.join("\n", internalIps));
                configMapData.put(IptablesConstants.INNER_CLUSTER_IPS, String.join("\n", internalIps));
            }
            this.editConfigmap(Constants.CONFIGMAP_IPTABLES, configMapData);
        } else {
            String innerAndCustomIps = innerClusterIps.trim() + "\n" + customClusterIps.trim();
            String newIps = null;
            if (StringUtils.isNotBlank(ips)) {
                newIps = ips.replace("\n", "");
            }
            String newInnerAndCustomIps = innerAndCustomIps.replace("\n", "");
            if (!newInnerAndCustomIps.equals(newIps)) {
                if (StringUtils.isNotBlank(ips)) {
                    updateIps(ips, innerClusterIps, customClusterIps, configMapData);
                } else {
                    configMapData.put("ips.txt", innerAndCustomIps);
                }
                this.editConfigmap(Constants.CONFIGMAP_IPTABLES, configMapData);
            }
        }
    }

    private void updateIps(String ips, String innerClusterIps, String customClusterIps, Map<String, String> configMapData) {
        String[] ipsList = ips.split("\n");
        List<String> innerIpsList = Arrays.asList(innerClusterIps.split("\n"));
        List<String> customIpsList = Arrays.asList(customClusterIps.split("\n"));
        StringBuffer innerIps = new StringBuffer();
        StringBuffer customIps = new StringBuffer();
        Set<String> inClusterIps = getInnerIps();
        Arrays.stream(ipsList).forEach(ip -> {
            if (innerIpsList.contains(ip)) {
                innerIps.append(ip).append("\n");
            } else if (customIpsList.contains(ip)) {
                customIps.append(ip).append("\n");
            } else {
                if (inClusterIps.contains(ip)) {
                    innerIps.append(ip).append("\n");
                } else {
                    customIps.append(ip).append("\n");
                }
            }
        });
        configMapData.put(IptablesConstants.INNER_CLUSTER_IPS, innerIps.toString().trim());
        configMapData.put(IptablesConstants.CUSTOM_CLUSTER_IPS, customIps.toString().trim());
    }

    private Set<String> getInnerIps() {
        Set<String> internalIps = Collections.synchronizedSet(new HashSet<>());
        cacheService.cacheNodeList().stream()
                .map(nodeCache -> nodeCache.getStatus().getAddresses().stream().filter(x -> IptablesConstants.INTERNAL_IP.equalsIgnoreCase(x.getType())).map(NodeAddressCache::getAddress).findFirst().get())
                .forEach(internalIps::add);
        return internalIps;
    }

    private void updateInnerIpsAndCustomIps(String ips, Set<String> internalIps, Map<String, String> configMapData) {
        String[] ipsList = ips.split("\n");
        StringBuffer innerIps = new StringBuffer();
        StringBuffer customIps = new StringBuffer();
        Arrays.stream(ipsList).forEach(ip -> {
            if (internalIps.contains(ip)) {
                innerIps.append(ip).append("\n");
            } else {
                customIps.append(ip).append("\n");
            }
        });
        configMapData.put(IptablesConstants.INNER_CLUSTER_IPS, innerIps.toString());
        configMapData.put(IptablesConstants.CUSTOM_CLUSTER_IPS, customIps.toString());
    }

    public Optional<com.xylink.manager.model.deploy.Pod> getPodWithLabelInApp(String appName) {
        return deployService.listPodsByAppLabel(appName)
                .stream()
                .findFirst();
    }

    public List<com.xylink.manager.model.deploy.Pod> getPodListWithLabelInApp(String appName) {
        return deployService.listPodsByAppLabel(appName);
    }

    public List<com.xylink.manager.model.deploy.Pod> getPodListWithLabelsInApp(String[] appNames) {
        return deployService.listPodsByLabels("app", appNames);
    }

    //自动重启服务-重启所有节点上对应的pod
    public void restartAllPodByNodeAppLabel(String label) {
        List<String> services = Constants.labelToPodNames.get(label);
        List<com.xylink.manager.model.deploy.Pod> deletePods = deployService.listPodsByAppLabels(services);
        if (CollectionUtils.isEmpty(deletePods)) {
            logger.info("no pod need delete");
            return;
        }
        for (com.xylink.manager.model.deploy.Pod pod : deletePods) {
            logger.info("delete pod: {}", pod.getPodName());
            deployService.deletePod(pod);
        }
    }

    //自动重启服务-重启指定节点对应的pod
    public void restartOnePodByNodeAppLabelAndNodeName(String label, String nodeName) {
        List<String> services = Constants.labelToPodNames.get(label);
        List<com.xylink.manager.model.deploy.Pod> deletePods = deployService.listPodsByAppLabels(services)
                .stream()
                .filter(it -> StringUtils.equals(it.getNodeName(), nodeName))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deletePods)) {
            logger.info("no pod need delete");
            return;
        }
        for (com.xylink.manager.model.deploy.Pod pod : deletePods) {
            logger.info("delete pod: {}", pod.getPodName());
            deployService.deletePod(pod);
        }
    }

    /**
     * 根据appLabel获取pod host信息列表
     */
    public List<PodHostInfoDTO> getPodHostInfoList(String appLabel) {
        if (StringUtils.isBlank(appLabel)) {
            return Collections.emptyList();
        }
        return deployService.listPodsByAppLabel(appLabel).stream()
                .map(it -> new PodHostInfoDTO(it.getPodName(), it.getNodeName(), it.getHostIp()))
                .collect(Collectors.toList());
    }

    /**
     * 国密相关nginx镜像操作
     * <p>
     * <code>
     * 新镜像=镜像+"-gm"
     * </code>
     */
    public void changeDaemonSetKindToGmImage(String daemonSetName) {
        DaemonSet daemonSet = deployService.getDaemonSetByName(daemonSetName, Constants.NAMESPACE_DEFAULT);
        if (daemonSet == null) {
            printLogForChangeImageNoApp("DaemonSet", daemonSetName);
            return;
        }
        List<Container> containers = daemonSet.getContainers();
        List<Container> initContainers = daemonSet.getInitContainers();
        final String[] image = {null, null};
        containers.stream()
                .filter(c -> c.getName().equals(daemonSetName))
                .findFirst()
                .ifPresent(c -> {
                    image[0] = c.getImage();
                    if (!image[0].endsWith("-gm")) {
                        image[1] = image[0] + "-gm";
                        c.setImage(image[1]);
                    } else {
                        logAndRestart("DaemonSet", daemonSetName, image[0]);
                    }
                });
        if (image[1] != null) {
            printLogForChangeImage("DaemonSet", daemonSetName, image[0], image[1]);
            initContainers.stream()
                    .filter(c -> c.getImage().equals(image[0]))
                    .findFirst()
                    .ifPresent((c -> c.setImage(image[1])));
            deployService.patchDaemonSetImage(daemonSetName, Constants.NAMESPACE_DEFAULT, containers, initContainers);
        }
    }

    public void changeDaemonSetKindToCommonImage(String daemonSetName) {
        DaemonSet daemonSet = deployService.getDaemonSetByName(daemonSetName, Constants.NAMESPACE_DEFAULT);
        if (daemonSet == null) {
            printLogForChangeImageNoApp("DaemonSet", daemonSetName);
            return;
        }
        List<Container> containers = daemonSet.getContainers();
        List<Container> initContainers = daemonSet.getInitContainers();
        final String[] image = {null, null};
        containers.stream()
                .filter(c -> c.getName().equals(daemonSetName))
                .findFirst()
                .ifPresent(c -> {
                    image[0] = c.getImage();
                    if (image[0].endsWith("-gm")) {
                        image[1] = image[0].replace("-gm", "");
                        c.setImage(image[1]);
                    } else {
                        logAndRestart("DaemonSet", daemonSetName, image[0]);
                    }
                });
        if (image[1] != null) {
            printLogForChangeImage("DaemonSet", daemonSetName, image[0], image[1]);
            initContainers.stream()
                    .filter(c -> c.getImage().equals(image[0]))
                    .findFirst()
                    .ifPresent((c -> {
                        c.setImage(image[1]);
                    }));
            deployService.patchDaemonSetImage(daemonSetName, Constants.NAMESPACE_DEFAULT, containers, initContainers);
        }
    }

    public void changeDeploymentKindToGmImage(String deploymentName) {
        Deployment deployment = deployService.getDeploymentByName(deploymentName, Constants.NAMESPACE_DEFAULT);
        if (deployment == null) {
            printLogForChangeImageNoApp("Deployment", deploymentName);
            return;
        }
        List<Container> containers = deployment.getContainers();
        List<Container> initContainers = deployment.getInitContainers();
        final String[] image = {null, null};
        containers.stream()
                .filter(c -> c.getName().equals(deploymentName))
                .findFirst()
                .ifPresent(c -> {
                    image[0] = c.getImage();
                    if (!image[0].endsWith("-gm")) {
                        image[1] = image[0] + "-gm";
                        c.setImage(image[1]);
                    } else {
                        logAndRestart("Deployment", deploymentName, image[0]);
                    }
                });
        if (image[1] != null) {
            printLogForChangeImage("Deployment", deploymentName, image[0], image[1]);
            initContainers.stream()
                    .filter(c -> c.getImage().equals(image[0]))
                    .findFirst()
                    .ifPresent((c -> c.setImage(image[1])));
            deployService.patchDeploymentImage(deploymentName, Constants.NAMESPACE_DEFAULT, containers, initContainers);
        }
    }

    public void changeDeploymentKindToCommonImage(String deploymentName) {
        Deployment deployment = deployService.getDeploymentByName(deploymentName, Constants.NAMESPACE_DEFAULT);
        if (deployment == null) {
            printLogForChangeImageNoApp("Deployment", deploymentName);
            return;
        }
        List<Container> containers = deployment.getContainers();
        List<Container> initContainers = deployment.getInitContainers();
        final String[] image = {null, null};
        containers.stream()
                .filter(c -> c.getName().equals(deploymentName))
                .findFirst()
                .ifPresent(c -> {
                    image[0] = c.getImage();
                    if (image[0].endsWith("-gm")) {
                        image[1] = image[0].replace("-gm", "");
                        c.setImage(image[1]);
                    } else {
                        logAndRestart("Deployment", deploymentName, image[0]);
                    }
                });
        if (image[1] != null) {
            printLogForChangeImage("Deployment", deploymentName, image[0], image[1]);
            initContainers.stream()
                    .filter(c -> c.getImage().equals(image[0]))
                    .findFirst()
                    .ifPresent((c -> {
                        c.setImage(image[1]);
                    }));
            deployService.patchDeploymentImage(deploymentName, Constants.NAMESPACE_DEFAULT, containers, initContainers);
        }
    }

    private void logAndRestart(String kind, String appName, String currentImage) {
        printLogForNoChangeImage(kind, appName, currentImage);
        this.restartAllPodByNodeAppLabel(appName.replace("private-", ""));
    }

    private void printLogForChangeImageNoApp(String kind, String appName) {
        logger.info("Replace kind:{},appName:{} not found", kind, appName);
    }

    private void printLogForChangeImage(String kind, String appName, String currentImage, String newImage) {
        logger.info("Replace kind:{},appName:{},image:{} ------> {}", kind, appName, currentImage, newImage);
    }

    private void printLogForNoChangeImage(String kind, String appName, String currentImage) {
        logger.info("Replace kind:{},appName:{},current image:{},only restart app", kind, appName, currentImage);
    }

    /**
     * 根据 type获取node节点
     */
    public Optional<NodeCache> getNode(String type) {
        Objects.requireNonNull(type);
        return cacheService.cacheNodeList()
                .stream()
                .filter(item -> type.equals(item.getMetadata().getLabels().get(Constants.TYPE)))
                .findFirst();
    }

    /**
     * 是否部署服务
     */
    public boolean isDeployed(String appLabel) {
        return !CollectionUtils.isEmpty(deployService.listPodsByAppLabel(appLabel));
    }

    /**
     * 节点上是否配置部署服务
     */
    public boolean isConfigDeployed(String appLabel) {
        return !CollectionUtils.isEmpty(deployService.listNodesByAppLabel(appLabel));
    }

    public boolean isNewCms() {
        Map<String, String> configmap = getConfigmap(Constants.CUSTOMIZE_INSTALL);
        String deployType = configmap.get("DEPLOY_TYPE");
        logger.info("DEPLOY_TYPE flag: {}", deployType);
        return "cms".equalsIgnoreCase(deployType);
    }

    public boolean isContainerD() {
        Map<String, String> configmap = getConfigmap(Constants.CUSTOMIZE_INSTALL);
        String criType = configmap.get("CRI");
        logger.info("CRI flag: {}", criType);
        return "containerd".equalsIgnoreCase(criType);
    }

    public List<com.xylink.manager.model.deploy.Pod> getPodsByLabel(String namespace, String labelName, String labelValue) {
        try {
            return deployService.listPodsByLabels(labelName, labelValue)
                    .stream()
                    .filter(it -> StringUtils.equals(it.getNamespace(), namespace))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("fail to get pods for labelName: {}, labelName:{} ,labelValue: {}", namespace, labelName, labelValue, e);
            throw new ServiceErrorException(ErrorStatus.UNEXPECTED_ERROR);
        }
    }

    public ConfigMapCache getConfigMapCacheOrCreateIfNotExist(String configMapName) {
        ConfigMapCache configMapCache = cacheService.cacheConfigMapByName(configMapName);
        if (configMapCache == null) {
            //调用方法创建一个，然后再次从缓存获取数据
            deployService.createEmptyConfigMap(configMapName, Constants.NAMESPACE_DEFAULT);
            return cacheService.cacheConfigMapByName(configMapName);
        }
        return configMapCache;
    }

    /**
     * 触发kafka的高级配置刷新KAFKA_LISTENERS_SECURITY
     *
     * @param enableAuthorize
     */
    public void configKafkaCm(Boolean enableAuthorize) {
        logger.info("change authorize , start configKafkaCm, enableAuthorize:[{}]", enableAuthorize);
        Map<String, String> kafkaCm = getConfigmap(Constants.CONFIGMAP_KAFKA);
        if (Objects.isNull(kafkaCm)) {
            logger.info("kafkaCm is null");
            return;
        }
        boolean hasSASL = Objects.nonNull(enableAuthorize) && enableAuthorize;
        for (Map.Entry<String, String> next : kafkaCm.entrySet()) {
            String value = next.getValue();
            String key = next.getKey();

            if (Objects.nonNull(value) && value.contains("PLAINTEXT") && Objects.nonNull(key) && key.contains(KafkaCM.KAFKA_LISTENERS_SECURITY)) {
                String replaceValue;
                if (hasSASL) {
                    replaceValue = value.replace(":PLAINTEXT", ":SASL_PLAINTEXT");
                } else {
                    replaceValue = value.replace(":SASL_PLAINTEXT", ":PLAINTEXT");
                }
                kafkaCm.put(next.getKey(), replaceValue);
                logger.info("kafka cm replace [{}]'s value from [{}] to [{}]", next.getKey(), value, replaceValue);
            }
        }
        patchConfigMap(Constants.CONFIGMAP_KAFKA, kafkaCm);
    }
}