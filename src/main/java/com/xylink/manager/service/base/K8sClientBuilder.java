package com.xylink.manager.service.base;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import io.fabric8.kubernetes.client.Config;
import io.fabric8.kubernetes.client.ConfigBuilder;
import io.fabric8.kubernetes.client.DefaultKubernetesClient;
import io.fabric8.kubernetes.client.KubernetesClient;
import io.fabric8.kubernetes.client.utils.HttpClientUtils;
import okhttp3.OkHttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Service
public class K8sClientBuilder {
    private static final Logger logger = LoggerFactory.getLogger(K8sClientBuilder.class);

    public static ScheduledExecutorService executor = new ScheduledThreadPoolExecutor(4,
            new ThreadFactoryBuilder().setNameFormat("Watching_Pool-%d").setDaemon(true).build(),
            new ThreadPoolExecutor.AbortPolicy());

    @Value("${k8s.prefix.url}")
    private String k8sUrl;

    @Value("${k8s.protocol.default.http}")
    private boolean k8sProtocolHttp;

    @Value("${k8s.config.path}")
    private String configPath;

    private Config config;
    private KubernetesClient client;
    private OkHttpClient httpClient;

    @PostConstruct
    private void initKubernetesClient(){
        if (k8sProtocolHttp) {
            config = new ConfigBuilder().withMasterUrl(k8sUrl).build();
            client = new DefaultKubernetesClient(config);
            logger.info("init http k8s client");
        } else {
            System.setProperty("kubeconfig", configPath);
            client = new DefaultKubernetesClient();
            config = client.getConfiguration();
            config.setTrustCerts(true);
            logger.info("init https k8s client");
        }
        httpClient = HttpClientUtils.createHttpClient(config);
        httpClient = httpClient.newBuilder()
                .connectTimeout(5, TimeUnit.SECONDS)
                .readTimeout(3, TimeUnit.SECONDS)
                .build();
    }

    public KubernetesClient getClient() {
        return client;
    }

    public Config getConfig() {
        return config;
    }

    public OkHttpClient getHttpClient() {
        return httpClient;
    }
}
