package com.xylink.manager.service.base;

import com.xylink.config.Constants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServiceErrorException;
import com.xylink.manager.model.deploy.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Consumer;

public interface IDeployService {
    //--------------------------- 节点相关操作 ---------------------------

    /**
     * 列出系统中所有节点的信息。
     *
     * @return 包含所有节点信息的列表，不会为 null。
     */
    @Nonnull
    List<Node> listAllNodes();

    /**
     * 根据节点名称获取对应的节点信息。
     * 如果系统中存在与给定名称匹配的节点，则返回对应的节点对象；如果未找到，则返回 null。
     *
     * @param name 节点的名称，不可为空。表示需要查找的节点的唯一标识。
     * @return 如果找到对应名称的节点，则返回该节点对象；如果未找到，返回 null。
     */
    @Nullable
    Node getNodeByName(@Nonnull String name);

    /**
     * 根据指定的键和值标签数组列出符合条件的节点列表。
     *
     * @param key    用于匹配节点属性的键，不能为空。
     * @param labels 用于匹配节点属性值的标签数组，不能为空。
     * @return 返回匹配指定键和值标签的节点列表。
     */
    @Nonnull
    List<Node> listNodesByLabels(@Nonnull String key, @Nonnull String[] labels);

    /**
     * 根据指定的键和值标签筛选节点列表。
     *
     * @param key   用于筛选的键，表示节点的属性名称。
     * @param label 用于筛选的值标签，与键配合使用确定筛选条件。
     * @return 匹配筛选条件的节点列表。如果没有符合条件的节点，返回空列表。
     */
    @Nonnull
    default List<Node> listNodesByLabels(@Nonnull String key, @Nonnull String label) {
        return listNodesByLabels(key, new String[]{label});
    }

    /**
     * 根据xylink标签列出节点列表
     * 如果节点上有 xxx=xylink 的标签，则认为该节点需要部署xxx服务，当调用此方法查询xxx时，会返回该节点
     *
     * @param appName 应用名称（不是严格意义上的服务名称）
     * @return 符合标签的节点信息列表
     */
    @Nonnull
    default List<Node> listNodesByAppLabel(@Nonnull String appName) {
        return listNodesByLabels(appName, new String[]{Constants.XYLINK});
    }

    /**
     * 更新指定节点的标签信息。
     *
     * @param nodeName 节点的名称，不能为空。
     * @param consumer 用于修改节点标签的消费者函数，不能为空。消费者函数接受一个节点标签的映射，允许对标签进行更新或修改。
     */
    void patchNodeLabels(@Nonnull String nodeName, @Nonnull Consumer<Map<String, String>> consumer);

    /**
     * 为指定的节点添加标签。
     *
     * @param nodeName 节点的名称，不能为空。
     * @param labels   一个键值对形式的标签集合，用于标识节点特性，不能为空。
     */
    void addNodeLabels(@Nonnull String nodeName, @Nonnull Map<String, String> labels);

    /**
     * 给节点打标签，一次只能打一个标签
     *
     * @param nodeName 节点名称
     * @param key      标签键名
     * @param value    标签值
     */
    default void addNodeLabel(@Nonnull String nodeName, @Nonnull String key, @Nonnull String value) {
        Map<String, String> labels = new LinkedHashMap<>(1);
        labels.put(key, value);
        addNodeLabels(nodeName, labels);
    }

    /**
     * 给节点打上服务标签，xxx=xylink
     *
     * @param nodeName 节点名称
     * @param appName  应用名称（不是严格意义上的服务名称）
     */
    default void addNodeAppLabel(@Nonnull String nodeName, @Nonnull String... appName) {
        Map<String, String> labels = new LinkedHashMap<>(appName.length);
        for (String s : appName) {
            labels.put(s, Constants.XYLINK);
        }
        addNodeLabels(nodeName, labels);
    }

    /**
     * 移除指定节点上的标签。
     *
     * @param nodeName  节点的名称，不能为空。
     * @param labelKeys 要移除的标签键集合，不能为空。
     */
    void removeNodeLabels(@Nonnull String nodeName, @Nonnull String[] labelKeys);

    /**
     * 移除指定节点上的标签。
     *
     * @param nodeName  节点的名称，不能为空。
     * @param labelKeys 要移除的标签键集合，不能为空。
     */
    default void removeNodeLabels(@Nonnull String nodeName, @Nonnull Collection<String> labelKeys) {
        removeNodeLabels(nodeName, labelKeys.toArray(new String[0]));
    }

    /**
     * 移除指定节点上键为 key 的标签。
     *
     * @param nodeName 节点名称，不能为空
     * @param key      标签的键，不能为空
     */
    default void removeNodeLabel(@Nonnull String nodeName, @Nonnull String key) {
        removeNodeLabels(nodeName, new String[]{key});
    }

    /**
     * 移除节点上的应用标签，xxx=xylink
     *
     * @param nodeName 节点名称，可以为 null。如果为 null，将应用标签从所有与该应用相关的节点中移除。
     * @param appName  应用名称，不可为 null。表示需要移除的应用标签的名称。
     */
    void removeNodeAppLabel(@Nullable String nodeName, @Nonnull String appName);

    /**
     * 删除指定名称的节点。
     *
     * @param nodeName 节点名称，不可为 null。表示需要删除的节点的名称。
     */
    void deleteNode(@Nonnull String nodeName);

    /**
     * 批准指定节点的证书。
     *
     * @param nodeName 节点的名称，不能为空。
     */
    void approveNodeCertificate(@Nonnull String nodeName);

    //--------------------------- Pod相关操作 ---------------------------

    /**
     * 列出所有 Pod。
     *
     * @return 包含所有 Pod 信息的列表，不会为 null。
     */
    @Nonnull
    List<Pod> listAllPod();

    /**
     * 获取指定命名空间中所有的 Pod 列表。
     *
     * @param namespace 指定的命名空间名称，不能为空。
     * @return 返回属于指定命名空间的 Pod 列表，列表不为 null。
     */
    @Nonnull
    List<Pod> listAllPodByNamespace(@Nonnull String namespace);

    /**
     * 根据指定的标签键和值列出符合条件的 Pod 列表。
     *
     * @param key    标签键名，不可为 null。
     * @param labels 标签值数组，不可为 null。
     * @return 符合指定标签的 Pod 列表，不会为 null。
     */
    @Nonnull
    List<Pod> listPodsByNamespaceAndLabels(@Nonnull String namespace, @Nonnull String key, @Nonnull String[] labels);

    /**
     * 根据指定的标签键和值数组列出匹配的 Pods。
     *
     * @param key    标签的键，用于过滤 Pods。
     * @param labels 标签的值数组，用于匹配 Pods。
     * @return 返回匹配指定标签键和值的 Pod 列表。
     */
    @Nonnull
    default List<Pod> listPodsByLabels(@Nonnull String key, @Nonnull String[] labels) {
        return listPodsByNamespaceAndLabels(Constants.NAMESPACE_DEFAULT, key, labels);
    }

    /**
     * 根据指定的标签键和值列出符合条件的 Pod 列表。
     *
     * @param key   标签键名，不可为 null。
     * @param label 单个标签值，不可为 null。
     * @return 符合指定标签的 Pod 列表，不会为 null。
     */
    @Nonnull
    default List<Pod> listPodsByLabels(@Nonnull String key, @Nonnull String label) {
        return listPodsByLabels(key, new String[]{label});
    }

    /**
     * 根据应用标签列出符合条件的 Pod 列表。
     *
     * @param appName 应用名称，不可为 null。用于匹配 Pod 的 "app" 标签。
     * @return 符合指定应用标签的 Pod 列表，不会为 null。
     */
    @Nonnull
    default List<Pod> listPodsByAppLabel(@Nonnull String appName) {
        return listPodsByLabels("app", new String[]{appName});
    }

    @Nonnull
    default List<Pod> listPodsByAppLabels(@Nonnull Collection<String> appNames) {
        return listPodsByLabels("app", appNames.toArray(new String[0]));
    }

    /**
     * 删除指定的 Pod。
     *
     * @param pod 要删除的 Pod 对象，不可为 null。包含需要删除的 Pod 的详细信息。
     */
    void deletePod(@Nonnull Pod pod);

    /**
     * 根据指定的 Pod 名称删除默认命名空间下的 Pod。
     *
     * @param podName Pod 名称，不可为 null。表示需要删除的 Pod 的名称。
     */
    default void deletePodByName(@Nonnull String podName) {
        deletePodByName(podName, Constants.NAMESPACE_DEFAULT);
    }

    /**
     * 根据指定的 Pod 名称和命名空间删除 Pod。
     *
     * @param podName   Pod 名称，不可为 null。表示需要删除的 Pod 的名称。
     * @param namespace 命名空间，不可为 null。表示目标 Pod 所在的命名空间。
     */
    void deletePodByName(@Nonnull String podName, @Nonnull String namespace);

    /**
     * 根据应用标签删除所有符合条件的 Pod。
     *
     * @param appName 应用名称，不可为 null。用于匹配 Pod 的 "app" 标签，并删除符合条件的 Pod。
     */
    default void deletePodByAppLabel(@Nonnull String appName) {
        for (Pod pod : listPodsByAppLabel(appName)) {
            deletePod(pod);
        }
    }

    /**
     * 根据指定的标签键和值删除匹配的Pod。
     * 此方法会遍历所有匹配指定键和值的Pod，并逐个进行删除操作。
     *
     * @param key   指定的标签键，不能为空
     * @param label 指定的标签值，不能为空
     */
    default void deletePodByLabel(@Nonnull String key, @Nonnull String label) {
        for (Pod pod : listPodsByLabels(key, label)) {
            deletePod(pod);
        }
    }

    /**
     * 根据指定的标签键值对删除相应的 Pod。
     *
     * @param key   标签的键，非空值，用于指定匹配的标签分类。
     * @param label 标签的值数组，非空值，用于指定匹配的标签值。
     */
    default void deletePodByLabels(@Nonnull String key, @Nonnull String[] label) {
        for (Pod pod : listPodsByLabels(key, label)) {
            deletePod(pod);
        }
    }

    /**
     * 在指定的 pod 中执行命令，并判断是否成功。
     *
     * @param podName   pod 的名称，不能为空。
     * @param namespace pod 所在的命名空间，不能为空。
     * @param command   要执行的命令数组，不能为空。
     * @return 如果命令执行成功，返回 true；否则返回 false。
     */
    default boolean executeCommandForPodGotSuccess(@Nonnull String podName,
                                                   @Nonnull String namespace,
                                                   @Nonnull String[] command) {
        return executeCommandForPodGotSuccess(podName, namespace, null, command);
    }

    /**
     * 执行指定Pod内的命令并判断是否成功。
     *
     * @param podName       Pod的名称，不允许为空。
     * @param namespace     Pod所在的命名空间，不允许为空。
     * @param containerName 容器的名称，可以为空。如果为空，则默认执行到Pod的主容器。
     * @param command       要执行的命令，不允许为空。
     * @return 如果命令执行成功返回true；如果发生异常或执行失败返回false。
     */
    default boolean executeCommandForPodGotSuccess(@Nonnull String podName,
                                                   @Nonnull String namespace,
                                                   @Nullable String containerName,
                                                   @Nonnull String[] command) {
        Logger logger = LoggerFactory.getLogger(IDeployService.class);
        try {
            String output = executeCommandForPod(podName, namespace, containerName, command);
            logger.info("execute command for pod {} in namespace {} success, output: {}", podName, namespace, output);
            return true;
        } catch (ServiceErrorException e) {
            return false;
        }
    }

    /**
     * 执行指定 Pod 的命令。
     *
     * @param podName   Pod 的名称，不能为 null。
     * @param namespace Pod 所在的命名空间，不能为 null。
     * @param command   要在 Pod 中执行的命令，不能为 null，可以包含多个命令参数。
     * @return 执行命令后的输出结果，永远不为 null。
     */
    @Nonnull
    default String executeCommandForPod(@Nonnull String podName,
                                        @Nonnull String namespace,
                                        @Nonnull String[] command) {
        return executeCommandForPod(podName, namespace, null, command);
    }

    /**
     * 在指定的 Pod 中执行命令，并返回执行结果。
     *
     * @param podName       Pod 的名称，不能为 null。
     * @param namespace     Pod 所在的命名空间，不能为 null。
     * @param containerName 容器的名称，如果为 null，则在默认容器中执行命令。
     * @param command       要在 Pod 中执行的命令，不能为 null。
     * @return 命令的执行结果，以字符串形式返回。
     * @throws ServiceErrorException 如果命令执行失败或超时，将抛出此异常。
     */
    @Nonnull
    default String executeCommandForPod(@Nonnull String podName,
                                        @Nonnull String namespace,
                                        @Nullable String containerName,
                                        @Nonnull String[] command) {
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        Future<Void> future = executeCommandForPod(podName, namespace, containerName, stream, stream, command);
        try {
            future.get(30, TimeUnit.MINUTES);
            return stream.toString();
        } catch (ExecutionException | InterruptedException | TimeoutException e) {
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
            Logger logger = LoggerFactory.getLogger(IDeployService.class);
            logger.error("execute command for pod {} in namespace {} failed: {}", podName, namespace, e.getMessage());
            throw new ServiceErrorException(ErrorStatus.UNEXPECTED_ERROR);
        }
    }

    /**
     * 在指定的 Kubernetes Pod 中执行命令。
     *
     * @param podName       要执行命令的 Pod 的名称，不能为空。
     * @param namespace     Pod 所在的命名空间，不能为空。
     * @param containerName 指定 Pod 中的容器名称，若为 null，则默认为第一个容器。
     * @param outputStream  用于接收命令标准输出的输出流，不能为空。
     * @param errorStream   用于接收命令错误输出的输出流，不能为空。
     * @param command       需要在 Pod 中执行的命令，不能为空。
     * @return 一个表示命令执行状态的 Future 对象。
     */
    @Nonnull
    Future<Void> executeCommandForPod(@Nonnull String podName,
                                      @Nonnull String namespace,
                                      @Nullable String containerName,
                                      @Nonnull OutputStream outputStream,
                                      @Nonnull OutputStream errorStream,
                                      @Nonnull String[] command);

    //--------------------------- ConfigMap相关操作 ---------------------------

    /**
     * 根据指定名称获取默认命名空间(不一定是default)下的 ConfigMap 信息。
     *
     * @param name ConfigMap 的名称，不可为 null。
     * @return 如果找到对应的 ConfigMap，则返回其详细信息；如果未找到，返回 null。
     */
    @Nullable
    default ConfigMap getConfigMapByName(@Nonnull String name) {
        return getConfigMapByName(name, Constants.NAMESPACE_DEFAULT);
    }

    /**
     * 根据指定的 ConfigMap 名称和命名空间获取对应 ConfigMap 的详细信息。
     *
     * @param name      ConfigMap 的名称，不可为 null。
     * @param namespace ConfigMap 所在的命名空间，不可为 null。
     * @return 如果找到对应的 ConfigMap，则返回其详细信息；如果未找到，返回 null。
     */
    @Nullable
    ConfigMap getConfigMapByName(@Nonnull String name, @Nonnull String namespace);

    /**
     * 根据指定的 ConfigMap 名称和命名空间获取对应 ConfigMap 的详细信息。
     * 如果未找到对应的 ConfigMap，则抛出 IllegalArgumentException。
     *
     * @param name      ConfigMap 的名称，不可为 null。
     * @param namespace ConfigMap 所在的命名空间，不可为 null。
     * @return 找到的 ConfigMap 对象，不会为 null。
     * @throws IllegalArgumentException 如果未找到对应的 ConfigMap 时抛出。
     */
    @Nonnull
    default ConfigMap getConfigMapByNameNotNull(@Nonnull String name, @Nonnull String namespace) {
        ConfigMap configMap = getConfigMapByName(name, namespace);
        if (configMap == null) {
            throw new IllegalArgumentException("configMap not found: " + name);
        }
        return configMap;
    }

    /**
     * 获取包含所有IP的配置映射（ConfigMap）。
     * 该方法默认从指定的命名空间中获取名为CONFIGMAP_ALLIP的ConfigMap。
     *
     * @return 返回获取到的ConfigMap对象，确保不为null
     */
    @Nonnull
    default ConfigMap getConfigMapAllIp() {
        return getConfigMapByNameNotNull(Constants.CONFIGMAP_ALLIP, Constants.NAMESPACE_DEFAULT);
    }

    /**
     * 获取配置映射管理器中的私有数据。
     *
     * @return 返回配置映射对象，如果不存在则返回null
     */
    @Nullable
    default ConfigMap getConfigMapManagerData() {
        return getConfigMapByName(Constants.CONFIGMAP_PRIVATE_DATA, Constants.NAMESPACE_DEFAULT);
    }

    void createEmptyConfigMap(@Nonnull String name, @Nonnull String namespace);

    /**
     * 更新指定命名空间下的 ConfigMap。如果ConfigMap不存在，会自动创建一个
     *
     * @param name      ConfigMap 的名称，不能为空。
     * @param namespace ConfigMap 所在的命名空间，不能为空。
     * @param consumer  用于修改 ConfigMap 数据的消费者对象，不能为空。
     * @return 更新后的 ConfigMap 数据，返回一个键值对的映射。
     */
    @Nonnull
    Map<String, String> patchConfigMap(@Nonnull String name, @Nonnull String namespace, @Nonnull Consumer<Map<String, String>> consumer);

    @Nonnull
    default Map<String, String> patchConfigMapAllIp(@Nonnull Consumer<Map<String, String>> consumer) {
        return patchConfigMap(Constants.CONFIGMAP_ALLIP, Constants.NAMESPACE_DEFAULT, consumer);
    }

    @Nonnull
    default Map<String, String> patchConfigMapAllIpForAddData(@Nonnull Map<String, String> addData) {
        return patchConfigMapAllIp(d -> d.putAll(addData));
    }

    /**
     * 根据给定的二进制数据更新指定的 ConfigMap。
     *
     * @param name      ConfigMap 的名称，不能为空。
     * @param namespace ConfigMap 所在的命名空间，不能为空。
     * @param data      一个消费者函数，用于操作和修改 ConfigMap 的二进制数据字段，不能为空。
     * @return 返回操作后更新的二进制数据的键值对集合。
     */
    @Nonnull
    Map<String, String> patchConfigMapByBinaryData(@Nonnull String name, @Nonnull String namespace, @Nonnull Consumer<Map<String, String>> data);

    /**
     * 删除指定名称和命名空间的 ConfigMap。
     *
     * @param name      ConfigMap 的名称，不可为 null。表示需要删除的 ConfigMap 的名称。
     * @param namespace ConfigMap 所在的命名空间，不可为 null。表示需要删除的 ConfigMap 所在的命名空间。
     */
    void deleteConfigMap(@Nonnull String name, @Nonnull String namespace);

    //--------------------------- Job相关操作 ---------------------------

    /**
     * @param namespace ConfigMap 所在的命名空间，不可为 null
     * @return
     */
    @Nonnull
    List<Job> listAllJob(@Nonnull String namespace);

    /**
     * 基于给定的 YAML 配置创建或替换一个Job。
     *
     * @param yaml 非空的 YAML 配置字符串，用于描述Job的详细信息。
     */
    @Nonnull
    Job createOrReplaceJobByYaml(@Nonnull String yaml);

    /**
     * 基于给定的 cronJob 配置创建一个Job。
     *
     * @param cronJobName 非空的 cronJob
     * @param jobName 非空的 jobName
     * @param namespace 作业所属的命名空间，不能为空
     */
    @Nullable
    Job createJobFromCronJob(@Nonnull String cronJobName,@Nonnull String jobName,@Nonnull String namespace);

    /**
     * 根据给定的作业名称和命名空间获取对应的作业信息。
     *
     * @param name      作业的名称，不能为空
     * @param namespace 作业所属的命名空间，不能为空
     * @return 如果找到对应的作业则返回作业对象，否则返回null
     */
    @Nullable
    Job getJobByName(@Nonnull String name, @Nonnull String namespace);

    /**
     * 根据指定的命名空间列出所有的作业列表。
     *
     * @param namespace 命名空间的唯一标识符，不能为null。
     * @return 包含指定命名空间下所有作业的列表，列表不会为null。
     */
    @Nonnull
    List<Job> listJobsByNamespace(@Nonnull String namespace);

    /**
     * 删除指定的作业。
     *
     * @param jobName   要删除的作业名称，不能为空
     * @param namespace 作业所在的命名空间，不能为空
     */
    void deleteJob(@Nonnull String jobName, @Nonnull String namespace);

    /**
     * 重启指定的Job。
     * 实际实现是将其删掉然后重新创建一个一样的
     *
     * @param jobName   要重启的Job名称，不能为空。
     * @param namespace Job所在的命名空间，不能为空。
     */
    void restartJob(@Nonnull String jobName, @Nonnull String namespace);

    @Nullable
    CronJob getCronJobByName(@Nonnull String name, @Nonnull String namespace);

    /**
     * 更新指定命名空间中的 CronJob，并修改其容器配置。
     *
     * @param cronJobName 必填，CronJob 的名称。
     * @param namespace   必填，目标 CronJob 所在的命名空间。
     * @param containers  必填，新的容器配置列表，用于覆盖 CronJob 的现有容器配置。
     */
    void patchCronJobCommand(@Nonnull String cronJobName, @Nonnull String namespace, @Nonnull List<Container> containers);

    //--------------------------- Service相关操作 ---------------------------

    /**
     * 根据提供的服务名称和命名空间获取对应的SVC实例。
     *
     * @param name      服务的名称，不能为空
     * @param namespace 服务的命名空间，不能为空
     * @return 返回对应的SVC实例，如果未找到则返回空
     */
    @Nullable
    SVC getSvcByName(@Nonnull String name, @Nonnull String namespace);

    //--------------------------- DaemonSet相关操作 ---------------------------

    /**
     * 根据指定的命名空间列出所有的 DaemonSet。
     *
     * @param namespace 命名空间的名称，不能为空。
     * @return 包含所有属于指定命名空间的 DaemonSet 的列表，列表不能为空但可能为空列表。
     */
    @Nonnull
    List<DaemonSet> listDaemonSetByNamespace(@Nonnull String namespace);

    /**
     * 根据指定的命名空间和标签列表列出所有匹配的 DaemonSet。
     *
     * @param namespace 命名空间名称，不能为空。
     * @param key       标签的键，不能为空。
     * @param labels    标签的值数组，不能为空。
     * @return 返回一个匹配指定命名空间和标签条件的 DaemonSet 列表。
     */
    @Nonnull
    List<DaemonSet> listDaemonSetByNamespaceAndLabels(@Nonnull String namespace, @Nonnull String key, @Nonnull String[] labels);

    /**
     * 根据指定的标签键和值数组获取符合条件的DaemonSet列表。
     *
     * @param key    标签的键，非空值，用于匹配目标DaemonSet。
     * @param labels 标签值数组，非空值，用于匹配目标DaemonSet。
     * @return 符合指定标签键和值的DaemonSet列表。若无匹配项，则返回空列表。
     */
    @Nonnull
    default List<DaemonSet> listDaemonSetByLabels(@Nonnull String key, @Nonnull String[] labels) {
        return listDaemonSetByNamespaceAndLabels(Constants.NAMESPACE_DEFAULT, key, labels);
    }

    /**
     * 根据指定的标签键和值，列出符合条件的DaemonSet。
     *
     * @param key   标签的键，不能为空。
     * @param label 标签的值，不能为空。
     * @return 返回符合指定键和值的DaemonSet列表，不能为空。
     */
    @Nonnull
    default List<DaemonSet> listDaemonSetByLabels(@Nonnull String key, @Nonnull String label) {
        return listDaemonSetByLabels(key, new String[]{label});
    }

    /**
     * 根据给定的应用标签列出所有匹配的 DaemonSet 对象。
     *
     * @param appName 应用标签的名称，用于查找匹配的 DaemonSet。
     * @return 匹配给定应用标签的 DaemonSet 列表。
     */
    @Nonnull
    default List<DaemonSet> listDaemonSetByAppLabel(@Nonnull String appName) {
        return listDaemonSetByLabels("app", new String[]{appName});
    }

    /**
     * 根据指定的应用标签列表获取对应的 DaemonSet 列表。
     *
     * @param appNames 应用标签的集合，指定需要匹配的应用标签名称，不能为空。
     * @return 返回匹配指定应用标签的 DaemonSet 列表，如果没有匹配的内容，则返回空列表。
     */
    @Nonnull
    default List<DaemonSet> listDaemonSetByAppLabels(@Nonnull Collection<String> appNames) {
        return listDaemonSetByLabels("app", appNames.toArray(new String[0]));
    }

    /**
     * 根据指定的名称和命名空间获取对应的 DaemonSet 对象。
     *
     * @param name      DaemonSet 的名称，不能为空。
     * @param namespace DaemonSet 所属的命名空间，不能为空。
     * @return 如果找到对应的 DaemonSet，则返回该对象；如果未找到，则返回 null。
     */
    @Nullable
    DaemonSet getDaemonSetByName(@Nonnull String name, @Nonnull String namespace);

    /**
     * 更新指定命名空间中 DaemonSet 的环境变量。
     *
     * @param name       DaemonSet 的名称，不能为空。
     * @param namespace  命名空间的名称，不能为空。
     * @param containers 包含更新的环境变量信息的容器列表，不能为空。
     */
    void patchDaemonSetEnv(@Nonnull String name, @Nonnull String namespace, @Nonnull List<Container> containers);

    /**
     * 更新指定DaemonSet中容器的镜像。
     *
     * @param name       DaemonSet的名称，不能为空。
     * @param namespace  DaemonSet所在的命名空间，不能为空。
     * @param containers 包含容器名称和新镜像的列表，不能为空。
     */
    void patchDaemonSetImage(@Nonnull String name, @Nonnull String namespace, @Nonnull List<Container> containers, @Nonnull List<Container> initContainers);

    /**
     * 删除指定命名空间中的指定 DaemonSet。
     *
     * @param name      DaemonSet 的名称，不能为空
     * @param namespace 命名空间的名称，不能为空
     */
    void deleteDaemonSet(@Nonnull String name, @Nonnull String namespace);

    //--------------------------- StatefulSet相关操作 ---------------------------

    /**
     * 根据指定的命名空间列出所有的 DaemonSet。
     *
     * @param namespace 命名空间的名称，不能为空。
     * @return 包含所有属于指定命名空间的 DaemonSet 的列表，列表不能为空但可能为空列表。
     */
    @Nonnull
    List<StatefulSet> listStatefulSetByNamespace(@Nonnull String namespace);

    //--------------------------- Deployment相关操作 ---------------------------

    /**
     * 根据指定的命名空间列出所有的 Deployment。
     *
     * @param namespace 命名空间的名称，不能为空。
     * @return 包含所有属于指定命名空间的 Deployment 的列表，列表不能为空但可能为空列表。
     */
    @Nonnull
    List<Deployment> listDeploymentByNamespace(@Nonnull String namespace);

    /**
     * 根据指定的命名空间和标签列表列出所有匹配的 Deployment。
     *
     * @param namespace 命名空间名称，不能为空。
     * @param key       标签的键，不能为空。
     * @param labels    标签的值数组，不能为空。
     * @return 返回一个匹配指定命名空间和标签条件的 Deployment 列表。
     */
    @Nonnull
    List<Deployment> listDeploymentByNamespaceAndLabels(@Nonnull String namespace, @Nonnull String key, @Nonnull String[] labels);

    /**
     * 根据指定的标签键和值数组获取符合条件的Deployment列表。
     *
     * @param key    标签的键，非空值，用于匹配目标Deployment。
     * @param labels 标签值数组，非空值，用于匹配目标Deployment。
     * @return 符合指定标签键和值的Deployment列表。若无匹配项，则返回空列表。
     */
    @Nonnull
    default List<Deployment> listDeploymentByLabels(@Nonnull String key, @Nonnull String[] labels) {
        return listDeploymentByNamespaceAndLabels(Constants.NAMESPACE_DEFAULT, key, labels);
    }

    /**
     * 根据指定的标签键和值，列出符合条件的Deployment。
     *
     * @param key   标签的键，不能为空。
     * @param label 标签的值，不能为空。
     * @return 返回符合指定键和值的Deployment列表，不能为空。
     */
    @Nonnull
    default List<Deployment> listDeploymentByLabels(@Nonnull String key, @Nonnull String label) {
        return listDeploymentByLabels(key, new String[]{label});
    }

    /**
     * 根据给定的应用标签列出所有匹配的 Deployment 对象。
     *
     * @param appName 应用标签的名称，用于查找匹配的 Deployment。
     * @return 匹配给定应用标签的 Deployment 列表。
     */
    @Nonnull
    default List<Deployment> listDeploymentByAppLabel(@Nonnull String appName) {
        return listDeploymentByLabels("app", new String[]{appName});
    }

    /**
     * 根据指定的应用标签列表获取对应的 Deployment 列表。
     *
     * @param appNames 应用标签的集合，指定需要匹配的应用标签名称，不能为空。
     * @return 返回匹配指定应用标签的 Deployment 列表，如果没有匹配的内容，则返回空列表。
     */
    @Nonnull
    default List<Deployment> listDeploymentByAppLabels(@Nonnull Collection<String> appNames) {
        return listDeploymentByLabels("app", appNames.toArray(new String[0]));
    }

    /**
     * 根据指定的名称和命名空间获取对应的 Deployment 对象。
     *
     * @param name      Deployment 的名称，不能为空。
     * @param namespace Deployment 所属的命名空间，不能为空。
     * @return 如果找到对应的 Deployment，则返回该对象；如果未找到，则返回 null。
     */
    @Nullable
    Deployment getDeploymentByName(@Nonnull String name, @Nonnull String namespace);

    /**
     * 调整指定部署的副本数量。
     *
     * @param deploymentName 部署的名称，不能为空。
     * @param namespace      部署所在的命名空间，不能为空。
     * @param replicas       要将部署缩放到的副本数量。
     */
    void scaleDeployment(@Nonnull String deploymentName, @Nonnull String namespace, int replicas);

    /**
     * 更新指定部署的容器镜像信息。
     *
     * @param name           部署名称，不能为空。
     * @param namespace      部署所在的命名空间，不能为空。
     * @param containers     需更新的容器列表，不能为空。
     * @param initContainers 需更新的初始化容器列表，不能为空。
     */
    void patchDeploymentImage(@Nonnull String name, @Nonnull String namespace, @Nonnull List<Container> containers, @Nonnull List<Container> initContainers);

    //--------------------------- Namespace相关操作 ---------------------------

    /**
     * 根据命名空间的名称获取对应的命名空间对象。
     *
     * @param name 命名空间的名称，不能为空
     * @return 命名空间对象，如果找不到对应的命名空间则返回空
     */
    @Nullable
    Namespace getNamespaceByName(@Nonnull String name);

    //--------------------------- 其他操作 ---------------------------

    /**
     * 实时监控指定 Pod 的日志输出。
     *
     * @param podName       Pod 的名称，不能为空。
     * @param namespace     命名空间的名称，不能为空。
     * @param containerName 容器的名称，可以为空。如果为空，默认监控 Pod 的主容器日志。
     * @param outputStream  输出流，不能为空，用于将日志输出到指定的流。
     */
    void watchPodLog(@Nonnull String podName,
                     @Nonnull String namespace,
                     @Nullable String containerName,
                     @Nonnull OutputStream outputStream);
}
