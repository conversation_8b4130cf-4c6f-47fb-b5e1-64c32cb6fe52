package com.xylink.manager.service.base.impl;

import com.google.gson.Gson;
import com.xylink.config.Constants;
import com.xylink.manager.controller.dto.servicemanage.ServerManageData;
import com.xylink.manager.controller.dto.servicemanage.ServerManageInitData;
import com.xylink.manager.iptables.util.AesUtil;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.service.api.NoahApiService;
import com.xylink.manager.service.base.SystemModeConfig;
import io.fabric8.kubernetes.api.model.Node;
import io.fabric8.kubernetes.api.model.Taint;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR> create on 2025/7/8
 */
@Service
@Slf4j
public class NoahServerManagerInitService {

    @Autowired
    private NoahApiService noahApiService;

    @Autowired
    private ServerManageCommonService commonService;

    @Autowired
    private K8sDeployService k8sDeployService;


    /**
     * CMS & XMS按型号初始化
     * 1、检测是否已经【初始化】过
     * 2、初始化对应型号服务列表
     * 3、写入【初始化完成】标识
     */
    public void initDeployServers() {
        if (hadInitServers()) {
            log.info("[init deploy] had init servers and return");
            return;
        }
        String productName = SystemModeConfig.productName();
        log.info("[init deploy] init servers start, productName:[{}]", productName);
        List<String> initServers = new ArrayList<>();
        if (Objects.isNull(productName)) {
            productName = "";
        }
        if (productName.startsWith(ServerManageData.ServerInit.PREFIX_CMS)) {
            log.info("[init deploy] start init cms1000 servers");
            initServers.addAll(ServerManageInitData.INIT_SERVERS_CMS1000);
        } else if (productName.startsWith(ServerManageData.ServerInit.PREFIX_XMS)) {
            log.info("[init deploy] start init xms1000 servers");
            initServers.addAll(ServerManageInitData.INIT_SERVERS_XMS1000);
        } else {
            log.info("[init deploy] productName not cms1000 or xms1000, so keep origin servers and return");
            return;
        }
        //将服务列表写入configmap
        log.info("[init deploy] start write servers to private-manager-data");
        writeServersToCm(initServers);

        if (productName.startsWith(ServerManageData.ServerInit.PREFIX_CMS)) {
            initServers.addAll(ServerManageInitData.CMS1000_NOT_START);
        }
        //服务下线操作
        log.info("[init deploy] start cancel servers");
        List<NoahApiService.NoahServerInfo> envServerList = noahApiService.serverList();
        List<NoahApiService.NoahServerInfo> needCancelDeployServers = new ArrayList<>();
        for (NoahApiService.NoahServerInfo server : envServerList) {
            if (!initServers.contains(server.getServerName())) {
                needCancelDeployServers.add(server);
            }
        }
        cancelDeployBatch(needCancelDeployServers);
        //通知配置中心更新
        log.info("[init deploy] start notify noah");
        noahApiService.notifyDeployLabelChange();
        //写入【已初始化】标识
        log.info("[init deploy] start update had_init_servers flag");
        k8sDeployService.patchConfigMap(Constants.CONFIGMAP_PRIVATE_DATA, Constants.NAMESPACE_DEFAULT,
                data -> data.put(ServerManageData.ServerInit.HAD_INIT_SERVERS, "true"));
        log.info("[init deploy] init deploy servers end");
    }

    private boolean hadInitServers() {
        log.info("[init deploy] start check had_init_servers");
        ConfigMap cm = k8sDeployService.getConfigMapByName(Constants.CONFIGMAP_PRIVATE_DATA, Constants.NAMESPACE_DEFAULT);
        if (Objects.isNull(cm)) {
            log.info("[init deploy] not found cm private-manager-data");
            return false;
        }
        Map<String, String> data = cm.getData();
        String hadInitServers = data.get(ServerManageData.ServerInit.HAD_INIT_SERVERS);
        log.info("[init deploy] had_init_servers flag: [{}]", hadInitServers);
        return "true".equalsIgnoreCase(hadInitServers);
    }

    private void writeServersToCm(List<String> initServers) {
        log.info("[init deploy] initServers:[{}]", initServers);
        k8sDeployService.patchConfigMap(Constants.CONFIGMAP_PRIVATE_DATA, Constants.NAMESPACE_DEFAULT, new Consumer<Map<String, String>>() {
            @Override
            public void accept(Map<String, String> data) {
                try {
                    String encrypt = AesUtil.encryptCBC(new Gson().toJson(initServers));
                    log.info("[init deploy]write to private-manager-data:[{}]", encrypt);
                    data.put(ServerManageData.ServerInit.INIT_SERVERS, encrypt);
                } catch (Exception e) {
                    log.info("[init deploy]writeServersToCm error {}", e.getMessage(), e);
                }
            }
        });
    }

    /**
     * 批量下线服务
     */
    private void cancelDeployBatch(List<NoahApiService.NoahServerInfo> serverLists) {
        List<String> serverNames = serverLists.stream().map(NoahApiService.NoahServerInfo::getServerName).collect(Collectors.toList());
        log.info("[init deploy] will cancel deploy servers: [{}]", serverNames);
        Map<String, Node> clusterNodes = commonService.getClusterNodes();
        //移除node的标签、污点
        cancelDeployBatch(serverNames, clusterNodes);
    }

    private void cancelDeployBatch(List<String> serverNames, Map<String, Node> clusterNodes) {
        log.info("[init deploy] start batch cancel deploy");
        for (Map.Entry<String, Node> entry : clusterNodes.entrySet()) {
            String nodeName = entry.getKey();
            Node node = entry.getValue();
            boolean hasChange = removeLabelAndTaintNodeBatch(serverNames, node);
            if (hasChange) {
                k8sDeployService.client().nodes().withName(nodeName).replace(node);
                log.info("[init deploy] {} remove origin label and taints changed node {}", serverNames, nodeName);
            }
        }
        log.info("[init deploy] end batch cancel deploy");
    }

    private boolean removeLabelAndTaintNodeBatch(List<String> serverNames, Node node) {
        //移除 [服务名:xylink] 标签
        boolean hasChange = false;
        String nodeName = node.getMetadata().getName();
        Map<String, String> labels = node.getMetadata().getLabels();
        if (Objects.isNull(labels)) {
            labels = new HashMap<>();
        }
        Iterator<Map.Entry<String, String>> iterator = labels.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, String> label = iterator.next();
            String labelKey = label.getKey();
            String labelValue = label.getValue();
            for (String serverName : serverNames) {
                String deployKey = serverName.replace(ServerManageData.NAME_PREFIX, "");
                String deployValue = ServerManageData.DeployConfig.NODE_SELECTOR_VALUE;
                if (StringUtils.equals(labelKey, deployKey) && StringUtils.equals(labelValue, deployValue)) {
                    log.info("[init deploy] {} node will remove label {}:{}", nodeName, deployKey, deployValue);
                    iterator.remove();
                    hasChange = true;
                }
            }
        }
        //移除 污点
        List<Taint> taints = node.getSpec().getTaints();
        if (Objects.isNull(taints)) {
            taints = new ArrayList<>();
        }
        Iterator<Taint> iter = taints.iterator();
        while (iter.hasNext()) {
            Taint taint = iter.next();
            String taintKey = taint.getKey();
            String taintValue = taint.getValue();
            String taintEffect = taint.getEffect();
            for (String serverName : serverNames) {
                String deployTaintKey = ServerManageData.DeployConfig.TAINT_KEY;
                String deployTaintValue = serverName.replace(ServerManageData.NAME_PREFIX, "");
                String deployTaintEffect = ServerManageData.DeployConfig.TAINT_EFFECT_NO_EXECUTE;
                if (StringUtils.equals(taintKey, deployTaintKey) && StringUtils.equals(taintValue, deployTaintValue)
                        && StringUtils.equals(taintEffect, deployTaintEffect)) {
                    log.info("[init deploy] {} node will remove taint {}={}:{}", nodeName, taintKey, taintValue, taintEffect);
                    iter.remove();
                    hasChange = true;
                }
            }
        }
        return hasChange;
    }
}
