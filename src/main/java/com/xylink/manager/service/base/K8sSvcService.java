package com.xylink.manager.service.base;

import com.google.common.collect.Lists;
import com.xylink.config.Constants;
import com.xylink.config.K8sSvcConstants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.model.deploy.SVC;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/11/15/10:41
 */
@Service
@Slf4j
public class K8sSvcService {
    @Autowired
    private IDeployService deployService;

    /**
     * 获取svcIP
     *
     * @param svcName svc名称
     */
    public String getServiceIpByDefaultNs(String svcName) {
        try {
            SVC svc = deployService.getSvcByName(svcName, Constants.NAMESPACE_DEFAULT);
            if (svc == null) {
                log.info("get svc ip from {} is null", svcName);
                return null;
            }
            String svcIP = svc.getClusterIp();
            log.info("get svc ip from {} is {}", svcName, svcIP);
            return svcIP;
        } catch (Exception ex) {
            log.info("getServiceIpByDefaultNs error {} and return null", ex.getMessage(), ex);
            return null;
        }
    }

    /**
     * 获取某台node上的logagent pod ipd
     *
     * @param nodeIp nodeIp
     * @return pod ip of logagent
     */
    public String getLogAgentPodIpByNodeIp(String nodeIp) {
        try {
            log.info("get loagent pod ip of:{}", nodeIp);
            if (StringUtils.isBlank(nodeIp)) {
                return null;
            }
            Optional<String> optional = deployService.listPodsByAppLabels(Lists.newArrayList("private-logagent", "private-logagent-x86", "private-logagent-arm"))
                    .stream()
                    .filter(pod -> StringUtils.equals(pod.getHostIp(), nodeIp))
                    .findFirst()
                    .map(com.xylink.manager.model.deploy.Pod::getIp);
            if (optional.isPresent()) {
                return optional.get();
            }
            log.info("not found logagent pod of {}", nodeIp);
            return null;
        } catch (Exception ex) {
            log.info("getLogAgentPodIpByNode error {} and return null", ex.getMessage(), ex);
            return null;
        }
    }

    public String getMainNodeInternalIPNotNull() {
        String svcIp = getServiceIpByDefaultNs(K8sSvcConstants.OPENRESTY_MAIN_INTERNAL);
        if (StringUtils.isNotBlank(svcIp)) {
            return svcIp;
        }
        try {
            com.xylink.manager.model.deploy.ConfigMap configMap = deployService.getConfigMapByName(Constants.CONFIGMAP_ALLIP, Constants.NAMESPACE_DEFAULT);
            if (configMap != null) {
                return configMap.getData().get(NetworkConstants.MAIN_INTERNAL_IP);
            }
        } catch (Exception e) {
            log.error("fail to get main internal ip!\n", e);
        }
        return "127.0.0.1";
    }

}
