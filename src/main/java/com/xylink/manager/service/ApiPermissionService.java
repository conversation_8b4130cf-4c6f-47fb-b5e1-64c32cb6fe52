package com.xylink.manager.service;

import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServiceErrorException;
import com.xylink.manager.model.ApiPermissionRequestDto;
import com.xylink.manager.model.ApiPermissionResponseDto;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.converter.FormHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StreamUtils;
import org.springframework.web.client.RequestCallback;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URI;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Enumeration;
import java.util.Iterator;
import java.util.List;


/**
 * <AUTHOR>
 * @Date: 2024/02/26/11:25
 * @Description:
 */
@Service
public class ApiPermissionService {

    private Logger logger = LoggerFactory.getLogger(ApiPermissionService.class);

    private static final String UAA_UPDATE_API_CONFIG = "/api/rest/uaabase/internal/api/updateConfig";

    private static final String UAA_QUERY_API_CONFIG = "/api/rest/uaabase/internal/api/config";

    private static final String UAA_QUERY_API_CONFIG_BY_ID = "/api/rest/uaabase/internal/api/config/%s";

    @Autowired
    ServerListService serverListService;
    @Autowired
    RestTemplate restTemplate;

    public void addApiPermission(ApiPermissionRequestDto apiPermissionRequestDto) {
        apiPermissionRequestDto.setOrigin("manager");
        String mainIp = serverListService.getMainNodeInternalIP();
        String url = "http://" + mainIp + ":11111/api/rest/uaabase/internal/gatewayWhiteApi/create/v1";
        try {
            restTemplate.postForObject(url, apiPermissionRequestDto, Void.class);
        } catch (Exception e) {
            logger.error("add api white list failed : ", e);
            throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_UAA_API_FAILED);
        }

    }


    public void updateApiPermission(ApiPermissionRequestDto apiPermissionRequestDto) {
        apiPermissionRequestDto.setOrigin("manager");
        String mainIp = serverListService.getMainNodeInternalIP();
        String url = "http://" + mainIp + ":11111/api/rest/uaabase/internal/gatewayWhiteApi/modify/v1";
        try {
            restTemplate.postForObject(url, apiPermissionRequestDto, Void.class);
        } catch (Exception e) {
            logger.error("update api white list failed : ", e);
            throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_UAA_API_FAILED);
        }

    }

    public void deleteApiPermission(ApiPermissionRequestDto apiPermissionRequestDto) {
        String mainIp = serverListService.getMainNodeInternalIP();
        String url = "http://" + mainIp + ":11111/api/rest/uaabase/internal/gatewayWhiteApi/remove/v1";
        try {
            restTemplate.postForObject(url, apiPermissionRequestDto, Void.class);
        } catch (Exception e) {
            logger.error("delete api white list failed : ", e);
            throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_UAA_API_FAILED);
        }

    }

    public ApiPermissionResponseDto getApiPermission(ApiPermissionRequestDto apiPermissionRequestDto) {
        String mainIp = serverListService.getMainNodeInternalIP();
        String url = null;
        URI uri = null;
        String requestPath = apiPermissionRequestDto.getRequestPath();
        if (StringUtils.isBlank(requestPath)) {
            url = "http://" + mainIp + ":11111/api/rest/uaabase/internal/gatewayWhiteApi/list/v1?page=" + apiPermissionRequestDto.getPage()
                    + "&limit=" + apiPermissionRequestDto.getLimit();
            try {
                return restTemplate.getForObject(url, ApiPermissionResponseDto.class);
            } catch (Exception e) {
                logger.error("get api white list failed : ", e);
                throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_UAA_API_FAILED);
            }

        } else {
            url = "http://" + mainIp + ":11111/api/rest/uaabase/internal/gatewayWhiteApi/list/v1?requestPath=" + requestPath
                    + "&page=" + apiPermissionRequestDto.getPage()
                    + "&limit=" + apiPermissionRequestDto.getLimit();
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
            uri = builder.build().encode().toUri();
            try {
                return restTemplate.getForObject(uri, ApiPermissionResponseDto.class);
            } catch (Exception e) {
                logger.error("get api white list failed : ", e);
                throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_UAA_API_FAILED);
            }
        }

    }

    public ApiPermissionResponseDto getApiPermissionByPath(String requestPath) {
        String mainIp = serverListService.getMainNodeInternalIP();
        String url = "http://" + mainIp + ":11111/api/rest/uaabase/internal/gatewayWhiteApi/list/v1?requestPath;=" + requestPath;
        try {
            return restTemplate.getForObject(url, ApiPermissionResponseDto.class);
        } catch (Exception e) {
            logger.error("get api white list by id failed : ", e);
            throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_UAA_API_FAILED);
        }

    }

    public ApiPermissionResponseDto getWhiteApiMode() {
        String mainIp = serverListService.getMainNodeInternalIP();
        String url = "http://" + mainIp + ":11111/api/rest/uaabase/internal/gatewayWhiteApi/whiteApiMode/query/v1";
        try {
            return restTemplate.getForObject(url, ApiPermissionResponseDto.class);
        } catch (Exception e) {
            logger.error("get white api model failed :", e);
            throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_UAA_API_FAILED);
        }

    }

    public void updateWhiteApiMode(ApiPermissionRequestDto apiPermissionRequestDto) {
        String mainIp = serverListService.getMainNodeInternalIP();
        String url = "http://" + mainIp + ":11111/api/rest/uaabase/internal/gatewayWhiteApi/whiteApiMode/modify/v1";
        try {
            restTemplate.postForObject(url, apiPermissionRequestDto, Void.class);
        } catch (Exception e) {
            logger.error("update white api model failed :", e);
            throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_UAA_API_FAILED);
        }

    }



    public void uaaUpdateApiConfig(HttpServletRequest request, HttpServletResponse response) {
        String mainIp = serverListService.getMainNodeInternalIP();
        String url = "http://" + mainIp + ":11111"+UAA_UPDATE_API_CONFIG;
        try {
            proxy(request,response,url);
        } catch (Exception e) {
            logger.error("update white api model failed :", e);
            throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_UAA_API_FAILED);
        }

    }



    public void uaaQueryApiConfig(HttpServletRequest request, HttpServletResponse response) {
        String mainIp = serverListService.getMainNodeInternalIP();
        String url = "http://" + mainIp + ":11111"+UAA_QUERY_API_CONFIG;
        try {
            proxy(request,response,url);
        } catch (Exception e) {
            logger.error("update white api model failed :", e);
            throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_UAA_API_FAILED);
        }

    }

    public void uaaQueryApiConfigById(String apiId, HttpServletRequest request, HttpServletResponse response) {
        String mainIp = serverListService.getMainNodeInternalIP();
        String url = "http://" + mainIp + ":11111"+String.format(UAA_QUERY_API_CONFIG_BY_ID, apiId);
        try {
            proxy(request,response,url);
        } catch (Exception e) {
            logger.error("update white api model failed :", e);
            throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_UAA_API_FAILED);
        }

    }

    public void proxy(HttpServletRequest request, HttpServletResponse response,String serviceUrl) throws IOException{
        // 获取请求的query参数
        String queryParams = request.getQueryString(); // 获取URL中的query参数

        String decodedQueryParams = null;

        if (queryParams != null && !queryParams.isEmpty()) {
            // URL 解码 query 参数
            decodedQueryParams = URLDecoder.decode(queryParams, StandardCharsets.UTF_8.name());
        }

        if (decodedQueryParams != null && !decodedQueryParams.isEmpty()) {
            serviceUrl += "?" + decodedQueryParams;  // 如果有 query 参数，拼接到 URL 后面
        }

        // 判断是否是 multipart 请求
        boolean isMultipart = "POST".equalsIgnoreCase(request.getMethod()) && request.getContentType() != null
                && request.getContentType().toLowerCase().contains("multipart/form-data");

        RequestCallback requestCallback = clientHttpRequest -> {
            // 复制 headers
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                String headerValue = request.getHeader(headerName);
                if (!"content-length".equalsIgnoreCase(headerName) && !"Authorization".equalsIgnoreCase(headerName)) { // 忽略 content-length
                    clientHttpRequest.getHeaders().add(headerName, headerValue);
                }
            }

            if (isMultipart && request instanceof MultipartHttpServletRequest) {
                // 使用 MultiValueMap 构造 multipart body
                MultiValueMap<String, Object> multipartBody = new LinkedMultiValueMap<>();

                // 添加表单字段
                request.getParameterMap().forEach((key, values) -> {
                    for (String value : values) {
                        multipartBody.add(key, value);
                    }
                });

                // 添加文件
                for (Iterator<String> it = ((MultipartHttpServletRequest) request).getFileNames(); it.hasNext(); ) {
                    String fileName = it.next();
                    List<MultipartFile> files = ((MultipartHttpServletRequest) request).getFiles(fileName);
                    for (MultipartFile file : files) {
                        multipartBody.add(fileName,
                                new ByteArrayResource(file.getBytes()) {
                                    @Override
                                    public String getFilename() {
                                        return file.getOriginalFilename();
                                    }
                                });
                    }
                }

                HttpHeaders headers = clientHttpRequest.getHeaders();
                headers.setContentType(MediaType.MULTIPART_FORM_DATA);

                // 用 FormHttpMessageConverter 序列化
                new FormHttpMessageConverter().write(multipartBody, MediaType.MULTIPART_FORM_DATA, clientHttpRequest);
            } else {
                // 非 multipart 请求，直接复制 body
                StreamUtils.copy(request.getInputStream(), clientHttpRequest.getBody());
            }
        };
        logger.info("uaa proxy url:{}", serviceUrl);
        restTemplate.execute(serviceUrl, org.springframework.http.HttpMethod.valueOf(request.getMethod()), requestCallback, clientHttpResponse -> {
            response.setStatus(clientHttpResponse.getStatusCode().value());
            if (null != clientHttpResponse.getHeaders().getContentType()) {
                response.setContentType(clientHttpResponse.getHeaders().getContentType().toString());
            }
            StreamUtils.copy(clientHttpResponse.getBody(), response.getOutputStream());
            return null;
        });
    }

}
