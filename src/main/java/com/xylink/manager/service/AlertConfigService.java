package com.xylink.manager.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xylink.config.Constants;
import com.xylink.config.RoleConfig;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.config.security.ConsoleUser;
import com.xylink.manager.controller.dto.alert.*;
import com.xylink.config.util.JsonUtil;
import com.xylink.manager.model.NodeExtDto;
import com.xylink.manager.model.common.Page;
import com.xylink.manager.model.common.PageRequest;
import com.xylink.manager.model.common.Pageable;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.cache.bean.ConfigMapCache;
import com.xylink.manager.service.cache.service.ICacheService;
import com.xylink.manager.service.influxdb.AlertInfoInfluxdbDao;
import com.xylink.manager.service.node.NodeService;
import com.xylink.util.SecurityContextUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by liujian on 2017/11/2.
 */
@Service
public class AlertConfigService {
    private final static Logger logger = LoggerFactory.getLogger(AlertConfigService.class);

    @Autowired
    private PrivateDataService privateDataService;
    @javax.annotation.Resource
    private NodeService nodeService;
    @Autowired
    private K8sService k8sService;
    @Autowired
    private ICacheService cacheService;
    @Autowired
    private AlertInfoInfluxdbDao alertInfoInfluxdbDao;
    @Autowired
    private ObjectMapper objectMapper;
    @Resource
    private RoleConfig roleConfig;

    public List<AlertConfigDto> getAlertConfigs(){
        List<String> nodeNames = cacheService.cacheNodeList()
                .stream()
                .map(nodeCache -> nodeCache.getMetadata().getName())
                .collect(Collectors.toList());
        return nodeNames.parallelStream().map(nodeName -> privateDataService.getAlertThredsholdByNode(nodeName)).collect(Collectors.toList());
    }

    public AlertConfigDto getAlertConfigByname(String nodeName){
        return privateDataService.getAlertThredsholdByNode(nodeName);
    }

    public void updateAlertConfig(AlertConfigDto alertConfigDto) {
        privateDataService.saveAlertThreadsholdByNode(alertConfigDto);
    }

    public String deleteMail(String email) {
        Map<String, String> alertMaps = privateDataService.getAlertMails();
        if(alertMaps.containsKey(email)) {
            alertMaps.remove(email);
            privateDataService.saveAlertMails(alertMaps);
        }
        return Constants.SUCCESS_RESPONSE;
    }


    public String addMail(AlertMailDto alertMailDto) {
        Map<String, String> alertMaps = privateDataService.getAlertMails();
        if (!alertMaps.containsKey(alertMailDto.getEmail())) {
            alertMaps.put(alertMailDto.getEmail(), alertMailDto.getName());
            privateDataService.saveAlertMails(alertMaps);
        } else {
            throw new ServerException(ErrorStatus.ENTITY_NOT_UNIQUE);
        }
        return Constants.SUCCESS_RESPONSE;
    }

    public Map<String,String> getMails() {
//        return privateDataService.getAlertMails();
        return privateDataService.getAlertMailsCache();
    }

    public AlertMailTypeDto getMailTypes() {
        return privateDataService.getAlertMailTypes();
    }

    public void updateMailTypes(AlertMailTypeDto alertMailTypeDto) {
        privateDataService.updateMailTypes(alertMailTypeDto);
    }

    /**
     * 获取告警信息列表
     */
    public Page<AlertEventDto> getAlertEventPage(boolean isRemoveRead, String measurement, Pageable pageable) {
        if (StringUtils.isBlank(measurement)) {
            return Page.emptyPage(pageable);
        }
        List<String> observableNodeList = getObservableNode(SecurityContextUtil.currentUser());
        List<AlertEventTotalDto> totalList = alertInfoInfluxdbDao.getTotalByInfluxdb(isRemoveRead, measurement, observableNodeList);
        if (CollectionUtils.isEmpty(totalList)) {
            return Page.emptyPage(pageable);
        }
        Long total = totalList.get(0).getTotal();
        if (total == null || total < 1) {
            return Page.emptyPage(pageable);
        }
        List<AlertEventDto> alertList = alertInfoInfluxdbDao.getAlertListByInfluxdb(isRemoveRead, measurement, pageable, observableNodeList);
        if (CollectionUtils.isEmpty(alertList)) {
            return Page.emptyPage(pageable);
        }
        alertList.forEach(alert -> {
            alert.setInfluxTime(alert.getTime().toString());
            alert.setCreateTime(LocalDateTime.ofInstant(alert.getTime(), ZoneId.systemDefault()).toString());
        });
        return new Page<>(pageable.getPageNumber(), pageable.getPageSize(), alertList.size(), alertList);
    }

    public List<String> getObservableNode(String user) {
        if (roleConfig.getList().containsKey(user)) {
            return null;
        }
        ConfigMapCache privateConsoleUser = cacheService.cacheConfigMapByName(Constants.CONFIGMAP_PRIVATE_CONSOLE_USER);
        if (privateConsoleUser == null) {
            return null;
        }
        Map<String, String> consoleUserMap = privateConsoleUser.getData();
        if (consoleUserMap == null) {
            return null;
        }
        try {
            ConsoleUser consoleUser = objectMapper.readValue(consoleUserMap.get(user), ConsoleUser.class);
            List<String> permissionList = Arrays.asList(consoleUser.getPermissions().split(","));
            if (permissionList.contains("*")) {
                return null;
            }

            Map<String, NodeExtDto> nodeNameToNodeExtDtoMap = nodeService.getNodeNameToNodeExtDtoMap();
            return nodeNameToNodeExtDtoMap.entrySet()
                    .stream()
                    .filter(entry -> permissionList.contains(entry.getValue().getShortName()))
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Console user info not illegal. ",e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取告警信息列表
     */
    public List<AlertEventDto> exportEvents(boolean isRemoveRead, String measurement, int pageIndex) {
        List<String> observableNodeList = getObservableNode(SecurityContextUtil.currentUser());
        List<AlertEventDto> alertList = alertInfoInfluxdbDao.getAlertListByInfluxdb(isRemoveRead, measurement, 1000, (pageIndex - 1) * 10L, observableNodeList);
        if (CollectionUtils.isEmpty(alertList)) {
            return new ArrayList<>();
        }
        alertList.forEach(alert -> {
            alert.setInfluxTime(alert.getTime().toString());
            alert.setCreateTime(LocalDateTime.ofInstant(alert.getTime(), ZoneId.systemDefault()).toString());
        });
        return alertList;
    }

    /**
     * 查询未读消息条数
     */
    public Long getNotReadCount(String measurement) {
        List<String> observableNodeList = getObservableNode(SecurityContextUtil.currentUser());
        List<AlertEventTotalDto> totalList = alertInfoInfluxdbDao.getTotalByInfluxdb(true, measurement, observableNodeList);
        if (CollectionUtils.isEmpty(totalList)) {
            return 0L;
        }
        Long total = totalList.get(0).getTotal();
        if (total == null || total < 1) {
            return 0L;
        }
        return total;
    }

    /**
     * 设置告警信息为已读
     */
    public void setRead(ReadAlertInfoVo readAlertInfoVo) {
        if (StringUtils.isEmpty(readAlertInfoVo.getTime())) {
            //该类型全部设置为已读
            setAllRead(readAlertInfoVo);
            return;
        }
        setPartRead(readAlertInfoVo);
    }

    /**
     * 该类型告警全部设置为已读
     */
    private void setAllRead(ReadAlertInfoVo readAlertInfoVo) {
        int page = 0, sum;
        PageRequest pageRequest;
        List<AlertEventDto> alertList;
        long startTime = System.currentTimeMillis();
        String alertType = readAlertInfoVo.getAlertType();
        List<AlertEventDto> tempList = new ArrayList<>(50);
        List<String> observableNodeList = getObservableNode(SecurityContextUtil.currentUser());
        do {
            sum = 0;
            if (System.currentTimeMillis() - startTime > 10 * 60 * 60 * 1000) {
                logger.error("setAllRead time over 10 min");
                //默认超过10分钟就终止
                break;
            }
            page++;
            pageRequest = new PageRequest(page, 1000);
            alertList = alertInfoInfluxdbDao.getAlertListByInfluxdb(true, alertType, pageRequest, observableNodeList);
            for (AlertEventDto alert : alertList) {
                alert.setReaded(true);
                tempList.add(alert);
                if (++sum % 50 == 0) {
                    alertInfoInfluxdbDao.insertAlertInfo(alertType, tempList);
                    tempList = new ArrayList<>(50);
                }
            }
            if (!CollectionUtils.isEmpty(tempList)) {
                alertInfoInfluxdbDao.insertAlertInfo(alertType, tempList);
                tempList = new ArrayList<>(50);
            }
        } while (alertList.size() > 0);
    }

    /**
     * 该类型告警部分设置为已读
     */
    private void setPartRead(ReadAlertInfoVo readAlertInfoVo) {
        String alertType = readAlertInfoVo.getAlertType();
        List<AlertEventDto> alertList = alertInfoInfluxdbDao.getAlertListByInfluxdb(alertType, readAlertInfoVo.getTime());
        int sum = 0;
        List<AlertEventDto> tempList = new ArrayList<>(50);
        for (AlertEventDto alert : alertList) {
            alert.setReaded(true);
            tempList.add(alert);
            if (++sum % 50 == 0) {
                alertInfoInfluxdbDao.insertAlertInfo(alertType, tempList);
                tempList = new ArrayList<>(50);
            }
        }
        if (!CollectionUtils.isEmpty(tempList)) {
            alertInfoInfluxdbDao.insertAlertInfo(alertType, tempList);
        }
    }

    public void updateAlertRestrainConfig(List<String> restrainConfig) {
        Map<String, String> map = new HashMap<>();
        map.put("restrain-alert", JsonUtil.toJson(restrainConfig));
        k8sService.replaceConfigmap(Constants.CONFIGMAP_PRIVATE_RESTRAIN_ALERT, map);
    }

    public List<String> getAlertRestrainConfig() {
        String value = k8sService.getConfigmap(Constants.CONFIGMAP_PRIVATE_RESTRAIN_ALERT).get("restrain-alert");
        if(StringUtils.isBlank(value)) {
            return Collections.emptyList();
        }
        return JsonUtil.parseToList(value, String.class);
    }
}
