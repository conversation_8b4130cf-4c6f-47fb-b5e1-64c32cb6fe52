package com.xylink.manager.service.nightingale;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.xylink.config.util.JsonUtil;
import com.xylink.config.util.RsaUtils;
import com.xylink.manager.model.nightingale.NightingaleResponseDto;
import com.xylink.manager.model.nightingale.SmtpInfoDto;
import com.xylink.manager.model.nightingale.SmtpInfoTestDto;
import com.xylink.manager.service.db.JasyptService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service
public class SmtpService {

    @Autowired
    private N9eWebFluxResolver n9eWebFluxResolver;
    @Autowired
    private JasyptService jasyptService;

    public Map<String, Object> getSmtpConfig() {
        ResponseEntity<NightingaleResponseDto> response = n9eWebFluxResolver.getForEntity("n9e.stmp.list");
        if (Objects.isNull(response) || Objects.isNull(response.getBody())) {
            return new HashMap<>(0);
        }
        NightingaleResponseDto body = response.getBody();
        JsonNode jsonNode = JsonUtil.parseJson(JsonUtil.toJson(body.getDat()));
        String smtpPass = jsonNode.get("smtpPass") == null ? "" : jsonNode.get("smtpPass").asText();
        if (StringUtils.isNotBlank(smtpPass)) {
            String smtpPassPlaintext=jasyptService.decrypt(smtpPass);
            ((ObjectNode) jsonNode).put("smtpPass", RsaUtils.encryptWithPrivateKey(smtpPassPlaintext));
        }
        // 将 JSONObject 转换为 Map<String, Object>
        Map<String, Object> resultMap = new HashMap<>();
        jsonNode.fields().forEachRemaining(entry -> resultMap.put(entry.getKey(), entry.getValue()));

        return resultMap;
    }

    public ResponseEntity<NightingaleResponseDto> addSmtpConfig(SmtpInfoDto smtpInfo) {
        return n9eWebFluxResolver.putForEntity("n9e.stmp.add", smtpInfo);
    }

    public ResponseEntity<NightingaleResponseDto> testSmtpConfig(SmtpInfoDto smtpInfo) {
        SmtpInfoTestDto smtpInfoTestDto = toSmtpInfoTestDto(smtpInfo);
        return n9eWebFluxResolver.postForEntity("n9e.stmp.test", smtpInfoTestDto);
    }

    private SmtpInfoTestDto toSmtpInfoTestDto(SmtpInfoDto smtpInfo) {
        SmtpInfoTestDto smtpInfoTestDto = new SmtpInfoTestDto();
        smtpInfoTestDto.setSmtpHost(smtpInfo.getSmtpHost());
        smtpInfoTestDto.setSmtpPort(Integer.parseInt(smtpInfo.getSmtpPort()));
        smtpInfoTestDto.setSmtpInsecureSkipVerify(smtpInfo.getSmtpInsecureSkipVerify());
        smtpInfoTestDto.setTargets(smtpInfo.getTargets());
        smtpInfoTestDto.setSmtpUser(smtpInfo.getSmtpUser());
        smtpInfoTestDto.setSmtpPass(smtpInfo.getSmtpPass());
        smtpInfoTestDto.setDefaultEmailSeverities(smtpInfo.getDefaultEmailSeverities());
        return smtpInfoTestDto;
    }

}
