package com.xylink.manager.service.nightingale.monitor;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

/**
 * Created by niulong on 2023/1/28 10:08 上午
 */
@Component
@Getter
@PropertySource(value = {"classpath:n9e-monitor.properties"} , encoding = "utf-8")
public class MonitorConfig {
    @Value("${n9e.v5.host.default.monitor.screen}")
    private String hostDefaulltMonitorScreen;
}
