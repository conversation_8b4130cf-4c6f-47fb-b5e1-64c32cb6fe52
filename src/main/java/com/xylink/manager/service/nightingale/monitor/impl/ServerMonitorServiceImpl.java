package com.xylink.manager.service.nightingale.monitor.impl;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.xylink.manager.inspection.common.OpsManagerException;
import com.xylink.manager.inspection.common.monitor.MonitorData;
import com.xylink.manager.inspection.common.monitor.MonitorIndicatorDto;
import com.xylink.manager.inspection.common.monitor.MonitorMetricsDto;
import com.xylink.manager.inspection.entity.vo.MonitorIndicatorVo;
import com.xylink.manager.inspection.utils.StringFormatUtils;
import com.xylink.manager.model.nightingale.NightingaleResponseDto;
import com.xylink.manager.service.nightingale.N9eWebFluxResolver;
import com.xylink.manager.service.nightingale.monitor.ServerMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ServerWebExchange;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/10 10:46
 */
@Slf4j
@Service
public class ServerMonitorServiceImpl implements ServerMonitorService {

    @Autowired
    private N9eWebFluxResolver n9eWebFluxResolver;

    @Override
    public ResponseEntity<NightingaleResponseDto> getServerTreeList(String query, ServerWebExchange exchange) {
        return n9eWebFluxResolver.getForEntity("n9e.server.tree", query);
    }

    @Override
    public ResponseEntity<NightingaleResponseDto> getNodeExpandInfo(Long nid, ServerWebExchange exchange) {
        return n9eWebFluxResolver.getForEntity("n9e.org-res-tree.fields", nid);
    }

    @Override
    public NightingaleResponseDto getServerExample(String serverName, Long nid, ServerWebExchange exchange) {
        Object[] params = {
                StringFormatUtils.object2String(serverName),
                StringFormatUtils.object2String(nid)
        };
        NightingaleResponseDto responseBody = n9eWebFluxResolver.getForEntity("n9e.monitor.server.example", params).getBody();
        formatRate(responseBody);
        Object obj = responseBody.getDat();
        ArrayNode array = (ArrayNode) obj;
        if (array.size() == 0) {
            return null;
        }
        //dev和qa是可以获取到值
        JsonNode o = array.get(0);
        if (!(o.get("cpu_rate").asText().equals("0.0"))) {
            return responseBody;
        }
        if (!(o.get("memory_rate").asText().equals("0.0"))) {
            return responseBody;
        }
        //prd无法直接获取到cpu、内存的使用率
        try {
            for (Object jsonObj : (ArrayNode) obj) {
                if (jsonObj instanceof ObjectNode) {
                    ObjectNode jbt = (ObjectNode) jsonObj;
                    MonitorIndicatorDto cpuMonitorIndicatorDto = new MonitorIndicatorDto();
                    cpuMonitorIndicatorDto.setNids(Arrays.asList(jbt.get("nod_id").toString()));
                    cpuMonitorIndicatorDto.setMetric("cpu_util");
                    cpuMonitorIndicatorDto.setEnd(System.currentTimeMillis() / 1000);
                    cpuMonitorIndicatorDto.setStart(getTime());
                    giveExCpuAndMer(jbt, cpuMonitorIndicatorDto, "cpu_rate", exchange);
                    cpuMonitorIndicatorDto.setMetric("mem_bytes_used_percent");
                    giveExCpuAndMer(jbt, cpuMonitorIndicatorDto, "memory_rate", exchange);
                }
            }
        } catch (Exception e) {
            log.error("[getServerExample error],servername={},e={}", serverName, e);
            throw new OpsManagerException(1000, e.getMessage());
        }
        log.info("[getServerExample success], serverName = {}", serverName);
        return responseBody;
    }

    @Override
    public NightingaleResponseDto getServerMarket(MonitorIndicatorDto monitorIndicatorDto, ServerWebExchange exchange) {
        MonitorMetricsDto metricsDto = new MonitorMetricsDto();
        BeanUtils.copyProperties(monitorIndicatorDto, metricsDto);
        if (monitorIndicatorDto.getMetric() == null && monitorIndicatorDto.getMetrics() != null) {
            metricsDto.setMetric(monitorIndicatorDto.getMetrics().get(0));
        }
        ResponseEntity<NightingaleResponseDto> fullMatch = getNodeOrMacInfo(Collections.singletonList(metricsDto), exchange);
        MonitorIndicatorVo.MonitorIndicatorVoBuilder builder = MonitorIndicatorVo.builder();
        // 如果请求失败
        if (fullMatch.getStatusCode() != HttpStatus.OK) {
            return NightingaleResponseDto.builder().dat(builder.build()).build();
        }
        JSONObject fullMatchJson = new JSONObject(fullMatch.getBody().getDat());
        if (fullMatchJson.get("list") == null || fullMatchJson.get("list").equals("null") || fullMatchJson.get("list").equals("")) {
            return NightingaleResponseDto.builder().dat(builder.build()).build();
        }
        JSONArray fullMatchList = fullMatchJson.getJSONArray("list");
        if (fullMatchList.length() == 0) {
            return NightingaleResponseDto.builder().dat(builder.build()).build();
        }
        JSONObject listObj = fullMatchList.getJSONObject(0);
        JSONArray nids = null;
        JSONArray tags = null;
        if (ObjectUtils.isNotEmpty(listObj.get("nids")) && !JSONObject.NULL.equals(listObj.get("nids"))) {
            nids = listObj.getJSONArray("nids");
        }
        if (ObjectUtils.isNotEmpty(listObj.get("tags")) && !JSONObject.NULL.equals(listObj.get("tags"))) {
            tags = listObj.getJSONArray("tags");
        }
        if (metricsDto.getMetric() == null) {
            metricsDto.setMetric(listObj.getString("metric"));
        }
        List<String> nidList = new ArrayList<>();
        if (nids != null && nids.length() > 0) {
            nidList = nids.toList().stream().map(Object::toString).collect(Collectors.toList());
        }
        List<String> tagList = new ArrayList<>();
        if (tags != null && tags.length() > 0) {
            tagList = tags.toList().stream().map(Object::toString).collect(Collectors.toList());
        }
        MonitorData monitorData = MonitorData.builder()
                .comparisons(monitorIndicatorDto.getComparisons())
                .count(listObj.getLong("count"))
                .dstype("GAUGE")
                .end(monitorIndicatorDto.getEnd())
                .endpoints(monitorIndicatorDto.getEndpoints())
                .metric(metricsDto.getMetric())
                .aggrFunc(monitorIndicatorDto.getAggrFunc())
                .nids(nidList)
                .start(monitorIndicatorDto.getStart())
                .step(listObj.getLong("step"))
                .groupKey(monitorIndicatorDto.getGroupKey())
                .consolFuc("AVERAGE")
                .tags(tagList)
                .mode(monitorIndicatorDto.getMode())
                .build();
        ResponseEntity<NightingaleResponseDto> ui = getMonitorData(monitorData, exchange);
        MonitorIndicatorVo indicatorVo = builder
                .fullMatch(fullMatch.getBody().getDat())
                .ui(ui.getBody().getDat()).build();
        // 获取ui
        return NightingaleResponseDto.builder().dat(indicatorVo).build();
    }


    @Override
    public ResponseEntity<NightingaleResponseDto> getUnrelatedTagKv(MonitorMetricsDto metricsDto, ServerWebExchange exchange) {
        return n9eWebFluxResolver.postForEntity("n9e.monitor.tagkv.unre", metricsDto, exchange);
    }

    private void giveExCpuAndMer(ObjectNode jbt, MonitorIndicatorDto monitorIndicatorDto, String type, ServerWebExchange exchange) {
        String pod_name = jbt.get("pod_name").asText();
        NightingaleResponseDto ops = getServerMarket(monitorIndicatorDto, exchange);//获取ui
        MonitorIndicatorVo data = null;
        ArrayList<LinkedHashMap> uiList;//获取ui里的值
        if (ops.getDat() != null && ops.getDat() instanceof MonitorIndicatorVo && ((MonitorIndicatorVo) ops.getDat()).getUi() != null) {
            data = (MonitorIndicatorVo) ops.getDat();
        }
        uiList = (ArrayList) data.getUi();
        for (int i = 0; i < uiList.size(); i++) {
            //取pod_name,赋值value
            if (containerGet(uiList.get(i).get("counter").toString(), pod_name)) {
                ArrayList arrayList = (ArrayList) uiList.get(i).get("values");
                //取出最大时间戳对应的value
                LinkedHashMap<String, Double> resultMap = (LinkedHashMap) arrayList.get(arrayList.size() - 1);
                jbt.put(type, String.format("%.4f", resultMap.get("value")));
            }
        }
    }

    /*
     * 获取5分钟之前的时间戳
     */
    public static Long getTime() {
        Date now = new Date();
        Date now_five = new Date(now.getTime() - 300000);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            date = simpleDateFormat.parse(simpleDateFormat.format(now_five));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date.getTime() / 1000;
    }

    /**
     * 获取podName和container
     *
     * @param containerAll
     * @return
     */
    public Boolean containerGet(String containerAll, String oldPodName) {
        String[] res = containerAll.split("/");
        if (res.equals("")) {
            return false;
        }
        String[] str1 = res[1].split(",");
        HashMap<String, String> counterList = new HashMap<>();
        for (String str : str1) {
            String[] counter = str.split("=");
            if (counter.length != 2) {
                return false;
            }
            counterList.put(counter[0], counter[1]);
        }
        String podName = counterList.get("pod");
        String container = counterList.get("container");
        if (podName.contains(container) && oldPodName.equals(podName)) {
            return true;
        }
        return false;
    }

    /**
     * 格式化使用率(cpu,memory)
     *
     * @param responseBody
     */
    private void formatRate(NightingaleResponseDto responseBody) {
        JSONObject data = new JSONObject(responseBody);
        JSONArray array = data.getJSONArray("dat");
        for (int i = 0; i < array.length(); i++) {
            JSONObject jsonObject = array.getJSONObject(i);
            data.put("dat." + i + ".memory_rate", jsonObject.getDouble("memory_rate") * 100.0);
            data.put("dat." + i + ".cpu_rate", jsonObject.getDouble("cpu_rate") * 100.0);
        }
        responseBody.setDat(data.get("dat"));
    }

    public ResponseEntity<NightingaleResponseDto> getNodeOrMacInfo(List<MonitorMetricsDto> metricsDtos, ServerWebExchange exchange) {
        return n9eWebFluxResolver.postForEntity("n9e.monitor.node-or-mac-info", metricsDtos, exchange);
    }

    public ResponseEntity<NightingaleResponseDto> getMonitorData(MonitorData monitorData, ServerWebExchange exchange) {
        return n9eWebFluxResolver.postForEntity("n9e.monitor.data", monitorData, exchange);
    }


}
