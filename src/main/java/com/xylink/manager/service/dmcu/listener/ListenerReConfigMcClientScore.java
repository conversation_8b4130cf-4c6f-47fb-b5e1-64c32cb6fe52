package com.xylink.manager.service.dmcu.listener;

import com.xylink.config.Constants;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.dmcu.DmcuPersistenceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * change mc config : client_score.properties
 *
 * <AUTHOR>
 * @since 2021/7/14 6:25 下午
 */
@Slf4j
@Component
public class ListenerReConfigMcClientScore extends AbstractListenerReConfigMcScoreProperties implements DmcuConnChangedListener {


    private static final String SUPPORT_PREFIX = Constants.TERMINAL_INSERT_PREFIX;
    private static final String KEY = "client_score.properties";

    public ListenerReConfigMcClientScore(DmcuPersistenceService dmcuPersistenceService, K8sService k8sService) {
        super(dmcuPersistenceService, k8sService);
    }

    @Override
    public void onDmcuConnChanged(String siteCode) {
        common(Constants.CONFIGMAP_MC_CLIENT_SCORE, KEY, SUPPORT_PREFIX);
    }

    @Override
    public boolean supports(String siteCode) {
        return support(siteCode, SUPPORT_PREFIX);
    }
}
