package com.xylink.manager.service.dmcu.listener;

import com.xylink.config.Constants;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.dmcu.DmcuPersistenceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * * change mc config : server_score.properties
 *
 * <AUTHOR>
 * @since 2021/7/14 6:25 下午
 */
@Slf4j
@Component
public class ListenerReConfigMcServerScore extends AbstractListenerReConfigMcScoreProperties implements DmcuConnChangedListener {

    private static final String KEY = "server_score.properties";
    private static final String SUPPORT_PREFIX = Constants.DMCU_CONNECTION_PREFIX;

    public ListenerReConfigMcServerScore(DmcuPersistenceService dmcuPersistenceService, K8sService k8sService) {
        super(dmcuPersistenceService, k8sService);
    }


    @Override
    public void onDmcuConnChanged(String siteCode) {
        common(Constants.CONFIGMAP_MC_SERVER_SCORE, KEY, SUPPORT_PREFIX);
    }

    @Override
    public boolean supports(String siteCode) {
        return support(siteCode, SUPPORT_PREFIX);
    }
}
