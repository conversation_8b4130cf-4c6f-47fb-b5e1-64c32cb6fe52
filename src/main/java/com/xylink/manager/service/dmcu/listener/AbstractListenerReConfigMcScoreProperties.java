package com.xylink.manager.service.dmcu.listener;

import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.model.DmcuConnectionDto;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.dmcu.DmcuPersistenceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/7/30 3:13 下午
 */
public abstract class AbstractListenerReConfigMcScoreProperties implements DmcuConnChangedListener {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    protected DmcuPersistenceService dmcuPersistenceService;

    protected K8sService k8sService;

    public AbstractListenerReConfigMcScoreProperties(DmcuPersistenceService dmcuPersistenceService, K8sService k8sService) {
        this.dmcuPersistenceService = dmcuPersistenceService;
        this.k8sService = k8sService;
    }

    protected boolean support(String siteCode, String prefix) {
        return siteCode.startsWith(prefix);
    }

    protected void common(String configMapName, String key, String dataPrefix) {
        List<DmcuConnectionDto> properties = dmcuPersistenceService.getDmcuOrTerminalList(dataPrefix);
        Map<String, String> data = k8sService.getConfigmap(configMapName);
        logger.info("ConfigMap:{} old data is:{}", configMapName, data);
        int version = 0;
        if (data != null && !data.isEmpty()) {
            // get version
            String value = data.get(key);
            if (value != null) {
                try (BufferedReader bufferedReader = new BufferedReader(new StringReader(value))) {
                    String propertiesVersion = bufferedReader.lines().filter(line -> line.contains("version")).findFirst().orElse("0");
                    version = Integer.parseInt(propertiesVersion.replace("version=", "")) + 1;
                } catch (IOException e) {
                    throw new ServerException(e, ErrorStatus.UNEXPECTED_ERROR);
                }
            }
        }
        StringBuilder sb = new StringBuilder();
        sb.append("version=").append(version).append("\n");
        for (DmcuConnectionDto property : properties) {
            property.propertiesStyle().ifPresent(item ->
                    sb.append(item).append("\n")
            );
        }
        Map<String, String> result = new HashMap<>();
        result.put(key, sb.toString());
        logger.info("ConfigMap:{} new data is:{}", configMapName, result);
        k8sService.replaceConfigmap(configMapName, result);
    }

}
