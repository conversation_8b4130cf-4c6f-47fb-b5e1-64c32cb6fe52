package com.xylink.manager.service.dmcu;

import com.xylink.manager.model.dmcu.SiteCodeInfo;
import com.xylink.manager.model.dmcu.SiteCodeStructure;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @since 2021/7/30 5:22 下午
 */
public final class SiteCodeUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(SiteCodeUtils.class);
    private static final String SPLIT_CHAR = "-";
    private static final String JOIN_CHAR = ".";

    private SiteCodeUtils() {

    }

    public static SiteCodeInfo buildSiteCode(String siteCode) {
        if (StringUtils.isBlank(siteCode)) {
            return null;
        }
        String[] partArray = siteCode.split(SPLIT_CHAR);
        int length = partArray.length;
        try {
            if (length == 2) {
                return SiteCodeInfo
                        .builder()
                        .enterprise(SiteCodeStructure.ENTERPRISE.getDefaultValue())
                        .site(partArray[0])
                        .subsite(SiteCodeStructure.SUBSITE.getDefaultValue())
                        .provider(partArray[1]).build();
            } else if (length >= 4) {
                return SiteCodeInfo
                        .builder()
                        .enterprise(partArray[0])
                        .site(partArray[1])
                        .subsite(partArray[2])
                        .provider(partArray[3]).build();
            }
        } catch (Exception e) {
            LOGGER.error("Split site code error.", e);
        }
        return SiteCodeInfo.builder().enterprise(siteCode).build();
    }

    public static String getEnterpriseAndSite(SiteCodeInfo siteCodeInfo) {
        if (siteCodeInfo == null) {
            return null;
        }
        return siteCodeInfo.getEnterprise() + JOIN_CHAR + siteCodeInfo.getSite();
    }
}
