package com.xylink.manager.service.dmcu;

import com.xylink.config.Constants;
import com.xylink.config.DmcuConstants;
import com.xylink.config.exception.basic.ClientErrorException;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.manager.controller.dto.basic.PodHostInfoDTO;
import com.xylink.manager.controller.dto.dmcu.DmcuExternalIpBindRuleDto;
import com.xylink.manager.model.common.Page;
import com.xylink.manager.model.common.Pageable;
import com.xylink.manager.service.ServerStatusService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.util.MemoryPaginationUtil;
import com.xylink.util.SearchDtoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023/11/21 10:26
 */
@Service
@Slf4j
public class DmcuExternalIpBindRuleService {

    @Autowired
    private K8sService k8sService;

    @Autowired
    private ServerStatusService serverStatusService;

    /**
     * 每个dmcu的外网绑定规则：hostName_
     *
     * @return
     */
    public Page<DmcuExternalIpBindRuleDto> list(String key, Pageable pageable) {
        List<PodHostInfoDTO> dmcuInfoList = getDmcuInfoList();
        Map<String, String> allDmcu = k8sService.getConfigmap(Constants.CONFIGMAP_DMCU);
        List<DmcuExternalIpBindRuleDto> res = new ArrayList<>();
        for (PodHostInfoDTO temp : dmcuInfoList) {
            String dmcuExtIpBindRule = allDmcu.get(temp.getHostname() + DmcuConstants.DMCU_EXT_IP_BIND_RULE_LIST);
            if (StringUtils.isBlank(dmcuExtIpBindRule)) {
                continue;
            }
            String[] ruleArr = dmcuExtIpBindRule.split("@");
            Arrays.stream(ruleArr).forEach(x -> {
                String[] split = x.split(",");
                DmcuExternalIpBindRuleDto ruleDto = new DmcuExternalIpBindRuleDto(temp.getPod(), temp.getHostname(), temp.getServerIp());
                ruleDto.setAllocationRule(split[0]);
                ruleDto.setExternalIp(split[1]);
                res.add(ruleDto);
            });
        }
        List<DmcuExternalIpBindRuleDto> records = SearchDtoUtil.searchKeyWord(res, key);

        if (CollectionUtils.isEmpty(records)) {
            return Page.emptyPage(pageable);
        }
        return MemoryPaginationUtil.pagination(records, pageable);
    }

    public List<PodHostInfoDTO> getDmcuInfoList() {
        return k8sService.getPodHostInfoList(Constants.POD_NAME_DMCU);
    }

    public void add(DmcuExternalIpBindRuleDto bindRuleDto) {
        Map<String, String> allDmcu = k8sService.getConfigmap(Constants.CONFIGMAP_DMCU);
        String currentRule = allDmcu.get(bindRuleDto.getHostname() + DmcuConstants.DMCU_EXT_IP_BIND_RULE_LIST);


        Map<String, String> ruleToExtIp = getRuleToExtIp(currentRule);

        if (StringUtils.isNotBlank(currentRule) && ruleToExtIp.containsKey(bindRuleDto.getAllocationRule())) {
            throw new ClientErrorException(ErrorStatus.DATA_EXISTED);
        }

        ruleToExtIp.put(bindRuleDto.getAllocationRule(), bindRuleDto.getExternalIp());
        String dmcuExtendInfo = getDmcuExtendInfo(ruleToExtIp);

        String resRule = StringUtils.isBlank(currentRule) ? bindRuleDto.getAllocationRule() + "," + bindRuleDto.getExternalIp() : currentRule + "@" + bindRuleDto.getAllocationRule() + "," + bindRuleDto.getExternalIp();

        HashMap<String, String> allDmcuForPatch = new HashMap<>();

        allDmcuForPatch.put(bindRuleDto.getHostname() + DmcuConstants.DMCU_EXT_IP_BIND_RULE_LIST, resRule);
        allDmcuForPatch.put(bindRuleDto.getHostname() + DmcuConstants.DMCU_EXTEND_INFO, dmcuExtendInfo);

        k8sService.patchConfigMap(Constants.CONFIGMAP_DMCU,allDmcuForPatch);
    }

    private Map<String, String> getRuleToExtIp(String currentRule) {
        if (StringUtils.isBlank(currentRule)) {
            return new HashMap<>();
        }
        return Arrays.stream(currentRule.split("@")).map(x -> x.split(",")).distinct().collect(Collectors.toMap(a -> a[0], a -> a[1]));
    }

    public void update(DmcuExternalIpBindRuleDto bindRuleDto) {
        Map<String, String> allDmcu = k8sService.getConfigmap(Constants.CONFIGMAP_DMCU);
        String currentRule = allDmcu.get(bindRuleDto.getHostname() + DmcuConstants.DMCU_EXT_IP_BIND_RULE_LIST);


        Map<String, String> ruleToExtIp = getRuleToExtIp(currentRule);
        if (MapUtils.isEmpty(ruleToExtIp)) {
            throw new ClientErrorException(ErrorStatus.DATA_NOT_EXIST);
        }

        for (Map.Entry<String, String> temp : ruleToExtIp.entrySet()) {
            String rule = temp.getKey();
            String extIp = temp.getValue();
            if (rule.equals(bindRuleDto.getAllocationRule()) && extIp.equals(bindRuleDto.getExternalIp())) {
                return;
            }
        }

        ruleToExtIp.put(bindRuleDto.getAllocationRule(), bindRuleDto.getExternalIp());
        updateRuleToCM(bindRuleDto.getHostname(), ruleToExtIp);
    }


    public void delete(DmcuExternalIpBindRuleDto bindRuleDto) {
        Map<String, String> allDmcu = k8sService.getConfigmap(Constants.CONFIGMAP_DMCU);
        String currentRuleKey = bindRuleDto.getHostname() + DmcuConstants.DMCU_EXT_IP_BIND_RULE_LIST;
        String currentRule = allDmcu.get(currentRuleKey);

        Map<String, String> ruleToExtIp = getRuleToExtIp(currentRule);
        if (MapUtils.isEmpty(ruleToExtIp) || !ruleToExtIp.containsKey(bindRuleDto.getAllocationRule())) {
            return;
        }
        ruleToExtIp.remove(bindRuleDto.getAllocationRule());

        if (MapUtils.isEmpty(ruleToExtIp)) {
            k8sService.removeDataFromCM(Constants.CONFIGMAP_DMCU, currentRuleKey, bindRuleDto.getHostname() + DmcuConstants.DMCU_EXTEND_INFO);
            serverStatusService.restartPod(bindRuleDto.getPod(), "name");
        } else {
            updateRuleToCM(bindRuleDto.getHostname(), ruleToExtIp);
        }
    }

    /**
     * 更新绑定规则到all-dmcu
     *
     * @param ruleToExtIp
     */
    private void updateRuleToCM(String hostname, Map<String, String> ruleToExtIp) {
        String dmcuExtendInfo = getDmcuExtendInfo(ruleToExtIp);
        String resRule = getResBindRule(ruleToExtIp);

        HashMap<String, String> allDmcuForPatch = new HashMap<>();
        allDmcuForPatch.put(hostname + DmcuConstants.DMCU_EXT_IP_BIND_RULE_LIST, resRule);
        allDmcuForPatch.put(hostname + DmcuConstants.DMCU_EXTEND_INFO, dmcuExtendInfo);
        k8sService.patchConfigMap(Constants.CONFIGMAP_DMCU, allDmcuForPatch);
    }

    /**
     * 遍历ruleToExtIp的map获取当前dmcu的绑定规则结果
     * @param ruleToExtIp
     * @return
     */
    private String getResBindRule(Map<String, String> ruleToExtIp) {
        if (MapUtils.isEmpty(ruleToExtIp)) {
            return null;
        }
        StringBuilder builder = new StringBuilder();
        for (Map.Entry<String, String> entry : ruleToExtIp.entrySet()) {
            builder.append(entry.getKey()).append(",").append(entry.getValue()).append("@");
        }

        return builder.deleteCharAt(builder.length() - 1).toString();
    }

    /**
     * dmcu的relayserver.conf配置文件
     * @param ruleToExtIp
     * @return
     */
    private String getDmcuExtendInfo(Map<String, String> ruleToExtIp) {
        if (MapUtils.isEmpty(ruleToExtIp)) {
            return null;
        }

        StringBuilder builder = new StringBuilder();

        //8个缩进空格
        String indentation = "        ";
        //4个缩进空格
        String innerIndentation = "    ";

        builder.append(indentation).append("\"extendInfo\" : [\n");
        for (Map.Entry<String, String> entry : ruleToExtIp.entrySet()) {
            builder.append(indentation).append(innerIndentation).append("{\"dedicateName\" : \"").append(entry.getKey()).append("\", \"extIp\" : \"").append(entry.getValue()).append("\"},\n");
        }
        // 删除最后一个逗号
        builder.deleteCharAt(builder.length() - 2);
        builder.append(indentation).append("],");
        return builder.toString();
    }

}
