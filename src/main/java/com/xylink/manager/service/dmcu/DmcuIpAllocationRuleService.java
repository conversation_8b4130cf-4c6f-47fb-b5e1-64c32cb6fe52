package com.xylink.manager.service.dmcu;

import com.xylink.config.exception.basic.ClientErrorException;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.manager.controller.dto.dmcu.DmcuIpAllocationRuleDto;
import com.xylink.util.JDBCUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/11/17 17:13
 */
@Service
@Slf4j
public class DmcuIpAllocationRuleService {

    @Autowired
    private JDBCUtils jdbcUtils;

    public List<DmcuIpAllocationRuleDto> getRuleList() {
        return jdbcUtils.getDmcuAllocationRuleList();
    }

    public void addRule(DmcuIpAllocationRuleDto ruleDto) {
        for (DmcuIpAllocationRuleDto temp : getRuleList()) {
            if (ruleDto.getSourceIp().equals(temp.getSourceIp())) {
                throw new ClientErrorException(ErrorStatus.DATA_EXISTED);
            }
        }

        jdbcUtils.addDmcuAllocationRule(ruleDto);
    }

    /**
     * 更新时，前端不允许sourceIp变更
     * @param ruleDto
     */
    public void updateRule(DmcuIpAllocationRuleDto ruleDto) {
        for (DmcuIpAllocationRuleDto temp : getRuleList()) {
            if (ruleDto.getAllocationRule().equals(temp.getAllocationRule()) && ruleDto.getSourceIp().equals(temp.getSourceIp())) {
                return;
            }
        }
        jdbcUtils.updateDmcuAllocationRuleByIp(ruleDto);
    }

    public void deleteRule(String sourceIp) {
        jdbcUtils.deleteDmcuAllocationRuleByIp(sourceIp);
    }
}
