package com.xylink.manager.service.dmcu;

import com.xylink.config.Constants;
import com.xylink.config.DmcuConstants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServiceErrorException;
import com.xylink.manager.model.DmcuConnectionDto;
import com.xylink.manager.model.DmcuInfoDto;
import com.xylink.manager.model.SiteCodeDto;
import com.xylink.manager.model.common.Page;
import com.xylink.manager.model.common.Pageable;
import com.xylink.manager.model.deploy.Node;
import com.xylink.manager.model.dmcu.ConnectionAndLevel;
import com.xylink.manager.model.dmcu.DmcuConnRequest;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.dmcu.listener.DmcuConnChangedListener;
import com.xylink.util.MemoryPaginationUtil;
import com.xylink.util.SearchDtoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/7/12 2:26 下午
 */
@Service
public class DmcuPersistenceService {
    private static final Logger logger = LoggerFactory.getLogger(DmcuPersistenceService.class);
    private final K8sService k8sService;
    @Autowired
    private List<DmcuConnChangedListener> listeners;
    @Autowired
    private IDeployService deployService;

    public DmcuPersistenceService(K8sService k8sService) {
        this.k8sService = k8sService;
    }

    public Page<DmcuConnectionDto> getTerminalStrategyList(String key, Pageable pageable) {
        return getDmcuOrTerminalListUtil(key, pageable, Constants.TERMINAL_INSERT_PREFIX);
    }

    public void updateTerminalInsertStrategy(DmcuConnRequest dmcuConnRequest) {
        updateResUtil(dmcuConnRequest, Constants.TERMINAL_INSERT_PREFIX);
    }

    public void deleteTerminalInsertStrategy(String target) {
        deleteResUtil(target, Constants.TERMINAL_INSERT_PREFIX);
    }

    public void addTerminalInsertStrategy(DmcuConnRequest dmcuConnRequest) {
        addPersistenceUtil(dmcuConnRequest, Constants.TERMINAL_INSERT_PREFIX);
    }

    public void updateDmcuConnection(DmcuConnRequest dmcuConnRequest) {
        updateResUtil(dmcuConnRequest, Constants.DMCU_CONNECTION_PREFIX);
    }

    public Page<DmcuConnectionDto> getDmcuConnectionList(String key, Pageable pageable) {
        return getDmcuOrTerminalListUtil(key, pageable, Constants.DMCU_CONNECTION_PREFIX);
    }

    public void deleteDmcuConnection(String target) {
        deleteResUtil(target, Constants.DMCU_CONNECTION_PREFIX);
    }

    public void addDmcuConnection(DmcuConnRequest dmcuConnRequest) {
        addPersistenceUtil(dmcuConnRequest, Constants.DMCU_CONNECTION_PREFIX);
    }

    public Page<DmcuInfoDto> getDmcu(String key, Pageable pageable) {
        List<DmcuInfoDto> dmcuInfoDtoList = dmcuListWithSiteCode();
        if (CollectionUtils.isEmpty(dmcuInfoDtoList)) {
            return Page.emptyPage(pageable);
        }

        List<DmcuInfoDto> records = SearchDtoUtil.searchKeyWord(dmcuInfoDtoList, key);
        return MemoryPaginationUtil.pagination(records, pageable);
    }

    /**
     * 根据sitecode去掉英文前缀并去重，比如EDEF.86，去掉EDEF，返回86
     * 不太明白
     *
     * @return
     */
    public List<String> getClientDistinctSiteCode() {
        List<DmcuInfoDto> dmcuList = dmcuListWithSiteCode();
        Set<String> temp = new HashSet<>();
        for (DmcuInfoDto infoDto : dmcuList) {
            String siteCode = infoDto.getSiteCode();
            if (org.apache.commons.lang3.StringUtils.isBlank(siteCode)) {
                continue;
            }
            String[] data = siteCode.split("\\.");
            if (data.length > 1) {
                temp.add(data[1]);
            }
        }
        return new ArrayList<>(temp).stream().sorted().collect(Collectors.toList());
    }

    /**
     * 根据sitecode去重
     *
     * @return
     */
    public List<String> getDistinctSiteCode() {
        List<DmcuInfoDto> dmcuList = dmcuListWithSiteCode();
        Set<String> temp = new HashSet<>();
        for (DmcuInfoDto infoDto : dmcuList) {
            temp.add(infoDto.getSiteCode());
        }
        return new ArrayList<>(temp);
    }

    /**
     * 获取包含转义之后的sitecode的DmcuInfoDto列表
     *
     * @return
     */
    public List<DmcuInfoDto> dmcuListWithSiteCode() {
        List<Node> nodeByLabel = k8sService.getNodeByLabel(Labels.dmcu.label(), Constants.XYLINK);
        List<Node> nodeByX86Label = k8sService.getNodeByLabel(Labels.dmcu_x86.label(), Constants.XYLINK);
        List<Node> nodeByArmLabel = k8sService.getNodeByLabel(Labels.dmcu_arm.label(), Constants.XYLINK);
        Set<String> dmcuNodeNameSet = new HashSet<>(20);
        nodeByLabel.forEach(node -> {
            dmcuNodeNameSet.add(node.getName());
        });
        nodeByX86Label.forEach(x86Node -> {
            dmcuNodeNameSet.add(x86Node.getName());
        });
        nodeByArmLabel.forEach(x86Node -> {
            dmcuNodeNameSet.add(x86Node.getName());
        });
        Map<String, String> allDmcu = k8sService.getConfigmap(Constants.CONFIGMAP_DMCU);
        if (CollectionUtils.isEmpty(allDmcu)) {
            return Collections.emptyList();
        }
        List<DmcuInfoDto> res = new ArrayList<>(dmcuNodeNameSet.size());
        Set<String> dmcuNodeList = dmcuNodeList(allDmcu);
        dmcuNodeList.forEach(nodeName->{
            if (!dmcuNodeNameSet.contains(nodeName)) {
                return;
            }
            DmcuInfoDto dmcuInfoDto = new DmcuInfoDto();
            String siteCodeSource = getDmcuSiteCode(nodeName,allDmcu);
            String siteCode = SiteCodeUtils.getEnterpriseAndSite(SiteCodeUtils.buildSiteCode(siteCodeSource));

            dmcuInfoDto.setPublicIp(allDmcu.get(nodeName + NetworkConstants.SUFFIX_PUBLIC_IP));
            dmcuInfoDto.setInternalIp(allDmcu.get(nodeName + NetworkConstants.SUFFIX_INTERNAL_IP));
            dmcuInfoDto.setSiteCode(siteCode);
            dmcuInfoDto.setNodeName(nodeName);

            res.add(dmcuInfoDto);
        });

        // nmst 内外网IP信息
        List<DmcuInfoDto> nmstList = res.stream().filter(item->org.apache.commons.lang3.StringUtils.isBlank(item.getInternalIp())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(nmstList)){
            Map<String, String> allNmst = k8sService.getConfigmap(Constants.CONFIGMAP_NMST);
            nmstList.forEach(item->{
                String nodeName = item.getNodeName();
                item.setPublicIp(allNmst.get(nodeName + NetworkConstants.SUFFIX_PUBLIC_IP));
                item.setInternalIp(allNmst.get(nodeName + NetworkConstants.SUFFIX_INTERNAL_IP));
            });
        }

        return res.stream().filter(item->!org.apache.commons.lang3.StringUtils.isBlank(item.getInternalIp())).collect(Collectors.toList());
    }

    private Set<String> dmcuNodeList(Map<String, String> configMapAllDmcu) {
        if (configMapAllDmcu == null || configMapAllDmcu.isEmpty()) {
            return Collections.emptySet();
        }
        return configMapAllDmcu.keySet().stream().map(this::dmcuNodeName).filter(Objects::nonNull).collect(Collectors.toSet());
    }

    private String dmcuNodeName(String key) {
        if (key.endsWith(NetworkConstants.SUFFIX_SITECODE) && !key.startsWith(NetworkConstants.SUFFIX_DEFAULT)) {
            return key.replace(NetworkConstants.SUFFIX_SITECODE, "");
        }
        if (key.endsWith(NetworkConstants.SUFFIX_INTERNAL_IP) && !key.endsWith(DmcuConstants.PEER_INTERNAL_IP)) {
            return key.replace(NetworkConstants.SUFFIX_INTERNAL_IP, "");
        }
        return null;
    }

    private String getDmcuSiteCode(String nodeName,Map<String,String> configMapAllDmcu){
        String siteCode = configMapAllDmcu.get(nodeName + NetworkConstants.SUFFIX_SITECODE);
        if (StringUtils.isEmpty(siteCode)){
            siteCode = configMapAllDmcu.get(NetworkConstants.SUFFIX_DEFAULT+NetworkConstants.SUFFIX_SITECODE);
        }
        return siteCode;
    }

    /**
     * 从allMc中获取 Dmcu级联关系 或 Terminal接入策略 的列表的工具方法，
     * 作用：1.替换配置文件时查询列表结果  2.直接查询列表结果返回给前端
     * @param constantsPrefix
     * @return
     */
    public List<DmcuConnectionDto> getDmcuOrTerminalList(String constantsPrefix) {
        Map<String, String> allMc = getAllMc();
        Map<String, List<DmcuInfoDto>> dmcuListMap = getDmcuInfoDtos();
        List<DmcuConnectionDto> res = new ArrayList<>();

        for (Map.Entry<String, String> entry : allMc.entrySet()) {
            if (entry.getKey().startsWith(constantsPrefix)) {
                DmcuConnectionDto temp = new DmcuConnectionDto();
                temp.setTargetSiteCode(setTargetSiteCode(entry.getKey(), constantsPrefix, dmcuListMap));
                temp.setConnSiteCode(setConnSiteCode(entry.getValue()));
                res.add(temp);
            }
        }
        return res;
    }

    private SiteCodeDto setTargetSiteCode(String targetKey, String constantsPrefix, Map<String, List<DmcuInfoDto>> dmcuListMap) {
        SiteCodeDto targetSiteCode = new SiteCodeDto();

        String target = targetKey.replace(constantsPrefix, "");
        targetSiteCode.setSiteCode(target);
        if (constantsPrefix.equalsIgnoreCase(Constants.DMCU_CONNECTION_PREFIX)) {
            targetSiteCode.setDmcuInfos(dmcuListMap.get(target));
        }
        return targetSiteCode;
    }

    private List<SiteCodeDto> setConnSiteCode(String targetValue) {
        List<SiteCodeDto> res = new ArrayList<>();
        String[] connInfos = targetValue.split(",");
        for (String connInfo : connInfos) {
            SiteCodeDto connDto = new SiteCodeDto();
            String[] info = connInfo.split(":");

            connDto.setSiteCode(info[0]);
            connDto.setLevel(Integer.parseInt(info[1]));
            res.add(connDto);
        }
        return res;
    }

    /**
     * 添加 Dmcu级联 / Terminal接入策略
     *
     * @param dmcuConnRequest
     * @param constantsPrefix 是终端前缀还是DmcuConn前缀
     */
    private void addPersistenceUtil(DmcuConnRequest dmcuConnRequest, String constantsPrefix) {
        Map<String, String> allMc = getAllMc();
        String targetKey = constantsPrefix + dmcuConnRequest.getTarget();
        if (allMc.containsKey(targetKey)) {
            throw new ServiceErrorException(ErrorStatus.ENTITY_NOT_UNIQUE);
        }

        StringBuilder builder = new StringBuilder();
        for (ConnectionAndLevel connectionAndLevel : dmcuConnRequest.getConnectionAndLevelList()) {
            builder.append(connectionAndLevel.getConnection()).append(":").append(connectionAndLevel.getLevel()).append(",");
        }
        String targetValue = builder.substring(0, builder.length() - 1);

        Map<String, String> res = new HashMap<>(4);
        res.put(targetKey, targetValue);

        editConfigMapAndListen(res, targetKey);
    }

    /**
     * 删除 Dmcu级联/终端接入策略 目标数据
     *
     * @param target
     * @param constantsPrefix configMap里的key值前缀
     */
    private void deleteResUtil(String target, String constantsPrefix) {
        Map<String, String> allMc = getAllMc();
        String targetKey = constantsPrefix + target;
        if (allMc.containsKey(targetKey)) {
            allMc.remove(targetKey);
            k8sService.replaceConfigmap(Constants.CONFIGMAP_MC, allMc);
            listeners.forEach(item -> {
                if (item.supports(targetKey)) {
                    item.onDmcuConnChanged(targetKey);
                }
            });
            logger.info("targetKey:{} of configMap:{} remove successfully", targetKey, Constants.CONFIGMAP_MC);
        }
    }

    /**
     * 更新 Dmcu级联/终端接入策略 目标数据
     *
     * @param dmcuConnRequest
     * @param constantsPrefix
     */
    private void updateResUtil(DmcuConnRequest dmcuConnRequest, String constantsPrefix) {
        String targetKey = constantsPrefix + dmcuConnRequest.getTarget();
        Map<String, String> res = new HashMap<>(16);
        StringBuilder builder = new StringBuilder();

        for (ConnectionAndLevel connectionAndLevel : dmcuConnRequest.getConnectionAndLevelList()) {
            builder.append(connectionAndLevel.getConnection()).append(":").append(connectionAndLevel.getLevel()).append(",");
        }
        res.put(targetKey, builder.substring(0, builder.length() - 1));

        editConfigMapAndListen(res, targetKey);
    }

    /**
     * 获取 Dmcu级联/终端接入策略 列表工具类
     *
     * @param key
     * @param pageable
     * @param constantsPrefix configMap里的key值前缀
     * @return
     */
    private Page<DmcuConnectionDto> getDmcuOrTerminalListUtil(String key, Pageable pageable, String constantsPrefix) {
        List<DmcuConnectionDto> records = SearchDtoUtil.searchKeyWord(getDmcuOrTerminalList(constantsPrefix), key);
        List<Node> nodeList = deployService.listAllNodes();
        HashMap<String, String> nodeTypeMap = new HashMap<>();
        nodeList.forEach(node -> nodeTypeMap.put(node.getName(), node.getType()));
        for (DmcuConnectionDto record : records) {
            List<DmcuInfoDto> dmcuInfos = record.getTargetSiteCode().getDmcuInfos();
            if (dmcuInfos != null) {
                List<DmcuInfoDto> dmcuRecordList = dmcuInfos.stream().filter(dmcuInfoDto -> nodeTypeMap.get(dmcuInfoDto.getNodeName()) != null && nodeTypeMap.get(dmcuInfoDto.getNodeName()).contains("dmcu")).collect(Collectors.toList());
                record.getTargetSiteCode().setDmcuInfos(dmcuRecordList);
            }
        }
        return MemoryPaginationUtil.pagination(records, pageable);
    }

    /**
     * 获取sitecode 对应的 DmcuInfoDto信息
     *
     * @return
     */
    private Map<String, List<DmcuInfoDto>> getDmcuInfoDtos() {
        Map<String, List<DmcuInfoDto>> res = new HashMap<>(16);

        for (DmcuInfoDto infoDto : dmcuListWithSiteCode()) {
            List<DmcuInfoDto> temp = new ArrayList<>();
            if (res.containsKey(infoDto.getSiteCode())) {
                temp = res.get(infoDto.getSiteCode());
            }
            temp.add(infoDto);
            res.put(infoDto.getSiteCode(), temp);
        }
        return res;
    }

    /**
     * 在ConfigMap原有数据上追加结果集，并且监听结果集中数据的写入
     *
     * @param res
     * @param targetKey
     */
    private void editConfigMapAndListen(Map<String, String> res, String targetKey) {
        k8sService.editConfigmap(Constants.CONFIGMAP_MC, res);
        listeners.forEach(item -> {
            if (item.supports(targetKey)) {
                item.onDmcuConnChanged(targetKey);
            }
        });
        logger.info("targetKey:{} configMap:{} (add or update) successfully", targetKey, Constants.CONFIGMAP_MC);
    }

    private Map<String, String> getAllMc() {
        return k8sService.getConfigmap(Constants.CONFIGMAP_MC);
    }
}
