package com.xylink.manager.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.exception.basic.ClientErrorException;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.config.exception.basic.ServiceErrorException;
import com.xylink.manager.model.common.Page;
import com.xylink.manager.model.common.Pageable;
import com.xylink.manager.model.em.ClientPlatformEnum;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.base.K8sSvcService;
import com.xylink.manager.service.base.SystemModeConfig;
import com.xylink.manager.service.dto.SDKLibraryInfoDTO;
import com.xylink.manager.service.remote.logagent.DeviceSearchTypeEnum;
import com.xylink.manager.service.remote.logagent.LogagentRemoteClient;
import com.xylink.manager.service.remote.logagent.dto.LogagentDeviceTypeResponse;
import com.xylink.util.Ipv6Util;
import com.xylink.util.JDBCUtils;
import com.xylink.util.MemoryPaginationUtil;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.FileCopyUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.net.URI;
import java.net.URLEncoder;
import java.util.*;

@Service
public class ClientService {
    @Autowired
    private ServerListService serverListService;
    @Autowired
    private K8sService k8sService;
    @Autowired
    private K8sSvcService k8sSvcService;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private ObjectMapper objectMapper;
    @Resource
    private LogagentRemoteClient logagentRemoteClient;

    @Autowired
    private JDBCUtils jdbcUtils;

    @Autowired
    private GreyReleaseService greyReleaseService;
    private String appDownloadDir;
    private static final String FILE_TYPE = "fileType";
    //安装包类型
    private static final String TYPE = "type";
    private static final String TYPE_IOS = "ios";
    private static final String PUBTYPE = "pubType";
    private static final String CK = "customizedKey";
    private static final String RELEASE_NOTES = "releaseNotes";
    private static final String PUBTYPE_FULL_GREY = "full_grey";
    private static final String PUBTYPE_FULL_UPGRADE = "full_upgrade";
    private static final String FILE_TYPE_COMMON = "common";
    private static final String FILE_TYPE_CUSTOMIZED = "customized";
    private static final String FILE_TYPE_THIRD_PARTY = "thirdParty";
    private static final String FILE_TYPE_MANUAL = "manual";
    private static final String FILE_PATH= "/upload/client/";

    @PostConstruct
    private void loadDir() {
        appDownloadDir = StringUtils.isBlank(k8sService.getConfigmap(Constants.CONFIGMAP_PRIVATE_DATA).get("appDownloadUploadDir"))?"/var/nginx/www/appdownload/":k8sService.getConfigmap(Constants.CONFIGMAP_PRIVATE_DATA).get("appDownloadUploadDir");
    }

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    public void handleFileForward(MultipartFile file, HttpMethod method, HttpServletRequest request) throws Exception {
        String fileType = request.getParameter(FILE_TYPE);
        if (FILE_TYPE_THIRD_PARTY.equalsIgnoreCase(fileType)) {
            handleThirdPartyFileForward(file,request);
        }else if (FILE_TYPE_MANUAL.equalsIgnoreCase(fileType)){
            handleManualFileForward(file,request);
        }else {
            handleCommonAndCustomizedFileForward(file, method, request);
        }

    }

    public void handleCommonAndCustomizedFileForward(MultipartFile file, HttpMethod method, HttpServletRequest request) throws Exception {
        String server;
        if(SystemModeConfig.isNewCms()){
            String mainIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP).get(NetworkConstants.MAIN_INTERNAL_IP);
            server = k8sSvcService.getLogAgentPodIpByNodeIp(mainIp);
        }else {
            server = "127.0.0.1";
        }

        String fileType = request.getParameter(FILE_TYPE);
        String type = request.getParameter(TYPE);
        String pubType = request.getParameter(PUBTYPE);
        String customizedKey = request.getParameter(CK);
        String releaseNotes = StringUtils.isBlank(request.getParameter(RELEASE_NOTES)) ? "" : request.getParameter(RELEASE_NOTES);

        String path = FILE_PATH;
        if (FILE_TYPE_CUSTOMIZED.equalsIgnoreCase(fileType)) {
            if (StringUtils.isBlank(type)) throw new ClientErrorException(ErrorStatus.ILLEGAL_REQUEST);
            if (StringUtils.isBlank(pubType)) throw new ClientErrorException(ErrorStatus.ILLEGAL_REQUEST);
            if (StringUtils.isBlank(customizedKey)) throw new ServiceErrorException(ErrorStatus.NO_CUSTOMIZED_KEY);
            if (StringUtils.equalsIgnoreCase(type, TYPE_IOS) && StringUtils.equalsIgnoreCase(pubType, PUBTYPE_FULL_UPGRADE)
                    && greyReleaseService.existedIosMaxVersion(Constants.IOS_MAX_VERSION_PREFIX + customizedKey)) {
                pubType = PUBTYPE_FULL_GREY;
            }
            path = path + customizedKey.trim() + "?type=" + type + "&pubType=" + pubType + "&releaseNotes=" + URLEncoder.encode(releaseNotes, "UTF-8");
        } else {
            if (StringUtils.equalsIgnoreCase(pubType, PUBTYPE_FULL_UPGRADE) && greyReleaseService.existedIosMaxVersion(Constants.IOS_MAX_VERSION)) {
                pubType = PUBTYPE_FULL_GREY;
            }
            path = path + "?pubType=" + pubType + "&releaseNotes=" + URLEncoder.encode(releaseNotes, "UTF-8");
        }

        String url = "http://" + Ipv6Util.handlerIpv6Addr(server) + ":" + NetworkConstants.LOGAGENT_PORT + path;

        logger.info("upload client package: " + path);
        URI uri = new URI("http", null, server, NetworkConstants.LOGAGENT_PORT, path, null, null);

        LinkedMultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
        map.add(file.getName(), new MultipartFileResource(file.getOriginalFilename(), file.getSize(), file.getInputStream()));

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

        HttpEntity<LinkedMultiValueMap<String, Object>> requestEntity = new HttpEntity<>(map, headers);

        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setBufferRequestBody(false);

        restTemplate.setRequestFactory(requestFactory);

        restTemplate.exchange(url, method, requestEntity, String.class);
    }

    /**
     * sdk库文件上传
     * @param file
     */
    public void handleSDKFileForward(MultipartFile file) {
        String server;
        if(SystemModeConfig.isNewCms()){
            String mainIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP).get(NetworkConstants.MAIN_INTERNAL_IP);
            server = k8sSvcService.getLogAgentPodIpByNodeIp(mainIp);
        }else {
            server = "127.0.0.1";
        }

        String url = "http://" + Ipv6Util.handlerIpv6Addr(server) + ":" + NetworkConstants.LOGAGENT_PORT + "/upload/sdk/";

        LinkedMultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setBufferRequestBody(false);

        restTemplate.setRequestFactory(requestFactory);
        SDKLibraryInfoDTO dto = null;
        try {
            map.add(file.getName(), new MultipartFileResource(file.getOriginalFilename(), file.getSize(), file.getInputStream()));
            HttpEntity<LinkedMultiValueMap<String, Object>> requestEntity = new HttpEntity<>(map, headers);
            ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
            if (responseEntity.getStatusCodeValue() != HttpStatus.OK.value() || ObjectUtils.isEmpty(responseEntity.getBody())) {
                throw new ServiceErrorException(ErrorStatus.CLIENT_FILE_UPLOAD_FAILED);
            }
            String responseBody = responseEntity.getBody();
            dto = objectMapper.readValue(responseBody, SDKLibraryInfoDTO.class);
        } catch (ServiceErrorException e) {
            throw e;
        } catch (Exception e) {
            logger.error("handle SDK LibraryFile:{} Forward failed,",file.getOriginalFilename(), e);
            throw new ServiceErrorException(ErrorStatus.CLIENT_FILE_UPLOAD_FAILED);
        }

        logger.info("handle SDK LibraryFile dto:{}", dto);

        if (StringUtils.isAnyBlank(dto.getVersion(),dto.getMd5())) {
            throw new ClientErrorException(ErrorStatus.LOGAGENT_ANALYZE);
        }

        String[] arr = dto.getVersion().split("_");
        if (arr.length < 3) {
            throw new ServiceErrorException(ErrorStatus.LOGAGENT_ANALYZE);
        }

        String downloadUrl = serverListService.getAccessMainAddress() + "/appdownload/" + dto.getRelativePath();
        jdbcUtils.saveSDKFileUrl(dto.getVersion(), downloadUrl, dto.getMd5());
    }

    public Page<SDKLibraryInfoDTO> getSDKLibraryList(Pageable pageable, String key) {
        String server;
        if(SystemModeConfig.isNewCms()){
            String mainIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP).get(NetworkConstants.MAIN_INTERNAL_IP);
            server = k8sSvcService.getLogAgentPodIpByNodeIp(mainIp);
        }else {
            server = "127.0.0.1";
        }

        String url = "http://" + Ipv6Util.handlerIpv6Addr(server) + ":" + NetworkConstants.LOGAGENT_PORT + "/version/sdk/";
        ArrayList<SDKLibraryInfoDTO> list = new ArrayList<>();
        try {
            SDKLibraryInfoDTO[] libraryInfoDTO = restTemplate.getForObject(url, SDKLibraryInfoDTO[].class);
            if (ObjectUtils.isNotEmpty(libraryInfoDTO)) {
                list = Lists.newArrayList(libraryInfoDTO);
            }
        } catch (Exception e) {
            logger.error("getSDKLibraryList failed,", e);
            throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_LOGAGENT_FAILED);
        }

        for (SDKLibraryInfoDTO temp : list) {
            String[] arr = temp.getVersion().split("_");
            if (arr.length < 3) {
                continue;
            }
            temp.setAbiType(arr[2]);
        }

        return MemoryPaginationUtil.searchAndPage(pageable, key, list);
    }

    public void deleteSDKLibrary(SDKLibraryInfoDTO dto) {
        String server;
        if(SystemModeConfig.isNewCms()){
            String mainIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP).get(NetworkConstants.MAIN_INTERNAL_IP);
            server = k8sSvcService.getLogAgentPodIpByNodeIp(mainIp);
        }else {
            server = "127.0.0.1";
        }

        String url = "http://" + Ipv6Util.handlerIpv6Addr(server) + ":" + NetworkConstants.LOGAGENT_PORT + "/delete/sdk/";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<SDKLibraryInfoDTO> request = new HttpEntity<>(dto, headers);

        try {
            restTemplate.postForObject(url, request, String.class);
        } catch (Exception e) {
            logger.error("deleteSDKLibrary dto:{} failed,", dto, e);
            throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_LOGAGENT_FAILED);
        }
        jdbcUtils.deleteSDKFileUrl(dto.getVersion());
    }

    public void handleThirdPartyFileForward(MultipartFile file, HttpServletRequest request) throws Exception {
        //将file写入/var/nginx/www/appdownload目录下
        createFile(file,appDownloadDir);
        //在/var/nginx/www/appdownload目录下生成app_pkt_info_{}.json文件
        createJsonFile(file,request,appDownloadDir);
    }

    public void handleManualFileForward(MultipartFile file, HttpServletRequest request) throws Exception {
        logger.info("Upload manual file. Request info: isCustomer:{},greyName:{},jsonFile:{}", request.getParameter("isCustomer"), request.getParameter("greyName"), request.getParameter("jsonFile"));
        boolean isCustomer = Boolean.parseBoolean(request.getParameter("isCustomer"));
        String targetDir = appDownloadDir;
        String jsonFile = request.getParameter("jsonFile");
        if (StringUtils.isBlank(jsonFile)){
            throw new ClientErrorException(ErrorStatus.PARAM_ERROR);
        }
        if (isCustomer) {
            String greyName = request.getParameter("greyName");
            if (StringUtils.isBlank(greyName)){
                throw new ClientErrorException(ErrorStatus.PARAM_ERROR);
            }
            String customizedKey = request.getParameter(CK);
            if (StringUtils.isBlank(customizedKey)) {
                throw new ServiceErrorException(ErrorStatus.NO_CUSTOMIZED_KEY);
            }
            targetDir += greyName + "/" + customizedKey + "/";
        }
        String targetFile = targetDir + jsonFile;
        createFile(file, appDownloadDir);
        createManualJsonFile(file, targetFile);
    }

    public List<LogagentDeviceTypeResponse> list(String searchType){
        List<LogagentDeviceTypeResponse> result = logagentRemoteClient.deviceTypes(EnumUtils.getEnum(DeviceSearchTypeEnum.class, searchType));
        result.parallelStream().forEach(LogagentDeviceTypeResponse::convertClientName);
        return result;
    }

    private void createJsonFile(MultipartFile file, HttpServletRequest request,String targetDir) throws Exception {
        Map<String,Long> map = new HashMap<>();
        map.put("length", file.getSize());

        ObjectNode object = JsonNodeFactory.instance.objectNode();
        object.put("version", request.getParameter("version"));
        object.put("md5", md5sum(file.getOriginalFilename()));
        object.put("relativePath", file.getOriginalFilename());
        object.put("buildTime", System.currentTimeMillis());
        object.put("releaseNote", Strings.EMPTY);
        object.putPOJO("bypassParams", map);

        String platfrom = ClientPlatformEnum.valueOf(request.getParameter("clientType")).getPlatform();

        File jsonFile = new File(targetDir + "app_pkt_info_" + platfrom + ".json");
        if (jsonFile.exists()) {
            if(!jsonFile.delete()){
                throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
            }
        }
        if(!jsonFile.createNewFile()){
            throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
        }

        FileWriter fw = new FileWriter(jsonFile);
        BufferedWriter bw = new BufferedWriter(fw);
        bw.write(object.toString());
        bw.close();
    }

    private void createManualJsonFile(MultipartFile file, String targetFile) throws Exception {
        Map<String,Long> map = new HashMap<>();
        map.put("length", file.getSize());

        ObjectNode object = JsonNodeFactory.instance.objectNode();
        object.put("version", "1.0");
        object.put("md5", md5sum(file.getOriginalFilename()));
        object.put("relativePath", file.getOriginalFilename());
        object.put("buildTime", System.currentTimeMillis());
        object.put("releaseNote", Strings.EMPTY);
        object.putPOJO("bypassParams", map);

        File jsonFile = new File(targetFile);
        if (jsonFile.exists()) {
            if(!jsonFile.delete()){
                throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
            }
        }
        if (!jsonFile.getParentFile().exists()) {
            jsonFile.getParentFile().mkdirs();
        }
        if(!jsonFile.createNewFile()){
            throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
        }
        FileWriter fw = new FileWriter(jsonFile);
        BufferedWriter bw = new BufferedWriter(fw);
        bw.write(object.toString());
        bw.close();
        logger.info("createFile {} success.", jsonFile.getPath());
    }

    private String md5sum(String fileName) {
        File fileDisk = new File(appDownloadDir + fileName);
        FileInputStream fis;
        try {
            fis = new FileInputStream(fileDisk);
            String md5 = org.apache.commons.codec.digest.DigestUtils.md5Hex(fis);
            fis.close();
            logger.info(fileDisk.getName() + " MD5 value: " + md5);
            return md5;
        } catch (FileNotFoundException e) {
            logger.error("file not found :" + fileName + "!\n", e);
            throw new ServerException(ErrorStatus.NO_SUCH_FILE);
        } catch (IOException e) {
            logger.error("fail to get md5 for"+fileName+"!\n",e);
            throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
        }
    }

    private void createFile(MultipartFile multipartFile,String targetDir) throws Exception {
        if (!multipartFile.isEmpty()) {
            String fileName = multipartFile.getOriginalFilename();
            File fileDisk = new File(targetDir + fileName);
            if (fileDisk.exists()) {
                if(!fileDisk.delete()) {
                    throw new RuntimeException("file delete error");
                }
            }
            if (!fileDisk.getParentFile().exists()) {
                fileDisk.getParentFile().mkdirs();
            }
            if(!fileDisk.createNewFile()) {
                throw new RuntimeException("file create error");
            }
            BufferedOutputStream stream = new BufferedOutputStream(new FileOutputStream(fileDisk));
            FileCopyUtils.copy(multipartFile.getInputStream(), stream);
            logger.info("createFile {} success.",fileDisk.getPath());
        }else{
            logger.info("createFile multipartFile.isEmpty");
        }
    }

    static class MultipartFileResource extends InputStreamResource {

        private final String filename;
        private final long size;

        public MultipartFileResource(String filename, long size, InputStream inputStream) {
            super(inputStream);
            this.size = size;
            this.filename = filename;
        }

        @Override
        public String getFilename() {
            return this.filename;
        }

        @Override
        public InputStream getInputStream() throws IOException, IllegalStateException {
            return super.getInputStream(); //To change body of generated methods, choose Tools | Templates.
        }

        @Override
        public long contentLength() throws IOException {
            return size;
        }

    }

}
