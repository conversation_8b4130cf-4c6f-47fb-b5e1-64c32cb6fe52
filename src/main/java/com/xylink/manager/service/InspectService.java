package com.xylink.manager.service;

import com.xylink.config.Constants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServiceErrorException;
import com.xylink.manager.controller.bo.inspect.InspectionConditionBO;
import com.xylink.manager.controller.dto.inspect.*;
import com.xylink.manager.model.common.Page;
import com.xylink.manager.model.em.InspectItemEnum;
import com.xylink.manager.model.em.InspectLadderEnum;
import com.xylink.manager.service.cache.bean.PodCache;
import com.xylink.manager.service.cache.service.ICacheService;
import com.xylink.manager.service.nightingale.MonitorN9eService;
import com.xylink.util.InspectJDBCUtils;
import com.xylink.util.MemoryPaginationUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

@Slf4j
@Service
public class InspectService {

    private static ReentrantLock lock = new ReentrantLock();

    private DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private InspectJDBCUtils inspectJDBCUtils;

    @Autowired
    private InspectAsyncService inspectAsyncService;
    @Autowired
    private ICacheService cacheService;
    @Resource
    private MonitorN9eService monitorN9eService;

    /**
     * 获取巡检实例
     */
    public Page<InspectionInstanceDTO> pageInspectRecordList(String startTime, String endTime, long pageSize, long current) {
        inspectCompensation();
        long before30days = LocalDateTime.now().minusDays(30).toInstant(ZoneOffset.of("+8")).toEpochMilli();
        long now = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        long start = before30days;
        long end = now;
        if (StringUtils.isNotBlank(startTime)) {
            start = LocalDateTime.parse(startTime, formatter).toInstant(ZoneOffset.of("+8")).toEpochMilli();
        }
        if (StringUtils.isNotBlank(endTime)) {
            end = LocalDateTime.parse(endTime, formatter).toInstant(ZoneOffset.of("+8")).toEpochMilli();
        }
        start = start > before30days ? start : before30days;
        end = end > now ? now : end;
        List<InspectionInstanceDTO> inspectRecordList = inspectJDBCUtils.getInspectionInstance(start, end);
        return MemoryPaginationUtil.pagination(inspectRecordList, current, pageSize);
    }

    /**
     * 执行巡检
     */
    public void inspectExecute(InspectionConditionBO inspectionConditionBO) {
        if (inspectJDBCUtils.hasRunningInstance()) {
            //巡检补偿
            inspectCompensation();
            throw new ServiceErrorException(HttpStatus.BAD_REQUEST, ErrorStatus.INSPECT_TASK_IS_RUNNING);
        }
        long id = System.currentTimeMillis();
        InspectionInstanceDTO inspectionInstanceDTO = new InspectionInstanceDTO();
        inspectionInstanceDTO.setId(id);
        inspectionInstanceDTO.setCreateTime(System.currentTimeMillis());
        inspectionInstanceDTO.setFinalTime(System.currentTimeMillis());
        inspectionInstanceDTO.setJobStatus(2);
        inspectionInstanceDTO.setRiskNumber(0);
        inspectionInstanceDTO.setNormalNumber(0);
        inspectionInstanceDTO.setExceptNumber(0);
        if(StringUtils.isBlank(inspectionConditionBO.getInstanceName())){
            inspectionInstanceDTO.setInstanceName(String.valueOf(id));
        }else {
            inspectionInstanceDTO.setInstanceName(inspectionConditionBO.getInstanceName());
        }
        inspectionInstanceDTO.setInstanceFinalTime(inspectionConditionBO.getInstanceFinalTime());
        inspectJDBCUtils.insertInspectInstance(inspectionInstanceDTO);
        //异步执行巡检任务
        inspectAsyncService.inspectExecute(id, inspectionConditionBO);
    }

    /**
     * 获取巡检项列表
     */
    public InspectionItemVO getInspectItemList() {
        List<InspectionItemDTO> inspectItemList = inspectJDBCUtils.getInspectItemList();
        if (CollectionUtils.isEmpty(inspectItemList)) {
            throw new ServiceErrorException(ErrorStatus.UNEXPECTED_ERROR);
        }
        List<PodCache> podCacheList = cacheService.cachePodList();
        List<PodCache> serviceInspectionList = podCacheList.stream().filter(podCache ->
                (null != podCache.getMetadata().getLabels() && Constants.SERVICE_INSPECTION_NAME.equals(podCache.getMetadata().getLabels().get("app")))).collect(Collectors.toList());
        List<InspectionItemDTO> system = new ArrayList<>(3);
        List<InspectionItemDTO> service = new ArrayList<>(2);
        List<InspectionItemDTO> bigData = new ArrayList<>(7);
        List<InspectionItemDTO> middleware = new ArrayList<>(4);
        for (InspectionItemDTO item : inspectItemList) {
            InspectItemEnum inspectItemEnum = InspectItemEnum.getItem(item.getItemType());
            if (null == inspectItemEnum) {
                continue;
            }
            if (CollectionUtils.isEmpty(serviceInspectionList) && InspectItemEnum.SERVICE == inspectItemEnum
                    && Constants.INSPECT_SERVICE_INSPECTION.equals(item.getItemKey())) {
                continue;
            }
            switch (inspectItemEnum) {
                case SYSTEM:
                    system.add(item);
                    break;
                case SERVICE:
                    service.add(item);
                    break;
                case BIGDATA:
                    bigData.add(item);
                    break;
                case MIDDLEWARE:
                    middleware.add(item);
                    break;
            }
        }
        InspectionItemVO inspectionItemVO = new InspectionItemVO();
        if (!monitorN9eService.nightingaleSwitch()) {
            inspectionItemVO.setSystem(system);
        }
        inspectionItemVO.setService(service);
        inspectionItemVO.setBigData(bigData);
        inspectionItemVO.setMiddleware(middleware);
        return inspectionItemVO;
    }

    /**
     * 判断是否存在正在执行的巡检任务
     */
    public boolean hasRunningInstance() {
        boolean hasRunningInstance = inspectJDBCUtils.hasRunningInstance();
        if (hasRunningInstance) {
            //巡检补偿
            inspectCompensation();
        }
        return hasRunningInstance;
    }

    /**
     * 巡检补偿
     */
    public void inspectCompensation() {
        InspectionInstanceDTO inspectionInstance = inspectJDBCUtils.getRunningInspectionInstance();
        if (inspectionInstance != null) {
            InspectionServiceInstanceDTO inspectServiceInstance = inspectJDBCUtils.getInspectServiceInstance(inspectionInstance.getId());
            if (inspectServiceInstance != null && !Constants.INSPECT_STATUS_RUNNING.equals(inspectServiceInstance.getJobStatus())) {
                inspectionInstance.setJobStatus(inspectServiceInstance.getJobStatus());
                inspectionInstance.setFinalTime(System.currentTimeMillis());
                inspectionInstance.setRiskNumber(inspectionInstance.getRiskNumber() + inspectServiceInstance.getRiskNumber());
                inspectionInstance.setNormalNumber(inspectionInstance.getNormalNumber() + inspectServiceInstance.getNormalNumber());
                inspectionInstance.setExceptNumber(inspectionInstance.getExceptNumber() + inspectServiceInstance.getExceptNumber());
                inspectJDBCUtils.updateInspectInstance(inspectionInstance);
            }
        }
        inspectJDBCUtils.inspectCompensation(System.currentTimeMillis());
    }

    /**
     * 获取巡检详情
     */
    public InspectDetailsDTO getInspectDetails(long instanceId) {
        InspectDetailsDTO inspectDetailsDTO = new InspectDetailsDTO();
        InspectTaskDTO inspectTaskDTO = getInspectTaskList(instanceId);
        List<InspectionTaskDataDTO> inspectTaskBigDataList = inspectTaskDTO.getInspectTaskBigDataList();
        List<InspectionTaskMiddlewareDTO> inspectTaskMiddlewareList = inspectTaskDTO.getInspectTaskMiddlewareList();
        if(!monitorN9eService.nightingaleSwitch()) {
            List<InspectionTaskHostDTO> inspectTaskHostList = inspectTaskDTO.getInspectTaskHostList();
            if (!CollectionUtils.isEmpty(inspectTaskHostList)) {
                InspectDetailsChildBDTO hostInspectDetailsChild = getHostInspectDetailsChild(inspectTaskHostList);
                inspectDetailsDTO.setHostDetails(hostInspectDetailsChild);
            }
        }
        List<InspectionTaskServiceDTO> inspectTaskServiceList = inspectTaskDTO.getInspectTaskServiceList();
        //大数据巡检详情
        if (!CollectionUtils.isEmpty(inspectTaskBigDataList)) {
            List<InspectDetailsChildADTO> bigDataInspectDetailsChild = getBigDataInspectDetailsChild(inspectTaskBigDataList);
            inspectDetailsDTO.setBigDataDetails(bigDataInspectDetailsChild);
        }
        if (!CollectionUtils.isEmpty(inspectTaskMiddlewareList)) {
            List<InspectDetailsChildADTO> middlewareInspectDetailsChild = getMiddlewareInspectDetailsChild(inspectTaskMiddlewareList);
            inspectDetailsDTO.setMiddlewareDetails(middlewareInspectDetailsChild);
        }

        if (!CollectionUtils.isEmpty(inspectTaskServiceList)) {
            InspectDetailsChildBDTO serviceInspectDetailsChild = getServiceInspectDetailsChild(inspectTaskServiceList);
            inspectDetailsDTO.setServiceDetails(serviceInspectDetailsChild);
        }
        InspectionServiceInstanceDTO inspectServiceInstance = inspectJDBCUtils.getInspectServiceInstance(instanceId);
        if (inspectServiceInstance != null) {
            InspectDetailsChildBDTO serviceInspectDetail = new InspectDetailsChildBDTO(inspectServiceInstance.getNormalNumber(),
                    inspectServiceInstance.getRiskNumber(), inspectServiceInstance.getExceptNumber());
            inspectDetailsDTO.setServiceInspectDetails(serviceInspectDetail);
        }
        return inspectDetailsDTO;
    }

    /**
     * 获取大数据巡检详情
     */
    private List<InspectDetailsChildADTO> getBigDataInspectDetailsChild(List<InspectionTaskDataDTO> inspectTaskBigDataList) {
        Map<String, InspectLadderEnum> bigDataMap = new HashMap<>(4);
        for (InspectionTaskDataDTO inspectionTaskData : inspectTaskBigDataList) {
            InspectLadderEnum inspectLadderEnum = InspectLadderEnum.getInspectLadder(inspectionTaskData.getLadder());
            boolean contains = bigDataMap.containsKey(inspectionTaskData.getInspectionName());
            if (contains && bigDataMap.get(inspectionTaskData.getInspectionName()).getValue() >= inspectLadderEnum.getValue()) {
                continue;
            }
            bigDataMap.put(inspectionTaskData.getInspectionName(), inspectLadderEnum);
        }
        return bigDataMap.entrySet().stream().map(entry -> {
            InspectDetailsChildADTO inspectDetailsChildA = new InspectDetailsChildADTO();
            inspectDetailsChildA.setName(entry.getKey());
            inspectDetailsChildA.setStatus(entry.getValue().getType());
            return inspectDetailsChildA;
        }).collect(Collectors.toList());
    }

    /**
     * 获取中间件巡检详情
     */
    private List<InspectDetailsChildADTO> getMiddlewareInspectDetailsChild(List<InspectionTaskMiddlewareDTO> inspectTaskMiddlewareList) {
        Map<String, InspectLadderEnum> middlewareMap = new HashMap<>(4);
        for (InspectionTaskMiddlewareDTO inspectTaskMiddleware : inspectTaskMiddlewareList) {
            InspectLadderEnum inspectLadderEnum = InspectLadderEnum.getInspectLadder(inspectTaskMiddleware.getLadder());
            boolean contains = middlewareMap.containsKey(inspectTaskMiddleware.getTaskItem());
            if (contains && middlewareMap.get(inspectTaskMiddleware.getTaskItem()).getValue() >= inspectLadderEnum.getValue()) {
                continue;
            }
            middlewareMap.put(inspectTaskMiddleware.getTaskItem(), inspectLadderEnum);
        }
        return middlewareMap.entrySet().stream().map(entry -> {
            InspectDetailsChildADTO inspectDetailsChildA = new InspectDetailsChildADTO();
            inspectDetailsChildA.setName(entry.getKey());
            inspectDetailsChildA.setStatus(entry.getValue().getType());
            return inspectDetailsChildA;
        }).collect(Collectors.toList());
    }

    /**
     * 获取主机巡检详情
     */
    private InspectDetailsChildBDTO getHostInspectDetailsChild(List<InspectionTaskHostDTO> inspectTaskHostList) {
        InspectDetailsChildBDTO hostDetails = InspectDetailsChildBDTO.empty();
        inspectTaskHostList.forEach(inspectTaskHost -> {
            InspectLadderEnum inspectLadderEnum = InspectLadderEnum.getInspectLadder(inspectTaskHost.getLadder());
            if (inspectLadderEnum == InspectLadderEnum.NORMAL) {
                hostDetails.setNormalNumber(hostDetails.getNormalNumber() + 1);
            } else if (inspectLadderEnum == InspectLadderEnum.RISK) {
                hostDetails.setRiskNumber(hostDetails.getRiskNumber() + 1);
            } else {
                hostDetails.setExceptNumber(hostDetails.getExceptNumber() + 1);
            }
        });
        return hostDetails;
    }

    /**
     * 获取服务巡检详情
     */
    private InspectDetailsChildBDTO getServiceInspectDetailsChild(List<InspectionTaskServiceDTO> inspectTaskServiceList) {
        InspectDetailsChildBDTO serviceDetails = InspectDetailsChildBDTO.empty();
        inspectTaskServiceList.forEach(inspectTaskService -> {
            InspectLadderEnum inspectLadderEnum = InspectLadderEnum.getInspectLadder(inspectTaskService.getLadder());
            if (inspectLadderEnum == InspectLadderEnum.NORMAL) {
                serviceDetails.setNormalNumber(serviceDetails.getNormalNumber() + 1);
            } else if (inspectLadderEnum == InspectLadderEnum.RISK) {
                serviceDetails.setRiskNumber(serviceDetails.getRiskNumber() + 1);
            } else {
                serviceDetails.setExceptNumber(serviceDetails.getExceptNumber() + 1);
            }
        });
        return serviceDetails;
    }

    /**
     * 获取巡检任务列表
     */
    public InspectTaskDTO getInspectTaskList(long instanceId) {
        //查询巡检是否存在
        InspectionInstanceDTO inspect = inspectJDBCUtils.getInspect(instanceId);
        if (null == inspect) {
            throw new ServiceErrorException(HttpStatus.BAD_REQUEST, ErrorStatus.PARAM_NULL);
        }
        InspectTaskDTO inspectTaskDTO = new InspectTaskDTO();
        List<InspectionTaskDataDTO> inspectTaskBigDataList = inspectJDBCUtils.getInspectTaskBigDataList(instanceId);
        List<InspectionTaskMiddlewareDTO> inspectTaskMiddlewareList = inspectJDBCUtils.getInspectTaskMiddlewareList(instanceId);
        List<InspectionTaskHostDTO> inspectTaskHostList = inspectJDBCUtils.getInspectTaskHostList(instanceId);
        List<InspectionTaskServiceDTO> inspectTaskServiceList = inspectJDBCUtils.getInspectTaskServiceList(instanceId);
        inspectTaskServiceList = inspectTaskServiceList.stream().filter(s -> StringUtils.equals(Constants.INSPECT_SERVICE_PROBE, s.getTaskIndex())).collect(Collectors.toList());
        inspectTaskDTO.setInspectTaskBigDataList(inspectTaskBigDataList);
        inspectTaskDTO.setInspectTaskMiddlewareList(inspectTaskMiddlewareList);
        inspectTaskDTO.setInspectTaskHostList(inspectTaskHostList);
        inspectTaskDTO.setInspectTaskServiceList(inspectTaskServiceList);
        return inspectTaskDTO;
    }

    public List<Map<String, Object>> getExportExcelDataInspectHost(Long instanceId) {
        List<InspectionTaskHostDTO> hostDTOList = inspectJDBCUtils.getInspectTaskHostList(instanceId);
        return hostDTOList.stream().map(hostDTO -> {
            Map<String, Object> record = new HashMap<>();
            record.put("sheet1hostName", hostDTO.getHostName());
            record.put("sheet1systemDiskUsed", hostDTO.getSystemDiskUsed());
            record.put("sheet1dataDiskUsed", hostDTO.getDataDiskUsed());
            record.put("sheet1cpuUsed", hostDTO.getCpuUsed());
            record.put("sheet1memoryUsed", hostDTO.getMemoryUsed());
            InspectLadderEnum ladderEnum = InspectLadderEnum.getInspectLadder(hostDTO.getLadder());
            record.put("sheet1ladder", ladderEnum.getType());
            record.put("sheet1taskValue", hostDTO.getTaskValue());
            return record;
        }).collect(Collectors.toList());
    }

    public List<Map<String, Object>> getExportExcelDataInspectBigData(Long instanceId) {
        List<InspectionTaskDataDTO> bigDataDTOList = inspectJDBCUtils.getInspectTaskBigDataList(instanceId);
        return bigDataDTOList.stream().map(bigDataDTO -> {
            Map<String, Object> record = new HashMap<>();
            record.put("sheet2inspectionName", bigDataDTO.getInspectionName());
            record.put("sheet2taskItem", bigDataDTO.getTaskItem());
            record.put("sheet2taskIndex", bigDataDTO.getTaskIndex());
            InspectLadderEnum ladderEnum = InspectLadderEnum.getInspectLadder(bigDataDTO.getLadder());
            record.put("sheet2ladder", ladderEnum.getType());
            record.put("sheet2acquisitionTime", bigDataDTO.getAcquisitionTime());
            record.put("sheet2taskValue", bigDataDTO.getTaskValue());
            record.put("sheet2thresholdValue", bigDataDTO.getThresholdValue());
            return record;
        }).collect(Collectors.toList());
    }

    public List<Map<String, Object>> getExportExcelDataInspectMiddleware(Long instanceId) {
        List<InspectionTaskMiddlewareDTO> middlewareDTOList = inspectJDBCUtils.getInspectTaskMiddlewareList(instanceId);
        return middlewareDTOList.stream().map(middlewareDTO -> {
            Map<String, Object> record = new HashMap<>();
            record.put("sheet3inspectionName", "中间件");
            record.put("sheet3taskItem", middlewareDTO.getTaskItem());
            record.put("sheet3taskIndex", middlewareDTO.getTaskIndex());
            InspectLadderEnum ladderEnum = InspectLadderEnum.getInspectLadder(middlewareDTO.getLadder());
            record.put("sheet3ladder", ladderEnum.getType());
            record.put("sheet3taskValue", middlewareDTO.getTaskValue());
            return record;
        }).collect(Collectors.toList());
    }

    public List<Map<String, Object>> getExportExcelDataInspectService(Long instanceId) {
        List<InspectionTaskServiceDTO> serviceDTOList = inspectJDBCUtils.getInspectTaskServiceList(instanceId);
        return serviceDTOList.stream().sorted(Comparator.comparing(InspectionTaskServiceDTO::getServiceName)
                .thenComparing(InspectionTaskServiceDTO::getTaskIndex, Comparator.reverseOrder())).map(serviceDTO -> {
            Map<String, Object> record = new HashMap<>();
            record.put("sheet4podName", serviceDTO.getPodName());
            record.put("sheet4serviceName", serviceDTO.getServiceName());
            record.put("sheet4taskIndex", serviceDTO.getTaskIndex());
            InspectLadderEnum ladderEnum = InspectLadderEnum.getInspectLadder(serviceDTO.getLadder());
            record.put("sheet4ladder", ladderEnum.getType());
            record.put("sheet4taskValue", serviceDTO.getTaskValue());
            return record;
        }).collect(Collectors.toList());
    }

}
