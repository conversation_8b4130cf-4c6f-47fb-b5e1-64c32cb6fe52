package com.xylink.manager.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.controller.dto.DsAndDeploymentDto;
import com.xylink.manager.controller.dto.RestartServiceDto;
import com.xylink.manager.controller.dto.meetingcrypto.CryptoConfig;
import com.xylink.manager.controller.dto.meetingcrypto.CryptoConfigInfo;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.service.base.K8sService;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * 商密
 */
@Service
public class CryptoConfigService {

    private Logger logger = LoggerFactory.getLogger(CryptoConfigService.class);

    /**
     * 关闭商密
     */
    private static final String CRYPTO_CLOSE = "close";

    /**
     * 统一加密类型
     */
    private static final String CRYPTO_COMMON = "common";

    private static final String APPLICATION_SECURITY_STRATEGY = "APPLICATION_SECURITY_STRATEGY";

    private static final String APPLICATION_SECURITY_FEATURES = "APPLICATION_SECURITY_FEATURES";

    @Resource
    private K8sService k8sService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private ServerNetworkService serverNetworkService;


    public CryptoConfigInfo getCryptoConfigInfo() {
        return CryptoConfigInfo.builder().build();
    }

    public void uploadCaptchaJar(MultipartFile file, HttpMethod method) throws IOException {
        List<Pod> podListWithLabelInApp = k8sService.getPodListWithLabelInApp("private-captcha");
        //支持高可用
        if(podListWithLabelInApp != null && !podListWithLabelInApp.isEmpty()) {
            for (Pod pod : podListWithLabelInApp) {
                String url = "http://" + pod.getIp() + ":" + NetworkConstants.LOGAGENT_PORT + "/upload/jar/";
                LinkedMultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
                map.add(file.getName(), new MultipartFileResource(file.getOriginalFilename(), file.getSize(), file.getInputStream()));
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.MULTIPART_FORM_DATA);
                headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
                HttpEntity<LinkedMultiValueMap<String, Object>> requestEntity = new HttpEntity<>(map, headers);
                SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
                requestFactory.setBufferRequestBody(false);
                restTemplate.setRequestFactory(requestFactory);
                restTemplate.exchange(url, method, requestEntity, String.class);
            }
        }

    }

    static class MultipartFileResource extends InputStreamResource {

        private final String filename;
        private final long size;

        public MultipartFileResource(String filename, long size, InputStream inputStream) {
            super(inputStream);
            this.size = size;
            this.filename = filename;
        }

        @Override
        public String getFilename() {
            return this.filename;
        }

        @Override
        public InputStream getInputStream() throws IOException, IllegalStateException {
            return super.getInputStream(); //To change body of generated methods, choose Tools | Templates.
        }

        @Override
        public long contentLength() throws IOException {
            return size;
        }

    }

    public String testingCaptchaService() {
        Optional<Pod> podInApp = k8sService.getPodWithLabelInApp(Constants.POD_NAME_OPENRESTY_MAIN);
        if (podInApp.isPresent()) {
            String ip = podInApp.get().getIp();
            String url = "http://" + ip + ":11111/api/rest/secs/internal/crypto/test";
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
            return checkCaptchaService(responseEntity.getBody());
        }
        return "";
    }

    private String checkCaptchaService(String body) {
        JSONObject resultJson = new JSONObject(body);
        String cryptoServiceClassName = resultJson.getString("cryptoServiceClassName");
        if ("com.xylink.crypto.sdk.common.DefaultCryptoServiceImpl".equals(cryptoServiceClassName)) {
            return "";
        }
        return body;
    }

    // 重启对应的服务
    public void saveCryptoConfig(CryptoConfig cryptoConfig, HttpMethod method) {
        String cryptType = cryptoConfig.getCryptType();
        if (CRYPTO_CLOSE.equals(cryptType)) {
            handleCryptoCloseState();
            //重启所有服务
            resetAllService();
            return;
        }

        if (CRYPTO_COMMON.equals(cryptType)) {
            try {
                MultipartFile file = cryptoConfig.getFile();
                if (file != null) {
                    uploadCaptchaJar(file, method);
                }
                //JSON转换 String 转换为 Map<String, Map<String, Set<String>>>
                TypeReference<Map<String, Map<String, Set<String>>>> typeRef =
                        new TypeReference<Map<String, Map<String, Set<String>>>>() {};

                // 解析 JSON 到目标类型
                Map<String, Map<String, Set<String>>> resultMap =
                        objectMapper.readValue(cryptoConfig.getConfigData(), typeRef);

                String configData = generate(resultMap);
                Map<String, String> cryptoMap = new HashMap<>();
                cryptoMap.put(APPLICATION_SECURITY_FEATURES, configData);
                cryptoMap.put(APPLICATION_SECURITY_STRATEGY, cryptType);
                k8sService.editConfigmap("all-ip", cryptoMap);
                //重启captcha iauth user-permission basic-management buffet dating vodmanager basic-xyauth
                resetCommonService();

            } catch (Exception e) {
                logger.error("saveCryptoConfig err, {}", e.getMessage());
            }
            return;
        }
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put(APPLICATION_SECURITY_STRATEGY, cryptType);
        k8sService.editConfigmap("all-ip", dataMap);
        k8sService.removeDataFromCM("all-ip", APPLICATION_SECURITY_FEATURES);
        //重启所有服务
        resetAllService();
    }

    private void resetCommonService() {
        k8sService.deletePodByLabelApp("private-captcha");
        k8sService.deletePodByLabelApp("private-userpermission");
        k8sService.deletePodByLabelApp("private-basic-management");
        k8sService.deletePodByLabelApp("private-buffet");
        k8sService.deletePodByLabelApp("private-dating");
        k8sService.deletePodByLabelApp("private-vodmanager");
        k8sService.deletePodByLabelApp("private-basic-xyauth");
        k8sService.deletePodByLabelApp("private-iauth");
    }

    // 重启所有服务
    private void resetAllService() {
        RestartServiceDto allServices = serverNetworkService.getAllServices();
        if(allServices != null) {
            List<DsAndDeploymentDto> restartServices = allServices.getRestartServices();
            serverNetworkService.batchRestartServiceV2New(restartServices);
        }
    }

    /**
     * 关闭商密开关
     */
    private void handleCryptoCloseState() {
        k8sService.removeDataFromCM("all-ip", APPLICATION_SECURITY_FEATURES, APPLICATION_SECURITY_STRATEGY);
    }

    public CryptoConfig getCryptoConfig() {
        Map<String, String> configmap = k8sService.getConfigmap("all-ip");
        String cryptoType = configmap.get(APPLICATION_SECURITY_STRATEGY);
        if ("".equals(cryptoType)
                || cryptoType == null) {
            cryptoType = CRYPTO_CLOSE;
        }
        Map<String, Map<String, Set<String>>> configData = new HashMap<>();
        if (CRYPTO_COMMON.equals(cryptoType)) {
            String cryptoStr = configmap.get(APPLICATION_SECURITY_FEATURES);
            if (StringUtils.isNotEmpty(cryptoStr)) {
                configData = load(cryptoStr);
            }
        }
        CryptoConfig res = null;
        try {
            res = CryptoConfig.builder()
                    .cryptType(cryptoType)
                    .configData(objectMapper.writeValueAsString(configData))
                    .build();
        } catch (Exception e) {
            logger.error("getCryptoConfig err, {}", e.getMessage());
        }

        return res;
    }

    /**
     * 将结构化数据转换为配置字符串
     * @param configData 结构：Map<服务名, Map<功能名, Set<操作组>>>
     * @return 符合规范的配置字符串
     */
    private String generate(Map<String, Map<String, Set<String>>> configData) {
        StringBuilder sb = new StringBuilder();

        configData.forEach((service, funcMap) -> {
            // 构建服务配置块
            sb.append(service).append(">");

            // 构建功能列表
            List<String> funcEntries = new ArrayList<>();
            funcMap.forEach((func, ops) ->
                    funcEntries.add(func + "@" + String.join("|", ops))
            );

            sb.append(String.join(",", funcEntries));
            sb.append("#");
        });

        // 移除末尾多余的#号
        return sb.length() > 0 ? sb.deleteCharAt(sb.length()-1).toString() : "";
    }

    private Map<String, Map<String, Set<String>>> load(String configStr) {
        Map<String, Map<String, Set<String>>> configData = new HashMap<>();
        Arrays.stream(configStr.split("#"))
                .filter(block -> !block.isEmpty())
                .forEach(block -> {
                    String[] serviceParts = block.split(">", 2);
                    if (serviceParts.length != 2) return;

                    String service = serviceParts[0];
                    Map<String, Set<String>> funcMap = new HashMap<>();

                    Arrays.stream(serviceParts[1].split(","))
                            .filter(entry -> !entry.isEmpty())
                            .forEach(entry -> {
                                String[] funcParts = entry.split("@", 2);
                                if (funcParts.length == 2) {
                                    Set<String> ops = new HashSet<>(Arrays.asList(funcParts[1].split("\\|")));
                                    funcMap.put(funcParts[0], ops);
                                }
                            });

                    configData.put(service, funcMap);
                });
        return configData;
    }
}
