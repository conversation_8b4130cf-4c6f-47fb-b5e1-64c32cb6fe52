package com.xylink.manager.service.cluster;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.controller.dto.clusters.ClusterCollection;
import com.xylink.manager.controller.dto.result.MiddleClusterInfo;
import com.xylink.manager.model.em.RedisMode;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.db.JasyptService;
import com.xylink.manager.service.event.AccessAddressChangedEvent;
import com.xylink.util.JDBCUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/7/14 3:33 PM
 */
@Service
public class ClusterServiceImpl implements ClusterService {

    @Value("${clusterInfoUnifyConfiguration}")
    private boolean clusterInfoUnifyConfiguration;

    @Resource
    private K8sService k8sService;

    @Autowired
    private JDBCUtils jdbcUtils;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    @Autowired
    JasyptService jasyptService;

    @Override
    public ClusterCollection query() {
        Map<String, String> dataConfigMap = k8sService.getConfigmap(Constants.CONFIGMAP_PRIVATE_DATA);
        String etcdClusters = dataConfigMap.get("ETCD_CLUSTERS");
        String apiServerClusters = dataConfigMap.get("APISERVER_CLUSTERS");
        ClusterCollection clusterCollection = new ClusterCollection();
        clusterCollection.setEtcdClusters(ipStringToSet(etcdClusters));
        clusterCollection.setApiServerClusters(ipStringToSet(apiServerClusters));
        return clusterCollection;
    }

    @Override
    public MiddleClusterInfo queryMiddleClusterInfo() {
        Map<String, String> allIpMap = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        MiddleClusterInfo info = new MiddleClusterInfo();
        info.setMasterWVip(allIpMap.get(NetworkConstants.MASTER_W_VIP));
        info.setSlaveRVip(allIpMap.get(NetworkConstants.SLAVE_R_VIP));
        info.setRedisIp(allIpMap.get(NetworkConstants.MAIN_REDIS_IP));
        info.setRedisPort(allIpMap.get(NetworkConstants.MAIN_REDIS_PORT));
        info.setRedisMode(RedisMode.getRedisMode(allIpMap.get(NetworkConstants.REDIS_MODE)).getMode());
        String redisPwd = allIpMap.get(NetworkConstants.REDIS_PWD);
        if (StringUtils.isNotBlank(redisPwd)) {
            redisPwd = jasyptService.decrypt(redisPwd);
        }
        info.setRedisPwd(redisPwd);
        info.setEsIp(allIpMap.get(NetworkConstants.ES_IP));
        info.setEsUserName(allIpMap.get(NetworkConstants.ES_USERNAME));
        String esPassword = allIpMap.get(NetworkConstants.ES_PASSWORD);
        if (StringUtils.isNotBlank(esPassword)) {
            esPassword = jasyptService.decrypt(esPassword);
        }
        info.setEsPassword(esPassword);
        info.setMongodbIp(allIpMap.get(NetworkConstants.MONGODB_IP));
        info.setMongoDbUser(allIpMap.get(NetworkConstants.MONGODB_USER));
        String mongodbPassword = allIpMap.get(NetworkConstants.MONGODB_PASSWORD);
        if (StringUtils.isNotBlank(mongodbPassword)) {
            mongodbPassword = jasyptService.decrypt(mongodbPassword);
        }
        info.setMongoDbPassword(mongodbPassword);
        info.setMainDomainName(allIpMap.get(NetworkConstants.MAIN_DOMAIN_NAME));
        info.setMainNginxPort(allIpMap.get(NetworkConstants.MAIN_NGINX_PORT));
        info.setMainNginxSSLPort(allIpMap.get(NetworkConstants.MAIN_NGINX_SSL_PORT));
        info.setMainInternalIp(allIpMap.get(NetworkConstants.MAIN_INNER_IP));
        info.setMainExternalIp(allIpMap.get(NetworkConstants.MAIN_OUTER_IP));
        info.setMainIp(allIpMap.get(NetworkConstants.MAIN_IP));
        return info;
    }

    @Override
    public void saveMiddleClusterInfo(MiddleClusterInfo info) {
        Map<String, String> allIpMap = new HashMap<>();
        if (RedisMode.SINGLE.getMode().equals(info.getRedisMode())) {
            //redis单机时，需要填写端口，默认为6379
            allIpMap.put(NetworkConstants.MAIN_REDIS_PORT, StringUtils.isBlank(info.getRedisPort()) ? NetworkConstants.DEFAULT_MAIN_REDIS_PORT : info.getRedisPort());
        }
        String redisPwd = StringUtils.isBlank(info.getRedisPwd()) ? NetworkConstants.REDIS_PWD_DEFAULT : info.getRedisPwd();
        allIpMap.put(NetworkConstants.REDIS_PWD, jasyptService.encrypt(redisPwd));
        allIpMap.put(NetworkConstants.REDIS_MODE, info.getRedisMode());
        //数据库ip修改
        allIpMap.put(NetworkConstants.MASTER_W_VIP, info.getMasterWVip());
        allIpMap.put(NetworkConstants.SLAVE_R_VIP, info.getSlaveRVip());
        allIpMap.put(NetworkConstants.ES_USERNAME, StringUtils.isBlank(info.getEsUserName()) ? NetworkConstants.ES_USERNAME_DEFAULT : info.getEsUserName());
        String esPassword = StringUtils.isBlank(info.getEsPassword()) ? NetworkConstants.ES_PASSWORD_DEFAULT : info.getEsPassword();
        allIpMap.put(NetworkConstants.ES_PASSWORD, jasyptService.encrypt(esPassword));

        allIpMap.put(NetworkConstants.MONGODB_USER, StringUtils.isBlank(info.getMongoDbUser()) ? NetworkConstants.MONGODB_USER_DEFAULT : info.getMongoDbUser());
        String mongodbPassword = StringUtils.isBlank(info.getMongoDbPassword()) ? NetworkConstants.MONGODB_PASSWORD_DEFAULT : info.getMongoDbPassword();
        allIpMap.put(NetworkConstants.MONGODB_PASSWORD, jasyptService.encrypt(mongodbPassword));


        if (StringUtils.isNotBlank(info.getRedisIp())) {
            allIpMap.put(NetworkConstants.MAIN_REDIS_IP, info.getRedisIp());
        }
        if (StringUtils.isNotBlank(info.getMasterWVip())) {
            allIpMap.put(NetworkConstants.DATABASE_IP, info.getMasterWVip());
            allIpMap.put(NetworkConstants.STATIS_DATABASE_IP, info.getMasterWVip());
            allIpMap.put(NetworkConstants.UAA_DATABASE_IP, info.getMasterWVip());
        }
        if (StringUtils.isNotBlank(info.getEsIp())) {
            allIpMap.put(NetworkConstants.ES_IP, info.getEsIp());
        }
        if (StringUtils.isNotBlank(info.getMongodbIp())) {
            allIpMap.put(NetworkConstants.MONGODB_IP, info.getMongodbIp());
        }

        k8sService.editConfigmap(Constants.CONFIGMAP_ALLIP, allIpMap);
        otherProcess(info);
        applicationEventPublisher.publishEvent(new AccessAddressChangedEvent(this,"Edit cluster info."));
    }

    @Override
    public void saveOrUpdate(String apiServerClusters, String etcdClusters) {
        Map<String, String> dataConfigMap = k8sService.getConfigmap(Constants.CONFIGMAP_PRIVATE_DATA);
        dataConfigMap.put("ETCD_CLUSTERS", etcdClusters);
        dataConfigMap.put("APISERVER_CLUSTERS", apiServerClusters);
        k8sService.editConfigmap(Constants.CONFIGMAP_PRIVATE_DATA, dataConfigMap);
    }

    private Set<String> ipStringToSet(String ips) {
        Set<String> ipSet = new HashSet<>();
        if (StringUtils.isNotBlank(ips)) {
            ipSet = Arrays.stream(ips.split(",")).collect(Collectors.toSet());
        }
        return ipSet;
    }

    /**
     * 获取集群统一配置标注，如果为true则代表es、redis、mongodb、数据库都使用三方提供ip，且主数据库、statis数据库、uaa数据库都使用同一个数据库（针对邮储项目）
     */
    public boolean getClusterInfoUnifyConfiguration() {
        return clusterInfoUnifyConfiguration;
    }

    /**
     * @Description: 邮储main节点保存逻辑迁移
    **/
    public void otherProcess(MiddleClusterInfo info){
        Map<String, String> allIpMap = new HashMap<>();
        allIpMap.put(NetworkConstants.MAIN_DOMAIN_NAME, StringUtils.isBlank(info.getMainDomainName()) ? info.getMainExternalIp() : info.getMainDomainName());
        allIpMap.put(NetworkConstants.MAIN_NGINX_PORT, StringUtils.isBlank(info.getMainNginxPort()) ? "80" : info.getMainNginxPort());
        allIpMap.put(NetworkConstants.MAIN_NGINX_SSL_PORT, StringUtils.isBlank(info.getMainNginxSSLPort()) ? "443" : info.getMainNginxSSLPort());
        allIpMap.put(NetworkConstants.MAIN_INNER_IP, info.getMainInternalIp());
        allIpMap.put(NetworkConstants.MAIN_OUTER_IP, info.getMainInternalIp());
        allIpMap.put(NetworkConstants.MAIN_IP, info.getMainIp());
        k8sService.editConfigmap(Constants.CONFIGMAP_ALLIP, allIpMap);
        //内外网探测
        jdbcUtils.asyncConfigureNetworkDetection(info.getMainInternalIp(),info.getMainExternalIp(),info.getMainNginxPort());
    }

}
