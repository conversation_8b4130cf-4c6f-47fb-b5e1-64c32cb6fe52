package com.xylink.manager.service.cluster;

import com.xylink.manager.controller.dto.clusters.ClusterCollection;
import com.xylink.manager.controller.dto.result.MiddleClusterInfo;

/**
 * <AUTHOR>
 * @since 2023/7/14 3:02 PM
 */
public interface ClusterService {
    /**
     * query
     *
     * @return
     */
    ClusterCollection query();

    MiddleClusterInfo queryMiddleClusterInfo();

    void saveMiddleClusterInfo(MiddleClusterInfo info);

    /**
     * save or update
     *
     * @param apiServerClusters
     * @param etcdClusters
     */
    void saveOrUpdate(String apiServerClusters, String etcdClusters);

    /**
     * 获取集群统一配置标注，如果为true则代表es、redis、mongodb、数据库都使用三方提供ip，且主数据库、statis数据库、uaa数据库都使用同一个数据库（针对邮储项目）
     */
    boolean getClusterInfoUnifyConfiguration();
}
