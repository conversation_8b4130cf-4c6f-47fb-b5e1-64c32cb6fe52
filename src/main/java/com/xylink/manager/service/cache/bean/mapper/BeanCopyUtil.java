package com.xylink.manager.service.cache.bean.mapper;

import com.xylink.manager.service.cache.bean.ConfigMapCache;
import com.xylink.manager.service.cache.bean.DeploymentCache;
import com.xylink.manager.service.cache.bean.NodeCache;
import com.xylink.manager.service.cache.bean.PodCache;
import io.fabric8.kubernetes.api.model.ConfigMap;
import io.fabric8.kubernetes.api.model.Node;
import io.fabric8.kubernetes.api.model.Pod;
import io.fabric8.kubernetes.api.model.apps.Deployment;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface BeanCopyUtil {
    BeanCopyUtil INSTANCE = Mappers.getMapper(BeanCopyUtil.class);

    NodeCache node2Cache(Node node);

    PodCache pod2Cache(Pod pod);

    ConfigMapCache configMap2Cache(ConfigMap configMap);

    DeploymentCache deployment2Cache(Deployment deployment);
}
