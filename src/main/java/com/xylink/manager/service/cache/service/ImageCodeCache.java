package com.xylink.manager.service.cache.service;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2022/7/26 3:17 下午
 */
@Service
public class ImageCodeCache {
    public static Cache<String, String> imageCodeCache = CacheBuilder.newBuilder()
            .expireAfterWrite(30, TimeUnit.SECONDS)
            .maximumSize(1000L)
            .build();

    public static Cache<String, String> imageCodeCheckSuccessSessionCache = CacheBuilder.newBuilder()
            .expireAfterWrite(300, TimeUnit.SECONDS)
            .maximumSize(1000L)
            .build();

    public String get(String key) {
        return imageCodeCache.getIfPresent(key);
    }

    public void set(String key, String obj){
        imageCodeCache.put(key, obj);
    }

    public void delete(String key){
        imageCodeCache.invalidate(key);
    }

    public boolean exists(String key) {
        String value = get(key);
        return StringUtils.isNotBlank(value);
    }

    public void checkSuccess(String session,String token){
        imageCodeCheckSuccessSessionCache.put(session,token);
    }

    public String getTokenFromCheckSuccessCache(String session){
        return imageCodeCheckSuccessSessionCache.getIfPresent(session);
    }

    public void deleteTokenFromCheckSuccessCache(String session){
         imageCodeCheckSuccessSessionCache.invalidate(session);
    }
}
