package com.xylink.manager.service.cache.bean;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
public class ObjectMetaCache {
    private String name;
    private String namespace;
    private Map<String, String> labels;
    private List<OwnerReferenceCache> ownerReferences = new ArrayList<>();
    private String creationTimestamp;
    private String deletionTimestamp;
}
