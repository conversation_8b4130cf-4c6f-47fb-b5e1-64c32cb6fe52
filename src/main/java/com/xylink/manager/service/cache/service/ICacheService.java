package com.xylink.manager.service.cache.service;

import com.xylink.config.Constants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.service.cache.bean.ConfigMapCache;
import com.xylink.manager.service.cache.bean.DeploymentCache;
import com.xylink.manager.service.cache.bean.NodeCache;
import com.xylink.manager.service.cache.bean.PodCache;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.List;
import java.util.Objects;

public interface ICacheService {
    void refreshAll();

    void refreshNodeCache();

    void refreshDeploymentCache();

    void refreshPodCache();

    void refreshAllConfigMapCache();

    void refreshConfigMapCache(@Nonnull String cmName);

    @Nonnull
    List<NodeCache> cacheNodeList();

    @Nullable
    NodeCache cacheNodeByName(@Nonnull String name);

    @Nonnull
    default NodeCache cacheMainNode() {
        return cacheNodeList().stream()
                .filter(item -> (Constants.NODETYPE_MAIN.equals(item.getMetadata().getLabels().get(Constants.TYPE)) || Constants.NODE_TYPE_COMMON_MAIN.equals(item.getMetadata().getLabels().get(Constants.TYPE))))
                .findFirst()
                .orElseThrow(() -> new ServerException(ErrorStatus.NOT_FOUND_NODE_TYPE));
    }

    @Nonnull
    List<DeploymentCache> cacheDeploymentList();

    @Nullable
    DeploymentCache cacheDeploymentByName(@Nonnull String name);

    @Nonnull
    List<PodCache> cachePodList();

    @Nullable
    PodCache cachePodByName(@Nonnull String name);

    @Nullable
    ConfigMapCache cacheConfigMapByName(@Nonnull String name);

    @Nonnull
    default ConfigMapCache cacheConfigMapAllIp() {
        return Objects.requireNonNull(cacheConfigMapByName(Constants.CONFIGMAP_ALLIP));
    }
}
