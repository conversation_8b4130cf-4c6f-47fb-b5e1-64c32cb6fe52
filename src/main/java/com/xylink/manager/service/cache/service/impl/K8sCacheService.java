package com.xylink.manager.service.cache.service.impl;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.xylink.manager.service.base.K8sClientBuilder;
import com.xylink.manager.service.cache.bean.ConfigMapCache;
import com.xylink.manager.service.cache.bean.DeploymentCache;
import com.xylink.manager.service.cache.bean.NodeCache;
import com.xylink.manager.service.cache.bean.PodCache;
import com.xylink.manager.service.cache.constants.ConstantsCacheKey;
import com.xylink.manager.service.cache.service.ICacheService;
import com.xylink.manager.service.watch.k8s.*;
import com.xylink.util.NamedThreadFactory;
import com.xylink.util.SpringBeanUtil;
import io.fabric8.kubernetes.api.model.*;
import io.fabric8.kubernetes.api.model.apps.Deployment;
import io.fabric8.kubernetes.api.model.apps.DeploymentList;
import lombok.extern.slf4j.Slf4j;
import org.ehcache.impl.internal.concurrent.ConcurrentHashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@DependsOn("springBeanUtil")
public class K8sCacheService implements ICacheService {
    private static final ExecutorService executor = Executors.newSingleThreadExecutor(new NamedThreadFactory("k8s-cache-service"));

    public static final LoadingCache<String, Map<String, NodeCache>> nodeCacheStore = buildCacheStore(
            ConstantsCacheKey.CACHE_KEY_NODE_LIST,
            m -> SpringBeanUtil.getBean(K8sNodeWatcherDataLoader.class).reloadAllData(m)
    );
    public static final LoadingCache<String, Map<String, DeploymentCache>> deploymentCacheStore = buildCacheStore(
            ConstantsCacheKey.CACHE_KEY_DEPLOYMENT_LIST,
            m -> SpringBeanUtil.getBean(K8sDeploymentWatcherDataLoader.class).reloadAllData(m)
    );
    public static final LoadingCache<String, Map<String, PodCache>> podCacheStore = buildCacheStore(
            ConstantsCacheKey.CACHE_KEY_POD_LIST,
            m -> SpringBeanUtil.getBean(K8sPodWatcherDataLoader.class).reloadAllData(m)
    );
    public static final LoadingCache<String, ConfigMapCache> configMapCacheStore = buildMapCacheStore(
            50,
            ConstantsCacheKey.CACHE_KEY_CONFIGMAP_LIST,
            m -> {
                K8sConfigMapWatcherDataLoader dataLoader = SpringBeanUtil.getBean(K8sConfigMapWatcherDataLoader.class);
                return dataLoader.getSingleConfigMap(m);
            }
    );

    @Autowired
    private K8sClientBuilder k8sClientBuilder;
    @Autowired
    private K8sNodeWatcherDataLoader k8sNodeWatcherDataLoader;
    @Autowired
    private K8sDeploymentWatcherDataLoader k8sDeploymentWatcherDataLoader;
    @Autowired
    private K8sPodWatcherDataLoader k8sPodWatcherDataLoader;
    @Autowired
    private K8sConfigMapWatcherDataLoader k8sConfigMapWatcherDataLoader;

    @PostConstruct
    public void init() {
        //等待30秒再启动任务
        int waitSeconds = 30;
        K8sClientBuilder.executor.schedule(new K8sPodWatcher(k8sClientBuilder.getClient(), k8sPodWatcherDataLoader), waitSeconds, TimeUnit.SECONDS);
        K8sClientBuilder.executor.schedule(new K8sConfigMapWatcher(k8sClientBuilder.getClient(), k8sConfigMapWatcherDataLoader), waitSeconds, TimeUnit.SECONDS);
        K8sClientBuilder.executor.schedule(new K8sDeploymentWatcher(k8sClientBuilder.getClient(), k8sDeploymentWatcherDataLoader), waitSeconds, TimeUnit.SECONDS);
        K8sClientBuilder.executor.schedule(new K8sNodeWatcher(k8sClientBuilder.getClient(), k8sNodeWatcherDataLoader), waitSeconds, TimeUnit.SECONDS);
    }

    @Override
    public void refreshAll() {
        nodeCacheStore.refresh(ConstantsCacheKey.CACHE_KEY_NODE_LIST);
        deploymentCacheStore.refresh(ConstantsCacheKey.CACHE_KEY_DEPLOYMENT_LIST);
        podCacheStore.refresh(ConstantsCacheKey.CACHE_KEY_POD_LIST);
        //cm的缓存是一个个的，刷新全部的时候不处理它
    }

    @Override
    public void refreshNodeCache() {
        nodeCacheStore.refresh(ConstantsCacheKey.CACHE_KEY_NODE_LIST);
    }

    @Override
    public void refreshDeploymentCache() {
        deploymentCacheStore.refresh(ConstantsCacheKey.CACHE_KEY_DEPLOYMENT_LIST);
    }

    @Override
    public void refreshPodCache() {
        podCacheStore.refresh(ConstantsCacheKey.CACHE_KEY_POD_LIST);
    }

    @Override
    public void refreshAllConfigMapCache() {
        configMapCacheStore.invalidateAll();
    }

    @Override
    public void refreshConfigMapCache(@Nonnull String cmName) {
        configMapCacheStore.refresh(cmName);
    }

    @Nonnull
    @Override
    public List<NodeCache> cacheNodeList() {
        List<NodeCache> cacheList = new ArrayList<>();
        try {
            Map<String, NodeCache> cacheMap = nodeCacheStore.get(ConstantsCacheKey.CACHE_KEY_NODE_LIST);
            cacheList.addAll(cacheMap.values());
        } catch (Exception e) {
            log.warn("nodeCacheStore get error", e);
        }
        if (!CollectionUtils.isEmpty(cacheList)) {
            return cacheList;
        }
        NodeList list = k8sClientBuilder.getClient().nodes().list();
        if (list != null && list.getItems() != null) {
            cacheList = list.getItems()
                    .parallelStream()
                    .map(k8sNodeWatcherDataLoader::mapToCache)
                    .collect(Collectors.toList());
            //异步刷新缓存
            executor.submit(() -> nodeCacheStore.refresh(ConstantsCacheKey.CACHE_KEY_NODE_LIST));
        }
        return cacheList;
    }

    @Nullable
    @Override
    public NodeCache cacheNodeByName(@Nonnull String name) {
        NodeCache cache = null;
        try {
            Map<String, NodeCache> cacheMap = nodeCacheStore.get(ConstantsCacheKey.CACHE_KEY_NODE_LIST);
            cache = cacheMap.get(name);
        } catch (Exception e) {
            log.warn("nodeCacheStore get error", e);
        }
        if (cache == null) {
            Node data = k8sClientBuilder.getClient().nodes().withName(name).get();
            if (data != null) {
                cache = k8sNodeWatcherDataLoader.mapToCache(data);
                //异步刷新缓存
                executor.submit(() -> nodeCacheStore.refresh(ConstantsCacheKey.CACHE_KEY_NODE_LIST));
            }
        }
        return cache;
    }

    @Nonnull
    @Override
    public List<DeploymentCache> cacheDeploymentList() {
        List<DeploymentCache> cacheList = new ArrayList<>();
        try {
            Map<String, DeploymentCache> cacheMap = deploymentCacheStore.get(ConstantsCacheKey.CACHE_KEY_DEPLOYMENT_LIST);
            cacheList.addAll(cacheMap.values());
        } catch (Exception e) {
            log.warn("deploymentCacheStore get error", e);
        }
        if (!CollectionUtils.isEmpty(cacheList)) {
            return cacheList;
        }
        DeploymentList list = k8sClientBuilder.getClient().apps().deployments().list();
        if (list != null && list.getItems() != null) {
            cacheList = list.getItems()
                    .parallelStream()
                    .map(k8sDeploymentWatcherDataLoader::mapToCache)
                    .collect(Collectors.toList());
            //异步刷新缓存
            executor.submit(() -> deploymentCacheStore.refresh(ConstantsCacheKey.CACHE_KEY_DEPLOYMENT_LIST));
        }
        return cacheList;
    }

    @Nullable
    @Override
    public DeploymentCache cacheDeploymentByName(@Nonnull String name) {
        DeploymentCache cache = null;
        try {
            Map<String, DeploymentCache> cacheMap = deploymentCacheStore.get(ConstantsCacheKey.CACHE_KEY_DEPLOYMENT_LIST);
            cache = cacheMap.get(name);
        } catch (Exception e) {
            log.warn("deploymentCacheStore get error", e);
        }
        if (cache == null) {
            Deployment data = k8sClientBuilder.getClient().apps().deployments().withName(name).get();
            if (data != null) {
                cache = k8sDeploymentWatcherDataLoader.mapToCache(data);
                //异步刷新缓存
                executor.submit(() -> deploymentCacheStore.refresh(ConstantsCacheKey.CACHE_KEY_DEPLOYMENT_LIST));
            }
        }
        return cache;
    }

    @Nonnull
    @Override
    public List<PodCache> cachePodList() {
        List<PodCache> cacheList = new ArrayList<>();
        try {
            Map<String, PodCache> cacheMap = podCacheStore.get(ConstantsCacheKey.CACHE_KEY_POD_LIST);
            cacheList.addAll(cacheMap.values());
        } catch (Exception e) {
            log.warn("podCacheStore get error", e);
        }
        if (!CollectionUtils.isEmpty(cacheList)) {
            return cacheList;
        }
        PodList list = k8sClientBuilder.getClient().pods().list();
        if (list != null && list.getItems() != null) {
            cacheList = list.getItems()
                    .parallelStream()
                    .map(k8sPodWatcherDataLoader::mapToCache)
                    .collect(Collectors.toList());
            //异步刷新缓存
            executor.submit(() -> podCacheStore.refresh(ConstantsCacheKey.CACHE_KEY_POD_LIST));
        }
        return cacheList;
    }

    @Nullable
    @Override
    public PodCache cachePodByName(@Nonnull String name) {
        PodCache cache = null;
        try {
            Map<String, PodCache> cacheMap = podCacheStore.get(ConstantsCacheKey.CACHE_KEY_POD_LIST);
            cache = cacheMap.get(name);
        } catch (Exception e) {
            log.warn("podCacheStore get error", e);
        }
        if (cache == null) {
            Pod data = k8sClientBuilder.getClient().pods().withName(name).get();
            if (data != null) {
                cache = k8sPodWatcherDataLoader.mapToCache(data);
                //异步刷新缓存
                executor.submit(() -> podCacheStore.refresh(ConstantsCacheKey.CACHE_KEY_POD_LIST));
            }
        }
        return cache;
    }

    @Nullable
    @Override
    public ConfigMapCache cacheConfigMapByName(@Nonnull String name) {
        ConfigMapCache cache = null;
        try {
            cache = configMapCacheStore.get(name);
        } catch (Exception e) {
            log.warn("configMapCacheStore get error", e);
        }
        if (cache == null) {
            ConfigMap data = k8sClientBuilder.getClient().configMaps().withName(name).get();
            if (data != null) {
                cache = k8sConfigMapWatcherDataLoader.mapToCache(data);
                //异步刷新缓存
                executor.submit(() -> configMapCacheStore.refresh(name));
            }
        }
        return cache;
    }

    private static <XY> LoadingCache<String, Map<String, XY>> buildCacheStore(@Nonnull String cacheName,
                                                                              @Nonnull Consumer<Map<String, XY>> dataLoader) {
        return CacheBuilder.newBuilder()
                .expireAfterWrite(1, TimeUnit.DAYS)
                .refreshAfterWrite(1, TimeUnit.HOURS)
                .build(CacheLoader.asyncReloading(new CacheLoader<String, Map<String, XY>>() {
                    @Nonnull
                    @Override
                    public Map<String, XY> load(@Nonnull String key) {
                        Map<String, XY> dataMap = new ConcurrentHashMap<>();
                        dataLoader.accept(dataMap);
                        return dataMap;
                    }
                }, Executors.newSingleThreadExecutor(new NamedThreadFactory("k8s-watcher-" + cacheName + "-reload"))));
    }

    private static <XY> LoadingCache<String, XY> buildMapCacheStore(int maxSize,
                                                                    @Nonnull String cacheName,
                                                                    @Nonnull Function<String, XY> dataLoader) {
        return CacheBuilder.newBuilder()
                .maximumSize(maxSize)
                .expireAfterWrite(1, TimeUnit.DAYS)
                .refreshAfterWrite(1, TimeUnit.HOURS)
                .build(CacheLoader.asyncReloading(new CacheLoader<String, XY>() {
                    @Nonnull
                    @Override
                    public XY load(@Nonnull String key) {
                        return dataLoader.apply(key);
                    }
                }, Executors.newSingleThreadExecutor(new NamedThreadFactory("k8s-watcher-" + cacheName + "-reload"))));
    }
}
