package com.xylink.manager.service.develop;

import com.xylink.config.Constants;
import com.xylink.manager.model.common.Page;
import com.xylink.manager.model.common.Pageable;
import com.xylink.manager.model.develop.DmcuDto;
import com.xylink.manager.service.base.K8sService;
import com.xylink.util.JDBCUtils;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Service
public class DmcuManagerService {

    private final static Logger logger = LoggerFactory.getLogger(DmcuManagerService.class);


    @Autowired
    private JDBCUtils jdbcUtils;
    @Autowired
    private K8sService k8sService;

    public List<DmcuDto> listDmcuConfig() {
        return jdbcUtils.listDmcuConfig();
    }


    public void addDmcuConfig(DmcuDto dmcuDto) {
        jdbcUtils.addDmcuConfig(dmcuDto);
    }

    public void updateDmcuConfig(DmcuDto dmcuDto) {
        jdbcUtils.updateDmcuConfig(dmcuDto);
    }

    public void deleteDmcuConfig(DmcuDto dmcuDto) {
        jdbcUtils.deleteDmcuConfig(dmcuDto);
    }

    public List<Integer> listDmcuSiteCode() {
        Map<String, String> allDmcu = k8sService.getConfigmap(Constants.CONFIGMAP_DMCU);
        if (CollectionUtils.isEmpty(allDmcu)) {
            return Collections.emptyList();
        }

        List<Integer> dmcuList = Lists.newArrayList();

        for (Map.Entry<String, String> entry : allDmcu.entrySet()) {
            try{
                if (entry.getKey().endsWith("-SITECODE") && entry.getValue().split("-").length == 2) {
                    String[] ary = entry.getValue().split("-");
                    dmcuList.add(Integer.parseInt(ary[0]));
                }
            }catch (Exception e) {
                logger.warn("listDmcuSiteCode error, key : {}, value : {}", entry.getKey(), entry.getValue());
            }
        }

        return new ArrayList<>(new HashSet<>(dmcuList));
    }


    public Page<DmcuDto> pageDmcuConfig(String key, Pageable pageable) {
        long total = jdbcUtils.countDmcuConfig(key);
        if (total > 0) {
            List<DmcuDto> data = jdbcUtils.pageDmcuConfig(key, (pageable.getPageNumber() -1 )* pageable.getPageSize(), pageable.getPageSize());
            return new Page<>(pageable.getPageNumber(), pageable.getPageSize(), total, data);
        }
        return Page.emptyPage(pageable);
    }
}
