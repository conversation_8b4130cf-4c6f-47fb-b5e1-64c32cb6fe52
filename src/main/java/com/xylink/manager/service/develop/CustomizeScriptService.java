package com.xylink.manager.service.develop;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xylink.config.NetworkConstants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.config.mapper.JsonMapper;
import com.xylink.config.util.JsonUtil;
import com.xylink.manager.model.develop.CustomizeScriptDto;
import com.xylink.manager.model.develop.RunScriptDto;
import com.xylink.manager.model.develop.WebhookDto;
import com.xylink.manager.service.base.K8sService;
import com.xylink.util.Ipv6Util;
import com.xylink.util.WebHookHttpUtil;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;

@Service
public class CustomizeScriptService {
    private final static Logger logger = LoggerFactory.getLogger(CustomizeScriptService.class);
    private static final String CUSTOMIZE_SCRIPT_CM_NAME = "private-customize-webhook";
    private static final String DEFAULT_SCRIPT_CM_NAME = "private-webhook";

    @Value("${base.dir}")
    private String baseDir;
    @Autowired
    private K8sService k8sService;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private ObjectMapper objectMapper;

    public void addCustomizeScript(CustomizeScriptDto scriptDto) {
        addCustomizeScriptCm(scriptDto);
        addWebhookCm(scriptDto);
    }

    public void removeCustomizeScript(String name) {
        // 删除描述
        removeCustomizeScriptCm(name);
        // 删除webhook json文件
        removeWebhookCm(name);
    }

    public CustomizeScriptDto getCustomizeScript(String name) {
        Map<String, String> config = k8sService.getConfigmap(CUSTOMIZE_SCRIPT_CM_NAME);
        String descStr = config.get("all-customize-script-desc");
        if (StringUtils.isBlank(descStr)) {
            throw new ServerException(ErrorStatus.NO_EXIST_CUSTOMIZE_SCRIPT);
        }

        Map<String, CustomizeScriptDto> descMap = objectMapper.convertValue(descStr, new TypeReference<Map<String, CustomizeScriptDto>>() {
        });
        if (!descMap.containsKey(name)) {
            throw new ServerException(ErrorStatus.NO_EXIST_CUSTOMIZE_SCRIPT);
        }

        CustomizeScriptDto scriptDto = descMap.get(name);
        scriptDto.setContent(config.get(name));
        return scriptDto;
    }

    public List<CustomizeScriptDto> listCustomizeScript() {
        Map<String, String> config = k8sService.getConfigmap(CUSTOMIZE_SCRIPT_CM_NAME);
        String descStr = config.get("all-customize-script-desc");
        if (StringUtils.isBlank(descStr)) {
            return Collections.emptyList();
        }

        Map<String, CustomizeScriptDto> descMap = objectMapper.convertValue(descStr, new TypeReference<Map<String, CustomizeScriptDto>>() {
        });

        List<CustomizeScriptDto> scriptDtoList = Lists.newArrayList();
        scriptDtoList.addAll(descMap.values());
        return scriptDtoList;
    }

    public String runCustomizeScript(RunScriptDto scriptDto) {
        String url;
        for(String nodeIp: scriptDto.getNodeIps()) {
            url = "http://" + Ipv6Util.handlerIpv6Addr(nodeIp) + ":" + NetworkConstants.WEB_HOOK + "/hooks/"+ getId(scriptDto.getName());
            WebHookHttpUtil.getStringByPostWebHookResult(restTemplate, url, Strings.EMPTY);
        }
        return "SUCCESS";
    }

    /**
     * 更新webhoo.json
     */
    private void addWebhookCm(CustomizeScriptDto scriptDto) {
        Map<String, String> config = k8sService.getConfigmap(DEFAULT_SCRIPT_CM_NAME);
        config.put("webhook.json", addWebhookJson(config.get("webhook.json"), scriptDto));
        k8sService.editConfigmap(DEFAULT_SCRIPT_CM_NAME, config);
    }

    private void removeWebhookCm(String name) {
        Map<String, String> config = k8sService.getConfigmap(DEFAULT_SCRIPT_CM_NAME);
        config.put("webhook.json", removeWebhookJson(config.get("webhook.json"), name));
        k8sService.editConfigmap(DEFAULT_SCRIPT_CM_NAME, config);
    }

    /**
     * 更新cm
     */
    private void addCustomizeScriptCm(CustomizeScriptDto scriptDto) {
        Map<String, String> config = k8sService.getConfigmap(CUSTOMIZE_SCRIPT_CM_NAME);

        config.put("all-customize-script-desc", addScriptDesc(config.get("all-customize-script-desc"), scriptDto));
        config.put(scriptDto.getName(), scriptDto.getContent());

        k8sService.editConfigmap(CUSTOMIZE_SCRIPT_CM_NAME, config);
    }

    /**
     * 删除自定义脚本描述
     */
    private void removeCustomizeScriptCm(String name) {
        Map<String, String> config = k8sService.getConfigmap(CUSTOMIZE_SCRIPT_CM_NAME);
        config.remove(name);
        config.put("all-customize-script-desc", removeScriptDesc(config.get("all-customize-script-desc"), name));
        k8sService.replaceConfigmap(CUSTOMIZE_SCRIPT_CM_NAME, config);
    }

    /**
     * 更新自定义脚本描述信息
     */
    private String addScriptDesc(String old, CustomizeScriptDto scriptDto) {
        Map<String, CustomizeScriptDto> descMap;
        if (StringUtils.isBlank(old)) {
            descMap = new HashMap<>();
        } else {
            descMap = JsonUtil.parseJson(old, Map.class);
        }
        descMap.put(scriptDto.getName(), scriptDto);
        return JsonUtil.toJson(descMap);
    }

    private String removeScriptDesc(String old, String name) {
        Map<String, CustomizeScriptDto> descMap;
        if (StringUtils.isBlank(old)) {
            descMap = new HashMap<>();
        } else {
            descMap = JsonUtil.parseJson(old, Map.class);
            descMap.remove(name);
        }
        return JsonUtil.toJson(descMap);
    }

    /**
     * 更新webhook.json文件信息
     */
    private String addWebhookJson(String old, CustomizeScriptDto scriptDto) {
        List<WebhookDto> webhookDtos;
        if (StringUtils.isBlank(old)) {
            webhookDtos = new ArrayList<>();
        } else {
            JsonMapper mapper = JsonMapper.nonEmptyMapper();
            webhookDtos = mapper.fromJson(old, mapper.contructCollectionType(List.class, WebhookDto.class));
            webhookDtos.removeIf(webhookDto -> webhookDto.getId().equals(getId(scriptDto.getName())));
        }
        //由于 WebhookDto 移除 commandWorkingDirectory 默认值，此处根据manager运行环境补全该属性
        webhookDtos.forEach(webhookDto -> webhookDto.setCommandWorkingDirectory(""+baseDir+"/logagent/customize"));
        webhookDtos.add(createWebhookDto(scriptDto));
        return JsonMapper.nonEmptyMapper().toJson(webhookDtos);
    }

    private String removeWebhookJson(String old, String name) {
        List<WebhookDto> webhookDtos;
        if (StringUtils.isBlank(old)) {
            webhookDtos = new ArrayList<>();
        } else {
            JsonMapper mapper = JsonMapper.nonEmptyMapper();
            webhookDtos = mapper.fromJson(old, mapper.contructCollectionType(List.class, WebhookDto.class));
            webhookDtos.removeIf(webhookDto -> webhookDto.getId().equals(getId(name)));
        }
        //由于 WebhookDto 移除 commandWorkingDirectory 默认值，此处根据manager运行环境补全该属性
        webhookDtos.forEach(webhookDto -> webhookDto.setCommandWorkingDirectory(""+baseDir+"/logagent/customize"));
        return JsonMapper.nonEmptyMapper().toJson(webhookDtos);
    }

    private String getId(String name) {
        if (name.indexOf(".sh") > 0) {
            return name.substring(0, name.indexOf(".sh"));
        } else {
            return name;
        }
    }

    private WebhookDto createWebhookDto(CustomizeScriptDto scriptDto) {
        WebhookDto webhookDto = new WebhookDto();
        webhookDto.setId(getId(scriptDto.getName()));
        webhookDto.setExecuteCommand(""+baseDir+"/logagent/customize/" + scriptDto.getName());
        webhookDto.setCommandWorkingDirectory(""+baseDir+"/logagent/customize");
        if (Objects.nonNull(scriptDto.getParamNum()) && scriptDto.getParamNum() > 0) {
            webhookDto.setPassArgumentsToCommand(createCommand(scriptDto.getParamNum()));
        }
        if (Objects.nonNull(scriptDto.getSync()) && scriptDto.getSync()) {
            webhookDto.setIncludeCommandOutputInResponse(true);
            webhookDto.setIncludeCommandOutputInResponseOnError(true);
        }

        return webhookDto;
    }

    private List<WebhookDto.PassArgumentsToCommand> createCommand(int num) {
        List<WebhookDto.PassArgumentsToCommand> commands = Lists.newArrayList();
        for (int i = 0; i < num; i++) {
            WebhookDto.PassArgumentsToCommand command = new WebhookDto.PassArgumentsToCommand();
            command.setName("p" + i);
            commands.add(command);
        }
        return commands;
    }
}
