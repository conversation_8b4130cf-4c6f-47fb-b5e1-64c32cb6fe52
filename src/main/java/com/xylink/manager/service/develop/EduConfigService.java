package com.xylink.manager.service.develop;

import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.model.CurriculumConfigInfo;
import com.xylink.manager.model.EduManageEnterprise;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.base.K8sSvcService;
import com.xylink.util.JDBCUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Objects;

@Service
public class EduConfigService {

    private final static Logger logger = LoggerFactory.getLogger(EduConfigService.class);

    @Autowired
    private JDBCUtils jdbcUtils;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private K8sService k8sService;
    @Autowired
    private K8sSvcService k8sSvcService;

    public void openTrainEntry() {
        trainEntry(Boolean.TRUE);
    }

    public void closeTrainEntry() {
        trainEntry(Boolean.FALSE);
    }

    private void trainEntry(boolean isOpen) {
        EduManageEnterprise eme = jdbcUtils.getEduManageEnterprise();
        if (Objects.isNull(eme)) {
            throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
        }

        try {
            String mainIp = k8sSvcService.getMainNodeInternalIPNotNull();
            String url = "http://" + mainIp + ":11111/api/rest/internal/v1/en/edu/manage/enterprise";
            HttpEntity<String> entity = new HttpEntity<>(initBody(eme, isOpen), initHeader());
            ResponseEntity<String> exchange = restTemplate.exchange(url, HttpMethod.PUT, entity, String.class);
            logger.info(exchange.getStatusCode().toString());
            if (!exchange.getStatusCode().is2xxSuccessful()) {
                throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
            }
        } catch (RestClientException e) {
            logger.error("openTrainEntry error", e);
            throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
        }
    }


    private String initBody(EduManageEnterprise eme, boolean isOpen) {
        ObjectNode object = JsonNodeFactory.instance.objectNode();
        object.put("modelId", eme.getModelId());
        object.put("id", eme.getId());
        object.put("openResource", eme.getOpenResource() > 0);
        object.put("enterpriseId", eme.getEnterpriseId());
        object.put("clientEduEntry", isOpen ? 1 : 0);
        object.put("platformId", eme.getPlatformId());
        object.put("host", eme.getHost());
        object.put("operatorId", -1);
        return object.toString();
    }

    private HttpHeaders initHeader() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        return headers;
    }

    public List<CurriculumConfigInfo> getEduCurriculumConfig(String configKey){
        if (StringUtils.isBlank(configKey)){
            return jdbcUtils.getEduCurriculumConfigs();
        }else{
            return jdbcUtils.getEduCurriculumConfig(configKey);
        }
    }

    /**
     * 教育模式切换
     *
     * @param modelId
     */
    public void editEduModelId(long modelId) {
        jdbcUtils.editEduModelId(modelId);
        eduModeIdChangedNotifyByRemoteCall();
    }

    /**
     * 通知教育服务角色权限变更
     */
    private void eduModeIdChangedNotifyByRemoteCall() {
        try {
            logger.info("Notify edit edu mode.");
            String mainIp = k8sSvcService.getMainNodeInternalIPNotNull();
            String url = "http://" + mainIp + ":11111/api/rest/internal/v1/en/edu/manage/role/change/model?enterpriseId=default_enterprise";
            restTemplate.delete(url);
        } catch (RestClientException e) {
            logger.error("ModeId changed notify remote error.", e);
        }
    }
}
