package com.xylink.manager.service.develop;

import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServiceErrorException;
import com.xylink.manager.controller.dto.TerminalCardReqDto;
import com.xylink.manager.controller.dto.TerminalCardUpdateStatusDTO;
import com.xylink.manager.model.common.Page;
import com.xylink.manager.service.ServerListService;
import com.xylink.manager.service.dto.TerminalCardStatusDTO;
import com.xylink.manager.service.dto.TerminalCardStatusPageDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2024/4/15 10:47
 */
@Service
@Slf4j
public class TerminalCardService {
    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ServerListService serverListService;

    /**
     * 分页查询，终端卡片列表结果
     * @return
     */
    public Page<TerminalCardStatusPageDTO.TerminalCardStatus> getPageNemo(TerminalCardReqDto dto) {
        String internalNginxUrl = serverListService.getInternalNginxUrl();
        String url = internalNginxUrl + "/api/rest/internal/v1/vcs/customizedfeature/" + dto.getFeatureId() + "/nemo/list?startNum=" + (dto.getCurrent() - 1) + "&pageSize=" + dto.getPageSize();
        if (StringUtils.isNotBlank(dto.getSn())) {
            url = url + "&sn=" + dto.getSn();
        }

        HashMap<String, Object> map = new HashMap<>();
        //vcs页数是从0开始，manager页数是从1开始
        map.put("startNum", dto.getCurrent()-1);
        map.put("pageSize", dto.getPageSize());
        map.put("sn", dto.getSn());
        TerminalCardStatusPageDTO statusPageDTO;
        try {
            statusPageDTO = restTemplate.getForObject(url, TerminalCardStatusPageDTO.class);
        } catch (Exception e) {
            log.error("TerminalCard get pageList failed!", e);
            throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_VCS_FAILED);
        }

        return new Page<>(dto.getCurrent(), statusPageDTO.getPageSize(), statusPageDTO.getTotalRows(), statusPageDTO.getData());
    }

    /**
     * 更新终端卡片状态
     * @param statusDTO
     */
    public void updateTerminalCardStatus(TerminalCardUpdateStatusDTO statusDTO) {

        boolean mainShortCut = !ObjectUtils.isEmpty(statusDTO.getMainShortCut()) && statusDTO.getMainShortCut();
        String featureIds = statusDTO.getFeatureIds();

        List<String> sns = Lists.newArrayList(statusDTO.getSns().split(";"));
        String internalNginxUrl = serverListService.getInternalNginxUrl();
        String queryTypeUrl = internalNginxUrl + "/api/rest/bm/internal/enterprise/sns/v1";
        TerminalType[] terminalTypes;
        try {
            terminalTypes = restTemplate.postForObject(queryTypeUrl, sns, TerminalType[].class);
        } catch (Exception e) {
            log.error("TerminalCard query terminalType by snList failed,", e);
            throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_BM_FAILED);
        }

        if (ObjectUtils.isEmpty(terminalTypes)) {
            throw new ServiceErrorException(ErrorStatus.SN_NOT_MATCH_TERMINAL_TYPE);
        }

        for (TerminalType temp : terminalTypes) {
            if (!statusDTO.getDeviceType().equalsIgnoreCase(temp.getSubType())) {
                throw new ServiceErrorException(ErrorStatus.SN_NOT_MATCH_TERMINAL_TYPE);
            }
        }

        String updateStatusUrl = internalNginxUrl + "/api/rest/internal/v1/vcs/customizedfeature/" + featureIds + "/controlFeature";
        TerminalCardStatusDTO terminalCardStatusDTO = new TerminalCardStatusDTO();
        terminalCardStatusDTO.setAll(false);
        terminalCardStatusDTO.setSns(sns);
        terminalCardStatusDTO.setTrial(false);
        terminalCardStatusDTO.setStatus(statusDTO.getStatus());
        terminalCardStatusDTO.setMainShortCut(mainShortCut);
        terminalCardStatusDTO.setFeatureIds(featureIds);
        try {
            restTemplate.postForObject(updateStatusUrl, terminalCardStatusDTO, Void.class);
        } catch (Exception e) {
            log.error("TerminalCard status update failed!", e);
            throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_VCS_FAILED);
        }
    }

    public void delete(Set<String> ids) {
        String internalNginxUrl = serverListService.getInternalNginxUrl();
        String url = internalNginxUrl + "/api/rest/vcs/internal/nemoFeature/v1/delete";
        try {
            restTemplate.postForObject(url, ids, Void.class);
        } catch (Exception e) {
            log.error("TerminalCard status delete failed", e);
            throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_VCS_FAILED);
        }
    }

    @Data
    private static class TerminalType{
        private String deviceCategory;
        private String subType;
    }
}
