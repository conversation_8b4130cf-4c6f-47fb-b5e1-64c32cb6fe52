package com.xylink.manager.service.business;

import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.model.business.InitRoleDto;
import com.xylink.manager.service.remote.buffet.BuffetRemoteClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-09-20 11:45
 */
@Service
public class BuffetService {

    @Resource
    private BuffetRemoteClient buffetRemoteClient;

    public Map<String, Integer> initRoleStatus() {
        boolean isInitRole = buffetRemoteClient.isInitRole();
        Map<String, Integer> data = new HashMap<>();
        data.put("status", isInitRole ? 1 : 0);
        return data;
    }

    public void initRole(InitRoleDto initRoleDto) {
        try {
            buffetRemoteClient.initRole(initRoleDto.getSystemPwd(), initRoleDto.getAuditPwd(), initRoleDto.getSecurityPwd());
        } catch (Exception e) {
            throw new ServerException(e, ErrorStatus.THIRD_INTERFACE_BUFFET_FAILED);
        }
    }

}
