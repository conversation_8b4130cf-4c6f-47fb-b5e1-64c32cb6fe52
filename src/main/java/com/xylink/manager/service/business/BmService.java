package com.xylink.manager.service.business;

import com.xylink.manager.model.common.Page;
import com.xylink.manager.service.remote.basicmanagement.BasicManagementRemoteClient;
import com.xylink.manager.service.remote.basicmanagement.dto.RoomsPageResponse;
import com.xylink.manager.service.remote.basicmanagement.dto.RoomsRequest;
import com.xylink.manager.service.remote.basicmanagement.dto.RoomsSearchRequest;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024-10-21 15:31
 */
@Service
public class BmService {
    @Resource
    private BasicManagementRemoteClient basicManagementRemoteClient;

    public Page<RoomsRequest> page(RoomsSearchRequest searchRequest) {
        RoomsPageResponse data = basicManagementRemoteClient.page(searchRequest);
        return new Page<>(data.getCurrent(), data.getSize(), data.getTotal(), data.getRecords());
    }

    public void addRooms(RoomsRequest roomsRequest) {
        basicManagementRemoteClient.addRooms(roomsRequest);
    }

    public void updateRooms(RoomsRequest roomsRequest) {
        basicManagementRemoteClient.updateRooms(roomsRequest);
    }

    public void deleteRooms(String id) {
        basicManagementRemoteClient.deleteRooms(id);
    }

}
