package com.xylink.manager.service.categorytag;

import com.xylink.manager.model.CategoryTagsDto;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2021/5/14 5:58 下午
 */
public interface CategoryTagsService {
    /**
     * query if name is null return all.
     *
     * @param name
     * @return
     */
    List<CategoryTagsDto> query(String name);

    /**
     * query by id
     *
     * @param id
     * @return
     */
    Optional<CategoryTagsDto> getById(String id);

    Optional<CategoryTagsDto> getCategoryTagsDto(String categoryTagId);

    /**
     * saveOrUpdate
     *
     * @param categoryTagsDto
     * @return
     */
    boolean saveOrUpdate(CategoryTagsDto categoryTagsDto);

    /**
     * delete by id
     *
     * @param id
     * @return
     */
    boolean deleteById(String id);

}
