package com.xylink.manager.service.categorytag;

import com.xylink.config.Constants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.config.mapper.JsonMapper;
import com.xylink.manager.model.CategoryTagsDto;
import com.xylink.manager.model.NodeExtDto;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.cache.bean.ConfigMapCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/5/14 6:00 下午
 */
@Service
@Slf4j
public class CategoryTagsServiceImpl implements CategoryTagsService {

    private final K8sService k8sService;

    public CategoryTagsServiceImpl(K8sService k8sService) {
        this.k8sService = k8sService;
    }

    @Override
    public List<CategoryTagsDto> query(String name) {
        List<CategoryTagsDto> all = getAllCategoryTagsDto();
        if (StringUtils.isBlank(name)) {
            return all.stream()
                    .sorted(Comparator.comparing(CategoryTagsDto::getCreateTime).reversed())
                    .collect(Collectors.toList());
        }
        return all.stream()
                .filter(item -> item.getName().contains(name))
                .sorted(Comparator.comparing(CategoryTagsDto::getCreateTime).reversed())
                .collect(Collectors.toList());
    }

    @Override
    public Optional<CategoryTagsDto> getById(String id) {
        Map<String, String> metadata = getMetadata();
        String detailStr = metadata.get(id);
        return StringUtils.isNotBlank(detailStr) ? Optional.of(getObjFromStr(detailStr)) : Optional.empty();
    }

    @Override
    public Optional<CategoryTagsDto> getCategoryTagsDto(String categoryTagId) {
        ConfigMapCache configMapCache = k8sService.getConfigMapCacheOrCreateIfNotExist(Constants.CONFIGMAP_CATEGORY_TAGS);
        String detailStr = configMapCache.getData().get(categoryTagId);
        return StringUtils.isNotBlank(detailStr) ? Optional.of(getObjFromStr(detailStr)) : Optional.empty();
    }


    @Override
    public boolean saveOrUpdate(CategoryTagsDto categoryTagsDto) {
        String id = categoryTagsDto.getId();
        CategoryTagsDto persistent;
        // 判断标签名称是否存在
        categoryTagNameExistCheck(categoryTagsDto.getName());
        if (StringUtils.isNotBlank(id)) {
            Optional<CategoryTagsDto> exist = this.getById(id);
            if (!exist.isPresent()) {
                throw new ServerException(ErrorStatus.CATEGORY_TAG_NOT_EXIST);
            }
            CategoryTagsDto data = exist.get();
            data.setName(categoryTagsDto.getName());
            data.setDescription(categoryTagsDto.getDescription());
            data.setSort(categoryTagsDto.getSort());
            data.setUpdateTime(new Date());
            persistent = data;
        } else {
            Date now = new Date();
            categoryTagsDto.setId(generateUUID());
            categoryTagsDto.setCreateTime(now);
            categoryTagsDto.setUpdateTime(now);
            persistent = categoryTagsDto;
        }
        return edit(persistent);
    }

    @Override
    public boolean deleteById(String id) {
        List<CategoryTagsDto> allCategoryTagsDto = getAllCategoryTagsDto();
        List<CategoryTagsDto> replaceList = allCategoryTagsDto.stream().filter(item -> !item.getId().equalsIgnoreCase(id)).collect(Collectors.toList());
        if (replaceList.size() == allCategoryTagsDto.size()) {
            return true;
        }
        log.info("Del category tag before is:{}, now is: {}", joinName(allCategoryTagsDto), joinName((replaceList)));
        deleteIdFromNodeExt(id);
        replaceData(replaceList);
        return true;
    }

    private void deleteIdFromNodeExt(String id) {
        Map<String, String> allNodeExt = k8sService.getConfigmapOrCreate(Constants.CONFIGMAP_NODE_EXT);
        if (CollectionUtils.isEmpty(allNodeExt)) {
            return;
        }

        for (Map.Entry<String, String> entry : allNodeExt.entrySet()) {
            if (entry.getValue().contains(id)) {
                NodeExtDto nodeExtDto = JsonMapper.nonEmptyMapper().fromJson(entry.getValue(), NodeExtDto.class);
                nodeExtDto.removeCategoryTagId();
                allNodeExt.put(entry.getKey(), JsonMapper.nonEmptyMapper().toJson(nodeExtDto));
            }
        }

        k8sService.editConfigmap(Constants.CONFIGMAP_NODE_EXT, allNodeExt);
    }

    private void categoryTagNameExistCheck(String name) throws ServerException {
        List<CategoryTagsDto> all = getAllCategoryTagsDto();
        if (all.stream().anyMatch(item -> item.getName().equalsIgnoreCase(name))) {
            throw new ServerException(ErrorStatus.CATEGORY_TAG_EXIST);
        }
    }

    private Map<String, String> getMetadata() {
        return k8sService.getConfigmapOrCreate(Constants.CONFIGMAP_CATEGORY_TAGS);
    }

    private List<CategoryTagsDto> getAllCategoryTagsDto() {
        Map<String, String> data = getMetadata();
        if (data.isEmpty()) {
            return Collections.emptyList();
        }
        Collection<String> values = data.values();
        List<CategoryTagsDto> result = new ArrayList<>(values.size());
        data.values().forEach((item) ->
                result.add(getObjFromStr(item))
        );
        return result;
    }

    private boolean edit(CategoryTagsDto data) {
        k8sService.editConfigmap(Constants.CONFIGMAP_CATEGORY_TAGS, convertToMap(Collections.singletonList(data)));
        return true;
    }

    private boolean replaceData(List<CategoryTagsDto> data) {
        k8sService.replaceConfigmap(Constants.CONFIGMAP_CATEGORY_TAGS, convertToMap(data));
        return true;
    }

    private Map<String, String> convertToMap(List<CategoryTagsDto> categoryTagsDto) {
        if (CollectionUtils.isEmpty(categoryTagsDto)) {
            return null;
        }
        return categoryTagsDto.stream().collect(Collectors.toMap(CategoryTagsDto::getId, this::toJsonStr, (key1, key2) -> key2));
    }

    private CategoryTagsDto getObjFromStr(String jsonStr) {
        return JsonMapper.nonEmptyMapper().fromJson(jsonStr, CategoryTagsDto.class);
    }

    private String toJsonStr(CategoryTagsDto categoryTagsDto) {
        return JsonMapper.nonEmptyMapper().toJson(categoryTagsDto);
    }

    private String joinName(List<CategoryTagsDto> categoryTagsDtoList) {
        return categoryTagsDtoList.stream().map(CategoryTagsDto::getName).collect(Collectors.joining());
    }

    private String generateUUID() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }
}
