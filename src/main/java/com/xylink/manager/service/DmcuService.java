package com.xylink.manager.service;

import com.xylink.config.mapper.JsonMapper;
import com.xylink.manager.controller.dto.BaseResponseDto;
import com.xylink.manager.controller.dto.DmcuLicenseDto;
import com.xylink.manager.controller.dto.DmcuSiteDto;
import com.xylink.manager.model.common.BuffetPage;
import com.xylink.manager.model.common.Page;
import com.xylink.manager.model.common.Pageable;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.client.support.BasicAuthorizationInterceptor;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/9/26.
 */
@Service
public class DmcuService {

    private final static Logger logger = LoggerFactory.getLogger(DmcuService.class);

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ServerListService serverListService;
    private String dmcuAgentImportUrl = "http://localhost:8081/dmcuagent/license/import";


    public void importLicense(String ip, MultipartFile multipartFile) {
        logger.info(multipartFile.getOriginalFilename());
        if (!multipartFile.isEmpty()) {

            try {
                try (InputStream inStream = multipartFile.getInputStream()) {
                    String result = IOUtils.toString(inStream, StandardCharsets.UTF_8.toString());
                    logger.info("dmcu ip: " + ip);
                    logger.info("content:" + result);
                    DmcuLicenseDto dmcuLicenseDto = new JsonMapper().fromJson(result, DmcuLicenseDto.class);
                    HttpEntity<DmcuLicenseDto> request = new HttpEntity<>(dmcuLicenseDto);
                    restTemplate.getInterceptors().add(
                            new BasicAuthorizationInterceptor("admin", "123456"));
                    Boolean a = restTemplate.postForObject(dmcuAgentImportUrl, request, Boolean.class);
                    logger.info("result: " + a);
                }

            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }
    public Page<DmcuSiteDto> getPageDmcuSite(Pageable pageRequest) {
        String mainIp = serverListService.getMainNodeInternalIP();
        long page = pageRequest.getPageNumber() - 1;
        long size = pageRequest.getPageSize();
        String url = "http://" + mainIp + ":11111/console/api/rest/internal/v1/siteConfig/findPage?enterpriseId=default_enterprise" +
                "&page="+ page + "&size=" + size;
        BuffetPage response = restTemplate.getForObject(url, BuffetPage.class);
        return new Page<>(pageRequest.getPageNumber(),pageRequest.getPageSize(),response.getTotalElements(),response.getContent());
    }

    public void saveDmcuSite(DmcuSiteDto siteDto) {
        String mainIp = serverListService.getMainNodeInternalIP();
        String url = "http://" + mainIp + ":11111/console/api/rest/internal/v1/siteConfig/saveOrUpdate";
        restTemplate.postForObject(url, siteDto, BaseResponseDto.class);
    }

    public void updateDmcuSite(DmcuSiteDto siteDto) {
        String mainIp = serverListService.getMainNodeInternalIP();
        String url = "http://" + mainIp + ":11111/console/api/rest/internal/v1/siteConfig/saveOrUpdate";
        restTemplate.postForObject(url, siteDto, BaseResponseDto.class);
    }

    public void deleteDmcuSite(String id) {
        String mainIp = serverListService.getMainNodeInternalIP();
        String url = "http://" + mainIp + ":11111/console/api/rest/internal/v1/siteConfig/delete";
        Map<String, String> param = new HashMap<>();
        param.put("id", id);
        restTemplate.postForObject(url, param, BaseResponseDto.class);
    }

    public List<DmcuSiteDto> getAllDmcuSite() {
        String mainIp = serverListService.getMainNodeInternalIP();
        String url = "http://" + mainIp + ":11111/console/api/rest/internal/v1/siteConfig/findAll?enterpriseId=default_enterprise";
        List<DmcuSiteDto> siteDtos = restTemplate.getForObject(url, List.class);
        return siteDtos;
    }
}
