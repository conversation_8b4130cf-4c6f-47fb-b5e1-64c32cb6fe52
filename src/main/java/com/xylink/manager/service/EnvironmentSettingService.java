package com.xylink.manager.service;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xylink.config.Constants;
import com.xylink.config.DmcuConstants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.RecordConstants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServiceErrorException;
import com.xylink.manager.controller.dto.FeatureDto;
import com.xylink.manager.controller.dto.NodeDto;
import com.xylink.manager.controller.dto.servicemanage.ServerManageData;
import com.xylink.manager.model.deploy.Node;
import com.xylink.manager.model.em.IppbxSiggatewayEnum;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.util.JDBCUtilsForEnvSetting;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.Connection;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2022/4/2 10:47 上午
 */
@Service
public class EnvironmentSettingService {
    private static final Logger logger = LoggerFactory.getLogger(EnvironmentSettingService.class);

    @Autowired
    private IDeployService deployService;
    @Autowired
    private K8sService k8sService;
    @Autowired
    private NetToolService netToolService;
    @Autowired
    private ServerListService serverListService;
    @Autowired
    private JDBCUtilsForEnvSetting jdbcUtilsForEnvSetting;

    /**
     * 涉及到NodeHandler子类重写的afterConfigure()涉及数据库操作的node类型
     */
    private static final String TYPE_NODE_HANDLER = "node-handler";

    public static ExecutorService executor = new ThreadPoolExecutor(2, 2,
            30L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(10),
            new ThreadFactoryBuilder().setNameFormat("SetEnv_Pool-%d").setDaemon(true).build(),
            new ThreadPoolExecutor.AbortPolicy());

    /**
     * 保存节点saveNode()操作涉及保存数据库的操作
     */
    private static List<String> nodeTypeListForDb = Arrays.asList(ServerManageData.NODE_TYPE_COMMON, Constants.NODE_TYPE_COMMON_MAIN, "uaa", "webrtc", "main", "dmcu", "dmcu-x86", "dmcu-arm", "dmcu-side", "ivr", "main-partner", "record", "record-x86", "record-arm", "vod", "edu", "edu-file", "hadoop", "ippbx-siggateway");

    public void freshEnvInfo() {
        logger.info("start to set env info!");
        if (!getDbPodStatus()) {
            throw new ServiceErrorException(ErrorStatus.DB_NOT_RUNNING);
        }

        Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        Map<String, List<NodeDto>> nodeTypeToNodeDtoList = getNodeDtoListForDb(allIp);
        if (CollectionUtils.isEmpty(nodeTypeToNodeDtoList)) {
            throw new ServiceErrorException(ErrorStatus.NOT_GET_NODELIST);
        }

        for (Map.Entry<String, List<NodeDto>> temp : nodeTypeToNodeDtoList.entrySet()) {
            if (ServerManageData.NODE_TYPE_COMMON.equals(temp.getKey())) {
                executor.execute(() -> setMainEnv(nodeTypeToNodeDtoList.get(ServerManageData.NODE_TYPE_COMMON), allIp));
                executor.execute(() -> setDmcuOrIvrMonitorEnv(nodeTypeToNodeDtoList.get(ServerManageData.NODE_TYPE_COMMON), allIp, temp.getKey()));
                executor.execute(() -> setRecordMonitorEnv(nodeTypeToNodeDtoList.get(ServerManageData.NODE_TYPE_COMMON), allIp));
                executor.execute(() -> setVodEnv(allIp));
                executor.execute(() -> setEduConfigEnv(allIp));
                executor.execute(() -> setNodeHandlerEnv(nodeTypeToNodeDtoList.get(ServerManageData.NODE_TYPE_COMMON), allIp));
            } else if (Constants.NODE_TYPE_COMMON_MAIN.equals(temp.getKey())) {
                executor.execute(() -> setMainEnv(nodeTypeToNodeDtoList.get(Constants.NODE_TYPE_COMMON_MAIN), allIp));
                executor.execute(() -> setDmcuOrIvrMonitorEnv(nodeTypeToNodeDtoList.get(Constants.NODE_TYPE_COMMON_MAIN), allIp, temp.getKey()));
                executor.execute(() -> setRecordMonitorEnv(nodeTypeToNodeDtoList.get(Constants.NODE_TYPE_COMMON_MAIN), allIp));
                executor.execute(() -> setVodEnv(allIp));
                executor.execute(() -> setEduConfigEnv(allIp));
                executor.execute(() -> setNodeHandlerEnv(nodeTypeToNodeDtoList.get(Constants.NODE_TYPE_COMMON_MAIN), allIp));
            } else if (Constants.NODETYPE_MAIN.equals(temp.getKey())) {
                executor.execute(() -> setMainEnv(nodeTypeToNodeDtoList.get(Constants.NODETYPE_MAIN), allIp));
            } else if (Labels.dmcu.label().equals(temp.getKey())
                    || Labels.ivr.label().equals(temp.getKey())
                    || Labels.ivr_x86.label().equals(temp.getKey())
                    || Labels.ivr_arm.label().equals(temp.getKey())) {
                executor.execute(() -> setDmcuOrIvrMonitorEnv(nodeTypeToNodeDtoList.get(Labels.dmcu.label()), allIp, temp.getKey()));
            } else if (Labels.record.label().equals(temp.getKey())) {
                executor.execute(() -> setRecordMonitorEnv(nodeTypeToNodeDtoList.get(Labels.record.label()), allIp));
            } else if (Labels.vod.label().equals(temp.getKey())) {
                executor.execute(() -> setVodEnv(allIp));
            } else if (Labels.edu.label().equals(temp.getKey())) {
                executor.execute(() -> setEduConfigEnv(allIp));
            } else if (TYPE_NODE_HANDLER.equals(temp.getKey())) {
                executor.execute(() -> setNodeHandlerEnv(nodeTypeToNodeDtoList.get(TYPE_NODE_HANDLER), allIp));
            }
        }
    }

    public void setMainEnv(List<NodeDto> nodeDtoList, Map<String, String> allIp) {
        logger.info("start to set main env info!");
        String scheme = allIp.get("SCHEME");
        Connection buffetConnection = jdbcUtilsForEnvSetting.getConnectionV2(Labels.mysql.label(), "buffet", allIp);
        Connection ainemoConnection = jdbcUtilsForEnvSetting.getConnectionV2(Labels.mysql.label(), "ainemo", allIp);
        if (Objects.isNull(buffetConnection) || Objects.isNull(ainemoConnection)) {
            logger.error("buffet or ainemo connection ,not set main env");
            return;
        }
        try {
            for (NodeDto nodeDto : nodeDtoList) {
                //内外网探测
                jdbcUtilsForEnvSetting.configureNetworkDetection(nodeDto.getInternalIp(), nodeDto.getExternalIp(), nodeDto.getNginxPort(), ainemoConnection, buffetConnection);
                //终端网络测试
                String netToolPort = netToolService.getMainNodeNetToolPort(nodeDto.getName());
                String mainNodeNetToolPublicIpV6 = netToolService.getMainNodeNetToolPublicIpV6(nodeDto.getName());
                String mainNodeNetToolPublicIpV4 = netToolService.getMainNodeNetToolPublicIpV4(nodeDto.getName());
                String outIpV4 = StringUtils.isNotBlank(mainNodeNetToolPublicIpV4) ? mainNodeNetToolPublicIpV4 : nodeDto.getExternalIp();

                jdbcUtilsForEnvSetting.configureNetworkTest(outIpV4, mainNodeNetToolPublicIpV6, netToolPort, ainemoConnection);

                updateServerConfig(StringUtils.isBlank(scheme) ? "default" : scheme, ainemoConnection);
            }
        } catch (Exception e) {
            logger.error("failed set main node env info,", e);
        } finally {
            jdbcUtilsForEnvSetting.close(buffetConnection, ainemoConnection);
            logger.info("end to set main env info!");
        }
    }

    public void setDmcuOrIvrMonitorEnv(List<NodeDto> nodeDtoList, Map<String, String> allIp, String type) {
        if (Labels.ivr.label().equalsIgnoreCase(type) || Labels.ivr_x86.label().equalsIgnoreCase(type) || Labels.ivr_arm.label().equalsIgnoreCase(type)) {
            return;
        }
        Map<String, String> allDmcu = k8sService.getConfigmap(Constants.CONFIGMAP_DMCU);
        Connection buffetConnection = jdbcUtilsForEnvSetting.getConnectionV2(Labels.mysql.label(), "buffet", allIp);
        if (CollectionUtils.isEmpty(allDmcu) || Objects.isNull(buffetConnection)) {
            logger.error("all-dmcu or connection is null,not set dmcuMonitor config");
            return;
        }
        logger.info("start to set dmcuMonitor info!");

        try {
            for (NodeDto nodeDto : nodeDtoList) {
                String capacity = allDmcu.get(nodeDto.getName() + DmcuConstants.EPNUM_PERTASK);
                String comments = allDmcu.get(nodeDto.getName() + DmcuConstants.COMMENTS);
                if (StringUtils.isBlank(capacity)) {
                    capacity = allDmcu.get(DmcuConstants.DEFAULT_PREFIX + DmcuConstants.EPNUM_PERTASK);
                }
                boolean addLabel = checkDmcuAddLabel(nodeDto);

                jdbcUtilsForEnvSetting.configureDmcuOrRecordMonitor(nodeDto, comments, capacity, "0", addLabel, buffetConnection);
            }
        } catch (Exception e) {
            logger.error("failed set dmcuMonitor config,", e);
        } finally {
            jdbcUtilsForEnvSetting.close(buffetConnection);
            logger.info("end to set dmcuMonitor info!");
        }
    }

    /**
     * 检查当前node是否添加了哪种dmcu类型的标签
     *
     * @param nodeDto
     * @return
     */
    private boolean checkDmcuAddLabel(NodeDto nodeDto) {
        Boolean hasDmcuLabel;
        if (Labels.dmcu_x86.label().equalsIgnoreCase(nodeDto.getType())) {
            hasDmcuLabel = nodeDto.getLabelMap().get(Labels.dmcu_x86.label());
        } else if (Labels.dmcu_arm.label().equalsIgnoreCase(nodeDto.getType())) {
            hasDmcuLabel = nodeDto.getLabelMap().get(Labels.dmcu_arm.label());
        } else if (Labels.dmcu_side.label().equalsIgnoreCase(nodeDto.getType())) {
            hasDmcuLabel = nodeDto.getLabelMap().get(Labels.dmcu_side.label());
        } else if (Labels.dmcu_side_x86.label().equalsIgnoreCase(nodeDto.getType())) {
            hasDmcuLabel = nodeDto.getLabelMap().get(Labels.dmcu_side_x86.label());
        } else {
            hasDmcuLabel = nodeDto.getLabelMap().get(Labels.dmcu.label());
        }
        if (Objects.isNull(hasDmcuLabel)) {
            logger.info("hasDmcuLabel result is null");
            return false;
        }
        return hasDmcuLabel;
    }

    public void setRecordMonitorEnv(List<NodeDto> nodeDtoList, Map<String, String> allIp) {
        Map<String, String> allRecord = k8sService.getConfigmap(Constants.CONFIGMAP_RECORD);
        Connection buffetConnection = jdbcUtilsForEnvSetting.getConnectionV2(Labels.mysql.label(), "buffet", allIp);
        if (CollectionUtils.isEmpty(allRecord) || Objects.isNull(buffetConnection)) {
            logger.error("all-record or connection is null,not set recordMonitor config");
            return;
        }
        logger.info("start to set recordMonitor info!");

        try {
            for (NodeDto nodeDto : nodeDtoList) {
                String capacity = allRecord.get(nodeDto.getName() + RecordConstants.PROCESS_TASKNUM);
                String comments = allRecord.get(nodeDto.getName() + DmcuConstants.COMMENTS);
                if (StringUtils.isBlank(capacity)) {
                    capacity = allRecord.get(RecordConstants.DEFAULT_PREFIX + RecordConstants.PROCESS_TASKNUM);
                }
                boolean addLabel = checkRecordAddLabel(nodeDto);

                jdbcUtilsForEnvSetting.configureDmcuOrRecordMonitor(nodeDto, comments, capacity, "1", addLabel, buffetConnection);
            }
        } catch (Exception e) {
            logger.error("failed set recordMonitor config,", e);
        } finally {
            jdbcUtilsForEnvSetting.close(buffetConnection);
            logger.info("end to set recordMonitor info!");
        }
    }

    private boolean checkRecordAddLabel(NodeDto nodeDto) {
        if (Labels.record_x86.label().equalsIgnoreCase(nodeDto.getType())) {
            return Optional.ofNullable(nodeDto.getLabelMap().get(Labels.record_x86.label())).orElse(false);
        } else if (Labels.record_arm.label().equalsIgnoreCase(nodeDto.getType())) {
            return Optional.ofNullable(nodeDto.getLabelMap().get(Labels.record_arm.label())).orElse(false);
        } else {
            return Optional.ofNullable(nodeDto.getLabelMap().get(Labels.record.label())).orElse(false);
        }
    }

    public void setVodEnv(Map<String, String> allIp) {
        String scheme = allIp.get("SCHEME");
        Connection ainemoConnection = jdbcUtilsForEnvSetting.getConnectionV2(Labels.mysql.label(), "ainemo", allIp);
        if (Objects.isNull(ainemoConnection)) {
            logger.error("ainemo connection is null,not set vod env");
            return;
        }
        logger.info("start to set vod env info!");
        try {
            updateServerConfig(StringUtils.isBlank(scheme) ? "default" : scheme, ainemoConnection);
        } catch (Exception e) {
            logger.error("failed set vod env info,", e);
        } finally {
            jdbcUtilsForEnvSetting.close(ainemoConnection);
            logger.info("end to set vod env info!");
        }
    }

    public void setEduConfigEnv(Map<String, String> allIp) {
        Connection eduConnection = jdbcUtilsForEnvSetting.getConnection(Labels.edu.label(), allIp);
        if (Objects.isNull(eduConnection)) {
            logger.error("edu connection is null,not set edu env");
            return;
        }
        logger.info("start to set edu env info!");
        try {
            updateEduConfig(allIp, eduConnection);
        } catch (Exception e) {
            logger.error("failed set edu config,", e);
        } finally {
            jdbcUtilsForEnvSetting.close(eduConnection);
            logger.info("end to set edu env info!");
        }
    }

    /**
     * 处理saveNode()中涉及NodeHandler的afterConfigure()中操作数据库动作
     *
     * @param nodeDtoList
     * @param allIp
     */
    public void setNodeHandlerEnv(List<NodeDto> nodeDtoList, Map<String, String> allIp) {
        logger.info("start to set node-handler env info!");
        Map<String, String> allIppbxSig = k8sService.getConfigmap(Constants.CONFIGMAP_IPPBX_SIGGW);
        Connection buffetConnection = jdbcUtilsForEnvSetting.getConnectionV2(Labels.mysql.label(), "buffet", allIp);
        Connection externalConnection = jdbcUtilsForEnvSetting.getConnectionV2(Labels.mysql.label(), "external", allIp);
        Connection webrtcConnection = jdbcUtilsForEnvSetting.getConnection(Labels.webrtc.label(), allIp);
        Connection ainemoConnection = jdbcUtilsForEnvSetting.getConnectionV2(Labels.mysql.label(), "ainemo", allIp);
        try {
            for (NodeDto nodeDto : nodeDtoList) {
                logger.info("start to set node-handler:{} env info!", nodeDto.getType());
                if (Labels.hadoop.label().equals(nodeDto.getType())) {
                    afterConfigureHadoop(buffetConnection, externalConnection);
                } else if (Labels.ippbx_siggw.label().equals(nodeDto.getType())) {
                    afterConfigureIppbxSig(nodeDto, allIppbxSig, ainemoConnection);
                }
            }
        } catch (Exception e) {
            logger.error("failed set node-handler env info", e);
        } finally {
            jdbcUtilsForEnvSetting.close(buffetConnection, externalConnection, webrtcConnection, ainemoConnection);
            logger.info("end to set node-handler env info!");
        }
    }

    public void afterConfigureHadoop(Connection buffetConnection, Connection externalConnection) {
        try {
            jdbcUtilsForEnvSetting.saveBuffetConfigDict(ConfigService.DATACENTER_SETTING_CONFIG.getConfigName(), ConfigService.DATACENTER_SETTING_CONFIG.getName(), externalConnection);
            jdbcUtilsForEnvSetting.saveBuffetConfig(ConfigService.DATACENTER_SETTING_CONFIG.getConfigName(), ConfigService.DATACENTER_SETTING_CONFIG.getConfigValue(), buffetConnection);
        } catch (Exception e) {
            logger.error("Save config:{} error.", ConfigService.DATACENTER_SETTING_CONFIG, e);
        }
    }

    public void afterConfigureIppbxSig(NodeDto nodeDto, Map<String, String> allIppbxSig, Connection ainemoConnection) {
        if (CollectionUtils.isEmpty(allIppbxSig)) {
            logger.warn("all-ippbx-siggateway is null,not set ippbxSig env info");
            return;
        }
        logger.info("start to set node-handler:{ippbx-siggateway} env info!");
        String sn = allIppbxSig.get(nodeDto.getName() + IppbxSiggatewayEnum.IPPBX_SN.getValue());

        if (StringUtils.isBlank(sn)) {
            logger.warn("ippbx-siggateway node internalIp:{} ,not sn!", nodeDto.getInternalIp());
            return;
        }
        jdbcUtilsForEnvSetting.configureIppbxSigGatewaySN(sn, ainemoConnection);
    }

    public void updateServerConfig(String scheme, Connection ainemoConnection) {

        /**
         * 修改scheme
         * 1、nginx监听端口更改
         * 2、下发给终端的配置更改(spring appliacation.yaml)
         *
         * 这里只是开关，具体实现在pod启动后，通过ip-change 脚本实现，保证升级/重启后该改动不会丢失
         */
        k8sService.editConfigmap(Constants.CONFIGMAP_ALLIP, new HashMap<String, String>() {{
            put("SCHEME", scheme);
        }});

        // TODO 是否需要自动重启对应服务

        //配置 default_server_config
        String defaultServerConfig = k8sService.getConfigmap(Constants.CONFIGMAP_PRIVATE_SERVER_CONFIG).get("default_server_config");
        String[] datas = defaultServerConfig.split("\\n");

        Map<String, String> all = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);

        List<String> params = new ArrayList<>();

        String mainDomain = all.get(NetworkConstants.MAIN_DOMAIN_NAME);
        String mainPublicIp = all.get(NetworkConstants.MAIN_PUBLIC_IP);
        String nginxPort = all.get(NetworkConstants.MAIN_NGINX_PORT);
        String nginxSslPort = all.get(NetworkConstants.MAIN_NGINX_SSL_PORT);

        String survDomain = all.get(NetworkConstants.SURV_DOMAIN_NAME);
        String survNginxPort = all.get(NetworkConstants.SURV_NGINX_PORT);
        String survNginxSSLPort = all.get(NetworkConstants.SURV_NGINX_SSL_PORT);

        String vodDomain = all.get(NetworkConstants.VOD_DOMAIN_NAME);
        String vodNginxPort = all.get(NetworkConstants.VOD_NGINX_PORT);
        String vodNginxSslPort = all.get(NetworkConstants.VOD_NGINX_SSL_PORT);

        String vod = "80".equals(vodNginxPort) ? vodDomain : vodDomain + ":" + vodNginxPort;
        String vodSsl = "443".equals(vodNginxSslPort) ? vodDomain : vodDomain + ":" + vodNginxSslPort;
        String main = "80".equals(nginxPort) ? mainDomain : mainDomain + ":" + nginxPort;
        String mainSsl = "443".equals(nginxSslPort) ? mainDomain : mainDomain + ":" + nginxSslPort;


        for (String data : datas) {
            if ("https/wss".equals(scheme)) {
                data = data.replace("http://{MAIN_DOMAIN_NAME}:{NGINX_PORT}", "https://" + mainSsl);
                data = data.replace("https://{MAIN_DOMAIN_NAME}:{NGINX_SSL_PORT}", "https://" + mainSsl);
                data = data.replace("ws://{MAIN_DOMAIN_NAME}:{NGINX_PORT}", "wss://" + mainSsl);
                data = data.replace("wss://{MAIN_DOMAIN_NAME}:{NGINX_SSL_PORT}", "wss://" + mainSsl);
                data = data.replace("{MAIN_DOMAIN_NAME}:{NGINX_PORT}", mainSsl);
                data = data.replace("{MAIN_DOMAIN_NAME}:{NGINX_SSL_PORT}", mainSsl);
                data = data.replace("{MAIN_PUBLIC_IP}", mainPublicIp);

                data = data.replace("{SURV_DOMAIN_NAME}", survDomain);

                data = data.replace("http://{VOD_DOMAIN_NAME}:{VOD_NGINX_PORT}", "https://" + vodSsl);
                data = data.replace("https://{VOD_DOMAIN_NAME}:{VOD_NGINX_SSL_PORT}", "https://" + vodSsl);

            } else if ("http/ws".equals(scheme)) {
                data = data.replace("http://{MAIN_DOMAIN_NAME}:{NGINX_PORT}", "http://" + main);
                data = data.replace("https://{MAIN_DOMAIN_NAME}:{NGINX_SSL_PORT}", "http://" + main);
                data = data.replace("ws://{MAIN_DOMAIN_NAME}:{NGINX_PORT}", "ws://" + main);
                data = data.replace("wss://{MAIN_DOMAIN_NAME}:{NGINX_SSL_PORT}", "ws://" + main);
                data = data.replace("{MAIN_DOMAIN_NAME}:{NGINX_PORT}", main);
                data = data.replace("{MAIN_DOMAIN_NAME}:{NGINX_SSL_PORT}", main);
                data = data.replace("{MAIN_PUBLIC_IP}", mainPublicIp);

                data = data.replace("{SURV_DOMAIN_NAME}", survDomain);

                data = data.replace("http://{VOD_DOMAIN_NAME}:{VOD_NGINX_PORT}", "http://" + vod);
                data = data.replace("https://{VOD_DOMAIN_NAME}:{VOD_NGINX_SSL_PORT}", "http://" + vod);
            } else if ("38/https/wss".equals(scheme)) {
                //兼容3.8配置
                data = data.replace("http://{MAIN_DOMAIN_NAME}:{NGINX_PORT}", "http://" + main);
                data = data.replace("https://{MAIN_DOMAIN_NAME}:{NGINX_SSL_PORT}", "http://" + main);
                data = data.replace("ws://{MAIN_DOMAIN_NAME}:{NGINX_PORT}", "wss://" + mainSsl);
                data = data.replace("wss://{MAIN_DOMAIN_NAME}:{NGINX_SSL_PORT}", "wss://" + mainSsl);
                data = data.replace("{MAIN_DOMAIN_NAME}:{NGINX_PORT}", main);
                data = data.replace("{MAIN_DOMAIN_NAME}:{NGINX_SSL_PORT}", main);
                data = data.replace("{MAIN_PUBLIC_IP}", mainPublicIp);

                data = data.replace("http://{VOD_DOMAIN_NAME}:{VOD_NGINX_PORT}", "http://" + vod);
                data = data.replace("https://{VOD_DOMAIN_NAME}:{VOD_NGINX_SSL_PORT}", "http://" + vod);


            } else {
                data = data.replace("http://{MAIN_DOMAIN_NAME}:{NGINX_PORT}", "http://" + main);
                data = data.replace("https://{MAIN_DOMAIN_NAME}:{NGINX_SSL_PORT}", "https://" + mainSsl);
                data = data.replace("ws://{MAIN_DOMAIN_NAME}:{NGINX_PORT}", "ws://" + main);
                data = data.replace("wss://{MAIN_DOMAIN_NAME}:{NGINX_SSL_PORT}", "wss://" + mainSsl);
                data = data.replace("{MAIN_DOMAIN_NAME}:{NGINX_PORT}", main);
                data = data.replace("{MAIN_DOMAIN_NAME}:{NGINX_SSL_PORT}", mainSsl);
                data = data.replace("{MAIN_PUBLIC_IP}", mainPublicIp);

                data = data.replace("{SURV_DOMAIN_NAME}", survDomain);

                data = data.replace("http://{VOD_DOMAIN_NAME}:{VOD_NGINX_PORT}", "http://" + vod);
                data = data.replace("https://{VOD_DOMAIN_NAME}:{VOD_NGINX_SSL_PORT}", "https://" + vodSsl);
            }
            params.add(data);
        }

        jdbcUtilsForEnvSetting.configureDefaultServerConfig(params, ainemoConnection);
    }

    public void updateEduConfig(Map<String, String> allIp, Connection eduConnection) {
        String scheme = allIp.get("SCHEME");
        scheme = StringUtils.isBlank(scheme) ? "default" : scheme;

        /**
         * 教育配置
         * education.edu_resource_organization resource_host
         * filemanage.repository url_host
         * filemanage.file_manage_upload_account view_host
         * 这几个配置主要影响web 访问教育相关资源文件(图片、视频、文档等)
         * 在点播量比较大的情况下，为避免影响教育nginx(业务请求)及后期扩张，可以将文件流拆分，单独走openresty-edu-file/vod-proxy
         * 如果没有特别配置,默认使用edu节点的nginx
         */
        Map<String, String> allIpMap = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        Map<String, String> udMap = k8sService.getConfigmap("all-file-manage-ud");

        String mainInternalIp, edu, eduNgPort, fm, fmNgport;
        boolean http = "http/ws".equals(scheme);
        mainInternalIp = allIpMap.get(NetworkConstants.MAIN_INTERNAL_IP);

        edu = allIpMap.get("EDU_DOMAIN_NAME");

        eduNgPort = http ? allIpMap.get("EDU_NGINX_PORT") : allIpMap.get("EDU_NGINX_SSL_PORT");

        fm = StringUtils.isNotBlank(udMap.get("EDU_FILE_PLAY_DOMAIN")) ? udMap.get("EDU_FILE_PLAY_DOMAIN") : edu;

        fmNgport = http ?
                (StringUtils.isNotBlank(udMap.get("EDU_FILE_PLAY_PORT")) ? udMap.get("EDU_FILE_PLAY_PORT") : eduNgPort) :
                (StringUtils.isNotBlank(udMap.get("EDU_FILE_PLAY_SSL_PORT")) ? udMap.get("EDU_FILE_PLAY_SSL_PORT") : eduNgPort);

        if (StringUtils.isBlank(eduNgPort)) eduNgPort = http ? "80" : "443";
        if (StringUtils.isBlank(fmNgport)) fmNgport = http ? "80" : "443";

        jdbcUtilsForEnvSetting.configureEduDomain(mainInternalIp, edu, eduNgPort, fm, fmNgport, http ? "http" : "https", eduConnection);
    }

    /**
     * 获取当前数据库的服务状态
     *
     * @return
     */
    public boolean getDbPodStatus() {
        String dbType = k8sService.getDataBaseType();
        String label;

        if ("ST".equalsIgnoreCase(dbType)) {
            label = Constants.POD_NAME_ST;
        } else if ("DM".equalsIgnoreCase(dbType)) {
            label = Constants.POD_NAME_DM;
        } else if ("JC".equalsIgnoreCase(dbType) || "OB".equalsIgnoreCase(dbType)) {
            return true;
        } else {
            label = Constants.POD_NAME_MYSQL;
        }
        try {
            return "Running".equalsIgnoreCase(deployService.listPodsByAppLabel(label).get(0).getStatusPhase());
        } catch (Exception e) {
            logger.warn("getDbPodStatus dbType:{},exception : {}", dbType, e.getMessage());
        }
        return false;
    }

    /**
     * 获取当前环境涉及到数据库操作的节点列表，Map<节点类型,该节点类型的NodeDto列表>
     *
     * @return
     */
    public Map<String, List<NodeDto>> getNodeDtoListForDb(Map<String, String> allIp) {
        Map<String, List<NodeDto>> nodeTypeToNodeDtoList = new HashMap<>();
        Map<String, Map<String, String>> nodeTypeToCM = new HashMap<>();

        Map<String, String> allWebrtc = k8sService.getConfigmap(Constants.CONFIGMAP_WEBRTC);

        List<String> distributedLabellist = Labels.distributedLabels();
        List<Node> nodeList = deployService.listAllNodes();
        for (Node item : nodeList) {
            NodeDto nodeDto = generateNodeDto(item, distributedLabellist, allIp, allWebrtc, nodeTypeToCM);
            if (nodeTypeListForDb.contains(nodeDto.getType())) {
                handleSpecialType(nodeDto, nodeTypeToNodeDtoList);
                String type = getType(nodeDto);
                List<NodeDto> dtoList = nodeTypeToNodeDtoList.get(type);
                if (CollectionUtils.isEmpty(dtoList)) {
                    dtoList = new ArrayList<>();
                }
                dtoList.add(nodeDto);
                nodeTypeToNodeDtoList.put(type, dtoList);
            }
        }

        return nodeTypeToNodeDtoList;
    }

    /**
     * 针对vod和main等特殊类型进行特殊处理，vod有recordMonitor处理逻辑，main有dmcu处理逻辑
     *
     * @param nodeDto
     * @param nodeTypeToNodeDtoList
     */
    private void handleSpecialType(NodeDto nodeDto, Map<String, List<NodeDto>> nodeTypeToNodeDtoList) {
        if (Labels.vod.label().equals(nodeDto.getType())) {
            List<NodeDto> records = nodeTypeToNodeDtoList.get(Labels.record.label());
            if (CollectionUtils.isEmpty(records)) {
                records = new ArrayList<>();
            }
            records.add(nodeDto);
            nodeTypeToNodeDtoList.put(Labels.record.label(), records);
        } else if (Constants.NODETYPE_MAIN.equals(nodeDto.getType())) {
            List<NodeDto> dmcus = nodeTypeToNodeDtoList.get(Labels.dmcu.label());
            if (CollectionUtils.isEmpty(dmcus)) {
                dmcus = new ArrayList<>();
            }
            dmcus.add(nodeDto);
            nodeTypeToNodeDtoList.put(Labels.dmcu.label(), dmcus);
        }
    }

    private String getType(NodeDto nodeDto) {
        String type = nodeDto.getType();
        if (checkNeedDmcuMonitor(nodeDto.getType())) {
            type = Labels.dmcu.label();
        } else if (checkNeedRecordMonitor(nodeDto.getType())) {
            type = Labels.record.label();
        } else if (checkNeedEduConfig(nodeDto.getType())) {
            type = Labels.edu.label();
        } else if (checkNeedNodeHandler(nodeDto.getType())) {
            type = TYPE_NODE_HANDLER;
        }
        return type;
    }

    private boolean checkNeedRecordMonitor(String nodeType) {
        return Labels.record.label().equals(nodeType) ||
                Labels.record_x86.label().equals(nodeType) ||
                Labels.record_arm.label().equals(nodeType);
    }

    /**
     * 检查当前node类型是否需要dmcu监控
     *
     * @param nodeType
     * @return
     */
    private boolean checkNeedDmcuMonitor(String nodeType) {
        return Labels.main_partner.label().equalsIgnoreCase(nodeType) ||
                Labels.dmcu.label().equalsIgnoreCase(nodeType) ||
                Labels.dmcu_x86.label().equalsIgnoreCase(nodeType) ||
                Labels.dmcu_arm.label().equalsIgnoreCase(nodeType) ||
                Labels.dmcu_side.label().equalsIgnoreCase(nodeType) ||
                Labels.dmcu_side_x86.label().equalsIgnoreCase(nodeType);
    }

    /**
     * 检查当前node类型是否需要更新edu域名配置
     *
     * @param nodeType
     * @return
     */
    private boolean checkNeedEduConfig(String nodeType) {
        return Labels.edu.label().equals(nodeType) || Labels.edu_file.label().equals(nodeType);
    }

    /**
     * 检查当前node类型是否需要 NodeHandler的afterConfigure()方法操作数据库
     *
     * @param nodeType
     * @return
     */
    private boolean checkNeedNodeHandler(String nodeType) {
        return Labels.uaa.label().equals(nodeType)
                || Labels.hadoop.label().equals(nodeType)
                || Labels.webrtc.label().equals(nodeType)
                || Labels.ippbx_siggw.label().equals(nodeType);
    }

    private NodeDto generateNodeDto(Node item, List<String> distributedLabellist, Map<String, String> allIp, Map<String, String> allWebrtc, Map<String, Map<String, String>> nodeTypeToCM) {
        NodeDto nodeDto = new NodeDto();
        nodeDto.setName(item.getName());

        //UI show node type  可优化
        nodeDto.setType(item.getType());
        nodeDto.setReportInternalIp(item.getIp());
        nodeDto.setInternalIp(nodeDto.getReportInternalIp());
        if (Constants.NODE_TYPE_COMMON_MAIN.equalsIgnoreCase(nodeDto.getType())) {
            //common节点，main nginx与vod nginx都是本机的IP
            nodeDto.setDomain(allIp.get(NetworkConstants.COMMON_MAIN_DOMAIN_NAME));
            nodeDto.setInternalIp(allIp.get(NetworkConstants.COMMON_MAIN_IP));
            nodeDto.setExternalIp(allIp.get(NetworkConstants.COMMON_MAIN_PUBLIC_IP));
            nodeDto.setNginxPort(allIp.get(NetworkConstants.MAIN_NGINX_PORT));
            nodeDto.setNginxSslPort(allIp.get(NetworkConstants.MAIN_NGINX_SSL_PORT));
            nodeDto.setCommonNodeVodNginxPort(NetworkConstants.VOD_NGINX_PORT);
            nodeDto.setCommonNodeVodNginxSslPort(NetworkConstants.VOD_NGINX_SSL_PORT);
        }
        if (Constants.NODETYPE_MAIN.equalsIgnoreCase(nodeDto.getType())) {
            nodeDto.setDomain(allIp.get(NetworkConstants.MAIN_DOMAIN_NAME));
            nodeDto.setNginxPort(allIp.get(NetworkConstants.MAIN_NGINX_PORT));
            nodeDto.setNginxSslPort(allIp.get(NetworkConstants.MAIN_NGINX_SSL_PORT));
            nodeDto.setInternalIp(allIp.get(NetworkConstants.MAIN_IP));
            nodeDto.setExternalIp(allIp.get(NetworkConstants.MAIN_PUBLIC_IP));
        } else if (Labels.vod.label().equalsIgnoreCase(nodeDto.getType())) {
            nodeDto.setDomain(allIp.get(NetworkConstants.VOD_DOMAIN_NAME));
            nodeDto.setNginxPort(allIp.get(NetworkConstants.VOD_NGINX_PORT));
            nodeDto.setNginxSslPort(allIp.get(NetworkConstants.VOD_NGINX_SSL_PORT));
            nodeDto.setInternalIp(allIp.get(NetworkConstants.VOD_INTERNAL_IP));
            nodeDto.setExternalIp(allIp.get(NetworkConstants.VOD_PUBLIC_IP));
        } else if (Labels.surv.label().equalsIgnoreCase(nodeDto.getType())) {
            nodeDto.setDomain(allIp.get(NetworkConstants.SURV_DOMAIN_NAME));
            nodeDto.setNginxPort(allIp.get(NetworkConstants.SURV_NGINX_PORT));
            nodeDto.setNginxSslPort(allIp.get(NetworkConstants.SURV_NGINX_SSL_PORT));
            nodeDto.setInternalIp(allIp.get(NetworkConstants.SURV_INTERNAL_IP));
            nodeDto.setExternalIp(allIp.get(NetworkConstants.SURV_PUBLIC_IP));
        } /*else if (Labels.shuttle.label().equalsIgnoreCase(nodeDto.getType())) {
            nodeDto.setDomain(allIp.get(NetworkConstants.SHUTTLE_DOMAIN_NAME));
            nodeDto.setInternalIp(allIp.get(NetworkConstants.SHUTTLE_INTERNAL_IP));
            nodeDto.setExternalIp(allIp.get(NetworkConstants.SHUTTLE_PUBLIC_IP));
        } */ else if (Labels.mysql.label().equalsIgnoreCase(nodeDto.getType())) {
            nodeDto.setNodeDbPort(allIp.get(NetworkConstants.DATABASE_PORT));
        } else if (Labels.edu.label().equalsIgnoreCase(nodeDto.getType())) {
            nodeDto.setDomain(allIp.get(NetworkConstants.EDU_DOMAIN_NAME));
            nodeDto.setNginxPort(allIp.get(NetworkConstants.EDU_NGINX_PORT));
            nodeDto.setNginxSslPort(allIp.get(NetworkConstants.EDU_NGINX_SSL_PORT));
            nodeDto.setExternalIp(allIp.get(NetworkConstants.EDU_PUBLIC_IP));
        } else if (Labels.ippbx.label().equalsIgnoreCase(nodeDto.getType())) {
            nodeDto.setInternalIp(allIp.get(NetworkConstants.IPPBX_INTERNAL_IP));
            nodeDto.setExternalIp(allIp.get(NetworkConstants.IPPBX_PUBLIC_IP));
            nodeDto.setDomain(allIp.get(NetworkConstants.IPPBX_DOMAIN));
        } else if (Labels.dns_inner.label().equalsIgnoreCase(nodeDto.getType())) {
            nodeDto.setInternalIp(allIp.get(NetworkConstants.INNER_DNS_INTERNAL_IP));
            nodeDto.setExternalIp(allIp.get(NetworkConstants.INNER_DNS_PUBLIC_IP));
        } else if (Labels.dns_outer.label().equalsIgnoreCase(nodeDto.getType())) {
            nodeDto.setInternalIp(allIp.get(NetworkConstants.OUTER_DNS_INTERNAL_IP));
            nodeDto.setExternalIp(allIp.get(NetworkConstants.OUTER_DNS_PUBLIC_IP));
        } else if (Labels.webrtc.label().equalsIgnoreCase(nodeDto.getType())) {
            nodeDto.setExternalIp(allWebrtc.get(nodeDto.getName() + "-" + NetworkConstants.WEBRTC_PUBLIC_IP));
            nodeDto.setDomain(allWebrtc.get(nodeDto.getName() + "-" + NetworkConstants.WEBRTC_DOMAIN_NAME));
            nodeDto.setNginxPort(allWebrtc.get(nodeDto.getName() + "-" + NetworkConstants.WEBRTC_NGINX_PORT));
            nodeDto.setNginxSslPort(allWebrtc.get(nodeDto.getName() + "-" + NetworkConstants.WEBRTC_NGINX_SSL_PORT));
        } else if (Labels.edu_file.label().equalsIgnoreCase(nodeDto.getType())) {
            nodeDto.setDomain(allIp.get(NetworkConstants.EDU_FILE_DOMAIN_NAME));
            nodeDto.setExternalIp(allIp.get(NetworkConstants.EDU_FILE_PUBLIC_IP));
        } else if (Labels.uaa.label().equalsIgnoreCase(nodeDto.getType())) {
            String uaaInternalIp = allIp.get(NetworkConstants.UAA_INTERNAL_IP);
            if (StringUtils.isNotBlank(uaaInternalIp)) {
                nodeDto.setInternalIp(uaaInternalIp);
            }
        } else if (Labels.statis.label().equalsIgnoreCase(nodeDto.getType())) {
            String statisInternalIp = allIp.get(NetworkConstants.STATIS_INTERNAL_IP);
            if (StringUtils.isNotBlank(statisInternalIp)) {
                nodeDto.setInternalIp(statisInternalIp);
            }
        } else if (Labels.database.label().equalsIgnoreCase(nodeDto.getType())) {
            nodeDto.setInternalIp(allIp.get(NetworkConstants.MASTER_DATABASE_IP));
        } else if (Labels.sdk_file.label().equalsIgnoreCase(nodeDto.getType())) {
            nodeDto.setDomain(allIp.get(NetworkConstants.SDK_FILE_DOMAIN_NAME));
            nodeDto.setNginxPort(allIp.get(NetworkConstants.SDK_FILE_NGINX_PORT));
            nodeDto.setNginxSslPort(allIp.get(NetworkConstants.SDK_FILE_NGINX_SSL_PORT));
            nodeDto.setExternalIp(allIp.get(NetworkConstants.SDK_FILE_PUBLIC_IP));
            nodeDto.setInternalIp(allIp.get(NetworkConstants.SDK_FILE_INTERNAL_IP));
        }


        if (StringUtils.isNotBlank(nodeDto.getType())) {
            if (nodeDto.getType().equalsIgnoreCase(Labels.sharing.label())) {
                nodeDto.setExternalIp(allIp.get(NetworkConstants.SHARING_SERVER_PUBLIC_IP));
            } else if (StringUtils.isNotEmpty(Constants.interIps.get(nodeDto.getType()))) {
                nodeDto.setInternalIp(allIp.get(Constants.interIps.get(nodeDto.getType())));

                if (Constants.exterIps.containsKey(nodeDto.getType())) {
                    nodeDto.setExternalIp(allIp.get(Constants.exterIps.get(nodeDto.getType())));
                }
            } else if (distributedLabellist.contains(nodeDto.getType())
                    || nodeDto.getType().equalsIgnoreCase(Labels.main_partner.label()) || Constants.NODE_TYPE_COMMON_MAIN.equalsIgnoreCase(nodeDto.getType())) {
                // dmcu-arm 的内外网/域名信息 从all-dmcu读取
                String nodeType = nodeDto.getType();
                if (nodeDto.getType().equalsIgnoreCase(Labels.dmcu_x86.label()) || nodeDto.getType().equalsIgnoreCase(Labels.dmcu_arm.label()) || nodeDto.getType().equalsIgnoreCase(Labels.dmcu_side.label()) || nodeDto.getType().equalsIgnoreCase(Labels.dmcu_side_x86.label())) {
                    nodeType = Labels.dmcu.label();
                } else if (nodeDto.getType().equalsIgnoreCase(Labels.main_proxy_x86.label()) || nodeDto.getType().equalsIgnoreCase(Labels.main_proxy_arm.label())) {
                    nodeType = Labels.main_proxy.label();
                } else if (nodeDto.getType().equalsIgnoreCase(Labels.record_x86.label()) || nodeDto.getType().equalsIgnoreCase(Labels.record_arm.label())) {
                    nodeType = Labels.record.label();
                } else if (nodeDto.getType().equalsIgnoreCase(Labels.h323_x86.label()) || nodeDto.getType().equalsIgnoreCase(Labels.h323_arm.label())) {
                    nodeType = Labels.h323.label();
                } else if (nodeDto.getType().equalsIgnoreCase(Labels.rtmp.label())) {
                    nodeType = Labels.converged_mediagw.label();
                }
                String key = "all-" + nodeType;
                Map<String, String> configMapData = nodeTypeToCM.get(key);
                if (CollectionUtils.isEmpty(configMapData)) {
                    configMapData = k8sService.getConfigmapOrCreate(key);
                    nodeTypeToCM.put(key, configMapData);
                }

                String nodeInterIpKey = item.getName() + NetworkConstants.SUFFIX_INTERNAL_IP;
                String nodePubIpKey = item.getName() + NetworkConstants.SUFFIX_PUBLIC_IP;
                String nodeDomainKey = item.getName() + NetworkConstants.SUFFIX_DOMAIN;

                nodeDto.setInternalIp(configMapData.get(nodeInterIpKey));
                nodeDto.setExternalIp(configMapData.get(nodePubIpKey));
                nodeDto.setDomain(configMapData.get(nodeDomainKey));
            } else if (Labels.transcript.label().equalsIgnoreCase(nodeDto.getType())) {
                nodeDto.setInternalIp(allIp.get(NetworkConstants.TRANSCRIPTION_INTERNAL_IP));
                nodeDto.setExternalIp(allIp.get(NetworkConstants.TRANSCRIPTION_PUBLIC_IP));
            }
        }

        List<FeatureDto> featureDtos = serverListService.getFeatureList(nodeDto.getType(), null);
        for (FeatureDto featureDto : featureDtos) {
            if (item.getLabels().containsKey(featureDto.getFeature())) {
                nodeDto.getLabelMap().put(featureDto.getFeature(), true);
            } else {
                nodeDto.getLabelMap().put(featureDto.getFeature(), false);
            }
        }

        return nodeDto;
    }
}