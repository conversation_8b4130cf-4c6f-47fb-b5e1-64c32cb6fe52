package com.xylink.manager.service.dto;

import com.xylink.manager.controller.dto.ISearchDto;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @create 2024/4/19 16:28
 */
@Data
public class SDKLibraryInfoDTO implements ISearchDto {
    /**
     * 文件名称
     */
    private String sdkName;


    private String platform;
    private Long buildTime;

    /**
     * 架构
     */
    private String abiType;
    private String relativePath;

    /**
     * 版本
     */
    private String version;
    private String md5;

    @Override
    public boolean containSearchContent(String key) {
        String content = sdkName + ";" + this.version + ";" + this.abiType;
        return StringUtils.isNotBlank(content) && content.contains(key);
    }
}
