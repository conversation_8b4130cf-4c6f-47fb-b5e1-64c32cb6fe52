package com.xylink.manager.service.dto.advance;

import com.xylink.manager.model.advanced.AdvanceConfigOption;
import com.xylink.manager.model.advanced.AdvancedConfigItemEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/1/16 15:48
 */
@Data
public class AdvanceConfigTemplateDto {
   private String itemType;
   private String id;
   private String label;
   private String extra;
   private boolean required;
   private List<AdvanceConfigOption> options;

   public static AdvanceConfigTemplateDto transform(AdvancedConfigItemEnum itemEnum) {
      AdvanceConfigTemplateDto res = new AdvanceConfigTemplateDto();
      res.setItemType(itemEnum.getItemType().name());
      res.setId(itemEnum.getId());
      res.setLabel(itemEnum.getLabel());
      res.setExtra(itemEnum.getExtra());
      res.setRequired(itemEnum.isRequired());
      res.setOptions(itemEnum.getOptions());
      return res;
   }
}
