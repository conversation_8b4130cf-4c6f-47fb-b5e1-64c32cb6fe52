package com.xylink.manager.service.haproxy;

import com.xylink.config.HaproxyConstants;
import com.xylink.manager.controller.dto.HaproxyAutoSwitchDto;
import com.xylink.manager.controller.dto.HaproxyFarmsDto;
import com.xylink.manager.controller.dto.HaproxyServerStatusDto;
import com.xylink.util.HaproxyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author: liyang
 * @DateTime: 2021/11/9 9:34 下午
 **/
@Component
@Slf4j
public class HaproxyMasterRunable implements Runnable  {
    @Autowired
    private HaproxyService haproxyServcie;

    private AtomicInteger currentNum = new AtomicInteger();

    @Override
    public void run() {
        try{
            HaproxyAutoSwitchDto autoSwitchConfig = haproxyServcie.getAutoSwitchConfig();
            String currentMode = autoSwitchConfig.getCurrentMode();
            String serverList = autoSwitchConfig.getServerList();
            if ("slave".equals(currentMode)){
                return;
            }
            checkStatus(currentMode, serverList);
            if (currentNum.get() >= autoSwitchConfig.getSwitchFreq()) {
                haproxyServcie.addHaproxyEvent(haproxyServcie.getAllHaproxyName(), HaproxyConstants.LEVEL_WARNING,"Auto switchover occurs from master to slave.");
                haproxyServcie.switchHaproxyStatus(currentMode);
            }
        }catch (Exception e){
            log.error("master system haproxyRunable error ",e);
        }
    }

    public void checkStatus(String currentMode,String serverList){
        boolean flag = true;
        if (haproxyServcie.isServerNotRunning(serverList) || !checkAllServerStatus(currentMode)) {
            flag = false;
        }
        if (flag){
            haproxyRecoveryEvent();
            initCurrentNum();
        }else {
            currentNum.incrementAndGet();
        }
    }

    public boolean checkAllServerStatus(String currentMode) {
        AtomicBoolean flag = new AtomicBoolean(true);
        haproxyServcie.allHaproxyHostIp().forEach(ip ->
            haproxyServcie.getAllFarms(ip).stream().filter(f -> !f.getName().contains("webrtc")).forEach(farm -> {
                String bkName = farm.getName();

                long serverUp = farm.getServers().stream().map(HaproxyFarmsDto.Servers::getName)
                        .filter(name -> name.contains(currentMode))
                        .filter(x -> checkSingleServerStatus(ip, x, bkName)).count();

                if (serverUp == 0){
                    flag.set(false);
                }
            })
        );
        return flag.get();
    }

    private void haproxyRecoveryEvent(){
        if (currentNum.get() > 0) {
            haproxyServcie.addHaproxyEvent(haproxyServcie.getAllHaproxyName(),HaproxyConstants.LEVEL_INFO,"backend,the flow returns to normal.Service state recovery.");
        }
    }

    public boolean checkSingleServerStatus(String ip,String serverName,String bkName){
        String url = HaproxyUtil.getRuntimeServerStatusUrl(ip) + serverName + "?backend=" + bkName;
        try {
            ResponseEntity<HaproxyServerStatusDto> entity = haproxyServcie.requestHaproxyApi(url, HttpMethod.GET, null, HaproxyServerStatusDto.class);
            HaproxyServerStatusDto body = entity.getBody();
            if (Objects.nonNull(body) && !body.getOperationalState().equalsIgnoreCase("up")) {
                String nodeName = haproxyServcie.getHaproxyNameAndIp().get(ip);
                String detail = "Warning, backend flow switchover occurs.<--->backend name : " + bkName + ",server name : " + serverName + ".";
                haproxyServcie.addHaproxyEvent(nodeName, HaproxyConstants.LEVEL_WARNING, detail);
                return false;
            }
        } catch (Exception e) {
            log.error("check server error,backend name:{},server name:{}", bkName, serverName, e);
        }
        return true;
    }

    public void initCurrentNum(){
        currentNum.set(0);
    }

}
