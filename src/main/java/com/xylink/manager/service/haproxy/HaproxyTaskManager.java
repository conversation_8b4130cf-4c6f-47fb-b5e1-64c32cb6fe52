package com.xylink.manager.service.haproxy;

import com.xylink.manager.controller.dto.HaproxyAutoSwitchDto;
import com.xylink.manager.model.SystemInfo;
import com.xylink.manager.service.ServiceManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Objects;
import java.util.concurrent.ScheduledFuture;

/**
 * @Author: liyang
 * @DateTime: 2021/10/13 6:54 下午
 **/
@Component
@Slf4j
public class HaproxyTaskManager {


    @Autowired
    private ThreadPoolTaskScheduler threadPoolTaskScheduler;

    @Autowired
    private HaproxyService haproxyServcie;

    @Autowired
    private HaproxyMasterRunable haproxyMasterRunable;

    @Autowired
    private HaproxySlaveRunable haproxySlaveRunable;

    @Autowired
    private ServiceManageService serviceManageService;

    private ScheduledFuture<?> future;

    @Bean
    public ThreadPoolTaskScheduler threadPoolTaskScheduler() {
        return new ThreadPoolTaskScheduler();
    }

    @PostConstruct
    public void init() {
        try {
            startCron();
        } catch (Exception e) {
            log.error("HaproxyTaskManager postConstruct failed", e);
        }
    }

    public void startCron() {
        HaproxyAutoSwitchDto autoSwitchConfig = haproxyServcie.getAutoSwitchConfig();
        if (!autoSwitchConfig.isAutoSwitch()) {
            return;
        }
        log.info("HaproxyTaskManager.startCron(),period:{}", autoSwitchConfig.getInspectionTime());
        if (systemIsBackupMode()) {
            haproxySlaveRunable.initCurrentNum();
            future = threadPoolTaskScheduler.scheduleAtFixedRate(haproxySlaveRunable, autoSwitchConfig.getInspectionTime() * 1000);
        } else {
            haproxyMasterRunable.initCurrentNum();
            future = threadPoolTaskScheduler.scheduleAtFixedRate(haproxyMasterRunable, autoSwitchConfig.getInspectionTime() * 1000);
        }
    }

    public void stopCron() {
        if (Objects.nonNull(future)) {
            future.cancel(true);
            log.info("HaproxyTaskManager.stopCron()");
        }
    }

    public void restartCron() {
        stopCron();
        startCron();
    }

    private boolean systemIsBackupMode() {
        SystemInfo systemInfo = serviceManageService.loadSystemInfo();
        return Objects.nonNull(systemInfo) && "backup".equals(systemInfo.getType());
    }
}
