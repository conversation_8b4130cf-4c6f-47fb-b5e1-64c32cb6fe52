package com.xylink.manager.service.haproxy;

import com.xylink.config.HaproxyConstants;
import com.xylink.manager.controller.dto.HaproxyAutoSwitchDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author: liyang
 * @DateTime: 2021/11/9 9:34 下午
 **/
@Component
@Slf4j
public class HaproxySlaveRunable implements Runnable{
    @Autowired
    private HaproxyService haproxyServcie;
    @Autowired
    private RestTemplate restTemplate;

    private AtomicInteger currentNum = new AtomicInteger();

    @Override
    public void run() {
        try{
            HaproxyAutoSwitchDto autoSwitchConfig = haproxyServcie.getAutoSwitchConfig();
            String currentMode = autoSwitchConfig.getCurrentMode();
            String serviceUrl = autoSwitchConfig.getServiceUrl();
            if ("slave".equals(currentMode) || StringUtils.isBlank(serviceUrl)){
                return;
            }
            checkServiceUrl(serviceUrl);
            if (currentNum.get() >= autoSwitchConfig.getSwitchFreq()) {
                haproxyServcie.addHaproxyEvent(haproxyServcie.getAllHaproxyName(), HaproxyConstants.LEVEL_WARNING,"Auto switchover occurs from master to slave.");
                //需要走专门的途径
                haproxyServcie.switchHaproxy(currentMode);
            }
        }catch (Exception e){
            log.error("backup system haproxyRunable error ",e);
        }
    }

    public void checkServiceUrl(String serviceUrl){
        boolean allMatch = Arrays.stream(serviceUrl.split(",")).allMatch(this::serviceUrlIsOK);
        if (allMatch){
            initCurrentNum();
        }else {
            currentNum.incrementAndGet();
        }
    }

    private boolean serviceUrlIsOK(String url){
        try {
            ResponseEntity<Object> exchange = restTemplate.getForEntity(url, Object.class);
            return HttpStatus.OK.equals(exchange.getStatusCode());
        }catch (Exception e){
            log.error("backup system service url restTemplate error",e);
        }
        return false;
    }

    public void initCurrentNum(){
        currentNum.set(0);
    }

}
