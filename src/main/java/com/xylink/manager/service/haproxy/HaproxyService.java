package com.xylink.manager.service.haproxy;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.xylink.config.Constants;
import com.xylink.config.HaproxyConstants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServiceErrorException;
import com.xylink.manager.controller.dto.HaproxyAutoSwitchDto;
import com.xylink.manager.controller.dto.HaproxyFarmsDto;
import com.xylink.manager.controller.dto.HaproxyServerStatusDto;
import com.xylink.manager.controller.dto.HaproxySiteDto;
import com.xylink.manager.controller.dto.alert.AlertEventDto;
import com.xylink.manager.model.SystemInfo;
import com.xylink.manager.model.deploy.ConfigMap;
import com.xylink.manager.model.deploy.Node;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.model.em.HaproxyRuleTypeEnum;
import com.xylink.manager.model.haproxy.HaproxyReq;
import com.xylink.manager.service.ServiceManageService;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.config.IServerConfigService;
import com.xylink.util.HaproxyUtil;
import com.xylink.util.WatchEventTasks;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Nullable;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * @Author: liyang
 * @DateTime: 2021/10/12 7:26 下午
 **/
@Service
@Slf4j
public class HaproxyService {
    @Autowired
    private IDeployService deployService;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private HaproxyTaskManager haproxyTaskManager;
    @Autowired
    private K8sService k8sService;
    @Autowired
    private ServiceManageService serviceManageService;
    @Autowired
    private IServerConfigService iServerConfigService;
    @Autowired
    private WatchEventTasks watchEventTasks;
    @Autowired
    private ObjectMapper objectMapper;


    public boolean saveAndRestartHaproxyConfig(HaproxyReq haproxyReq, String confName) {
        String nodeName = haproxyReq.getNodeName();
        boolean enableXFF = haproxyReq.isEnableXFF();
        Map<String, String> cm = new HashMap<>();
        cm.put(nodeName + HaproxyConstants.XFF_SUFFIX, String.valueOf(enableXFF));
        cm.put(nodeName + HaproxyConstants.IPV6_SUFFIX, String.valueOf(haproxyReq.isSupportIpv6()));
        k8sService.editConfigmap(confName, cm);

        //重启pod
        List<Pod> items = deployService.listPodsByAppLabels(Lists.newArrayList("private-haproxy", "private-haproxy-arm"));
        Pod pod = items.stream().filter(x -> x.getNodeName().equalsIgnoreCase(nodeName)).findFirst().orElse(null);
        if (pod != null) {
            deployService.deletePod(pod);
            return true;
        }
        return false;
    }

    public void deleteHaproxyType(String nodeName, String nodeType) {
        List<String> allRemoveKey = HaproxyRuleTypeEnum.getRemoveKey(nodeName, nodeType);
        deployService.patchConfigMap(Constants.CONFIGMAP_ALL_HAPROXY, Constants.NAMESPACE_DEFAULT, d -> {
            allRemoveKey.forEach(d::remove);
        });
    }

    public HaproxyAutoSwitchDto getAutoSwitchConfig() {
        ConfigMap configMap = getAllHaproxyConfigMapResource();
        return toHaproxyAutoSwitchDto(configMap);
    }

    private HaproxyAutoSwitchDto toHaproxyAutoSwitchDto(@Nullable ConfigMap configMap) {
        Map<String, String> data = configMap == null ? new HashMap<>() : configMap.getData();
        String currentMode = null;
        try {
            currentMode = getHaproxyMode();
        } catch (Exception e) {
            log.error("get master system mode failed", e);
        }
        String configMode = StringUtils.isBlank(data.get(HaproxyConstants.CURRENT_MODE)) ? HaproxyConstants.CURRENT_MODE_DEFAULT : data.get(HaproxyConstants.CURRENT_MODE);
        currentMode = StringUtils.isBlank(currentMode) ? configMode : currentMode;

        String autoSwitch = StringUtils.isBlank(data.get(HaproxyConstants.AUTO_SWITCH)) ? HaproxyConstants.AUTO_SWITCH_DEFAULT : data.get(HaproxyConstants.AUTO_SWITCH);
        String inspectionTime = StringUtils.isBlank(data.get(HaproxyConstants.INSPECTION_TIME)) ? HaproxyConstants.INSPECTION_TIME_DEFAULT : data.get(HaproxyConstants.INSPECTION_TIME);
        String switchFreq = StringUtils.isBlank(data.get(HaproxyConstants.SWITCH_FREQ)) ? HaproxyConstants.SWITCH_FREQ_DEFAULT : data.get(HaproxyConstants.SWITCH_FREQ);
        String checkCondition = StringUtils.isBlank(data.get(HaproxyConstants.CHECK_CONDITION)) ? "serverList" : data.get(HaproxyConstants.CHECK_CONDITION);
        String serverList = data.get(HaproxyConstants.SERVER_LIST);
        String serviceUrl = data.get(HaproxyConstants.SERVICE_URL);
        return new HaproxyAutoSwitchDto(currentMode, Boolean.parseBoolean(autoSwitch), Integer.parseInt(inspectionTime), Integer.parseInt(switchFreq), checkCondition, "serviceUrl".equals(checkCondition) ? serviceUrl : serverList);
    }

    public void saveAutoSwitchConfig(HaproxyAutoSwitchDto haproxyAutoSwitchDto) {
        deployService.patchConfigMap(Constants.CONFIGMAP_ALL_HAPROXY, Constants.NAMESPACE_DEFAULT, d -> {
            d.put(HaproxyConstants.AUTO_SWITCH, String.valueOf(haproxyAutoSwitchDto.isAutoSwitch()));
            d.put(HaproxyConstants.INSPECTION_TIME, String.valueOf(haproxyAutoSwitchDto.getInspectionTime()));
            d.put(HaproxyConstants.SWITCH_FREQ, String.valueOf(haproxyAutoSwitchDto.getSwitchFreq()));
            d.put(HaproxyConstants.SERVER_LIST, haproxyAutoSwitchDto.getServerList());
            d.put(HaproxyConstants.SERVICE_URL, haproxyAutoSwitchDto.getServiceUrl());
            d.put(HaproxyConstants.CHECK_CONDITION, haproxyAutoSwitchDto.getCheckCondition());
        });
        haproxyTaskManager.restartCron();
    }

    public void editCurrentModeConfig(String currentMode) {
        String targetMode = getTargetMode(currentMode);
        _editCurrentModeConfig(targetMode);
        editPeerSystem(targetMode);
    }

    public void _editCurrentModeConfig(String targetMode) {
        deployService.patchConfigMap(Constants.CONFIGMAP_ALL_HAPROXY, Constants.NAMESPACE_DEFAULT, d -> {
            d.put(HaproxyConstants.CURRENT_MODE, targetMode);
        });
    }

    public void editPeerSystem(String targetMode) {
        String peerSystem = "";
        try {
            if (this.systemIsBackupMode()) {
                Triple<String, String, String> info = k8sService.getMainDataBaseInfo();
                peerSystem = info.getMiddle();
            } else {
                Triple<String, String, String> info = k8sService.getStandbyMainDataBaseInfo();
                peerSystem = info.getMiddle();
            }
        } catch (Exception e) {
            log.error("Get peer system info error.", e);
        }
        iServerConfigService.editPeerSystemCurrentModeConfig(targetMode, peerSystem);
    }

    public String getTargetMode(String realMode) {
        return "master".equalsIgnoreCase(realMode) ? "slave" : "master";
    }

    @Nullable
    private ConfigMap getAllHaproxyConfigMapResource() {
        return deployService.getConfigMapByName(Constants.CONFIGMAP_ALL_HAPROXY, Constants.NAMESPACE_DEFAULT);
    }

    /**
     * 修改所有部署的的haproxy的主备(主系统下使用)
     */
    public void switchHaproxyStatus(String currentMode) {
        //当前需要切换的状态已经实行（其他用户修改了配置，而当前页面没有更新）
        HaproxyAutoSwitchDto autoSwitchConfig = getAutoSwitchConfig();
        if (!currentMode.equalsIgnoreCase(autoSwitchConfig.getCurrentMode())) {
            log.warn("request currentMode is {},query currentMode is {},please refresh page!!!", currentMode, autoSwitchConfig.getCurrentMode());
            return;
        }
        //当前为备需要切换为主，进行服务器列表检测
        if ("slave".equalsIgnoreCase(currentMode) && checkServerListNotRunning()) {
            throw new ServiceErrorException(ErrorStatus.HAPROXY_SWICTH_STATUS_ERROR);
        }
        editCurrentModeConfig(currentMode);
        databaseChange(currentMode);
        //切换主/备，需要拿到所有的haproxy主机ip
        allHaproxyHostIp().forEach(ip -> {
            //每一个haproxy的ip都要将他们的的主备状态切换
            try {
                switchSingleHaproxyStatus(ip, currentMode);
            } catch (Exception e) {
                log.error("haproxy ip {} switch error", ip, e);
            }
        });
        haproxyTaskManager.restartCron();
    }

    /**
     * 修改所有部署的的haproxy的主备(备系统下使用)
     */
    public void switchHaproxy(String currentMode) {
        Map<String, String> configmap = k8sService.getConfigmap(Constants.CONFIGMAP_ALL_HAPROXY);
        List<String> collect = configmap.keySet().stream().filter(x -> x.contains("INTERNAL-IP")).collect(Collectors.toList());
        databaseChange(currentMode);
        editCurrentModeConfig(currentMode);
        collect.forEach(key -> {
            //每一个haproxy的ip都要将他们的的主备状态切换
            String ip = configmap.get(key);
            try {
                switchSingleHaproxyStatus(ip, currentMode);
            } catch (Exception e) {
                log.error("haproxy ip {} switch error", ip, e);
            }
        });
        haproxyTaskManager.restartCron();
    }

    public List<String> allHaproxyHostIp() {
        return new ArrayList<>(getHaproxyNameAndIp().keySet());
    }

    public String getAllHaproxyName() {
        StringBuilder sb = new StringBuilder();
        getHaproxyNameAndIp().values().forEach(name -> sb.append(",").append(name));
        return sb.toString().replaceFirst(",", "");
    }

    public Map<String, String> getHaproxyNameAndIp() {
        Map<String, String> haproxyNameAndIp = new HashMap<>();
        Map<String, String> config = k8sService.getConfigmapOrCreate(Constants.CONFIGMAP_ALL_HAPROXY);
        config.entrySet().stream().filter(x -> x.getKey().contains("INTERNAL-IP"))
                .forEach(entry -> haproxyNameAndIp.put(entry.getValue(), entry.getKey().replace("-INTERNAL-IP", "")));
        return haproxyNameAndIp;
    }

    /**
     * 每一次的切换都需要down当前系统，up目标系统
     */
    public void switchSingleHaproxyStatus(String ip, String currentMode) {
        List<HaproxyFarmsDto> allFarms = getAllFarms(ip);
        allFarms.forEach(farm -> {
            String bkName = farm.getName();
            List<String> serverNameList = farm.getServers().stream().map(HaproxyFarmsDto.Servers::getName).collect(Collectors.toList());
            //先开启另一个server
            serverNameList.stream().filter(x -> !x.contains(currentMode)).forEach(serverName -> {
                String url = HaproxyUtil.getSwitchStatusUrl(ip) + serverName + "?backend=" + bkName;
                try {
                    requestHaproxyApi(url, HttpMethod.PUT, HaproxyUtil.PARAM_READY_STATUS, JsonNode.class);
                } catch (Exception e) {
                    log.error("up server error,ip:{},backend name:{},server name:{}", ip, bkName, serverName, e);
                }
            });
            //再关闭当前server
            serverNameList.stream().filter(x -> x.contains(currentMode)).forEach(serverName -> {
                String url = HaproxyUtil.getSwitchStatusUrl(ip) + serverName + "?backend=" + bkName;
                try {
                    requestHaproxyApi(url, HttpMethod.PUT, HaproxyUtil.PARAM_MAINT_STATUS, JsonNode.class);
                } catch (Exception e) {
                    log.error("down server error,ip:{},backend name:{},server name:{}", ip, bkName, serverName, e);
                }
            });
        });
    }

    public List<HaproxyFarmsDto> getAllFarms(String ip) {
        List<HaproxyFarmsDto> allFarms = new ArrayList<>();
        getAllSites(ip).forEach(sites -> allFarms.addAll(sites.getFarms()));
        return allFarms;
    }

    public List<HaproxySiteDto> getAllSites(String ip) {
        HttpEntity<JsonNode> ans = requestHaproxyApi(HaproxyUtil.getSitesUrl(ip), HttpMethod.GET, null, JsonNode.class);
        List<HaproxySiteDto> resultList = (Objects.isNull(ans) || ObjectUtils.isEmpty(ans.getBody())
                || ans.getBody().get("data").isEmpty()) ? new ArrayList<>() :
                objectMapper.convertValue(ans.getBody().get("data"), new TypeReference<List<HaproxySiteDto>>() {
                });
        log.info("getAllSites from ip {} , data size:{}", ip, resultList.size());
        return resultList;
    }

    public <T> ResponseEntity<T> requestHaproxyApi(String url, HttpMethod httpMethod, Map<String, String> requestParam, Class<T> responseType) {
        //在请求头信息中携带Basic认证信息
        HttpHeaders headers = new HttpHeaders();
        headers.set("authorization", "Basic " + Base64.getEncoder().encodeToString(HaproxyConstants.DATA_PLANE_API_PWD.getBytes()));
        try {
            return restTemplate.exchange(url, httpMethod, new HttpEntity<>(requestParam, headers), responseType);
        } catch (Exception e) {
            log.error("requestHaproxyApi error,url:{}", url, e);
        }
        return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    //当备手动切换为主，需要检查服务列表中服务时候存在非running状态
    public boolean checkServerListNotRunning() {
        String serverList = getAutoSwitchConfig().getServerList();
        if (StringUtils.isBlank(serverList)) {
            return false;
        }
        return isServerNotRunning(serverList);
    }

    private String getMasterName() {
        List<Node> items = deployService.listNodesByLabels("type", "main");
        return items.stream().findFirst().map(Node::getName).get();
    }

    public boolean isServerNotRunning(String serverList) {
        if (StringUtils.isBlank(serverList)) {
            return false;
        }
        AtomicBoolean flag = new AtomicBoolean(false);
        String[] split = serverList.trim().split(",");
        Arrays.stream(split).forEach(x -> {
            List<Pod> podList = deployService.listPodsByAppLabel(x);
            if (CollectionUtils.isEmpty(podList)) {
                addHaproxyEvent(x, HaproxyConstants.LEVEL_WARNING, "The server in the server list is abnormal.server:" + x);
                flag.set(true);
            }
            long count = podList.stream().filter(pod -> !podCheckAndAddEvent(pod)).count();
            if (podList.size() == count) {
                flag.set(true);
            }
        });
        return flag.get();
    }

    private boolean podCheckAndAddEvent(Pod pod) {
        boolean podIsRunning = podIsRunning(pod);
        if (!podIsRunning) {
            addHaproxyEvent(pod.getPodName(), HaproxyConstants.LEVEL_WARNING, "The server in the server list is abnormal.server:" + pod.getPodName());
        }
        return podIsRunning;
    }

    private boolean podIsRunning(Pod pod) {
        String s = pod.getConditions().stream()
                .filter(it -> it.getType().equalsIgnoreCase(Constants.STATUS_READY))
                .findFirst()
                .map(Pod.Condition::getStatus)
                .orElse("");
        return "true".equalsIgnoreCase(s);
    }

    public void addHaproxyEvent(String serviceName, String level, String detail) {
        try {
            AlertEventDto haproxyEvent = new AlertEventDto();
            long nowTime = System.currentTimeMillis();
            haproxyEvent.setCreateTime(new Date(nowTime).toString());
            String finalDetail = detail + haproxyEvent.getCreateTime();
            haproxyEvent.setReaded(false);
            haproxyEvent.setTime(Instant.now());
            haproxyEvent.setServiceName(serviceName);
            haproxyEvent.setDetail(finalDetail);
            haproxyEvent.setLevel(level);
            haproxyEvent.setReason("");
            log.info(haproxyEvent.toString());
            watchEventTasks.addHaproxyAlertEvent(haproxyEvent);
        } catch (Exception e) {
            log.error("addHaproxyEvent error.", e);
        }
    }

    public boolean haproxyIsNotAvailable() {
        return deployService.listPodsByLabels("app", new String[]{"private-haproxy", "private-haproxy-arm"}).isEmpty() && ObjectUtils.isEmpty(k8sService.getConfigmap(Constants.CONFIGMAP_ALL_HAPROXY));
    }

    public boolean systemIsBackupMode() {
        SystemInfo systemInfo = serviceManageService.loadSystemInfo();
        return Objects.nonNull(systemInfo) && "backup".equals(systemInfo.getType());
    }

    @Nullable
    public String getHaproxyMode() {
        List<Pod> pods = deployService.listPodsByNamespaceAndLabels(Constants.NAMESPACE_DEFAULT, "app", new String[]{"private-haproxy", "private-haproxy-arm"});
        List<String> ips = pods.stream().map(Pod::getHostIp).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(ips)) {
            ConfigMap configMap = deployService.getConfigMapByName(Constants.CONFIGMAP_ALL_HAPROXY, Constants.NAMESPACE_DEFAULT);
            if (configMap == null) {
                return null;
            }
            Map<String, String> allHaproxy = configMap.getData();
            ips = allHaproxy.entrySet().stream().filter(entry -> entry.getKey().contains("INTERNAL-IP")).map(Map.Entry::getValue).collect(Collectors.toList());
        }

        return getMode(ips);
    }


    //备系统通过haproxy api来拿到主系统的haproxy主备状态
    public String getMode(List<String> ips) {
        for (String ip : ips) {
            List<HaproxyFarmsDto> farms = getAllFarms(ip);
            if (ObjectUtils.isEmpty(farms)) {
                continue;
            }
            HaproxyFarmsDto haproxyFarmsDto = farms.get(0);
            String bkName = haproxyFarmsDto.getName();
            List<String> serverNameList = haproxyFarmsDto.getServers().stream().map(HaproxyFarmsDto.Servers::getName).collect(Collectors.toList());

            String currentMode = getModeByServerList(serverNameList, ip, bkName);
            if (!StringUtils.isBlank(currentMode)) {
                return currentMode;
            }

            List<String> collect = serverNameList.stream().filter(s -> s.contains("master")).collect(Collectors.toList());
            String url = HaproxyUtil.getServerStatusUrl(ip) + collect.get(0) + "?backend=" + bkName;
            ResponseEntity<JsonNode> requestHaproxyApi = requestHaproxyApi(url, HttpMethod.GET, null, JsonNode.class);
            JsonNode body = requestHaproxyApi.getBody();
            if (Objects.isNull(body)) {
                continue;
            }
            JsonNode data = body.get("data");
            if (!ObjectUtils.isEmpty(data.get("backup"))) {
                return "slave";
            } else {
                return "master";
            }
        }

        return "master";
    }

    private String getModeByServerList(List<String> serverNameList, String ip, String bkName) {
        for (String name : serverNameList) {
            String runtimeUrl = HaproxyUtil.getRuntimeServerStatusUrl(ip) + name + "?backend=" + bkName;
            ResponseEntity<HaproxyServerStatusDto> entity = requestHaproxyApi(runtimeUrl, HttpMethod.GET, null, HaproxyServerStatusDto.class);
            HaproxyServerStatusDto body = entity.getBody();
            if (Objects.isNull(body)) {
                continue;
            }
            if ("maint".equalsIgnoreCase(body.getAdminState()) && name.contains("master")) {
                return "slave";
            }
            if ("maint".equalsIgnoreCase(body.getAdminState()) && name.contains("slave")) {
                return "master";
            }
        }
        return "";
    }

    private void databaseChange(String currentMode){
        iServerConfigService.failover(currentMode);
    }
}
