package com.xylink.manager.service;

import com.xylink.config.NetworkConstants;
import com.xylink.config.util.JsonUtil;
import com.xylink.util.Ipv6Util;
import com.xylink.util.WebHookHttpUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 计算媒体服务的参数 {@link com.xylink.manager.model.cm.DmcuCM}
 *
 * <AUTHOR>
 * @since 2025-07-15 14:25
 */
@Slf4j
@Service
public class CalculateMediaParametersService {
    @Resource(name = "serviceRestTemplate")
    private RestTemplate restTemplate;

    public MediaParameters calculateMediaParameters(String nodeName, String internalIp, String serviceType) {
        try {
            String url = "http://" + Ipv6Util.handlerIpv6Addr(internalIp) + ":"
                    + NetworkConstants.WEB_HOOK + "/hooks/mediaParameters";

            Map<String, String> map = new HashMap<>();
            map.put("serviceType", serviceType);
            String postWebHookResult = WebHookHttpUtil.getStringByPostWebHookResult(restTemplate, url, JsonUtil.toJson(map));
            WebhookMediaParametersDto result = JsonUtil.parseJson(postWebHookResult, WebhookMediaParametersDto.class);
            return result == null ? new MediaParameters() : result.getConfig();
        } catch (Exception e) {
            log.error("call webhook remote error: ", e);
            // 还走媒体服务默认值
            return new MediaParameters();
        }
    }

    @Data
    public static class MediaParameters {
        private String maxRxBandWidth;
        private String maxTxBandWidth;
        private String instanceNum;
        private String mediaProcessTaskNum;
        private String mediaEndpointNumPerMediaTask;
        private String mediaAccessTaskNumber;
    }

    @Data
    private static class WebhookMediaParametersDto {
        private MediaParameters config;
    }

}
