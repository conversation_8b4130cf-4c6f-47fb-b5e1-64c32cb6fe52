package com.xylink.manager.service.factory;

import com.xylink.manager.model.RechargeRequestDto;
import org.apache.commons.lang3.StringUtils;

import java.util.UUID;

/**
 * <AUTHOR>
 * @since 2021/9/28 4:36 下午
 */
public final class RechargeRequestDtoFactory {

    private static final String DEFAULT_ENTERPRISE = "default_enterprise";
    private static final String DEFAULT_SERIAL_SOURCE = "private_manager";

    public static RechargeRequestDto create4KRecharge(String deviceSn, long expireTime) {
        if (StringUtils.isBlank(deviceSn) || expireTime < 0) {
            throw new IllegalArgumentException("deviceSn or expireTime is illegal");
        }
        RechargeRequestDto rechargeRequestDto = new RechargeRequestDto();
        rechargeRequestDto.setEnterpriseId(DEFAULT_ENTERPRISE);
        rechargeRequestDto.setDeviceSn(deviceSn);
        rechargeRequestDto.setBeginTime(System.currentTimeMillis());
        rechargeRequestDto.setExpireTime(expireTime);
        rechargeRequestDto.setSerialSource(DEFAULT_SERIAL_SOURCE);
        rechargeRequestDto.setSerialNumber(UUID.randomUUID().toString());
        rechargeRequestDto.setResolutionRatio("K4");
        return rechargeRequestDto;
    }

}
