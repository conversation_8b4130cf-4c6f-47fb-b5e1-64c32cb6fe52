package com.xylink.manager.service;

import com.xylink.config.Constants;
import com.xylink.manager.controller.dto.KafkaAddrDTO;
import com.xylink.manager.controller.dto.McserverAddrDTO;
import com.xylink.manager.controller.dto.RmServerAddrDTO;
import com.xylink.manager.handler.NodeHelper;
import com.xylink.manager.model.cm.McAccessCM;
import com.xylink.manager.model.deploy.Node;
import com.xylink.manager.model.em.Labels;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.base.SystemModeConfig;
import com.xylink.util.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import java.net.*;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/1/15
 */
@Service
@Slf4j
public class ChangeIpService {
    @Autowired
    private K8sService k8sService;
    @Autowired
    private NodeHelper nodeHelper;
    @Autowired
    private IDeployService deployService;
    @Autowired
    private ServerStatusService serverStatusService;

    private static final String INIT_STATUS_KEY = "has_initialized_configmap";

    private static final String[] RESTART_SERVICE_LABELS = {"private-hbase",
            "private-oceanbase",
            "private-kafka",
            "private-kafka-cluster",
            "private-redis",
            "private-redis-sentinel",
            "private-es",
            "private-zookeeper",
            "private-zookeeper-cluster",
            "private-mongo"
    };

    /**
     * CMS保存服务器节点信息时默认初始化部分configMap信息
     */
    public void changeIp(String pageIp, String externalIp, String domain) {
        if (!SystemModeConfig.isNewCms()) {
            log.info("the systemMode isn't newCms");
            return;
        }

        Map<String, String> configmap = k8sService.getConfigmap(Constants.CUSTOMIZE_INSTALL);
        String initCm = configmap.get(INIT_STATUS_KEY);
        boolean hasInitCm = StringUtils.equals(initCm, "true");

        if ((StringUtils.equals(pageIp, externalIp) && hasInitCm)
                || StringUtils.equals(pageIp, "**********")) {
            log.info("will not updateCm,pageIp:{}, externalIp:{}", pageIp, externalIp);
            return;
        }
        IpUtils.IpAddress ipAddress = getCmsIp();

        boolean isIpv4 = IpUtils.isIpv4(pageIp);
        boolean isIpv6 = IpUtils.isIpv6(pageIp);

        if (!isIpv4 && !isIpv6) {
            log.warn("the node ip is illegal, externalIp:{}", externalIp);
            return;
        }
        String ipv4 = pageIp;
        String ipv6 = pageIp;
        if (isIpv4) {
            ipv6 = ipAddress.getIpv6();
        }
        if (isIpv6) {
            ipv4 = ipAddress.getIpv4();
        }
        updateConfigmap(ipv4, ipv6, isIpv6, domain);
        Map<String, String> map = new HashMap<>();
        map.put(INIT_STATUS_KEY, "true");
        k8sService.editConfigmap(Constants.CUSTOMIZE_INSTALL, map);
    }

    private void updateConfigmap(String ipv4, String ipv6, boolean isIpv6, String domain) {
        Node commonNode = deployService.listNodesByAppLabel(Constants.NODE_TYPE_COMMON_MAIN).stream().findFirst().orElse(null);
        String nodeName = commonNode.getName();
        String firstIp = ipv4;
        String secondIp = ipv4;
        if (isIpv6) {
            firstIp = ipv6;
        }
        if (StringUtils.isBlank(domain)) {
            domain = firstIp;
        }
        createMcAccessCm(firstIp, nodeName);
        updateAllKafka(firstIp, nodeName);
        updateAllDmcu(firstIp, nodeName, domain);
        updateAllNetTool(firstIp, secondIp, nodeName);
        updateAllIp(firstIp, domain);
        updateAllOpenrestyMain(firstIp, nodeName, domain, ipv4, ipv6);
        updateAllMainProxy(firstIp, nodeName, domain, ipv4, ipv6);
        //重启相关服务
        serverStatusService.restartByServiceLabelIn("app", RESTART_SERVICE_LABELS);
    }

    private void createMcAccessCm(String firstIp, String nodeName) {
        McAccessCM mcAccessCM = getMcAccessCM(firstIp, nodeName);
        //配置机制优化 保存配置后自动重启服务
        try {
            nodeHelper.advanceConfigure(mcAccessCM, Constants.CONFIGMAP_MCACCESS_CONFIG, Labels.mcaccess.label(), nodeName);
        } catch (Exception e) {
            log.error("createMcAccessCm error,", e);
        }
    }

    @Nonnull
    private static McAccessCM getMcAccessCM(String firstIp, String nodeName) {
        McAccessCM mcAccessCM = new McAccessCM();
        List<McserverAddrDTO> mcserverAddr = new ArrayList<>();
        mcAccessCM.setMcserverAddr(mcserverAddr);
        McserverAddrDTO mcserverAddrDTO = new McserverAddrDTO();
        mcserverAddr.add(mcserverAddrDTO);
        mcserverAddrDTO.setUpThriftIp(firstIp);
        KafkaAddrDTO kafkaAddrDTO = new KafkaAddrDTO();
        kafkaAddrDTO.setKafkaIp(firstIp);
        kafkaAddrDTO.setKafkaAlarmAddr(firstIp);
        kafkaAddrDTO.setKafkaAudioEnergyAddr(firstIp);
        mcAccessCM.setKafkaAddr(kafkaAddrDTO);
        mcAccessCM.setNodeName(nodeName);
        mcAccessCM.setInternalNginxAddr(firstIp + ":11111");
        mcAccessCM.setVodclustermgrAddr("wss://" + firstIp + ":18126");
        List<RmServerAddrDTO> rmServerAddr = new ArrayList<>();
        RmServerAddrDTO rmServerAddrDTO = new RmServerAddrDTO();
        rmServerAddr.add(rmServerAddrDTO);
        rmServerAddrDTO.setUpThriftIp(firstIp);
        rmServerAddrDTO.setUpThriftPort("9090");
        mcAccessCM.setRmserverAddr(rmServerAddr);
        return mcAccessCM;
    }

    private void updateAllKafka(String firstIp, String nodeName) {
        Map<String, String> map = new HashMap<>();
        map.put(nodeName + "-KAFKA-ADVERTISED-LISTENERS", ",OUTSIDE0://" + firstIp + ":9092");
        map.put(nodeName + "-KAFKA-OUT-SIDE-KEY", firstIp + ":9092");
        updateConfigMap(Constants.CONFIGMAP_KAFKA, map);
    }

    private void updateAllDmcu(String firstIp, String nodeName, String domain) {
        Map<String, String> map = new HashMap<>();
        map.put(nodeName + "-PUBLIC-IP", firstIp);
        map.put(nodeName + "-DOMAIN", domain);
        updateConfigMap(Constants.CONFIGMAP_DMCU, map);
    }

    private void updateAllNetTool(String firstIp, String secondIp, String nodeName) {
        Map<String, String> map = new HashMap<>();
        map.put(nodeName + "-PUBLIC-IP", firstIp);
        map.put(nodeName + "-PUBLIC-IP-V4", secondIp);

        updateConfigMap(Constants.CONFIGMAP_NET_TOOL, map);
    }

    private void updateAllIp(String firstIp, String domain) {
        Map<String, String> map = new HashMap<>();
        map.put("MAIN_DOMAIN_NAME", domain);
        map.put("MAIN_PUBLIC_IP", firstIp);
        map.put("SHARING_SERVER_PUBLIC_IP", firstIp);
        map.put("VOD_DOMAIN_NAME", domain);
        map.put("VOD_PUBLIC_IP", firstIp);
        updateConfigMap(Constants.CONFIGMAP_ALLIP, map);
    }

    private void updateAllOpenrestyMain(String firstIp, String nodeName, String domain,
                                        String ipv4, String ipv6) {
        //      ********** ************ cmszzbsb.xiaoyuonline.com [2409:8745:39a:c700::2]
        //      ************ [2409:8745:39a:c700::4] ********** cms10.xiaoyuonline.com cms10.xylink.com
        Map<String, String> map = new HashMap<>();
        map.put(nodeName + "-DOMAIN", domain);
        map.put(nodeName + "-PUBLIC-IP", firstIp);
        if (StringUtils.isBlank(ipv6)) {
            map.put(nodeName + "-NIGNX_SERVER_NAME", "********** " + ipv4 + " " + domain);
            map.put("NIGNX_SERVER_NAME", "********** " + ipv4 + " " + domain);
        } else {
            map.put(nodeName + "-NIGNX_SERVER_NAME", "********** " + ipv4 + " "
                    + domain + " [" + ipv6 + "]" + " [" + IpUtils.abbreviateIPv6(ipv6) + "]");
            map.put("NIGNX_SERVER_NAME", "********** " + ipv4 + " "
                    + domain + " [" + ipv6 + "]" + " [" + IpUtils.abbreviateIPv6(ipv6) + "]");
        }
        updateConfigMap(Constants.CONFIGMAP_OPENRESTY_MAIN, map);
    }

    private void updateAllMainProxy(String firstIp, String nodeName, String domain,
                                    String ipv4, String ipv6) {
        Map<String, String> map = new HashMap<>();
        map.put(nodeName + "-PUBLIC-IP", firstIp);
        map.put(nodeName + "-UPSTREAM-ADDR", firstIp);
        map.put(nodeName + "-DOMAIN", domain);
        if (StringUtils.isBlank(ipv6)) {
            map.put(nodeName + "-NIGNX_SERVER_NAME", "********** " + ipv4 + " " + domain);
        } else {
            map.put(nodeName + "-NIGNX_SERVER_NAME", "********** " + ipv4 + " "
                    + domain + " [" + ipv6 + "]" + " [" + IpUtils.abbreviateIPv6(ipv6) + "]");
        }
        updateConfigMap(Constants.CONFIGMAP_MAIN_PROXY, map);
    }

    private void updateConfigMap(String configMapName, Map<String, String> data) {
        if (data.isEmpty()) {
            log.info("the configMapName has been config, name:{}", configMapName);
            return;
        }
        k8sService.editConfigmap(configMapName, data);
    }

    public static void main(String[] args) {
        IpUtils.IpAddress ipAddress = getCmsIp();
        System.out.println(ipAddress);
    }


    public static IpUtils.IpAddress getCmsIp() {
        IpUtils.IpAddress ipAddress = new IpUtils.IpAddress();
        log.info("start getCmsIp");
        // 固定的网卡名称
        String targetInterfaceName = "bond0";
        try {
            // 获取所有网络接口
            NetworkInterface networkInterface = NetworkInterface.getByName(targetInterfaceName);
            // 检查网卡名称是否匹配
            if (networkInterface == null || !networkInterface.getName().equals(targetInterfaceName)) {
                log.warn("can't find bond0");
                return ipAddress;
            }
            // 遍历网卡的所有IP地址
            for (InterfaceAddress interfaceAddress : networkInterface.getInterfaceAddresses()) {
                interfaceAddress.getNetworkPrefixLength();
            }

            Enumeration<InetAddress> inetAddresses = networkInterface.getInetAddresses();
            String globalIPv4;
            String globalIPv6;
            while (inetAddresses.hasMoreElements()) {
                InetAddress inetAddress = inetAddresses.nextElement();
                // 检查是否是全局 IPv4 地址
                if (inetAddress instanceof Inet4Address) {
                    if (isGlobalAddress(inetAddress)) {
                        globalIPv4 = inetAddress.getHostAddress();
                        ipAddress.setIpv4(globalIPv4);
                    }
                }

                // 检查是否是全局 IPv6 地址
                if (inetAddress instanceof Inet6Address) {
                    if (isGlobalAddress(inetAddress)) {
                        globalIPv6 = inetAddress.getHostAddress();
                        if (globalIPv6.contains("%")) {
                            globalIPv6 = globalIPv6.split("%")[0];
                        }
                        ipAddress.setIpv6(globalIPv6);
                    }
                }
            }
        } catch (SocketException ex) {
            log.error("getIp error,", ex);
        }
        return ipAddress;
    }


    /**
     * 判断是否是全局地址（排除本地地址、环回地址和链路本地地址）
     */
    private static boolean isGlobalAddress(InetAddress address) {
        // 排除环回地址（如 127.0.0.1 或 ::1）
        if (address.isLoopbackAddress()) {
            return false;
        }

        // 排除本地地址（如 192.168.x.x、10.x.x.x、172.16.x.x-172.31.x.x）
        if (address.isSiteLocalAddress()) {
            return false;
        }

        // 排除22网段
        if (address.getHostAddress().startsWith("22.22.22.")) {
            return false;
        }

        // 对于 IPv6，排除链路本地地址（如 fe80::/10）
        if (address instanceof Inet6Address && address.isLinkLocalAddress()) {
            return false;
        }

        // 如果都不符合上述条件，则认为是全局地址
        return true;
    }
}
