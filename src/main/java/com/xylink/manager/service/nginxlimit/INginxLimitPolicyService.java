package com.xylink.manager.service.nginxlimit;

import com.xylink.manager.controller.dto.nginxlimit.NginxLimitApplicationDto;
import com.xylink.manager.controller.dto.nginxlimit.NginxLimitPolicyDto;
import com.xylink.manager.model.common.Page;
import com.xylink.manager.model.common.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/7/11 7:04 下午
 */
public interface INginxLimitPolicyService {

    /**
     * 分页列表数据
     *
     * @param key
     * @param pageable
     * @return
     */
    Page<NginxLimitPolicyDto> policyPage(String key, Pageable pageable);

    /**
     * 保存更新配置
     *
     * @param nginxLimitPolicyDto
     */
    void saveOrUpdate(NginxLimitPolicyDto nginxLimitPolicyDto);

    /**
     * 根据ID查询
     *
     * @param id
     * @return
     */
    NginxLimitPolicyDto policyInfo(String id);

    /**
     * 根据ID删除
     *
     * @param id
     * @return
     */
    void deleteById(String id);

    /**
     * 规则类型列表
     *
     * @return
     */
    List<PolicyTypeDto> policyTypeList();

    /**
     * 应用列表
     *
     * @return
     */
    List<NginxLimitApplicationDto> policyApplication(String type);

    /**
     * 重启服务
     *
     * @return
     */
    void restartPolicy();

}
