package com.xylink.manager.service;

import com.xylink.manager.controller.dto.ClientAuditDto;
import com.xylink.manager.controller.dto.CloudMeetingRoomAuditDto;
import com.xylink.manager.controller.dto.EnterpriseAuditLogDto;
import com.xylink.manager.model.CloudMeetingRoomConfig;
import com.xylink.manager.model.common.Page;
import com.xylink.manager.model.common.PageRequest;
import com.xylink.util.AuditLogJDBCUtils;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @date 2024/03/28/14:20
 */
@Service
public class AuditLogService {

    private final static Logger logger = LoggerFactory.getLogger(AuditLogService.class);

    @Autowired
    private AuditLogJDBCUtils auditLogJDBCUtils;

    public Set<String> getClientTypeList() {
        String sql = "select deviceType from manager.client_config_audit_log";
        List<String> clientTypeList = auditLogJDBCUtils.getClientTypeList(sql);
        Set<String> set = new HashSet<>(10);
        set.addAll(clientTypeList);
        return set;
    }

    public Page<ClientAuditDto> getClientAuditLogPage(String key, String type, List<String> deviceType, Boolean order, PageRequest pageRequest) {
        String selectTotal = "SELECT count(id) as total FROM manager.client_config_audit_log where type = ?";
        String selectList = "SELECT id, configKey, deviceType, newConfigValue, oldConfigValue, alterType, operator, create_time FROM manager.client_config_audit_log where type = ?";
        if (StringUtils.isNotBlank(key)) {
            selectTotal += " and configKey like ?";
            selectList += " and configKey like ?";
        }
        if (!CollectionUtils.isEmpty(deviceType)) {
            StringBuilder deviceTypeUrl = new StringBuilder();
            for (int i = 0; i < deviceType.size(); i++) {
                deviceTypeUrl.append("?");
                if (i != deviceType.size() - 1) {
                    deviceTypeUrl.append(",");
                }
            }
            selectTotal += " and deviceType in (" + deviceTypeUrl + ")";
            selectList += " and deviceType in (" + deviceTypeUrl + ")";
        }
        if (order != null) {
            selectList += order ? " order by create_time asc" : " order by create_time desc";
        }
        selectList += " limit ?,?";
        long total = auditLogJDBCUtils.getClientAuditLogTotal(key, type, deviceType, selectTotal);
        List<ClientAuditDto> result = auditLogJDBCUtils.getClientAuditLogList(key, type, deviceType, selectList, pageRequest);
        return new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize(), total, result);
    }

    public Page<CloudMeetingRoomAuditDto> getCloudMeetingRoomAuditLogPage(String key, int cloudType, Boolean order, PageRequest pageRequest) {
        String selectTotal = "SELECT count(id) as total FROM manager.cloudmeetingroom_config_audit_log";
        String selectList = "SELECT id, name, configName, newConfigValue, oldConfigValue, configLevel, meetingType, configType, alterType, operator, create_time FROM manager.cloudmeetingroom_config_audit_log";
        selectTotal += " where cloudType = ?";
        selectList += " where cloudType = ?";
        if (StringUtils.isNotBlank(key)) {
            selectTotal += " and (name like ? or configName like ?)";
            selectList += " and (name like ? or configName like ?)";
        }
        if (order != null) {
            selectList += order ? " order by create_time asc" : " order by create_time desc";
        }
        selectList += " limit ?,?";
        long total = auditLogJDBCUtils.getCloudMeetingRoomAuditLogTotal(key, cloudType, selectTotal);
        List<CloudMeetingRoomAuditDto> result = auditLogJDBCUtils.getCloudMeetingRoomAuditLogList(key, cloudType, selectList, pageRequest);
        return new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize(), total, result);
    }

    public Page<EnterpriseAuditLogDto> getEnterpriseAuditLogPage(String key, Boolean order, PageRequest pageRequest) {
        String selectTotal = "SELECT count(id) as total FROM manager.enterprise_config_audit_log";
        String selectList = "SELECT id, name, configName, newConfigValue, oldConfigValue, enterpriseId, alterType, operator, create_time FROM manager.enterprise_config_audit_log";
        if (StringUtils.isNotBlank(key)) {
            selectTotal += " where name like ? or configName like ?";
            selectList += " where name like ? or configName like ?";
        }
        if (order != null) {
            selectList += order ? " order by create_time asc" : " order by create_time desc";
        }
        selectList += " limit ?,?";
        long total = auditLogJDBCUtils.getEnterpriseAuditLogTotal(key, selectTotal);
        List<EnterpriseAuditLogDto> result = auditLogJDBCUtils.getEnterpriseAuditLogList(key, selectList, pageRequest);
        return new Page<>(pageRequest.getPageNumber(), pageRequest.getPageSize(), total, result);
    }

    @Async
    public void saveEnterpriseAuditLog(String name, String configName, String configValue, String oldConfigValue, String alterType,String operator) {
        String sql = "INSERT INTO manager.enterprise_config_audit_log (id, name, configName, newConfigValue, oldConfigValue, enterpriseId, alterType, operator, create_time, update_time) VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        EnterpriseAuditLogDto enterpriseAuditLogDto = new EnterpriseAuditLogDto();
        enterpriseAuditLogDto.setId(System.currentTimeMillis());
        enterpriseAuditLogDto.setName(name);
        enterpriseAuditLogDto.setConfigName(configName);
        enterpriseAuditLogDto.setNewConfigValue(configValue);
        enterpriseAuditLogDto.setOldConfigValue(StringUtils.isBlank(oldConfigValue) ? "" : oldConfigValue);
        enterpriseAuditLogDto.setEnterpriseId("default_enterprise");
        enterpriseAuditLogDto.setOperator(operator);
        enterpriseAuditLogDto.setAlterType(alterType);
        auditLogJDBCUtils.saveEnterpriseAuditLog(sql, enterpriseAuditLogDto);
    }

    /**
     * @param meetingType 会议室类型，使能配置才需要传
     * @param cloudType 1代表为使能配置、2代表UI显示配置
     */
    @Async
    public void saveCloudMeetingRoomAuditLog(CloudMeetingRoomConfig config, String meetingType, String cloudType, String alterType,String operator) {
        String sql = "INSERT INTO manager.cloudmeetingroom_config_audit_log(id, name, configName, newConfigValue, oldConfigValue, configLevel, meetingType, configType, cloudType, alterType, operator, create_time, update_time) VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        CloudMeetingRoomAuditDto cloudMeetingRoomAuditDto = new CloudMeetingRoomAuditDto();
        cloudMeetingRoomAuditDto.setId(System.currentTimeMillis());
        cloudMeetingRoomAuditDto.setName(config.getConfigNameC());
        cloudMeetingRoomAuditDto.setConfigName(config.getConfigName());
        cloudMeetingRoomAuditDto.setNewConfigValue(config.getValue());
        cloudMeetingRoomAuditDto.setOldConfigValue(StringUtils.isBlank(config.getOldValue()) ? "" : config.getOldValue());
        cloudMeetingRoomAuditDto.setConfigType("enterprise".equals(config.getConfigType()) ? "企业定制" : "云会议室");
        cloudMeetingRoomAuditDto.setConfigLevel(config.getConfigLevel());
        cloudMeetingRoomAuditDto.setMeetingType(meetingType);
        cloudMeetingRoomAuditDto.setOperator(operator);
        cloudMeetingRoomAuditDto.setAlterType(alterType);
        cloudMeetingRoomAuditDto.setCloudType(cloudType);
        auditLogJDBCUtils.saveCloudMeetingRoomAuditLog(sql, cloudMeetingRoomAuditDto);
    }
    @Async
    public void saveClientAuditLog(String configKey, String clientType, String configValue, String oldConfigValue, String alterType,String operator) {
        String sql = "INSERT INTO manager.client_config_audit_log(id, configKey, deviceType, newConfigValue, oldConfigValue, alterType, operator, create_time, update_time) VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?)";
        ClientAuditDto clientAuditDto = new ClientAuditDto();
        clientAuditDto.setId(System.currentTimeMillis());
        clientAuditDto.setConfigKey(configKey);
        clientAuditDto.setDeviceType(clientType);
        clientAuditDto.setNewConfigValue(configValue);
        clientAuditDto.setOldConfigValue(StringUtils.isBlank(oldConfigValue) ? "" : oldConfigValue);
        clientAuditDto.setAlterType(alterType);
        clientAuditDto.setOperator(operator);
        auditLogJDBCUtils.saveClientAuditLog(sql, clientAuditDto);
    }

}
