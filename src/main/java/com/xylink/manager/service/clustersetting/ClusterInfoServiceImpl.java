package com.xylink.manager.service.clustersetting;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.exception.basic.ClientErrorException;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServiceErrorException;
import com.xylink.manager.controller.dto.SigClusterInfoDto;
import com.xylink.manager.controller.dto.cluster.ClusterMasterInfoDto;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.clustersetting.domain.SigServerMasterSlaveDto;
import com.xylink.manager.service.clustersetting.failover.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/12/7 5:34 下午
 */
@Slf4j
@Service
public class ClusterInfoServiceImpl implements ClusterService {
    @Resource
    private ActiveMqFailover activeMqFailover;
    @Resource
    private SigServerFailover sigServerFailover;
    @Resource
    private ZookeeperFailover zookeeperFailover;
    @Resource
    private RedisFailover redisFailover;
    @Resource
    private K8sService k8sService;
    @Resource
    private ClusterTaskManagerFactory clusterTaskManagerFactory;

    @Override
    public ClusterMasterInfoDto getWorkingAmq() {
        String realMasterIp = activeMqFailover.working().orElseThrow((Supplier<RuntimeException>) () -> new ServiceErrorException(ErrorStatus.AMQ_MASTER_INFO_ERROR));
        Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        String recordMasterIp = allIp.get(NetworkConstants.MAIN_MASTER_AMQ_IP);

        return new ClusterMasterInfoDto(realMasterIp, recordMasterIp);
    }

    @Override
    public void setWorkingAmq(String workingAmqIp) {
        activeMqFailover.setWorking(workingAmqIp);
    }

    @Override
    public Optional<Map<String, List<SigServerMasterSlaveDto>>> getAllSigServer() {
        return sigServerFailover.working();
    }

    @Override
    public List<SigClusterInfoDto> getSigClusterInfo() {
        List<SigClusterInfoDto> res = new ArrayList<>();
        Optional<Map<String, List<SigServerMasterSlaveDto>>> allSigServerMapping = getAllSigServer();
        if (allSigServerMapping.isPresent()) {
            Map<String, List<SigServerMasterSlaveDto>> map = allSigServerMapping.get();
            for (Map.Entry<String, List<SigServerMasterSlaveDto>> temp : map.entrySet()) {
                SigClusterInfoDto dto = new SigClusterInfoDto();
                dto.setDomain(temp.getKey());
                dto.setSigServerList(temp.getValue());
                res.add(dto);
            }
        }
        return res;
    }

    @Override
    public void setMasterSigServer(String masterSigServerIp) {
        sigServerFailover.setWorking(masterSigServerIp);
    }

    @Override
    public Optional<List<String>> getWorkingZookeeper() {
        return zookeeperFailover.working();
    }

    @Override
    public Optional<String> getWorkingRedis() {
        return redisFailover.working();
    }

    @Override
    public void setWorkingRedis(String workingRedisIp) {
        redisFailover.setWorking(workingRedisIp);
    }

    @Override
    public SigServerFailoverConfig getSigServerFailoverConfig() {
        Map<String, String> allCluster = k8sService.getConfigmap(Constants.CONFIGMAP_CLUSTER);
        SigServerFailoverConfig res = new SigServerFailoverConfig();
        String mode = allCluster.get("SIG_CONFIG_MODE");
        if (StringUtils.isBlank(mode)) {
            mode = SigServerFailoverConfig.Mode.getDefaultMode().name();
        }
        res.setMode(mode);
        try {
            res.setPeriodOfSecond(Long.parseLong(allCluster.get("SIG_CONFIG_INSPECTION_PERIOD")));
        } catch (NumberFormatException ignored) {
        }
        try {
            res.setThreshold(Long.parseLong(allCluster.get("SIG_CONFIG_TRIGGER_THRESHOLD")));
        } catch (NumberFormatException ignored) {
        }

        res.setEnableSigMasterSlave(isEnableSigMasterSlave());
        return res;
    }

    @Override
    public Map<String, String> sigMasterSlaveSwitch() {
        Map<String, String> result = new HashMap<>();
        Map<String, String> allCluster = k8sService.getConfigmap(Constants.CONFIGMAP_ALL_SIGSERVER_HA);
        if (allCluster != null) {
            if (allCluster.get("SIG-MASTER-SLAVE-ENABLE") != null) {
                result.put("sigMasterSlaveEnable", allCluster.get("SIG-MASTER-SLAVE-ENABLE"));
            } else {
                result.put("sigMasterSlaveEnable", allCluster.get("DEFAULT-SIG-MASTER-SLAVE-ENABLE"));
            }
        } else {
            result.put("sigMasterSlaveEnable", "false");
        }
        return result;
    }

    @Override
    public void sigMasterSlaveSwitch(String value) {
        Map<String, String> allCluster = k8sService.getConfigmap(Constants.CONFIGMAP_ALL_SIGSERVER_HA);
        if (allCluster != null) {
            allCluster.put("SIG-MASTER-SLAVE-ENABLE", value);
            k8sService.editConfigmap(Constants.CONFIGMAP_ALL_SIGSERVER_HA, allCluster);
        } else {
            log.error("Lack of necessary dependencies:configmap-{}", Constants.CONFIGMAP_ALL_SIGSERVER_HA);
        }

        clusterTaskManagerFactory.singleSigServerFailoverTaskManager()
                .start(getSigServerFailoverConfig());
    }

    public void updateSigServerFailoverConfig(SigServerFailoverConfig config) {
        if (!Arrays.stream(SigServerFailoverConfig.Mode.values()).map(Enum::name).collect(Collectors.toSet()).contains(config.getMode())) {
            throw new ClientErrorException(ErrorStatus.PARAM_ERROR);
        }
        HashMap<String, String> map = new HashMap<>();
        map.put("SIG_CONFIG_MODE", config.getMode());
        map.put("SIG_CONFIG_INSPECTION_PERIOD", String.valueOf(config.getPeriodOfSecond()));
        map.put("SIG_CONFIG_TRIGGER_THRESHOLD", String.valueOf(config.getThreshold()));
        k8sService.patchConfigMap(Constants.CONFIGMAP_CLUSTER, map);
        config.setEnableSigMasterSlave(isEnableSigMasterSlave());
        SigServerFailoverTaskManager sigServerFailoverTaskManager = clusterTaskManagerFactory.singleSigServerFailoverTaskManager();
        sigServerFailoverTaskManager.start(config);
    }

    private boolean isEnableSigMasterSlave(){
        Map<String, String> sigMasterSlaveSwitch = this.sigMasterSlaveSwitch();
        return Boolean.parseBoolean(sigMasterSlaveSwitch.get("sigMasterSlaveEnable"));
    }
}
