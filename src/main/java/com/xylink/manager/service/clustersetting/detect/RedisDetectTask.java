package com.xylink.manager.service.clustersetting.detect;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.RedisConstants;
import com.xylink.util.ClusterUtil;
import com.xylink.util.K8sUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/9/6 10:38 AM
 */
@Slf4j
public class RedisDetectTask implements Runnable {
    @Override
    public void run() {
        log.info("redis detect task start...");
        try {
            detectRedis();
        } catch (Exception e) {
            log.error("redis detect master ip failed!!!", e);
        }
    }

    private void detectRedis() {
        Map<String, String> allRedis = K8sUtils.getConfigMap(Constants.CONFIGMAP_REDIS);
        String sentinelHosts = allRedis.get(RedisConstants.REDIS_SENTINEL_HOSTS);
        String redisSentinelMasterIp = ClusterUtil.getRedisSentinelMasterIp(sentinelHosts);
        if (StringUtils.isBlank(redisSentinelMasterIp)) {
            return;
        }


        Map<String, String> allIp = new HashMap<>();
        allIp.put(NetworkConstants.MAIN_REDIS_IP, redisSentinelMasterIp);
        K8sUtils.patchConfigMap(Constants.CONFIGMAP_ALLIP, allIp);

        allRedis.entrySet().stream().filter(x -> RedisConstants.REDIS_ROLE_MASTER.equalsIgnoreCase(x.getValue())).forEach(x -> allRedis.put(x.getKey(), RedisConstants.REDIS_ROLE_SLAVE));
        String hostnameOfMaster = K8sUtils.getHostNameByAppLabelAndIp("private-redis", redisSentinelMasterIp);
        if (StringUtils.isBlank(hostnameOfMaster)) {
            return;
        }

        allRedis.put(hostnameOfMaster + RedisConstants.REDIS_ROLE_SUFFIX, RedisConstants.REDIS_ROLE_MASTER);
        allRedis.put(NetworkConstants.REDIS_MASTER_IP, redisSentinelMasterIp);

        K8sUtils.createOrReplaceConfigMap(Constants.CONFIGMAP_REDIS, allRedis);
    }
}
