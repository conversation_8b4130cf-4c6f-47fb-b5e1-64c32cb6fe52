package com.xylink.manager.service.clustersetting.failover.telnet;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.Socket;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/12/7 8:07 下午
 */
public class SigServerTelnetClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(SigServerTelnetClient.class);
    private static final String OPERATION_GET_MASTER_INFO = "getMasterInfo";
    private static final String OPERATION_EXIT = "exit";
    private static final String OPERATION_SET_SELF_MASTER = "setSelfMaster";
    private static final String MASTER_ROLE_KEY = "Role: Master";
    private static final String OPERATION_GET_MASTER_INFO_BEGIN = "begin ---< Operation: \"getMasterInfo\">--- begin";
    private static final String OPERATION_GET_MASTER_INFO_END = "end  ---< Operation: \"getMasterInfo\">---  end";
    private static final String OPERATION_SET_SELF_MASTER_BEGIN = "begin ---< Operation: \"setSelfMaster\">--- begin";
    private static final String OPERATION_SET_SELF_MASTER_END = "end  ---< Operation: \"setSelfMaster\">---  end";
    private final TelnetClient telnetClient;

    public SigServerTelnetClient(String host) {
        this.telnetClient = new TelnetClient(host, 53322);
    }

    public Role role() {
        List<String> responseLine = new ArrayList<>();
        try (Socket socket = telnetClient.getSocket();
             PrintWriter pw = new PrintWriter(socket.getOutputStream());
             BufferedReader is = new BufferedReader(new InputStreamReader(socket.getInputStream()))) {
            String info;
            pw.println(OPERATION_GET_MASTER_INFO);
            pw.flush();
            boolean responseLineStart = false;
            while ((info = is.readLine()) != null) {
                if (info.contains(OPERATION_GET_MASTER_INFO_BEGIN)) {
                    responseLineStart = true;
                }
                if (responseLineStart) {
                    responseLine.add(info);
                }
                if (info.contains(OPERATION_GET_MASTER_INFO_END)) {
                    break;
                }
            }
            pw.println(OPERATION_EXIT);
            pw.flush();
            LOGGER.info("{} call: {} response is : \r\n {}", telnetClient, OPERATION_GET_MASTER_INFO, StringUtils.join(responseLine, "\r\n"));
        } catch (Exception e) {
            LOGGER.error("{} request error by call: {}", telnetClient, OPERATION_GET_MASTER_INFO, e);
            return Role.UNKNOWN;
        }

        for (String s : responseLine) {
            if (s.contains(MASTER_ROLE_KEY)) {
                return Role.MASTER;
            }
        }
        return Role.SLAVE;
    }

    public void setSelfMaster() {
        try (Socket socket = telnetClient.getSocket(); PrintWriter pw = new PrintWriter(socket.getOutputStream()); BufferedReader is = new BufferedReader(new InputStreamReader(socket.getInputStream()))) {
            pw.println(OPERATION_SET_SELF_MASTER);
            pw.flush();
            String info;
            List<String> responseLine = new ArrayList<>();
            boolean responseLineStart = false;
            while ((info = is.readLine()) != null) {
                if (info.contains(OPERATION_SET_SELF_MASTER_BEGIN)) {
                    responseLineStart = true;
                }
                if (responseLineStart) {
                    responseLine.add(info);
                }
                if (info.contains(OPERATION_SET_SELF_MASTER_END)) {
                    break;
                }
            }
            pw.println(OPERATION_EXIT);
            pw.flush();
            LOGGER.info("{} call: {} response is : {}", telnetClient, OPERATION_SET_SELF_MASTER, StringUtils.join(responseLine, "\r\n"));
        } catch (Exception e) {
            LOGGER.error("{} request error by call: {}", telnetClient, OPERATION_SET_SELF_MASTER, e);
        }
    }

    public enum Role {
        /**
         * master
         */
        MASTER,
        /**
         * slave
         */
        SLAVE,
        /**
         * 未知状态，sigserver服务有问题
         */
        UNKNOWN
    }
}
