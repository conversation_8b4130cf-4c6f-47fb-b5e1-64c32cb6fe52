package com.xylink.manager.service.clustersetting.failover.telnet;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;

/**
 * <AUTHOR>
 * @since 2021/12/7 6:02 下午
 */
public class TelnetClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(TelnetClient.class);
    private final String host;
    private final int port;
    private Socket socket;

    public TelnetClient(String host, int port) {
        if (host == null) {
            throw new IllegalArgumentException("Host must not be null.");
        }
        this.host = host;
        this.port = port;
        this.socket = null;
    }

    public boolean tryTelnet() {
        try (Socket socket = getSocket()) {
            LOGGER.info(socket.toString());
            return true;
        } catch (Exception e) {
            LOGGER.error("TryTelnet error.Socket[addr=/{},port={}]", host, port);
        }
        return false;
    }

    public synchronized Socket getSocket() throws IOException {
        if (socket == null || socket.isClosed()) {
            Socket socket = new Socket();
            InetSocketAddress address = new InetSocketAddress(this.host, this.port);
            socket.connect(address, 3000);
            return socket;
        }
        return this.socket;
    }

    @Override
    public String toString() {
        return "TelnetClient[addr=" + this.host +
                ",port=" + this.port + "]";
    }
}
