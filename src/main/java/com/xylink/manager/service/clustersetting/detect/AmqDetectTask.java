package com.xylink.manager.service.clustersetting.detect;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.service.clustersetting.failover.telnet.TelnetClient;
import com.xylink.util.K8sUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/9/6 10:38 AM
 */
@Slf4j
public class AmqDetectTask implements Runnable {
    @Override
    public void run() {
        log.info("amq detect task start...");
        try {
            detectAmq();
        } catch (Exception e) {
            log.error("amq detect failed!!!", e);
        }
    }

    private void detectAmq() {
        Map<String, String> allIp = K8sUtils.getConfigMap(Constants.CONFIGMAP_ALLIP);
        String mainAmqIp = allIp.get(NetworkConstants.MAIN_AMQ_IP).replaceAll(":61616", "").replaceAll("tcp://", "");

        // telnet 616161
        for (String amqIp : mainAmqIp.split(",")) {
            if (new TelnetClient(amqIp, 61616).tryTelnet()) {
                HashMap<String, String> map = new HashMap<>();
                map.put(NetworkConstants.MAIN_MASTER_AMQ_IP, amqIp);
                K8sUtils.patchConfigMap(Constants.CONFIGMAP_ALLIP, map);
                break;
            }
        }
    }
}
