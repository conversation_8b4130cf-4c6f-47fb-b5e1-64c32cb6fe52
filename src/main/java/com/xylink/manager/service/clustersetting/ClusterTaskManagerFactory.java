package com.xylink.manager.service.clustersetting;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/9/6 6:01 PM
 */
@Component
public class ClusterTaskManagerFactory {
    @Value("${detect.amq.period}")
    private int detectAmqPeriodMs;
    @Value("${detect.redis.period}")
    private int detectRedisPeriodMs;
    @Resource
    private ClusterService clusterService;
    private DetectTaskManager detectTaskManager;
    private SigServerFailoverTaskManager sigServerFailoverTaskManager;

    public DetectTaskManager singleDetectTaskManager() {
        if (detectTaskManager == null) {
            detectTaskManager = new DetectTaskManager(detectAmqPeriodMs, detectRedisPeriodMs);
        }
        return detectTaskManager;
    }

    public SigServerFailoverTaskManager singleSigServerFailoverTaskManager() {
        if (sigServerFailoverTaskManager == null) {
            sigServerFailoverTaskManager = new SigServerFailoverTaskManager(clusterService, clusterService.getSigServerFailoverConfig());
        }
        return sigServerFailoverTaskManager;
    }
}
