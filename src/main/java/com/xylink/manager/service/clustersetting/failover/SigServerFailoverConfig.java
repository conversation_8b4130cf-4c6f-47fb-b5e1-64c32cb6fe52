package com.xylink.manager.service.clustersetting.failover;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * <AUTHOR>
 * @since 2023/9/7 2:29 PM
 */
public class SigServerFailoverConfig {
    /**
     * 模式,AUTO表示自动,MANUAL表示手动
     */
    private String mode;
    /**
     * 巡检周期,注释:单位s
     */
    private long periodOfSecond;
    /**
     * 切换周期,注释:自动切换模式下:连续N个巡展周期，检测均有故障，执行自动切换。手动切换模式下不执行。
     */
    private long threshold;

    @JsonIgnore
    private boolean enableSigMasterSlave = true;

    public enum Mode {
        /**
         * auto
         */
        AUTO,
        /**
         * manual
         */
        MANUAL;

        public static Mode getDefaultMode() {
            return MANUAL;
        }
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public long getPeriodOfSecond() {
        return periodOfSecond;
    }

    public void setPeriodOfSecond(long periodOfSecond) {
        this.periodOfSecond = periodOfSecond;
    }

    public long getThreshold() {
        return threshold;
    }

    public void setThreshold(long threshold) {
        this.threshold = threshold;
    }

    public boolean isAuto() {
        return Mode.AUTO.name().equalsIgnoreCase(this.mode);
    }

    public boolean isEnableSigMasterSlave() {
        return enableSigMasterSlave;
    }

    public void setEnableSigMasterSlave(boolean enableSigMasterSlave) {
        this.enableSigMasterSlave = enableSigMasterSlave;
    }

    @Override
    public String toString() {
        return "SigServerFailoverConfig{" +
                "mode='" + mode + '\'' +
                ", periodOfSecond=" + periodOfSecond +
                ", threshold=" + threshold +
                ", enableSigMasterSlave=" + enableSigMasterSlave +
                '}';
    }
}
