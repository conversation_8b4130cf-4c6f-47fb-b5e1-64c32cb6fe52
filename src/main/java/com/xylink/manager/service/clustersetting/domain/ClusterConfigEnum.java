package com.xylink.manager.service.clustersetting.domain;

import com.xylink.config.NetworkConstants;
import com.xylink.config.exception.basic.ClientErrorException;
import com.xylink.config.exception.basic.ErrorStatus;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/9/6 14:52
 */
@Getter
@Slf4j
public enum ClusterConfigEnum {
    INTERNAL_HAPROXY("INTERNAL_HAPROXY","INTERNAL_HAPROXY_NAME","INTERNAL_HAPROXY_TYPE", "INTERNAL_HAPROXY_HA_IP", NetworkConstants.MAIN_INTERNAL_IP, "INTERNAL_HAPROXY_MASTER_IP", "INTERNAL_HAPROXY_SLAVE_IP", false),
    CLIENT_INTERNAL_HAPROXY("CLIENT_INTERNAL_HAPROXY","CLIENT_INTERNAL_HAPROXY_NAME","CLIENT_INTERNAL_HAPROXY_TYPE", "CLIENT_INTERNAL_HAPROXY_HA_IP", "", "CLIENT_INTERNAL_HAPROXY_MASTER_IP", "CLIENT_INTERNAL_HAPROXY_SLAVE_IP", false),
    MAIN_DATABASE("MAIN_DATABASE","MAIN_DATABASE_NAME","MAIN_DATABASE_TYPE", "MAIN_DATABASE_HA_IP", NetworkConstants.DATABASE_IP, "MAIN_DATABASE_MASTER_IP", "MAIN_DATABASE_SLAVE_IP", true),
    UAA_DATABASE("UAA_DATABASE","UAA_DATABASE_NAME","UAA_DATABASE_TYPE", "UAA_DATABASE_HA_IP", NetworkConstants.UAA_DATABASE_IP, "UAA_DATABASE_MASTER_IP", "UAA_DATABASE_SLAVE_IP", true),
    ;


    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 名称key
     */
    private String nameKey;

    /**
     * 类型key
     */
    private String typeKey;

    /**
     * 高可用地址key
     */
    private String haAddressKey;

    /**
     * 非高可用地址key
     */
    private String nonHaAddressKey;

    /**
     * masterip key
     */
    private String masterIpKey;

    /**
     * slaveIp key
     */
    private String slaveIpKey;

    /**
     * 是否支持显示同步按钮
     */
    private boolean supportSync;

    ClusterConfigEnum(String businessType, String nameKey, String typeKey, String haAddressKey, String nonHaAddressKey, String masterIpKey, String slaveIpKey, boolean supportSync) {
        this.businessType = businessType;
        this.nameKey = nameKey;
        this.typeKey = typeKey;
        this.haAddressKey = haAddressKey;
        this.nonHaAddressKey = nonHaAddressKey;
        this.masterIpKey = masterIpKey;
        this.slaveIpKey = slaveIpKey;
        this.supportSync = supportSync;
    }

    public static void main(String[] args) {
        ClusterConfigEnum sss = ClusterConfigEnum.valueOf("sss");
        System.out.println(sss);
    }

    public static List<String> getBusinessTypeList() {
        List<String> res = new ArrayList<>();
        for (ClusterConfigEnum value : ClusterConfigEnum.values()) {
            res.add(value.getBusinessType());
        }
        return res;
    }

    public static ClusterConfigEnum getEnum(String businessType) {
        try {
            return ClusterConfigEnum.valueOf(businessType);
        } catch (Exception e) {
            log.error("enum valueOf:{} failed,", businessType, e);
            throw new ClientErrorException(ErrorStatus.PARAM_ERROR);
        }
    }


    public static String getHaAddressKey(String nonHaAddressKey) {
        for (ClusterConfigEnum temp : ClusterConfigEnum.values()) {
            if (temp.getNonHaAddressKey().equalsIgnoreCase(nonHaAddressKey)) {
                return temp.getHaAddressKey();
            }
        }
        return null;
    }
}
