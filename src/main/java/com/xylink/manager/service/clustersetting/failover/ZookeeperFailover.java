package com.xylink.manager.service.clustersetting.failover;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.clustersetting.failover.telnet.TelnetClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2022/9/13 3:12 下午
 */
@Slf4j
@Component
public class ZookeeperFailover implements Failover<String, List<String>> {

    @Resource
    private K8sService k8sService;

    @Override
    public Optional<List<String>> working() {
        // get all zookeeper
        Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        List<String> workingIp = new ArrayList<>();
        if (allIp != null && StringUtils.isNotBlank(allIp.get(NetworkConstants.ZOOKEEPER_IP))) {
            String ips = allIp.get(NetworkConstants.ZOOKEEPER_IP).replaceAll(":2181", "");
            // telnet 616161
            for (String ip : ips.split(",")) {
                if (new TelnetClient(ip, 2181).tryTelnet()) {
                    workingIp.add(ip);
                }
            }
        }
        // return working zookeeper
        log.info("Current alive zookeeper is: {}", workingIp);
        return Optional.of(workingIp);
    }

    @Override
    public void setWorking(String t) {
        if (StringUtils.isBlank(t)) {
            throw new IllegalArgumentException("Zookeeper Ip must not be null.");
        }
        String masterZookeeperIpKey = NetworkConstants.MASTER_ZOOKEEPER_IP;
        Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        allIp.put(masterZookeeperIpKey, t);
        log.info("Update all-ip:key:{} value:{}", masterZookeeperIpKey, t);
        k8sService.editConfigmap(Constants.CONFIGMAP_ALLIP, allIp);
    }
}
