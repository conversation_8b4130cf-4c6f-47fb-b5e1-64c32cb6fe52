package com.xylink.manager.service.clustersetting.failover;

import com.xylink.config.SigServerHaConfigMap;
import com.xylink.manager.service.clustersetting.domain.SigServerMasterSlaveDto;
import com.xylink.manager.service.clustersetting.failover.telnet.SigServerTelnetClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2021/12/7 7:18 下午
 */
@Slf4j
@Component
public class SigServerFailover implements Failover<String, Map<String, List<SigServerMasterSlaveDto>>> {
    @Override
    public Optional<Map<String, List<SigServerMasterSlaveDto>>> working() {
        Map<String, List<Map<String, String>>> all = SigServerHaConfigMap.getAllSigServer();
        if (!all.isEmpty()) {
            Map<String, List<SigServerMasterSlaveDto>> result = new HashMap<>();
            all.forEach((key, value) -> {
                        List<SigServerMasterSlaveDto> data = new ArrayList<>();
                        value.forEach(item -> {
                            SigServerMasterSlaveDto sigServerMasterSlaveDto = new SigServerMasterSlaveDto();
                            sigServerMasterSlaveDto.setServerId(item.get("serverId"));
                            sigServerMasterSlaveDto.setAddress(item.get("address"));
                            sigServerMasterSlaveDto.setServerId(item.get("serverId"));
                            sigServerMasterSlaveDto.setDomain(key);
                            SigServerTelnetClient.Role role = new SigServerTelnetClient(sigServerMasterSlaveDto.getAddress()).role();
                            sigServerMasterSlaveDto.setRole(role.name());
                            data.add(sigServerMasterSlaveDto);
                        });
                        result.put(key, data);
                    }
            );
            return Optional.of(result);
        }
        return Optional.empty();
    }

    @Override
    public void setWorking(String host) {
        if (StringUtils.isEmpty(host)) {
            log.error("Master sigserver address is null.Check param.");
        }
        new SigServerTelnetClient(host).setSelfMaster();
    }
}
