package com.xylink.manager.service.clustersetting;

import com.xylink.manager.service.clustersetting.detect.AmqDetectTask;
import com.xylink.manager.service.clustersetting.detect.RedisDetectTask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * amq redis 探测可用节点
 *
 * <AUTHOR>
 * @since 2023/9/6 10:04 AM
 */
public class DetectTaskManager {
    private static final Logger LOG = LoggerFactory.getLogger(DetectTaskManager.class);
    private ScheduledExecutorService schedule;
    private final long amqDelay;
    private final long amqPeriodMs;
    private final long redisDelay;
    private final long redisPeriodMs;
    private DetectTaskStatus detectTaskStatus = DetectTaskStatus.NOT_STARTED;

    public DetectTaskManager(long amqPeriodMs, long redisPeriodMs) {
        this(0, amqPeriodMs, 0, redisPeriodMs);
    }

    public DetectTaskManager(long amqDelay, long amqPeriodMs, long redisDelay, long redisPeriodMs) {
        this.amqDelay = amqDelay;
        this.amqPeriodMs = amqPeriodMs;
        this.redisDelay = redisDelay;
        this.redisPeriodMs = redisPeriodMs;
        LOG.info("amqPeriodMs set to {}", amqPeriodMs);
        LOG.info("redisPeriodMs set to {}", redisPeriodMs);
    }

    public enum DetectTaskStatus {
        /**
         * not started
         */
        NOT_STARTED,
        /**
         * started
         */
        STARTED,
        /**
         * completed
         */
        COMPLETED
    }

    public void start() {

        if (DetectTaskStatus.STARTED == detectTaskStatus) {
            LOG.warn("Detect task is already running.");
            return;
        }

        if (amqPeriodMs <= 0 && redisPeriodMs <= 0) {
            LOG.info("Detect task is not scheduled. Cause by amqPeriodMs && redisPeriodMs is 0");
            return;
        }

        schedule = Executors.newScheduledThreadPool(2);

        if (amqPeriodMs > 0) {
            schedule.scheduleAtFixedRate(new AmqDetectTask(), amqDelay, amqPeriodMs, TimeUnit.MILLISECONDS);
        }

        if (redisPeriodMs > 0) {
            schedule.scheduleAtFixedRate(new RedisDetectTask(), redisDelay, redisPeriodMs, TimeUnit.MILLISECONDS);
        }

        this.detectTaskStatus = DetectTaskStatus.STARTED;

        LOG.info("Detect task running.");
    }

    public void shutdown() {
        if (DetectTaskStatus.STARTED == detectTaskStatus) {
            LOG.info("Shutting down detect task.");
            schedule.shutdown();
            detectTaskStatus = DetectTaskStatus.COMPLETED;
        } else {
            LOG.warn("Detect task not started. Ignoring shutdown!");
        }
    }


}
