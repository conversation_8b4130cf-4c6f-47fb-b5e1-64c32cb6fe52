package com.xylink.manager.service.clustersetting;

import com.xylink.manager.controller.dto.SigClusterInfoDto;
import com.xylink.manager.service.clustersetting.domain.SigServerMasterSlaveDto;
import com.xylink.manager.service.clustersetting.failover.SigServerFailoverConfig;
import com.xylink.manager.service.clustersetting.failover.telnet.SigServerTelnetClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * sigserver 切主
 *
 * <AUTHOR>
 * @since 2023/9/6 10:04 AM
 */
public class SigServerFailoverTaskManager {
    private static final Logger LOG = LoggerFactory.getLogger(SigServerFailoverTaskManager.class);
    private ScheduledExecutorService schedule;
    private final ClusterService clusterService;

    private SigServerFailoverConfig initSigServerFailoverConfig;
    private TaskStatus taskStatus = TaskStatus.NOT_STARTED;

    private volatile Map<String, Long> errorMap;
    private volatile Map<String, String> masterMapping;

    public SigServerFailoverTaskManager(ClusterService clusterService, SigServerFailoverConfig initSigServerFailoverConfig) {
        Objects.requireNonNull(clusterService, "ClusterService must not be bull.");
        this.clusterService = clusterService;
        this.initSigServerFailoverConfig = initSigServerFailoverConfig;
        this.errorMap = new ConcurrentHashMap<>();
        this.masterMapping = new ConcurrentHashMap<>();
    }

    public enum TaskStatus {
        /**
         * not started
         */
        NOT_STARTED,
        /**
         * started
         */
        STARTED,
        /**
         * completed
         */
        COMPLETED
    }

    public void start(SigServerFailoverConfig reqSigServerFailoverConfig) {
        if (reqSigServerFailoverConfig == null) {
            reqSigServerFailoverConfig = initSigServerFailoverConfig;
        }

        if (reqSigServerFailoverConfig == null) {
            LOG.info("Starting sigserver failover task with config:null");
            return;
        }

        LOG.info("Starting sigserver failover task with config:{}", reqSigServerFailoverConfig);

        if (reqSigServerFailoverConfig.isAuto() && reqSigServerFailoverConfig.isEnableSigMasterSlave()) {
            if (TaskStatus.STARTED == taskStatus) {
                shutdown();
            }
            doStart(reqSigServerFailoverConfig);
        } else {
            if (TaskStatus.STARTED == taskStatus) {
                shutdown();
            }
        }
    }

    public void shutdown() {
        if (TaskStatus.STARTED == taskStatus) {
            LOG.info("Shutting down sigserver failover task.");
            schedule.shutdown();
            errorMap.clear();
            initSigServerFailoverConfig = null;
            taskStatus = TaskStatus.COMPLETED;
        } else {
            LOG.warn("Sigserver failover task not started. Ignoring shutdown!");
        }
    }

    private void doStart(SigServerFailoverConfig sigServerFailoverConfig) {

        this.initSigServerFailoverConfig = sigServerFailoverConfig;
        schedule = Executors.newSingleThreadScheduledExecutor();

        schedule.scheduleAtFixedRate(() -> {
            try {
                long threshold = initSigServerFailoverConfig.getThreshold();

                List<SigClusterInfoDto> sigServers = clusterService.getSigClusterInfo();
                sigServers.forEach(sigServerGroupMapping -> {

                    String group = sigServerGroupMapping.getDomain();

                    List<SigServerMasterSlaveDto> sigServerGroup = sigServerGroupMapping.getSigServerList();

                    if (sigServerGroup.stream().allMatch(sigServer ->
                            SigServerTelnetClient.Role.UNKNOWN.name().equals(sigServer.getRole())
                    )) {
                        LOG.error("Group:{} instances is all down.", group);
                        return;
                    }
                    String targetMasterIp = masterMapping.get(group);
                    Optional<SigServerMasterSlaveDto> master = sigServerGroup.stream().filter(sigServer ->
                                    SigServerTelnetClient.Role.MASTER.name().equals(sigServer.getRole()))
                            .findFirst();

                    if (master.isPresent()) {
                        SigServerMasterSlaveDto masterSigServer = master.get();
                        if (!masterSigServer.getAddress().equals(targetMasterIp)) {
                            // 获取主
                            masterMapping.put(group, masterSigServer.getAddress());
                            errorMap.remove(group);
                        }
                    } else {
                        Long errorCount = errorMap.getOrDefault(group, 0L);
                        errorCount++;
                        if (errorCount >= threshold) {
                            // 随机找一个slave 切换
                            Optional<SigServerMasterSlaveDto> slave = sigServerGroup.stream().filter(sigServer ->
                                            SigServerTelnetClient.Role.SLAVE.name().equals(sigServer.getRole()))
                                    .findFirst();
                            slave.ifPresent(slaveSigServer -> {
                                clusterService.setMasterSigServer(slaveSigServer.getAddress());
                                masterMapping.put(group, slaveSigServer.getAddress());
                                errorMap.remove(group);
                            });
                        } else {
                            errorMap.put(group, errorCount);
                        }
                    }
                });
            } catch (Exception e) {
                LOG.error("Sigserver current failover task execution failed.", e);
            }

        }, 0, initSigServerFailoverConfig.getPeriodOfSecond(), TimeUnit.SECONDS);

        this.taskStatus = TaskStatus.STARTED;
        LOG.info("Sigserver failover task running.");
    }

}
