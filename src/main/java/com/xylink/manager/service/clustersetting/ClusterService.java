package com.xylink.manager.service.clustersetting;

import com.xylink.manager.controller.dto.SigClusterInfoDto;
import com.xylink.manager.controller.dto.cluster.ClusterMasterInfoDto;
import com.xylink.manager.service.clustersetting.domain.SigServerMasterSlaveDto;
import com.xylink.manager.service.clustersetting.failover.SigServerFailoverConfig;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2021/12/7 5:29 下午
 */
public interface ClusterService {
    /**
     * 获取master amq(activeMq master-slave共享文件夹存储,通过排它锁只有一个可以提供服务)
     *
     * @return
     */
    ClusterMasterInfoDto getWorkingAmq();

    /**
     * set master amq ip into all-ip
     *
     * @param workingAmqIp if workingAmqIp is null use getWorkingAmq()
     */
    void setWorkingAmq(String workingAmqIp);

    /**
     * 获取所有的sigServer (相同domain为master-slave 不同domain 一致性hash算法分配)
     *
     * @return
     */
    Optional<Map<String, List<SigServerMasterSlaveDto>>> getAllSigServer();

    /**
     * sigServer
     *
     * @return
     */
    List<SigClusterInfoDto> getSigClusterInfo();

    /**
     * 设置主信令
     *
     * @param masterSigServerIp
     */
    void setMasterSigServer(String masterSigServerIp);

    /**
     * 获取所有可用的zookeeper
     *
     * @return
     */
    Optional<List<String>> getWorkingZookeeper();

    /**
     * 获取master redis ip
     *
     * @return
     */
    Optional<String> getWorkingRedis();

    /**
     * 设置all-redis中REDIS_MASTER_IP，并更新对应节点的角色
     *
     * @param workingRedisIp
     */
    void setWorkingRedis(String workingRedisIp);

    /**
     * SigServerFailoverConfig
     *
     * @return
     */
    SigServerFailoverConfig getSigServerFailoverConfig();

    /**
     * 获取开关状态
     *
     * @return
     */
    Map<String, String> sigMasterSlaveSwitch();

    /**
     * 设置开关状态
     *
     * @param value
     */
    void sigMasterSlaveSwitch(String value);
}
