package com.xylink.manager.service.clustersetting.failover;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.clustersetting.failover.telnet.TelnetClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2021/12/7 5:47 下午
 */
@Slf4j
@Component
public class ActiveMqFailover implements Failover<String, String> {
    @Resource
    private K8sService k8sService;

    @Override
    public Optional<String> working() {
        // get all activemq
        Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        String workingIp = null;
        if (allIp != null && StringUtils.isNotBlank(allIp.get(NetworkConstants.MAIN_AMQ_IP))) {
            String mainAmqIp = allIp.get(NetworkConstants.MAIN_AMQ_IP).replaceAll(":61616", "").replaceAll("tcp://", "");
            // telnet 616161
            for (String amq : mainAmqIp.split(",")) {
                if (new TelnetClient(amq, 61616).tryTelnet()) {
                    workingIp = amq;
                    break;
                }
            }
        }
        // return working activemq
        log.info("Current master activeMq is: {}", workingIp);
        return Optional.ofNullable(workingIp);
    }

    @Override
    public void setWorking(String s) {
        String mainMasterAmqIpKey = NetworkConstants.MAIN_MASTER_AMQ_IP;
        Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        if (StringUtils.isBlank(s)) {
            working().ifPresent(data ->
                    allIp.put(mainMasterAmqIpKey, data)
            );
        } else {
            allIp.put(mainMasterAmqIpKey, s);
        }
        log.info("Update all-ip:key:{} value:{}", mainMasterAmqIpKey, allIp.get(mainMasterAmqIpKey));
        k8sService.editConfigmap(Constants.CONFIGMAP_ALLIP, allIp);
    }
}
