package com.xylink.manager.service.clustersetting;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.exception.basic.ClientErrorException;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.manager.controller.dto.cluster.ClusterConfigDto;
import com.xylink.manager.controller.dto.cluster.ClusterConfigTypeDto;
import com.xylink.manager.controller.dto.cluster.VipInfoDto;
import com.xylink.manager.model.SystemDatabaseDto;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.base.K8sSvcService;
import com.xylink.manager.service.clustersetting.domain.ClusterConfigEnum;
import com.xylink.manager.service.config.IServerConfigService;
import com.xylink.util.Ipv6Util;
import com.xylink.util.WebHookHttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023/9/6 14:29
 */
@Service
@Slf4j
public class ClusterConfigService {
    @Autowired
    private K8sService k8sService;
    @Autowired
    private K8sSvcService k8sSvcService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private IServerConfigService iServerConfigService;

    public List<ClusterConfigDto> getClusterConfigList() {
        Map<String, String> allCluster = k8sService.getConfigmapOrCreate(Constants.CONFIGMAP_CLUSTER);

        List<ClusterConfigDto> res = new ArrayList<>();
        for (ClusterConfigEnum temp : ClusterConfigEnum.values()) {
            String haAddress = allCluster.get(temp.getHaAddressKey());
            if (StringUtils.isBlank(haAddress)) {
                continue;
            }
            ClusterConfigDto clusterConfigDto = new ClusterConfigDto();
            clusterConfigDto.setName(allCluster.get(temp.getNameKey()));
            clusterConfigDto.setBusinessType(temp.getBusinessType());
            clusterConfigDto.setType(allCluster.get(temp.getTypeKey()));
            clusterConfigDto.setAddress(haAddress);
            clusterConfigDto.setMasterIp(allCluster.get(temp.getMasterIpKey()));
            clusterConfigDto.setSlaveIp(allCluster.get(temp.getSlaveIpKey()));
            clusterConfigDto.setSupportSync(temp.isSupportSync());
            res.add(clusterConfigDto);
        }
        return res;
    }

    public ClusterConfigTypeDto getSupportType() {
        return ClusterConfigTypeDto.getSupportType();
    }

    public void addOrUpdate(ClusterConfigDto clusterConfigDto, boolean needAdd) {
        ClusterConfigEnum configEnum = ClusterConfigEnum.getEnum(clusterConfigDto.getBusinessType());

        Map<String, String> allCluster = k8sService.getConfigmap(Constants.CONFIGMAP_CLUSTER);

        Map<String, String> allIp = new HashMap<>();

        if (needAdd && StringUtils.isNotBlank(allCluster.get(configEnum.getHaAddressKey()))) {
            throw new ClientErrorException(ErrorStatus.CLUSTER_CONFIG_ADDED);
        }
        allCluster.put(configEnum.getNameKey(), clusterConfigDto.getName());
        allCluster.put(configEnum.getTypeKey(), clusterConfigDto.getType());
        allCluster.put(configEnum.getHaAddressKey(), clusterConfigDto.getAddress());
        allCluster.put(configEnum.getMasterIpKey(), clusterConfigDto.getMasterIp());
        allCluster.put(configEnum.getSlaveIpKey(), clusterConfigDto.getSlaveIp());

        if (StringUtils.isNotBlank(configEnum.getNonHaAddressKey())) {
            allIp.put(configEnum.getNonHaAddressKey(), clusterConfigDto.getAddress());
        }

        k8sService.patchConfigMap(Constants.CONFIGMAP_CLUSTER, allCluster);
        k8sService.patchConfigMap(Constants.CONFIGMAP_ALLIP, allIp);
    }

    public void delete(String businessType) {
        ClusterConfigEnum clusterConfigEnum = ClusterConfigEnum.getEnum(businessType);
        Map<String, String> allCluster = k8sService.getConfigmapOrCreate(Constants.CONFIGMAP_CLUSTER);
        allCluster.remove(clusterConfigEnum.getNameKey());
        allCluster.remove(clusterConfigEnum.getTypeKey());
        allCluster.remove(clusterConfigEnum.getHaAddressKey());
        allCluster.remove(clusterConfigEnum.getMasterIpKey());
        allCluster.remove(clusterConfigEnum.getSlaveIpKey());
        k8sService.replaceConfigmap(Constants.CONFIGMAP_CLUSTER, allCluster);
    }

    public VipInfoDto vipInfo(String businessType) {
        Triple<String, String, String> vipRecord = vipRecord(businessType);
        VipInfoDto vipInfoDto = new VipInfoDto();
        String vip = vipRecord.getLeft();
        String recordMaster = vipRecord.getMiddle();
        String recordSlave = vipRecord.getRight();
        vipInfoDto.setVip(vip);
        vipInfoDto.setMaster(recordMaster);
        vipInfoDto.setSlave(recordSlave);

        String tmpIp = recordMaster;
        String url = "http://" + Ipv6Util.handlerIpv6Addr(tmpIp) + ":" + NetworkConstants.WEB_HOOK + "/hooks/hostnameI";
        String masterResponse = WebHookHttpUtil.getStringByPostWebHookResult(restTemplate, url, Strings.EMPTY);

        if (ipsStrToArr(masterResponse).contains(vip)) {
            vipInfoDto.setMaster(recordMaster);
            vipInfoDto.setSlave(recordSlave);
            return vipInfoDto;
        }
        tmpIp = recordSlave;
        url = "http://" + Ipv6Util.handlerIpv6Addr(tmpIp) + ":" + NetworkConstants.WEB_HOOK + "/hooks/hostnameI";
        String slaveResponse = WebHookHttpUtil.getStringByPostWebHookResult(restTemplate, url, Strings.EMPTY);
        if (ipsStrToArr(slaveResponse).contains(vip)) {
            vipInfoDto.setMaster(recordSlave);
            vipInfoDto.setSlave(recordMaster);
            return vipInfoDto;
        }

        log.info("Vip info:{}", vipInfoDto);

        return vipInfoDto;
    }

    public List<SystemDatabaseDto> replicationInfo(String businessType) {
        ClusterConfigEnum clusterConfigEnum = ClusterConfigEnum.getEnum(businessType);
        if (ClusterConfigEnum.UAA_DATABASE == clusterConfigEnum || ClusterConfigEnum.MAIN_DATABASE == clusterConfigEnum) {
            Triple<String, String, String> vipRecord = vipRecord(businessType);
            List<SystemDatabaseDto> result = new ArrayList<>();
            String port = queryDatabasePort(clusterConfigEnum);
            result.add(iServerConfigService.getSystemDatabaseDto("master", Pair.of(vipRecord.getMiddle(), port)));
            result.add(iServerConfigService.getSystemDatabaseDto("slave", Pair.of(vipRecord.getRight(), port)));
            return result;
        } else {
            throw new UnsupportedOperationException("Unsupported businessType:" + businessType);
        }
    }

    /**
     * 主从同步建立
     *
     * @param businessType
     * @param type
     * @return
     */
    public boolean replicationEstablish(String businessType, String type) {
        ClusterConfigEnum clusterConfigEnum = ClusterConfigEnum.getEnum(businessType);
        if (ClusterConfigEnum.UAA_DATABASE == clusterConfigEnum || ClusterConfigEnum.MAIN_DATABASE == clusterConfigEnum) {
            // 查看当前真实主数据库
            Triple<String, String, String> vipRecord = vipRecord(businessType);
            String recordMaster = vipRecord.getMiddle();

            VipInfoDto vipInfoDto = vipInfo(businessType);
            String port = queryDatabasePort(clusterConfigEnum);

            String master = vipInfoDto.getMaster();
            String slave = vipInfoDto.getSlave();

            boolean recordIsCurrent = recordMaster.equals(master);

            if ("MASTER_TO_SLAVE".equals(type)) {
                if (recordIsCurrent) {
                    iServerConfigService.backUpAndRestoreThenReplication(master, port, slave, port);
                } else {
                    iServerConfigService.replication(master, port, slave, port);
                }
            } else if ("SLAVE_TO_MASTER".equals(type)) {
                if (recordIsCurrent) {
                    iServerConfigService.replication(slave, port, master, port);
                } else {
                    iServerConfigService.backUpAndRestoreThenReplication(slave, port, master, port);
                }
            } else {
                throw new UnsupportedOperationException("Unsupported type:" + type);
            }
            return true;
        } else {
            throw new UnsupportedOperationException("Unsupported businessType:" + businessType);
        }
    }

    /**
     * 主从同步中断
     *
     * @param businessType
     * @param type
     * @return
     */
    public boolean replicationDisrupt(String businessType, String type) {
        ClusterConfigEnum clusterConfigEnum = ClusterConfigEnum.getEnum(businessType);
        if (ClusterConfigEnum.UAA_DATABASE == clusterConfigEnum || ClusterConfigEnum.MAIN_DATABASE == clusterConfigEnum) {
            Triple<String, String, String> vipRecord = vipRecord(businessType);
            String recordMaster = vipRecord.getMiddle();
            String recordSlave = vipRecord.getRight();
            String port = queryDatabasePort(clusterConfigEnum);

            if ("MASTER_TO_SLAVE".equals(type)) {
                iServerConfigService.stopSlave(recordSlave, port);
            } else if ("SLAVE_TO_MASTER".equals(type)) {
                iServerConfigService.stopSlave(recordMaster, port);
            } else {
                throw new UnsupportedOperationException("Unsupported type:" + type);
            }
            return true;
        } else {
            throw new UnsupportedOperationException("Unsupported businessType:" + businessType);
        }
    }

    public String queryDatabasePort(ClusterConfigEnum clusterConfigEnum) {
        String port = "3306";
        Map<String, String> allIp = k8sService.getConfigmapOrCreate(Constants.CONFIGMAP_ALLIP);
        if (ClusterConfigEnum.UAA_DATABASE == clusterConfigEnum) {
            String databasePort = allIp.get("UAA_DATABASE_PORT");
            if (StringUtils.isNotBlank(databasePort)) {
                port = databasePort;
            }
        } else if (ClusterConfigEnum.MAIN_DATABASE == clusterConfigEnum) {
            String databasePort = allIp.get(NetworkConstants.DATABASE_PORT);
            if (StringUtils.isNotBlank(databasePort)) {
                port = databasePort;
            }
        }
        return port;
    }

    /**
     * left:vip
     * middle:master
     * right:slave
     *
     * @param businessType
     * @return
     */
    public Triple<String, String, String> vipRecord(String businessType) {
        ClusterConfigEnum clusterConfigEnum = ClusterConfigEnum.getEnum(businessType);
        Map<String, String> allCluster = k8sService.getConfigmapOrCreate(Constants.CONFIGMAP_CLUSTER);
        return Triple.of(allCluster.get(clusterConfigEnum.getHaAddressKey()), allCluster.get(clusterConfigEnum.getMasterIpKey()), allCluster.get(clusterConfigEnum.getSlaveIpKey()));
    }

    private List<String> ipsStrToArr(String ips) {
        if (StringUtils.isBlank(ips)) {
            return Collections.emptyList();
        }
        return Arrays.stream(ips.split(" ")).collect(Collectors.toList());
    }
}
