package com.xylink.manager.service;

import com.xylink.config.Constants;
import com.xylink.config.exception.basic.ClientErrorException;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.manager.service.base.K8sService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date: 2022/11/07/16:39
 */
@Service
public class ChangePasswordValidityService {

    @Autowired
    private K8sService k8sService;

    //key-设置密码期限的当前时间 设置密码期限的当前时间 24h之内只能设置一次
    private static final String currentTimeSetPwdValidity = "CURRENT-TIME-SET-PWD-VALIDITY";
    //key-账号上次设置的期限时间
    private static final String pwdValidity = "PWD-VALIDITY";
    //key-拼接-每个账号修改密码的时间
    private static final String updatePwdTime = "-UPDATE-PWD-TIME";

    private static final long minTime = 24 * 60 * 60 * 1000L;

    public boolean allowUpdateValidity() {
        long currentTime = System.currentTimeMillis();
        Map<String, String> configmap = k8sService.getConfigmap(Constants.CONFIGMAP_PRIVATE_DATA);
        if (StringUtils.isBlank(configmap.get(currentTimeSetPwdValidity))) {
            return true;
        }
        return currentTime - Long.parseLong(configmap.get(currentTimeSetPwdValidity)) > minTime;

    }

    public void updateValidity(Long validityTime) {
        long currentTime = System.currentTimeMillis();
        Map<String, String> configmap = k8sService.getConfigmap(Constants.CONFIGMAP_PRIVATE_DATA);
        if (allowUpdateValidity()) {
            configmap.put(currentTimeSetPwdValidity, String.valueOf(currentTime));
            configmap.put(pwdValidity, String.valueOf(validityTime));
        } else {
            // 不能更新密码期限 置灰
            throw new ClientErrorException(ErrorStatus.CANNOT_UPDATE_VALIDITY);
        }
        k8sService.editConfigmap(Constants.CONFIGMAP_PRIVATE_DATA, configmap);

    }

    public Long getLastSetPwdValidity() {
        String lastSetPwdValidity = k8sService.getConfigmap(Constants.CONFIGMAP_PRIVATE_DATA).get(ChangePasswordValidityService.pwdValidity);
        if (StringUtils.isBlank(lastSetPwdValidity)) {
            return Long.parseLong(initValidity().get(ChangePasswordValidityService.pwdValidity));
        }
        return Long.parseLong(lastSetPwdValidity);
    }

    public boolean ifPwdValidity(String user) {
        Map<String, String> configmap = k8sService.getConfigmap(Constants.CONFIGMAP_PRIVATE_DATA);
        //获取修改密码的时间
        String updatepwdTime = configmap.get(user + updatePwdTime);
        long currentTime = System.currentTimeMillis();
        //获取不到默认修改密码时间为当前时间
        if (StringUtils.isBlank(updatepwdTime)) {
            configmap.put(user + updatePwdTime, String.valueOf(currentTime));
            k8sService.editConfigmap(Constants.CONFIGMAP_PRIVATE_DATA, configmap);
            return false;
        }

        //密码期限未设置 默认永久有效
        if (StringUtils.isBlank(configmap.get(pwdValidity))) {
            configmap.put(pwdValidity, String.valueOf(1L));
            k8sService.editConfigmap(Constants.CONFIGMAP_PRIVATE_DATA, configmap);
            return false;
        }

        if (configmap.get(pwdValidity).equalsIgnoreCase("1")) {
            return false;
        }

        return Long.parseLong(updatepwdTime) + Long.parseLong(configmap.get(pwdValidity)) <= currentTime;
    }

    public HashMap<String, String> initValidity() {
        long currentTime = System.currentTimeMillis();
        HashMap<String, String> timeMap = new HashMap<>();
//        timeMap.put(currentTimeSetPwdValidity, String.valueOf(currentTime));
        timeMap.put(pwdValidity, String.valueOf(1L));
        k8sService.editConfigmap(Constants.CONFIGMAP_PRIVATE_DATA, timeMap);
        return timeMap;
    }


}
