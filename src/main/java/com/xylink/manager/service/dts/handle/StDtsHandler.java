package com.xylink.manager.service.dts.handle;

import com.xylink.manager.model.dts.ConnectionInfoReqDto;
import com.xylink.manager.repository.dts.DtsJobItemsRepository;
import com.xylink.manager.repository.dts.DtsJobRepository;
import com.xylink.manager.service.dts.constant.DtsConstant;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/11/17 4:16 下午
 */
public class StDtsHandler extends AbstractDtsHandler {

    public StDtsHandler(String workspace, DtsJobRepository dtsJobRepository, DtsJobItemsRepository dtsJobItemsRepository) {
        super(workspace, dtsJobRepository, dtsJobItemsRepository);
    }

    @Override
    protected String queryDatabasesCommand(ConnectionInfoReqDto connectionInfoReqDto) {
        return DtsConstant.ST_COMMAND_SHOW_DATABASE;
    }

    /**
     * /opt/ShenTong/bin/osrexp -U$user/$passwd -h $ip -p $port -d instancename level=schema schema=$DBname file=/tmp/$DBname.osr log=/tmp/$DBname.log constraint=true index=true trigger=true
     *
     * @param connectionInfoReqDto
     * @param object
     * @param backUpFile
     * @return
     */
    @Override
    protected List<String> backUpCommand(ConnectionInfoReqDto connectionInfoReqDto, String object, File backUpFile) {
        List<String> command = new ArrayList<>();
        command.add("/opt/ShenTong/bin/osrexp");
        command.add("-U" + connectionInfoReqDto.getUsername() + "/" + connectionInfoReqDto.getPassword());
        command.add("-h " + connectionInfoReqDto.getIp());
        command.add("-p " + connectionInfoReqDto.getPort());
        command.add("-d " + connectionInfoReqDto.getDatabase());
        command.add("level=schema");
        command.add("schema=" + object);
        command.add("file=" + getContainerPath(object) + "/" + backUpFile.getName());
        command.add("constraint=true index=true trigger=true");
        return command;
    }

    /**
     * /opt/ShenTong/bin/osrimp -U$user/$passwd -h $ip -p $port -d instancename level=schema schema="$DBname" file=/tmp/$DBname.osr log=/tmp/$DBname.log fromuser="$DBname" touser="$DBname" ignore=true constraint=true index=true trigger=true recreatetable=true tableStorageTS="$DBname"_DATA indexStorageTS="$DBname"_DATA
     *
     * @param connectionInfoReqDto
     * @param object
     * @param backUpFile
     * @return
     */
    @Override
    protected List<String> restoreCommand(ConnectionInfoReqDto connectionInfoReqDto, String object, File backUpFile) {
        List<String> command = new ArrayList<>();
        command.add("/opt/ShenTong/bin/osrimp");
        command.add("-U" + connectionInfoReqDto.getUsername() + "/" + connectionInfoReqDto.getPassword());
        command.add("-h " + connectionInfoReqDto.getIp());
        command.add("-p " + connectionInfoReqDto.getPort());
        command.add("-d " + connectionInfoReqDto.getDatabase());
        command.add("level=schema");
        command.add("schema=" + object);
        command.add("file=" + getContainerPath(object) + "/" + backUpFile.getName());
        command.add("fromuser=" + object);
        command.add("touser=" + object);
        command.add("ignore=true constraint=true index=true trigger=true recreatetable=true");
        command.add("tableStorageTS=" + object + "_DATA");
        command.add("indexStorageTS=" + object + "_DATA");
        return command;
    }
}
