package com.xylink.manager.service.dts;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.model.common.Page;
import com.xylink.manager.model.common.Pageable;
import com.xylink.manager.model.dts.*;
import com.xylink.manager.repository.dts.DtsJobItemsRepository;
import com.xylink.manager.repository.dts.DtsJobRepository;
import com.xylink.manager.repository.dts.entity.DtsJobEntity;
import com.xylink.manager.repository.dts.entity.DtsJobItemsEntity;
import com.xylink.manager.service.db.JasyptService;
import com.xylink.manager.service.dts.constant.JobStatusEnum;
import com.xylink.util.DbConfigUtil;
import com.xylink.util.MemoryPaginationUtil;
import com.xylink.util.UUIDGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/10/29 5:43 下午
 */
@Slf4j
@Service
public class DtsServiceImpl implements DtsService {

    @Autowired
    private JasyptService jasyptService;
    @Resource
    private List<DtsHandlerAdapter> dtsHandlerAdapters;
    @Resource
    private List<DtsHandlerMapping> dtsHandlerMappings;
    @Resource
    private DtsJobRepository dtsJobRepository;
    @Resource
    private DtsJobItemsRepository dtsJobItemsRepository;

    private final static ExecutorService DTS_JOB_EXEC = new ThreadPoolExecutor(1, 1,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<Runnable>(),
            new ThreadFactoryBuilder().setNameFormat("dts-exec-%d").setDaemon(true).build());

    private final Object lock = new Object();

    @Override
    public List<InstanceTypeDto> getInstanceTypes() {
        return Arrays.stream(InstanceTypeEnum.values()).map(item ->
                new InstanceTypeDto(item.getName(), item.getValue())
        ).collect(Collectors.toList());
    }

    @Override
    public List<DatabaseTypeDto> getDatabaseTypes() {
        return Arrays.stream(DatabaseTypeEnum.values()).map(item ->
                new DatabaseTypeDto(item.getName(), item.getValue())
        ).collect(Collectors.toList());
    }

    @Override
    public ConnectionTestDto connectionTest(ConnectionInfoReqDto reqDto) {
        try {
            DriverManager.setLoginTimeout(3);
            Connection connection = DriverManager.getConnection(getUrl(reqDto), reqDto.getUsername(), reqDto.getPassword());
            connection.close();
            return ConnectionTestDto.successOf();
        } catch (SQLException exception) {
            log.error("Test database connection error.", exception);
            String detailMessage = exception.getMessage();
            Throwable cause = exception;
            while (cause.getCause() != null) {
                cause = cause.getCause();
                if (StringUtils.isNotBlank(cause.getMessage())) {
                    detailMessage = cause.getMessage();
                    break;
                }
            }
            return ConnectionTestDto.errorOf(detailMessage);
        }
    }

    @Override
    public List<String> getDatabase(ConnectionInfoReqDto reqDto) {
        Object handler = getHandler(reqDto.getDatabaseType());
        DtsHandlerAdapter ha = getHandlerAdapter(handler);
        try {
            return ha.queryDatabases(reqDto, handler);
        } catch (SQLException exception) {
            throw new ServerException(exception, ErrorStatus.UNEXPECTED_ERROR);
        }
    }

    @Override
    public JobPreCheckDto jobPreCheck(DtsReqDto dtsReqDto) {
        List<JobPreCheckDto.CheckItem> checkItems = new ArrayList<>();
        // source check
        try {
            getDatabase(dtsReqDto.getSource());
            checkItems.add(buildCheckItem(CheckListEnum.SOURCE_CONNECTION, true, "success."));
            checkItems.add(buildCheckItem(CheckListEnum.SOURCE_PERMISSION, true, "success."));
        } catch (Exception e) {
            log.error("Source database connection error.", e);
            checkItems.add(buildCheckItem(CheckListEnum.SOURCE_CONNECTION, false, e.getCause().getMessage()));
            checkItems.add(buildCheckItem(CheckListEnum.SOURCE_PERMISSION, false, "Caused by source_connection error."));
        }
        // target check
        try {
            getDatabase(dtsReqDto.getTarget());
            checkItems.add(buildCheckItem(CheckListEnum.TARGET_CONNECTION, true, "success."));
            checkItems.add(buildCheckItem(CheckListEnum.TARGET_PERMISSION, true, "success."));
        } catch (Exception e) {
            log.error("Target database connection error.", e);
            checkItems.add(buildCheckItem(CheckListEnum.TARGET_CONNECTION, false, e.getCause().getMessage()));
            checkItems.add(buildCheckItem(CheckListEnum.TARGET_PERMISSION, false, "Caused by target_connection error."));
        }
        return new JobPreCheckDto(checkItems);
    }

    @Override
    public void jobCreate(DtsReqDto dtsReqDto) {
        Objects.requireNonNull(dtsReqDto);
        Objects.requireNonNull(dtsReqDto.getObjects());
        Objects.requireNonNull(dtsReqDto.getSource());
        Objects.requireNonNull(dtsReqDto.getTarget());
        // 创建job
        DtsJobEntity dtsJobEntity = createDtsJobEntity(dtsReqDto);
        List<DtsJobItemsEntity> items = createJobItems(dtsReqDto, dtsJobEntity);
        dtsJobRepository.save(dtsJobEntity);
        dtsJobItemsRepository.save(items, dtsJobEntity.getId());
        // 开启job
        startJob(dtsJobEntity.getId());
    }

    @Override
    public Page<JobListDto> page(Pageable pageable) {
        List<DtsJobEntity> data = dtsJobRepository.findAll();
        List<JobListDto> result = data.stream().map(this::convert).sorted().collect(Collectors.toList());
        Collections.reverse(result);
        return MemoryPaginationUtil.pagination(result, pageable);
    }

    @Override
    public JobDetailDto jobInfo(String id) {
        Objects.requireNonNull(id);
        Optional<DtsJobEntity> jobEntityOptional = dtsJobRepository.findOne(id);
        if (!jobEntityOptional.isPresent()) {
            throw new ServerException(ErrorStatus.DTS_JOB_ID_NOT_EXIST);
        }
        DtsJobEntity job = jobEntityOptional.get();
        List<DtsJobItemsEntity> jobItems = dtsJobItemsRepository.queryByJobId(job.getId());
        return buildJobDetailDto(job, jobItems);
    }

    @Override
    public void startJob(String id) {
        if (StringUtils.isBlank(id)) {
            throw new IllegalArgumentException("Job id is null.");
        }
        synchronized (lock) {
            Optional<DtsJobEntity> jobEntityOptional = dtsJobRepository.findOne(id);
            if (!jobEntityOptional.isPresent()) {
                throw new ServerException(ErrorStatus.DTS_JOB_ID_NOT_EXIST);
            }
            if (JobStatusEnum.done.name().equals(jobEntityOptional.get().getStatus()) || JobStatusEnum.running.name().equals(jobEntityOptional.get().getStatus())) {
                throw new ServerException(ErrorStatus.DTS_JOB_STATUS_EXCEPTION);
            }
            List<DtsJobItemsEntity> items = dtsJobItemsRepository.queryByJobId(id);
            Date start = new Date();
            DtsJobEntity job = jobEntityOptional.get();
            job.setStartTime(start);
            if (CollectionUtils.isEmpty(items)) {
                // 标注任务完成
                job.setEndTime(start);
                job.setStatus(JobStatusEnum.done.name());
            } else {
                job.setStatus(JobStatusEnum.running.name());
                DTS_JOB_EXEC.execute(() -> {
                    Object handler = getHandler(job.getDatabaseType());
                    getHandlerAdapter(handler).handleDts(job, items, handler);
                });
            }
            job.setUpdateTime(new Date());
            dtsJobRepository.deleteById(id);
            dtsJobRepository.save(job);
        }
    }

    @Override
    public void deleteById(String id) {
        dtsJobRepository.deleteById(id);
        dtsJobItemsRepository.deleteByJodId(id);
    }

    private List<DtsJobItemsEntity> createJobItems(DtsReqDto dtsReqDto, DtsJobEntity dtsJobEntity) {
        List<DtsJobItemsEntity> detailEntities = new ArrayList<>();
        for (String object : dtsReqDto.getObjects()) {
            DtsJobItemsEntity dtsJobItemsEntity = new DtsJobItemsEntity();
            dtsJobItemsEntity.setId(UUIDGenerator.generate());
            dtsJobItemsEntity.setJobId(dtsJobEntity.getId());
            dtsJobItemsEntity.setObject(object);
            dtsJobItemsEntity.setStatus(JobStatusEnum.created.name());
            detailEntities.add(dtsJobItemsEntity);
        }
        return detailEntities;
    }

    private DtsJobEntity createDtsJobEntity(DtsReqDto dtsReqDto) {
        DtsJobEntity dtsJobEntity = new DtsJobEntity();
        dtsJobEntity.setId(UUIDGenerator.generate());
        dtsJobEntity.setName(dtsReqDto.getName());
        dtsJobEntity.setStatus(JobStatusEnum.created.name());
        // source
        ConnectionInfoReqDto source = dtsReqDto.getSource();
        dtsJobEntity.setDatabaseType(source.getDatabaseType());
        dtsJobEntity.setSourceInstanceType(source.getInstanceType());
        dtsJobEntity.setSourceIp(source.getIp());
        dtsJobEntity.setSourcePort(source.getPort());
        dtsJobEntity.setSourceUsername(source.getUsername());
        dtsJobEntity.setSourcePassword(jasyptService.encrypt(source.getPassword()));
        dtsJobEntity.setSourceDatabase(source.getDatabase());
        // target
        ConnectionInfoReqDto target = dtsReqDto.getTarget();
        dtsJobEntity.setTargetInstanceType(target.getInstanceType());
        dtsJobEntity.setTargetIp(target.getIp());
        dtsJobEntity.setTargetPort(target.getPort());
        dtsJobEntity.setTargetUsername(target.getUsername());
        dtsJobEntity.setTargetPassword(jasyptService.encrypt(target.getPassword()));
        dtsJobEntity.setTargetDatabase(target.getDatabase());
        // time
        Date now = new Date();
        dtsJobEntity.setCreateTime(now);
        dtsJobEntity.setUpdateTime(now);
        return dtsJobEntity;
    }

    private String getUrl(ConnectionInfoReqDto reqDto) throws SQLException {
        DbConfigUtil.DbConfig dbConfig = new DbConfigUtil.DbConfig();
        dbConfig.setDbType(reqDto.getDatabaseType());
        dbConfig.setAddress(reqDto.getIp());
        dbConfig.setPort(String.valueOf(reqDto.getPort()));
        dbConfig.setDatabaseName(reqDto.getDatabase());
        return dbConfig.jdbcConnectionTestUrl();
    }

    private Object getHandler(String databaseType) {
        if (dtsHandlerMappings != null) {
            for (DtsHandlerMapping dtsHandlerMapping : dtsHandlerMappings) {
                Object handler = dtsHandlerMapping.getHandler(databaseType);
                if (handler != null) {
                    return handler;
                }
            }
        }
        throw new ServerException("No handler for dts request: [" + databaseType + "]");

    }

    private DtsHandlerAdapter getHandlerAdapter(Object handler) {
        if (dtsHandlerAdapters != null) {
            for (DtsHandlerAdapter dtsHandlerAdapter : dtsHandlerAdapters) {
                if (dtsHandlerAdapter.supports(handler)) {
                    return dtsHandlerAdapter;
                }
            }
        }
        throw new ServerException("No adapter for handler: [" + handler + "]");
    }

    private JobPreCheckDto.CheckItem buildCheckItem(CheckListEnum checkListEnum, boolean success, String detailMessage) {
        JobPreCheckDto.CheckItem checkItem = new JobPreCheckDto.CheckItem();
        checkItem.setCheckListKey(checkListEnum.getKey());
        checkItem.setCheckListName(checkListEnum.getName());
        checkItem.setCheckListComment(checkListEnum.getComment());
        checkItem.setSuccess(success);
        checkItem.setDetailMessage(detailMessage);
        return checkItem;
    }

    private JobListDto convert(DtsJobEntity entity) {
        JobListDto jobListDto = new JobListDto();
        jobListDto.setId(entity.getId());
        jobListDto.setName(entity.getName());
        jobListDto.setStatus(entity.getStatus());
        jobListDto.setStartTime(entity.getStartTime());
        jobListDto.setEndTime(entity.getEndTime());
        jobListDto.setCreateTime(entity.getCreateTime());
        jobListDto.setUpdateTime(entity.getUpdateTime());
        return jobListDto;
    }

    private JobDetailDto buildJobDetailDto(DtsJobEntity jobEntity, List<DtsJobItemsEntity> dtsJobItemsEntities) {
        Objects.requireNonNull(jobEntity);
        JobDetailDto jobDetailDto = new JobDetailDto();
        jobDetailDto.setId(jobEntity.getId());
        jobDetailDto.setName(jobEntity.getName());
        jobDetailDto.setCreateTime(jobEntity.getCreateTime());
        jobDetailDto.setUpdateTime(jobEntity.getUpdateTime());
        jobDetailDto.setStartTime(jobEntity.getStartTime());
        jobDetailDto.setEndTime(jobEntity.getEndTime());
        ConnectionInfoReqDto source = new ConnectionInfoReqDto();
        source.setInstanceType(jobEntity.getSourceInstanceType());
        source.setDatabaseType(jobEntity.getDatabaseType());
        source.setIp(jobEntity.getSourceIp());
        source.setPort(jobEntity.getSourcePort());
        source.setUsername(jobEntity.getSourceUsername());
        source.setPassword(jasyptService.decrypt(jobEntity.getSourcePassword()));
        source.setDatabase(jobEntity.getSourceDatabase());
        jobDetailDto.setSource(source);
        ConnectionInfoReqDto target = new ConnectionInfoReqDto();
        target.setInstanceType(jobEntity.getTargetInstanceType());
        target.setDatabaseType(jobEntity.getDatabaseType());
        target.setIp(jobEntity.getTargetIp());
        target.setPort(jobEntity.getTargetPort());
        target.setUsername(jobEntity.getTargetUsername());
        target.setPassword(jasyptService.decrypt(jobEntity.getTargetPassword()));
        target.setDatabase(jobEntity.getTargetDatabase());
        jobDetailDto.setTarget(target);
        if (dtsJobItemsEntities != null) {
            jobDetailDto.setObjects(dtsJobItemsEntities.stream().map(this::convert).collect(Collectors.toList()));
        }
        return jobDetailDto;
    }

    private JobDetailDto.ObjectDetail convert(DtsJobItemsEntity dtsJobItemsEntity) {
        JobDetailDto.ObjectDetail objectDetail = new JobDetailDto.ObjectDetail();
        objectDetail.setId(dtsJobItemsEntity.getId());
        objectDetail.setJobId(dtsJobItemsEntity.getJobId());
        objectDetail.setObject(dtsJobItemsEntity.getObject());
        objectDetail.setStatus(dtsJobItemsEntity.getStatus());
        objectDetail.setStartTime(dtsJobItemsEntity.getStartTime());
        objectDetail.setEndTime(dtsJobItemsEntity.getEndTime());
        return objectDetail;
    }
}
