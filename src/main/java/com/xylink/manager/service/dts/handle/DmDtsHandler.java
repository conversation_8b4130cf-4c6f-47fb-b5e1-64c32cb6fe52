package com.xylink.manager.service.dts.handle;

import com.xylink.manager.model.dts.ConnectionInfoReqDto;
import com.xylink.manager.repository.dts.DtsJobItemsRepository;
import com.xylink.manager.repository.dts.DtsJobRepository;
import com.xylink.manager.service.dts.constant.DtsConstant;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/11/17 4:16 下午
 */
public class DmDtsHand<PERSON> extends AbstractDtsHandler {

    public DmDtsHandler(String workspace, DtsJobRepository dtsJobRepository, DtsJobItemsRepository dtsJobItemsRepository) {
        super(workspace, dtsJobRepository, dtsJobItemsRepository);
    }

    @Override
    protected String queryDatabasesCommand(ConnectionInfoReqDto connectionInfoReqDto) {
        return DtsConstant.DM_COMMAND_SHOW_DATABASE;
    }

    /**
     * /opt/dmdbms/bin/dexp USERID=$user/$passwd@$ip:$port FILE="$DBname".dmp DIRECTORY=/tmp LOG="$DBname".log SCHEMAS=$DBname TABLESPACE=N
     *
     * @param connectionInfoReqDto
     * @param object
     * @param backUpFile
     * @return
     */
    @Override
    protected List<String> backUpCommand(ConnectionInfoReqDto connectionInfoReqDto, String object, File backUpFile) {
        List<String> command = new ArrayList<>();
        command.add("/opt/dmdbms/bin/dexp");
        command.add("USERID=" + connectionInfoReqDto.getUsername() + "/\"" + connectionInfoReqDto.getPassword() + "\"@" + connectionInfoReqDto.getIp() + ":" + connectionInfoReqDto.getPort());
        command.add("FILE=" + backUpFile.getName());
        command.add("DIRECTORY=" + getContainerPath(object));
        command.add("SCHEMAS=" + object);
        command.add("TABLESPACE=N");
        return command;
    }

    /**
     * /opt/dmdbms/bin/dimp USERID=$user/$passwd@$ip:$port FILE="$DBname".dmp DIRECTORY=/tmp LOG="$DBname".log SCHEMAS=$DBname TABLE_EXISTS_ACTION=REPLACE IGNORE_INIT_PARA=2
     *
     * @param connectionInfoReqDto
     * @param object
     * @param backUpFile
     * @return
     */
    @Override
    protected List<String> restoreCommand(ConnectionInfoReqDto connectionInfoReqDto, String object, File backUpFile) {
        List<String> command = new ArrayList<>();
        command.add("/opt/dmdbms/bin/dimp");
        command.add("USERID=" + connectionInfoReqDto.getUsername() + "/\"" + connectionInfoReqDto.getPassword() + "\"@" + connectionInfoReqDto.getIp() + ":" + connectionInfoReqDto.getPort());
        command.add("FILE=" + backUpFile.getName());
        command.add("DIRECTORY=" + getContainerPath(object));
        command.add("SCHEMAS=" + object);
        command.add("TABLE_EXISTS_ACTION=REPLACE");
        return command;
    }
}
