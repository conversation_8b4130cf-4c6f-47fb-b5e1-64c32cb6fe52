package com.xylink.manager.service.dts.handle;

import com.xylink.manager.model.dts.DatabaseTypeEnum;
import com.xylink.manager.repository.dts.DtsJobItemsRepository;
import com.xylink.manager.repository.dts.DtsJobRepository;
import com.xylink.manager.service.dts.DtsHandlerMapping;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/11/1 5:43 上午
 */
@Slf4j
@Component
public class DefaultDtsHandlerMapping implements DtsHandlerMapping {

    private final Map<DatabaseTypeEnum, Object> registerMapping = new HashMap<>();

    @Value("${dts.workspace}")
    private String workspace;
    @Resource
    private DtsJobRepository dtsJobRepository;
    @Resource
    private DtsJobItemsRepository dtsJobItemsRepository;

    @PostConstruct
    private void register() {
        registerMapping.put(DatabaseTypeEnum.MYSQL, new MysqlDtsHandler(workspace, dtsJobRepository, dtsJobItemsRepository));
        registerMapping.put(DatabaseTypeEnum.DM, new DmDtsHandler(workspace, dtsJobRepository, dtsJobItemsRepository));
        registerMapping.put(DatabaseTypeEnum.ST, new StDtsHandler(workspace, dtsJobRepository, dtsJobItemsRepository));
        registerMapping.put(DatabaseTypeEnum.JC, new JcDtsHandler(workspace, dtsJobRepository, dtsJobItemsRepository));

    }

    @Override
    public Object getHandler(String databaseType) {
        if (StringUtils.isBlank(databaseType)) {
            log.error("No handler found for:{}", databaseType);
            return null;
        }
        DatabaseTypeEnum databaseTypeEnum = EnumUtils.getEnum(DatabaseTypeEnum.class, databaseType.toUpperCase());
        return registerMapping.get(databaseTypeEnum);
    }
}
