package com.xylink.manager.service.dts.handle;

import com.xylink.manager.model.dts.ConnectionInfoReqDto;
import com.xylink.manager.repository.dts.DtsJobItemsRepository;
import com.xylink.manager.repository.dts.DtsJobRepository;
import com.xylink.manager.service.dts.constant.DtsConstant;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/11/17 4:16 下午
 */
public class JcDts<PERSON>and<PERSON> extends AbstractDtsHandler {

    public JcDtsHandler(String workspace, DtsJobRepository dtsJobRepository, DtsJobItemsRepository dtsJobItemsRepository) {
        super(workspace, dtsJobRepository, dtsJobItemsRepository);
    }

    @Override
    protected String queryDatabasesCommand(ConnectionInfoReqDto connectionInfoReqDto) {
        return DtsConstant.JC_COMMAND_SHOW_DATABASE;
    }

    /**
     * sys_dump "host=$ip port=$port user=$user password=$passwd dbname=$DBname" --schema=$DBname  -c --if-exists --inserts --column-inserts --no-tablespaces --no-unlogged-table-data --file=/tmp/"$DBname".sql >> /tmp/"$DBname"_bak.log 2>&1
     *
     * @param connectionInfoReqDto
     * @param object
     * @param backUpFile
     * @return
     */
    @Override
    protected List<String> backUpCommand(ConnectionInfoReqDto connectionInfoReqDto, String object, File backUpFile) {
        List<String> command = new ArrayList<>();
        command.add("sys_dump");
        command.add("host=" + connectionInfoReqDto.getIp() + " port=" + connectionInfoReqDto.getPort() + " user=" + connectionInfoReqDto.getUsername() + " password=" + connectionInfoReqDto.getPassword() + " dbname=" + connectionInfoReqDto.getDatabase());
        command.add("--schema=" + object);
        command.add("-c");
        command.add("--if-exists");
        command.add("--inserts");
        command.add("--column-inserts");
        command.add("--no-tablespaces");
        command.add("--no-unlogged-table-data");
        command.add("--file=" + getContainerPath(object) + "/" + backUpFile.getName());
        return command;
    }

    @Override
    protected void afterBackUp(File backupFile, File logFile) {
        if (backupFile == null || logFile == null) {
            return;
        }
        String filePath = backupFile.getPath();
        List<String> sedCommand = new ArrayList<>();
        sedCommand.add("sed");
        sedCommand.add("-i");
        sedCommand.add("/^SET escape = off;/d");
        sedCommand.add(filePath);
        exec(sedCommand, logFile);
        List<String> sedCommand2 = new ArrayList<>();
        sedCommand2.add("sed");
        sedCommand2.add("-i");
        sedCommand2.add("s/DROP SCHEMA IF EXISTS \\(.*\\);/DROP SCHEMA IF EXISTS \\1 CASCADE;/g");
        sedCommand2.add(filePath);
        exec(sedCommand2, logFile);
    }

    /**
     * ksql "host=$ip port=$port user=$user password=$passwd dbname=$DBname" --file=/tmp/"$DBname".sql -b --log-file=/tmp/"$DBname"_restory.log >>/tmp/"$DBname"_restory_error.log 2>&1
     *
     * @param connectionInfoReqDto
     * @param object
     * @param backUpFile
     * @return
     */
    @Override
    protected List<String> restoreCommand(ConnectionInfoReqDto connectionInfoReqDto, String object, File backUpFile) {
        List<String> command = new ArrayList<>();
        command.add("ksql");
        command.add("host=" + connectionInfoReqDto.getIp() + " port=" + connectionInfoReqDto.getPort() + " user=" + connectionInfoReqDto.getUsername() + " password=" + connectionInfoReqDto.getPassword() + " dbname=" + connectionInfoReqDto.getDatabase());
        command.add("--file=" + getContainerPath(object) + "/" + backUpFile.getName());
        return command;
    }
}
