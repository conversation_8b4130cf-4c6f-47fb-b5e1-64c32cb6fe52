package com.xylink.manager.service.dts;

import com.xylink.manager.model.dts.ConnectionInfoReqDto;
import com.xylink.manager.repository.dts.entity.DtsJobEntity;
import com.xylink.manager.repository.dts.entity.DtsJobItemsEntity;

import java.sql.SQLException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/10/31 2:24 下午
 */
public interface DtsHandlerAdapter {
    /**
     * 是否支持 handler
     *
     * @param handler
     * @return
     */
    boolean supports(Object handler);

    /**
     * 查询实例下数据库 非系统库
     *
     * @param connectionInfoReqDto
     * @param handler
     * @return
     * @throws SQLException
     */
    List<String> queryDatabases(ConnectionInfoReqDto connectionInfoReqDto, Object handler) throws SQLException;

    /**
     * 迁移
     *
     * @param dtsJobEntity
     * @param dtsJobItemsEntities
     * @param handler
     */
    void handleDts(DtsJobEntity dtsJobEntity, List<DtsJobItemsEntity> dtsJobItemsEntities, Object handler);

}
