package com.xylink.manager.service.dts.handle;

import com.xylink.manager.model.dts.ConnectionInfoReqDto;
import com.xylink.manager.repository.dts.entity.DtsJobEntity;
import com.xylink.manager.repository.dts.entity.DtsJobItemsEntity;
import com.xylink.manager.service.dts.DtsHandlerAdapter;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/11/17 4:16 下午
 */
@Component
public class JcDtsHandlerAdapter implements DtsHandlerAdapter {

    @Override
    public boolean supports(Object handler) {
        return handler instanceof JcDtsHandler;
    }

    @Override
    public List<String> queryDatabases(ConnectionInfoReqDto connectionInfoReqDto, Object handler) throws SQLException {
        return ((JcDtsHandler) handler).queryDatabases(connectionInfoReqDto);
    }

    @Override
    public void handleDts(DtsJobEntity dtsJobEntity, List<DtsJobItemsEntity> dtsJobItemsEntities, Object handler) {
        ((JcDtsHandler) handler).handleDts(dtsJobEntity, dtsJobItemsEntities);
    }
}
