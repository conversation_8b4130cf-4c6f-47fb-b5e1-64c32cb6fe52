package com.xylink.manager.service.dts.handle;

import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.model.dts.ConnectionInfoReqDto;
import com.xylink.manager.model.dts.DatabaseTypeEnum;
import com.xylink.manager.repository.dts.DtsJobItemsRepository;
import com.xylink.manager.repository.dts.DtsJobRepository;
import com.xylink.manager.repository.dts.entity.DtsJobEntity;
import com.xylink.manager.repository.dts.entity.DtsJobItemsEntity;
import com.xylink.manager.service.db.JasyptService;
import com.xylink.manager.service.dts.DtsHandler;
import com.xylink.manager.service.dts.constant.JobStatusEnum;
import com.xylink.util.CommandUtils;
import com.xylink.util.DbConfigUtil;
import com.xylink.util.DockerImagesUtils;
import com.xylink.util.SpringBeanUtil;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2022/11/1 6:55 上午
 */
public abstract class AbstractDtsHandler implements DtsHandler {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    public AbstractDtsHandler(String workspace, DtsJobRepository dtsJobRepository, DtsJobItemsRepository dtsJobItemsRepository) {
        this.workspace = workspace;
        this.dtsJobRepository = dtsJobRepository;
        this.dtsJobItemsRepository = dtsJobItemsRepository;
    }

    protected DtsJobItemsRepository dtsJobItemsRepository;

    protected DtsJobRepository dtsJobRepository;

    protected String workspace;

    protected String workspaceInContainer = "/usr/libra/dts";

    @Override
    public List<String> queryDatabases(ConnectionInfoReqDto connectionInfoReqDto) throws SQLException {
        Objects.requireNonNull(connectionInfoReqDto, "ConnectionInfoReqDto must not be null.");
        List<String> result = new ArrayList<>();
        try (Connection connection = DriverManager.getConnection(getUrl(connectionInfoReqDto), connectionInfoReqDto.getUsername(), connectionInfoReqDto.getPassword()); PreparedStatement ps = connection.prepareStatement(queryDatabasesCommand(connectionInfoReqDto)); ResultSet rs = ps.executeQuery()) {
            while (rs.next()) {
                result.add(rs.getString(1));
            }
        }
        if (result.isEmpty()) {
            return result;
        }
        List<String> excludeSystemDatabases = excludeDatabases(connectionInfoReqDto);
        if (!CollectionUtils.isEmpty(excludeSystemDatabases)) {
            result.removeAll(excludeSystemDatabases);
        }
        return result;
    }

    /**
     * 查询数据库的命令
     *
     * @param connectionInfoReqDto
     * @return
     */
    protected abstract String queryDatabasesCommand(ConnectionInfoReqDto connectionInfoReqDto);

    /**
     * 排除的数据库
     *
     * @param connectionInfoReqDto
     * @return
     */
    protected List<String> excludeDatabases(ConnectionInfoReqDto connectionInfoReqDto) {
        return Collections.emptyList();
    }

    @Override
    public void handleDts(DtsJobEntity dtsJobEntity, List<DtsJobItemsEntity> dtsJobItemsEntities) {
        logger.info("Dts running for {}-{}", dtsJobEntity, dtsJobItemsEntities);
        Objects.requireNonNull(dtsJobEntity, "DtsJobEntity must not be null.");
        Objects.requireNonNull(dtsJobItemsEntities, "DtsJobItemsEntity must not be null.");
        // 需要获取环境中的镜像
        String databaseType = dtsJobEntity.getDatabaseType();
        String databaseImage = databaseImage(databaseType);
        // 创建工作目录
        File workspaceDir = createWorkspaceDir(dtsJobEntity.getName());
        // 处理
        for (DtsJobItemsEntity dtsJobItemsEntity : dtsJobItemsEntities) {
            logger.info("Dts running for item {}-{}", dtsJobEntity, dtsJobItemsEntity);
            dtsJobItemsEntity.setStatus(JobStatusEnum.running.name());
            dtsJobItemsEntity.setStartTime(new java.util.Date());
            dtsJobItemsRepository.deleteByJodId(dtsJobEntity.getId());
            dtsJobItemsRepository.save(dtsJobItemsEntities, dtsJobEntity.getId());
            String object = dtsJobItemsEntity.getObject();

            try {
                File file = backUp0(databaseImage, object, dtsJobEntity, workspaceDir);
                boolean result = restore0(databaseImage, object, dtsJobEntity, file);
                dtsJobItemsEntity.setStatus(result ? JobStatusEnum.done.name() : JobStatusEnum.error.name());
            } catch (Exception e) {
                logger.error("BackAndRestoreError.", e);
                dtsJobItemsEntity.setStatus(JobStatusEnum.error.name());
            }

            dtsJobItemsEntity.setEndTime(new java.util.Date());
            dtsJobItemsRepository.deleteByJodId(dtsJobEntity.getId());
            dtsJobItemsRepository.save(dtsJobItemsEntities, dtsJobEntity.getId());
            logger.info("Dts done for item {}-{}", dtsJobEntity, dtsJobItemsEntity);
        }

        boolean anyError = dtsJobItemsEntities.stream().anyMatch(dtsJobItemsEntity -> JobStatusEnum.error.name().equals(dtsJobItemsEntity.getStatus()));
        dtsJobEntity.setStatus(anyError ? JobStatusEnum.error.name() : JobStatusEnum.done.name());
        java.util.Date now = new java.util.Date();
        dtsJobEntity.setEndTime(now);
        dtsJobEntity.setUpdateTime(now);
        // save job
        dtsJobRepository.deleteById(dtsJobEntity.getId());
        dtsJobRepository.save(dtsJobEntity);
        logger.info("Dts done for {}-{}", dtsJobEntity, dtsJobItemsEntities);
    }

    /**
     * 备份
     *
     * @param databaseImage
     * @param object
     * @param dtsJobEntity
     * @return
     */
    protected File backUp0(String databaseImage, String object, DtsJobEntity dtsJobEntity, File workspaceDir) {
        ConnectionInfoReqDto source = buildSource(dtsJobEntity);
        File file = createDtsFile(object, source.getDatabaseType(), workspaceDir);
        List<String> dockerCommands = new ArrayList<>();
        dockerCommands.add("docker");
        dockerCommands.add("run");
        dockerCommands.add("--rm");
        dockerCommands.add("-v");
        dockerCommands.add(getHostPath(file) + ":" + getContainerPath(object) + ":z");
        dockerCommands.add("--net=host");
        dockerCommands.add(databaseImage);
        dockerCommands.addAll(backUpCommand(source, object, file));
        File logFile = createBackUpLogFile(file);
        beforeBackUpLogAppend(logFile, dockerCommands);
        file.delete();
        exec(dockerCommands, logFile);
        afterBackUp(file,logFile);
        afterBackUpLogAppend(logFile);
        return file;
    }

    /**
     * 备份命令
     *
     * @param connectionInfoReqDto
     * @param object
     * @param backUpFile
     * @return
     */
    protected abstract List<String> backUpCommand(ConnectionInfoReqDto connectionInfoReqDto, String object, File backUpFile);

    /**
     * 记录备份信息
     *
     * @param logFile
     */
    protected void beforeBackUpLogAppend(File logFile, List<String> dockerCommands) {
        if (logFile == null) {
            return;
        }
        try (FileWriter fileWriter = new FileWriter(logFile, true)) {
            String commandPrint = StringUtils.join(dockerCommands.toArray(new String[0]), " ").replaceAll("U1O5ZeRyLFd#u9T6TF9h", "******");
            fileWriter.write(printlnLog("Exec back up command: " + commandPrint));
        } catch (IOException e) {
            logger.error("BeforeBackUpLogAppend error.", e);
        }
    }

    /**
     * 记录备份信息
     *
     * @param logFile
     */
    protected void afterBackUpLogAppend(File logFile) {
        if (logFile == null) {
            return;
        }
        try (FileWriter fileWriter = new FileWriter(logFile, true)) {
            fileWriter.write(printlnLog("end"));
        } catch (IOException e) {
            logger.error("BeforeBackUpLogAppend error.", e);
        }
    }

    /**
     * 备份完成后动作
     *
     * @param backupFile
     * @param logFile
     */
    protected void afterBackUp(File backupFile, File logFile){

    }

    /**
     * 还原
     *
     * @param databaseImage
     * @param object
     * @param dtsJobEntity
     * @param backUpFile
     * @return
     */
    protected boolean restore0(String databaseImage, String object, DtsJobEntity dtsJobEntity, File backUpFile) {
        ConnectionInfoReqDto target = buildTarget(dtsJobEntity);
        if (backUpFile.length() == 0) {
            logger.error("BackUp file is null.Interrupt operation!!!");
            return false;
        }
        List<String> dockerCommands = new ArrayList<>();
        dockerCommands.add("docker");
        dockerCommands.add("run");
        dockerCommands.add("--rm");
        dockerCommands.add("-v");
        dockerCommands.add(getHostPath(backUpFile) + ":" + getContainerPath(object) + ":z");
        dockerCommands.add("--net=host");
        dockerCommands.add(databaseImage);
        dockerCommands.addAll(restoreCommand(target, object, backUpFile));
        File logFile = createRestoreFile(backUpFile);
        beforeRestoreLogAppend(logFile, dockerCommands);
        boolean result = exec(dockerCommands, logFile);
        afterRestoreLogAppend(logFile);
        return result;
    }

    /**
     * 还原命令
     *
     * @return
     */
    protected abstract List<String> restoreCommand(ConnectionInfoReqDto connectionInfoReqDto, String object, File backUpFile);

    /**
     * 记录还原信息
     *
     * @param logFile
     */
    protected void beforeRestoreLogAppend(File logFile, List<String> dockerCommands) {
        if (logFile == null) {
            return;
        }
        try (FileWriter fileWriter = new FileWriter(logFile, true)) {
            String commandPrint = StringUtils.join(dockerCommands.toArray(new String[0]), " ").replaceAll("U1O5ZeRyLFd#u9T6TF9h", "******");
            fileWriter.write(printlnLog("Exec restore command: " + commandPrint));
        } catch (IOException e) {
            logger.error("BeforeRestoreLogAppend error.", e);
        }
    }

    /**
     * 记录还原信息
     *
     * @param logFile
     */
    protected void afterRestoreLogAppend(File logFile) {
        if (logFile == null) {
            return;
        }
        try (FileWriter fileWriter = new FileWriter(logFile, true)) {
            fileWriter.write(printlnLog("end"));
        } catch (IOException e) {
            logger.error("BeforeRestoreLogAppend error.", e);
        }
    }

    private String printlnLog(String log) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return format.format(new java.util.Date()) + " - " + log + "\r\n";
    }

    /**
     * 执行命令 并记录日志文件
     *
     * @param commands
     * @param logFile
     */
    protected boolean exec(List<String> commands, File logFile) {
        return CommandUtils.execLogExist(commands.toArray(new String[0]), logFile);
    }

    /**
     * jdbc url
     *
     * @param reqDto
     * @return
     * @throws SQLException
     */
    protected String getUrl(ConnectionInfoReqDto reqDto) throws SQLException {
        DbConfigUtil.DbConfig dbConfig = new DbConfigUtil.DbConfig();
        dbConfig.setDbType(reqDto.getDatabaseType());
        dbConfig.setAddress(reqDto.getIp());
        dbConfig.setPort(String.valueOf(reqDto.getPort()));
        dbConfig.setDatabaseName(reqDto.getDatabase());
        return dbConfig.jdbcConnectionTestUrl();
    }

    /**
     * database images
     *
     * @param databaseType
     * @return
     */
    protected String databaseImage(String databaseType) {
        DockerImagesUtils.ImagesEnum imagesEnum = EnumUtils.getEnum(DockerImagesUtils.ImagesEnum.class, databaseType);
        return DockerImagesUtils.getImages(imagesEnum);
    }

    @SuppressWarnings("ResultOfMethodCallIgnored")
    protected File createWorkspaceDir(String jobName) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
        File file = new File(getWorkspace() + "/" + jobName + "-" + formatter.format(new java.util.Date()));
        file.mkdirs();
        return file;
    }

    /**
     * 容器内备份文件
     *
     * @param target
     * @param databaseType
     * @return
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    protected File createDtsFile(String target, String databaseType, File workspaceDir) {
        DatabaseTypeEnum databaseTypeEnum = EnumUtils.getEnum(DatabaseTypeEnum.class, databaseType);
        String suffix;
        switch (databaseTypeEnum) {
            case DM:
                suffix = ".dmp";
                break;
            case ST:
                suffix = ".osr";
                break;
            default:
                suffix = ".sql";
        }
        File file = new File(workspaceDir, target + "/" + target + suffix);
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
        try {
            file.createNewFile();
        } catch (IOException e) {
            logger.error("Create file error.");
            throw new ServerException(ErrorStatus.DTS_JOB_CREATE_BACKUP_FILE_EXCEPTION);
        }
        return file;
    }

    /**
     * 备份日志文件
     *
     * @param backupFile
     * @return
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    protected File createBackUpLogFile(File backupFile) {
        if (!backupFile.exists()) {
            throw new ServerException("BackupFile: " + backupFile + " is not exists.");
        }
        String fileName = backupFile.getName();
        String logName = fileName.substring(0, fileName.lastIndexOf(".")) + "-backup.log";
        File file = new File(backupFile.getParent(), logName);
        if (file.exists()) {
            file.delete();
        }
        try {
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
            file.createNewFile();
        } catch (IOException e) {
            logger.error("create new file error!", e);
            throw new ServerException(e, ErrorStatus.UNEXPECTED_ERROR);
        }
        return file;
    }

    /**
     * 恢复日志文件
     *
     * @param restoreFile
     * @return
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    protected File createRestoreFile(File restoreFile) {
        if (!restoreFile.exists()) {
            throw new ServerException("RestoreFile: " + restoreFile + " is not exists.");
        }
        String fileName = restoreFile.getName();
        String logName = fileName.substring(0, fileName.lastIndexOf(".")) + "-restore.log";
        File file = new File(restoreFile.getParent(), logName);
        if (file.exists()) {
            file.delete();
        }
        try {
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
            file.createNewFile();
        } catch (IOException e) {
            logger.error("create new file error!", e);
            throw new ServerException(e, ErrorStatus.UNEXPECTED_ERROR);
        }
        return file;
    }

    protected ConnectionInfoReqDto buildSource(DtsJobEntity dtsJobEntity) {
        ConnectionInfoReqDto source = new ConnectionInfoReqDto();
        source.setDatabaseType(dtsJobEntity.getDatabaseType());
        source.setIp(dtsJobEntity.getSourceIp());
        source.setPort(dtsJobEntity.getSourcePort());
        source.setUsername(dtsJobEntity.getSourceUsername());
        JasyptService jasyptService = SpringBeanUtil.getBean(JasyptService.class);
        source.setPassword(jasyptService.decrypt(dtsJobEntity.getSourcePassword()));
        source.setDatabase(dtsJobEntity.getSourceDatabase());
        return source;
    }

    protected ConnectionInfoReqDto buildTarget(DtsJobEntity dtsJobEntity) {
        ConnectionInfoReqDto source = new ConnectionInfoReqDto();
        source.setDatabaseType(dtsJobEntity.getDatabaseType());
        source.setIp(dtsJobEntity.getTargetIp());
        source.setPort(dtsJobEntity.getTargetPort());
        source.setUsername(dtsJobEntity.getTargetUsername());
        JasyptService jasyptService = SpringBeanUtil.getBean(JasyptService.class);
        source.setPassword(jasyptService.decrypt(dtsJobEntity.getTargetPassword()));
        source.setDatabase(dtsJobEntity.getTargetDatabase());
        return source;
    }

    protected String getHostPath(File backUpFile) {
        return backUpFile.getParent();
    }

    protected String getContainerPath(String object) {
        return getWorkspaceInContainer() + "/" + object;
    }

    public String getWorkspace() {
        return workspace;
    }

    public String getWorkspaceInContainer() {
        return workspaceInContainer;
    }

    public void setWorkspaceInContainer(String workspaceInContainer) {
        this.workspaceInContainer = workspaceInContainer;
    }
}
