package com.xylink.manager.service.dts;

import com.xylink.manager.model.common.Page;
import com.xylink.manager.model.common.Pageable;
import com.xylink.manager.model.dts.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/10/27 2:15 下午
 */
public interface DtsService {
    /**
     * 获取实例类型列表
     *
     * @return
     */
    List<InstanceTypeDto> getInstanceTypes();

    /**
     * 获取数据库类型
     *
     * @return
     */
    List<DatabaseTypeDto> getDatabaseTypes();

    /**
     * 连接测试
     *
     * @param reqDto
     * @return
     */
    ConnectionTestDto connectionTest(ConnectionInfoReqDto reqDto);

    /**
     * 获取实例下对象
     *
     * @param reqDto
     * @return
     */
    List<String> getDatabase(ConnectionInfoReqDto reqDto);

    /**
     * 预检查
     *
     * @param dtsReqDto
     * @return
     */
    JobPreCheckDto jobPreCheck(DtsReqDto dtsReqDto);

    /**
     * 创建任务
     *
     * @param dtsReqDto
     */
    void jobCreate(DtsReqDto dtsReqDto);

    /**
     * 列表
     *
     * @param pageable
     * @return
     */
    Page<JobListDto> page(Pageable pageable);

    /**
     * 任务详情
     *
     * @param id
     * @return
     */
    JobDetailDto jobInfo(String id);

    /**
     * 开启任务
     *
     * @param id
     */
    void startJob(String id);

    /**
     * 删除任务
     *
     * @param id
     */
    void deleteById(String id);

}
