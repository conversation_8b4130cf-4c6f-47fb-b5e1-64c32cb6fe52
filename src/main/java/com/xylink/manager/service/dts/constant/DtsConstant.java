package com.xylink.manager.service.dts.constant;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/10/31 2:46 下午
 */
public abstract class DtsConstant {
    private DtsConstant() {
    }

    public static String MYSQL_COMMAND_SHOW_DATABASE = "show databases;";

    public static List<String> MYSQL_SYSTEM_DATABASE = Arrays.asList("mysql", "sys", "information_schema", "performance_schema");

    public static String DM_COMMAND_SHOW_DATABASE = "select username from dba_users where username not in ('SYSAUDITOR','SYSSSO','SYSDBA','SYS','DBBAK');";

    public static String ST_COMMAND_SHOW_DATABASE = "select username from dba_users where username not in ('SYSAUDIT','SYSDBA','SYSFTSDBA','SYSSECURE','DBBAK');";

    public static String JC_COMMAND_SHOW_DATABASE = "select username from dba_users where username not in('system','sao','sso','DBBA<PERSON>');";

}
