package com.xylink.manager.service.dts.handle;

import com.xylink.manager.model.dts.ConnectionInfoReqDto;
import com.xylink.manager.repository.dts.DtsJobItemsRepository;
import com.xylink.manager.repository.dts.DtsJobRepository;
import com.xylink.manager.service.dts.constant.DtsConstant;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/10/31 2:37 下午
 */
public class MysqlDtsHandler extends AbstractDtsHandler {

    public MysqlDtsHandler(String workspace, DtsJobRepository dtsJobRepository, DtsJobItemsRepository dtsJobItemsRepository) {
        super(workspace, dtsJobRepository, dtsJobItemsRepository);
    }

    @Override
    protected String queryDatabasesCommand(ConnectionInfoReqDto connectionInfoReqDto) {
        return DtsConstant.MYSQL_COMMAND_SHOW_DATABASE;
    }

    @Override
    protected List<String> excludeDatabases(ConnectionInfoReqDto connectionInfoReqDto) {
        return DtsConstant.MYSQL_SYSTEM_DATABASE;
    }

    @Override
    protected List<String> backUpCommand(ConnectionInfoReqDto connectionInfoReqDto, String object, File backUpFile) {
        List<String> command = new ArrayList<>();
        command.add("mysqldump");
        command.add("--add-drop-database");
        command.add("-h" + connectionInfoReqDto.getIp());
        command.add("-P" + connectionInfoReqDto.getPort());
        command.add("-u" + connectionInfoReqDto.getUsername());
        command.add("-p" + connectionInfoReqDto.getPassword());
        command.add(object);
        command.add("--set-gtid-purged=OFF");
        command.add("-r");
        command.add(getContainerPath(object) + "/" + backUpFile.getName());
        return command;
    }

    @Override
    protected List<String> restoreCommand(ConnectionInfoReqDto connectionInfoReqDto, String object, File backUpFile) {
        List<String> command = new ArrayList<>();
        command.add("mysql");
        command.add("-h" + connectionInfoReqDto.getIp());
        command.add("-P" + connectionInfoReqDto.getPort());
        command.add("-u" + connectionInfoReqDto.getUsername());
        command.add("-p" + connectionInfoReqDto.getPassword());
        command.add(object);
        command.add("-e");
        command.add("source " + getContainerPath(object) + "/" + backUpFile.getName());
        return command;
    }

}
