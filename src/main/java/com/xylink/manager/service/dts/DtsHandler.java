package com.xylink.manager.service.dts;

import com.xylink.manager.model.dts.ConnectionInfoReqDto;
import com.xylink.manager.repository.dts.entity.DtsJobEntity;
import com.xylink.manager.repository.dts.entity.DtsJobItemsEntity;

import java.sql.SQLException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/10/31 10:26 上午
 */
public interface DtsHandler {
    /**
     * 查询实例下数据库 非系统库
     *
     * @param connectionInfoReqDto
     * @return
     * @throws SQLException
     */
    List<String> queryDatabases(ConnectionInfoReqDto connectionInfoReqDto) throws SQLException;

    /**
     * 迁移
     *
     * @param dtsJobEntity
     * @param dtsJobItemsEntities
     */
    void handleDts(DtsJobEntity dtsJobEntity, List<DtsJobItemsEntity> dtsJobItemsEntities);

}
