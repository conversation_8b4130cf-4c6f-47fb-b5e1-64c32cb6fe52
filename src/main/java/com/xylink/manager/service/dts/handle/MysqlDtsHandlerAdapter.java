package com.xylink.manager.service.dts.handle;

import com.xylink.manager.model.dts.ConnectionInfoReqDto;
import com.xylink.manager.repository.dts.entity.DtsJobEntity;
import com.xylink.manager.repository.dts.entity.DtsJobItemsEntity;
import com.xylink.manager.service.dts.DtsHandlerAdapter;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/10/31 2:34 下午
 */
@Component
public class MysqlDtsHandlerAdapter implements DtsHandlerAdapter {
    @Override
    public boolean supports(Object handler) {
        return (handler instanceof MysqlDtsHandler);
    }

    @Override
    public List<String> queryDatabases(ConnectionInfoReqDto connectionInfoReqDto, Object handler) throws SQLException {
        return ((MysqlDtsHandler) handler).queryDatabases(connectionInfoReqDto);
    }

    @Override
    public void handleDts(DtsJobEntity dtsJobEntity, List<DtsJobItemsEntity> dtsJobItemsEntities, Object handler) {
        ((MysqlDtsHandler) handler).handleDts(dtsJobEntity, dtsJobItemsEntities);
    }
}
