package com.xylink.manager.service.api;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.service.base.K8sService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.Enumeration;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Service
public class JaegerApiService {

    // Jaeger服务地址前缀
    private static final String JAEGER_SERVER_PREFIX = "http://";
    private static final String JAEGER_SERVER_PORT = ":16686";
    // 缓存过期时间（毫秒）
    private static final long CACHE_EXPIRE_TIME = 5 * 60 * 1000; // 5分钟

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private K8sService k8sService;

    // 使用AtomicReference来存储缓存的服务地址和更新时间
    private final AtomicReference<CacheEntry> jaegerServerCache = new AtomicReference<>();

    private static class CacheEntry {
        private final String serverUrl;
        private final long timestamp;

        public CacheEntry(String serverUrl, long timestamp) {
            this.serverUrl = serverUrl;
            this.timestamp = timestamp;
        }

        public String getServerUrl() {
            return serverUrl;
        }

        public long getTimestamp() {
            return timestamp;
        }
    }

    /**
     * 获取Jaeger服务地址，带缓存机制
     */
    private String getJaegerServer() {
        CacheEntry currentCache = jaegerServerCache.get();
        long currentTime = System.currentTimeMillis();

        // 如果缓存为空或已过期，则更新缓存
        if (currentCache == null || (currentTime - currentCache.getTimestamp()) > CACHE_EXPIRE_TIME) {
            try {
                Map<String, String> allIps = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
                String newServerUrl = JAEGER_SERVER_PREFIX + allIps.get(NetworkConstants.JAEGER_IP)
                        + JAEGER_SERVER_PORT;
                CacheEntry newCache = new CacheEntry(newServerUrl, currentTime);

                // 使用CAS操作更新缓存
                if (jaegerServerCache.compareAndSet(currentCache, newCache)) {
                    log.info("Jaeger server address updated: {}", newServerUrl);
                }
            } catch (Exception e) {
                log.error("Failed to update Jaeger server address", e);
                // 如果更新失败且缓存为空，则使用默认值
                if (currentCache == null) {
                    throw new RuntimeException("Failed to get Jaeger server address", e);
                }
            }
        }

        // 返回当前缓存的值
        return jaegerServerCache.get().getServerUrl();
    }

    /**
     * 将请求转发到Jaeger服务
     * 
     * @param request  HTTP请求
     * @param response HTTP响应
     */
    public void getApiResult(HttpServletRequest request, HttpServletResponse response) {
        try {
            // 获取请求路径并转换
            String path = request.getRequestURI();

            // 获取最后一段路径
            int lastIndex = path.lastIndexOf("/api");
            if (lastIndex != -1) {
                path = path.substring(lastIndex);
            }

            // 构建目标URL
            String queryString = request.getQueryString();
            String urlStr = getJaegerServer() + path;
            if (queryString != null && !queryString.isEmpty()) {
                urlStr += "?" + queryString;
            }

            // 打印完整URL以便调试
            log.info("Original request query string: {}", queryString);
            log.info("Forwarding Jaeger request to URL: {}", urlStr);

            // 使用URI类构建URL，确保正确处理字符编码
            URI targetUri = new URI(urlStr);

            // 复制请求头
            HttpHeaders headers = new HttpHeaders();
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                String headerValue = request.getHeader(headerName);
                // 跳过某些头信息
                if (!"host".equalsIgnoreCase(headerName) &&
                        !"connection".equalsIgnoreCase(headerName) &&
                        !"content-length".equalsIgnoreCase(headerName)) {
                    headers.add(headerName, headerValue);
                }
            }

            // 创建请求实体
            HttpEntity<byte[]> httpEntity;
            if ("POST".equals(request.getMethod()) || "PUT".equals(request.getMethod())) {
                byte[] body = StreamUtils.copyToByteArray(request.getInputStream());
                httpEntity = new HttpEntity<>(body, headers);
            } else {
                httpEntity = new HttpEntity<>(headers);
            }

            // 发送请求
            ResponseEntity<byte[]> responseEntity = restTemplate.exchange(
                    targetUri,
                    HttpMethod.valueOf(request.getMethod()),
                    httpEntity,
                    byte[].class);

            // 设置响应状态
            response.setStatus(responseEntity.getStatusCodeValue());

            // 设置响应头
            responseEntity.getHeaders().forEach((name, values) -> {
                if (!"content-length".equalsIgnoreCase(name) &&
                        !"transfer-encoding".equalsIgnoreCase(name)) {
                    values.forEach(value -> response.addHeader(name, value));
                }
            });

            // 写入响应体
            if (responseEntity.getBody() != null) {
                response.getOutputStream().write(responseEntity.getBody());
            }

            log.info("Jaeger response status code: {}", responseEntity.getStatusCodeValue());

        } catch (URISyntaxException e) {
            log.error("Invalid URI syntax for Jaeger request", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            try {
                response.getWriter().write("Invalid URI syntax: " + e.getMessage());
            } catch (IOException ex) {
                log.error("Failed to write error response", ex);
            }
        } catch (HttpStatusCodeException e) {
            // 处理HTTP错误状态码
            log.error("Failed to forward Jaeger request with status: {}, response: {}",
                    e.getStatusCode(), new String(e.getResponseBodyAsByteArray(), StandardCharsets.UTF_8));
            response.setStatus(e.getStatusCode().value());
            try {
                if (e.getResponseBodyAsByteArray() != null) {
                    response.getOutputStream().write(e.getResponseBodyAsByteArray());
                }
            } catch (IOException ex) {
                log.error("Failed to write error response", ex);
            }
        } catch (Exception e) {
            // 处理其他异常
            log.error("Failed to forward Jaeger request", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            try {
                response.getWriter().write("Failed to forward Jaeger request: " + e.getMessage());
            } catch (IOException ex) {
                log.error("Failed to write error response", ex);
            }
        }
    }
}
