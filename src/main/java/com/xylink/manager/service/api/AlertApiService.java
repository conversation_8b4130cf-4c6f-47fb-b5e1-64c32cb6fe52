package com.xylink.manager.service.api;

import com.xylink.manager.controller.dto.alert.AlertApiDto;
import com.xylink.manager.controller.dto.alert.AlertApiRes;
import com.xylink.manager.controller.dto.alert.AlertEventDto;
import com.xylink.manager.controller.dto.alert.AlertType;
import com.xylink.manager.model.common.Page;
import com.xylink.manager.model.common.PageRequest;
import com.xylink.manager.model.em.EnhanceLevels;
import com.xylink.manager.service.AlertConfigService;
import com.xylink.manager.service.base.IDeployService;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class AlertApiService {
    private final static Logger logger = LoggerFactory.getLogger(AlertApiService.class);

    @Autowired
    private AlertConfigService alertConfigService;
    @Autowired
    private IDeployService deployService;

    public AlertApiRes getAlertList(String eventType) {
        String measurement;
        if ("k8s".equals(eventType)) {
            measurement = AlertType.K8S_ALERT_MEASUREMENT.getType();
        } else if ("bus".equals(eventType)) {
            measurement = AlertType.BUS_ALERT_MEASUREMENT.getType();
        } else {
            measurement = AlertType.HOST_ALERT_MEASUREMENT.getType();
        }
        PageRequest pageRequest = new PageRequest(1, 1000);
        Page<AlertEventDto> alertEventList = alertConfigService.getAlertEventPage(true, measurement, pageRequest);
        return install(alertEventList.getRecords(), eventType);
    }

    public AlertApiRes install(List<AlertEventDto> data, String type) {
        AlertApiRes alertApiRes = new AlertApiRes();
        alertApiRes.setCode(HttpStatus.SC_OK);
        if ("k8s".equalsIgnoreCase(type)) {
            installK8s(data);
        }
        if ("bus".equalsIgnoreCase(type)) {
            installBus(data);
        }
        if ("host".equalsIgnoreCase(type)) {
            installHost(data);
        }
        List<AlertApiDto> apiDtos = data.stream().map(AlertApiDto::transform).collect(Collectors.toList());
        alertApiRes.setData(apiDtos);
        return alertApiRes;
    }

    private void installK8s(List<AlertEventDto> data) {
        Map<String, String> nodeAndInternalIP = nodeAndInternalIP();
        data.forEach(event -> {
            k8sSetEnhanceLevel(event);
            event.setInternalIp(StringUtils.isBlank(nodeAndInternalIP.get(event.getNodeName())) ? "" : nodeAndInternalIP.get(event.getNodeName()));
        });
    }

    private void installBus(List<AlertEventDto> data) {
        Map<String, String> podAndNodeName = podAndNodeName();
        Map<String, String> nodeAndInternalIP = nodeAndInternalIP();
        data.forEach(event -> {
            String serviceName = podAndNodeName.entrySet().stream().filter(entry ->
                            entry.getKey().contains(event.getServiceName()) &&
                                    entry.getValue().equalsIgnoreCase(event.getNodeName())).
                    map(Map.Entry::getKey).findFirst().orElse(event.getServiceName());
            event.setServiceName(serviceName);
            busSetEnhance(event);
            event.setInternalIp(StringUtils.isBlank(nodeAndInternalIP.get(event.getNodeName())) ? "" : nodeAndInternalIP.get(event.getNodeName()));
        });
    }

    private void installHost(List<AlertEventDto> data) {
        Map<String, String> nodeAndInternalIP = nodeAndInternalIP();
        data.forEach(event -> {
            event.setEnhanceLevel(StringUtils.isBlank(event.getEnhanceLevel()) ? EnhanceLevels.common.getEnhanceLevel() : event.getEnhanceLevel());
            event.setInternalIp(StringUtils.isBlank(nodeAndInternalIP.get(event.getNodeName())) ? "" : nodeAndInternalIP.get(event.getNodeName()));
        });
    }

    private boolean isNotRemoveEvent(String detail) {
        return detail.contains("|1|") || detail.contains("|2|") || detail.contains("|3|");
    }

    private void busSetEnhance(AlertEventDto alertEventDto) {
        String[] split = alertEventDto.getDetail().split("\\|");
        String enhanceLevel = "";
        if (split.length > 0) {
            enhanceLevel = Arrays.stream(split).filter(s -> s.length() == 1 && "1,2,3".contains(s)).findFirst().orElse("");
        }
        if ("1".equalsIgnoreCase(enhanceLevel)) {
            enhanceLevel = EnhanceLevels.serious.getEnhanceLevel();
        } else if ("2".equalsIgnoreCase(enhanceLevel)) {
            enhanceLevel = EnhanceLevels.important.getEnhanceLevel();
        } else {
            enhanceLevel = EnhanceLevels.common.getEnhanceLevel();
        }
        alertEventDto.setEnhanceLevel(enhanceLevel);
    }

    private void k8sSetEnhanceLevel(AlertEventDto alertEventDto) {
        //初始值
        alertEventDto.setEnhanceLevel(EnhanceLevels.common.getEnhanceLevel());
        //日志增强
        for (EnhanceLevels enhanceLevel : EnhanceLevels.values()) {
            if (enhanceLevel.getInitialLevel().contains(alertEventDto.getReason())) {
                alertEventDto.setEnhanceLevel(enhanceLevel.getEnhanceLevel());
                break;
            }
        }
    }

    private Map<String, String> podAndNodeName() {
        Map<String, String> map = new HashMap<>();

        try {
            deployService.listAllPod().forEach(pod -> map.put(pod.getPodName(), pod.getNodeName()));
        } catch (Exception e) {
            logger.error("fail to get node names for all pods", e);
        }
        return map;
    }

    private Map<String, String> nodeAndInternalIP() {
        Map<String, String> map = new HashMap<>();

        try {
            deployService.listAllNodes().forEach(node -> {
                map.put(node.getName(), node.getIp());
            });
        } catch (Exception e) {
            logger.error("fail to get all node ip address", e);
        }

        return map;
    }
}
