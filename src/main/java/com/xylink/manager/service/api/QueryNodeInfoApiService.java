package com.xylink.manager.service.api;

import com.xylink.manager.controller.dto.query.NodeInfoApiRes;
import com.xylink.manager.controller.dto.query.NodeInfoDto;
import com.xylink.manager.service.QueryNodeInfoService;
import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * @Author: liyang
 * @DateTime: 2021/8/5 6:29 下午
 **/
@Service
public class QueryNodeInfoApiService {
    @Autowired
    private QueryNodeInfoService queryNodeInfoService;

    public NodeInfoApiRes getNodeInfoApiResByIp(String internalIp){
        return install(queryNodeInfoService.getNodeInfoByIp(internalIp));
    }

    public NodeInfoApiRes install(NodeInfoDto nodeInfoDto){
        NodeInfoApiRes nodeInfoApiRes = new NodeInfoApiRes();
        nodeInfoApiRes.setCode(HttpStatus.SC_OK);
        nodeInfoApiRes.setData(nodeInfoDto);
        return nodeInfoApiRes;
    }

}
