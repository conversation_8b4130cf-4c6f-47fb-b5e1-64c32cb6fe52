package com.xylink.manager.service.api;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.model.registry.ImageNoahVarDTO;
import com.xylink.manager.model.upgrade.ImagesPublishDto;
import com.xylink.manager.model.upgrade.PublishAppMetadataDto;
import com.xylink.util.Ipv6Util;
import com.xylink.util.NoahEncryptUtil;
import com.xylink.util.SecurityContextUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RequestCallback;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Nonnull;
import javax.annotation.Resource;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;

/**
 * <AUTHOR> create on 2024/3/25
 */
@Slf4j
@Service
public class NoahApiService {

    @Value("${noah.ip:127.0.0.1}")
    private String noahIp;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private NoahEncryptUtil noahEncryptUtil;
    @Resource(name = "fileRestTemplate")
    private RestTemplate fileRestTemplate;
    @Resource(name = "longtimeRestTemplate")
    private RestTemplate longtimeRestTemplate;

    private static final String URI_IMAGE_LIST = "/api/rest/internal/v1/noah/image/list";
    private static final String URI_APP_VAR_IMAGE_LIST = "/api/rest/internal/v1/noah/image/var/list?appName=%s";
    private static final String URI_IMAGE_APPLY = "/api/rest/internal/v1/noah/image/apply";
    private static final String URI_APPNAMES = "/api/rest/internal/v1/noah/resource/appNames";
    private static final String URL_UPGRADE = "/api/rest/internal/v1/noah/iteration/config/apply";
    private static final String URL_UPGRADE_DETAIL = "/api/rest/internal/v1/noah/iteration/config/apply/detail";
    private static final String URL_BACKUP = "/api/rest/internal/v1/noah/backup/datas/backup";
    private static final String URL_CONFIG_PUBLISH = "/api/rest/internal/v1/noah/iteration/config/publish";
    private static final String URL_IMAGES_PUBLISH = "/api/rest/internal/v1/noah/iteration/image/publish";
    private static final String URI_NOTIFY_NGINX_PORT = "/api/rest/internal/v1/noah/config/entity/varupdate";
    private static final String URI_NOTIFY_VAR_UPDATE = "/api/rest/internal/v1/noah/config/entity/varupdate";
    //更新配置中心某个具体的标签变动
    private static final String URI_NOTIFY_DEPLOY_UPDATE = "/api/rest/internal/v1/noah/module/deploy/put";

    private static final String URI_MODULE_LIST_WITH_STATUS = "/api/rest/internal/v1/noah/module/status";
    private static final String URI_SERVER_LIST = "/api/rest/internal/v1/noah/server/list";
    private static final String URI_CHANGE_DEPLOY_MODE = "/api/rest/internal/v1/noah/server/run/mode";
    private static final String URI_PROXY_UPDATE_CONFIG_MAP = "/api/rest/internal/v1/noah/adapter/proxy/config/map";
    private static final String URI_SERVER_LIST_BY_SERVER_NAME = "/api/rest/internal/v1/noah/server/list/server/name";
    private static final String URI_VAR_SELECT = "/api/rest/internal/v1/noah/var/select?dataId={dataId}";

    public List<ImageListVO> getImageList() {
        String body = "";
        try {
            String url = getNoahApiPrefix() + URI_IMAGE_LIST;
            ResponseEntity<String> resp = restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<>(getNoahHeader()), String.class);
            body = resp.getBody();
            TypeToken<List<ImageListVO>> typeToken = new TypeToken<List<ImageListVO>>() {
            };
            return new Gson().fromJson(body, typeToken.getType());
        } catch (Exception ex) {
            throw new ServerException("[noah_api]get image list error, body:" + body, ex);
        }
    }

    private HttpHeaders getNoahHeader() {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", noahEncryptUtil.getNoahAuthorization());
        try {
            headers.add("username", SecurityContextUtil.currentUser());
        } catch (Exception e) {
            log.warn("get current user error", e);
            headers.add("username", "someone@manager");
        }
        return headers;
    }

    public List<ImageNoahVarDTO> getVarImageList(String appName) {
        String body = "";
        try {
            String url = getNoahApiPrefix() + URI_APP_VAR_IMAGE_LIST;
            url = String.format(url, appName);
            ResponseEntity<String> resp = restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<>(getNoahHeader()), String.class);
            body = resp.getBody();
            TypeToken<List<ImageNoahVarDTO>> typeToken = new TypeToken<List<ImageNoahVarDTO>>() {
            };
            return new Gson().fromJson(body, typeToken.getType());
        } catch (Exception ex) {
            throw new ServerException("[noah_api]get image var path error of" + appName + ", body:" + body, ex);
        }
    }

    public void applyImage(String imageVarName, String imagePath, String traceId) {
        String url = getNoahApiPrefix() + URI_IMAGE_APPLY;
        log.info("{} [noah_api_apply_image]-----------req url:{}", traceId, url);
        Map<String, String> param = Maps.newHashMap();
        param.put("imageVarName", imageVarName);
        param.put("imagePath", imagePath);
        HttpEntity<Map<String, String>> entity = new HttpEntity<>(param, getNoahHeader());
        log.info("{} [noah_api_apply_image]-----------req param:{}", traceId, new Gson().toJson(param));
        ResponseEntity<String> resp = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
        HttpStatus statusCode = resp.getStatusCode();
        log.info("{} [noah_api_apply_image]-----------resp code:{}", traceId, statusCode);
        if (!statusCode.is2xxSuccessful()) {
            throw new ServerException(traceId + "[noah_api]apply image error of [" + imageVarName + "]-[" + imagePath + "], statusCode:" + statusCode);
        }
    }

    public List<String> getAllServices() {
        String url = getNoahApiPrefix() + URI_APPNAMES;
        String body = "";
        try {
            ResponseEntity<String> resp = restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<>(getNoahHeader()), String.class);
            body = resp.getBody();
            DocumentContext ctx = JsonPath.parse(body);
            return ctx.read("$.*['name']");
        } catch (Exception ex) {
            throw new ServerException("[noah_api]get all app names error, body:" + body, ex);
        }
    }

    public List<ServiceGroupDTO> getAllServiceInfos() {
        String url = getNoahApiPrefix() + URI_APPNAMES;
        String body = "";
        try {
            ResponseEntity<String> resp = restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<>(getNoahHeader()), String.class);
            body = resp.getBody();
            TypeToken<List<ServiceGroupDTO>> typeToken = new TypeToken<List<ServiceGroupDTO>>() {
            };
            return new Gson().fromJson(body, typeToken.getType());
        } catch (Exception ex) {
            throw new ServerException("[noah_api]get all app names error, body:" + body, ex);
        }
    }


    public void configApply(String packageName, File file) {
        try {
            String url = getNoahApiPrefix() + URL_UPGRADE;
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("applyId", packageName);
            body.add("file", new FileSystemResource(file));
            HttpHeaders noahHeader = getNoahHeader();
            noahHeader.setContentType(MediaType.MULTIPART_FORM_DATA);
            longtimeRestTemplate.exchange(url, HttpMethod.POST, new HttpEntity<>(body, noahHeader), String.class);
        } catch (Exception ex) {
            throw new ServerException("[noah_api] config apply error.packageName:" + packageName, ex);
        }
    }

    public ConfigApplyDetailResponse configApplyDetail(String packageName) {
        String url = getNoahApiPrefix() + URL_UPGRADE_DETAIL + "?applyId=" + packageName;
        ResponseEntity<ConfigApplyDetailResponse> response = restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<>(getNoahHeader()), ConfigApplyDetailResponse.class);
        return response.getBody();
    }

    public Path configBackUp(String releaseVersion, String backUpFilePath) {
        String url = getNoahApiPrefix() + URL_BACKUP;
        RequestCallback requestCallback = request -> {
            request.getHeaders().setAccept(Arrays.asList(MediaType.APPLICATION_OCTET_STREAM, MediaType.ALL));
            request.getHeaders().add("username", "license");
            request.getHeaders().add("Authorization", noahEncryptUtil.getNoahAuthorization());
        };

        return fileRestTemplate.execute(url, HttpMethod.GET, requestCallback, clientHttpResponse -> {
            String fileName = getFileNameFromResponse(clientHttpResponse.getHeaders());
            if (StringUtils.isBlank(fileName)) {
                fileName = releaseVersion + ".zip";
            }

            File destFile = new File(backUpFilePath, fileName);
            if (!destFile.getParentFile().exists() && !destFile.getParentFile().mkdir()) {
                throw new ServerException("parentDestFile mkdir error");
            }

            Path target = Paths.get(backUpFilePath, fileName);
            Files.copy(clientHttpResponse.getBody(), target, StandardCopyOption.REPLACE_EXISTING);
            return target;
        });
    }

    public List<PublishAppMetadataDto> publishConfig(String packageName) {
        try {
            String url = getNoahApiPrefix() + URL_CONFIG_PUBLISH + "?applyId=" + packageName;
            HttpHeaders noahHeader = getNoahHeader();
            noahHeader.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(noahHeader);
            log.info("[noah_api] config publish request:[{}]", requestEntity);
            ResponseEntity<List<PublishAppMetadataDto>> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, new ParameterizedTypeReference<List<PublishAppMetadataDto>>() {
            });
            log.info("[noah_api] config publish response:[{}]", response);
            return response.getBody();
        } catch (Exception ex) {
            throw new ServerException("[noah_api] config publish error.packageName:" + packageName, ex);
        }
    }

    public List<PublishAppMetadataDto> publishImages(String packageName, ImagesPublishDto imagesPublishDto) {
        try {
            String url = getNoahApiPrefix() + URL_IMAGES_PUBLISH;
            HttpHeaders noahHeader = getNoahHeader();
            noahHeader.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<ImagesPublishDto> requestEntity = new HttpEntity<>(imagesPublishDto, noahHeader);
            log.info("[noah_api] images publish request:[{}]", requestEntity);
            ResponseEntity<List<PublishAppMetadataDto>> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, new ParameterizedTypeReference<List<PublishAppMetadataDto>>() {
            });
            log.info("[noah_api] images publish response:[{}]", response);
            return response.getBody();
        } catch (Exception ex) {
            throw new ServerException("[noah_api] images publish error.packageName:" + packageName, ex);
        }
    }

    private String getFileNameFromResponse(HttpHeaders headers) {
        String contentDisposition = headers.getFirst(HttpHeaders.CONTENT_DISPOSITION);
        if (contentDisposition != null && contentDisposition.contains("filename=")) {
            String fileName = contentDisposition.substring(contentDisposition.indexOf("filename=") + 9);
            fileName = fileName.replaceAll("\"", "");
            return fileName;
        }
        return null;
    }

    public void notifyNginxPort(Map<String, Object> paramMap) {
        String traceId = UUID.randomUUID().toString().replace("-", "");
        String url = getNoahApiPrefix() + URI_NOTIFY_NGINX_PORT;
        HttpHeaders noahHeader = getNoahHeader();
        noahHeader.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map> requestEntity = new HttpEntity<>(paramMap, noahHeader);
        log.info("{} [noah_api] notify nginx port request:[{}]", traceId, new Gson().toJson(paramMap));
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
        HttpStatus statusCode = response.getStatusCode();
        log.info("{} [noah_api] notify nginx port response:[{}]", traceId, response);
        log.info("{} [noah_api] notify nginx port status:[{}]", traceId, statusCode.value());
        if (!statusCode.is2xxSuccessful()) {
            throw new ServerException("[noah_api]notify nginx port error of [" + new Gson().toJson(paramMap) + "], statusCode:" + statusCode);
        }
    }

    public void notifyVarUpdate(Map<String, Object> paramMap) {
        String traceId = UUID.randomUUID().toString().replace("-", "");
        String url = getNoahApiPrefix() + URI_NOTIFY_VAR_UPDATE;
        HttpHeaders noahHeader = getNoahHeader();
        noahHeader.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map> requestEntity = new HttpEntity<>(paramMap, noahHeader);
        log.info("{} [noah_api] notify var update request:[{}]", traceId, new Gson().toJson(paramMap));
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
        HttpStatus statusCode = response.getStatusCode();
        log.info("{} [noah_api] notify var update response:[{}]", traceId, response);
        log.info("{} [noah_api] notify var update status:[{}]", traceId, statusCode.value());
        if (!statusCode.is2xxSuccessful()) {
            throw new ServerException("[noah_api]notify var update error of [" + new Gson().toJson(paramMap) + "], statusCode:" + statusCode);
        }
    }

    public List<ModuleStatus> getAllModules() {
        try {
            String url = getNoahApiPrefix() + URI_MODULE_LIST_WITH_STATUS;
            HttpHeaders noahHeader = getNoahHeader();
            noahHeader.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(noahHeader);
            log.info("[noah_api] getAllModules request:[{}]", requestEntity);
            ResponseEntity<List<ModuleStatus>> response = restTemplate.exchange(url, HttpMethod.GET, requestEntity, new ParameterizedTypeReference<List<ModuleStatus>>() {
            });
            log.info("[noah_api] getAllModules response:[{}]", response);
            return response.getBody();
        } catch (Exception ex) {
            throw new ServerException("[noah_api] getAllModules error,", ex);
        }
    }

    public void notifyDeployLabelChange() {
        String traceId = UUID.randomUUID().toString().replace("-", "");
        try {
            String url = getNoahApiPrefix() + URI_NOTIFY_DEPLOY_UPDATE;
            HttpHeaders noahHeader = getNoahHeader();
            noahHeader.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<List<Map<String, Object>>> requestEntity = new HttpEntity<>(noahHeader);
            log.info("{} [noah_api] update deploy label request url {}", traceId, url);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
            HttpStatus statusCode = response.getStatusCode();
            log.info("{} [noah_api] update deploy label response:[{}]", traceId, response);
            log.info("{} [noah_api] update deploy label status:[{}]", traceId, statusCode.value());
            if (!statusCode.is2xxSuccessful()) {
                throw new ServerException("[noah_api]notify deploy label error, statusCode:" + statusCode);
            }
        } catch (Exception ex) {
            log.warn("{} [noah_api] update deploy label error {}", traceId, ex.getMessage(), ex);
        }

    }

    public List<NoahServerInfo> serverList() {
        String traceId = UUID.randomUUID().toString().replace("-", "");
        try {
            String url = getNoahApiPrefix() + URI_SERVER_LIST;
            HttpHeaders noahHeader = getNoahHeader();
            log.info("{} [noah_api] server list request", traceId);
            ResponseEntity<NoahServerInfo[]> response = restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<>(noahHeader), NoahServerInfo[].class);
            HttpStatus statusCode = response.getStatusCode();
            log.info("{} [noah_api] server list response:[{}]", traceId, response);
            log.info("{} [noah_api] server list:[{}]", traceId, statusCode.value());
            if (!statusCode.is2xxSuccessful()) {
                throw new ServerException("[noah_api] server list error, statusCode:" + statusCode);
            }
            return Arrays.asList(response.getBody());
        } catch (Exception ex) {
            log.warn("{} [noah_api] server list error {}", traceId, ex.getMessage(), ex);
        }
        return Collections.emptyList();
    }

    public void proxyUpdateConfigMap(@Nonnull ProxyConfigMap configMap) {
        String url = getNoahApiPrefix() + URI_PROXY_UPDATE_CONFIG_MAP;
        HttpHeaders noahHeader = getNoahHeader();
        HttpEntity<ProxyConfigMap> requestEntity = new HttpEntity<>(configMap, noahHeader);
        ResponseEntity<Void> response = restTemplate.exchange(url, HttpMethod.PUT, requestEntity, Void.class);
        HttpStatus statusCode = response.getStatusCode();
        log.info("[noah_api] proxy update config map: {}", statusCode.value());
        if (!statusCode.is2xxSuccessful()) {
            throw new ServerException("[noah_api] proxy update config map error, statusCode:" + statusCode);
        }
    }

    public void changeDeployMode(String serverName, String deployType, Integer replica) {
        String traceId = UUID.randomUUID().toString().replace("-", "");
        try {
            String url = getNoahApiPrefix() + URI_CHANGE_DEPLOY_MODE;
            log.info("{} [noah_api] server change deploy mode request:{},{},{}", traceId, serverName, deployType, replica);
            Map<String, Object> body = new HashMap<>();
            body.put("serverName", serverName);
            body.put("deployType", deployType);
            body.put("replica", replica);
            HttpHeaders noahHeader = getNoahHeader();
            HttpEntity<Object> entity = new HttpEntity<>(body, noahHeader);
            ResponseEntity<Void> response = longtimeRestTemplate.exchange(url, HttpMethod.PUT, entity, Void.class);
            HttpStatus statusCode = response.getStatusCode();
            log.info("{} [noah_api] server change deploy mode response:[{}]", traceId, response);
            log.info("{} [noah_api] server change deploy mode:[{}]", traceId, statusCode.value());
            if (!statusCode.is2xxSuccessful()) {
                throw new ServerException("[noah_api]访问配置中心失败, statusCode:" + statusCode);
            }
        } catch (Exception ex) {
            throw new ServerException("[noah_api]访问配置中心异常, msg:" + ex.getMessage(), ex);
        }
    }


    public String getControllerType(String serverName) {
        String traceId = UUID.randomUUID().toString().replace("-", "");
        try {
            String url = getNoahApiPrefix() + URI_SERVER_LIST_BY_SERVER_NAME;
            log.info("{} [noah_api] server get controller type request:{},", traceId, serverName);

            HttpHeaders noahHeader = getNoahHeader();
            HttpEntity<Object> entity = new HttpEntity<>(Lists.newArrayList(serverName), noahHeader);
            ResponseEntity<NoahServerInfo[]> response = restTemplate.exchange(url, HttpMethod.POST, entity, NoahServerInfo[].class);
            HttpStatus statusCode = response.getStatusCode();
            log.info("{} [noah_api] server get controller type  response:[{}]", traceId, response);
            log.info("{} [noah_api] server get controller type :[{}]", traceId, statusCode.value());
            if (!statusCode.is2xxSuccessful()) {
                throw new ServerException("[noah_api]访问配置中心失败, statusCode:" + statusCode);
            }
            NoahServerInfo[] body = response.getBody();
            if (Objects.isNull(body) || body.length < 1) {
                throw new ServerException("[noah_api]配置数据查询为空, msg");
            }
            return body[0].getResType();
        } catch (Exception ex) {
            throw new ServerException("[noah_api]访问配置中心异常, msg:" + ex.getMessage(), ex);
        }
    }

    public Map<String, String> selectVars(String dataId, String[] varNames) {
        String traceId = UUID.randomUUID().toString().replace("-", "");
        try {
            String url = getNoahApiPrefix() + URI_VAR_SELECT;
            log.info("{} [noah_api] select vars request:{},", traceId, varNames);

            HttpHeaders noahHeader = getNoahHeader();
            HttpEntity<Object> entity = new HttpEntity<>(varNames, noahHeader);
            ResponseEntity<Map<String, String>> response = restTemplate.exchange(url, HttpMethod.POST, entity, new ParameterizedTypeReference<Map<String, String>>() {
            }, dataId);
            HttpStatus statusCode = response.getStatusCode();
            log.info("{} [noah_api] select vars  response:[{}]", traceId, response);
            log.info("{} [noah_api] select vars :[{}]", traceId, statusCode.value());
            if (!statusCode.is2xxSuccessful()) {
                throw new ServerException("[noah_api]访问配置中心失败, statusCode:" + statusCode);
            }
            return response.getBody();
        } catch (Exception ex) {
            throw new ServerException("[noah_api]访问配置中心异常, msg:" + ex.getMessage(), ex);
        }
    }

    public ProxyConfigMap proxyQueryConfigMap(String name, String namespace) {
        String traceId = UUID.randomUUID().toString().replace("-", "");
        try {
            String url = getNoahApiPrefix() + URI_PROXY_UPDATE_CONFIG_MAP + "?name={name}&namespace={namespace}";
            log.info("{} [noah_api] proxy query config map request: name={}, namespace={}", traceId, name, namespace);
            HttpHeaders noahHeader = getNoahHeader();
            HttpEntity<Object> entity = new HttpEntity<>(noahHeader);
            ResponseEntity<ProxyConfigMap> response = restTemplate.exchange(url, HttpMethod.GET, entity, ProxyConfigMap.class, name, namespace);
            HttpStatus statusCode = response.getStatusCode();
            log.info("{} [noah_api] proxy query config map response:[{}]", traceId, response);
            log.info("{} [noah_api] proxy query config map status:[{}]", traceId, statusCode.value());
            if (!statusCode.is2xxSuccessful()) {
                throw new ServerException("[noah_api]proxy query config map error, statusCode:" + statusCode);
            }
            ProxyConfigMap body = response.getBody();
            log.info("{} [noah_api] proxy config map query result: {}", traceId, new Gson().toJson(body));
            return body;
        } catch (Exception ex) {
            throw new ServerException("[noah_api]proxy query config map error, name: " + name + ", namespace: " + namespace + ", msg:" + ex.getMessage(), ex);
        }
    }


    @Data
    public static class ServiceGroupDTO {
        /**
         * 服务
         */
        private String name;

        /**
         * 分组（以-middle结尾代表中间件）
         */
        private String groupId;
    }

    @Data
    public static class ImageListVO {

        private String appName;

        private List<Item> itemList;

        @Data
        public static class Item {
            private String imageVarName;
            private String imagePath;
        }

    }

    public String getNoahApiPrefix() {
        return "http://" + Ipv6Util.handlerIpv6Addr(noahIp) + ":18315";
    }

    @Data
    public static class ConfigApplyDetailResponse {
        private String status;
        /*
         * 升级状态枚举,NOT_FOUND,WAIT,SUCCESS,FAIL
         */
        private String statusEnum;
        private boolean finish;
        private List<ConfigApplyDetailItem> items;

        public boolean isNotFound() {
            return "NOT_FOUND".equals(this.statusEnum);
        }
    }

    @Data
    public static class ConfigApplyDetailItem {
        private String appName;
        private String fileType;
        private String id;
        private String status;
        private String reason;
        private String remark;
    }

    @Data
    public static class ModuleStatus {
        private String moduleName;
        private String moduleTitle;
        private boolean enable;
        private List<Map<String, Boolean>> serviceList;
    }

    @Data
    public static class NoahServerInfo {
        private String serverName;
        private String deployType;
        private String resType;
        private String imageTag;
    }


    @Data
    public static class ProxyMetadata {
        private String name;
        private String namespace;
    }

    @Data
    public static class ProxyConfigMap {
        private ProxyMetadata metadata;
        private Map<String, String> data;
    }
}
