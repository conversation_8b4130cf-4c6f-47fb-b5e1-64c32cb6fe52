package com.xylink.manager.service.api;

import com.google.common.collect.Lists;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.service.base.K8sService;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONArray;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * <AUTHOR> create on 2024/3/21
 */
@Slf4j
@Service
public class RegistryApiService {

    private static final String URI_REPOSITORIES = "/v2/_catalog?n=%s&last=%s";
    private static final String URI_TAGS = "/v2/%s/tags/list?n=%s&last=%s";
    private static final String URI_MANIFESTS = "/v2/%s/manifests/%s";
    private static final String URI_MANIFEST_DELETE = "/v2/%s/manifests/%s";

    private static final String REGISTRY_SECRET_NAME = "private-cloud-ali";
    private static final String REGISTRY_SECRET_KEY = ".dockerconfigjson";


    @Value("${harbor.prefix.url}")
    private String harborUrl;
    @Value("${harbor.basicauth.username:read}")
    private String harborAuthUsername;
    @Value("${harbor.basicauth.password:read}")
    private String harborAuthPassword;

    @Autowired
    private K8sService k8sService;

    @Autowired
    private RestTemplate restTemplate;

    private MultiValueMap<String, String> authHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setBasicAuth(harborAuthUsername, harborAuthPassword);
        return headers;
    }

    public List<String> getRepositories() {
        int n = 1000;
        String last = null;
        List<String> resultList = Lists.newArrayList();
        while (true) {
            String body = "";
            try {
                String url = harborUrl + URI_REPOSITORIES;
                url = String.format(url, n, last);
                ResponseEntity<String> resp = restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<>(authHeaders()), String.class);
                body = resp.getBody();
                DocumentContext ctx = JsonPath.parse(body);
                List<String> repositories = ctx.read("$.repositories[*]");
                if (CollectionUtils.isEmpty(repositories) || repositories.size() < n) {
                    resultList.addAll(CollectionUtils.isEmpty(repositories) ? Lists.newArrayList() : repositories);
                    return resultList;
                }
                resultList.addAll(repositories);
                last = repositories.get(repositories.size() - 1);
            } catch (Exception ex) {
                String msg = String.format("[registry api] get repository error,n=%s,last=%s, body:[%s]", n, last, body);
                log.error(msg);
                throw new ServerException("访问Registry失败", ex);
            }
        }
    }


    public List<String> getTags(String repository) {
        int n = 1000;
        String last = null;
        List<String> resultList = Lists.newArrayList();
        while (true) {
            String body = "";
            try {
                String url = harborUrl + URI_TAGS;
                url = String.format(url, repository, n, last);
                ResponseEntity<String> resp = restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<>(authHeaders()), String.class);
                body = resp.getBody();
                DocumentContext ctx = JsonPath.parse(body);
                List<String> tags = ctx.read("$.tags[*]");
                if (CollectionUtils.isEmpty(tags) || tags.size() < n) {
                    resultList.addAll(CollectionUtils.isEmpty(tags) ? Lists.newArrayList() : tags);
                    return resultList;
                }
                resultList.addAll(tags);
                last = tags.get(tags.size() - 1);
            } catch (Exception ex) {
                if (ex instanceof HttpClientErrorException) {
                    if (ex.getMessage().contains("repository name not known to registry")) {
                        log.info("[registry api]empty tags of {}", repository);
                        return Lists.newArrayList();
                    }
                } else {
                    String msg = String.format("[registry api] get tags error,repository=%s, body:[%s]", repository, body);
                    log.error(msg);
                    throw new ServerException("查询RegistryTag失败", ex);
                }
            }
        }
    }


    public Long getSizeBytes(String repository, String tag) {
        String body = "";
        try {
            String url = harborUrl + URI_MANIFESTS;
            url = String.format(url, repository, tag);
            MultiValueMap<String, String> headers = authHeaders();
            headers.add("Accept", "application/vnd.docker.distribution.manifest.v2+json");
            ResponseEntity<String> resp = restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<>(headers), String.class);
            body = resp.getBody();
            DocumentContext ctx = JsonPath.parse(body);
            JSONArray sizesArray = ctx.read("$.layers[*]['size']");
            long sum = 0L;
            for (Object size : sizesArray) {
                sum += Long.parseLong(String.valueOf(size)) ;
            }
            return sum;
        } catch (Exception ex) {
            String msg = String.format("[registry api] get manifests error,repository=%s, tag=%s, body:[%s]", repository, tag, body);
            log.error(msg);
            throw new ServerException("查询镜像大小失败", ex);
        }

    }

    public void deleteManifests(String repository, String tag, String traceId) {
        String body = "";
        ResponseEntity<String> resp;
        String digest;
        try {
            String url = harborUrl + URI_MANIFESTS;
            url = String.format(url, repository, tag);
            MultiValueMap<String, String> headers = authHeaders();
            headers.add("Accept", "application/vnd.docker.distribution.manifest.v2+json");
            resp = restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<>(headers), String.class);
            body = resp.getBody();
            HttpHeaders respHeaders = resp.getHeaders();
            List<String> digests = respHeaders.get("Docker-Content-Digest");
            if (CollectionUtils.isEmpty(digests)) {
                digest = StringUtils.EMPTY;
            } else {
                digest = digests.get(0);
            }
            log.info("{} [registry api]get digest of {}:{} is {}", traceId, repository, tag, digest);
            if (StringUtils.isBlank(digest)) {
                String msg = String.format("%s [registry api] get manifests error, no header[digest], repository=%s,tag=%s, body:[%s]", traceId, repository, tag, body);
                log.error(msg);
                throw new ServerException("删除镜像失败");
            }
        } catch (Exception ex) {
            String msg = String.format("%s [registry api] get manifests error, repository=%s,tag=%s, body:[%s]", traceId, repository, tag, body);
            log.error(msg);
            throw new ServerException("删除镜像失败", ex);
        }
        String url = harborUrl + URI_MANIFEST_DELETE;
        url = String.format(url, repository, digest);
        try {
            resp = restTemplate.exchange(url, HttpMethod.DELETE, new HttpEntity<>(authHeaders()), String.class);
        } catch (Exception ex) {
            String msg = String.format("%s [registry api] delete manifests error, repository=%s, tag=%s", traceId, repository, tag);
            log.error(msg);
            throw new ServerException("删除镜像失败", ex);
        }
        if (resp.getStatusCode() != HttpStatus.ACCEPTED) {
            log.warn("{} [registry_api]delete manifest failed, with http status:{}", traceId, resp.getStatusCode());
            String msg = String.format("%s [registry_api]delete manifest failed, with http status %s", traceId, resp.getStatusCode());
            log.error(msg);
            throw new ServerException("删除镜像失败");
        }
    }
}
