package com.xylink.manager.service;

import com.xylink.config.Constants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.base.K8sSvcService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.*;

@Service
public class EcoServerService {
    private final static Logger logger = LoggerFactory.getLogger(EcoServerService.class);

    @Autowired
    private K8sService k8sService;
    @Autowired
    private K8sSvcService k8sSvcService;
    @Autowired
    private IDeployService deployService;

    private static final int MAX_FAILS = 3;
    private static final int FAIL_TIMEOUT = 10;

    // IP keys for different services
    private static final String AIBUSINESS_IP_KEY = "AIBUSINESS_IP";
    private static final String AICONTROLLER_IP_KEY = "AICONTROLLER_IP";
    private static final String MATRIX_INTERNAL_IP_KEY = "MATRIX_INTERNAL_IP";

    // Service port mappings for different zones
    private static final Map<String, Integer> ZONE_PORT_MAPPINGS = new HashMap<String, Integer>() {{
        put("zone_for_aibusiness_ws", 18312);
        put("zone_for_aibusiness", 18311);
        put("zone_for_aicontroller", 18326);
        put("zone_for_matrixupload", 18056);
        put("zone_for_matrixapp", 18055);
    }};

    public void addServer(String ip) {
        logger.info("Adding server with IP: {}", ip);

        addIpToEcosystemServerIp(ip);

        callDynamicAddApi(ip);
    }

    public void deleteServer(String ip) throws Exception {
        logger.info("Deleting server with IP: {}", ip);

        removeIpFromEcosystemServerIp(ip);

        callDynamicRemoveApi(ip);
    }

    private void addIpToEcosystemServerIp(String ip) {
        logger.info("Adding IP {} to multiple IP keys", ip);

        // 获取当前all-ip configmap
        Map<String, String> data = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);

        // 定义需要更新的IP键列表
        String[] ipKeys = {AIBUSINESS_IP_KEY, AICONTROLLER_IP_KEY, MATRIX_INTERNAL_IP_KEY};

        Map<String, String> updateData = new HashMap<>();

        // 循环处理每个IP键
        for (String ipKey : ipKeys) {
            logger.info("Processing IP key: {}", ipKey);

            // 获取当前IP值
            String currentIps = data.get(ipKey);

            Set<String> ipSet = new HashSet<>();
            if (StringUtils.isNotBlank(currentIps)) {
                // 解析现有IP列表
                String[] ips = currentIps.split(",");
                for (String existingIp : ips) {
                    if (StringUtils.isNotBlank(existingIp.trim())) {
                        ipSet.add(existingIp.trim());
                    }
                }
            }

            // 添加新IP
            ipSet.add(ip);

            // 重新组装IP列表
            String newIpList = String.join(",", ipSet);
            updateData.put(ipKey, newIpList);

            logger.info("Updated {} to: {}", ipKey, newIpList);
        }

        // 一次性更新所有IP键
        k8sService.patchConfigMap(Constants.CONFIGMAP_ALLIP, updateData);

        logger.info("Successfully updated all IP keys with IP: {}", ip);
    }

    private void removeIpFromEcosystemServerIp(String ip) {
        logger.info("Removing IP {} from multiple IP keys", ip);

        // 获取当前all-ip configmap
        Map<String, String> data = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);

        // 获取需要更新的IP键列表
        String[] ipKeys = getAllIpKeys();

        Map<String, String> updateData = new HashMap<>();

        // 循环处理每个IP键
        for (String ipKey : ipKeys) {
            logger.info("Processing IP key for removal: {}", ipKey);

            // 获取当前IP值
            String currentIps = data.get(ipKey);

            if (StringUtils.isBlank(currentIps)) {
                logger.warn("{} is empty, nothing to remove", ipKey);
                updateData.put(ipKey, "");
                continue;
            }

            Set<String> ipSet = new HashSet<>();
            String[] ips = currentIps.split(",");
            for (String existingIp : ips) {
                if (StringUtils.isNotBlank(existingIp.trim()) && !existingIp.trim().equals(ip)) {
                    ipSet.add(existingIp.trim());
                }
            }

            // 重新组装IP列表
            String newIpList = String.join(",", ipSet);
            updateData.put(ipKey, newIpList);

            logger.info("Updated {} to: {}", ipKey, newIpList);
        }

        // 一次性更新所有IP键
        k8sService.patchConfigMap(Constants.CONFIGMAP_ALLIP, updateData);

        logger.info("Successfully removed IP {} from all IP keys", ip);
    }

    /**
     * Get all IP keys that need to be updated
     *
     * @return Array of IP key names
     */
    private String[] getAllIpKeys() {
        return new String[]{AIBUSINESS_IP_KEY, AICONTROLLER_IP_KEY, MATRIX_INTERNAL_IP_KEY};
    }

    /**
     * Get the zone port mappings for dynamic API calls
     *
     * @return Map of zone names to their corresponding ports
     */
    private Map<String, Integer> getZonePortMappings() {
        return ZONE_PORT_MAPPINGS;
    }


    private void callDynamicAddApi(String ip) {
        logger.info("Adding IP {} to all configured zones", ip);
        List<Pod> openrestyMainPods = deployService.listPodsByAppLabel("private-openresty-main");
        // Loop through all zone port mappings and add the server
        Map<String, Integer> zoneMappings = getZonePortMappings();
        for (Map.Entry<String, Integer> entry : zoneMappings.entrySet()) {
            String zoneName = entry.getKey();
            Integer port = entry.getValue();

            callDynamicApiForZone(openrestyMainPods, ip, zoneName, port, "add");
        }

        logger.info("Completed adding IP {} to {} zones", ip, zoneMappings.size());
    }

    private void callDynamicRemoveApi(String ip) {
        logger.info("Removing IP {} from all configured zones", ip);
        List<Pod> openrestyMainPods = deployService.listPodsByAppLabel("private-openresty-main");
        // Loop through all zone port mappings and remove the server
        Map<String, Integer> zoneMappings = getZonePortMappings();
        for (Map.Entry<String, Integer> entry : zoneMappings.entrySet()) {
            String zoneName = entry.getKey();
            Integer port = entry.getValue();

            callDynamicApiForZone(openrestyMainPods, ip, zoneName, port, "remove");
        }

        logger.info("Completed removing IP {} from {} zones", ip, zoneMappings.size());
    }

    /**
     * Call dynamic API for a specific zone and operation
     *
     * @param openrestyMainPods openrestyMainPods
     * @param ip                The IP address to add/remove
     * @param zoneName          The zone name
     * @param port              The port number
     * @param operation         The operation ("add" or "remove")
     */
    private void callDynamicApiForZone(List<Pod> openrestyMainPods, String ip, String zoneName, Integer port, String operation) {
        try {
            UriComponentsBuilder builder = UriComponentsBuilder.fromUriString("http://127.0.0.1:11111/dynamic")
                    .queryParam("upstream", zoneName)
                    .queryParam("server", ip + ":" + port)
                    .queryParam("max_fails", MAX_FAILS)
                    .queryParam("fail_timeout", FAIL_TIMEOUT);

            // Add the operation parameter (add or remove)
            if ("add".equals(operation)) {
                builder.queryParam("add", "");
            } else if ("remove".equals(operation)) {
                builder.queryParam("remove", "");
            }

            URI uri = builder.build().toUri();
            logger.info("Calling dynamic {} API for zone [{}]: {}", operation, zoneName, uri);
            String[] command = new String[]{"curl", uri.toString()};
            openrestyMainPods.stream()
                    .filter(it -> StringUtils.equalsIgnoreCase(it.getStatusPhase(), "Running"))
                    .forEach(pod -> {
                        deployService.executeCommandForPodGotSuccess(pod.getPodName(), pod.getNamespace(), command);
                        logger.info("Dynamic {} API response for zone [{}]: success", operation, zoneName);
                    });

        } catch (Exception e) {
            logger.error("Error calling dynamic {} API for IP: {} in zone: {}", operation, ip, zoneName, e);
            throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
        }
    }
}