package com.xylink.manager.service.monitor;

/**
 * <AUTHOR>
 * @since 2023/3/8 3:40 下午
 */
public abstract class DataBaseMonitorConstant {

    private DataBaseMonitorConstant() {

    }

    public static String MYSQL_COMMAND_MAX_CONNECTION = "show variables like 'max_connections';";
    public static String MYSQL_COMMAND_USED_CONNECTION = "select count(*) as 'count' from information_schema.PROCESSLIST";
    public static String MYSQL_COMMAND_USED_ACTIVE_SESSION = "select count(*) as 'count' from information_schema.PROCESSLIST where COMMAND not in ('Sleep','Binlog Dump')";
    public static String MYSQL_COMMAND_USED_MAX_TIME_ACTIVE_SESSION = "select max(TIME) as 'max' from information_schema.PROCESSLIST where COMMAND not in ('Sleep','Binlog Dump')";
    public static String MYSQL_COMMAND_USER_SESSION = "select user as 'target',count(*) as 'total' from information_schema.PROCESSLIST group by user";
    public static String MYSQL_COMMAND_ACCESS_SESSION = "select SUBSTRING_INDEX(host,':',1)  as 'target',count(*) as 'total' from information_schema.PROCESSLIST group by SUBSTRING_INDEX(host,':',1)";
    public static String MYSQL_COMMAND_DB_SESSION = "select DB  as 'target',count(*) as 'total' from information_schema.PROCESSLIST group by DB";
    public static String MYSQL_COMMAND_USER_SESSION_LIST = "SELECT ID ,USER ,SUBSTRING_INDEX(HOST,':',1)  AS HOST,DB, COMMAND,`TIME`,STATE,INFO AS 'SQL' FROM INFORMATION_SCHEMA.PROCESSLIST WHERE USER=?";
    public static String MYSQL_COMMAND_ACCESS_SESSION_LIST = "SELECT ID ,USER ,SUBSTRING_INDEX(HOST,':',1)  AS HOST,DB, COMMAND,`TIME`,STATE,INFO AS 'SQL' FROM INFORMATION_SCHEMA.PROCESSLIST WHERE SUBSTRING_INDEX(HOST,':',1)=?";
    public static String MYSQL_COMMAND_DB_SESSION_LIST = "SELECT ID ,USER ,SUBSTRING_INDEX(HOST,':',1)  AS HOST,DB, COMMAND,`TIME`,STATE,INFO AS 'SQL' FROM INFORMATION_SCHEMA.PROCESSLIST WHERE DB=?";
    public static String MYSQL_COMMAND_DB_SIZE = "select TABLE_SCHEMA as 'db',sum(DATA_LENGTH)+sum(INDEX_LENGTH) 'size' from information_schema.`TABLES` where TABLE_SCHEMA not in ('information_schema','performance_schema','mysql','sys') GROUP BY TABLE_SCHEMA order by size desc";
    public static String MYSQL_COMMAND_DB_TABLES_SIZE = "select\n" +
            "\tTABLE_NAME 'tableName',\n" +
            "\tTABLE_SCHEMA 'tableSchema',\n" +
            "\t`ENGINE` 'engine',\n" +
            "\tDATA_LENGTH + INDEX_LENGTH 'tableSpaceSize',\n" +
            "\tDATA_LENGTH 'dataSpaceSize',\n" +
            "\tINDEX_LENGTH 'indexSpaceSize',\n" +
            "\tDATA_FREE 'dataFreeSpaceSize',\n" +
            "\tTABLE_ROWS 'tableRows',\n" +
            "\tAVG_ROW_LENGTH 'avgRowLength',\n" +
            "\tCREATE_TIME 'createTime',\n" +
            "\tUPDATE_TIME 'updateTime'\n" +
            "from\n" +
            "\tinformation_schema.`TABLES`\n" +
            "where\n" +
            "\tTABLE_SCHEMA not in ('information_schema', 'performance_schema', 'mysql', 'sys')";
    public static String MYSQL_COMMAND_CREATE_TABLE_INFO = "show create table {tableName}";
    public static String MYSQL_COMMAND_SHOW_TABLE_COLUMNS_INFO = "show full columns from {tableName}";
    public static String MYSQL_COMMAND_SHOW_TABLE_INDEXES_INFO = "show index from {tableName}";

}
