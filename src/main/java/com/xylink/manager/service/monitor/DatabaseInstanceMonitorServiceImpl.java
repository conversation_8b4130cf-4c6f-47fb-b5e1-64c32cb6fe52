package com.xylink.manager.service.monitor;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.model.common.MySort;
import com.xylink.manager.model.common.Page;
import com.xylink.manager.model.common.Pageable;
import com.xylink.manager.model.common.pagehelper.Dialect;
import com.xylink.manager.model.common.pagehelper.PageHelper;
import com.xylink.manager.model.common.pagehelper.dialect.MysqlDialect;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.model.monitor.*;
import com.xylink.manager.service.ServerListService;
import com.xylink.manager.service.ServerNetworkService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.util.DbConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/1/31 11:32 上午
 */
@Slf4j
@Service
public class DatabaseInstanceMonitorServiceImpl implements IDatabaseInstanceMonitorService {
    @Resource
    private K8sService k8sService;
    @Resource
    private ServerNetworkService serverNetworkService;
    @Resource
    private ServerListService serverListService;
    @Resource
    private PageHelper pageHelper;
    private final Dialect dialect = new MysqlDialect();


    @Override
    public List<DatabaseInstanceListDto> databaseInstanceList() {
        List<DatabaseInstanceListDto> result = new ArrayList<>();
        Arrays.stream(DBS.values()).forEach(item -> {
            Optional<Pod> optionalPod = k8sService.getPodWithLabelInApp(item.getAppName());
            optionalPod.ifPresent((pod) -> {
                DatabaseInstanceListDto dto = new DatabaseInstanceListDto();
                dto.setInstanceId(item.getAppName());
                dto.setInstanceName(pod.getPodName());
                dto.setHostname(pod.getNodeName());
                dto.setInnerIp(getInnerIp(item));
                result.add(dto);
            });
        });
        return result.stream().filter((item) -> !"127.0.0.1".equals(item.getInnerIp())).collect(Collectors.toList());
    }

    @Override
    public DatabaseInstanceConnectionsDto databaseInstanceConnections(String instanceId) {
        Connection connection = connection(instanceId);
        Statement statement = null;
        ResultSet resultSet = null;
        int max, used;
        try {
            statement = connection.createStatement();
            resultSet = statement.executeQuery(DataBaseMonitorConstant.MYSQL_COMMAND_MAX_CONNECTION);
            resultSet.next();
            max = resultSet.getInt(2);
            resultSet = statement.executeQuery(DataBaseMonitorConstant.MYSQL_COMMAND_USED_CONNECTION);
            resultSet.next();
            used = resultSet.getInt(1);
        } catch (Exception exception) {
            throw new ServerException(exception, ErrorStatus.UNEXPECTED_ERROR);
        } finally {
            close(connection, statement, resultSet);
        }
        DatabaseInstanceConnectionsDto result = new DatabaseInstanceConnectionsDto();
        result.setInstanceId(instanceId);
        result.setMaxConnections(max);
        result.setUsedConnections(used);
        return result;
    }

    @Override
    public DatabaseInstanceSessionsDto databaseInstanceSessions(String instanceId) {
        Connection connection = connection(instanceId);
        Statement statement = null;
        ResultSet resultSet = null;
        List<Map<String, Object>> resumes = new ArrayList<>();
        try {
            statement = connection.createStatement();
            // 会话总数
            resultSet = statement.executeQuery(DataBaseMonitorConstant.MYSQL_COMMAND_USED_CONNECTION);
            resultSet.next();
            HashMap<String, Object> totalSessions = new HashMap<>();
            totalSessions.put(DatabaseInstanceSessionsDto.ResumesKeys.totalSessions.name(), resultSet.getInt(1));
            resumes.add(totalSessions);
            // 运行中会话总数
            resultSet = statement.executeQuery(DataBaseMonitorConstant.MYSQL_COMMAND_USED_ACTIVE_SESSION);
            resultSet.next();
            HashMap<String, Object> activeSession = new HashMap<>();
            activeSession.put(DatabaseInstanceSessionsDto.ResumesKeys.activeSession.name(), resultSet.getInt(1));
            resumes.add(activeSession);
            // 运行中会话最长时间
            resultSet = statement.executeQuery(DataBaseMonitorConstant.MYSQL_COMMAND_USED_MAX_TIME_ACTIVE_SESSION);
            resultSet.next();
            HashMap<String, Object> maxTimeSession = new HashMap<>();
            maxTimeSession.put(DatabaseInstanceSessionsDto.ResumesKeys.maxTimeSession.name(), resultSet.getInt(1));
            resumes.add(maxTimeSession);
        } catch (Exception exception) {
            throw new ServerException(exception, ErrorStatus.UNEXPECTED_ERROR);
        } finally {
            close(connection, statement, resultSet);
        }
        DatabaseInstanceSessionsDto result = new DatabaseInstanceSessionsDto();
        result.setInstanceId(instanceId);
        result.setResumes(resumes);
        return result;
    }

    @Override
    public Page<DatabaseInstanceSessionsDetailDto> databaseInstanceSessionsByUser(String instanceId, MySort sort, Pageable pageable) {
        return query0(instanceId, sort, DataBaseMonitorConstant.MYSQL_COMMAND_USER_SESSION, pageable);
    }

    @Override
    public Page<DatabaseInstanceSessionsDetailDto> databaseInstanceSessionsByAccess(String instanceId, MySort sort, Pageable pageable) {
        return query0(instanceId, sort, DataBaseMonitorConstant.MYSQL_COMMAND_ACCESS_SESSION, pageable);

    }

    @Override
    public Page<DatabaseInstanceSessionsDetailDto> databaseInstanceSessionsByDb(String instanceId, MySort sort, Pageable pageable) {
        return query0(instanceId, sort, DataBaseMonitorConstant.MYSQL_COMMAND_DB_SESSION, pageable);
    }

    private Page<DatabaseInstanceSessionsDetailDto> query0(String instanceId, MySort sort, String boundSql, Pageable pageable) {
        if (sort.orderBy()) {
            String sqlOrderBy = " order by " + sort.sortColumn() + " " + sort.sortRule();
            boundSql += sqlOrderBy;
        }
        Connection connection = connection(instanceId);
        Statement statement = null;
        try {
            statement = connection.createStatement();
            PageHelper.startPage(pageable);
            List<DatabaseInstanceSessionsDetailDto> result = pageHelper.query(boundSql, pageable, statement, (rs) -> {
                List<DatabaseInstanceSessionsDetailDto> data = new ArrayList<>();
                try {
                    while (rs.next()) {
                        DatabaseInstanceSessionsDetailDto databaseInstanceSessionsDetailDto = new DatabaseInstanceSessionsDetailDto();
                        databaseInstanceSessionsDetailDto.setTarget(rs.getString(1));
                        databaseInstanceSessionsDetailDto.setTotal(rs.getInt(2));
                        data.add(databaseInstanceSessionsDetailDto);
                    }
                } catch (SQLException e) {
                    throw new ServerException(e, ErrorStatus.UNEXPECTED_ERROR);
                } finally {
                    close(null, null, rs);
                }
                return data;
            });

            return new Page<>(result);
        } catch (Exception exception) {
            throw new ServerException(exception, ErrorStatus.UNEXPECTED_ERROR);
        } finally {
            close(connection, statement, null);
        }
    }

    @Override
    public Page<DatabaseInstanceSessionsListDto> databaseInstanceSessionsList(String instanceId, String
            target, String targetValue, MySort sort, Pageable pageable) {
        String boundSql = null;
        switch (target) {
            case "user":
                boundSql = DataBaseMonitorConstant.MYSQL_COMMAND_USER_SESSION_LIST;
                break;
            case "access":
                boundSql = DataBaseMonitorConstant.MYSQL_COMMAND_ACCESS_SESSION_LIST;
                break;
            case "db":
                boundSql = DataBaseMonitorConstant.MYSQL_COMMAND_DB_SESSION_LIST;
                break;
            default:
        }
        if (StringUtils.isBlank(boundSql)) {
            throw new ServerException("Sql must not be null.");
        }
        Connection connection = null;
        PreparedStatement statement = null;
        try {
            connection = connection(instanceId);
            if (sort.orderBy()) {
                String sqlOrderBy = " order by " + sort.sortColumn() + " " + sort.sortRule();
                if ("sql".equalsIgnoreCase(sort.sortColumn())) {
                    sqlOrderBy = " order by `" + sort.sortColumn() + "` " + sort.sortRule();
                }
                boundSql += sqlOrderBy;
            }
            if ("db".equals(target) && StringUtils.isBlank(targetValue)) {
                boundSql = boundSql.replace("DB=?", "DB IS NULL");
            }

            String countSql = dialect.getCountSql(boundSql, pageable);
            statement = connection.prepareStatement(countSql);
            statement.setString(1, targetValue);
            log.debug("Query count sql:{}", countSql);
            ResultSet countRs = statement.executeQuery();
            long count = 0;
            while (countRs.next()) {
                count = countRs.getLong(1);
            }

            if (count <= 0) {
                return Page.emptyPage(pageable);
            }

            PageHelper.startPage(pageable);
            String pageSql = dialect.getPageSql(boundSql, pageable);
            PageHelper.clearPage();
            statement = connection.prepareStatement(pageSql);
            statement.setString(1, targetValue);
            List<DatabaseInstanceSessionsListDto> result = pageHelper.queryByPstm(pageable, statement, (rs) -> {
                List<DatabaseInstanceSessionsListDto> data = new ArrayList<>();
                try {
                    while (rs.next()) {
                        DatabaseInstanceSessionsListDto databaseInstanceSessionsListDto = new DatabaseInstanceSessionsListDto();
                        databaseInstanceSessionsListDto.setId(rs.getString(1));
                        databaseInstanceSessionsListDto.setUser(rs.getString(2));
                        databaseInstanceSessionsListDto.setHost(rs.getString(3));
                        databaseInstanceSessionsListDto.setDb(rs.getString(4));
                        databaseInstanceSessionsListDto.setCommand(rs.getString(5));
                        databaseInstanceSessionsListDto.setTime(rs.getInt(6));
                        databaseInstanceSessionsListDto.setState(rs.getString(7));
                        databaseInstanceSessionsListDto.setSql(rs.getString(8));
                        data.add(databaseInstanceSessionsListDto);
                    }
                } catch (SQLException e) {
                    throw new ServerException(e, ErrorStatus.UNEXPECTED_ERROR);
                } finally {
                    close(null, null, rs);
                }
                return data;
            });
            return new Page<>(pageable.getPageNumber(), pageable.getPageSize(), count, result);
        } catch (SQLException e) {
            throw new ServerException(e, ErrorStatus.UNEXPECTED_ERROR);
        } finally {
            close(connection, statement, null);
        }
    }

    @Override
    public Page<DatabaseInstanceSpacesDto> databaseInstanceSpaces(String instanceId, Pageable pageable) {
        Connection connection = null;
        PreparedStatement statement = null;
        try {
            String boundSql = DataBaseMonitorConstant.MYSQL_COMMAND_DB_SIZE;
            connection = connection(instanceId);
            statement = connection.prepareStatement(boundSql);
            PageHelper.startPage(pageable);
            List<DatabaseInstanceSpacesDto> result = pageHelper.query(boundSql, pageable, statement, (rs) -> {
                List<DatabaseInstanceSpacesDto> data = new ArrayList<>();
                try {
                    while (rs.next()) {
                        DatabaseInstanceSpacesDto databaseInstanceSpacesDto = new DatabaseInstanceSpacesDto();
                        databaseInstanceSpacesDto.setDb(rs.getString(1));
                        databaseInstanceSpacesDto.setSize(rs.getLong(2));
                        data.add(databaseInstanceSpacesDto);
                    }
                } catch (SQLException e) {
                    throw new ServerException(e, ErrorStatus.UNEXPECTED_ERROR);
                } finally {
                    close(null, null, rs);
                }
                return data;
            });
            return new Page<>(result);
        } catch (SQLException e) {
            throw new ServerException(e, ErrorStatus.UNEXPECTED_ERROR);
        } finally {
            close(connection, statement, null);
        }
    }

    @Override
    public Page<DatabaseInstanceTablesSpacesDto> databaseInstanceTablesSpaces(String instanceId, MySort sort, Pageable pageable, String database, String table) {
        Connection connection = null;
        PreparedStatement statement = null;
        try {
            String boundSql = DataBaseMonitorConstant.MYSQL_COMMAND_DB_TABLES_SIZE;
            String sqlOrderBy = " order by tableSpaceSize desc";
            if (sort.orderBy()) {
                sqlOrderBy = " order by " + sort.sortColumn() + " " + sort.sortRule();
            }
            if (StringUtils.isNotBlank(database)) {
                boundSql += " AND TABLE_SCHEMA=? ";
            }
            if (StringUtils.isNotBlank(table)) {
                boundSql += " AND LOCATE(?,TABLE_NAME)>0";
            }
            boundSql += sqlOrderBy;
            connection = connection(instanceId);

            String countSql = dialect.getCountSql(boundSql, pageable);
            statement = connection.prepareStatement(countSql);
            int paramIndex = 1;
            if (StringUtils.isNotBlank(database)) {
                statement.setString(paramIndex++, database);
            }
            if (StringUtils.isNotBlank(table)) {
                statement.setString(paramIndex, table);
            }
            log.debug("Query count sql:{}", countSql);
            ResultSet countRs = statement.executeQuery();
            long count = 0;
            while (countRs.next()) {
                count = countRs.getLong(1);
            }

            if (count <= 0) {
                return Page.emptyPage(pageable);
            }
            PageHelper.startPage(pageable);
            String pageSql = dialect.getPageSql(boundSql, pageable);
            PageHelper.clearPage();
            statement = connection.prepareStatement(pageSql);
            paramIndex = 1;
            if (StringUtils.isNotBlank(database)) {
                statement.setString(paramIndex++, database);
            }
            if (StringUtils.isNotBlank(table)) {
                statement.setString(paramIndex, table);
            }

            List<DatabaseInstanceTablesSpacesDto> result = pageHelper.queryByPstm(pageable, statement, (rs) -> {
                List<DatabaseInstanceTablesSpacesDto> data = new ArrayList<>();
                try {
                    while (rs.next()) {
                        DatabaseInstanceTablesSpacesDto databaseInstanceTablesSpacesDto = new DatabaseInstanceTablesSpacesDto();
                        databaseInstanceTablesSpacesDto.setTableName(rs.getString(1));
                        databaseInstanceTablesSpacesDto.setTableSchema(rs.getString(2));
                        databaseInstanceTablesSpacesDto.setEngine(rs.getString(3));
                        databaseInstanceTablesSpacesDto.setTableSpaceSize(rs.getLong(4));
                        databaseInstanceTablesSpacesDto.setDataSpaceSize(rs.getLong(5));
                        databaseInstanceTablesSpacesDto.setIndexSpaceSize(rs.getLong(6));
                        databaseInstanceTablesSpacesDto.setDataFreeSpaceSize(rs.getLong(7));
                        databaseInstanceTablesSpacesDto.setTableRows(rs.getLong(8));
                        databaseInstanceTablesSpacesDto.setAvgRowLength(rs.getLong(9));
                        databaseInstanceTablesSpacesDto.setCreateTime(rs.getTimestamp(10));
                        databaseInstanceTablesSpacesDto.setUpdateTime(rs.getTimestamp(11));
                        data.add(databaseInstanceTablesSpacesDto);
                    }
                } catch (SQLException e) {
                    throw new ServerException(e, ErrorStatus.UNEXPECTED_ERROR);
                } finally {
                    close(null, null, rs);
                }
                return data;
            });
            return new Page<>(pageable.getPageNumber(), pageable.getPageSize(), count, result);
        } catch (SQLException e) {
            throw new ServerException(e, ErrorStatus.UNEXPECTED_ERROR);
        } finally {
            close(connection, statement, null);
        }
    }

    @Override
    public TablesDDLInfoDto tablesDDLInfo(String instanceId, String database, String table) {
        Connection connection = null;
        PreparedStatement statement = null;
        ResultSet resultSet = null;
        TablesDDLInfoDto result = new TablesDDLInfoDto();
        // 基础信息
        result.setTableName(table);
        result.setDb(database);
        String tableName = database + "." + table;
        try {
            connection = connection(instanceId);

            // ddl
            statement = connection.prepareStatement(DataBaseMonitorConstant.MYSQL_COMMAND_CREATE_TABLE_INFO.replace("{tableName}", tableName));
            resultSet = statement.executeQuery();
            resultSet.next();
            result.setDdlStr(resultSet.getString(2));
            // columns
            List<TablesDDLInfoDto.ColumnsInfo> columns = new ArrayList<>();
            statement = connection.prepareStatement(DataBaseMonitorConstant.MYSQL_COMMAND_SHOW_TABLE_COLUMNS_INFO.replace("{tableName}", tableName));
            resultSet = statement.executeQuery();
            while (resultSet.next()) {
                TablesDDLInfoDto.ColumnsInfo columnsInfo = new TablesDDLInfoDto.ColumnsInfo();
                columnsInfo.setColumnField(resultSet.getString(1));
                columnsInfo.setColumnType(resultSet.getString(2));
                columnsInfo.setColumnIsNull(resultSet.getString(4).toUpperCase());
                columnsInfo.setColumnDefault(resultSet.getString(6));
                String columns7 = resultSet.getString(7);
                if ("auto_increment".equalsIgnoreCase(columns7)) {
                    columnsInfo.setColumnIsAutoIncrement("YES");
                } else {
                    columnsInfo.setColumnIsAutoIncrement("NO");
                }
                columnsInfo.setColumnComment(resultSet.getString(9));
                columns.add(columnsInfo);
            }
            result.setColumns(columns);
            // index
            statement = connection.prepareStatement(DataBaseMonitorConstant.MYSQL_COMMAND_SHOW_TABLE_INDEXES_INFO.replace("{tableName}", tableName));
            resultSet = statement.executeQuery();
            Map<String, TablesDDLInfoDto.IndexesInfo> tmp = new HashMap<>();
            while (resultSet.next()) {
                String indexName = resultSet.getString(3);
                TablesDDLInfoDto.IndexesInfo indexesInfo;
                if (tmp.containsKey(indexName)) {
                    indexesInfo = tmp.get(indexName);
                } else {
                    indexesInfo = new TablesDDLInfoDto.IndexesInfo();
                    indexesInfo.setIndexName(indexName);
                    indexesInfo.setIndexType(resultSet.getString(11));
                    tmp.put(indexName, indexesInfo);
                }
                indexesInfo.addColumn(resultSet.getString(5));
            }
            result.setIndexes(new ArrayList<>(tmp.values()));
            return result;
        } catch (SQLException e) {
            throw new ServerException(e, ErrorStatus.UNEXPECTED_ERROR);
        } finally {
            close(connection, statement, resultSet);
        }
    }

    private String getInnerIp(DBS dbs) {
        if (DBS.mysql_slave.equals(dbs)) {
            Optional<Pod> optionalPod = k8sService.getPodWithLabelInApp("private-mysqlslave");
            if (optionalPod.isPresent()) {
                return optionalPod.get().getIp();
            }
            return "127.0.0.1";
        }
        if (DBS.uaa_mysqlslave.equals(dbs)) {
            Optional<Pod> optionalPod = k8sService.getPodWithLabelInApp("private-uaa-mysqlslave");
            if (optionalPod.isPresent()) {
                return optionalPod.get().getIp();
            }
            return "127.0.0.1";
        }
        return serverNetworkService.getNetworkConfiguration().get(dbs.getAllIpIpKey());
    }

    private String getPort(DBS dbs) {
        return serverNetworkService.getNetworkConfiguration().get(dbs.getAllIpPortKey());
    }

    private Connection connection(String instanceId) {
        DBS db = DBS.valueOfAppName(instanceId);
        String dbBak = serverNetworkService.getNetworkConfiguration().get(NetworkConstants.MAIN_DB_BACKUP_USERNAME);
        String dbBakPassword = serverListService.getUsernameOrPwdFromCM(k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP), NetworkConstants.MAIN_DB_BACKUP_PASSWORD);
        String url = "";
        try {
            url = jdbcUrl(db);
            return DriverManager.getConnection(url, dbBak, dbBakPassword);
        } catch (Exception exception) {
            log.error("fail to create mysql connection by {} !!! msg:{}", url, exception.getMessage());
            throw new ServerException();
        }
    }

    private String jdbcUrl(DBS db) {
        DbConfigUtil.DbConfig dbConfig = new DbConfigUtil.DbConfig();
        dbConfig.setDbType("MYSQL");
        dbConfig.setAddress(getInnerIp(db));
        dbConfig.setPort(getPort(db));
        try {
            return dbConfig.jdbcConnectionTestUrl();
        } catch (SQLException exception) {
            throw new ServerException(exception, ErrorStatus.UNEXPECTED_ERROR);
        }
    }

    public void close(Connection con, Statement stat, ResultSet rs) {
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException ex) {
                log.error("close ResultSet error! ", ex);
            }
        }

        if (stat != null) {
            try {
                stat.close();
            } catch (SQLException ex) {
                log.error("close Statement error! ", ex);
            }
        }

        if (con != null) {
            try {
                con.close();
            } catch (SQLException ex) {
                log.error("close Connection error! ", ex);
            }
        }

    }

    public enum DBS {
        /**
         * main
         */
        mysql("private-mysql", 1, NetworkConstants.DATABASE_IP, NetworkConstants.DATABASE_PORT),
        /**
         * slave
         */
        mysql_slave("private-mysqlslave", 2, "", NetworkConstants.DATABASE_PORT),
        /**
         * statis
         */
        statis_mysql("private-statis-mysql", 3, NetworkConstants.STATIS_DATABASE_IP, "STATIS_DATABASE_PORT"),
        /**
         * uaa
         */
        uaa_mysql("private-uaa-mysql", 4, NetworkConstants.UAA_DATABASE_IP, "UAA_DATABASE_PORT"),
        uaa_mysqlslave("private-uaa-mysqlslave", 5, "", "UAA_DATABASE_PORT"),
        /**
         * matrix
         */
        matrix_mysql("private-matrix-mysql", 6, NetworkConstants.MATRIX_DATABASE_IP, "MATRIX_DATABASE_PORT"),
        /**
         * surv
         */
        surv_mysql("private-surv-mysql", 7, NetworkConstants.SURV_INTERNAL_IP, "SURV_DATABASE_PORT"),
        /**
         * edu
         */
        edu_mysql("private-edu-mysql", 8, NetworkConstants.EDU_DATABASE_IP, "EDU_DATABASE_PORT");

        private final String appName;
        private final int sort;
        private final String allIpIpKey;
        private final String allIpPortKey;

        DBS(String appName, int sort, String allIpIpKey, String allIpPortKey) {
            this.appName = appName;
            this.sort = sort;
            this.allIpIpKey = allIpIpKey;
            this.allIpPortKey = allIpPortKey;
        }

        public static DBS valueOfAppName(String appName) {
            return Arrays.stream(DBS.values()).filter(item -> appName.equals(item.appName)).findFirst().orElseThrow(() -> new ServerException(ErrorStatus.UNEXPECTED_ERROR));
        }

        public String getAppName() {
            return appName;
        }

        public int getSort() {
            return sort;
        }

        public String getAllIpIpKey() {
            return allIpIpKey;
        }

        public String getAllIpPortKey() {
            return allIpPortKey;
        }
    }
}
