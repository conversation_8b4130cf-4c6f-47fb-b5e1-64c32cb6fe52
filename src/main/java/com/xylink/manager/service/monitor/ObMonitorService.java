package com.xylink.manager.service.monitor;

import com.xylink.config.NetworkConstants;
import com.xylink.config.util.RSAUtil;
import com.xylink.manager.controller.dto.moitor.ObClusterInfo;
import com.xylink.manager.controller.dto.moitor.ObTenantsPassEmpty;
import com.xylink.manager.service.base.SystemModeConfig;
import com.xylink.manager.service.cache.service.ICacheService;
import com.xylink.manager.service.db.JasyptService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StreamUtils;
import org.springframework.web.client.RequestCallback;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URI;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ObMonitorService {
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private ICacheService cacheService;
    @Autowired
    private JasyptService jasyptService;
    @Value("${ocp.admin.password:ju9Z()U3}")
    private String password;
    @Value("${ocp.port:8180}")
    private String ocpPort;
    @Value("${ocp.tenant.password:Mia8b#n2Hr}")
    private String tenantPassword;
    @Value("${ocp.ocp_meta.password:TT0lxBh2dP}")
    private String ocpMetaPassword;
    private boolean isInit = false;

    @Scheduled(initialDelayString = "3000", fixedDelayString = "3600000")
    public void configPass() {
        if (isInit) {
            log.info("the ocp tenant pass is already init");
            return;
        }
        if(!SystemModeConfig.isCmsOrXms()) {
            log.info("the systemMode is not cms");
            return;
        }
        try {
            List<String> cookies = ocpLogin(UUID.randomUUID().toString());
            String cookieValue = cookies.stream().filter(cookie -> cookie.contains(";")
                            && !cookie.contains("XSRF-TOKEN=\"\"")).map(cookie -> cookie.split(";")[0])
                    .collect(Collectors.joining("; "));
            //查询所有租户id
            ObClusterInfo clusterInfo = getTenantList(cookieValue);
            List<ObClusterInfo.DataDTO.TenantInfosDTO> tenantInfosDTOS = clusterInfo.getData().getTenantInfos();
            for (ObClusterInfo.DataDTO.TenantInfosDTO tenant : tenantInfosDTOS) {
                log.info("config tenant pass, name:{}", tenant.getTenantName());
                //创建租户密码
                changeTenantPass(tenant.getTenantName(), cookieValue);
                configPass(tenant.getTenantName(), cookieValue);
            }

            isInit = true;
        } catch (Exception e) {
            log.error("ObMonitor init error, ", e);
        }
    }

    public String getHost() {
        Map<String, String> allIp = cacheService.cacheConfigMapByName("all-ip").getData();
        String ip = allIp.get(NetworkConstants.DATABASE_IP);
        String port = allIp.get("OCP_PORT");
        if (StringUtils.isBlank(port)) {
            port = ocpPort;
        }
        return String.format("%s://%s:%s", "http", ip, port);
    }

    public String getPassword() {
        Map<String, String> allIp = cacheService.cacheConfigMapByName("all-ip").getData();
        String pass = allIp.get("OCP_ADMIN_PASSWORD");
        if (StringUtils.isBlank(pass)) {
            log.info("configmap all-ip is not config OCP_ADMIN_PASSWORD, use the default password");
            pass = password;
        }
        pass = jasyptService.decrypt(pass);
        return pass;
    }

    public String getPublicKey() {
        String host = getHost();
        String rasPubKeyUrl = String.format("%s%s", host, "/api/v1/loginKey");
        Map map = restTemplate.getForObject(rasPubKeyUrl, Map.class);
        String publicKey = ((Map) map.get("data")).get("publicKey").toString();
        return publicKey;
    }

    public List<String> ocpLogin(String sessionId) {
        try {
            String host = getHost();
            String publicKey = getPublicKey();
            String uri = String.format("%s%s", host, "/api/v1/login");
            MultiValueMap<String, Object> params = new LinkedMultiValueMap<>(2);
            params.add("username", "admin");
            params.add("password", RSAUtil.encrypt(getPassword(), publicKey));
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            //XSRF-TOKEN=7c0845ce-8c92-4851-b73b-30b327dd1506; JSESSIONID=7D5F632C9670E9DEE9AD28F123202D90
            String token = UUID.randomUUID().toString();
            headers.add("Cookie", "XSRF-TOKEN=" + token + "; " + "JSESSIONID=" + sessionId);
            headers.add("X-Xsrf-token", token);
            HttpEntity httpEntity = new HttpEntity(params, headers);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(uri, httpEntity, String.class);
            return responseEntity.getHeaders().get("Set-Cookie");
        } catch (Exception e) {
            log.info("ocpLogin error,{}", e);
        }
        return null;
    }

    public List<String> getOcpCookie(HttpServletRequest request) {
        Object ocpCookie = request.getSession().getAttribute("ocpCookie");
        if (Objects.nonNull(ocpCookie)) {
            return (List<String>) ocpCookie;
        }
        List<String> cookies = ocpLogin(request.getSession().getId());
        request.getSession().setAttribute("ocpCookie", cookies);
        return cookies;
    }

    /**
     * 查询租户列表
     *
     * @return
     */
    public ObClusterInfo getTenantList(String cookieValue) {
        String requestURI = "/api/v1/ob/cluster/unitView";
        URI uri = URI.create(getHost() + requestURI);
        ResponseEntity<ObClusterInfo> responseEntity = restTemplate.exchange(uri, HttpMethod.GET,
                getHttpEntity(null, cookieValue), ObClusterInfo.class);
        return responseEntity.getBody();
    }

    public ObTenantsPassEmpty checkPassEmpty(Integer tenantId, String cookieValue) {
        String requestURI = "/api/v1/ob/tenants/" + tenantId + "/preCheck";
        URI uri = URI.create(getHost() + requestURI);
        ResponseEntity<ObTenantsPassEmpty> responseEntity = restTemplate.exchange(uri, HttpMethod.GET,
                getHttpEntity(null, cookieValue), ObTenantsPassEmpty.class);
        return responseEntity.getBody();
    }

    public void changeTenantPass(String tenantName, String cookieValue) throws Exception {
        String requestURI = "/api/v1/ob/tenants/checkTenantPassword";
        URI uri = URI.create(getHost() + requestURI);
        Map<String, String> request = new HashMap<>();
        request.put("tenantName", tenantName);
        String password;
        if (tenantName.equals("tenant")) {
            password = tenantPassword;
        } else if (tenantName.equals("ocp_meta")) {
            password = ocpMetaPassword;
        } else {
            log.warn("the tenant's password don't need change, tenant name:{}", tenantName);
            return;
        }
        String publicKey = getPublicKey();
        String passwordEncrypt = RSAUtil.encrypt(password, publicKey);
        request.put("newPassword", passwordEncrypt);
        HttpEntity httpEntity = getHttpEntity(request, cookieValue);
        ResponseEntity<String> responseEntity = restTemplate.exchange(uri, HttpMethod.POST, httpEntity, String.class);
        log.info("password change result:{}", responseEntity.getBody());
    }

    public void configPass(String tenantName, String cookieValue) throws Exception {
        String requestURI = "/api/v1/ob/tenants/createOrReplacePassword";
        URI uri = URI.create(getHost() + requestURI);
        Map<String, String> request = new HashMap<>();
        request.put("tenantName", tenantName);
        String password;
        if (tenantName.equals("tenant")) {
            password = tenantPassword;
        } else if (tenantName.equals("ocp_meta")) {
            password = ocpMetaPassword;
        } else {
            log.warn("the tenant's password don't need config, tenant name:{}", tenantName);
            return;
        }
        String publicKey = getPublicKey();
        String passwordEncrypt = RSAUtil.encrypt(password, publicKey);
        request.put("newPassword", passwordEncrypt);
        HttpEntity httpEntity = getHttpEntity(request, cookieValue);
        ResponseEntity<String> responseEntity = restTemplate.exchange(uri, HttpMethod.POST, httpEntity, String.class);
        log.info("password config result:{}", responseEntity.getBody());
    }

    public HttpEntity getHttpEntity(Object body, String cookieValue) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Cookie", cookieValue);
        String[] cookieArray = cookieValue.split(";");
        for (String value: cookieArray) {
            if(value.contains("XSRF-TOKEN")) {
                String token = value.split("=")[1];
                headers.add("X-Xsrf-token", token);
            }
        }
        if (Objects.isNull(body)) {
            return new HttpEntity(headers);
        }
        return new HttpEntity(body, headers);
    }

    public void getRestResult(HttpServletRequest request, HttpServletResponse response) {
        List<String> cookies = getOcpCookie(request);
        String requestURI = request.getRequestURI().substring(request.getRequestURI().indexOf("/manager/monitor/ocb") + 20);
        String queryString = request.getQueryString();
        URI uri = URI.create(getHost() + requestURI + (queryString == null ? "" : ("?" + queryString)));

        HttpMethod method = HttpMethod.valueOf(request.getMethod());

        if (HttpMethod.GET.equals(method)) {
            executeGetRequest(response, uri, cookies, restTemplate);
        } else {
            executeNoGetRequest(response, uri, cookies, restTemplate, request);
        }
    }

    public static void executeGetRequest(HttpServletResponse response, URI uri, List<String> cookies, RestTemplate restTemplate) {
        RequestCallback getCallback = clientHttpRequest -> {
            HttpHeaders headers = clientHttpRequest.getHeaders();
            String cookieValue = cookies.stream().filter(cookie -> cookie.contains(";")
                            && !cookie.contains("XSRF-TOKEN=\"\"")).map(cookie -> cookie.split(";")[0])
                    .collect(Collectors.joining("; "));
            headers.add("Cookie", cookieValue);
            String[] cookieArray = cookieValue.split(";");
            for (String value: cookieArray) {
                if(value.contains("XSRF-TOKEN")) {
                    String token = value.split("=")[1];
                    headers.add("X-Xsrf-token", token);
                }
            }
        };
        executeRestRequest(response, uri, HttpMethod.GET, getCallback, restTemplate);
    }

    public static void executeNoGetRequest(HttpServletResponse response, URI uri, List<String> cookies,
                                           RestTemplate restTemplate, HttpServletRequest request) {
        RequestCallback requestCallback = clientHttpRequest -> {
            HttpHeaders headers = clientHttpRequest.getHeaders();
            String cookieValue = cookies.stream().filter(cookie -> cookie.contains(";")
                            && !cookie.contains("XSRF-TOKEN=\"\"")).map(cookie -> cookie.split(";")[0])
                    .collect(Collectors.joining("; "));
            headers.add("Cookie", cookieValue);
            String[] cookieArray = cookieValue.split(";");
            for (String value: cookieArray) {
                if(value.contains("XSRF-TOKEN")) {
                    String token = value.split("=")[1];
                    headers.add("X-Xsrf-token", token);
                }
            }
            headers.add(HttpHeaders.CONTENT_TYPE, "application/json");
            StreamUtils.copy(request.getInputStream(), clientHttpRequest.getBody());
        };
        executeRestRequest(response, uri, HttpMethod.valueOf(request.getMethod()), requestCallback, restTemplate);
    }

    public static void executeRestRequest(HttpServletResponse response, URI uri, HttpMethod method,
                                          RequestCallback getCallback, RestTemplate restTemplate) {
        restTemplate.execute(uri, method, getCallback, clientHttpResponse -> {
            response.setStatus(clientHttpResponse.getStatusCode().value());
            if (null != clientHttpResponse.getHeaders().getContentType()) {
                response.setContentType(clientHttpResponse.getHeaders().getContentType().toString());
            }
            StreamUtils.copy(clientHttpResponse.getBody(), response.getOutputStream());
            return null;
        });
    }
}
