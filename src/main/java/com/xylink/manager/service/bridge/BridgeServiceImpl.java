package com.xylink.manager.service.bridge;

import com.xylink.config.bridge.H5Config;
import com.xylink.manager.service.remote.bridge.BridgeRemoteClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025-04-27 18:09
 */
@Service
public class BridgeServiceImpl implements IBridgeService{
    @Resource
    private BridgeRemoteClient bridgeRemoteClient;
    @Override
    public H5Config getH5Config() {
        return bridgeRemoteClient.getH5Config();
    }

    @Override
    public void setH5Config(H5Config h5Config) {
        bridgeRemoteClient.setH5Config(h5Config);
    }
}
