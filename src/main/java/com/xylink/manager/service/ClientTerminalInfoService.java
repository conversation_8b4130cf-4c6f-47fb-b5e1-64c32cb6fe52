package com.xylink.manager.service;

import com.xylink.manager.inspection.config.DataSourceManager;
import com.xylink.manager.mapper.ClientTerminalInfoMapper;
import com.xylink.manager.model.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class ClientTerminalInfoService {

    @Autowired
    private DataSourceManager dataSourceManager;

    @Autowired
    private ClientTerminalInfoMapper clientTerminalInfoMapper;

    private static final Logger logger = LoggerFactory.getLogger(ClientTerminalInfoService.class);

    public Map<String, ClientTerminalInfo> getAllClientTerminalInfoList(String terminalType, String customizedkey) {
        List<ClientTerminalInfo> allClientTerminalInfoList = clientTerminalInfoMapper.getAllClientTerminalInfoList(terminalType);
        if (StringUtils.isNotEmpty(customizedkey)) {
            return allClientTerminalInfoList.stream()
                    .collect(Collectors.toMap(ClientTerminalInfo -> customizedkey + "_" +ClientTerminalInfo.getClientName(),
                            ClientTerminalInfo -> convertClientTerminalInfo(ClientTerminalInfo, customizedkey)));
        }
        return allClientTerminalInfoList.stream()
                .collect(Collectors.toMap(ClientTerminalInfo::getClientName, ClientTerminalInfo -> ClientTerminalInfo));
    }

    private ClientTerminalInfo convertClientTerminalInfo(ClientTerminalInfo clientTerminalInfo, String customizedkey) {
        clientTerminalInfo.setClientName(customizedkey + "_" + clientTerminalInfo.getClientName());
        return clientTerminalInfo;
    }

    public List<TerminalFrontPlatform> getClientTerminalFrontPlatformList() {
        return clientTerminalInfoMapper.getClientTerminalFrontPlatformList();
    }

    public Map<String, Object> getClientTerminalFrontInfoList() {
        Map<String, Object> frontInfoMap = new HashMap<>();
        List<Map<String, String>> clientTerminalFrontInfoList = clientTerminalInfoMapper.getClientTerminalFrontInfoList();
        clientTerminalFrontInfoList.forEach(e->{
            String platform = e.get("platform");
            String key = e.get("terminalKey");
            String value = e.get("terminalValue");
            Map<String, String> valueMap;
            if (frontInfoMap.containsKey(platform)) {
                valueMap = (Map<String, String>) frontInfoMap.get(platform);
            } else {
                valueMap = new HashMap<>();
            }
            valueMap.put(key, value);
            frontInfoMap.put(platform, valueMap);
        });
        return frontInfoMap;
    }

    @Transactional(rollbackFor = Exception.class)
    public void importAllTerminalInfo(TerminalConfigInfo terminalConfigInfo) {
        List<ClientTerminalResult> terminalInfoList = terminalConfigInfo.getTerminalInfo();
        try {
            if (terminalInfoList != null && !terminalInfoList.isEmpty()) {
                clientTerminalInfoMapper.removeAllTerminalInfo();
                clientTerminalInfoMapper.batchAddAllTerminalInfo(terminalInfoList);
            }
            List<TerminalFrontPlatform> terminalFrontPlatformList = terminalConfigInfo.getTerminalFrontPlatform();
            if (terminalFrontPlatformList != null && !terminalFrontPlatformList.isEmpty()) {
                clientTerminalInfoMapper.removeAllTerminalFrontPlatform();
                clientTerminalInfoMapper.batchAddAllTerminalFrontPlatform(terminalFrontPlatformList);
            }
            Map<String, Object> terminalFrontInfoMap = terminalConfigInfo.getTerminalFrontInfo();
            if (terminalFrontInfoMap != null && !terminalFrontInfoMap.isEmpty()) {
                clientTerminalInfoMapper.removeAllTerminalFrontInfo();
                List<ClientTerminalFrontInfo> terminalFrontInfoList = convertToClientTerminalFrontInfo(terminalFrontInfoMap);
                clientTerminalInfoMapper.batchAddAllTerminalFrontInfo(terminalFrontInfoList);
            }
        } catch (Exception e) {
            logger.error("importAllTerminalInfo error", e);
            e.printStackTrace();
        }
    }

    private List<ClientTerminalFrontInfo> convertToClientTerminalFrontInfo(Map<String, Object> terminalFrontInfoMap) {
        List<ClientTerminalFrontInfo> terminalFrontInfoList = new ArrayList<>();
        for (Map.Entry<String, Object> entry : terminalFrontInfoMap.entrySet()) {
            String platform = entry.getKey();
            Map<String, String> contentMap = (Map<String, String>)entry.getValue();
            for (Map.Entry<String, String> valueEntry : contentMap.entrySet()) {
                String terminalKey = valueEntry.getKey();
                String terminalValue = valueEntry.getValue();
                ClientTerminalFrontInfo terminalFrontInfo = ClientTerminalFrontInfo.builder()
                        .terminalPlatform(platform)
                        .terminalKey(terminalKey)
                        .terminalValue(terminalValue)
                        .build();
                terminalFrontInfoList.add(terminalFrontInfo);
            }
        }
        return terminalFrontInfoList;
    }

    public Map<String, TerminalConfigInfo> loadAllTerminalInfoList() {
        Map<String, TerminalConfigInfo> resultMap = new HashMap<>();
        TerminalConfigInfo tList = TerminalConfigInfo.builder()
                .terminalFrontPlatform(getClientTerminalFrontPlatformList())
                .terminalFrontInfo(getClientTerminalFrontInfoList())
                .terminalInfo(clientTerminalInfoMapper.getAllTerminalInfoList())
                .build();
        resultMap.put("terminal", tList);
        return resultMap;
    }
}
