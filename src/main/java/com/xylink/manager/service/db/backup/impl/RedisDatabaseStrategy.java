package com.xylink.manager.service.db.backup.impl;

import com.xylink.manager.service.db.backup.base.DatabaseType;
import com.xylink.manager.service.db.backup.base.param.BaseDatabaseBackupParam;
import com.xylink.manager.service.db.backup.base.param.BaseDatabaseRestoreParam;
import com.xylink.manager.service.db.backup.base.param.NoSQLDBBackupParam;
import com.xylink.manager.service.db.backup.base.param.NoSQLDBRestoreParam;
import com.xylink.manager.service.ecvs.RedisOpsService;
import com.xylink.util.SpringBeanUtil;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @since 2024/11/6 17:56 下午
 * Redis数据库操作类
 */
public class RedisDatabaseStrategy extends AbstractDatabaseStrategy {

    public RedisDatabaseStrategy(DatabaseType databaseType) {
        super(databaseType);
    }

    @Override
    public List<CompletableFuture<Void>> backup(BaseDatabaseBackupParam param) {
        NoSQLDBBackupParam noSQLDBBackupParam = (NoSQLDBBackupParam)param;
        SpringBeanUtil.getBean(RedisOpsService.class).backup(noSQLDBBackupParam.getFileName(),noSQLDBBackupParam.getTime());
        return null;
    }

    @Override
    public List<CompletableFuture<Void>> restore(BaseDatabaseRestoreParam param) {
        NoSQLDBRestoreParam rparam = (NoSQLDBRestoreParam)param;
        SpringBeanUtil.getBean(RedisOpsService.class).restore(rparam.getFileName(),rparam.getTime());
        return null;
    }
}
