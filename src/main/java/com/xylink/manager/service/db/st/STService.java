package com.xylink.manager.service.db.st;

import com.xylink.config.NetworkConstants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.config.exception.basic.WebException;
import com.xylink.manager.model.DbBackupFile;
import com.xylink.manager.model.em.BackupFileType;
import com.xylink.manager.model.em.DBType;
import com.xylink.manager.model.em.ShenTongDBType;
import com.xylink.manager.service.BackUpNotifyService;
import com.xylink.manager.service.ServerNetworkService;
import com.xylink.manager.service.base.K8sSvcService;
import com.xylink.manager.service.db.DBCommon;
import com.xylink.manager.service.db.HasRunningTaskException;
import com.xylink.manager.service.db.RunningStTask;
import com.xylink.manager.service.db.RunningStTaskHolder;
import com.xylink.manager.service.remote.logagent.LogAgentBackupFileService;
import com.xylink.util.Ipv6Util;
import com.xylink.util.JDBCUtils;
import com.xylink.util.TaskUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 神通数据库
 */
@Slf4j
@Service
public class STService extends DBCommon {
    private static final Logger logger = LoggerFactory.getLogger(STService.class);

    @Autowired
    private RunningStTaskHolder runningStTaskHolder;
    @Autowired
    private JDBCUtils jdbcUtils;
    @Autowired
    private K8sSvcService k8sSvcService;
    @Autowired
    private ServerNetworkService serverNetworkService;
    @Autowired
    private BackUpNotifyService backUpNotifyService;
    @Autowired
    private LogAgentBackupFileService logAgentBackupFileService;
    @Autowired
    private RestTemplate restTemplate;

    @Value("${base.dir}")
    private String baseDir;

    public RunningStTask backup() {
        if (runningStTaskHolder.attemptToBeginStTask()) {
            try {
                RunningStTask runningStTask = backupSt();
                runningStTaskHolder.setRunningStTask(runningStTask);
                return runningStTask;
            } finally {
                runningStTaskHolder.cancelAttemptToBeginStTask();
            }
        } else {
            logger.warn("There is another shentong task is running...");
            throw new HasRunningTaskException(null);
        }
    }

    /**
     * backup shentong db
     *
     * @param fileName
     * @param time
     * @return
     */
    public RunningStTask backup(String fileName, String time, String stPodIp, String mainIp) {
//        if (runningStTaskHolder.attemptToBeginStTask()) {
//            try {
//                RunningStTask runningStTask = backupSt(fileName, time,stPodIp,mainIp);
//                runningStTaskHolder.setRunningStTask(runningStTask);
//                return runningStTask;
//            } finally {
//                runningStTaskHolder.cancelAttemptToBeginStTask();
//            }
//        } else {
//            logger.warn("There is another shentong task is running...");
//            throw new HasRunningTaskException(null);
//        }
        RunningStTask runningStTask = backupSt(fileName, time, stPodIp, mainIp);
        if (!backUpNotifyService.checkBackUpNotify(BackupFileType.StType, fileName)) {
            logAgentBackupFileService.cpBackupFileToMainNode(stPodIp, time, fileName, fileName, BackupFileType.StType.getValue());
        }
        return runningStTask;
    }

    public List<DbBackupFile> listStBackupFiles() {
        String stPodIp = serverNetworkService.getNetworkConfiguration().get(NetworkConstants.DATABASE_IP);
        if (StringUtils.isBlank(stPodIp)) {
            return Collections.emptyList();
        }
        String url = "http://" + Ipv6Util.handlerIpv6Addr(stPodIp) + ":" + NetworkConstants.LOGAGENT_PORT + "/st/list";
        logger.info("listStBackupFiles http url : " + url);
        DbBackupFile[] dbBackupFiles = restTemplate.getForObject(url, DbBackupFile[].class);
        if (ArrayUtils.isNotEmpty(dbBackupFiles)) {
            return Arrays.asList(dbBackupFiles);
        } else {
            return Collections.emptyList();
        }
    }

    public RunningStTask getCurrentRunningStTask() {
        return runningStTaskHolder.getRunningStTask();
    }

    public boolean downloadBackupFile(HttpServletResponse response, String fileName) {
        String stPodIp = serverNetworkService.getNetworkConfiguration().get(NetworkConstants.DATABASE_IP);
        if (StringUtils.isBlank(stPodIp)) {
            return Boolean.FALSE;
        }
        String clientVersionUrl = "http://" + Ipv6Util.handlerIpv6Addr(stPodIp) + ":" + NetworkConstants.LOGAGENT_PORT + "/st/download/" + fileName;
        logger.info("downloadBackupFile http url : " + clientVersionUrl);
        ResponseEntity<Resource> responseEntity = restTemplate.exchange(clientVersionUrl, HttpMethod.GET, null, Resource.class);
        if (responseEntity.getStatusCode().is2xxSuccessful()) {
            TaskUtils.downloadBackupFile(fileName, responseEntity.getBody(), response);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    public void deleteBackupFile(String fileName) {
        String stPodIp = serverNetworkService.getNetworkConfiguration().get(NetworkConstants.DATABASE_IP);
        if (StringUtils.isBlank(stPodIp)) {
            return;
        }
        String clientVersionUrl = "http://" + Ipv6Util.handlerIpv6Addr(stPodIp) + ":" + NetworkConstants.LOGAGENT_PORT + "/st/delete/" + fileName;
        logger.info("deleteBackupFile http url : " + clientVersionUrl);
        ResponseEntity<Void> responseEntity = restTemplate.exchange(clientVersionUrl, HttpMethod.GET, null, Void.class);
        logger.info("deleteBackupFile status:{}", responseEntity.getStatusCode());
    }

    public String uploadAndRestore(MultipartFile file, DBType dbType) {
        String stPodIp = getStPodIp(dbType);
        uploadStBackupFile(file, stPodIp);
        return restore(file.getOriginalFilename(), stPodIp);
    }

    private String getStPodIp(DBType dbType) {
        String stPodIp = null;
        switch (dbType) {
            case main:
                stPodIp = serverNetworkService.getNetworkConfiguration().get(NetworkConstants.DATABASE_IP);
                break;
            case statis:
                stPodIp = serverNetworkService.getNetworkConfiguration().get(NetworkConstants.STATIS_DATABASE_IP);
                break;
            default:
        }
        return stPodIp;
    }

    public String restore(String restoreFileName, String stPodIp) {
        if (restoreFileName == null || !restoreFileName.endsWith(".osrbk")) {
            throw new WebException(ErrorStatus.FILE_PATH_ILLEGAL);
        }

        if (runningStTaskHolder.attemptToBeginStTask()) {
            try {
                RunningStTask runningStTask = restoreSt(restoreFileName, stPodIp);
                runningStTaskHolder.setRunningStTask(runningStTask);
                return runningStTask.uuid;
            } finally {
                runningStTaskHolder.cancelAttemptToBeginStTask();
            }
        } else {
            logger.warn("There is another st task is running...");
            throw new HasRunningTaskException(null);
        }
    }

    private void uploadStBackupFile(MultipartFile file, String stPodIp) {
        if (StringUtils.isBlank(stPodIp)) {
            throw new ServerException("该服务数据库未启动!");
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        MultiValueMap<String, Object> parts = new LinkedMultiValueMap<>();
        parts.add(file.getOriginalFilename(), file.getResource());
        HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(parts, headers);
        String clientVersionUrl = "http://" + Ipv6Util.handlerIpv6Addr(stPodIp) + ":" + NetworkConstants.LOGAGENT_PORT + "/st/upload";
        logger.info("uploadStBackupFile http url : " + clientVersionUrl);

        try {
            ResponseEntity<Void> responseEntity = restTemplate.postForEntity(clientVersionUrl, httpEntity, Void.class);
            HttpStatus status = responseEntity.getStatusCode();
            logger.info("uploadStBackupFile status:{}", status);
            if (HttpStatus.INTERNAL_SERVER_ERROR == status || HttpStatus.EXPECTATION_FAILED == status) {
                throw new ServerException("上传文件校验失败!");
            }
            if (!status.is2xxSuccessful()) {
                throw new ServerException("上传文件失败!");
            }
        } catch (Exception e) {
            logger.error("uploadStBackupFile error", e);
            throw new ServerException("上传文件失败!");
        }
    }

    private RunningStTask backupSt() {
        Map<String, String> map = serverNetworkService.getNetworkConfiguration();
        String stPodIp = map.get(NetworkConstants.DATABASE_IP);
        String mainIp = map.get(NetworkConstants.MAIN_INTERNAL_IP);

        RunningStTask baseTask = new RunningStTask();
        baseTask.backupRun(stPodIp, mainIp, restTemplate, null);
        return baseTask;
    }

    private RunningStTask backupSt(String fileName, String time, String stPodIp, String mainIp) {
        RunningStTask baseTask = new RunningStTask();
        try {
            baseTask.backupRun(stPodIp, mainIp, restTemplate, fileName);
            TimeUnit.MINUTES.sleep(2);
        } catch (Exception e) {
            logger.error(fileName + " backup error.", e);
            e.printStackTrace();
        }
        return baseTask;
    }

    public RunningStTask restoreSt(String restoreFileName, String stPodIp) {
        RunningStTask baseTask = new RunningStTask();
        Map<String, String> map = serverNetworkService.getNetworkConfiguration();
        String mainIp = map.get(NetworkConstants.MAIN_INTERNAL_IP);
        baseTask.restoreRun(stPodIp, mainIp, restTemplate, restoreFileName);
        backUpNotifyService.checkBackUpNotify(BackupFileType.StRestoreType, restoreFileName);
        return baseTask;
    }

    /**
     * 清理神通数据库备份文件
     */
    public void batchDeleteShenTongBackupFile() {
        Arrays.stream(ShenTongDBType.values()).forEach(shenTongDBType -> batchDeleteBackupFile(shenTongDBType.getAllIpKey()));
    }


    public void batchDeleteBackupFile(String allIpKey) {
        Map<String, String> map = serverNetworkService.getNetworkConfiguration();
        String stPodIp = map.get(allIpKey);
        if (StringUtils.isBlank(stPodIp)) {
            return;
        }
        String url = "http://" + Ipv6Util.handlerIpv6Addr(stPodIp) + ":" + NetworkConstants.LOGAGENT_PORT + "/st/delete?expiredInterval=30";
        logger.info("batchDeleteBackupFile http url : " + url);
        try {
            ResponseEntity<Void> responseEntity = restTemplate.exchange(url, HttpMethod.GET, null, Void.class);
            logger.info("batchDeleteBackupFile status:{}", responseEntity.getStatusCode());
        } catch (Exception e) {
            logger.error("remove expired st backup file error, ", e);
        }
    }

    @Override
    public int getLicenseValidDays(String dbIp, String dbPort) throws SQLException {
        String sql = "SELECT validdays";
        Connection connection = null;
        PreparedStatement ps = null;
        ResultSet resultSet = null;
        try {
            connection = jdbcUtils.getBackupAccountConnection("ST", dbIp, dbPort, null);
            ps = connection.prepareStatement(sql);
            resultSet = ps.executeQuery();
            if (resultSet.next()) {
                int validdays = resultSet.getInt(1);
                logger.info("ST getLicenseValidDays:{}", validdays);
                return validdays;
            }
        } catch (Exception e) {
            log.error("Query [{}] error. \n", sql, e);
            throw e;
        } finally {
            jdbcUtils.close(connection, ps, resultSet);
        }
        return Integer.MIN_VALUE;
    }

}
