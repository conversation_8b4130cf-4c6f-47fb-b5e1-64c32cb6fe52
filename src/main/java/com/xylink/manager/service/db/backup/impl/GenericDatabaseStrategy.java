package com.xylink.manager.service.db.backup.impl;

import com.xylink.manager.service.clustersetting.failover.telnet.TelnetClient;
import com.xylink.manager.service.db.backup.base.DatabaseType;
import com.xylink.manager.service.db.backup.base.param.BaseDatabaseBackupParam;
import com.xylink.manager.service.db.backup.base.param.BaseDatabaseRestoreParam;
import com.xylink.manager.service.db.backup.base.param.RelationalDBBackupParam;
import com.xylink.manager.service.db.backup.base.param.RelationalDBRestoreParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/11/6 17:56 下午
 * 数据库操作通用类，如果没有特殊要求，就用这个实现类
 */
public class GenericDatabaseStrategy extends AbstractDatabaseStrategy {

    private static final Logger LOGGER = LoggerFactory.getLogger(GenericDatabaseStrategy.class);

    public GenericDatabaseStrategy(DatabaseType databaseType) {
        super(databaseType);
    }



    @Override
    public List<CompletableFuture<Void>> backup(BaseDatabaseBackupParam param) {
        RelationalDBBackupParam rparam = (RelationalDBBackupParam) param;

        //获取时间
        String dbBackupPath = rparam.getDbBackupPath();
        dbBackupPath = dbBackupPath.substring(0, dbBackupPath.length() - 1);
        String time = dbBackupPath.substring(dbBackupPath.lastIndexOf('/') + 1);

        List<RelationalDBBackupParam.Address> addressList = rparam.getAddressList();
        if(addressList == null || addressList.isEmpty()){
            //如果地址集合为空，说明配置参数有误，打印错误日志，结束
            LOGGER.error("db backup addressList is empty, please check your config");
            return null;
        }
        List<CompletableFuture<Void>> res = addressList.stream()
                .map(t -> CompletableFuture.runAsync(() -> {
                            try {
                                TelnetClient telnetClient = new TelnetClient(t.getIpAddress(),Integer.parseInt(t.getPort()));
                                boolean b = telnetClient.tryTelnet();
                                if(b){
                                    LOGGER.info("starting to bakup: " + databaseType.getName()+ " " +
                                            t.getIpAddress() + " " + t.getPort());

                                    executeShellBackup(databaseType.getBackupScriptFileName(),
                                            t.getIpAddress(),
                                            t.getPort(),
                                            rparam.getBaseDir(),
                                            rparam.getDbUser(),
                                            rparam.getDbPassword(),
                                            rparam.getDbBackupPath(),
                                            rparam.getBaseDir() +  "/logs/db/backup/" + time
                                    );
                                }else {
                                    LOGGER.error("backup failed : telnet " + t.getIpAddress() + " " + t.getPort() + " failed");
                                }
                            } catch (Exception e) {
                                LOGGER.error("backup failed: " + e.getMessage());
                            }
                        }
                        , rparam.getDbPool()))
                .collect(Collectors.toList());

        return res;
    }

    @Override
    public List<CompletableFuture<Void>> restore(BaseDatabaseRestoreParam param) {
        RelationalDBRestoreParam rparam = (RelationalDBRestoreParam) param;
        //获取时间
        String dbBackupPath = rparam.getDbBackupPath();
        dbBackupPath = dbBackupPath.substring(0, dbBackupPath.length() - 1);
        String time = dbBackupPath.substring(dbBackupPath.lastIndexOf('/') + 1);

        List<RelationalDBRestoreParam.Address> addressList = rparam.getAddressList();
        if(addressList == null || addressList.isEmpty()){
            //如果地址集合为空，说明配置参数有误，打印错误日志，结束
            LOGGER.error("db backup addressList is empty, please check your config");
            return null;
        }
        List<CompletableFuture<Void>> res = addressList.stream()
                .map(t -> CompletableFuture.runAsync(() -> {
                            try {
                                TelnetClient telnetClient = new TelnetClient(t.getIpAddress(),Integer.parseInt(t.getPort()));
                                boolean b = telnetClient.tryTelnet();
                                if(b){
                                    LOGGER.info("starting to restore: " + databaseType.getName()+ " " +
                                            t.getIpAddress() + " " + t.getPort());

                                    executeShellRestore(databaseType.getRestoreScriptFileName(),
                                            t.getIpAddress(),
                                            t.getPort(),
                                            rparam.getBaseDir(),
                                            rparam.getDbUser(),
                                            rparam.getDbPassword(),
                                            rparam.getDbBackupPath(),
                                            rparam.getBaseDir() +  "/logs/db/restore/" + time
                                    );
                                }else {
                                    LOGGER.error("restore failed : telnet " + t.getIpAddress() + " " + t.getPort() + " failed");
                                }
                            } catch (Exception e) {
                                LOGGER.error("restore failed: " + e.getMessage());
                            }

                        }
                        , rparam.getDbPool()))
                .collect(Collectors.toList());

        return res;
    }
}
