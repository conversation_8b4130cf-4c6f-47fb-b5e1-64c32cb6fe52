package com.xylink.manager.service.db;

public class HasRunningTaskException extends DBException{
    private final RunningDBTask runningDBTask;

    public RunningDBTask getRunningDBTask() {
        return runningDBTask;
    }

    public HasRunningTaskException(RunningDBTask runningDBTask) {
        this.runningDBTask = runningDBTask;
    }

    public HasRunningTaskException(String message, RunningDBTask runningDBTask) {
        super(message);
        this.runningDBTask = runningDBTask;
    }

    public HasRunningTaskException(String message, Throwable cause, RunningDBTask runningDBTask) {
        super(message, cause);
        this.runningDBTask = runningDBTask;
    }

    public HasRunningTaskException(Throwable cause, RunningDBTask runningDBTask) {
        super(cause);
        this.runningDBTask = runningDBTask;
    }

    public HasRunningTaskException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace, RunningDBTask runningDBTask) {
        super(message, cause, enableSuppression, writableStackTrace);
        this.runningDBTask = runningDBTask;
    }
}
