package com.xylink.manager.service.db;

import com.xylink.util.EtcdBackupUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicLong;

public class RunningEtcdTask {
    private static final Logger logger = LoggerFactory.getLogger(RunningEtcdTask.class);
    private static final ExecutorService executor = Executors.newFixedThreadPool(5);

    public final String uuid = UUID.randomUUID().toString();
    public final AtomicLong startTimestamp = new AtomicLong();

    public RunningEtcdTask() {
    }

    public RunningEtcdTask(Long time) {
        super();
        startTimestamp.set(time);
    }

    public void restoreRun(RestTemplate restTemplate, String fileName) {
        startTimestamp.set(System.currentTimeMillis());
        executor.execute(() -> EtcdBackupUtil.etcdRestoreUtil(fileName, restTemplate));
    }

    public void backupRun(RestTemplate restTemplate) {
        startTimestamp.set(System.currentTimeMillis());
        executor.execute(() -> EtcdBackupUtil.etcdBackUpUtil(restTemplate));
    }

    public String getTaskId() {
        return uuid;
    }

    public List<Pair<Integer, String>> getCachedErrorOut() {
        return Collections.emptyList();
    }

    public boolean isCompleted() {
        return System.currentTimeMillis() - startTimestamp.get() > 2 * 60 * 1000;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("RunningEtcdTask {");
        sb.append("startTimestamp = ").append(startTimestamp);
        sb.append('}');
        return sb.toString();
    }


}
