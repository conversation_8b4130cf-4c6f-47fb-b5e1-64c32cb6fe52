package com.xylink.manager.service.db.backup.base;

import com.xylink.manager.service.db.backup.impl.*;

/**
 * <AUTHOR>
 * @since 2024/11/6 17:56 下午
 * 数据库备份类型枚举
 */
public enum DatabaseType {

    MYSQL("MYSQL",true, GenericDatabaseStrategy.class, "mysql_backup.sh", "mysql_restore.sh"),
    DAMENG("DM",true, DamengDatabaseStrategy.class, "dm_backup.sh", "dm_restore.sh"),
    SHENTONG("ST", true,GenericDatabaseStrategy.class, "shentong_backup.sh", "shentong_restore.sh"),
    KINGBASE("JC",true, GenericDatabaseStrategy.class, "kingbase_backup.sh", "kingbase_restore.sh"),
    POSTGRESQL("PGSQL", true,GenericDatabaseStrategy.class, "pg_backup.sh", "pg_restore.sh"),
    OCEANBASE("OB",true, GenericDatabaseStrategy.class, "ob_backup.sh", "ob_restore.sh"),
    GAUSSDB("GAUSSDB",true, GaussDatabaseStrategy.class, "gaussdb_backup.sh", "gaussdb_restore.sh"),
    TDSQL("TDSQL",true, GenericDatabaseStrategy.class, "tdsql_backup.sh", "tdsql_restore.sh"),
    HBASE("hbase", false, HBaseDatabaseStrategy.class, null, null),
    ETCD("etcd",false, EtcdDatabaseStrategy.class, null, null),
    ES("es",false, ESDatabaseStrategy.class, null, null),
    REDIS("redis",false, RedisDatabaseStrategy.class, null, null);

    private final String name;
    private final boolean isRDBMS;
    private final Class<? extends DatabaseStrategy> strategyClass;
    private final String backupScriptFileName;
    private final String restoreScriptFileName;

    DatabaseType(String name, boolean isRDBMS, Class<? extends DatabaseStrategy> strategyClass, String backupScriptFileName, String restoreScriptFileName) {
        this.name = name;
        this.isRDBMS = isRDBMS;
        this.strategyClass = strategyClass;
        this.backupScriptFileName = backupScriptFileName;
        this.restoreScriptFileName = restoreScriptFileName;
    }

    public String getName() {
        return name;
    }

    public Class<? extends DatabaseStrategy> getStrategyClass() {
        return strategyClass;
    }

    public String getBackupScriptFileName() {
        return backupScriptFileName;
    }

    public String getRestoreScriptFileName() {
        return restoreScriptFileName;
    }

    public boolean isRDBMS() {
        return isRDBMS;
    }

    public static DatabaseType fromName(String name) {
        for (DatabaseType type : values()) {
            if (type.getName().equalsIgnoreCase(name)) {
                return type;
            }
        }
        throw new IllegalArgumentException("不支持的数据库类型: " + name);
    }
}
