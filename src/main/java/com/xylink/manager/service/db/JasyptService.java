package com.xylink.manager.service.db;

import com.xylink.config.Constants;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.base.PlatformConfig;
import com.xylink.manager.service.cache.bean.ConfigMapCache;
import com.xylink.manager.service.cache.service.ICacheService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jasypt.password.SEncryptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class JasyptService {
    @Autowired
    private ICacheService cacheService;
    @Autowired
    private K8sService k8sService;

    /**
     * 获取 jasypt 加密算法
     * @return
     */
    public String getJasyptEncryptorAlgorithm() {
        String jasyptConfig = getIpChangeCM().get("jasypt_config");
        if (StringUtils.isBlank(jasyptConfig)) {
            return "PBEWithHMACSHA512AndAES_256";
        }
        Matcher matcher = Pattern.compile("encryptor_algorithm:.*\n").matcher(jasyptConfig);
        return matcher.find() ? matcher.group().split(":")[1].trim() : null;
    }

    /**
     * 获取 jasypt 口令（也即 密码盐salt）
     * @return
     */
    public String getJasyptEncryptorPassword() {
        String jasyptConfig = getIpChangeCM().get("jasypt_config");
        if (StringUtils.isBlank(jasyptConfig)) {
            return "vw6i3yrvxFmb1OFz";
        }
        Matcher matcher = Pattern.compile("encryptor_password:.*\n").matcher(jasyptConfig);
        return matcher.find() ? matcher.group().split(":")[1].trim() : null;
    }

    private Map<String, String> getIpChangeCM() {
        ConfigMapCache configMapCache = cacheService.cacheConfigMapByName("ip-change");
        return configMapCache == null ? new HashMap<>() : configMapCache.getData();
    }

    /**
     * 加密
     * @param input
     * @return
     */
    public String encrypt(String input) {
        return encrypt(input, getJasyptEncryptorPassword());
    }
    private String encrypt(String input, String salt) {
        SEncryptor encryptor = new SEncryptor(getJasyptEncryptorAlgorithm());
        encryptor.setPassword(salt);
        return "ENC(" + encryptor.encrypt(input) + ")";
    }


    /**
     * 解密
     * @param input
     * @return
     */
    public String decrypt(String input) {
        return decrypt(input, getJasyptEncryptorPassword());
    }
    private String decrypt(String input, String salt) {
        SEncryptor encryptor = new SEncryptor(getJasyptEncryptorAlgorithm());
        encryptor.setPassword(salt);
//        return encryptor.decrypt(input.substring(4, input.length()-1));
        return encryptor.sDecrypt(input);
    }


    public String encryptMatrixPwd(String input) {
        String result = callExternalTool(input, "vw6i3yrvxFmb1OFz", "0");
        String encPwd = result.split("\n")[0];
        String salt = result.split("\n")[1];
        Map<String, String> allMatrix = k8sService.getConfigmap(Constants.CONFIGMAP_ALL_MATRIX);
        allMatrix.put("MATRIX_DB_MEDIA_PASSWORD_SALT", salt);
        k8sService.editConfigmap(Constants.CONFIGMAP_ALL_MATRIX, allMatrix);
        return encPwd;
    }
    public String decryptMatrixPwd(String input) {
        Map<String, String> allMatrix = k8sService.getConfigmap(Constants.CONFIGMAP_ALL_MATRIX);
        return callExternalTool(input, allMatrix.get("MATRIX_DB_MEDIA_PASSWORD_SALT"), "1");
    }
    private String callExternalTool(String pwd, String salt, String opt) {
        String xyencTools = "scripts/xyenc_tools_for_cpp_x64";
        if (PlatformConfig.isAnKe()) {
            xyencTools = "scripts/xyenc_tools_for_cpp_aarch64";
        }

        try {
            // 获取外部工具脚本文件
            ClassPathResource resource = new ClassPathResource(xyencTools);
            InputStream scriptInputStream = resource.getInputStream();

            Path tempFile = Files.createTempFile("xyenc_tools_for_cpp", "");
            Files.copy(scriptInputStream, tempFile, StandardCopyOption.REPLACE_EXISTING);

            // 设置脚本文件为可执行
            File scriptFile = tempFile.toFile();
            if (!scriptFile.setExecutable(true)) {
                throw new IOException("Failed to set executable permission on script file");
            }

            ProcessBuilder processBuilder = new ProcessBuilder(scriptFile.getAbsolutePath(), pwd, salt, opt);
            Process process = processBuilder.start();

            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                StringBuilder output = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
                // 等待工具执行完成
                List<String> command = processBuilder.command();
                String commandStr = String.join(" ", command);
                log.info("JasyptService callExternalTool invoke. command = {}, output={}", commandStr, output);

                int exitCode = process.waitFor();
                if (exitCode == 0) {
                    return output.toString();
                } else {
                    throw new ServerException(String.format("JasyptService callExternalTool invoke failed. command=%s, exitCode=%s, output=%s", commandStr, exitCode, output));
                }
            } catch (IOException e) {
                throw new IOException("Error occurred while reading output: " + e.getMessage());
            } finally {
                // 删除临时文件
                Files.deleteIfExists(tempFile);
            }

        } catch (IOException | InterruptedException e) {
            log.error("callExternalTool error.", e);
            throw new ServerException(String.format("JasyptService callExternalTool invoke failed. salt=%s, opt=%s", salt, opt), e);
        }
    }

}
