package com.xylink.manager.service.db;

import com.xylink.config.Constants;
import com.xylink.manager.model.DBCleanConfig;
import com.xylink.manager.model.em.DBType;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.util.ZipUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Map;

@Service
@EnableScheduling
public class DBService {
    private final Logger logger = LoggerFactory.getLogger(DBService.class);
    @Autowired
    private RunningDBTaskHolder runningDBTaskHolder;

    @Autowired
    private DBBackupService dbBackupService;

    @Autowired
    private DbUpgradeService dbUpgradeService;

    @Autowired
    private K8sService k8sService;

    @Autowired
    private IDeployService deployService;

    private static final long DB_BACKUP_INTERVAL = 24 * 3600 * 1000;

    public void removeExpiredEtcdBackupFiles() {
        File[] backupFiles = listBackupFiles("ETCD");
        long currentTime = System.currentTimeMillis();
        for (File f : backupFiles) {
            if (currentTime - f.lastModified() > DB_BACKUP_INTERVAL * 7) {
                f.delete();
            }

            if (!f.getName().endsWith(".zip")) {
                logger.info("zip file: " + f.getName());
                ZipUtil.zipAndRemove(f.getAbsolutePath(), f.getAbsolutePath() + ".zip", "");
            }
        }
    }

    private RunningDBTask execute(DBType type, DBTaskWrapper DBTaskWrapper) {
        if (runningDBTaskHolder.attemptToBeginDBTask(type)) {
            try {
                RunningDBTask runningDBTask = DBTaskWrapper.execute();

                runningDBTaskHolder.setRunningDBTask(runningDBTask);

                return runningDBTask;
            } finally {
                runningDBTaskHolder.cancelAttemptToBeginDBTask(type);
            }
        } else {
            logger.warn("There is another DB task (" + type.name() + ") is running...");
            throw new HasRunningTaskException(runningDBTaskHolder.getRunningDBTask(type));
        }
    }

    public RunningDBTask getCurrentRunningDBTask(DBType type) {
        return runningDBTaskHolder.getRunningDBTask(type);
    }

    /**
     * @return
     * @throws HasRunningTaskException:正在有任务执行
     * @throws UnknownException                未知错误
     */
    public RunningDBTask backup(String type) {
        return backup(type, "master");
    }

    public RunningDBTask backup(String type, String db) {
        return execute(DBType.valueOf(type), () -> dbBackupService.backup(type, db));
    }


    public File[] listBackupFiles(String type) {
        return dbBackupService.listBackupFiles(type);
    }

    /**
     * @param fullpath
     * @return
     * @throws HasRunningTaskException:正在有任务执行
     * @throws UnknownException                未知错误
     * @throws NotBackupFileException
     */
    public RunningDBTask restore(final String fullpath, String type) {
        return execute(DBType.valueOf(type), () -> dbBackupService.restore(fullpath, type));
    }

    /**
     * @param upgradeSqlFiles
     * @return
     * @throws HasRunningTaskException:正在有任务执行
     * @throws UnknownException                未知错误
     */
    public RunningDBTask upgrade(DBType type, final File[] upgradeSqlFiles) {

        return execute(type, () -> {
                    for (File upgradeSqlFile : upgradeSqlFiles) {
//                    dbUpgradeService.addUpgradeSql(upgradeSqlFile.getName(),upgradeSqlFile);
                    }
                    return dbUpgradeService.upgrade(type);
                }
        );
    }

    /**
     * @return
     * @throws HasRunningTaskException:正在有任务执行
     * @throws UnknownException                未知错误
     */
    public RunningDBTask upgrade(DBType type) {
        return execute(type, () -> dbUpgradeService.upgrade(type));
    }

    public void deleteBackupFile(String filePath, String type) {
        dbBackupService.deleteBackupFile(filePath, type);
    }

    public String getDBBackupDir() {
        return dbBackupService.getDBBackupDir();
    }

    public String getHostDBBackupDir(String type) {
        return dbBackupService.getHostBackupDir(type, null, false);
    }

    public DBCleanConfig cleanupConfig() throws Exception {
        DBCleanConfig config = new DBCleanConfig();
        Map<String, String> statisCm = k8sService.getConfigmap("private-statis-cleanup");
        config.setStatis(statisCm.get("INTERVAL"));

        Map<String, String> mainCm = k8sService.getConfigmap("private-mysql-cleanup");
        config.setMain(mainCm.get("INTERVAL"));

        return config;

    }

    public void saveCleanupConfig(DBCleanConfig config) {
        try {
            deployService.patchConfigMap("private-mysql-cleanup", Constants.NAMESPACE_DEFAULT, d -> d.put("INTERVAL", config.getMain()));
            deployService.patchConfigMap("private-statis-cleanup", Constants.NAMESPACE_DEFAULT, d -> d.put("INTERVAL", config.getStatis()));
        } catch (Exception e) {
            logger.error("fail to save db clean configmap!\n", e);
            throw e;
        }
    }
}

interface DBTaskWrapper {
    RunningDBTask execute();
}