package com.xylink.manager.service.db.backup.impl;

import com.xylink.config.Constants;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.db.backup.base.DatabaseType;
import com.xylink.manager.service.db.backup.base.param.BaseDatabaseBackupParam;
import com.xylink.manager.service.db.backup.base.param.BaseDatabaseRestoreParam;
import com.xylink.manager.service.db.backup.base.param.RelationalDBBackupParam;
import com.xylink.manager.service.db.backup.base.param.RelationalDBRestoreParam;
import com.xylink.util.SpringBeanUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @since 2024/11/12 11:27 上午
 * 达梦数据库处理类
 */
public class DamengDatabaseStrategy extends GenericDatabaseStrategy {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    public DamengDatabaseStrategy(DatabaseType databaseType) {
        super(databaseType);
    }

    @Override
    public List<CompletableFuture<Void>> backup(BaseDatabaseBackupParam param) {
        RelationalDBBackupParam rparam = (RelationalDBBackupParam) param;
        //区分是主从，还是单体
        K8sService k8sService = SpringBeanUtil.getBean(K8sService.class);
        Map<String, String> configmap = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        String cluster = configmap.get("DM_CLUSTER_MODE");
        if (StringUtils.isNotBlank(cluster) && "master-slave".equalsIgnoreCase(cluster)) {
           //集群
            String ipPorts = configmap.get("DM_CLUSTER_IP_PORT");
            if(StringUtils.isBlank(ipPorts)){
                logger.error("达梦数据库ip port配置dmClusterIpPort有误！");
                return null;
            }

            ArrayList<RelationalDBBackupParam.Address> addresses = new ArrayList<>();
            rparam.setAddressList(addresses);

            String[] split = ipPorts.split(",");
            for (String s : split) {
                String[] address = s.split(":");
                addresses.add(new RelationalDBBackupParam.Address(address[0], address[1]));
            }
        }

        return super.backup(param);
    }

    @Override
    public List<CompletableFuture<Void>> restore(BaseDatabaseRestoreParam param) {
        RelationalDBRestoreParam rparam = (RelationalDBRestoreParam) param;
        //区分是主从，还是单体
        K8sService k8sService = SpringBeanUtil.getBean(K8sService.class);
        Map<String, String> configmap = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        String cluster = configmap.get("DM_CLUSTER_MODE");
        if (StringUtils.isNotBlank(cluster) && "master-slave".equalsIgnoreCase(cluster)) {
            //集群
            String ipPorts = configmap.get("DM_CLUSTER_IP_PORT");
            if(StringUtils.isBlank(ipPorts)){
                logger.error("达梦数据库ip port配置dmClusterIpPort有误！");
                return null;
            }

            ArrayList<RelationalDBRestoreParam.Address> addresses = new ArrayList<>();
            rparam.setAddressList(addresses);

            String[] split = ipPorts.split(",");
            for (String s : split) {
                String[] address = s.split(":");
                addresses.add(new RelationalDBRestoreParam.Address(address[0], address[1]));
            }
        }

        return super.restore(param);
    }
}
