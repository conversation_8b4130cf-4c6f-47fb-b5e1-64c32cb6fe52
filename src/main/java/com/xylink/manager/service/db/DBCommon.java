package com.xylink.manager.service.db;

import com.xylink.config.NetworkConstants;
import com.xylink.manager.controller.vo.config.ConfigCheckInfo;
import com.xylink.manager.model.em.ConfigCheckEnum;
import com.xylink.manager.service.cache.service.ICacheService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public abstract class DBCommon {
    @Autowired
    private ICacheService cacheService;

    public int getLicenseValidDays(String dbIp, String dbPort) throws Exception {
        return Integer.MIN_VALUE;
    }

    public List<ConfigCheckInfo> checkLicense() {
        List<ConfigCheckInfo> configCheckInfoList = new ArrayList<>();
        Map<String, String> allIp = cacheService.cacheConfigMapAllIp().getData();

        String mainDatabaseIp = allIp.get(NetworkConstants.DATABASE_IP);
        if (!"127.0.0.1".equals(mainDatabaseIp)) {
            configCheckInfoList.add(doCheckLicense(mainDatabaseIp, allIp.get(NetworkConstants.DATABASE_PORT), ConfigCheckEnum.MAIN_DB));
        }
        String statisDatabaseIp = allIp.get(NetworkConstants.STATIS_DATABASE_IP);
        if (!"127.0.0.1".equals(statisDatabaseIp)) {
            configCheckInfoList.add(doCheckLicense(statisDatabaseIp, allIp.get(NetworkConstants.STATIS_DATABASE_PORT), ConfigCheckEnum.STATIS_DB));
        }
        String uaaDatabaseIp = allIp.get(NetworkConstants.UAA_DATABASE_IP);
        if (!"127.0.0.1".equals(uaaDatabaseIp)) {
            configCheckInfoList.add(doCheckLicense(uaaDatabaseIp, allIp.get(NetworkConstants.UAA_DATABASE_PORT), ConfigCheckEnum.UAA_DB));
        }

        return configCheckInfoList.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    protected ConfigCheckInfo doCheckLicense(String ip, String port, ConfigCheckEnum configCheckEnum) {
        ConfigCheckInfo configCheckInfo = new ConfigCheckInfo(configCheckEnum);
        try {
            if (getLicenseValidDays(ip, port) < 30) {
                configCheckInfo.setStatus(false);
                configCheckInfo.setStatusDescription("数据库[" + ip + "]license即将过期，请及时处理");
            } else {
                return null;
            }
        } catch (Exception e) {
            configCheckInfo.setStatus(false);
            configCheckInfo.setStatusDescription("数据库[" + ip + "]license检查异常，请及时处理");
        }
        return configCheckInfo;
    }

    public void checkWarningMsg(String ip, String port, Map<String, String> warningMsg) {
        String emailHistory = warningMsg.get("emailHistory");
        StringBuilder message = new StringBuilder(warningMsg.get("message"));

        int days = Integer.MIN_VALUE;
        try {
            days = getLicenseValidDays(ip, port);
        } catch (Exception e) {
            log.error("Get license valid days faild. ip = {}, port = {}", ip, port);
        }
        if (days == Integer.MIN_VALUE) {
            return;
        } else if (days < 0) {
            message.append("数据库[").append(ip).append("]License已过期；");
        } else if (days < 7) {
            message.append("数据库[").append(ip).append("]License将于").append(LocalDate.now().plusDays(days)).append("过期；");
        } else if (days < 14) {
            String item = this.getClass().getSimpleName().substring(0, 2) + "-" + ip + "-15;";
            if (StringUtils.isBlank(emailHistory) || !emailHistory.contains(item)) {
                message.append("数据库[").append(ip).append("]License将于").append(LocalDate.now().plusDays(days)).append("过期；");
                emailHistory = emailHistory + item;
            }
        } else if (days < 30) {
            String item = this.getClass().getSimpleName().substring(0, 2) + "-" + ip + "-30;";
            if (StringUtils.isBlank(emailHistory) || !emailHistory.contains(item)) {
                message.append("数据库[").append(ip).append("]License将于").append(LocalDate.now().plusDays(days)).append("过期；");
                emailHistory = emailHistory + item;
            }
        } else {
            String item = this.getClass().getSimpleName().substring(0, 2) + "-" + ip + "-\\d+;";
            emailHistory = StringUtils.isBlank(emailHistory) ? "" : emailHistory.replaceAll(item, "");
        }

        warningMsg.put("message", message.toString());
        warningMsg.put("emailHistory", emailHistory);
    }
}
