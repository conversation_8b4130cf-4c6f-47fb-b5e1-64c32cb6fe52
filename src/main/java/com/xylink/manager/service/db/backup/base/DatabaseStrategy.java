package com.xylink.manager.service.db.backup.base;

import com.xylink.manager.service.db.backup.base.param.BaseDatabaseBackupParam;
import com.xylink.manager.service.db.backup.base.param.BaseDatabaseRestoreParam;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static com.xylink.config.NetworkConstants.*;

/**
 * <AUTHOR>
 * @since 2024/11/6 17:56 下午
 * 数据库操作策略
 */
public interface DatabaseStrategy {

    public static final String DB_BACKUP_USERNAME = "dbbak";
    public static final String OB_DB_BACKUP_USERNAME = "dbbak@tenant";
    public static final String DB_BACKUP_PWD = "U1O5ZeRyLFd#u9T6TF9h";
    public static final List<String> RELATIONAL_DB_BACKUP_IP =
            Arrays.asList(DATABASE_IP,STATIS_DATABASE_IP,EDU_INTERNAL_IP,DB_SURVEILLANCE,UAA_DATABASE_IP,MATRIX_DATABASE_IP);
    public static final List<String> RELATIONAL_DB_BACKUP_PORT =
            Arrays.asList(DATABASE_PORT,STATIS_DATABASE_PORT,EDU_DATABASE_PORT,SURV_DATABASE_PORT,UAA_DATABASE_PORT,MATRIX_DATABASE_PORT);

    public static String getDbBackUpUsername(String dbType){
        return "OB".equals(dbType) ?  OB_DB_BACKUP_USERNAME : DB_BACKUP_USERNAME;
    }

    // 备份
    List<CompletableFuture<Void>> backup(BaseDatabaseBackupParam param);
    // 恢复
    List<CompletableFuture<Void>> restore(BaseDatabaseRestoreParam param);
}
