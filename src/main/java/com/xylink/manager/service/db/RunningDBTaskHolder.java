package com.xylink.manager.service.db;

import com.xylink.manager.model.em.DBType;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <pre>
 * 一个合理的调用序列:
 * if(attemptToBeginDBTask()){
 *     try{
 *         RunningDBTask runningDBTask = ...
 *         runningDBTaskHolder.setRunningDBTask(runningDBTask);
 *     }finally{
 *         cancelAttemptToBeginDBTask();
 *     }
 * }
 * </pre>
 *
 */
@Service
public class RunningDBTaskHolder {
    private static final ConcurrentHashMap<DBType, AtomicReference<RunningDBTask>> runningDBTasks = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<DBType, AtomicBoolean> tryToBeginDBTaskMarks = new ConcurrentHashMap<>();

    static {
        //初始化
        Arrays.stream(DBType.values()).forEach(type -> runningDBTasks.put(type, new AtomicReference<>(RunningDBTask.idle(type))));
        Arrays.stream(DBType.values()).forEach(type -> tryToBeginDBTaskMarks.put(type, new AtomicBoolean()));
    }

    public boolean hasRunningDBTask(DBType type){
        return !getRunningDBTask(type).isCompleted();
    }

    public void setRunningDBTask(RunningDBTask runningDBTask){
        this.runningDBTasks.get(runningDBTask.getDBType()).set(runningDBTask);
    }

    public RunningDBTask getRunningDBTask(DBType type){
        return runningDBTasks.get(type).get();
    }

    public boolean attemptToBeginDBTask(DBType type) {
        if(tryToBeginDBTaskMarks.get(type).compareAndSet(false,true)){
            if(!hasRunningDBTask(type)){
                return true;
            }else{
                tryToBeginDBTaskMarks.get(type).set(false);
            }
        }
        return false;
    }

    public void cancelAttemptToBeginDBTask(DBType type) {
        tryToBeginDBTaskMarks.get(type).set(false);
    }
}
