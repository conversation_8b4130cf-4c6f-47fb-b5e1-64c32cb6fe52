package com.xylink.manager.service.db.backup.base;


import java.lang.reflect.InvocationTargetException;

/**
 * <AUTHOR>
 * @since 2024/11/6 17:56 下午
 * 数据库备份类型枚举
 */
public class DatabaseStrategyFactory {

    public static DatabaseStrategy create(String name) {
        try {
            DatabaseType databaseType = DatabaseType.fromName(name);
            return databaseType.getStrategyClass().getConstructor(DatabaseType.class).newInstance(databaseType);
        } catch (InstantiationException | IllegalAccessException | NoSuchMethodException e) {
            throw new RuntimeException("无法实例化策略类: " + name, e);
        } catch (InvocationTargetException e) {
            throw new RuntimeException(e);
        }
    }
    public static DatabaseStrategy create(DatabaseType databaseType) {
        try {
            return databaseType.getStrategyClass().getConstructor(DatabaseType.class).newInstance(databaseType);
        } catch (InstantiationException | IllegalAccessException | NoSuchMethodException e) {
            throw new RuntimeException("无法实例化策略类: " + databaseType.getName(), e);
        } catch (InvocationTargetException e) {
            throw new RuntimeException(e);
        }
    }

}
