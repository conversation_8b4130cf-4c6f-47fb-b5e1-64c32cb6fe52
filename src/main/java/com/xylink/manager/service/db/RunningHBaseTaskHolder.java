package com.xylink.manager.service.db;

import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

@Service
public class RunningHBaseTaskHolder {
    private static final String HBASE_NAME = "hbase";
    private static final ConcurrentHashMap<String, AtomicReference<RunningHBaseTask>> runningDBTasks = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, AtomicBoolean> tryToBeginDBTaskMarks = new ConcurrentHashMap<>();

    static {
        //初始化
        runningDBTasks.put(HBASE_NAME, new AtomicReference<>(new RunningHBaseTask(1L)));
        tryToBeginDBTaskMarks.put(HBASE_NAME, new AtomicBoolean());
    }

    public boolean attemptToBeginHBaseTask() {
        if (tryToBeginDBTaskMarks.get(HBASE_NAME).compareAndSet(false, true)) {
            if (!hasRunningHBaseTask()) {
                return true;
            } else {
                tryToBeginDBTaskMarks.get(HBASE_NAME).set(false);
            }
        }
        return false;
    }

    public boolean hasRunningHBaseTask() {
        return !getRunningHBaseTask().isCompleted();
    }

    public void cancelAttemptToBeginHBaseTask() {
        tryToBeginDBTaskMarks.get(HBASE_NAME).set(false);
    }

    public void setRunningHBaseTask(RunningHBaseTask runningHBaseTask) {
        runningDBTasks.get(HBASE_NAME).set(runningHBaseTask);
    }

    public RunningHBaseTask getRunningHBaseTask() {
        return runningDBTasks.get(HBASE_NAME).get();
    }
}
