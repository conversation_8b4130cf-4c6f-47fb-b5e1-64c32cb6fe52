package com.xylink.manager.service.db;

import com.xylink.manager.model.deploy.Pod;
import com.xylink.util.HBaseBackupUtil;

import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicLong;

public class RunningEsTask {
    private static final ExecutorService executor = Executors.newFixedThreadPool(5);

    public final String uuid = UUID.randomUUID().toString();
    public final AtomicLong startTimestamp = new AtomicLong();

    public RunningEsTask() {
    }

    public RunningEsTask(Long time) {
        super();
        startTimestamp.set(time);
    }

    public void newRestoreRun(Pod pod, String fileName) {
        startTimestamp.set(System.currentTimeMillis());
        executor.execute(() -> HBaseBackupUtil.newEsRestoreUtil(pod, fileName));
    }

    public void newBackupRun(Pod pod, String fileName, String masterIp) {
        startTimestamp.set(System.currentTimeMillis());
        executor.execute(() -> HBaseBackupUtil.newEsBackUpUtil(pod, fileName, masterIp));
    }

    public String getTaskId() {
        return uuid;
    }

    public boolean isCompleted() {
        return System.currentTimeMillis() - startTimestamp.get() > 2 * 60 * 1000;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("RunningEsTask {");
        sb.append("startTimestamp = ").append(startTimestamp);
        sb.append('}');
        return sb.toString();
    }


}
