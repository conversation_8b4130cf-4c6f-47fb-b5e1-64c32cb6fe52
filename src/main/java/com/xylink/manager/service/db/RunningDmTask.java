package com.xylink.manager.service.db;

import com.xylink.util.DmBackupAndRestoreUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicLong;

public class RunningDmTask {
    private static final ExecutorService executor = Executors.newFixedThreadPool(5);

    public final String uuid = UUID.randomUUID().toString();
    public final AtomicLong startTimestamp = new AtomicLong();

    public RunningDmTask() {
    }

    public RunningDmTask(Long time) {
        super();
        startTimestamp.set(time);
    }

    public void restoreRun(String ip, String mainIp, RestTemplate restTemplate, String fileName) {
        startTimestamp.set(System.currentTimeMillis());
        DmBackupAndRestoreUtil.dmRestoreUtil(ip, mainIp, restTemplate, fileName);
    }

    public void backupRun(String dmIp, String mainIp, String fileName, RestTemplate restTemplate) {
        startTimestamp.set(System.currentTimeMillis());
        DmBackupAndRestoreUtil.dmBackUpUtil(dmIp, mainIp, fileName, restTemplate);
    }

    public String getTaskId() {
        return uuid;
    }

    public List<Pair<Integer, String>> getCachedErrorOut() {
        return Collections.emptyList();
    }

    public boolean isCompleted() {
        return System.currentTimeMillis() - startTimestamp.get() > 2 * 60 * 1000;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("RunningDmTask {");
        sb.append("startTimestamp = ").append(startTimestamp);
        sb.append('}');
        return sb.toString();
    }


}
