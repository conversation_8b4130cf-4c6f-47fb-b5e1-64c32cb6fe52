package com.xylink.manager.service.db;

import com.xylink.manager.model.em.DBType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class DBTask {
    private String type;
    private String time;
    private String fileName;
    private boolean running;

    public DBTask(String type,String time) {
        this.type = type;
        this.time = time;
        this.running = true;
        if (DBType.hbase.name().equals(type)) {
            this.fileName = type + "_backup_" + time + ".tar.gz";
        } else if (DBType.es.name().equals(type)) {
            this.fileName = type + "_backup_" + time + ".tar.gz";
        } else if (DBType.redis.name().equals(type)) {
            this.fileName = type + "_backup_" + time + ".aof";
        } else if (DBType.etcd.name().equals(type)) {
            this.fileName = type + "_backup_" + time + ".db";
        }else {
            this.fileName = type + "_backup_" + time + ".sql";
        }
    }

    public DBTask(String type,String time,String dbType) {
        this.type = type;
        this.time = time;
        this.running = true;
        if (DBType.hbase.name().equals(type)) {
            this.fileName = type + "_backup_" + time + ".tar.gz";
        } else if (DBType.es.name().equals(type)) {
            this.fileName = type + "_backup_" + time + ".tar.gz";
        } else if (DBType.redis.name().equals(type)) {
            this.fileName = type + "_backup_" + time + ".aof";
        } else if (DBType.etcd.name().equals(type)) {
            this.fileName = type + "_backup_" + time + ".db";
        }else {
            String suffix = ".sql";
            if ("ST".equals(dbType)) {
                suffix = ".osrbk";
            } else if ("DM".equals(dbType)) {
                suffix = ".dmp";
            }
            this.fileName = type + "_backup_" + time + suffix;
        }
    }

}
