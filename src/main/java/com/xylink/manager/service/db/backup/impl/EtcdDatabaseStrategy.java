package com.xylink.manager.service.db.backup.impl;

import com.xylink.manager.service.db.backup.base.DatabaseType;
import com.xylink.manager.service.db.backup.base.param.BaseDatabaseBackupParam;
import com.xylink.manager.service.db.backup.base.param.BaseDatabaseRestoreParam;
import com.xylink.manager.service.db.backup.base.param.NoSQLDBBackupParam;
import com.xylink.manager.service.db.backup.base.param.NoSQLDBRestoreParam;
import com.xylink.manager.service.db.etcd.EtcdClusterBackUpAndRestoreService;
import com.xylink.util.SpringBeanUtil;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @since 2024/11/6 17:56 下午
 * ETCD数据库操作类
 */
public class EtcdDatabaseStrategy extends AbstractDatabaseStrategy {

    public EtcdDatabaseStrategy(DatabaseType databaseType) {
        super(databaseType);
    }

    @Override
    public List<CompletableFuture<Void>> backup(BaseDatabaseBackupParam param) {
        NoSQLDBBackupParam noSQLDBBackupParam = (NoSQLDBBackupParam)param;
        SpringBeanUtil.getBean(EtcdClusterBackUpAndRestoreService.class).backup(noSQLDBBackupParam.getFileName());
        return null;
    }

    @Override
    public List<CompletableFuture<Void>> restore(BaseDatabaseRestoreParam param) {
        NoSQLDBRestoreParam rparam = (NoSQLDBRestoreParam)param;
        SpringBeanUtil.getBean(EtcdClusterBackUpAndRestoreService.class).restore(rparam.getFileName());
        return null;
    }
}
