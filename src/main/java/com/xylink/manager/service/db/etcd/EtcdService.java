package com.xylink.manager.service.db.etcd;

import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.WebException;
import com.xylink.manager.service.db.HasRunningTaskException;
import com.xylink.manager.service.db.RunningEtcdTask;
import com.xylink.manager.service.db.RunningEtcdTaskHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
public class EtcdService {
    private static final Logger logger = LoggerFactory.getLogger(EtcdService.class);

    @Autowired
    private RunningEtcdTaskHolder runningEtcdTaskHolder;
    @Autowired
    private RestTemplate restTemplate;

    public RunningEtcdTask backup() {
        if (runningEtcdTaskHolder.attemptToBeginEtcdTask()) {
            try {
                RunningEtcdTask runningEtcdTask = backupEtcd();
                runningEtcdTaskHolder.setRunningEtcdTask(runningEtcdTask);
                return runningEtcdTask;
            } finally {
                runningEtcdTaskHolder.cancelAttemptToBeginEtcdTask();
            }
        } else {
            logger.warn("There is another etcd task is running...");
            throw new HasRunningTaskException(null);
        }
    }

    public RunningEtcdTask getCurrentRunningEtcdTask() {
        return runningEtcdTaskHolder.getRunningEtcdTask();
    }

    public String restore(String restoreFileName) {
        if (!restoreFileName.endsWith(".db")) {
            throw new WebException(ErrorStatus.FILE_PATH_ILLEGAL);
        }

        if (runningEtcdTaskHolder.attemptToBeginEtcdTask()) {
            try {
                RunningEtcdTask runningEtcdTask = restoreEtcd(restoreFileName);
                runningEtcdTaskHolder.setRunningEtcdTask(runningEtcdTask);
                return runningEtcdTask.uuid;
            } finally {
                runningEtcdTaskHolder.cancelAttemptToBeginEtcdTask();
            }
        } else {
            logger.warn("There is another etcd task is running...");
            throw new HasRunningTaskException(null);
        }
    }

    private RunningEtcdTask backupEtcd() {
        RunningEtcdTask baseTask = new RunningEtcdTask();
        baseTask.backupRun(restTemplate);
        return baseTask;
    }

    private RunningEtcdTask restoreEtcd(String restoreFileName) {
        RunningEtcdTask baseTask = new RunningEtcdTask();
        baseTask.restoreRun(restTemplate, restoreFileName);
        return baseTask;
    }
}
