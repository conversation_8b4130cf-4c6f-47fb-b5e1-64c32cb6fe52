package com.xylink.manager.service.db;

import com.xylink.manager.model.em.DBType;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

/**
 * FIXME 我希望这是一个actor,有自己的logical thread.
 */
public class RunningDBTask {
    private Logger logger = LoggerFactory.getLogger(RunningDBTask.class);

    public enum TaskType{BACKUP,RESTORE,UPGRADE,IDLE,;}

//    public final RunningDBTask IDLE = new RunningDBTask(TaskType.IDLE,new String[]{},null){
//        public boolean isCompleted(){return true;}
//        public void run(){return;}
//        public void run(String workDir){return ;}
//        public List<Pair<Integer,String>> getCachedStandardOut(){ return Collections.emptyList();}
//        public List<Pair<Integer,String>> getCachedErrorOut(){ return Collections.emptyList();}
//        public List<Pair<Integer,String>> clearCachedStandardOutBefore(int lineNo){ return Collections.emptyList();}
//        public List<Pair<Integer,String>> clearCachedErrorOutBefore(int lineNo){ return Collections.emptyList();}
//
//    } ;

    public static RunningDBTask idle(DBType type) {
        return  new RunningDBTask(TaskType.IDLE,new String[]{},type){
            public boolean isCompleted(){return true;}
            public void run(){return;}
            public void run(String workDir){return ;}
            public List<Pair<Integer,String>> getCachedStandardOut(){ return Collections.emptyList();}
            public List<Pair<Integer,String>> getCachedErrorOut(){ return Collections.emptyList();}
            public List<Pair<Integer,String>> clearCachedStandardOutBefore(int lineNo){ return Collections.emptyList();}
            public List<Pair<Integer,String>> clearCachedErrorOutBefore(int lineNo){ return Collections.emptyList();}

        } ;
    }

    private final ExecutorService executor = Executors.newFixedThreadPool(5);

    private final AtomicReference<Stream> standard = new AtomicReference<>();
    private final AtomicReference<Stream> error = new AtomicReference<>();

    private final String[] commands ;
    private final DBType type ;

    private static List<Pair<Integer,String>> getCachedLines(AtomicReference<Stream> streamReference){
        Stream stream = streamReference.get();
        if(stream == null)
            throw new IllegalStateException("no stream");

        return stream.getCachedLines();
    }

    private static List<Pair<Integer,String>> clearCachedLinesBefore(AtomicReference<Stream> streamReference,int lineNo){
        Stream stream = streamReference.get();
        if(stream == null)
            throw new IllegalStateException("no stream");

        return stream.clearCachedLinesBefore(lineNo);
    }

    private final AtomicLong endTimestamp = new AtomicLong();

    private final AtomicBoolean ran = new AtomicBoolean();

    public final TaskType taskType;
    public final AtomicLong startTimestamp = new AtomicLong();
    public final String uuid = UUID.randomUUID().toString();

    public RunningDBTask(TaskType taskType,String[] commands,DBType type) {
        this.taskType = taskType;
        this.commands = commands;
        this.type = type;
    }

    public DBType getDBType() {
        return type;
    }


    public TaskType getTaskType() {
        return taskType;
    }

    public boolean isCompleted(){
        Stream s = standard.get();
        Stream e = error.get();

        if(s == null || e == null){
            throw new IllegalStateException("No stream for standard or error");
        }

        return s.completed() && e.completed();

    }

    public String getTaskId() {
        return uuid;
    }

    public void run() throws IOException{
        run(null,null);
    }

    public void run(String workDir, final File inputFile) throws IOException{

        if(!ran.compareAndSet(false,true)){
            throw new IllegalStateException(this+" has ran");
        }

        startTimestamp.set(System.currentTimeMillis());

        ProcessBuilder pb = new ProcessBuilder(commands);

        if(workDir != null)
            pb.directory(new File(workDir));

        if(inputFile != null) {
            pb.redirectInput(inputFile);
        }

        final Process p = pb.start();

        standard.set(new Stream(p.getInputStream(), true));
        error.set(new Stream(p.getErrorStream()));

        standard.get().startCachingLines(executor, false);
        error.get().startCachingLines(executor, true);

        executor.execute(() -> {
            try {
                while (true) {
                    if (standard.get().completed() && error.get().completed()) {
                        endTimestamp.set(System.currentTimeMillis());

                        standard.get().closeIgnoreException();
                        error.get().closeIgnoreException();
                        logger.info("task is over, cast {} s!",(endTimestamp.get()-startTimestamp.get())/1000 );
                        break;
                    }
                    Thread.sleep(1000);
                }
            }catch(Exception e){
                logger.error("ERROR:",e);
                standard.get().closeIgnoreException();
                error.get().closeIgnoreException();
            }
        });
    }

    public List<Pair<Integer,String>> getCachedStandardOut(){
        return getCachedLines(standard);
    }

    public List<Pair<Integer,String>> getCachedErrorOut(){
        return getCachedLines(error);
    }

    public List<Pair<Integer,String>> clearCachedStandardOutBefore(int lineNo){
        return clearCachedLinesBefore(standard,lineNo);
    }

    public List<Pair<Integer,String>> clearCachedErrorOutBefore(int lineNo){
        return clearCachedLinesBefore(error,lineNo);
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("RunningDBTask{");
        sb.append("taskType=").append(taskType);
        sb.append(", startTimestamp=").append(startTimestamp);
        sb.append('}');
        return sb.toString();
    }
}

/**
 * isCompleted()返回true后,getAndRemoveCachedLines必然返回所有lines.
 */
class Stream implements Closeable{
    private Logger logger = LoggerFactory.getLogger(RunningDBTask.class);

    private final InputStream inputStream;

    private final AtomicBoolean completed = new AtomicBoolean();

    private final AtomicReference<List<Pair<Integer,String>>> cachedLines = new AtomicReference<>();

    private final Boolean simpleLog;

    {
        cachedLines.set(Collections.<Pair<Integer,String>>emptyList());
    }

    private void appendLineToCache(int lineNo,String line, boolean isPrintOut) {
        while(true) {
            if(isPrintOut) {
                logger.info(lineNo+":) "+line);
            }
            List<Pair<Integer,String>> previous = cachedLines.get();

            List<Pair<Integer,String>> current = new ArrayList<>(previous);

            current.add(Pair.of(lineNo,line));

            if(cachedLines.compareAndSet(previous,current))
                break;
        }
    }

    public Stream(InputStream inputStream) {
        if(inputStream == null)
            throw new IllegalArgumentException("InputStream must not be null");
        this.inputStream = inputStream;
        this.simpleLog = false;
    }

    public Stream(InputStream inputStream, boolean simpleLog) {
        if(inputStream == null)
            throw new IllegalArgumentException("InputStream must not be null");
        this.inputStream = inputStream;
        this.simpleLog = simpleLog;
    }

    public boolean completed(){
        return completed.get();
    }

    public void startCachingLines(ExecutorService  executorService, boolean isPrintOut){
        executorService.execute(() -> {
            int lineNo = 1;
            try(BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))){
                String line;
                while((line = reader.readLine()) != null){
                    appendLineToCache(lineNo, simpleLog ? line.substring(0, line.length() >= 66 ? 66 : line.length()) + "..." : line, isPrintOut);
                    lineNo ++;
                }
            } catch (Exception e) {
                logger.error("ERROR: ",e);
            }finally {
                completed.set(true);
            }
        }) ;
    }

    public List<Pair<Integer,String>> getCachedLines(){
        return cachedLines.get();
    }

    public List<Pair<Integer,String>> clearCachedLinesBefore(int lineNo){
        while(true){
            List<Pair<Integer,String>> cached = cachedLines.get();

            List<Pair<Integer,String>> removed = new ArrayList<>(cached);

            Iterator<Pair<Integer,String>> it = removed.iterator();
            while(it.hasNext()){
                Pair<Integer,String> p = it.next();
                if(p.getLeft() >= lineNo){
                    it.remove();
                }
            }

            List<Pair<Integer,String>> reservered = new ArrayList<>(cached);
            it = reservered.iterator();

            while(it.hasNext()){
                Pair<Integer,String> p = it.next();
                if(p.getLeft() < lineNo){
                    it.remove();
                }
            }

            if(cachedLines.compareAndSet(cached,reservered)){
                return removed;
            }
        }
    }

    @Override
    public void close() throws IOException {
        inputStream.close();
    }

    public void closeIgnoreException(){
        try{
            close();
        }catch (IOException ignore){

        }
    }
}
