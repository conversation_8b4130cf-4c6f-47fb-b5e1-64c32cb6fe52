package com.xylink.manager.service.db;

import com.xylink.config.db.DBUpgradeConfig;
import com.xylink.manager.model.em.DBType;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class DbUpgradeService {
    private final Logger logger = LoggerFactory.getLogger(DbUpgradeService.class);
    @Value("${db.upgrade.image}")
    private String dbUpgradeImage;

    @Value("${db.upgradecommand.host.prop.path}")
    private String dbUpgradeHostPropPath;

    @Value("${db.upgradecommand.prop.path}")
    private String dbUpgradePropPath;

    @Value("${db.upgradecommand.host.workdir}")
    private String dbUpgraeHostWorkdir;

    @Value("${base.dir}")
    private String baseDir;


    @Autowired
    private DBUpgradeConfig dbUpgradeConfig;

    public RunningDBTask upgrade(DBType type){
        String[] commands =constructDockerUpgradeCommand();
        logger.debug("To upgrade db using "+Arrays.toString(commands));

        try {
            RunningDBTask runningDBTask = new RunningDBTask(RunningDBTask.TaskType.UPGRADE,commands,type);

            runningDBTask.run();

            return runningDBTask;
        }catch(IOException unexpected){
            logger.info(ExceptionUtils.getStackTrace(unexpected));
            throw new UnknownException("Unexpected exception for "+ Arrays.toString(commands),unexpected);
        }
    }

    public void addUpgradeSqlFiles(File tempUpgradeSqlDir){

        try{
            //FileUtils.copyDirectory(tempUpgradeSqlDir,new File(dbUpgradeConfig.getUpgradeSqlDir()));
            logger.info("Copy tmp dir: " + tempUpgradeSqlDir + ", to: " + dbUpgradeConfig.getUpgradeHostSqlDir());
            FileUtils.copyDirectory(tempUpgradeSqlDir,new File(dbUpgradeConfig.getUpgradeHostSqlDir()));
            logger.info("tempUpgradeSqlDir: " + tempUpgradeSqlDir);
        }catch (IOException unexpected) {
            throw new UnknownException("Unexpected exception for copy upgrade sql files from "+ tempUpgradeSqlDir+" to "+dbUpgradeConfig.getUpgradeSqlDir(),unexpected);
        }
    }

    public String[] constructDockerUpgradeCommand(){
        String[] originalCommands = new String[]{
                dbUpgradeConfig.getDbUpgradeCommand(),
                dbUpgradeConfig.getDbUpgradeCommandArg()

        };

        List<String> dockerCommands =  new ArrayList<>();
        dockerCommands.add("docker");
        dockerCommands.add("run");
        dockerCommands.add("--rm");
        dockerCommands.add("-v");
        dockerCommands.add(dbUpgradeHostPropPath+":"+dbUpgradePropPath+":z");
        dockerCommands.add("-v");
        dockerCommands.add(dbUpgraeHostWorkdir+":"+dbUpgradeConfig.getDbUpgradeCommandWorkDir()+":z");
        dockerCommands.add("-v");
        dockerCommands.add(""+baseDir+"/private_manager/script/:"+baseDir+"/private_manager/script/:z");

        dockerCommands.add("--net=host");
        dockerCommands.add(dbUpgradeImage);

        dockerCommands.add(""+baseDir+"/private_manager/script/mysqlupgrade.sh");
        //dockerCommands.addAll(Arrays.asList(originalCommands));

        String[] dockerCommandsArray = dockerCommands.toArray(new String[0]);

        return dockerCommandsArray;
    }
}
