package com.xylink.manager.service.db;

import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

@Service
public class RunningEsTaskHolder {
    private static final String ES_NAME = "es";
    private static final ConcurrentHashMap<String, AtomicReference<RunningEsTask>> runningDBTasks = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, AtomicBoolean> tryToBeginDBTaskMarks = new ConcurrentHashMap<>();

    static {
        //初始化
        runningDBTasks.put(ES_NAME, new AtomicReference<>(new RunningEsTask(1L)));
        tryToBeginDBTaskMarks.put(ES_NAME, new AtomicBoolean());
    }

    public boolean attemptToBeginEsTask() {
        if (tryToBeginDBTaskMarks.get(ES_NAME).compareAndSet(false, true)) {
            if (!hasRunningEsTask()) {
                return true;
            } else {
                tryToBeginDBTaskMarks.get(ES_NAME).set(false);
            }
        }
        return false;
    }

    public boolean hasRunningEsTask() {
        return !getRunningEsTask().isCompleted();
    }

    public void cancelAttemptToBeginEsTask() {
        tryToBeginDBTaskMarks.get(ES_NAME).set(false);
    }

    public void setRunningEsTask(RunningEsTask runningEsTask) {
        runningDBTasks.get(ES_NAME).set(runningEsTask);
    }

    public RunningEsTask getRunningEsTask() {
        return runningDBTasks.get(ES_NAME).get();
    }
}
