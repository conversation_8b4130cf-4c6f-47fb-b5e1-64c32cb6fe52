package com.xylink.manager.service.db;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.db.DbBackUpAndRestoreConfig;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.config.exception.basic.WebException;
import com.xylink.manager.model.deploy.DaemonSet;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.model.em.DBType;
import com.xylink.manager.service.ServerListService;
import com.xylink.manager.service.ServerNetworkService;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.util.DbConfigUtil;
import com.xylink.util.DockerImagesUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Service
public class DBBackupService {
    private final Logger logger = LoggerFactory.getLogger(DBBackupService.class);

    @Value("${db.backupcommand}")
    private String dbBackupcommand;

    @Value("${db.restorecommand}")
    private String dbRestorecommand;

    @Value("${db.databasenames}")
    private String dbDatabasenames;

    @Value("${db.statis.databasenames}")
    private String statisDbDatabasenames;

    @Value("${db.surv.databasenames}")
    private String survDbDatabasenames;

    @Value("${db.edu.databasenames}")
    private String eduDbDatabasenames;

    @Value("${db.webrtc.databasenames}")
    private String webrtcDbDatabasenames;

    @Value("${db.matrix.databasenames}")
    private String matrixDbDatabasenames;

    @Value("${db.backupdir}")
    private String dbBackupdir;

    @Value("${db.host.backupdir}")
    private String dbHostBackupdir;

    @Value("${db.host.statis.backupdir}")
    private String statisdDbHostBackupdir;

    @Value("${db.host.surv.backupdir}")
    private String survDbHostBackupdir;

    @Value("${db.host.edu.backupdir}")
    private String eduDbHostBackupdir;

    @Value("${db.host.webrtc.backupdir}")
    private String webrtcDbHostBackupdir;

    @Value("${db.host.matrix.backupdir}")
    private String matrixDbHostBackupdir;

    @Value("${db.host.nightingale.backupdir}")
    private String nightingaleDbHostBackupdir;

    @Value("${db.host.hbase.backupdir}")
    private String hbaseHostBackupdir;

    @Value("${base.dir}")
    private String baseDir;

    @Autowired
    private K8sService k8sService;


    @Autowired
    private ServerNetworkService serverNetworkService;
    @Autowired
    private ServerListService serverListService;
    @Autowired
    private DbConfigUtil dbConfigUtil;
    @Autowired
    private IDeployService deployService;
    @Autowired
    private DbBackUpAndRestoreConfig dbBackUpAndRestoreConfig;

    @PostConstruct
    public void init() {
        File backupDir = new File(dbHostBackupdir);
        if (!backupDir.exists() || !backupDir.isDirectory()) {
            logger.info("directory: {} not exist, mkdir.", dbHostBackupdir);
            backupDir.mkdir();
        }

        if (!dbHostBackupdir.endsWith(System.getProperty("file.separator"))) {
            logger.info("append [{}] with: {}", dbHostBackupdir, System.getProperty("file.separator"));
            dbHostBackupdir = dbHostBackupdir + System.getProperty("file.separator");
        }
    }

    private String getDBBackupRestoreImage() {
        String dbBackupRestoreImage = "hub.xylink.com:5000/private_cloud/mysql:5.7.23";
        try {
            DaemonSet mysqlDs = deployService.listDaemonSetByAppLabel("private-mysql").get(0);
            dbBackupRestoreImage = mysqlDs.getContainers().get(0).getImage();
            logger.info("mysql image: {}", dbBackupRestoreImage);

        } catch (Exception e) {
            logger.info(ExceptionUtils.getStackTrace(e));
        }

        return dbBackupRestoreImage;
    }

    private String getDBBackupRestoreImage(String dbType) {
        DockerImagesUtils.ImagesEnum imagesEnum = EnumUtils.getEnum(DockerImagesUtils.ImagesEnum.class, dbType);
        return DockerImagesUtils.getImages(imagesEnum);
    }

    public String getObServerIp() {
        return serverNetworkService.getNetworkConfiguration().get(NetworkConstants.DATABASE_IP);
    }

    public String getObServerPort() {
        return serverNetworkService.getNetworkConfiguration().get(NetworkConstants.DATABASE_PORT);
    }

    public String getServerIp(String type, String db) {
        if (DBType.main.name().equalsIgnoreCase(type)) {
            if (db.equals("master")) {
                return serverNetworkService.getNetworkConfiguration().get(NetworkConstants.DATABASE_IP);
            } else {
                List<Pod> pods = serverListService.getPods("private-mysqlslave");
                return pods.stream().map(Pod::getIp).findFirst().orElseThrow(() -> new ServerException("未发现从库!"));
            }

        } else if (DBType.surv.name().equalsIgnoreCase(type)) {
            return serverNetworkService.getNetworkConfiguration().get(NetworkConstants.SURV_INTERNAL_IP);
        } else if (DBType.statis.name().equalsIgnoreCase(type)) {
            return serverNetworkService.getNetworkConfiguration().get(NetworkConstants.STATIS_DATABASE_IP);
        } else if (DBType.edu.name().equalsIgnoreCase(type)) {
            return serverNetworkService.getEduDatabaseIpNetworkConfiguration();
        } else if (DBType.hbase.name().equalsIgnoreCase(type)) {
            return serverNetworkService.getNetworkConfiguration().get("HBASE_IP");
        } else if (DBType.webrtc.name().equalsIgnoreCase(type)) {
            return serverNetworkService.getNetworkConfiguration().get(NetworkConstants.UAA_DATABASE_IP);
        } else if (DBType.matrix.name().equalsIgnoreCase(type)) {
            return serverNetworkService.getNetworkConfiguration().get(NetworkConstants.MATRIX_DATABASE_IP);
        } else if (DBType.nightingale.name().equalsIgnoreCase(type)) {
            return serverNetworkService.getNetworkConfiguration().get(NetworkConstants.NIGHTINGALE_DATABASE_IP);
        } else {
            throw new ServerException();
        }
    }

    public String getServerPort(String type) {
        Map<String, String> allIp = dbConfigUtil.getDbConfigMap();
        if (DBType.nightingale.name().equalsIgnoreCase(type)) {
            return allIp.get(NetworkConstants.NIGHTINGALE_DATABASE_PORT);
        }
        return allIp.get(NetworkConstants.DATABASE_PORT);
    }

    private String[] getDbnames(String type) {
        if (DBType.main.name().equalsIgnoreCase(type)) {
            return dbDatabasenames.split(",");
        } else if (DBType.surv.name().equalsIgnoreCase(type)) {
            return survDbDatabasenames.split(",");
        } else if (DBType.statis.name().equalsIgnoreCase(type)) {
            return statisDbDatabasenames.split(",");
        } else if (DBType.edu.name().equalsIgnoreCase(type)) {
            return eduDbDatabasenames.split(",");
        } else if (DBType.webrtc.name().equalsIgnoreCase(type)) {
            return webrtcDbDatabasenames.split(",");
        } else if (DBType.matrix.name().equalsIgnoreCase(type)) {
            return matrixDbDatabasenames.split(",");
        } else {
            throw new ServerException();
        }
    }

    private String[] constructBackupCommand(String type, String db, String name, boolean isBackupAll, String dbType) {
        List<String> commands = new ArrayList<>();
        if ("OB".equalsIgnoreCase(dbType)) {
            String hostDir = getHostBackupDir(type, name, isBackupAll);
            LocalDate today = LocalDate.now(ZoneId.of(Constants.ZONEID));
            String backupFileName = isBackupAll ?
                    (type + "_backup_" + name + ".sql") :
                    (type + "-dbbackup-" + today + "-" + System.currentTimeMillis() + ".sql");
            Path path = Paths.get("/tmp/", backupFileName);
            if (k8sService.isContainerD()) {
                commands.add(baseDir + "/private_manager/script/oceanbasebackup_ctrd.sh");
            } else {
                commands.add(baseDir + "/private_manager/script/oceanbasebackup.sh");
            }
            commands.add(getObServerIp());
            commands.add(getObServerPort());
            commands.add(hostDir);
            commands.add("/tmp/");
            commands.add(path.normalize().toAbsolutePath().toFile().getPath());
            commands.add("dbbak@tenant");
            commands.add("U1O5ZeRyLFd#u9T6TF9h");
        } else {
            commands.add(dbBackupcommand);
            commands.add("--add-drop-database");
            commands.add("-h" + getServerIp(type, db));
            commands.add("-P" + getServerPort(type));
            commands.add("-udbbak");
            commands.add("-pU1O5ZeRyLFd#u9T6TF9h");
            commands.add("-c");
            commands.add("--all-databases");
            commands.add("--set-gtid-purged=off");
            commands.add("--single-transaction");
            commands.add("--master-data=2");
            commonCommands(type, name, isBackupAll, commands);
        }

        return commands.toArray(new String[0]);
    }

    private void commonCommands(String type, String name, boolean isBackupAll, List<String> commands) {
        LocalDate today = LocalDate.now(ZoneId.of(Constants.ZONEID));

        String backupFileName = isBackupAll ?
                (type + "_backup_" + name + ".sql") :
                (type + "-dbbackup-" + today + "-" + System.currentTimeMillis() + ".sql");

        Path path = Paths.get(dbBackupdir, backupFileName);

        commands.add("-r");
        commands.add(path.normalize().toAbsolutePath().toFile().getPath());
    }

    public String[] constructDockerBackupCommand(String type, String db, String name, boolean isBackupAll, String dbType) {
        String[] originalCommands = constructBackupCommand(type, db, name, isBackupAll, dbType);

        List<String> dockerCommands = new ArrayList<>();
        if (!"OB".equalsIgnoreCase(dbType)) {
            String hostDir = getHostBackupDir(type, name, isBackupAll);

            dockerCommands.add("docker");
            dockerCommands.add("run");
            dockerCommands.add("--rm");
            dockerCommands.add("-v");
            dockerCommands.add(hostDir + ":" + dbBackupdir + ":z");
            dockerCommands.add("--net=host");
            dockerCommands.add(getDBBackupRestoreImage(dbType));
        }

        dockerCommands.addAll(Arrays.asList(originalCommands));

        return dockerCommands.toArray(new String[0]);
    }

    private String[] constructDockerRestoreCommand(String fullPath, String type) {
        return constructDockerRestoreCommand(fullPath, type, null, false);
    }

    public String[] constructDockerRestoreCommand(String path, String type, String time, boolean notSingle) {
        Map<String, String> allIp = dbConfigUtil.getDbConfigMap();
        String hostDir = getHostBackupDir(type, time, notSingle);
        String dbType = allIp.getOrDefault(NetworkConstants.DATABASE_TYPE, "MYSQL");
        List<String> dockerCommands = new ArrayList<>();
        if ("MYSQL".equals(dbType)) {
            dockerCommands.add("docker");
            dockerCommands.add("run");
            dockerCommands.add("--rm");
            dockerCommands.add("-v");
            dockerCommands.add(hostDir + ":" + dbBackUpAndRestoreConfig.getDbBackupdir() + ":z");
            dockerCommands.add("-v");
            dockerCommands.add("" + baseDir + "/private_manager/script/:" + baseDir + "/private_manager/script/:z");
            dockerCommands.add("--net=host");
            dockerCommands.add(getDBBackupRestoreImage(dbType));

            dockerCommands.add("" + baseDir + "/private_manager/script/mysqlrestore.sh");
            dockerCommands.add(getServerIp(type, "master"));
            dockerCommands.add(getServerPort(type));
            dockerCommands.add("dbbak");
            dockerCommands.add("U1O5ZeRyLFd#u9T6TF9h");
            dockerCommands.add(path);
        } else if ("ST".equals(dbType)) {
            dockerCommands.add("docker");
            dockerCommands.add("run");
            dockerCommands.add("--rm");
            dockerCommands.add("-v");
            dockerCommands.add(hostDir + ":" + dbBackUpAndRestoreConfig.getDbSTBackupdir() + ":z");
            dockerCommands.add("--net=host");
            dockerCommands.add(getDBBackupRestoreImage(dbType));

            dockerCommands.add("opt/ShenTong/bin/brcmd -O restore -d OSRDB");
            dockerCommands.add("-h " + getServerIp(type, "master"));
            dockerCommands.add("-u dbbak");
            dockerCommands.add("-p U1O5ZeRyLFd#u9T6TF9h");
            dockerCommands.add("-P " + allIp.get(NetworkConstants.DATABASE_PORT));
            dockerCommands.add("-t backup_point_last -k file -f " + path);
            dockerCommands.add("-b 4096");
        } else if ("OB".equals(dbType)) {
            if (k8sService.isContainerD()) {
                dockerCommands.add(baseDir + "/private_manager/script/oceanbaserestore_ctrd.sh");
            } else {
                dockerCommands.add(baseDir + "/private_manager/script/oceanbaserestore.sh");
            }
            dockerCommands.add(getObServerIp());
            dockerCommands.add(getObServerPort());
            dockerCommands.add(hostDir);
            dockerCommands.add("/tmp/");
            dockerCommands.add(path.replace("/usr/libra/mysqlbackup/", "/tmp/"));
            dockerCommands.add(baseDir + "/private_manager/script");
            dockerCommands.add("dbbak@tenant");
            dockerCommands.add("U1O5ZeRyLFd#u9T6TF9h");
        }
        return dockerCommands.toArray(new String[0]);
    }
// /opt/ShenTong/bin/brcmd -O restore -d OSRDB  -u dbbak -p U1O5ZeRyLFd#u9T6TF9h -P 2003 -t backup_point_last -k file -f "+baseDir+"/db/stbackup/$2 -b 4096
//    docker run --rm -v "+baseDir+"/private_manager/script/:"+baseDir+"/private_manager/script/:z -v "+baseDir+"/db/mysqlbackup:/usr/libra/mysqlbackup/:z --net=host hub.xylink.com:5000/private_cloud/mysql "+baseDir+"/private_manager/script/mysqlrestore.sh ************* dbbackup-2018-02-03-1517646746345.sql

    private File validateBackupFile(String fullPath, String type) {
        Path path = Paths.get(fullPath).normalize();

        Path backupDir = Paths.get(getHostBackupDir(type, null, false)).normalize();

        if (!path.startsWith(backupDir)) {
            throw new NotBackupFileException(fullPath + " is not a file under the directory " + dbBackupdir);
        }

        File file = path.toFile();
        if (file.isDirectory() || !file.exists()) {
            throw new NotBackupFileException(fullPath + " is not a file under the directory " + dbBackupdir);
        }

        return file;
    }

    /**
     * @return 备份进程的命令, 标准输出, 错误输出.
     */
    public RunningDBTask backup(String type, String db) {
        String[] commands = constructDockerBackupCommand(type, db, null, false, null);

        logger.info("Backup db ： {} db", type);
        logger.info("Backup db using {}", Arrays.toString(commands));

        try {
            RunningDBTask runningDBTask = new RunningDBTask(RunningDBTask.TaskType.BACKUP, commands, DBType.valueOf(type));

            runningDBTask.run();

            return runningDBTask;

        } catch (Exception unexpected) {
            logger.error("Cannot backup db", unexpected);
            throw new UnknownException("Unexpected exception for " + Arrays.toString(commands), unexpected);
        }
    }

    public String getHostBackupDir(String type, String time, boolean isBackupAll) {
        if (isBackupAll) {
            if ("ETCD".equalsIgnoreCase(type)) {
                return baseDir + "/db/etcdbackup/";
            } else {
                String path = baseDir + "/db/all/" + time;
                File file = new File(path);
                if (!file.exists()) file.mkdirs();
                return path + File.separator;
            }
        } else {
            if (DBType.main.name().equalsIgnoreCase(type)) {
                return dbHostBackupdir;
            } else if (DBType.surv.name().equalsIgnoreCase(type)) {
                return survDbHostBackupdir;
            } else if (DBType.statis.name().equalsIgnoreCase(type)) {
                return statisdDbHostBackupdir;
            } else if (DBType.hbase.name().equalsIgnoreCase(type)) {
                return hbaseHostBackupdir;
            } else if (DBType.edu.name().equalsIgnoreCase(type)) {
                return eduDbHostBackupdir;
            } else if (DBType.webrtc.name().equalsIgnoreCase(type)) {
                return webrtcDbHostBackupdir;
            } else if (DBType.matrix.name().equalsIgnoreCase(type)) {
                return matrixDbHostBackupdir;
            } else if (DBType.nightingale.name().equalsIgnoreCase(type)) {
                return nightingaleDbHostBackupdir;
            } else if ("ETCD".equalsIgnoreCase(type)) {
                return baseDir + "/db/etcdbackup/";
            } else {
                throw new WebException(ErrorStatus.PARAM_ERROR);
            }
        }
    }

    public File[] listBackupFiles(String type) {

        String dir = getHostBackupDir(type, null, false);

        Path path = Paths.get(dir);
        File file = path.toFile();
        if (!file.isDirectory()) {
            try {
                Files.createDirectories(path);
            } catch (IOException e) {
                throw new NotDirectoryException(dir);
            }
        }

        File[] backupFiles = file.listFiles(pathname -> !pathname.isDirectory());

        Arrays.sort(backupFiles, (a, b) -> a.lastModified() > b.lastModified() ? -1 : 1);

        return backupFiles;
    }

    public void deleteBackupFile(String fullPath, String type) {
        File file = validateBackupFile(fullPath, type);
        file.delete();
    }

    public void deleteBackupDir() {
        Path backupDir = Paths.get(dbHostBackupdir).normalize();
        try {
            FileUtils.deleteDirectory(backupDir.toFile());
        } catch (IOException e) {
            throw new DBException(e);
        }
    }

    public String getDBBackupDir() {
        return dbBackupdir;
    }

    /**
     * @param fullPath
     * @return 恢复进程的命令, 标准输出, 错误输出.
     */
    public RunningDBTask restore(String fullPath, String type) {

        String[] commands = constructDockerRestoreCommand(fullPath, type);

        logger.info("To restore db using {}", Arrays.toString(commands));

        try {
            RunningDBTask runningDBTask = new RunningDBTask(RunningDBTask.TaskType.RESTORE, commands, DBType.valueOf(type));

            runningDBTask.run();

            return runningDBTask;

        } catch (IOException unexpected) {
            throw new UnknownException("Unexpected exception for " + Arrays.toString(commands), unexpected);
        }

    }

}
