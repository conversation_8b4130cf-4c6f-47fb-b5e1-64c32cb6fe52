package com.xylink.manager.service.db.backup.impl;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.db.backup.base.DatabaseType;
import com.xylink.manager.service.db.backup.base.param.BaseDatabaseBackupParam;
import com.xylink.manager.service.db.backup.base.param.BaseDatabaseRestoreParam;
import com.xylink.manager.service.db.backup.base.param.RelationalDBBackupParam;
import com.xylink.manager.service.db.backup.base.param.RelationalDBRestoreParam;
import com.xylink.util.SpringBeanUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @since 2024/11/15 17:09 下午
 * 高斯数据库处理类
 */
public class GaussDatabaseStrategy extends GenericDatabaseStrategy {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    public GaussDatabaseStrategy(DatabaseType databaseType) {
        super(databaseType);
    }

    @Override
    public List<CompletableFuture<Void>> backup(BaseDatabaseBackupParam param) {
        RelationalDBBackupParam rparam = (RelationalDBBackupParam) param;
        //目前只支持主备
        rparam.setAddressList(new ArrayList<>());
        K8sService k8sService = SpringBeanUtil.getBean(K8sService.class);
        Map<String, String> configmap = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        String master = configmap.get(NetworkConstants.MASTER_W_VIP);
        String slave = configmap.get(NetworkConstants.SLAVE_R_VIP);
        String port = configmap.get(NetworkConstants.DATABASE_PORT);

        if(StringUtils.isNotBlank(master)){
            rparam.getAddressList().add(new RelationalDBBackupParam.Address(master,port));
        }
        if(StringUtils.isNotBlank(slave)){
            rparam.getAddressList().add(new RelationalDBBackupParam.Address(slave, port));
        }

        return super.backup(param);
    }

    @Override
    public List<CompletableFuture<Void>> restore(BaseDatabaseRestoreParam param) {
        RelationalDBRestoreParam rparam = (RelationalDBRestoreParam) param;
        //目前只支持主备
        rparam.setAddressList(new ArrayList<>());
        K8sService k8sService = SpringBeanUtil.getBean(K8sService.class);
        Map<String, String> configmap = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        String master = configmap.get(NetworkConstants.MASTER_W_VIP);
        String slave = configmap.get(NetworkConstants.SLAVE_R_VIP);
        String port = configmap.get(NetworkConstants.DATABASE_PORT);

        if(StringUtils.isNotBlank(master)){
            rparam.getAddressList().add(new RelationalDBRestoreParam.Address(master,port));
        }
        if(StringUtils.isNotBlank(slave)){
            rparam.getAddressList().add(new RelationalDBRestoreParam.Address(slave, port));
        }

        return super.restore(param);
    }
}
