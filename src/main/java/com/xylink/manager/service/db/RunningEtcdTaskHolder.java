package com.xylink.manager.service.db;

import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

@Service
public class RunningEtcdTaskHolder {
    private static final String ETCD_NAME = "etcd";
    private static final ConcurrentHashMap<String, AtomicReference<RunningEtcdTask>> runningDBTasks = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, AtomicBoolean> tryToBeginDBTaskMarks = new ConcurrentHashMap<>();

    static {
        //初始化
        runningDBTasks.put(ETCD_NAME, new AtomicReference<>(new RunningEtcdTask(1L)));
        tryToBeginDBTaskMarks.put(ETCD_NAME, new AtomicBoolean());
    }

    public boolean attemptToBeginEtcdTask() {
        if (tryToBeginDBTaskMarks.get(ETCD_NAME).compareAndSet(false, true)) {
            if (!hasRunningEtcdTask()) {
                return true;
            } else {
                tryToBeginDBTaskMarks.get(ETCD_NAME).set(false);
            }
        }
        return false;
    }

    public boolean hasRunningEtcdTask() {
        return !getRunningEtcdTask().isCompleted();
    }

    public void cancelAttemptToBeginEtcdTask() {
        tryToBeginDBTaskMarks.get(ETCD_NAME).set(false);
    }

    public void setRunningEtcdTask(RunningEtcdTask runningEtcdTask) {
        runningDBTasks.get(ETCD_NAME).set(runningEtcdTask);
    }

    public RunningEtcdTask getRunningEtcdTask() {
        return runningDBTasks.get(ETCD_NAME).get();
    }
}
