package com.xylink.manager.service.db.etcd;

import com.xylink.config.NetworkConstants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.controller.dto.clusters.ClusterCollection;
import com.xylink.manager.model.em.BackupFileType;
import com.xylink.manager.service.BackUpNotifyService;
import com.xylink.manager.service.ServerNetworkService;
import com.xylink.manager.service.cluster.ClusterService;
import com.xylink.manager.service.db.BackUpResponse;
import com.xylink.manager.service.db.RestoreResponse;
import com.xylink.manager.service.remote.logagent.LogAgentBackupFileService;
import com.xylink.util.Ipv6Util;
import com.xylink.util.WebHookHttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.File;
import java.util.Collections;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/7/11 3:16 PM
 */
@Slf4j
@Service
public class EtcdClusterBackUpAndRestoreServiceImpl implements EtcdClusterBackUpAndRestoreService {
    @Resource
    private ClusterService clusterService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private BackUpNotifyService backUpNotifyService;
    @Resource
    private LogAgentBackupFileService logAgentBackupFileService;
    @Resource
    private ServerNetworkService serverNetworkService;

    private ClusterCollection clusterCollection = new ClusterCollection();

    private static final Set<String> LOCAL = new HashSet<>(Collections.singletonList("127.0.0.1"));

    private static final String WEBHOOK_ETCD_CLUSTER_BACKUP_PATH = "/hooks/etcdClusterBackup";
    private static final String WEBHOOK_ETCD_CLUSTER_RESTORE_PATH = "/hooks/etcdClusterRestore";
    private static final String WEBHOOK_STOP_API_PATH = "/hooks/stopApiServer";
    private static final String WEBHOOK_START_API_PATH = "/hooks/startApiServer";
    private static final String WEBHOOK_STOP_ETCD_PATH = "/hooks/stopEtcd";
    private static final String WEBHOOK_START_ETCD_PATH = "/hooks/startEtcd";


    @Override
    public BackUpResponse backup(String fileName) {
        initClusterCollection();
        BackUpResponse backUpResponse = new BackUpResponse();
        backUpResponse.setFileName(fileName);
        String logagentIp = firstEtcdEndpoint();
        String url = "http://" + Ipv6Util.handlerIpv6Addr(logagentIp) + ":" + NetworkConstants.WEB_HOOK + WEBHOOK_ETCD_CLUSTER_BACKUP_PATH;
        String managerIp = getManagerIp();
        String body = "{\"endpoints\":\"" + getEtcdEndpoints() + "\",\"fileName\":\"" + fileName + "\",\"managerIp\":\"" + managerIp + "\"}";
        log.info("Etcd backup request url:{}, body:{}", url, body);
        WebHookHttpUtil.isSuccessByPostWebHookResult(restTemplate, url, body);
        if (!backUpNotifyService.checkBackUpNotify(BackupFileType.etcdBackUp, fileName)) {
            // etcd和manager在相同节点不需要下载操作
            File file = logAgentBackupFileService.cpEtcdBackupFileToMainNode(logagentIp, managerIp, fileName, fileName);
            if (file != null) {
                backUpResponse.setFilePath(file.getPath());
                backUpResponse.setSize(file.length());
            }

        }
        backUpResponse.setEndTime(System.currentTimeMillis());
        log.info("Etcd backup response:{}", backUpResponse);
        return backUpResponse;
    }

    @Override
    public RestoreResponse restore(String fileName) {
        initClusterCollection();
        RestoreResponse restoreResponse = new RestoreResponse();
        restoreResponse.setFileName(fileName);
        File etcdBackFile = logAgentBackupFileService.getEtcdBackFile(fileName);
        if (etcdBackFile.exists() && etcdBackFile.isFile()) {
            restoreResponse.setFilePath(etcdBackFile.getPath());
            restoreResponse.setSize(etcdBackFile.length());
        } else {
            throw new ServerException(ErrorStatus.ETCD_BACK_FILE_NOT_EXIST);
        }
        log.info("Step 1: upload file to etcd host.");
        uploadEtcdBackupFileToEachHost(fileName);
        log.info("Step 2: stop api-server.");
        stopApiServer();
        log.info("Step 3: stop etcd.");
        stopEtcd();
        log.info("Step 4: restore etcd.");
        doRestore(fileName);
        log.info("Step 5: start etcd.");
        startEtcd();
        log.info("Step 6: start api-server.");
        startApiServer();
        restoreResponse.setEndTime(System.currentTimeMillis());
        log.info("Etcd restore response:{}", restoreResponse);
        return restoreResponse;
    }

    private void doRestore(String fileName) {
        Set<String> etcdClusters = getEtcdClusters();
        for (String etcdCluster : etcdClusters) {
            restore0(fileName, etcdCluster);
        }
    }

    private void restore0(String fileName, String etcdHostIp) {
        String url = "http://" + Ipv6Util.handlerIpv6Addr(etcdHostIp) + ":" + NetworkConstants.WEB_HOOK + WEBHOOK_ETCD_CLUSTER_RESTORE_PATH;
        String body = "{\"fileName\":\"" + fileName + "\"}";
        log.info("Etcd restore request url:{}, body:{}", url, body);
        WebHookHttpUtil.isSuccessByPostWebHookResult(restTemplate, url, body);
    }

    private String firstEtcdEndpoint() {
        return getEtcdClusters().stream().findFirst().get();
    }

    private String getEtcdEndpoints() {
        return getEtcdClusters().stream().map(item -> "https://" + Ipv6Util.handlerIpv6Addr(item) + ":2379").collect(Collectors.joining(","));
    }

    private Set<String> getEtcdClusters() {
        return this.clusterCollection.getEtcdClusters();
    }

    private Set<String> getApiServerClusters() {
        return this.clusterCollection.getApiServerClusters();

    }

    private String getManagerIp() {
        Map<String, String> map = serverNetworkService.getNetworkConfiguration();
        String managerIp = map.get(NetworkConstants.MAIN_IP);
        return StringUtils.isBlank(managerIp) ? map.get(NetworkConstants.MAIN_INTERNAL_IP) : managerIp;
    }

    private void initClusterCollection() {
        clusterCollection = clusterService.query();
        if (CollectionUtils.isEmpty(clusterCollection.getEtcdClusters())) {
            clusterCollection.setEtcdClusters(LOCAL);
        }
        if (CollectionUtils.isEmpty(clusterCollection.getApiServerClusters())) {
            clusterCollection.setApiServerClusters(LOCAL);
        }
        log.info("Cluster info:{}", clusterCollection);
    }

    private void uploadEtcdBackupFileToEachHost(String fileName) {
        Set<String> etcdClusters = this.getEtcdClusters();
        String managerIp = getManagerIp();
        for (String etcdCluster : etcdClusters) {
            if (!etcdCluster.equals(managerIp)) {
                logAgentBackupFileService.cpEtcdBackFileToOriginalNode(etcdCluster, fileName);
            } else {
                log.info("Etcd restore no need cp process.Restore endpoint is main.");
            }
        }
    }

    private void stopApiServer() {
        Set<String> apiClusters = this.getApiServerClusters();
        for (String apiCluster : apiClusters) {
            String url = "http://" + Ipv6Util.handlerIpv6Addr(apiCluster) + ":" + NetworkConstants.WEB_HOOK + WEBHOOK_STOP_API_PATH;
            log.info("Stop api-server request url:{}, body:{}", url, Strings.EMPTY);
            WebHookHttpUtil.isSuccessByPostWebHookResult(restTemplate, url, Strings.EMPTY);
        }
    }

    private void stopEtcd() {
        Set<String> etcdClusters = this.getEtcdClusters();
        for (String etcdCluster : etcdClusters) {
            String url = "http://" + Ipv6Util.handlerIpv6Addr(etcdCluster) + ":" + NetworkConstants.WEB_HOOK + WEBHOOK_STOP_ETCD_PATH;
            log.info("Stop etcd request url:{}, body:{}", url, Strings.EMPTY);
            WebHookHttpUtil.isSuccessByPostWebHookResult(restTemplate, url, Strings.EMPTY);
        }
    }

    private void startApiServer() {
        Set<String> apiClusters = this.getApiServerClusters();
        for (String apiCluster : apiClusters) {
            String url = "http://" + Ipv6Util.handlerIpv6Addr(apiCluster) + ":" + NetworkConstants.WEB_HOOK + WEBHOOK_START_API_PATH;
            log.info("Start api-server request url:{}, body:{}", url, Strings.EMPTY);
            WebHookHttpUtil.isSuccessByPostWebHookResult(restTemplate, url, Strings.EMPTY);
        }
    }

    private void startEtcd() {
        Set<String> etcdClusters = this.getEtcdClusters();
        for (String etcdCluster : etcdClusters) {
            String url = "http://" + Ipv6Util.handlerIpv6Addr(etcdCluster) + ":" + NetworkConstants.WEB_HOOK + WEBHOOK_START_ETCD_PATH;
            log.info("Start etcd request url:{}, body:{}", url, Strings.EMPTY);
            WebHookHttpUtil.isSuccessByPostWebHookResult(restTemplate, url, Strings.EMPTY);
        }
    }
}
