package com.xylink.manager.service.db.backup.base.param;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @since 2024/11/7 09:35 上午
 * 关系型数据库参数
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class RelationalDBBackupParam extends BaseDatabaseBackupParam{

    // CPU架构
    private String cpuArch;
    // 数据库用户名
    private String dbUser;
    // 数据库密码
    private String dbPassword;
    // 备份文件存放路径
    private String dbBackupPath;
    // 主目录
    private String baseDir;

    //多线程并发处理的参数

    //线程池
    ExecutorService dbPool;
    //所有数据库的IP地址
    List<Address> addressList = new ArrayList<>();

    // 内部类
    public static class Address {
        private String ipAddress;
        private String port;

        // 构造方法
        public Address(String ipAddress, String port) {
            this.ipAddress = ipAddress;
            this.port = port;
        }

        // 获取 IP 地址
        public String getIpAddress() {
            return ipAddress;
        }

        // 设置 IP 地址
        public void setIpAddress(String ipAddress) {
            this.ipAddress = ipAddress;
        }

        // 获取端口号
        public String getPort() {
            return port;
        }

        // 设置端口号
        public void setPort(String port) {
            this.port = port;
        }

        @Override
        public String toString() {
            return "Address{" +
                    "ipAddress='" + ipAddress + '\'' +
                    ", port=" + port +
                    '}';
        }
    }
}
