package com.xylink.manager.service.db;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.config.exception.basic.WebException;
import com.xylink.manager.model.DbBackupFile;
import com.xylink.manager.model.deploy.Node;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.model.em.DBType;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.cache.service.ICacheService;
import com.xylink.manager.service.db.backup.base.DatabaseStrategy;
import com.xylink.manager.service.db.backup.base.DatabaseStrategyFactory;
import com.xylink.manager.service.db.backup.base.DatabaseType;
import com.xylink.manager.service.db.backup.base.param.NoSQLDBBackupParam;
import com.xylink.manager.service.db.backup.base.param.NoSQLDBRestoreParam;
import com.xylink.manager.service.db.backup.base.param.RelationalDBBackupParam;
import com.xylink.manager.service.db.backup.base.param.RelationalDBRestoreParam;
import com.xylink.manager.service.db.dm.DMService;
import com.xylink.manager.service.db.etcd.EtcdClusterBackUpAndRestoreService;
import com.xylink.manager.service.db.st.STService;
import com.xylink.manager.service.ecvs.EsOpsService;
import com.xylink.manager.service.ecvs.HBaseOpsService;
import com.xylink.manager.service.remote.ocean.OceanRemoteClient;
import com.xylink.util.ZipUtil;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.*;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.xylink.manager.service.db.backup.base.DatabaseStrategy.RELATIONAL_DB_BACKUP_IP;
import static com.xylink.manager.service.db.backup.base.DatabaseStrategy.RELATIONAL_DB_BACKUP_PORT;

@Service
public class DBBackupAndRestoreService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private static String baseDir;
    private static String host_base_path;
    private static String host_tmp_path;

    @Value("${base.dir}")
    public void setBaseDir(String baseDir) {
        DBBackupAndRestoreService.baseDir = baseDir;
        host_base_path = baseDir + "/db/all";
        host_tmp_path = baseDir + "/db/tmp";
    }


    private ExecutorService dbPool = Executors.newFixedThreadPool(20);

    private static final ConcurrentHashMap<DBType, AtomicReference<RunningDBTask>> runningDBTasks = new ConcurrentHashMap<>();

    private AtomicBoolean taskIsRunning = new AtomicBoolean(false);
    private AtomicInteger taskType = new AtomicInteger(1);

    private final int BACKUP = 1;
    private final int RESTORE = 2;
    private final int UPGRADE = 3;

    @Autowired
    private K8sService k8sService;
    @Autowired
    private STService stService;
    @Autowired
    private DBBackupService dbBackupService;
    @Autowired
    private DbUpgradeService dbUpgradeService;
    @Autowired
    private HBaseOpsService hBaseOpsService;
    @Autowired
    private EsOpsService esOpsService;
    @Autowired
    private JasyptService jasyptService;
    @Autowired
    private DBService dbService;
    @Autowired
    private ICacheService cacheService;
    @Resource
    private DMService dmService;
    @Resource
    private EtcdClusterBackUpAndRestoreService etcdClusterBackUpAndRestoreService;
    @Resource
    private OceanRemoteClient oceanRemoteClient;

    private static final long DB_BACKUP_INTERVAL = 24 * 3600 * 1000;
    private static final long DB_BACKUP_REMOVE_INTERVAL = 30 * DB_BACKUP_INTERVAL;

    @PostConstruct
    private void init() {

        //存放db相关任务错误输出
        File file = new File("" + baseDir + "/logs/db");
        if (!file.exists()) file.mkdirs();

        //存放临时上传或生成的数据文件
        file = new File(host_tmp_path);
        if (!file.exists()) file.mkdirs();
    }

    public String getNosqlBackupFileName(DatabaseType type, String time) {
        switch (type) {
            case HBASE:
            case ES:
                return type.getName() + "_backup_" + time + ".tar.gz";
            case REDIS:
                return type.getName() + "_backup_" + time + ".aof";
            case ETCD:
                return type.getName() + "_backup_" + time + ".db";
            default:
                return "";
        }
    }

    /**
     * 备份数据库并发任务生成
     */
    public List<CompletableFuture<Void>> backupJob(String time,String dbType) {
        List<CompletableFuture<Void>> res = new ArrayList<>();

        //处理非关系型数据库
        List<CompletableFuture<Void>> nosqls = Arrays.stream(DatabaseType.values())
                .filter(t -> !t.isRDBMS())
                .map(t -> CompletableFuture.runAsync(() -> {
                            NoSQLDBBackupParam param = new NoSQLDBBackupParam();
                            param.setTime(time);
                            param.setFileName(getNosqlBackupFileName(t,time));
                            DatabaseStrategyFactory.create(t).backup(param);
                        }
                       , dbPool))
                .collect(Collectors.toList());

        res.addAll(nosqls);

        //处理关系型数据库，获取所有业务库的IP，然后封装成参数进行传递
        Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        Map<String,String> ipPort = new HashMap<>();
        for (int i = 0; i < RELATIONAL_DB_BACKUP_IP.size(); i++) {
            String ip = allIp.get(RELATIONAL_DB_BACKUP_IP.get(i));
            String port = allIp.get(RELATIONAL_DB_BACKUP_PORT.get(i));
            if(StringUtils.isNotBlank(ip) &&!NetworkConstants.DEFAULT_INTERNAL_IP.equals(ip) && !ipPort.containsKey(ip)){
                //去重，去除
                ipPort.put(ip,port);
            }
        }

        //map转换成list
        if(!ipPort.isEmpty()){

            //获取数据库备份的账号和密码
            String dbUser = allIp.getOrDefault(NetworkConstants.MAIN_DB_BACKUP_USERNAME, DatabaseStrategy.getDbBackUpUsername(dbType));
            String dbPwd = allIp.get(NetworkConstants.MAIN_DB_BACKUP_PASSWORD);
            if(StringUtils.isNotBlank(dbPwd)){
                dbPwd = jasyptService.decrypt(dbPwd);
            }else{
                dbPwd = DatabaseStrategy.DB_BACKUP_PWD;
            }

            RelationalDBBackupParam param = new RelationalDBBackupParam();
            param.setBaseDir(baseDir);
            param.setDbBackupPath(dbBackupService.getHostBackupDir(dbType,time,true));
            param.setDbUser(dbUser);
            param.setDbPassword(dbPwd);
            param.setDbPool(dbPool);
            //调用处理器
            List<RelationalDBBackupParam.Address> addressList = ipPort.entrySet().stream().
                    map(e -> new RelationalDBBackupParam.Address(e.getKey(), e.getValue())).collect(Collectors.toList());
            param.setAddressList(addressList);
            List<CompletableFuture<Void>> list = DatabaseStrategyFactory.create(dbType).backup(param);

            if(!CollectionUtils.isEmpty(list)){
                res.addAll(list);
            }
        }


        return res;
    }


    /**
     * 还原数据库并发任务生成
     */
    public List<CompletableFuture<Void>> restoreJob(String time,String dbType) {
        List<CompletableFuture<Void>> res = new ArrayList<>();

        //处理非关系型数据库
        List<CompletableFuture<Void>> nosqls = Arrays.stream(DatabaseType.values())
                .filter(t -> !t.isRDBMS() && t != DatabaseType.ETCD)
                .map(t -> CompletableFuture.runAsync(() -> {
                            NoSQLDBRestoreParam param = new NoSQLDBRestoreParam();
                            param.setTime(time);
                            param.setFileName(getNosqlBackupFileName(t,time));
                            DatabaseStrategyFactory.create(t).restore(param);
                        }
                        , dbPool))
                .collect(Collectors.toList());

        res.addAll(nosqls);

        //处理关系型数据库，获取所有业务库的IP，然后封装成参数进行传递
        Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        Map<String,String> ipPort = new HashMap<>();
        for (int i = 0; i < RELATIONAL_DB_BACKUP_IP.size(); i++) {
            String ip = allIp.get(RELATIONAL_DB_BACKUP_IP.get(i));
            String port = allIp.get(RELATIONAL_DB_BACKUP_PORT.get(i));
            if(StringUtils.isNotBlank(ip) &&!NetworkConstants.DEFAULT_INTERNAL_IP.equals(ip) && !ipPort.containsKey(ip)){
                //去重，去除
                ipPort.put(ip,port);
            }
        }

        //map转换成list
        if(!ipPort.isEmpty()){

            //获取数据库备份的账号和密码
            String dbUser = allIp.getOrDefault(NetworkConstants.MAIN_DB_BACKUP_USERNAME, DatabaseStrategy.getDbBackUpUsername(dbType));
            String dbPwd = allIp.get(NetworkConstants.MAIN_DB_BACKUP_PASSWORD);
            if(StringUtils.isNotBlank(dbPwd)){
                dbPwd = jasyptService.decrypt(dbPwd);
            }else{
                dbPwd = DatabaseStrategy.DB_BACKUP_PWD;
            }

            RelationalDBRestoreParam param = new RelationalDBRestoreParam();
            param.setBaseDir(baseDir);
            param.setDbBackupPath(dbBackupService.getHostBackupDir(dbType,time,true));
            param.setDbUser(dbUser);
            param.setDbPassword(dbPwd);
            param.setDbPool(dbPool);
            //调用处理器
            List<RelationalDBRestoreParam.Address> addressList = ipPort.entrySet().stream().
                    map(e -> new RelationalDBRestoreParam.Address(e.getKey(), e.getValue())).collect(Collectors.toList());
            param.setAddressList(addressList);
            List<CompletableFuture<Void>> list = DatabaseStrategyFactory.create(dbType).restore(param);
            if(!CollectionUtils.isEmpty(list)){
                res.addAll(list);
            }
        }


        return res;
    }

    /**
     * 备份所有数据库，包括etcd
     */
    @Async
    @Scheduled(cron = "0 30 3 * * ?")
    public void backupAll() {
        if (!taskIsRunning.compareAndSet(false, true)) {
            logger.error("backupAll compareAndSet fail");
            return;
        }
        try {
            taskType.set(BACKUP);

            String dbType = k8sService.getDataBaseType();

            removeExpiredBDBackupFiles();
            dbService.removeExpiredEtcdBackupFiles();

            Date time = new Date();

            File file = new File(host_base_path + File.separator + time.getTime());
            if (!file.exists()) file.mkdirs();

            logger.info("start backup all dbs ...");

            List<CompletableFuture<Void>> cfs = backupJob(String.valueOf(time.getTime()),dbType);

            CompletableFuture.allOf(cfs.toArray(new CompletableFuture[0]))
                    .thenRun(() -> {
                        taskIsRunning.set(false);
                        logger.info("bacckup job(all) complete!!");
                    })
                    .exceptionally(e -> {
                        logger.error("bacckup job complete exceptionally!!!", e);
                        taskIsRunning.set(false);
                        return Void.TYPE.cast(null);
                    });
        } catch (Exception e) {
            logger.error("backupAll error", e);
            taskIsRunning.set(false);
        }
    }

    /**
     * 单独备份etcd
     */
    @Async
    public void backupEtcd() {
        if (!taskIsRunning.compareAndSet(false, true)) {
            logger.error("backupEtcd compareAndSet fail");
            return;
        }
        try {
            taskType.set(BACKUP);

            dbService.removeExpiredEtcdBackupFiles();
            DBTask dbTask = new DBTask(DBType.etcd.name(), new Date().getTime() + "");

            logger.info(String.format("start backup etcd as %s ...", dbTask.getFileName()));

            CompletableFuture.runAsync(() ->  etcdClusterBackUpAndRestoreService.backup(dbTask.getFileName()), dbPool)
                    .thenRun(() -> {
                        taskIsRunning.set(false);
                        logger.info("etccd bacckup job completed");
                    })
                    .exceptionally(e -> {
                        logger.error("etccd bacckup job complete exceptionally!!!", e);
                        taskIsRunning.set(false);
                        return Void.TYPE.cast(null);
                    });
        } catch (Exception e) {
            taskIsRunning.set(false);
        }
    }

    /**
     * 备份mysql
     */
    public void backupMysql(String time,String masterOrSlave){
        //TODO 判断POD是否可用
        String[] commands = dbBackupService.constructDockerBackupCommand(DBType.main.name(), masterOrSlave, time, true, DatabaseType.MYSQL.getName());
        exec(commands, new File(baseDir + "/logs/db/mysql-backup.log"));
    }


    /**
     * 整体数据恢复,不包含etcd
     * etcd主要为环境数据，与业务数据无关，同时恢复可能会影响服务运行，没有必要一起回滚
     *
     * @param name 时间节点，对应备份文件目录
     */
    public void restoreAllExcludeEtcd(String name) {
        if (name.endsWith(".zip")) {
            String fileUrl = host_base_path + File.separator + name;
            ZipUtil.unzip(fileUrl, host_base_path + File.separator);
            try {
                FileUtils.forceDelete(new File(fileUrl));
            } catch (IOException e) {
                logger.warn("delete file {} fail", fileUrl);
            }
            name = name.substring(0, name.length() - 4);
        }
        // 归档时间 如：1635252518605
        String finalName = name;
        // 数据库类型
        String dbType = k8sService.getDataBaseType();

        try {
            if (taskIsRunning.compareAndSet(false, true)) {
                taskType.set(RESTORE);
                logger.info("start restore all dbs (exclude etcd) {}...", finalName);

                List<CompletableFuture<Void>> restore = restoreJob(name, dbType);
                CompletableFuture.allOf(restore.toArray(new CompletableFuture[0]))
                        .thenRun(this::stopServicesForSyncMongodb)
                        .thenRun(this::syncMongodb)
                        .thenRun(this::startServicesForSyncMongodb)
                        .thenRun(this::restartEs)
                        .thenRun(() -> {
                            taskIsRunning.set(false);
                            logger.info("restore job(all) completed!!");
                        })
                        .exceptionally(e -> {
                            logger.error("restore job complete exceptionally!!!", e);
                            taskIsRunning.set(false);
                            return Void.TYPE.cast(null);
                        });
            }
        } catch (Exception e) {
            logger.error("restore job complete exceptionally!!!", e);
            taskIsRunning.set(false);
        }
    }

    public void restoreCallback(String type) {
        try {
            if ("database".equals(type)) {
                stopServicesForSyncMongodb();
                syncMongodb();
                startServicesForSyncMongodb();
            }
            logger.info("restore job(all) completed by callback!!");
        } finally {
            taskIsRunning.set(false);
        }
    }

    /**
     * 恢复etcd数据
     *
     * @param name 文件名
     */
    public void restoreEtcd(String name) {
        try {
            if (taskIsRunning.compareAndSet(false, true)) {
                taskType.set(RESTORE);

                DBTask dbTask = new DBTask();
                dbTask.setFileName(name);
                dbTask.setType(DBType.etcd.name());

                logger.info(String.format("start to restore for eetcd (%s)...", name));

                CompletableFuture.runAsync(() -> restoreEtcd(dbTask), dbPool)
                        .thenRun(this::checkEtcd)
                        .thenRun(() -> {
                            taskIsRunning.set(false);
                            logger.error("restore etcd completed!!!");
                        })
                        .exceptionally(e -> {
                            logger.error("restore job completed exceptionally!!!");
                            taskIsRunning.set(false);
                            return Void.TYPE.cast(null);
                        });
            }
        } finally {
            taskIsRunning.set(false);
        }
    }

    private void checkEtcd() {
        // 等待6s, 调用webhook触发etcd恢复
        try {
            Thread.sleep(6000);
        } catch (InterruptedException e) {
            logger.error("thread sleep error!", e);
        }

        boolean success = false;
        long start = new Date().getTime();
        while (!success) {
            // 查询一个configmap, 若查询成功则认为 etcd功能已恢复， 如果报错则认为etcd数据正在恢复中
            try {
                k8sService.getConfigmap("all-ip");
                success = true;

                //刷新缓存
                try {
                    cacheService.refreshAll();
                    cacheService.refreshAllConfigMapCache();
                } catch (Exception e) {
                    logger.error("etcd恢复，缓存刷新失败。", e);
                }

                break;
            } catch (Exception e) {
                // 这里异常不处理

                // 10m 超时
                if (new Date().getTime() - start > 10 * 60 * 1000) {
                    logger.error("etcd restore timeout !!!");
                    throw new ServerException("etcd恢复失败");
                }

                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e1) {
                    logger.error("thread sleep error!", e);
                }

            }
        }

    }

    /**
     * 恢复etcd数据
     */
    private void restoreEtcd(DBTask task) {
        logger.info(String.format("start restore %s (%s) ...", task.getType(), task.getFileName()));
        String filePath = getHostDBBackupDir(task.getType(), task.getFileName());

        if (task.getFileName().endsWith(".zip")) {
            logger.info("unzip: " + task.getFileName() + " to " + filePath.replace(task.getFileName(), ""));
            ZipUtil.unzip(filePath, filePath.replace(task.getFileName(), ""), "");
            String unZipFile = filePath.substring(0, filePath.length() - 4);
            if (new File(unZipFile).exists()) new File(filePath).delete();
            task.setFileName(task.getFileName().substring(0, task.getFileName().length() - 4));
        }

        if (!task.getFileName().endsWith(".db")) {
            throw new WebException(ErrorStatus.FILE_PATH_ILLEGAL);
        }
        logger.info(String.format("restore etcd (%s) ...", task.getFileName()));
        etcdClusterBackUpAndRestoreService.restore(task.getFileName());
    }

    private void restartHbase() {
        Optional<Pod> optional = k8sService.getHBasePod();
        if (!optional.isPresent()) {
            logger.error("hbase pod is not present");
            return;
        }
        k8sService.deletePod(optional.get().getPodName());
    }

    private void restartEs() {
        Optional<Pod> optional = k8sService.getEsPod();
        if (!optional.isPresent()) {
            logger.error("es pod is not present");
            return;
        }
        k8sService.deletePod(optional.get().getPodName());
    }

    /**
     * 启动mongodb 同步之前停止的服务
     */
    private void startServicesForSyncMongodb() {
        renameNodeLabel("ocean", "no", Constants.XYLINK);
        renameNodeLabel("kafka", "no", Constants.XYLINK);
        renameNodeLabel("canal", "no", Constants.XYLINK);
    }

    /**
     * 停止部分服务，避免对monogodb数据同步产生影响
     */
    private void stopServicesForSyncMongodb() {
        renameNodeLabel("ocean", Constants.XYLINK, "no");
        renameNodeLabel("kafka", Constants.XYLINK, "no");
        renameNodeLabel("canal", Constants.XYLINK, "no");
    }

    /**
     * 修改node label
     *
     * @param service  服务名
     * @param oldValue old value
     * @param newValue new value
     */
    private void renameNodeLabel(String service, String oldValue, String newValue) {
        List<Node> nodes = k8sService.getNodeByLabel(service, oldValue);
        nodes.forEach(node -> k8sService.updateNodeLabel(node.getName(), service, newValue));
    }

    /**
     * 同步mongodb
     */
    private void syncMongodb() {
        logger.info("start sync mongodb data...");
        Map<String, String> data = new HashMap<>();
        data.put("sync_mongodb", "true");
        k8sService.editConfigmap(Constants.CONFIGMAP_PRIVATE_DATA, data);
        oceanRemoteClient.oceanEtlSync();
    }

    /**
     * 删除过期文件
     */
    private void removeExpiredBDBackupFiles() {
        if ("ST".equals(k8sService.getDataBaseType())) {
            stService.batchDeleteShenTongBackupFile();
        }
        if ("DM".equals(k8sService.getDataBaseType())) {
            dmService.batchDeleteDamengBackupFile();
        }
        File file = new File(host_base_path);
        File[] files = file.listFiles();
        if (files == null || files.length == 0) return;
        long currentTime = System.currentTimeMillis();

        for (File f : files) {
            if (currentTime - f.lastModified() > DB_BACKUP_REMOVE_INTERVAL) {
                try {
                    if (f.isDirectory()) {
                        FileUtils.deleteDirectory(f);
                    } else {
                        f.delete();
                    }
                } catch (Exception e) {
                    logger.error("delete file error, {}", f.getName());
                }
            }
            if (!f.getName().endsWith(".zip")) {
                logger.info("zip file: " + f.getName());
                ZipUtil.zipAndRemove(f.getAbsolutePath(), f.getAbsolutePath() + ".zip", "");
            }
        }
        hBaseOpsService.removeExpiredBackupFile();
        esOpsService.removeExpiredBackupFile();
    }

    /**
     * 执行系统命令
     * 这个不打印标准输出，部分操作可能标准输出数据量较大，避免磁盘占用
     *
     * @param error 错误输出文件
     * @return 成功与否
     */
    public boolean exec(String[] command, File error) {
        if (command == null || command.length == 0) return false;
        if (error.exists()) error.delete();
        try {
            error.createNewFile();
        } catch (IOException e) {
            logger.error("create new file error!", e);
            return false;
        }

        logger.info("exec: " + StringUtils.join(command, " "));

        ProcessBuilder pb = new ProcessBuilder(command);

        try {
            pb.redirectErrorStream(true);
//            if (error != null) pb.redirectOutput(error);
            if (error != null) pb.redirectOutput(error);
            Process process = pb.start();
            boolean over = process.waitFor(2, TimeUnit.HOURS);
            if (over) {
                boolean result = process.exitValue() == 0;
                process.destroy();
                logger.info("process exit( " + process.exitValue() + " ) : " + StringUtils.join(command, " "));

                return result;
            } else {
                logger.error("timeout(2h) and destory process forcibly, command:  " + Arrays.toString(command));
                process.destroyForcibly();
                return false;
            }
        } catch (IOException | InterruptedException e) {
            logger.error("process exec error!", e);
        }
        return false;
    }

    /**
     * 备份文件列表
     */
    public List<DbBackupFile> listBackupFiles() {
        List<DbBackupFile> list = new ArrayList<>();
        File[] files = new File(host_base_path).listFiles();
        if (files == null || files.length == 0) return list;
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (File dir : files) {
            DbBackupFile db = new DbBackupFile();
            db.setName(dir.getName());
            db.setTime(format.format(dir.lastModified()));

            File[] listFiles = dir.listFiles();
            if (listFiles != null && listFiles.length > 0) {
                db.setFiles(Arrays.stream(listFiles).map(f -> new DbBackupFile(f.getName(), format.format(f.lastModified()), f.length())).collect(Collectors.toList()));
            } else {
                db.setSize(dir.length());
            }

            list.add(db);
        }

        list.sort(Comparator.comparing(a -> {
            try {
                return Long.parseLong(((DbBackupFile) a).getName());
            } catch (Exception e) {
                try {
                    return format.parse(((DbBackupFile) a).getTime()).getTime();
                } catch (ParseException parseException) {
                    return Long.MAX_VALUE;
                }
            }
        }).reversed());
        return list;
    }

    /**
     * 获取当前任务状态
     */
    public Map<String, String> status() {
        Map<String, String> status = new HashMap<>();
        status.put("status", taskIsRunning.get() + "");
        status.put("type", taskType.get() + "");
        return status;
    }

    /**
     * 删除备份文件
     *
     * @param fileName 文件/文件夹名
     * @param type     all/etcd
     */
    public void deleteBackupFile(String fileName, String type) {
        String basePath, fullPath;
        File file;
        logger.info(String.format("try to delete %s for %s !", fileName, type));
        if ("all".equals(type)) {
            basePath = dbBackupService.getHostBackupDir(null, fileName, true);
            Path path = Paths.get(basePath).normalize();

            if (!path.toString().startsWith(host_base_path))
                throw new NotBackupFileException(basePath + " is not a file under the directory " + host_base_path);

            file = path.toFile();
        } else if (DBType.etcd.name().equals(type)) {
            basePath = dbBackupService.getHostBackupDir(type, null, true);
            fullPath = basePath + fileName;
            Path path = Paths.get(fullPath).normalize();
            Path backupDir = Paths.get(basePath).normalize();
            if (!path.startsWith(backupDir))
                throw new NotBackupFileException(fullPath + " is not a file under the directory " + backupDir.toString());

            file = path.toFile();
        } else {
            return;
        }

        if (file.isDirectory()) {
            try {
                FileUtils.deleteDirectory(file);
            } catch (IOException e) {
                logger.error("delete backup files error!", e);
            }
        } else {
            file.delete();
        }

    }

    /**
     * 生成备份文件路径
     *
     * @param type     类型 all / etcd
     * @param fileName 文件名,对于all 应该是时间戳
     * @return all ---> 目录路径， etcd ---> 文件路径
     */
    public String getHostDBBackupDir(String type, String fileName) {
        return "all".equals(type) ?
                dbBackupService.getHostBackupDir(null, fileName, true) :
                dbBackupService.getHostBackupDir(type, null, true) + fileName;
    }

    /**
     * 处理上传的文件
     * 将文件按照备份逻辑约定的路径解压存放
     */
    public void handleUploadFile(MultipartFile file, String type) throws IOException {
        if ("all".equals(type)) {
            String originalFilename = file.getOriginalFilename();

            //目前只支持.zip
            if (!originalFilename.endsWith(".zip")) throw new WebException(ErrorStatus.FILE_ILLEGAL);
            String name = originalFilename.substring(0, originalFilename.length() - 4);

            String path = host_tmp_path + File.separator + UUID.randomUUID().toString().replace("-", "");
            File tmpdir = new File(path);
            if (!tmpdir.exists()) tmpdir.mkdirs();

            File tmpFile = new File(path + File.separator + originalFilename);
            if (copyToFile(file.getInputStream(), tmpFile)) {
                File dest = new File(host_base_path + File.separator + name);
                if (!dest.exists()) dest.mkdirs();

                logger.info("save " + tmpFile.getAbsolutePath() + " to " + host_base_path + File.separator);
                ZipUtil.unzip(tmpFile.getAbsolutePath(), host_base_path + File.separator);
            }
            FileUtils.deleteDirectory(tmpdir);
        } else {
            String path = getHostDBBackupDir(type, file.getOriginalFilename());
            copyToFile(file.getInputStream(), new File(path));
        }

    }


    private boolean copyToFile(InputStream input, File file) {
        logger.info("save file to {}", file.getAbsolutePath());
        if (file.exists()) {
            logger.warn("uploaded file [{}] already exist, delete and recreate!", file.getName());
            file.delete();
        }
        try {
            file.createNewFile();
            FileOutputStream fOutput = new FileOutputStream(file);
            BufferedOutputStream bOutput = new BufferedOutputStream(fOutput);
            FileCopyUtils.copy(input, bOutput);
            fOutput.flush();
        } catch (Exception e) {
            logger.error("copy file error!", e);
            if (file.exists()) file.delete();
            return false;
        }
        return true;
    }

    /**
     * 升级数据库, 当前仅支持main 数据库（mysql）
     *
     * @param type 数据库类型
     */
    public void upgrade(String type) {
        String dbType = k8sService.getDataBaseType();
        if ("ST".equalsIgnoreCase(dbType)) throw new ServerException("当前操作不支持!");

        String[] commands = dbUpgradeService.constructDockerUpgradeCommand();
        try {
            if (taskIsRunning.compareAndSet(false, true)) {
                taskType.set(UPGRADE);

                CompletableFuture.runAsync(() -> exec(commands, new File(baseDir + "/logs/db/" + type + "-upgrade.log")))
                        .thenRun(() -> taskIsRunning.set(false))
                        .exceptionally(e -> {
                            logger.error("upgrade job complete exceptionally!!!", e);
                            taskIsRunning.set(false);
                            return Void.TYPE.cast(null);
                        });
            }
        } finally {
            taskIsRunning.set(false);
        }
    }
}
