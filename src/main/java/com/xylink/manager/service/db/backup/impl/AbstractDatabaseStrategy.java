package com.xylink.manager.service.db.backup.impl;

import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.db.backup.base.DatabaseStrategy;
import com.xylink.manager.service.db.backup.base.DatabaseType;
import com.xylink.util.CommandUtils;
import com.xylink.util.SpringBeanUtil;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.FileCopyUtils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * <AUTHOR>
 * @since 2024/11/6 17:56 下午
 * 数据库操作抽象类
 */
public abstract class AbstractDatabaseStrategy implements DatabaseStrategy {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractDatabaseStrategy.class);

    private static final String DOCKER_RUN = "docker run ";
    private static final String REPLACE_DOCKER_RUN = "nerdctl --namespace k8s.io run ";

    protected final DatabaseType databaseType;

    public AbstractDatabaseStrategy(DatabaseType databaseType) {
        this.databaseType = databaseType;
    }

    /**
     * main_host_ip=$1
     * main_host_port=$2
     * main_db_name=$3
     *
     * # 超管账号
     * sys_username=$4
     * # 超管密码
     * sys_password=$5
     *
     * # 宿主机备份文件夹位置
     * database_backup_dir=$6
     */
    protected void executeShellBackup(String script , String host,String port,
                                      String baseDir,String user,String pwd,String path,String logpath) {
        String shell = baseDir + "/private_manager/script/" + script;
        copyFileWithContainerD(shell);

        String cmd =  shell + " " +
                host + " " +
                port + " " +
                user + " " +
                pwd + " " +
                path + " " +
                logpath + " ";

        String logFile = baseDir + "/logs/db/db-common-backup.log";

        CommandUtils.execLogExist(new String[]{"/bin/sh", "-c", cmd}, new File(logFile));
    }

    protected void executeShellRestore(String script , String host,String port,String baseDir
            ,String user,String pwd,String path,String logDir) {
        String shell = baseDir + "/private_manager/script/" + script;
        copyFileWithContainerD(shell);
        String cmd =  shell + " " +
                host + " " +
                port + " " +
                user + " " +
                pwd + " " +
                path + " " +
                logDir + " ";

        String logFile = baseDir + "/logs/db/db-common-restore.log";

        CommandUtils.execLogExist(new String[]{"/bin/sh", "-c", cmd}, new File(logFile));
    }

    /**
     * 复制脚本文件
     *
     * @param path 文件路径
     */
    protected File copyFile(String path) {
        try {
            // 删除文件
            Files.deleteIfExists(Paths.get(path));
            String name = path.substring(path.lastIndexOf("/") + 1);
            ClassPathResource classPathResource = new ClassPathResource("scripts/dbbackup/" + name);

            File file = new File(path);
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }

            FileCopyUtils.copy(classPathResource.getInputStream(), Files.newOutputStream(file.toPath()));

            file.setExecutable(true);
            return file;
        } catch (IOException e) {
            throw new ServerException(e, ErrorStatus.FILE_PATH_ILLEGAL);
        }
    }

    protected void copyFileWithContainerD(String shell) {
        File file = copyFile(shell);
        //判断是否去kvm
        K8sService k8sService = SpringBeanUtil.getBean(K8sService.class);
        if(k8sService.isContainerD()){
            //通过FileUtils将文件中的DOCKER_RUN字符串统一替换为REPLACE_DOCKER_RUN字符串
            String content = null;
            try {
                content = FileUtils.readFileToString(file, "UTF-8");
                String newContent = content.replace(DOCKER_RUN, REPLACE_DOCKER_RUN);
                FileUtils.writeStringToFile(file, newContent, "UTF-8");
            } catch (IOException e) {
                LOGGER.error("kvm replace docker run error ...",e);
            }
        }
    }

}
