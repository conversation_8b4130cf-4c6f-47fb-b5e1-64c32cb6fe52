package com.xylink.manager.service.db.jc;

import com.xylink.manager.service.db.DBCommon;
import com.xylink.util.JDBCUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 金仓数据库
 */
@Slf4j
@Service
public class JCService extends DBCommon {
    private static final int DB_UNLIMITED = -2;
    private static final int RUNTIME_UNLIMITED = Integer.MAX_VALUE;

    @Autowired
    private JDBCUtils jdbcUtils;

    @Override
    public int getLicenseValidDays(String dbIp, String dbPort) throws SQLException {
        String sql = "select get_license_validdays()";
        Connection connection = null;
        PreparedStatement ps = null;
        ResultSet resultSet = null;
        try {
            connection = jdbcUtils.getBackupAccountConnection("JC", dbIp,  dbPort, null);
            ps = connection.prepareStatement(sql);
            resultSet = ps.executeQuery();
            while (resultSet.next()) {
                int licenseDay = resultSet.getInt(1);
                return licenseDay == DB_UNLIMITED ? RUNTIME_UNLIMITED : licenseDay;
            }
        } catch (Exception e) {
            log.error("Query [{}] error.\n", sql, e);
            throw e;
        } finally {
            jdbcUtils.close(connection, ps, resultSet);
        }
        return Integer.MIN_VALUE;
    }

}
