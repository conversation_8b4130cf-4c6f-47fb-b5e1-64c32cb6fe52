package com.xylink.manager.service.db;

import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

@Service
public class RunningStTaskHolder {
    private static final String ST_NAME = "st";
    private static final ConcurrentHashMap<String, AtomicReference<RunningStTask>> runningDBTasks = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, AtomicBoolean> tryToBeginDBTaskMarks = new ConcurrentHashMap<>();

    static {
        //初始化
        runningDBTasks.put(ST_NAME, new AtomicReference<>(new RunningStTask(1L)));
        tryToBeginDBTaskMarks.put(ST_NAME, new AtomicBoolean());
    }

    public boolean attemptToBeginStTask() {
        if (tryToBeginDBTaskMarks.get(ST_NAME).compareAndSet(false, true)) {
            if (!hasRunningStTask()) {
                return true;
            } else {
                tryToBeginDBTaskMarks.get(ST_NAME).set(false);
            }
        }
        return false;
    }

    public boolean hasRunningStTask() {
        return !getRunningStTask().isCompleted();
    }

    public void cancelAttemptToBeginStTask() {
        tryToBeginDBTaskMarks.get(ST_NAME).set(false);
    }

    public void setRunningStTask(RunningStTask runningStTask) {
        runningDBTasks.get(ST_NAME).set(runningStTask);
    }

    public RunningStTask getRunningStTask() {
        return runningDBTasks.get(ST_NAME).get();
    }
}
