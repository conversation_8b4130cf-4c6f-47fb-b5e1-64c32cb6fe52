package com.xylink.manager.service.db.dm;

import com.xylink.config.NetworkConstants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.config.exception.basic.WebException;
import com.xylink.manager.model.em.BackupFileType;
import com.xylink.manager.model.em.DBType;
import com.xylink.manager.model.em.DamengDBType;
import com.xylink.manager.service.BackUpNotifyService;
import com.xylink.manager.service.ServerNetworkService;
import com.xylink.manager.service.base.K8sSvcService;
import com.xylink.manager.service.db.DBCommon;
import com.xylink.manager.service.db.RunningDmTask;
import com.xylink.manager.service.db.RunningDmTaskHolder;
import com.xylink.manager.service.remote.logagent.LogAgentBackupFileService;
import com.xylink.util.Ipv6Util;
import com.xylink.util.JDBCUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Map;

/**
 * 达梦数据库
 */
@Slf4j
@Service
public class DMService extends DBCommon {
    private static final Logger logger = LoggerFactory.getLogger(DMService.class);

    @Autowired
    private K8sSvcService k8sSvcService;
    @Autowired
    private RunningDmTaskHolder runningDmTaskHolder;
    @Autowired
    private ServerNetworkService serverNetworkService;
    @Autowired
    private BackUpNotifyService backUpNotifyService;
    @Autowired
    private LogAgentBackupFileService logAgentBackupFileService;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private JDBCUtils jdbcUtils;

    public RunningDmTask backup(String fileName, String time, String dmPodIp, String mainIp) {
        RunningDmTask runningDmTask = backupDm(fileName, dmPodIp, mainIp);
        if (!backUpNotifyService.checkBackUpNotify(BackupFileType.DmType, fileName)) {
            logAgentBackupFileService.cpBackupFileToMainNode(dmPodIp, time, fileName, fileName, BackupFileType.DmType.getValue());
        }
        return runningDmTask;
    }

    public String uploadAndRestore(MultipartFile file, DBType dbType) {
        if (file == null) {
            throw new ServerException("Upload file is null");
        }
        String dmPodIp = getDmPodIp(dbType);
        uploadDmBackupFile(file, dmPodIp);
        return restore(file.getOriginalFilename(), dmPodIp);
    }

    private String getDmPodIp(DBType dbType) {
        String dmPodIp = null;
        switch (dbType) {
            case main:
                dmPodIp = serverNetworkService.getNetworkConfiguration().get(NetworkConstants.DATABASE_IP);
                break;
            case statis:
                dmPodIp = serverNetworkService.getNetworkConfiguration().get(NetworkConstants.STATIS_DATABASE_IP);
                break;
            default:
        }
        return dmPodIp;
    }

    public String restore(String restoreFileName, String dmPodIp) {
        if (restoreFileName == null || !restoreFileName.endsWith(".dmp")) {
            throw new WebException(ErrorStatus.FILE_PATH_ILLEGAL);
        }
        try {
            Map<String, String> map = serverNetworkService.getNetworkConfiguration();
            String mainIp = map.get(NetworkConstants.MAIN_INTERNAL_IP);
            RunningDmTask runningDmTask = restoreDm(restoreFileName, dmPodIp, mainIp);
            runningDmTaskHolder.setRunningDmTask(runningDmTask);
            return runningDmTask.uuid;
        } finally {
            runningDmTaskHolder.cancelAttemptToBeginDmTask();
        }
    }

    private void uploadDmBackupFile(MultipartFile file, String dmPodIp) {
        if (StringUtils.isBlank(dmPodIp)) {
            throw new ServerException("该服务数据库未启动!");
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        MultiValueMap<String, Object> parts = new LinkedMultiValueMap<>();
        parts.add(file.getOriginalFilename(), file.getResource());
        HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(parts, headers);
        String clientVersionUrl = "http://" + Ipv6Util.handlerIpv6Addr(dmPodIp) + ":" + NetworkConstants.LOGAGENT_PORT + "/dm/upload";
        logger.info("uploadDmBackupFile http url : " + clientVersionUrl);

        try {
            ResponseEntity<Void> responseEntity = restTemplate.postForEntity(clientVersionUrl, httpEntity, Void.class);
            HttpStatus status = responseEntity.getStatusCode();
            logger.info("uploadDmBackupFile status:{}", status);
            if (HttpStatus.INTERNAL_SERVER_ERROR == status || HttpStatus.EXPECTATION_FAILED == status) {
                throw new ServerException("上传文件校验失败!");
            }
            if (!status.is2xxSuccessful()) {
                throw new ServerException("上传文件失败!");
            }
        } catch (Exception e) {
            logger.error("uploadDmBackupFile error", e);
            throw new ServerException("上传文件失败!");
        }
    }

    private RunningDmTask backupDm(String fileName, String dmPodIp, String mainIp) {
        RunningDmTask baseTask = new RunningDmTask();
        baseTask.backupRun(dmPodIp, mainIp, fileName, restTemplate);
        return baseTask;
    }

    private RunningDmTask restoreDm(String restoreFileName, String dmPodIp, String mainIp) {
        RunningDmTask baseTask = new RunningDmTask();
        baseTask.restoreRun(dmPodIp, mainIp, restTemplate, restoreFileName);
        backUpNotifyService.checkBackUpNotify(BackupFileType.DmRestoreType, restoreFileName);
        return baseTask;
    }

    /**
     * 清理DM数据库备份文件
     */
    public void batchDeleteDamengBackupFile() {
        Arrays.stream(DamengDBType.values()).forEach(damengDBType -> batchDeleteBackupFile(damengDBType.getAllIpKey()));
    }

    public void batchDeleteBackupFile(String allIpKey) {
        Map<String, String> map = serverNetworkService.getNetworkConfiguration();
        String dmPodIp = map.get(allIpKey);
        if (StringUtils.isBlank(dmPodIp)) {
            return;
        }
        String url = "http://" + Ipv6Util.handlerIpv6Addr(dmPodIp) + ":" + NetworkConstants.LOGAGENT_PORT + "/dm/delete?expiredInterval=30";
        logger.info("batchDeleteBackupFile http url : " + url);
        try {
            ResponseEntity<Void> responseEntity = restTemplate.exchange(url, HttpMethod.GET, null, Void.class);
            logger.info("batchDeleteBackupFile status:{}", responseEntity.getStatusCode());
        } catch (Exception e) {
            logger.error("batchDeleteBackupFile fail,remote call error ", e);
        }
    }

    @Override
    public int getLicenseValidDays(String dbIp, String dbPort) throws Exception {
        String sql = "select EXPIRED_DATE from v$license;";
        Connection connection = null;
        PreparedStatement ps = null;
        ResultSet resultSet = null;
        try {
            connection = jdbcUtils.getBackupAccountConnection("DM", dbIp,  dbPort, "");
            ps = connection.prepareStatement(sql);
            resultSet = ps.executeQuery();
            while (resultSet.next()) {
                String expiredDateStr = resultSet.getString(1);
                if (StringUtils.isBlank(expiredDateStr)) {
                    return Integer.MAX_VALUE;
                }
                LocalDate expiredDate = LocalDate.parse(expiredDateStr);
                return (int) ChronoUnit.DAYS.between(LocalDate.now(), expiredDate);
            }
        } catch (Exception e) {
            log.error("Query [{}] error.\n", sql, e);
            throw e;
        } finally {
            jdbcUtils.close(connection, ps, resultSet);
        }
        return Integer.MIN_VALUE;
    }

}
