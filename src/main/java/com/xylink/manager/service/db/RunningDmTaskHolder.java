package com.xylink.manager.service.db;

import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

@Service
public class RunningDmTaskHolder {
    private static final String DM_NAME = "dm";
    private static final ConcurrentHashMap<String, AtomicReference<RunningDmTask>> runningDBTasks = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, AtomicBoolean> tryToBeginDBTaskMarks = new ConcurrentHashMap<>();

    static {
        //初始化
        runningDBTasks.put(DM_NAME, new AtomicReference<>(new RunningDmTask(1L)));
        tryToBeginDBTaskMarks.put(DM_NAME, new AtomicBoolean());
    }

    public boolean attemptToBeginDmTask() {
        if (tryToBeginDBTaskMarks.get(DM_NAME).compareAndSet(false, true)) {
            if (!hasRunningDmTask()) {
                return true;
            } else {
                tryToBeginDBTaskMarks.get(DM_NAME).set(false);
            }
        }
        return false;
    }

    public boolean hasRunningDmTask() {
        return !getRunningDmTask().isCompleted();
    }

    public void cancelAttemptToBeginDmTask() {
        tryToBeginDBTaskMarks.get(DM_NAME).set(false);
    }

    public void setRunningDmTask(RunningDmTask runningDmTask) {
        runningDBTasks.get(DM_NAME).set(runningDmTask);
    }

    public RunningDmTask getRunningDmTask() {
        return runningDBTasks.get(DM_NAME).get();
    }
}
