package com.xylink.manager.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.xylink.config.Constants;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.config.exception.basic.ServiceErrorException;
import com.xylink.manager.controller.dto.ConfigDto;
import com.xylink.manager.model.GreyLevel;
import com.xylink.manager.model.GreyMinVersion;
import com.xylink.manager.model.GreyNemo;
import com.xylink.manager.model.PlatformGreyVersion;
import com.xylink.manager.service.base.K8sService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class GreyReleaseService {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private K8sService k8sService;
    @Autowired
    private ServerListService serverListService;

    private final String FULL_UPGRADE = "full_upgrade";

    private static final Logger logger = LoggerFactory.getLogger(GreyReleaseService.class);

    public void addOrChangeNemoGrade(String[] sns, int grade) {
        String internalNginxUrl = serverListService.getInternalNginxUrl();

        String url = internalNginxUrl + "/api/rest/internal/v1/vcs/greyList/saveOrUpdate";
        MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
        param.add("level", grade);
        param.add("type", 3);
        for (String sn : sns) {
            param.add("params", sn.trim());
        }
        try {
            restTemplate.postForEntity(url, param, null);
        } catch (Exception e) {
            logger.error("fail to add or update nemo grey level for sn:" + sns, e);
            throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
        }
    }

    public void deleteNemoGrade(String sn) {
        if (!StringUtils.isEmpty(sn)) {
            String internalNginxUrl = serverListService.getInternalNginxUrl();
            String url = internalNginxUrl + "/api/rest/internal/v1/vcs/greyList/delete";
            MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
            param.add("param", sn);
            param.add("type", 3);
            try {
                restTemplate.postForEntity(url, param, null);
            } catch (Exception e) {
                logger.error("fail to delete nemo grey level for sn:" + sn, e);
                throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
            }
        }
    }

    public void addGreyAppName(String appName) {
        if (!StringUtils.isEmpty(appName)) {
            String internalNginxUrl = serverListService.getInternalNginxUrl();
            String url = internalNginxUrl + "/api/rest/internal/v1/vcs/greyPlatform/add";
            MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
            param.add("platform", appName.trim());
            try {
                restTemplate.postForEntity(url, param, null);
            } catch (Exception e) {
                logger.error("fail to add grey platform for " + appName, e);
                throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
            }
        }
    }

    public void deleteGreyAppName(String appName) {
        if (!StringUtils.isEmpty(appName)) {
            String internalNginxUrl = serverListService.getInternalNginxUrl();
            String url = internalNginxUrl + "/api/rest/internal/v1/vcs/greyPlatform/delete";
            MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
            param.add("platform", appName);
            try {
                restTemplate.postForEntity(url, param, null);
            } catch (Exception e) {
                logger.error("fail to delete grey platform for " + appName, e);
                throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
            }
        }
    }

    public List<String> getAppNameSet() {
        String internalNginxUrl = serverListService.getInternalNginxUrl();
        String url = internalNginxUrl + "/api/rest/internal/v1/vcs/greyPlatform/list";
        ResponseEntity<String[]> responseEntity = null;
        try {
            responseEntity = restTemplate.getForEntity(url, String[].class);
            if (HttpStatus.OK == responseEntity.getStatusCode() && responseEntity.getBody() != null) {
                return Arrays.asList(responseEntity.getBody());
            }
        } catch (Exception e) {
            logger.error("fail to list all grey platform !", e);
        }
        throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
    }

    public List<GreyNemo> getReleaseGrade() {
        String internalNginxUrl = serverListService.getInternalNginxUrl();
        String url = internalNginxUrl + "/api/rest/internal/v1/vcs/greyList/listAll?type=3";
        ResponseEntity<GreyNemo[]> responseEntity;
        try {
            responseEntity = restTemplate.getForEntity(url, GreyNemo[].class);

            if (HttpStatus.OK == responseEntity.getStatusCode() && responseEntity.getBody() != null) {
                return Arrays.asList(responseEntity.getBody());
            }
        } catch (Exception e) {
            logger.error("fail to list all grey list !", e);
        }
        throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
    }

    public List<String> getReleaseGradeBylevel(int level) {
        String internalNginxUrl = serverListService.getInternalNginxUrl();
        String url = internalNginxUrl + "/api/rest/internal/v1/vcs/greyList/listByLevel?type=3&level=" + level;
        ResponseEntity<String[]> responseEntity;
        try {
            responseEntity = restTemplate.getForEntity(url, String[].class);

            if (HttpStatus.OK == responseEntity.getStatusCode() && responseEntity.getBody() != null)
                return Arrays.asList(responseEntity.getBody());
        } catch (Exception e) {
            logger.error("fail to list all nemo grey list for level: " + level, e);
        }
        throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
    }

    public void setGradeAndVersionMapping(String effectiveStartTime, int grade, String appName, String version, String description, Boolean customized, String customizedKey) {
        String internalNginxUrl = serverListService.getInternalNginxUrl();
        String url = internalNginxUrl + "/api/rest/internal/v1/vcs/greyLevel/saveOrUpdate?level=" + grade + "&platform=" +
                appName + "&version=" + version;
        if (customized) {
            if (StringUtils.isBlank(customizedKey)) {
                throw new ServerException("当前环境未接入定制版customizedKey");
            }
            url += "&customizedkey=" + customizedKey;
        }
        if (StringUtils.isNotBlank(effectiveStartTime)) {
            url += "&effectiveStartTime=" + effectiveStartTime;
        }
        if (StringUtils.isNotBlank(description)) url += "&remark=" + description;
        try {
            restTemplate.getForEntity(url, String.class);

        } catch (Exception e) {
            logger.error("fail to save or update grey level for platform : " + appName, e);
            throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
        }
    }

    public void removeGradeAndVersionMapping(int grade, String appName, Boolean customized, String customizedkey, Boolean isForceUpdateDel) {
        String internalNginxUrl = serverListService.getInternalNginxUrl();
        String url = internalNginxUrl + "/api/rest/internal/v1/vcs/greyLevel/delete";
        try {
            MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
            param.add("platform", appName);
            param.add("level", grade);
            if (customized && isForceUpdateDel) {
                String customizedKey = k8sService.getConfigmap("private-manager-data").get("CustomizedKey");
                param.add("customizedkey", customizedKey);
            } else {
                param.add("customizedkey", customizedkey);
            }
            restTemplate.postForEntity(url, param, Void.class);
        } catch (Exception e) {
            logger.error("fail to remove grey level for " + appName + " : " + grade, e);
            throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
        }
    }


    public List<GreyLevel> getGradeAndVersionMapping(String platform) {
        List<String> filterStrings = new ArrayList<>();
        Constants.equipmentTypeToPlatform.forEach((key, value) -> {
            if (StringUtils.isBlank(platform)) {
                filterStrings.add(value);
            } else if (key.contains(platform)) {
                filterStrings.add(value);
            }
        });
        String internalNginxUrl = serverListService.getInternalNginxUrl();
        String url = internalNginxUrl + "/api/rest/internal/v1/vcs/greyLevel/listAll";
        ResponseEntity<GreyLevel[]> responseEntity;
        List<GreyLevel> greyLevels = new ArrayList<>();
        List<GreyLevel> result = new ArrayList<>();
        try {
            responseEntity = restTemplate.getForEntity(url, GreyLevel[].class);
            if (HttpStatus.OK == responseEntity.getStatusCode() && responseEntity.getBody() != null) {
                greyLevels = Arrays.asList(responseEntity.getBody());
                greyLevels.stream().forEach(greyLevel -> {
                    for (String filterString : filterStrings) {
                        if (greyLevel.getPlatform().equalsIgnoreCase(filterString)) {
                            result.add(greyLevel);
                            break;
                        }
                    }
                });
            }
        } catch (Exception e) {
            logger.error("fail to list all grey level!", e);
            throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
        }
        return result;
    }

    /**
     * 指定灰度版本，强制升级
     */
    public void forceUpdate(String platform, String version, int greyLevel, Boolean customized, String customizedKey) {
        try {
            String internalNginxUrl = serverListService.getInternalNginxUrl();
            String url = internalNginxUrl + "/api/rest/internal/v1/vcs/minVersion/add";
            ObjectNode object = JsonNodeFactory.instance.objectNode();
            object.put("platform", platform);
            object.put("minVersion", convertMinVersion(version));
            object.put("greyLevel", greyLevel);
            object.put("strategy", 2);
            if (customized) {
                if (StringUtils.isBlank(customizedKey)) {
                    throw new ServerException("当前环境未接入定制版customizedKey");
                }
                object.put("customizedKey", customizedKey);
            }
            String body = object.toString();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> requestEntity = new HttpEntity<>(body, headers);

            ResponseEntity<JsonNode> entity = restTemplate.postForEntity(url, requestEntity, JsonNode.class);
            logger.info("forceUpdate result:{}", entity.getBody().toString());
            if (entity.getBody().get("status").asInt() != 200) {
                removeGradeAndVersionMapping(greyLevel, platform, customized, null, true);
                throw new ServerException(entity.getBody().get("message").asText());
            }
        } catch (RestClientException e) {
            logger.error("forceUpdate error : {}", e.getMessage());
            removeGradeAndVersionMapping(greyLevel, platform, customized, null, true);
            throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
        }
    }

    /**
     * 格式如：102.29.0.55524 或者 102.29.1-111
     */
    private int convertMinVersion(String version) {
        String[] ary = version.split("\\.");
        return Integer.parseInt(ary[0]) * 10000 + Integer.parseInt(ary[1]) * 100 + Integer.parseInt(ary[2].split("-")[0]);
    }

    /**
     * 删除强制升级
     */
    public void deleteForceUpdate(String forceUpdateId) {
        String internalNginxUrl = serverListService.getInternalNginxUrl();
        String delUrl = internalNginxUrl + "/api/rest/internal/v1/vcs/minVersion/" + forceUpdateId;
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(headers);
        ResponseEntity<Void> res = restTemplate.exchange(delUrl, HttpMethod.DELETE, entity, Void.class);

        if (!res.getStatusCode().is2xxSuccessful()) {
            throw new ServerException("强制升级控制删除失败");
        }
    }

    /**
     * 批量获取灰度版本是否强制升级
     */
    public GreyMinVersion[] getForceUpdateList() {
        try {
            String internalNginxUrl = serverListService.getInternalNginxUrl();
            String url = internalNginxUrl + "/api/rest/internal/v1/vcs/minVersion/listAll";
            ResponseEntity<GreyMinVersion[]> response = restTemplate.getForEntity(url, GreyMinVersion[].class);
            if (response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.NO_CONTENT) {
                return response.getBody();
            }
        } catch (RestClientException e) {
            logger.error("getForceUpdateList error : {}", e.getMessage());

        }
        throw new ServerException(ErrorStatus.UNEXPECTED_ERROR);
    }

    /**
     * 刷新缓存
     * 通用platform=android_office&version=103.2.3-14852   定制platform=android_office&version=ck/103.2.3-14852
     */
    public void synJson(PlatformGreyVersion version) {
        String internalNginxUrl = serverListService.getInternalNginxUrl();
        String url = internalNginxUrl + "/api/rest/internal/v1/vcs/version/synjson?type=0&platform=" + version.getPlatform();
        String ckVersion;
        if (version.getIsCustomized()) {
            String customizedKey = version.getCustomizedKey();
            if (StringUtils.isBlank(customizedKey)) {
                throw new ServerException("当前环境未接入定制版customizedKey");
            }
            if (StringUtils.equals(version.getPubType(), FULL_UPGRADE)) {
                ckVersion = customizedKey;
            } else {
                ckVersion = customizedKey + "/" + version.getVersion();
            }
            url += "&version=" + ckVersion;
        } else {
            if (!StringUtils.equals(version.getPubType(), FULL_UPGRADE)) {
                url += "&version=" + version.getVersion();
            }
        }
        logger.info("synJson url:{}", url);
        restTemplate.getForEntity(url, Void.class);
    }

    public void updateIosMaxVersion(List<ConfigDto> configDtos) {
        String internalNginxUrl = serverListService.getInternalNginxUrl();
        String url = internalNginxUrl + "/api/rest/vcs/internal/common/config/save/batch/v1";
        try {
            restTemplate.postForObject(url, configDtos, Void.class);
        } catch (Exception e) {
            logger.error("updateIosMaxVersion failed", e);
            throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_VCS_FAILED);
        }
    }

    public ConfigDto[] getIosMaxVersion(List<String> keys) {
        String internalNginxUrl = serverListService.getInternalNginxUrl();
        String url = internalNginxUrl + "/api/rest/vcs/internal/common/config/list/v1";
        try {
            return restTemplate.postForObject(url, keys, ConfigDto[].class);
        } catch (Exception e) {
            logger.error("getIosMaxVersion failed", e);
            throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_VCS_FAILED);
        }
    }

    public boolean existedIosMaxVersion(String key) {
        List<String> keys = Arrays.asList(key);
        try {
            ConfigDto[] iosMaxVersion = this.getIosMaxVersion(keys);
            if (iosMaxVersion == null || iosMaxVersion.length == 0) {
                return false;
            }
            ConfigDto configDto = iosMaxVersion[0];
            if (StringUtils.equals(key, configDto.getConfigKey()) && StringUtils.isNotBlank(configDto.getConfigValue())) {
                return true;
            }
        } catch (Exception e) {
            logger.error("existedIosMaxVersion failed", e);
        }
        return false;
    }

}