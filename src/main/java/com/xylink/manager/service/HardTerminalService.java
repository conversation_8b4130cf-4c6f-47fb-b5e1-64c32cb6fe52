package com.xylink.manager.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.config.exception.basic.ClientErrorException;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.ServerException;
import com.xylink.config.exception.basic.ServiceErrorException;
import com.xylink.manager.controller.dto.*;
import com.xylink.manager.inspection.utils.JsonUtils;
import com.xylink.manager.iptables.dto.TotalRuleDTO;
import com.xylink.manager.model.ClientAccess;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.base.K8sSvcService;
import com.xylink.manager.service.base.SystemModeConfig;
import com.xylink.manager.service.remote.common.SubtypeManagerRemoteClient;
import com.xylink.manager.service.remote.common.dto.SubtypeInfoDto;
import com.xylink.manager.service.remote.pivotor.PivotorRemoteClient;
import com.xylink.util.Ipv6Util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Date: 2022/07/26/11:42
 * @Description:
 */
@Service
public class HardTerminalService {

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private K8sSvcService k8sSvcService;

    @Autowired
    private ServerListService serverListService;

    @Autowired
    private K8sService k8sService;

    @Autowired
    private PivotorRemoteClient pivotorRemoteClient;

    @Autowired
    private SubtypeManagerRemoteClient subtypeManagerRemoteClient;

    private static final String SECRET_KEY = "gzrs+hKGul3TzdIjSw8v1Oa5h2umPV3V3gl0dznRa/g=";

    private static final Logger logger = LoggerFactory.getLogger(HardTerminalService.class);

    public HardTerminalUpgradeConfig[] getTerminalUpgradeInfo() {
        String mainIp = serverListService.getMainNodeInternalIP();
        String url = "http://" + mainIp + ":11111/api/rest/internal/v1/vcs/supportClient/listAll";
        return restTemplate.getForObject(url, HardTerminalUpgradeConfig[].class);
    }

    /**
     * 获取客户端上传的平台名称列表
     * @return
     */
    public List<ClientAccess> getPlatformListOfClientUpload() {
        TerminalVersionInfo[] versionInfo = getVersionInfo();
        List<String> clientList = Arrays.stream(versionInfo).filter(TerminalVersionInfo::check).map(TerminalVersionInfo::getClientName).collect(Collectors.toList());
        List<ClientAccess> res = new ArrayList<>();
        clientList.forEach(x->{res.add(new ClientAccess(x));});

        return res;
    }

    public List<PlatformToVersion> getPlatformToVersion(String type) {
        List<PlatformToVersion> res = new ArrayList<>();

        TerminalVersionInfo[] versionInfos = getVersionInfo();
        List<TerminalVersionInfo> filteredInfos = Arrays.stream(versionInfos).filter(x -> type.equals(x.getClientName())).collect(Collectors.toList());
        for (TerminalVersionInfo temp : filteredInfos) {
            PlatformToVersion platformToVersion = new PlatformToVersion();
            platformToVersion.setPlatform(temp.getGreyName());
            platformToVersion.setSoftVersion(temp.getLastVersion());
            res.add(platformToVersion);
        }

        if (CollectionUtils.isEmpty(res)) {
            throw new ClientErrorException(ErrorStatus.TERMINAL_TO_PLATFORM_NOT_EXIST);
        }
        return res;
    }

    public List<PlatformToVersion> getPlatformToVersion() {
        List<PlatformToVersion> res = new ArrayList<>();

        TerminalVersionInfo[] versionInfos = getVersionInfo();
        for (TerminalVersionInfo temp : versionInfos) {
            PlatformToVersion platformToVersion = new PlatformToVersion();
            platformToVersion.setDeviceSeries(temp.getClientName());
            platformToVersion.setPlatform(temp.getGreyName());
            platformToVersion.setSoftVersion(temp.getLastVersion());
            res.add(platformToVersion);
        }

        return res;
    }

    /**
     * 检查当前版本信息versionInfo的clientName 是否包含type，要精准匹配
     * 比如 ME40/AE40/AE42 OS 是否包含ME40
     * @param versionInfo
     * @param type
     * @return
     */
    private boolean checkClientName(TerminalVersionInfo versionInfo,String type) {
        String[] clientNameArr = versionInfo.getClientName().split("/");
        for (String clientName : clientNameArr) {
            if (clientName.contains(type)) {
                return true;
            }
        }
        return false;
    }


    private TerminalVersionInfo[] getVersionInfo() {
        String server;
        if(SystemModeConfig.isNewCms()){
            String mainIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP).get(NetworkConstants.MAIN_INTERNAL_IP);
            server = k8sSvcService.getLogAgentPodIpByNodeIp(mainIp);
        }else {
            server = "127.0.0.1";
        }

        String clientVersionUrl = "http://" + Ipv6Util.handlerIpv6Addr(server) + ":" + NetworkConstants.LOGAGENT_PORT + "/version/v2/client/";

        try {
            return restTemplate.getForObject(clientVersionUrl, TerminalVersionInfo[].class);
        }catch(Exception e){
            logger.error("get version info failed", e);
            throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_LOGAGENT_FAILED);
        }
    }

    public void addOrUpdateTerminal(HardTerminalUpgradeConfig upgradeConfig) {
        String mainIp = serverListService.getMainNodeInternalIP();
        String url = "http://" + mainIp + ":11111/api/rest/internal/v1/vcs/supportClient/addOrUpdate";
        UpgradeExcludeIdDto upgradeExcludeIdDto = UpgradeExcludeIdDto.transform(upgradeConfig);
        try {
            restTemplate.postForObject(url, upgradeExcludeIdDto, Void.class);
        } catch (Exception e) {
            logger.error("add or update terminal upgrade config failed", e);
            throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_VCS_FAILED);
        }
    }

    public void deleteTerminal(String id) {
        String mainIp = serverListService.getMainNodeInternalIP();
        String url = "http://" + mainIp + ":11111/api/rest/internal/v1/vcs/supportClient/delete?id=" + id;
        try {
            restTemplate.getForObject(url, Void.class);
        } catch (Exception e) {
            logger.error("delete terminal upgrade config failed", e);
            throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_VCS_FAILED);
        }
    }

    public List<HardTerminalTypeDto> getCurrentHardTerminalList() {
        try {
            List<HardTerminalTypeDto> list = pivotorRemoteClient.getCurrentHardTerminalList();
            return list.stream().filter(x -> (x.getType() == 2 || x.getType() == 8 || x.getType() == 7) && !Objects.equals(x.getSubType(), x.getType())).sorted(Comparator.comparing(HardTerminalTypeDto::getType)).collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("get current hard terminal list failed,", e);
            throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_PIVOTOR_FAILED);
        }
    }

    public void subtypesUpload(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            SubtypeInfoDto subtypeInfoDto = JsonUtils.toPojo(inputStream, new TypeReference<SubtypeInfoDto>() {
            });
            String sign = sign(Objects.requireNonNull(subtypeInfoDto.calucateSignData()));
            if (!subtypeInfoDto.getSign().equals(sign)) {
                throw new ServerException(ErrorStatus.FILE_ILLEGAL);
            }
            subtypeManagerRemoteClient.importSubtype(subtypeInfoDto);
        } catch (IOException e) {
            throw new ServerException(e, ErrorStatus.UNEXPECTED_ERROR);
        }
    }

    public String subtypesDownload(Integer subtype){
        SubtypeInfoDto data = subtypeManagerRemoteClient.exportSubtype(subtype);
        data.setSign(sign(Objects.requireNonNull(data.calucateSignData())));
        return JsonUtils.objectToJsonString(data);
    }

    private String sign(String signStr) {
        // 使用HMAC-SHA256算法计算签名
        try {
            Mac hmacSha256 = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(SECRET_KEY.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            hmacSha256.init(secretKey);
            // 计算签名
            byte[] hash = hmacSha256.doFinal(signStr.getBytes(StandardCharsets.UTF_8));
            // 转换为十六进制字符串
            return bytesToHex(hash);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new ServerException(e, ErrorStatus.UNEXPECTED_ERROR);
        }
    }

    /**
     * 字节数组转十六进制字符串
     *
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }
}
