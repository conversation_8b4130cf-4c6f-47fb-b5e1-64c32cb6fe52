//package com.xylink.manager.service;
//
//import com.jayway.jsonpath.JsonPath;
//import com.jayway.jsonpath.ReadContext;
//import com.xylink.config.StatisItem;
//import com.xylink.manager.controller.dto.StatisDto;
//import com.xylink.manager.model.HeapsterResult;
//import com.xylink.manager.model.HeapsterSerie;
//import com.xylink.manager.model.HeapsterSeries;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.ResponseEntity;
//import org.springframework.stereotype.Service;
//import org.springframework.web.client.RestTemplate;
//
//import java.net.URI;
//import java.text.SimpleDateFormat;
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//
///**
// * Created by ha<PERSON><PERSON> on 2017/8/23.
// */
//@Service
//public class HybridService {
//    private static final Logger logger = LoggerFactory.getLogger(HybridService.class);
//
//    @Autowired
//    private RestTemplate restTemplate;
//
//    @Value("${k8s.heapster.query.prefix.url}")
//    private String heapsterQueryPrefix;
//
//    @Value("${k8s.heapster.prefix.url}")
//    private String heapsterPrefix;
//
//
//    private static final String nodeNamesSuffix             = "/api/v1/model/nodes";
//    private static final String podNamesSuffix              = "/api/v1/model/namespaces/default/pods";
//
//    // suffix section 1
//    private static final String query_suffix                = "/query?u=root&p=root&db=k8s&q=SELECT%20sum(%22value%22)%20FROM%20%22";
//    private static final String getQuery_suffix_ext         = "/api/datasources/proxy/1/query?db=k8s&q=SELECT%20sum(%22value%22)%20FROM%20%22";
//
//    // suffix section 2
//    private static final String query_node_cpu_suffix       = "%2Fusage_rate%22%20WHERE%20%22type%22%20%3D%20%27node%27%20AND%20%22nodename%22%20%3D~%20%2F";
//    private static final String query_node_usage_suffix     = "%2Fusage%22%20WHERE%20%22type%22%20%3D%20%27node%27%20AND%20%22nodename%22%20%3D~%20%2F";
//    private static final String query_node_limit_suffix     = "%2Flimit%22%20WHERE%20%22type%22%20%3D%20%27node%27%20AND%20%22nodename%22%20%3D~%20%2F";
//    private static final String query_node_tx_rate_suffix   = "%2Ftx_rate%22%20WHERE%20%22type%22%20%3D%20%27node%27%20AND%20%22nodename%22%20%3D~%20%2F";
//    private static final String query_node_rx_rate_suffix   = "%2Frx_rate%22%20WHERE%20%22type%22%20%3D%20%27node%27%20AND%20%22nodename%22%20%3D~%20%2F";
//
//    private static final String query_pod_cpu_suffix        = "%2Fusage_rate%22%20WHERE%20%22type%22%20%3D%20%27pod_container%27%20AND%20%22namespace_name%22%20%3D~%20%2Fdefault%24%2F%20AND%20%22pod_name%22%20%3D~%20%2F";
//    private static final String query_pod_usage_suffix      = "%2Fusage%22%20WHERE%20%22type%22%20%3D%20%27pod_container%27%20AND%20%22namespace_name%22%20%3D~%20%2Fdefault%24%2F%20AND%20%22pod_name%22%20%3D~%20%2F";
//    private static final String query_pod_fs_usage_suffix   = "%2Fusage%22%20WHERE%20%22type%22%20%3D%20%27pod%27%20AND%20%22namespace_name%22%20%3D~%20%2Fdefault%24%2F%20AND%20%22pod_name%22%20%3D~%20%2F";
//
//    private static final String query_pod_tx_rate_suffix    = "%2Ftx_rate%22%20WHERE%20%22type%22%20%3D%20%27pod%27%20AND%20%22namespace_name%22%20%3D~%20%2Fdefault%24%2F%20AND%20%22pod_name%22%20%3D~%20%2F";
//    private static final String query_pod_rx_rate_suffix    = "%2Frx_rate%22%20WHERE%20%22type%22%20%3D%20%27pod%27%20AND%20%22namespace_name%22%20%3D~%20%2Fdefault%24%2F%20AND%20%22pod_name%22%20%3D~%20%2F";
//
//    // suffix section 3
//    private static final String query_node_suffix           = "%24%2F%20AND%20time%20%3E%20now()%20-%2030m%20GROUP%20BY%20time(1m)%2C%20%22nodename%22%20fill(null)&epoch=ms";
//    private static final String query_pod_suffix            = "%24%2F%20AND%20time%20%3E%20now()%20-%2030m%20GROUP%20BY%20time(1m)%2C%20%22container_name%22%20fill(null)&epoch=ms";
//    private static final String query_pod_fs_nt_suffix      = "%24%2F%20AND%20time%20%3E%20now()%20-%2030m%20GROUP%20BY%20time(1m)%20fill(null)&epoch=ms";
//
//    private static String nodes_capacity = "api/v1/model/nodes/%s/metrics/%s";
//
//    protected SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
//
//    private static final String resource_type_node = "node";
//    private static final String resource_type_pod = "pod";
//
//
//    public boolean enabledHybrid(String enterpriseId) {
////        List<HybridResourcePO> resourcePOS = hybridResourceRepository.findByEnterpriseId(enterpriseId);
////
////        if (resourcePOS != null && !resourcePOS.isEmpty()) {
////            return true;
////        }
////
////        return false;
//        return true;
//    }
//
//    public List<String> getNodeNames() {
//        List<String> nameList = getPropertyList(nodeNamesSuffix);
//
////        List<HybridResourcePO> resourcePOS = hybridResourceRepository.findByEnterpriseIdAndResourceType(enterpriseId, resource_type_node);
////        List<String> nameList = new ArrayList<>();
////        for (HybridResourcePO resourcePO : resourcePOS) {
////            nameList.add(resourcePO.getResourceName());
////        }
////
////        logger.info("Get enterprise[" + enterpriseId + "] all node names: " + nameList.toString());
//        return nameList;
//    }
//
//    public List<String> getPropertyList(String suffix) {
//        String requestUrl = heapsterPrefix + suffix;
//        try {
//            ResponseEntity<List> responseEntity = restTemplate.getForEntity(requestUrl, List.class);
//            if (HttpStatus.OK != responseEntity.getStatusCode()) {
//                logger.error(requestUrl + " error and responseEntity = " + responseEntity.toString());
//            }
//            else {
//                return responseEntity.getBody();
//            }
//        }
//        catch (Exception e){
//            logger.error("Fail to get " + requestUrl, e);
//        }
//        return new ArrayList<>();
//    }
//
//    public List<String> getPodNamesOfDefaultSpaceName() {
//        List<String> nameList = getPropertyList(podNamesSuffix);
//
////        List<HybridResourcePO> resourcePOS = hybridResourceRepository.findByEnterpriseIdAndResourceType(enterpriseId, resource_type_pod);
////        List<String> nameList = new ArrayList<>();
////        for (HybridResourcePO resourcePO : resourcePOS) {
////            nameList.add(resourcePO.getResourceName());
////        }
////
////        logger.info("Get enterprise[" + enterpriseId + "] default namespace's pod names: " + nameList.toString());
//        return nameList;
//    }
//
//
//    private String generateQueryUrl(StatisItem item, String name) {
//        StringBuffer urlBuffer = new StringBuffer();
//        urlBuffer.append(query_suffix);
//        switch (item) {
//            case NODE_CPU:
//                urlBuffer.append("cpu");
//                urlBuffer.append(query_node_cpu_suffix);
//                urlBuffer.append(name);
//                urlBuffer.append(query_node_suffix);
//                break;
//            case NODE_MEMORY:
//                urlBuffer.append("memory");
//                urlBuffer.append(query_node_usage_suffix);
//                urlBuffer.append(name);
//                urlBuffer.append(query_node_suffix);
//                break;
//            case NODE_FILESYSTEM:
//                urlBuffer.append("filesystem");
//                urlBuffer.append(query_node_usage_suffix);
//                urlBuffer.append(name);
//                urlBuffer.append(query_node_suffix);
//                break;
//            case NODE_NETWORK_TX:
//                urlBuffer.append("network");
//                urlBuffer.append(query_node_tx_rate_suffix);
//                urlBuffer.append(name);
//                urlBuffer.append(query_node_suffix);
//                break;
//            case NODE_NETWORK_RX:
//                urlBuffer.append("network");
//                urlBuffer.append(query_node_rx_rate_suffix);
//                urlBuffer.append(name);
//                urlBuffer.append(query_node_suffix);
//                break;
//            case POD_CPU:
//                urlBuffer.append("cpu");
//                urlBuffer.append(query_pod_cpu_suffix);
//                urlBuffer.append(name);
//                urlBuffer.append(query_pod_suffix);
//                break;
//            case POD_MEMORY:
//                urlBuffer.append("memory");
//                urlBuffer.append(query_pod_usage_suffix);
//                urlBuffer.append(name);
//                urlBuffer.append(query_pod_suffix);
//                break;
//            case POD_FILESYSTEM:
//                urlBuffer.append("filesystem");
//                urlBuffer.append(query_pod_fs_usage_suffix);
//                urlBuffer.append(name);
//                urlBuffer.append(query_pod_fs_nt_suffix);
//                break;
//            case POD_NETWORK_TX:
//                urlBuffer.append("network");
//                urlBuffer.append(query_pod_tx_rate_suffix);
//                urlBuffer.append(name);
//                urlBuffer.append(query_pod_fs_nt_suffix);
//                break;
//            case POD_NETWORK_RX:
//                urlBuffer.append("network");
//                urlBuffer.append(query_pod_rx_rate_suffix);
//                urlBuffer.append(name);
//                urlBuffer.append(query_pod_fs_nt_suffix);
//                break;
//            default:
//                logger.error("Exception item: " + item);
//                return null;
//        }
//
//        return urlBuffer.toString();
//    }
//
//    public StatisDto getItemUsageByQuery(StatisItem item, String itemName) {
//        StatisDto statisDto = new StatisDto();
//        String requestUrl = generateQueryUrl(item, itemName);
//        //logger.info(item.name() + " | " + itemName + " | " + requestUrl);
//        HeapsterResult result = getHeapsterResult(requestUrl);
//
//
//        if (result != null && result.getResults() != null && !result.getResults().isEmpty()) {
//            HeapsterSeries series = result.getResults().get(0);
//            if (series != null && series.getSeries() != null && !series.getSeries().isEmpty()) {
//                HeapsterSerie serie = series.getSeries().get(0);
//                if (serie != null) {
//                    List<String> timestamps = new ArrayList<>();
//                    List<Float> values = new ArrayList<>();
//
//                    List<List<Long>> valuesList = serie.getValues();
//                    for (List<Long> valueList : valuesList) {
//                        Long timeMs = 0L, usage = 0L;
//                        if (valueList.size() == 2) {
//                            timeMs = valueList.get(0);
//                            usage = valueList.get(1);
//                            if (timeMs == null || usage == null) {
//                                continue;
//                            }
//                        }
//                        else {
//                            continue;
//                        }
//
//                        Date time = new Date(timeMs);
//                        timestamps.add(sdf.format(time));
//                        values.add(Float.parseFloat("" + usage));
//                    }
//
//                    statisDto.setSeries(values);
//                    statisDto.setxAxis(timestamps);
//                }
//            }
//            else {
//                logger.error("Service : " + itemName + ", check point: " + item + " is null");
//                return null;
//            }
//        }
//
//        return statisDto;
//    }
//
//
//    public HeapsterResult getHeapsterResult(String suffix) {
//        String requestUrl = heapsterQueryPrefix + suffix;
//        try {
//            URI uri = URI.create(requestUrl);
//            HeapsterResult reult = restTemplate.getForObject(uri, HeapsterResult.class);
//
//            return reult;
//        }
//        catch (Exception e){
//            logger.error("Fail to get " + requestUrl, e);
//        }
//        return null;
//    }
//
//    public Long getNodeItemLimit(String capa, String nodeName) {
//
//        String nodeItemUrl  = heapsterPrefix + String.format(nodes_capacity, nodeName, capa);
//        String resultStr  = restTemplate.getForObject(nodeItemUrl, String.class);
//        ReadContext ctx = JsonPath.parse(resultStr);
//        List<Long> valueList = ctx.read("$.metrics[-1:].value");
//        Long result = Long.valueOf(""+valueList.get(0));
//
//        return result;
//    }
//
//}
