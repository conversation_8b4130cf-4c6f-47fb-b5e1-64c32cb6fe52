package com.xylink.manager.service.ecvs;

import com.xylink.config.NetworkConstants;
import com.xylink.manager.model.em.BackupFileType;
import com.xylink.manager.model.em.DBType;
import com.xylink.manager.service.BackUpNotifyService;
import com.xylink.manager.service.ServerNetworkService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.hbase.HBaseService;
import com.xylink.manager.service.remote.logagent.LogAgentBackupFileService;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class HBaseOpsService {
    private static final Logger logger = LoggerFactory.getLogger(HBaseOpsService.class);

    @Autowired
    private K8sService k8sService;
    @Autowired
    private HBaseService hBaseService;
    @Autowired
    private BackUpNotifyService backUpNotifyService;
    @Autowired
    private ServerNetworkService serverNetworkService;
    @Autowired
    private LogAgentBackupFileService logAgentBackupFileService;

    private static final int expiredInterval = 7;

    public void backup(String fileName, String time) {
        if (!k8sService.getDbPodStatus(DBType.hbase.name(), Strings.EMPTY)) {
            return;
        }

        String hBasePodIp = serverNetworkService.getNetworkConfiguration().get(NetworkConstants.HBASE_IP);
        try {
            hBaseService.backup(fileName);
            if (backUpNotifyService.checkBackUpNotify(BackupFileType.HbaseType, fileName)) {
                logger.warn("hbase backup task is not completed, {}", fileName);
                return;
            }
            logAgentBackupFileService.cpBackupFileToMainNode(hBasePodIp, time, fileName, fileName, BackupFileType.HbaseType.getValue());
        } catch (Exception e) {
            logger.warn("hbase backup error", e);
        }
    }

    public void restore(String fileName, String time) {
        if (!k8sService.getDbPodStatus(DBType.hbase.name(), Strings.EMPTY)) {
            return;
        }

        String hBasePodIp = serverNetworkService.getNetworkConfiguration().get(NetworkConstants.HBASE_IP);
        try {
            logAgentBackupFileService.cpBackFileToOriginalNode(hBasePodIp, time, fileName, BackupFileType.HbaseType.getValue());
            hBaseService.restore(fileName);
        } catch (Exception e) {
            logger.error("restore error", e);
        }
    }

    /**
     * 清除hbase过期备份文件
     */
    public void removeExpiredBackupFile() {
        if (!k8sService.getDbPodStatus(DBType.hbase.name(), Strings.EMPTY)) {
            return;
        }
        String hBasePodIp = serverNetworkService.getNetworkConfiguration().get(NetworkConstants.HBASE_IP);
        try {
            logAgentBackupFileService.removeExpiredBackupFile(hBasePodIp, BackupFileType.HbaseType.getValue(), expiredInterval);
        } catch (Exception e) {
            logger.error("remove expired hbase backup file error, ", e);
        }
    }

}
