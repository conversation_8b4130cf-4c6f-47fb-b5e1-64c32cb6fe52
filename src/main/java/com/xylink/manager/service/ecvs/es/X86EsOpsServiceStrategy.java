package com.xylink.manager.service.ecvs.es;

import com.xylink.config.NetworkConstants;
import com.xylink.manager.model.em.BackupFileType;
import com.xylink.manager.service.BackUpNotifyService;
import com.xylink.manager.service.ServerNetworkService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.ecvs.IOpsService;
import com.xylink.manager.service.hbase.EsService;
import com.xylink.manager.service.remote.logagent.LogAgentBackupFileService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class X86EsOpsServiceStrategy extends AbstractEsOpsService implements IOpsService {

    private final EsService esService;
    private final BackUpNotifyService backUpNotifyService;
    @Resource
    private LogAgentBackupFileService logAgentBackupFileService;

    public X86EsOpsServiceStrategy(K8sService k8sService, ServerNetworkService serverNetworkService, EsService esService, BackUpNotifyService backUpNotifyService) {
        super(k8sService, serverNetworkService);
        this.esService = esService;
        this.backUpNotifyService = backUpNotifyService;
    }

    @Override
    protected void doBackup(String name, String time) {
        try {
            esService.backup(name);
        } catch (Exception e) {
            logger.warn("backup error", e);
        }
    }

    @Override
    protected void applyPostProcessorsAfterBackup(String name, String time) {
        if (backUpNotifyService.checkBackUpNotify(BackupFileType.EsType, name)) {
            logger.warn("es backup task is not completed, {}", name);
            return;
        }
        String esPodIp = serverNetworkService.getNetworkConfiguration().get(NetworkConstants.ES_IP);
        logAgentBackupFileService.cpBackupFileToMainNode(esPodIp, time, name, name, BackupFileType.EsType.getValue());
    }

    @Override
    protected void doRestore(String name, String time) {
        try {
            String esPodIp = serverNetworkService.getNetworkConfiguration().get(NetworkConstants.ES_IP);
            logAgentBackupFileService.cpBackFileToOriginalNode(esPodIp, time, name, BackupFileType.EsType.getValue());
            esService.restore(name);
        } catch (Exception e) {
            logger.error("restore error", e);
        }
    }
}
