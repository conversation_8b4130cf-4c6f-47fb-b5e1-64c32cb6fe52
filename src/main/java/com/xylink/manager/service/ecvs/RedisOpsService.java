package com.xylink.manager.service.ecvs;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.model.em.BackupFileType;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.remote.logagent.LogAgentBackupFileService;
import com.xylink.util.CommandUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class RedisOpsService {
    private static final Logger logger = LoggerFactory.getLogger(RedisOpsService.class);

    @Value("${base.dir}")
    private String baseDir;
    @Autowired
    private K8sService k8sService;
    @Autowired
    private LogAgentBackupFileService logAgentBackupFileService;

    /**
     * redis备份
     *
     * @param name 备份文件名称
     */
    public void backup(String name, String time) {
        if (StringUtils.isBlank(name)) {
            name = "appendonly.aof";
        }
        String redisIp = k8sService.getRedisNodeIp();
        String mainIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP).get(NetworkConstants.MAIN_INTERNAL_IP);
        if (mainIp.equals(redisIp)) {
            String cmd = "mkdir -p "+baseDir+"/db/all/" + time + ";cp -rf "+baseDir+"/redis_data/appendonly.aof "+baseDir+"/db/all/" + time + "/" + name;
            try {
                CommandUtils.execCommand(new String[]{"/bin/sh", "-c", cmd});
            } catch (Exception e) {
                logger.error("execCommand error", e);
            }
            return;
        }
        //!mainIp.equals(redisIp)
        logAgentBackupFileService.cpBackupFileToMainNode(redisIp, time, "appendonly.aof", name, BackupFileType.RedisType.getValue());
    }

    public void restore(String name, String time) {
        if (StringUtils.isBlank(name)) {
            name = "appendonly.aof";
        }

        String redisIp = k8sService.getRedisNodeIp();
        String mainIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP).get(NetworkConstants.MAIN_INTERNAL_IP);

        String cmd;
        if (mainIp.equals(redisIp)) {
            cmd = "rm -rf "+baseDir+"/redis_data/appendonly.aof && cp -f "+baseDir+"/db/all/" + time + "/" + name + " "+baseDir+"/redis_data/appendonly.aof && chown -R 2020:2020 "+baseDir+"/redis_data/appendonly.aof && kubectl delete pod `kubectl get pod | grep private-redis- | awk '{print $1}'` --grace-period=0 --force ";
        } else {
            cmd = "kubectl delete pod `kubectl get pod | grep private-redis- | awk '{print $1}'` --grace-period=0 --force";
            logAgentBackupFileService.cpBackFileToOriginalNode(redisIp, time, name, BackupFileType.RedisType.getValue());
        }
        try {
            CommandUtils.execCommand(new String[]{"/bin/sh", "-c", k8sService.getExportKubeConfigExec() + " && " + cmd});
        } catch (Exception e) {
            logger.error("execCommand error", e);
        }

    }
}
