package com.xylink.manager.service.ecvs.es;

import com.xylink.manager.service.base.PlatformConfig;
import com.xylink.manager.service.ecvs.IOpsService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2022/3/25 11:32 上午
 */
@Component
public class EsOpsServiceContext {

    @Resource
    private AnKeEsOpsServiceStrategy anKeEsOpsServiceStrategy;
    @Resource
    private X86EsOpsServiceStrategy x86EsOpsServiceStrategy;

    public IOpsService getEsOpsStrategy() {
        return PlatformConfig.isAnKe() ? anKeEsOpsServiceStrategy : x86EsOpsServiceStrategy;
    }

}
