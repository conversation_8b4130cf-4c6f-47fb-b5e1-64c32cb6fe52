package com.xylink.manager.service.ecvs.es;

import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.config.exception.basic.WebException;
import com.xylink.manager.model.em.DBType;
import com.xylink.manager.service.ServerNetworkService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.ecvs.IOpsService;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @since 2022/3/25 2:28 下午
 */
public abstract class AbstractEsOpsService implements IOpsService {
    protected Logger logger = LoggerFactory.getLogger(getClass());
    protected K8sService k8sService;
    protected ServerNetworkService serverNetworkService;

    public AbstractEsOpsService(K8sService k8sService, ServerNetworkService serverNetworkService) {
        this.k8sService = k8sService;
        this.serverNetworkService = serverNetworkService;
    }

    @Override
    public void backup(String name, String time) {
        if (isEsNotRunning()) {
            return;
        }
        doBackup(name, time);
        applyPostProcessorsAfterBackup(name, time);
    }

    @Override
    public void restore(String name, String time) {

        if (!name.endsWith(".tar.gz")) {
            throw new WebException(ErrorStatus.FILE_PATH_ILLEGAL);
        }
        if (isEsNotRunning()) {
            return;
        }
        doRestore(name, time);
        applyPostProcessorsAfterRestore();
    }

    protected abstract void doBackup(String name, String time);

    protected abstract void doRestore(String name, String time);

    protected void applyPostProcessorsAfterBackup(String name, String time) {
    }

    protected void applyPostProcessorsAfterRestore() {
    }

    protected boolean isEsNotRunning() {
        return !k8sService.getDbPodStatus(DBType.es.name(), Strings.EMPTY);
    }
}
