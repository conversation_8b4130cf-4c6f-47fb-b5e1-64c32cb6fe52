package com.xylink.manager.service.ecvs.es;

import com.xylink.config.K8sSvcConstants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.service.ServerNetworkService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.base.K8sSvcService;
import com.xylink.manager.service.ecvs.IOpsService;
import com.xylink.util.CommandUtils;
import com.xylink.util.DockerImagesUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/3/25 11:30 上午
 */
@Service
public class AnKeEsOpsServiceStrategy extends AbstractEsOpsService implements IOpsService {

    @Resource
    private RestTemplate restTemplate;
    @Autowired
    private K8sSvcService k8sSvcService;

    private String backUpDir;
    private static String mainNodePath;

    @Value("${base.dir}")
    public void setBaseDir(String baseDir) {
        mainNodePath = baseDir + "/db/all/";
        backUpDir = baseDir + "/db/esbackup/";
    }

    public AnKeEsOpsServiceStrategy(K8sService k8sService, ServerNetworkService serverNetworkService) {
        super(k8sService, serverNetworkService);
    }

    @Override
    protected void doBackup(String name, String time) {

        String image = DockerImagesUtils.getImages(DockerImagesUtils.ImagesEnum.ELASTICDUMP_ANKE);
        if (StringUtils.isBlank(image)) {
            logger.error("Cancel backup es data.Cause by has no ELASTICDUMP_IMAGE");
            return;
        }
        List<String> dockerCommands = new ArrayList<>();
        if (k8sService.isContainerD()) {
            dockerCommands.add("ctr");
            dockerCommands.add("run");
            dockerCommands.add("--rm");
            dockerCommands.add("--mount");
            dockerCommands.add("type=bind,src="+backUpDir + ",dst=" + backUpDir + ",options=rbind:rw");
            dockerCommands.add("--net-host");
            dockerCommands.add(image);
            dockerCommands.add("my-es-container-" + time);
            dockerCommands.add("sh -c" + " \"" + workingHome() + " && " + multielasticdumpCmd() + " && " + tarCmd(name) + "\"");
        } else {
            dockerCommands.add("docker");
            dockerCommands.add("run");
            dockerCommands.add("--rm");
            dockerCommands.add("-v");
            dockerCommands.add(backUpDir + ":" + backUpDir + ":z");
            dockerCommands.add("--net=host");
            dockerCommands.add(image);
            dockerCommands.add("sh -c" + " \"" + workingHome() + " && " + multielasticdumpCmd() + " && " + tarCmd(name) + "\"");
        }

        String cmd = moveFile(name, time);
        try {
            CommandUtils.execCommand(new String[]{"/bin/sh", "-c", "rm -fr " + backUpDir});
            CommandUtils.execCommand(new String[]{"/bin/sh", "-c", "mkdir -p " + backUpDir});
            String commands = StringUtils.join(dockerCommands, " ");
            logger.info("anke backup es info, backupDir [{}], commands:[{}]", backUpDir, commands);
            CommandUtils.execCommand(new String[]{"/bin/sh", "-c", commands});
            logger.info("anke backup es cmd: [{}]]", cmd);
            CommandUtils.execCommand(new String[]{"/bin/sh", "-c", cmd});
        } catch (Exception e) {
            logger.error("BackUp es error.", e);
            return;
        }

        logger.info("Backup es success. filename:{}", name);
    }

    @Override
    protected void doRestore(String name, String time) {
        String image = DockerImagesUtils.getImages(DockerImagesUtils.ImagesEnum.ELASTICDUMP_ANKE);
        if (StringUtils.isBlank(image)) {
            logger.error("Cancel restore es data.Cause by has no ELASTICDUMP_IMAGE");
            return;
        }
        // 复制到 backUpDir 解压
        decompress(name, time);
        // 清除老的索引数据
        clearIndex();
        // restore
        List<String> dockerCommands = new ArrayList<>();
        if(k8sService.isContainerD()){
            dockerCommands.add("ctr");
            dockerCommands.add("run");
            dockerCommands.add("--rm");
            dockerCommands.add("--mount");
            dockerCommands.add("type=bind,src="+backUpDir + ",dst=" + backUpDir + ",options=rbind:rw");
            dockerCommands.add("--net-host");
            dockerCommands.add(image);
            dockerCommands.add("my-es-container-" + time);
            dockerCommands.add("sh -c" + " \"" + workingHome() + " && " + multielasticloadCmd() + "\"");
        }else {
            dockerCommands.add("docker");
            dockerCommands.add("run");
            dockerCommands.add("--rm");
            dockerCommands.add("-v");
            dockerCommands.add(backUpDir + ":" + backUpDir + ":z");
            dockerCommands.add("--net=host");
            dockerCommands.add(image);
            dockerCommands.add("sh -c" + " \"" + workingHome() + " && " + multielasticloadCmd() + "\"");
        }

        try {
            CommandUtils.execCommand(new String[]{"/bin/sh", "-c", StringUtils.join(dockerCommands, " ")});
        } catch (Exception e) {
            logger.error("Restore es error.", e);
            return;
        }
        logger.info("Restore es success.");
    }

    private String esUrlWithBasicAuth() {
        String esPodIp;
        if (k8sService.isNewCms()) {
            esPodIp = k8sSvcService.getServiceIpByDefaultNs("es-xylinkservice");
        } else {
            esPodIp = serverNetworkService.getNetworkConfiguration().get(NetworkConstants.ES_IP);
        }
        return "http://" + esPodIp + ":9200  --headers='{\\\"Authorization\\\":\\\"Basic ZWxhc3RpYzpROGxMV3BtVks4d0F4Z3pVJiNPWg==\\\"}'";
    }

    private String esUrl() {
        String esPodIp;
        String svcIp = k8sSvcService.getServiceIpByDefaultNs(K8sSvcConstants.ES_NAME);
        if (StringUtils.isNotBlank(svcIp)) {
            esPodIp = svcIp;
        } else {
            esPodIp = serverNetworkService.getNetworkConfiguration().get(NetworkConstants.ES_IP);
        }
        return "http://" + esPodIp + ":9200";
    }

    private String workingHome() {
        return "cd " + backUpDir;
    }

    private String multielasticdumpCmd() {
        return "multielasticdump --direction=dump  --match='^.*$' --input=" + esUrlWithBasicAuth() + " --limit=10000 --output=" + backUpDir;
    }

    private String multielasticloadCmd() {
        return "multielasticdump --direction=load  --ignoreChildError=true --input=" + backUpDir + " --limit=10000 --output=" + esUrlWithBasicAuth();
    }

    private String tarCmd(String name) {
        return "tar zcvf " + name + " *";
    }

    private String moveFile(String name, String time) {
        return "cp " + backUpDir + name + " " + mainNodePath + time + "/" + name + " ; rm -fr " + backUpDir;
    }

    private void clearIndex() {
        HttpHeaders headers = new HttpHeaders();
        headers.setBasicAuth("elastic", "Q8lLWpmVK8wAxgzU&#OZ");
        restTemplate.exchange(esUrl() + "/xylink-buffet-admin-action*", HttpMethod.DELETE, new HttpEntity<>(headers), String.class);
        restTemplate.exchange(esUrl() + "/en_contact*", HttpMethod.DELETE, new HttpEntity<>(headers), String.class);
    }

    private void decompress(String name, String time) {
        String cmd = "rm -fr " + backUpDir + ";mkdir -p " + backUpDir + ";tar -zxvf " + mainNodePath + time + "/" + name + " -C " + backUpDir;
        try {
            CommandUtils.execCommand(new String[]{"/bin/sh", "-c", cmd});
        } catch (Exception e) {
            logger.error("Restore es error.", e);
        }
    }
}
