package com.xylink.manager.service.ecvs;

import com.xylink.config.NetworkConstants;
import com.xylink.manager.model.em.BackupFileType;
import com.xylink.manager.model.em.DBType;
import com.xylink.manager.service.ServerNetworkService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.ecvs.es.EsOpsServiceContext;
import com.xylink.manager.service.remote.logagent.LogAgentBackupFileService;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class EsOpsService implements InitializingBean {
    private static final Logger logger = LoggerFactory.getLogger(EsOpsService.class);

    @Autowired
    private K8sService k8sService;
    @Autowired
    private ServerNetworkService serverNetworkService;
    @Autowired
    private LogAgentBackupFileService logAgentBackupFileService;

    @Resource
    private EsOpsServiceContext esOpsServiceContext;

    private IOpsService esService;

    private static final int expiredInterval = 30;

    public void backup(String fileName, String time) {
        if (!k8sService.getDbPodStatus(DBType.es.name(), Strings.EMPTY)) {
            return;
        }

        try {
            esService.backup(fileName,time);
        } catch (Exception e) {
            logger.warn("Backup es error", e);
        }
    }

    public void restore(String fileName,String time) {
        if (!k8sService.getDbPodStatus(DBType.es.name(), Strings.EMPTY)) {
            return;
        }

        try {
            esService.restore(fileName,time);
        } catch (Exception e) {
            logger.error("Restore es error", e);
        }
    }

    /**
     * 清除es过期备份文件
     */
    public void removeExpiredBackupFile() {
        if (!k8sService.getDbPodStatus(DBType.es.name(), Strings.EMPTY)) {
            return;
        }
        String esPodIp = serverNetworkService.getNetworkConfiguration().get(NetworkConstants.ES_IP);
        try {
            logAgentBackupFileService.removeExpiredBackupFile(esPodIp, BackupFileType.EsType.getValue(), expiredInterval);
        } catch (Exception e) {
            logger.error("remove expired es backup file error, ", e);
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        this.esService = esOpsServiceContext.getEsOpsStrategy();
    }
}
