package com.xylink.manager.service.migrate;

import com.xylink.config.aop.aspect.DbOperationLogStorageDAOImpl;
import com.xylink.config.aop.aspect.ESOperationLogDTO;
import org.elasticsearch.index.query.QueryBuilders;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-01-08 11:44
 */
@Service
public class OptLogEsToDb {
    private static Logger LOG = LoggerFactory.getLogger(OptLogEsToDb.class);
    private final static int PER_SIZE = 50;
    private final static int BATCH_SIZE = 500;
    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;
    @Autowired
    private DbOperationLogStorageDAOImpl dbOperationLogStorageDAO;

    @Async
    public void migrate() {
        LOG.info("esToDb task begin");
        int total = transferData();
        LOG.info("esToDb task end,total is:[{}]", total);
    }

    private int transferData() {
        int page = 0;
        List<ESOperationLogDTO> dtoList;
        int total = 0;
        do {
            // 构建查询，使用分页
            Query searchQuery = new NativeSearchQueryBuilder()
                    .withQuery(QueryBuilders.matchAllQuery())
                    .withPageable(PageRequest.of(page, BATCH_SIZE))
                    .build();

            // 获取当前批次的记录
            SearchHits<ESOperationLogDTO> searchHits = elasticsearchRestTemplate.search(searchQuery, ESOperationLogDTO.class);

            dtoList = searchHits.stream()
                    .map(org.springframework.data.elasticsearch.core.SearchHit::getContent)
                    .collect(Collectors.toList());

            // 插入到数据库
            if (!dtoList.isEmpty()) {
                total += dtoList.size();
                dbOperationLogStorageDAO.save(dtoList.stream().map(ESOperationLogDTO::to).collect(Collectors.toList()));
            }

            page++;
        } while (dtoList.size() == BATCH_SIZE);
        return total;
    }


}
