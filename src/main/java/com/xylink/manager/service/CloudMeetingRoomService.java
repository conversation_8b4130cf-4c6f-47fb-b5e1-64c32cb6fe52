package com.xylink.manager.service;

import com.xylink.config.Constants;
import com.xylink.config.exception.basic.ServiceErrorException;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.manager.controller.dto.develop.CloudMeetingRoomEnableConfigDTO;
import com.xylink.manager.model.CloudMeetingRoomConfig;
import com.xylink.manager.model.CloudMeetingRoomNumberConfig;
import com.xylink.manager.model.deploy.Job;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.remote.cloudmeetingroom.CloudMeetingRoomRemoteClient;
import com.xylink.util.JDBCUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
public class CloudMeetingRoomService {
    private static final Logger logger = LoggerFactory.getLogger(CloudMeetingRoomService.class);
    private static final String REFRESH_CACHE_JOB_NAME = "refresh-cache-meeting-job";
    private static final AtomicInteger INDEX = new AtomicInteger();
    private static final List<CloudMeetingRoomConfig> SUPPORT_ALL_USER_MEETING_ROOM_CONFIG = new ArrayList<>();

    @Resource
    private CloudMeetingRoomRemoteClient cloudMeetingRoomRemoteClient;

    @Autowired
    private JDBCUtils jdbcUtils;
    @Resource
    private K8sService k8sService;
    @Resource
    private IDeployService deployService;

    @Autowired
    private ServerListService serverListService;

    @Autowired
    private RestTemplate restTemplate;

    static {
        CloudMeetingRoomConfig config = new CloudMeetingRoomConfig();
        config.setConfigName("disablePersonalConferenceCall");
        SUPPORT_ALL_USER_MEETING_ROOM_CONFIG.add(config);
    }

    /**
     * 获取云会议室功能开关配置list
     * @param config
     * @return
     */
    public List<CloudMeetingRoomConfig> getMeetingRoomConfigList(CloudMeetingRoomConfig config) {
        if (Objects.isNull(config) || StringUtils.isBlank(config.getConfigName())) {
            return jdbcUtils.listMeetingConfigName();
        } else {
            return jdbcUtils.listMeetingConfigValue(config);
        }
    }

    // $.items[?(@.status.succeeded==1 && @.metadata.name=="refresh-cache-meeting-job")].metadata.name
    public Boolean refreshMeetingCache() {
        int index = INDEX.addAndGet(1);
        Job job = deployService.createJobFromCronJob("cache-meeting-cronjob", REFRESH_CACHE_JOB_NAME + index, Constants.NAMESPACE_DEFAULT);

        if (job == null){
            logger.error("cronjobs.batch \"cache-meeting-cronjob\" not found.");
            return Boolean.FALSE;
        }

        try {
            clearCompleteJob();
        } catch (Exception e) {
            logger.error("clearCompleteJob error", e);
        }

        return Boolean.TRUE;
    }

    private void clearCompleteJob() throws Exception {
        List<Job> jobs = deployService.listAllJob(Constants.NAMESPACE_DEFAULT);
        if (CollectionUtils.isEmpty(jobs)){
            return;
        }

        List<Job> historyCompleteJobList = jobs.stream().filter(job ->
                job.getName().startsWith(REFRESH_CACHE_JOB_NAME) && job.getConditions().stream().anyMatch(condition -> "Complete".equals(condition.getType()))
        ).collect(Collectors.toList());

        logger.debug("successCacheJobNameList : {}", historyCompleteJobList);
        if (CollectionUtils.isEmpty(historyCompleteJobList)) {
            return;
        }
        historyCompleteJobList.forEach(job -> {
            deployService.deleteJob(job.getName(), job.getNamespace());
        });
    }

    public List<CloudMeetingRoomConfig> supportAllUserMeetingRoomConfig() {
        return SUPPORT_ALL_USER_MEETING_ROOM_CONFIG;
    }

    public List<CloudMeetingRoomConfig> getCloudMeetingRoomNumberConfigs(String number) {
        return cloudMeetingRoomRemoteClient.getByNumber(number);
    }

    public void editCloudMeetingRoomNumberConfigs(CloudMeetingRoomNumberConfig cloudMeetingRoomNumberConfig) {
        cloudMeetingRoomRemoteClient.editNumberConfigs(cloudMeetingRoomNumberConfig);
    }
    public void addEntMeetingConfig(String conferenceNumberType, Map<String, Object> configs) {
        cloudMeetingRoomRemoteClient.addEntMeetingConfig(conferenceNumberType,configs);
    }

    public Object getMeetingRoomTypeListForEnableConfig() {
        String internalNginxUrl = serverListService.getInternalNginxUrl();
        String url = internalNginxUrl + "/api/rest/cloudmeetingroom/internal/enterprise/config/numberTypeInfos/v1";

        try {
            return restTemplate.getForObject(url, Object.class);
        } catch (Exception e) {
            logger.error("getMeetingRoomTypeListForEnableConfig failed!", e);
            throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_CLOUDMEETINGROOM_FAILED);
        }
    }

    public CloudMeetingRoomEnableConfigDTO[] getEnableConfigList(Integer type) {
        String internalNginxUrl = serverListService.getInternalNginxUrl();

        String url = internalNginxUrl + "/api/rest/cloudmeetingroom/internal/enterprise/config/configKeyInfo/v1?type=" + type;
        try {
            return restTemplate.getForObject(url, CloudMeetingRoomEnableConfigDTO[].class);
        } catch (Exception e) {
            logger.error("getMeetingRoomTypeListForEnableConfig failed!", e);
            throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_CLOUDMEETINGROOM_FAILED);
        }
    }

    public CloudMeetingRoomEnableConfigDTO getEnableConfigValue(Integer type, String key) {
        CloudMeetingRoomEnableConfigDTO[] enableConfigList = getEnableConfigList(type);
        if (ObjectUtils.isEmpty(enableConfigList)) {
            return null;
        }
        for (CloudMeetingRoomEnableConfigDTO temp : enableConfigList) {
            if (key.equals(temp.getKey())) {
                return temp;
            }
        }
        return null;
    }

    public void updateCloudMeetingRoomEnableConfig(CloudMeetingRoomEnableConfigDTO dto) {
        String internalNginxUrl = serverListService.getInternalNginxUrl();
        String enterpriseId = "default_enterprise";

        String url = internalNginxUrl + "/api/rest/internal/v1/meetingroom/enterprise/config?enterpriseId=" + enterpriseId + "&conferenceNumberType=" + dto.getType();

        Map<String, String> config = new HashMap<>();
        config.put(dto.getKey(), dto.getValue());

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<Map<String, String>> request = new HttpEntity<>(config, headers);
        try {
            restTemplate.exchange(url, HttpMethod.POST, request, String.class);
        } catch (Exception e) {
            logger.error("updateCloudMeetingRoomEnableConfig failed!", e);
            throw new ServiceErrorException(ErrorStatus.THIRD_INTERFACE_CLOUDMEETINGROOM_FAILED);
        }
    }
}
