package com.xylink.manager.iptables.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.xylink.config.Constants;
import com.xylink.manager.inspection.utils.JsonUtils;
import com.xylink.manager.inspection.utils.UUIDUtil;
import com.xylink.manager.iptables.dao.DefaultRuleDao;
import com.xylink.manager.iptables.db.IptablesModule;
import com.xylink.manager.iptables.db.IptablesModuleOptions;
import com.xylink.manager.iptables.db.IptablesRule;
import com.xylink.manager.iptables.db.IptablesWhiteIp;
import com.xylink.manager.iptables.dto.*;
import com.xylink.manager.iptables.enums.NodeType;
import com.xylink.manager.iptables.service.DefaultRuleService;
import com.xylink.manager.iptables.service.TrustedDeviceService;
import com.xylink.manager.iptables.util.RuleUtil;
import com.xylink.manager.service.base.K8sService;
import com.xylink.util.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/24
 */
@Service
@Slf4j
public class DefaultRuleServiceImpl implements DefaultRuleService {
    private static final String BOND0_IP_PLACEHOLDER = "{bond0_ip}";

    private final DefaultRuleDao defaultRuleDao;
    private final TrustedDeviceService trustedDeviceService;
    private final K8sService k8sService;

    public DefaultRuleServiceImpl(DefaultRuleDao defaultRuleDao, TrustedDeviceService trustedDeviceService, K8sService k8sService) {
        this.defaultRuleDao = defaultRuleDao;
        this.trustedDeviceService = trustedDeviceService;
        this.k8sService = k8sService;
    }

    @Override
    public TotalRuleDTO getRules(NodeType nodeType) {
        RuleListDTO ruleListDTO = defaultRuleDao.getAllRuleDTO(nodeType);
        // 处理options中的cm相关的值
        processOptionsWithCm(ruleListDTO.getOptionsList());
        List<RuleDTO> ruleDTOs = toRuleDTOs(ruleListDTO);
        //关联服务部署规则
        removeRelationServiceRules(ruleDTOs);
        //先添加不需要白名单的规则
        List<RuleDTO> result = ruleDTOs.stream().filter(ruleDto -> StringUtils.isBlank(ruleDto.getWhiteMode()))
                .collect(Collectors.toList());
        //需要加上白名单的规则
        addWhiteIpRules(result, ruleDTOs);
        //cms占位符规则
        addCmsPlaceholderRule(result, ruleDTOs);
        //添加网卡bond0占位符规则
        addBond0Rules(result, ruleDTOs);

        log.debug("iptables all rules: {}", JsonUtils.objectToJsonNonNull(result));

        TotalRuleDTO totalRuleDTO = new TotalRuleDTO();
        List<RuleDTO> iptables = result.stream().filter(item -> StringUtils.equals(item.getRuleType(), "ipv4"))
                .collect(Collectors.toList());

        List<RuleDTO> ip6tables = result.stream().filter(item -> StringUtils.equals(item.getRuleType(), "ipv6"))
                .collect(Collectors.toList());
        totalRuleDTO.setIptables(iptables);
        totalRuleDTO.setIp6tables(ip6tables);
        RuleUtil.sortRules(totalRuleDTO.getIptables());
        RuleUtil.sortRules(totalRuleDTO.getIp6tables());
        return totalRuleDTO;
    }

    private void addBond0Rules(List<RuleDTO> result, List<RuleDTO> ruleDTOs) {
        List<RuleDTO> bond0Rules = ruleDTOs.stream()
                .filter(ruleDto -> StringUtils.equals(ruleDto.getDestIp(), BOND0_IP_PLACEHOLDER)
                        && StringUtils.equals(ruleDto.getWhiteMode(), "bond0-place"))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(bond0Rules)) {
            log.warn("bond0Rules is empty");
            return;
        }
        bond0Rules.forEach(ruleDto -> {
            RuleDTO ruleCopy = new RuleDTO(ruleDto);
            IpUtils.IpAddress ipAddress = IpUtils.getCmsIp();
            String cmsIp = ruleDto.getRuleType().equals("ipv4")
                    ? ipAddress.getIpv4()
                    : ipAddress.getIpv6();
            if (StringUtils.isBlank(cmsIp)) {
                log.warn("cmsIp is empty");
                return;
            }
            result.add(ruleCopy);
            ruleCopy.setDestIp(ruleCopy.getDestIp().replace(BOND0_IP_PLACEHOLDER, cmsIp));
        });
    }

    public void removeRelationServiceRules(List<RuleDTO> ruleDTOs) {
        if (CollectionUtils.isEmpty(ruleDTOs)) {
            return;
        }
        List<RuleDTO> relationServiceRules = ruleDTOs.stream()
                .filter(rule -> StringUtils.isNotBlank(rule.getRelationServiceLabel()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(relationServiceRules)) {
            return;
        }
        Iterator<RuleDTO> iterator = ruleDTOs.iterator();
        while (iterator.hasNext()) {
            RuleDTO rule = iterator.next();
            String label = rule.getRelationServiceLabel();
            if (StringUtils.isBlank(label)) {
                continue;
            }
            if (!k8sService.isDeployed(label)) {
                log.info("{} is not deployed，relation rule is removed, ruleId:{}", label, rule.getId());
                iterator.remove();
                continue;
            }
            log.info("{} is deployed", label);
        }
    }

    private List<RuleDTO> toRuleDTOs(RuleListDTO ruleListDTO) {
        List<IptablesRule> ruleList = ruleListDTO.getRuleList();
        List<IptablesModule> moduleList = ruleListDTO.getModuleList();
        List<IptablesModuleOptions> optionsList = ruleListDTO.getOptionsList();
        //将和cm有关联的options value 替换成cm的值
        List<RuleDTO> ruleDTOs = new ArrayList<>();
        ruleList.forEach(rule -> {
            RuleDTO ruleDto = new RuleDTO(rule);
            ruleDTOs.add(ruleDto);
            ruleDto.setMatches(new ArrayList<>());

            // 处理 matches 模块
            processModules(rule, moduleList, optionsList, "matches", (module, optionsMap) -> {
                MatchDTO matchDto = new MatchDTO(module);
                matchDto.setOptions(optionsMap);
                ruleDto.getMatches().add(matchDto);
            });

            // 对 matches 列表进行排序，让 module 为 comment 的排在后面
            ruleDto.getMatches().sort((dto1, dto2) -> {
                if ("comment".equals(dto1.getModule())) {
                    return 1;
                } else if ("comment".equals(dto2.getModule())) {
                    return -1;
                }
                return 0;
            });

            // 处理 target 模块
            processModules(rule, moduleList, optionsList, "target", (module, optionsMap) -> {
                TargetDTO targetDto = new TargetDTO(module);
                targetDto.setOptions(optionsMap);
                ruleDto.setTarget(targetDto);
            });
        });
        return new ArrayList<>(new HashSet<>(ruleDTOs));
    }

    private void processOptionsWithCm(List<IptablesModuleOptions> optionsList) {
        optionsList.forEach(this::processSingleOptionWithCm);
    }

    private void processSingleOptionWithCm(IptablesModuleOptions options) {
        if (StringUtils.isNotBlank(options.getRelationCmKey())) {
            if (options.getRelationCmKey().startsWith(Constants.CONFIGMAP_ALLIP)) {
                processAllIpConfigMap(options);
            } else if (options.getRelationCmKey().startsWith("main-proxy.")) {
                processMainProxyConfigMap(options);
            }
        }
    }

    private void processAllIpConfigMap(IptablesModuleOptions options) {
        Map<String, String> allIpMap = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        String[] keys = options.getRelationCmKey().split(":")[1].split(",");
        String[] values = options.getOptionsValue().split(",");
        for (String key : keys) {
            values[Arrays.asList(keys).indexOf(key)] = allIpMap.get(key);
        }
        options.setOptionsValue(String.join(",", values));
    }

    private void processMainProxyConfigMap(IptablesModuleOptions options) {
        Map<String, String> proxyMap = k8sService.getConfigmap(Constants.CONFIGMAP_MAIN_PROXY);
        String[] keys = options.getRelationCmKey().split(",");
        String[] values = new String[keys.length];
        for (String key : keys) {
            values[Arrays.asList(keys).indexOf(key)] = proxyMap.get(key);
        }
        if (values[0] != null) {
            options.setOptionsValue(String.join(",", values));
        }
    }

    /**
     * 处理模块逻辑的辅助方法
     *
     * @param rule        当前规则
     * @param moduleList  模块列表
     * @param optionsList 选项列表
     * @param moduleType  模块类型（matches 或 target）
     * @param consumer    处理模块和选项的回调函数
     */
    private void processModules(IptablesRule
                                        rule, List<IptablesModule> moduleList, List<IptablesModuleOptions> optionsList,
                                String moduleType, BiConsumer<IptablesModule, Map<String, String>> consumer) {
        moduleList.stream()
                .filter(module -> module.getRuleId().equals(rule.getId()) && module.getModuleType().equals(moduleType))
                .forEach(module -> {
                    Map<String, String> optionsMap = new HashMap<>();
                    optionsList.stream()
                            .filter(option -> option.getModuleId().equals(module.getId()))
                            .forEach(option -> optionsMap.put(option.getOptionsName(), option.getOptionsValue()));
                    consumer.accept(module, optionsMap);
                });
    }

    private void addWhiteIpRules(List<RuleDTO> result, List<RuleDTO> ruleDTOs) {
        List<IptablesWhiteIp> whiteIpList = trustedDeviceService.getTrustedDeviceList();
        IptablesWhiteIp mainNodeIpv4 = new IptablesWhiteIp();
        IptablesWhiteIp mainNodeIpv6 = new IptablesWhiteIp();
        mainNodeIpv4.setIp(IpUtils.getCmsIp().getIpv4());
        mainNodeIpv6.setIp(IpUtils.getCmsIp().getIpv6());
        whiteIpList.add(mainNodeIpv4);
        whiteIpList.add(mainNodeIpv6);
        if (shouldSkipProcessing(ruleDTOs, whiteIpList)) {
            log.info("whiteIpRules is empty or whiteIpList is empty");
            return;
        }

        filterAndProcessWhiteIpRules(result, ruleDTOs, whiteIpList);
    }

    private void addCmsPlaceholderRule(List<RuleDTO> result, List<RuleDTO> ruleDTOs) {
        List<RuleDTO> cmsPlaceholderRules = ruleDTOs.stream()
                .filter(ruleDto -> StringUtils.equals(ruleDto.getWhiteMode(), "dest-place")
                        && StringUtils.isBlank(ruleDto.getRelationServiceLabel()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cmsPlaceholderRules)) {
            log.warn("cmsPlaceholderRules is empty");
            return;
        }
        CmsClusterInfo cmsClusterInfo = trustedDeviceService.getCmsClusterInfo();
        if (StringUtils.isBlank(cmsClusterInfo.getBond0Ip()) || StringUtils.isBlank(cmsClusterInfo.getPeerIp())) {
            log.warn("cmsClusterInfo currentServerIP or peerIp is empty");
            return;
        }
        cmsPlaceholderRules.forEach(ruleDto -> {
            RuleDTO ruleCopy = new RuleDTO(ruleDto);
            ruleCopy.setDestIp(ruleCopy.getDestIp()
                    .replace("{other_cluster_ip}", cmsClusterInfo.getOtherClusterIp())
                    .replace(BOND0_IP_PLACEHOLDER, cmsClusterInfo.getBond0Ip())
                    .replace("{peer_ip}", cmsClusterInfo.getPeerIp())
                    .replace("{this_cluster_ip}", cmsClusterInfo.getThisClusterIp()));

            if (ruleCopy.getTarget().getOptions() != null) {
                ruleCopy.getTarget().getOptions().forEach((key, value) -> {
                    if (StringUtils.isNotBlank(value)) {
                        ruleCopy.getTarget().getOptions().put(key, value
                                .replace("{other_cluster_ip}", cmsClusterInfo.getOtherClusterIp())
                                .replace(BOND0_IP_PLACEHOLDER, cmsClusterInfo.getBond0Ip())
                                .replace("{peer_ip}", cmsClusterInfo.getPeerIp())
                                .replace("{this_cluster_ip}", cmsClusterInfo.getThisClusterIp()));
                    }
                });
            }
            if (!StringUtils.contains(ruleCopy.getDestIp(), "{")) {
                result.add(ruleCopy);
            }
        });
    }

    private boolean shouldSkipProcessing(List<RuleDTO> ruleDTOs, List<IptablesWhiteIp> whiteIpList) {
        List<RuleDTO> whiteIpRules = ruleDTOs.stream()
                .filter(ruleDto -> StringUtils.isNotBlank(ruleDto.getWhiteMode()))
                .collect(Collectors.toList());
        return CollectionUtils.isEmpty(whiteIpRules) || CollectionUtils.isEmpty(whiteIpList);
    }

    private void filterAndProcessWhiteIpRules(List<RuleDTO> result, List<RuleDTO> ruleDTOs, List<IptablesWhiteIp> whiteIpList) {
        List<RuleDTO> whiteIpRules = ruleDTOs.stream()
                .filter(ruleDto -> StringUtils.isNotBlank(ruleDto.getWhiteMode())
                        && !ruleDto.getWhiteMode().equals("dest-place")
                        && !ruleDto.getWhiteMode().equals("bond0-place"))
                .collect(Collectors.toList());

        whiteIpRules.forEach(ruleDto -> whiteIpList.forEach(whiteIp -> processSingleWhiteIp(result, ruleDto, whiteIp)));
    }

    private void processSingleWhiteIp(List<RuleDTO> result, RuleDTO ruleDto, IptablesWhiteIp whiteIp) {
        if (StringUtils.isBlank(whiteIp.getIp())) {
            log.warn("whiteIp ip is empty");
            return;
        }
        String ipWithoutMask = whiteIp.getIp().split("/")[0];
        if (isInvalidIpType(ruleDto, ipWithoutMask)) {
            return;
        }

        String ip = formatIpIfNeeded(whiteIp.getIp(), ipWithoutMask);

        RuleDTO ruleCopy = new RuleDTO(ruleDto);
        result.add(ruleCopy);

        if ("src".equals(ruleDto.getWhiteMode())) {
            ruleCopy.setSrcIp(ip);
        } else if ("dest".equals(ruleDto.getWhiteMode())) {
            ruleCopy.setDestIp(ip);
        }
    }

    private boolean isInvalidIpType(RuleDTO ruleDto, String ipWithoutMask) {
        return (ruleDto.getRuleType().equals("ipv4") && IpUtils.isIpv6(ipWithoutMask)) ||
                (ruleDto.getRuleType().equals("ipv6") && IpUtils.isIpv4(ipWithoutMask));
    }

    private String formatIpIfNeeded(String ip, String ipWithoutMask) {
        if (IpUtils.isIpv4(ipWithoutMask) && !ip.contains("/")) {
            return ip + "/32";
        }
        return ip;
    }

    @Override
    public void saveRules(String rulesJson, String nodeType, boolean saveWhiteIp, int order) {
        TotalRuleDTO totalRuleDTO = JsonUtils.jsonToPo(rulesJson, new TypeReference<TotalRuleDTO>() {
        });
        log.info("{}", JsonUtils.objectToJsonString(totalRuleDTO));
        saveRules(totalRuleDTO, nodeType, saveWhiteIp, order);
    }

    @Override
    public void saveRules(TotalRuleDTO totalRuleDTO, String nodeType, boolean saveWhiteIp, int order) {
        List<RuleDTO> iptables = totalRuleDTO.getIptables();
        if (CollectionUtils.isNotEmpty(iptables)) {
            iptables.forEach(ruleDto -> {
                ruleDto.setRuleType("ipv4");
                ruleDto.setNodeType(nodeType);
            });
            saveRules(iptables, saveWhiteIp, order);
        }
        List<RuleDTO> ip6tables = totalRuleDTO.getIp6tables();
        if (CollectionUtils.isNotEmpty(ip6tables)) {
            ip6tables.forEach(ruleDto -> {
                ruleDto.setRuleType("ipv6");
                ruleDto.setNodeType(nodeType);
            });
            saveRules(ip6tables, saveWhiteIp, order);
        }
    }

    @Override
    public void saveRules(List<RuleDTO> ruleDTOs, boolean saveWhiteIp, int order) {
        // 使用 AtomicInteger 生成递增的 ruleOrder
        AtomicInteger ruleOrderCounter = new AtomicInteger(order);
        // 将 RuleDTO 转换为 IptablesRule
        List<IptablesRule> rulesList = ruleDTOs.stream()
                .map(ruleDto -> convertRuleDTOToIptablesRule(ruleDto, ruleOrderCounter.getAndIncrement()))
                .collect(Collectors.toList());
        saveWhiteIp(ruleDTOs, saveWhiteIp);
        // 使用 flatMap 将 List<List<IptablesModule>> 扁平化为 List<IptablesModule>
        List<IptablesModule> moduleList = ruleDTOs.stream()
                .flatMap(ruleDto -> convertRuleDTOToIptablesModule(ruleDto).stream())
                .collect(Collectors.toList());
        // 将 RuleDTO 转换为 IptablesModuleOptions
        List<IptablesModuleOptions> moduleOptionsList = ruleDTOs.stream()
                .flatMap(ruleDto -> convertRuleDTOToIptablesModuleOptions(ruleDto).stream())
                .collect(Collectors.toList());
        defaultRuleDao.saveRules(rulesList, moduleList, moduleOptionsList);
    }

    private void saveWhiteIp(List<RuleDTO> ruleDTOs, boolean saveWhiteIp) {
        if (!saveWhiteIp) {
            log.info("skip save white ip");
            return;
        }
        List<IptablesWhiteIp> whiteIpList = new ArrayList<>(ruleDTOs.stream()
                .flatMap(ruleDto -> {
                    List<IptablesWhiteIp> ips = new ArrayList<>();
                    if (StringUtils.isNotBlank(ruleDto.getSrcIp())) {
                        ips.add(new IptablesWhiteIp(UUIDUtil.generate(), ruleDto.getSrcIp()));
                    }
                    if (StringUtils.isNotBlank(ruleDto.getDestIp())) {
                        ips.add(new IptablesWhiteIp(UUIDUtil.generate(), ruleDto.getDestIp()));
                    }
                    return ips.stream();
                })
                .collect(Collectors.toMap(
                        IptablesWhiteIp::getIp, // 使用 IP 地址作为键
                        ip -> ip, // 使用 IptablesWhiteIp 对象作为值
                        (existing, replacement) -> existing // 如果有重复，保留现有对象
                ))
                .values());
        trustedDeviceService.addTrustedDevice(whiteIpList);
    }

    /**
     * 将 RuleDTO 转换为 IptablesRule
     *
     * @param ruleDto RuleDTO 对象
     * @return IptablesRule 对象
     */
    private IptablesRule convertRuleDTOToIptablesRule(RuleDTO ruleDto, int ruleOrder) {
        IptablesRule iptablesRule = new IptablesRule();
        iptablesRule.setId(UUIDUtil.generate());
        iptablesRule.setRuleTable(ruleDto.getTable());
        iptablesRule.setRuleChain(ruleDto.getChain());
        iptablesRule.setInInterface(ruleDto.getInInterface());
        iptablesRule.setOutInterface(ruleDto.getOutInterface());
        iptablesRule.setProtocol(ruleDto.getProtocol());
        iptablesRule.setRuleType(ruleDto.getRuleType());
        iptablesRule.setRuleOrder(ruleOrder);
        iptablesRule.setNodeType(ruleDto.getNodeType());
        boolean isContainsPort = false;
        for (MatchDTO match : ruleDto.getMatches()) {
            if (match.getOptions() != null &&
                    (match.getOptions().containsKey("dport") || match.getOptions().containsKey("dports")
                            || match.getOptions().containsKey("sport") || match.getOptions().containsKey("sports"))) {
                isContainsPort = true;
            }
        }
        if (StringUtils.isNotBlank(ruleDto.getProtocol()) && ruleDto.getProtocol().equals("vrrp")) {
            isContainsPort = true;
        }
        if (StringUtils.isNotBlank(ruleDto.getDestIp()) && ruleDto.getDestIp().contains("{")) {
            iptablesRule.setDestIp(ruleDto.getDestIp());
        } else if (StringUtils.isNotBlank(ruleDto.getSrcIp()) && !"!*********/16".equals(ruleDto.getSrcIp())
                && !"!f0f0:6666:6666::/48".equals(ruleDto.getDestIp()) && isContainsPort) {
            iptablesRule.setWhiteMode("src");
            iptablesRule.setSrcIp("");
        } else if (StringUtils.isNotBlank(ruleDto.getDestIp()) && !"!*********/16".equals(ruleDto.getDestIp())
                && !"!f0f0:6666:6666::/48".equals(ruleDto.getDestIp()) && isContainsPort) {
            iptablesRule.setWhiteMode("dest");
            iptablesRule.setDestIp("");
        } else {
            iptablesRule.setSrcIp(ruleDto.getSrcIp());
            iptablesRule.setDestIp(ruleDto.getDestIp());
        }
        ruleDto.setId(iptablesRule.getId());
        // 其他属性的转换
        return iptablesRule;
    }

    private List<IptablesModule> convertRuleDTOToIptablesModule(RuleDTO ruleDto) {
        List<IptablesModule> moduleList = new ArrayList<>();
        if (ruleDto.getMatches() != null) {
            ruleDto.getMatches().forEach(matchDto -> {
                IptablesModule module = new IptablesModule();
                module.setId(UUIDUtil.generate());
                module.setRuleId(ruleDto.getId());
                module.setModuleType("matches");
                module.setModuleName(matchDto.getModule());
                moduleList.add(module);
                matchDto.setId(module.getId());
            });
            if (ruleDto.getTarget() != null) {
                IptablesModule module = new IptablesModule();
                module.setId(UUIDUtil.generate());
                module.setRuleId(ruleDto.getId());
                module.setModuleType("target");
                module.setModuleName(ruleDto.getTarget().getModule());
                moduleList.add(module);
                ruleDto.getTarget().setId(module.getId());
            }
        }
        return moduleList;
    }

    private List<IptablesModuleOptions> convertRuleDTOToIptablesModuleOptions(RuleDTO ruleDto) {
        List<IptablesModuleOptions> optionsList = new ArrayList<>();

        // 处理 matches 的 options
        if (ruleDto.getMatches() != null) {
            ruleDto.getMatches().forEach(matchDto -> matchDto.getOptions().forEach((key, value) -> {
                IptablesModuleOptions option = new IptablesModuleOptions();
                option.setId(UUIDUtil.generate());
                option.setModuleId(matchDto.getId());
                option.setOptionsName(key);
                option.setOptionsValue(value);
                optionsList.add(option);
            }));
        }

        // 处理 target 的 options
        if (ruleDto.getTarget() != null && ruleDto.getTarget().getOptions() != null) {
            ruleDto.getTarget().getOptions().forEach((key, value) -> {
                IptablesModuleOptions option = new IptablesModuleOptions();
                option.setId(UUIDUtil.generate());
                option.setModuleId(ruleDto.getTarget().getId());
                option.setOptionsName(key);
                option.setOptionsValue(value);
                optionsList.add(option);
            });
        }

        return optionsList;
    }

    @Override
    public void addRules(String rulesJson, String nodeType, boolean saveWhiteIp) {
        int maxOrder = defaultRuleDao.getMaxOrder();
        saveRules(rulesJson, nodeType, saveWhiteIp, maxOrder);
    }

    @Override
    public List<IptablesModuleOptions> getOptionsWithCm(String key) {
        return defaultRuleDao.getOptionsWithCm(key);
    }

    @Override
    @Transactional
    public void updateOptions(List<IptablesModuleOptions> optionsList) {
        defaultRuleDao.updateOptions(optionsList);
    }
}
