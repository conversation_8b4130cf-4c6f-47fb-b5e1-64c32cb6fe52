package com.xylink.manager.iptables.service.impl;

import com.xylink.config.Constants;
import com.xylink.config.NetworkConstants;
import com.xylink.manager.inspection.utils.JsonUtils;
import com.xylink.manager.iptables.db.IptablesModuleOptions;
import com.xylink.manager.iptables.enums.NodeType;
import com.xylink.manager.iptables.service.DefaultRuleService;
import com.xylink.manager.iptables.service.IptablesService;
import com.xylink.manager.iptables.service.RefreshNginxPortService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.watch.processor.impl.AllIpProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/9
 */
@Slf4j
@Service
public class RefreshNginxPortServiceImpl implements RefreshNginxPortService {

    private final IptablesService iptablesService;
    private final DefaultRuleService defaultRuleService;

    private final K8sService k8sService;

    public RefreshNginxPortServiceImpl(IptablesService iptablesService, DefaultRuleService defaultRuleService, K8sService k8sService) {
        this.iptablesService = iptablesService;
        this.defaultRuleService = defaultRuleService;
        this.k8sService = k8sService;
    }

    @Override
    public void refreshNginxPort(List<AllIpProcessor.ChangeData> changeData) {
        log.info("nginx port change, start refresh");
        List<IptablesModuleOptions> optionsList = defaultRuleService.getOptionsWithCm("all-ip:");
        if (CollectionUtils.isEmpty(optionsList)) {
            log.warn("nginx port change, but options is empty");
            return;
        }

        Set<String> set = changeData.stream()
                .map(AllIpProcessor.ChangeData::getKey)
                .collect(Collectors.toSet());
        if (!isRelevantKeyChanged(set)) {
            return;
        }

        updateOptionsValues(changeData, optionsList);
        defaultRuleService.updateOptions(optionsList);
        log.info("nginx port change, end refresh db");
        iptablesService.checkAndUpdateRules("refreshNginxPort", NodeType.COMMON_MAIN);
        log.info("nginx port change, end refresh iptables");
    }

    private boolean isRelevantKeyChanged(Set<String> set) {
        return set.contains(NetworkConstants.MAIN_NGINX_SSL_PORT) ||
                set.contains(NetworkConstants.MAIN_NGINX_PORT) ||
                set.contains(NetworkConstants.VOD_NGINX_SSL_PORT) ||
                set.contains(NetworkConstants.VOD_NGINX_PORT);
    }

    private void updateOptionsValues(List<AllIpProcessor.ChangeData> changeData, List<IptablesModuleOptions> optionsList) {
        for (AllIpProcessor.ChangeData change : changeData) {
            String key = change.getKey();
            optionsList.forEach(options -> {
                if (!options.getRelationCmKey().contains(key)) {
                    return;
                }
                String[] keys = extractKeysFromRelationCmKey(options);
                int portIndex = findKeyIndex(keys, key);
                if (portIndex != -1) {
                    String[] values = options.getOptionsValue().split(",");
                    values[portIndex] = String.valueOf(change.getNewValue());
                    options.setOptionsValue(String.join(",", values));
                }
            });
        }
    }

    private String[] extractKeysFromRelationCmKey(IptablesModuleOptions options) {
        return options.getRelationCmKey().split(":")[1].split(",");
    }

    private int findKeyIndex(String[] keys, String key) {
        for (int i = 0; i < keys.length; i++) {
            if (key.equals(keys[i])) {
                return i;
            }
        }
        return -1;
    }

    @Override
    public void refreshProxyPort(Map<String, String> keyValues) {
        log.info("proxy port change，start refresh, keyValues:{}", JsonUtils.objectToJsonString(keyValues));
        k8sService.editConfigmap(Constants.CONFIGMAP_MAIN_PROXY, keyValues);
        List<IptablesModuleOptions> optionsList = defaultRuleService.getOptionsWithCm("main-proxy.");
        if (CollectionUtils.isEmpty(optionsList)) {
            log.warn("proxy port change，but options is empty");
            return;
        }
        updateProxyOptionsValues(keyValues, optionsList);
        defaultRuleService.updateOptions(optionsList);
        log.info("proxy port change，end refresh db");
        iptablesService.checkAndUpdateRules("refreshProxyPort", NodeType.COMMON_MAIN);
        log.info("proxy port change，end refresh iptables");
    }

    private void updateProxyOptionsValues(Map<String, String> keyValues, List<IptablesModuleOptions> optionsList) {
        keyValues.forEach((key, value) -> optionsList.forEach(options -> {
            if (!options.getRelationCmKey().contains(key)) {
                return;
            }
            // 分割 relation_cm_key 得到键数组
            String[] keys = options.getRelationCmKey().split(",");
            // 查找 NGINX_SSL_PORT 的索引
            int portIndex = -1;
            for (int i = 0; i < keys.length; i++) {
                if (key.equals(keys[i])) {
                    portIndex = i;
                    break;
                }
            }

            // 若找到索引，则更新 options_value
            if (portIndex != -1) {
                String[] values = options.getOptionsValue().split(",");
                values[portIndex] = String.valueOf(value);
                String optionsNewValue = String.join(",", values);
                options.setOptionsValue(optionsNewValue);
            }
        }));
    }
}
