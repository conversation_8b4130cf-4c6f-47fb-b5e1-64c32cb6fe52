package com.xylink.manager.iptables.service.impl;

 import com.xylink.config.Constants;
import com.xylink.config.constant.CmsActiveStandbyConstants;
import com.xylink.config.exception.basic.ClientErrorException;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.manager.iptables.db.IptablesWhiteIp;
import com.xylink.manager.iptables.dto.CmsClusterInfo;
import com.xylink.manager.iptables.service.TrustedDeviceService;
import com.xylink.manager.model.common.Page;
import com.xylink.manager.model.common.Pageable;
import com.xylink.manager.service.base.K8sService;
import com.xylink.util.IpUtils;
import com.xylink.util.SearchDtoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2025/3/7
 */
@Service
@Slf4j
public class TrustedDeviceServiceImpl implements TrustedDeviceService {
    private static final String TRUSTED_IPV4_KEY = "trusted-ipv4";
    private static final String TRUSTED_IPV6_KEY = "trusted-ipv6";
    private static final String DEFAULT_NAMESPACE = "default";

    private static final String CONFIGMAP_NAME = "xylinkwalld-cm-white";

    private final K8sService k8sService;

    public TrustedDeviceServiceImpl(K8sService k8sService) {
        this.k8sService = k8sService;
    }

    @Override
    public Page<IptablesWhiteIp> getTrustedDevicePage(String key, Pageable pageable, boolean all) {
        List<IptablesWhiteIp> trustedDevices = getTrustedDeviceList();
        if (all) {
            return new Page<>(1, trustedDevices.size(), trustedDevices.size(), trustedDevices);
        }
        if (StringUtils.isNotBlank(key)) {
            trustedDevices = SearchDtoUtil.searchKeyWord(trustedDevices, key);
        }
        // 计算分页参数
        long pageNumber = pageable.getPageNumber();
        long pageSize = pageable.getPageSize();
        int totalElements = trustedDevices.size();
        int totalPages = (int) Math.ceil((double) totalElements / pageSize);

        // 处理页码超出范围的情况
        if (pageNumber < 1 || pageNumber > totalPages) {
            return new Page<>(pageNumber, pageSize, totalElements, new ArrayList<>());
        }

        // 计算起始和结束索引
        long start = (pageNumber - 1) * pageSize;
        long end = Math.min(start + pageSize, totalElements);

        // 获取分页数据
        List<IptablesWhiteIp> pageContent = trustedDevices.subList((int) start, (int) end);

        // 返回分页结果
        return new Page<>(pageNumber, pageSize, totalElements, pageContent);
    }

    @Override
    public List<IptablesWhiteIp> getTrustedDeviceList() {
        // 获取 ConfigMap 数据
        Map<String, String> configMapData = k8sService.getConfigmapOrCreate(DEFAULT_NAMESPACE, CONFIGMAP_NAME);

        // 获取 trusted-ipv4 和 trusted-ipv6 值
        String ipv4List = configMapData.getOrDefault(TRUSTED_IPV4_KEY, "");
        String ipv6List = configMapData.getOrDefault(TRUSTED_IPV6_KEY, "");
        // 将 IP 清单转换为对象列表
        List<IptablesWhiteIp> trustedDevices = new ArrayList<>();

        // 处理 IPv4 地址
        Arrays.stream(ipv4List.split(","))
                .filter(ip -> !ip.isEmpty())
                .map(ip -> {
                    IptablesWhiteIp iptablesWhiteIp = new IptablesWhiteIp();
                    iptablesWhiteIp.setIp(ip);
                    return iptablesWhiteIp;
                })
                .forEach(trustedDevices::add);

        // 处理 IPv6 地址
        Arrays.stream(ipv6List.split(","))
                .filter(ip -> !ip.isEmpty())
                .map(ip -> {
                    IptablesWhiteIp iptablesWhiteIp = new IptablesWhiteIp();
                    iptablesWhiteIp.setIp(ip);
                    return iptablesWhiteIp;
                })
                .forEach(trustedDevices::add);
        return trustedDevices;
    }

    @Override
    public CmsClusterInfo getCmsClusterInfo() {
        Map<String, String> configMapData = k8sService.getConfigmap("oceanbase-env");
        if (configMapData == null || configMapData.isEmpty()){
            return null;
        }
        CmsClusterInfo cmsClusterInfo = new CmsClusterInfo();
        if (configMapData.containsKey("current_cluster")) {
            String currentCluster = configMapData.get("current_cluster");
            if (currentCluster.equals("cluster1")) {
                cmsClusterInfo.setThisClusterIp(configMapData.get("cluster1_ip"));
                cmsClusterInfo.setOtherClusterIp(configMapData.get("cluster2_ip"));
            } else if (currentCluster.equals("cluster2")) {
                cmsClusterInfo.setThisClusterIp(configMapData.get("cluster2_ip"));
                cmsClusterInfo.setOtherClusterIp(configMapData.get("cluster1_ip"));
            }
        } else {
            log.warn("current_cluster not found in configMap");
        }
        Map<String, String> allIpMap = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        if(allIpMap != null && allIpMap.containsKey(CmsActiveStandbyConstants.ENV_PEER_IP)) {
            cmsClusterInfo.setPeerIp(allIpMap.get(CmsActiveStandbyConstants.ENV_PEER_IP));
        }
        if (allIpMap != null && allIpMap.containsKey(CmsActiveStandbyConstants.ENV_CURRENT_SERVER_IP)) {
            cmsClusterInfo.setBond0Ip(allIpMap.get(CmsActiveStandbyConstants.ENV_CURRENT_SERVER_IP));
        }
        return cmsClusterInfo;
    }

    @Override
    public void addTrustedDevice(List<IptablesWhiteIp> iptablesWhiteIpList) {
        // 获取或创建 ConfigMap
        Map<String, String> configMapData = k8sService.getConfigmapOrCreate(DEFAULT_NAMESPACE, CONFIGMAP_NAME);

        // 获取当前的 trusted-ipv4 和 trusted-ipv6 值
        StringBuilder currentIpv4 = new StringBuilder(configMapData.getOrDefault(TRUSTED_IPV4_KEY, ""));
        StringBuilder currentIpv6 = new StringBuilder(configMapData.getOrDefault(TRUSTED_IPV6_KEY, ""));

        for (IptablesWhiteIp iptablesWhiteIp : iptablesWhiteIpList) {
            processIpToAdd(iptablesWhiteIp, currentIpv4, currentIpv6);
        }

        // 更新 ConfigMap 数据
        updateConfigMapData(configMapData, currentIpv4, currentIpv6);

        // 更新 ConfigMap
        k8sService.editConfigmap(DEFAULT_NAMESPACE, CONFIGMAP_NAME, configMapData);
    }

    private void processIpToAdd(IptablesWhiteIp iptablesWhiteIp, StringBuilder currentIpv4, StringBuilder currentIpv6) {
        String newIpAndMask = iptablesWhiteIp.getIp();
        String newIp = extractIpWithoutMask(newIpAndMask);

        if (IpUtils.isIpv4(newIp)) {
            handleIpv4Addition(newIpAndMask, currentIpv4);
        } else if (IpUtils.isIpv6(newIp)) {
            handleIpv6Addition(newIpAndMask, currentIpv6);
        }
    }

    private void handleIpv4Addition(String newIpAndMask, StringBuilder currentIpv4) {
        if (currentIpv4.toString().contains(newIpAndMask)) {
            log.warn("IPv4 {} already exists", newIpAndMask);
            throw new ClientErrorException(ErrorStatus.TRUSTED_IP_HAS_ADD);
        }
        appendToBuilder(currentIpv4, newIpAndMask);
    }

    private void handleIpv6Addition(String newIpAndMask, StringBuilder currentIpv6) {
        if (currentIpv6.toString().contains(newIpAndMask) ||
                currentIpv6.toString().contains("[" + newIpAndMask + "]")) {
            log.warn("IPv6 {} already exists", newIpAndMask);
            throw new ClientErrorException(ErrorStatus.TRUSTED_IP_HAS_ADD);
        }
        appendToBuilder(currentIpv6, newIpAndMask);
    }

    private void appendToBuilder(StringBuilder builder, String value) {
        if (builder.length() == 0) {
            builder.append(value);
        } else {
            builder.append(",").append(value);
        }
    }

    private String extractIpWithoutMask(String ipWithMask) {
        return ipWithMask.contains("/") ? ipWithMask.split("/")[0] : ipWithMask;
    }

    private void updateConfigMapData(Map<String, String> configMapData, StringBuilder currentIpv4, StringBuilder currentIpv6) {
        if (currentIpv4.length() > 0) {
            configMapData.put(TRUSTED_IPV4_KEY, currentIpv4.toString());
        }
        if (currentIpv6.length() > 0) {
            configMapData.put(TRUSTED_IPV6_KEY, currentIpv6.toString());
        }
    }

    @Override
    public void deleteTrustedDevice(List<String> ipList) {
        // 获取或创建 ConfigMap
        Map<String, String> configMapData = k8sService.getConfigmapOrCreate(DEFAULT_NAMESPACE, CONFIGMAP_NAME);

        // 获取当前的 trusted-ipv4 和 trusted-ipv6 值
        String currentIpv4 = configMapData.getOrDefault(TRUSTED_IPV4_KEY, "");
        String currentIpv6 = configMapData.getOrDefault(TRUSTED_IPV6_KEY, "");

        // 将当前的 trusted-ipv4 和 trusted-ipv6 转换为列表
        List<String> ipv4List = new ArrayList<>(Arrays.asList(currentIpv4.split(",")));
        List<String> ipv6List = new ArrayList<>(Arrays.asList(currentIpv6.split(",")));

        // 移除空字符串
        ipv4List.removeIf(String::isEmpty);
        ipv6List.removeIf(String::isEmpty);


        for (String ip : ipList) {
            String ipToDelete = ip;
            // 遍历要删除的 IP 地址列表
            String ipToDeleteWithoutMask = ipToDelete;
            if (ipToDelete.contains("/")) {
                ipToDeleteWithoutMask = ipToDelete.split("/")[0];
            } else if (IpUtils.isIpv4(ipToDelete)) {
                ipToDelete = ipToDeleteWithoutMask + "/32";
            }
            // 判断 IP 地址类型并从相应的列表中移除
            if (IpUtils.isIpv4(ipToDeleteWithoutMask)) {
                ipv4List.remove(ipToDelete);
                ipv4List.remove(ipToDeleteWithoutMask);
            } else if (IpUtils.isIpv6(ipToDeleteWithoutMask)) {
                ipv6List.remove(ipToDelete);
                ipv6List.remove(ipToDeleteWithoutMask);
                ipv6List.remove("[" + ipToDeleteWithoutMask + "]");
            }
        }

        // 将更新后的 IP 列表转换回字符串
        String updatedIpv4 = String.join(",", ipv4List);
        String updatedIpv6 = String.join(",", ipv6List);

        // 将更新后的 IP 列表放入 configMapData
        configMapData.put(TRUSTED_IPV4_KEY, updatedIpv4);
        configMapData.put(TRUSTED_IPV6_KEY, updatedIpv6);

        // 更新 ConfigMap
        k8sService.editConfigmap(DEFAULT_NAMESPACE, CONFIGMAP_NAME, configMapData);
    }

}
