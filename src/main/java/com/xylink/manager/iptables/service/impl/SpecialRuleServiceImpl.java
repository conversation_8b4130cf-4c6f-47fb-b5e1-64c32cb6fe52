package com.xylink.manager.iptables.service.impl;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.xylink.manager.iptables.service.SpecialRuleService;
import com.xylink.manager.model.deploy.Node;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.manager.service.base.K8sService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.platform.commons.util.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/11
 */
@Service
@Slf4j
public class SpecialRuleServiceImpl implements SpecialRuleService {

    private static final String DEFAULT_NAMESPACE = "default";
    private static final String CONFIGMAP_NAME = "xylinkwalld-cm-white";
    private static final String RULES_IPV4_PREFIX = "rules-ipv4-"; // 定义常量
    private static final String RULES_IPV6_PREFIX = "rules-ipv6-"; // 定义常量

    private final K8sService k8sService;
    private final IDeployService deployService;

    public SpecialRuleServiceImpl(K8sService k8sService, IDeployService deployService) {
        this.k8sService = k8sService;
        this.deployService = deployService;
    }

    private Map<String, String> getConfigMapData() {
        return k8sService.getConfigmapOrCreate(DEFAULT_NAMESPACE, CONFIGMAP_NAME);
    }

    @Override
    public void addSpecialRule(String nodeName, String rule, int order, boolean isIpv4) {
        List<String> currentRules = getSpecialRules(nodeName, isIpv4);
        if (CollectionUtils.isNotEmpty(currentRules) && currentRules.contains(rule)) {
            log.warn("rules already exists: " + rule);
            return;
        }
        // 获取 ConfigMap 数据
        Map<String, String> configMapData = getConfigMapData();
        if (currentRules == null) {
            currentRules = new ArrayList<>();
        }
        currentRules.add(rule);
        // 使用Gson解析JSON列表
        Gson gson = new Gson();
        if (isIpv4) {
            configMapData.put(RULES_IPV4_PREFIX + nodeName, gson.toJson(currentRules));
        }
        if (!isIpv4) {
            configMapData.put(RULES_IPV6_PREFIX + nodeName, gson.toJson(currentRules));
        }
        // 更新 ConfigMap
        k8sService.editConfigmap(DEFAULT_NAMESPACE, CONFIGMAP_NAME, configMapData);
    }

    @Override
    public List<String> getSpecialRules(String nodeName, boolean isIpv4) {
        // 获取 ConfigMap 数据
        Map<String, String> configMapData = getConfigMapData();
        // 获取 trusted-ipv4 和 trusted-ipv6 值
        String currentRulesIpv4 = configMapData.getOrDefault(RULES_IPV4_PREFIX + nodeName, "");
        String currentRulesIpv6 = configMapData.getOrDefault(RULES_IPV6_PREFIX + nodeName, "");
        // 使用Gson解析JSON列表
        Gson gson = new Gson();
        Type listType = new TypeToken<List<String>>() {
        }.getType();
        if (isIpv4) {
            if (StringUtils.isBlank(currentRulesIpv4)) {
                return new ArrayList<>();
            }
            return gson.fromJson(currentRulesIpv4, listType);
        }
        if (StringUtils.isBlank(currentRulesIpv6)) {
            return new ArrayList<>();
        }
        return gson.fromJson(currentRulesIpv6, listType);
    }

    @Override
    public void clearSpecialRules(String nodeName, boolean isIpv4) {
        // 获取或创建 ConfigMap
        Map<String, String> configMapData = k8sService.getConfigmapOrCreate(DEFAULT_NAMESPACE, CONFIGMAP_NAME);
        if (isIpv4) {
            configMapData.put(RULES_IPV4_PREFIX + nodeName, "");
        } else {
            configMapData.put(RULES_IPV6_PREFIX + nodeName, "");
        }
        // 更新 ConfigMap
        k8sService.editConfigmap(DEFAULT_NAMESPACE, CONFIGMAP_NAME, configMapData);
    }

    @Override
    @Scheduled(cron = "0 0/10 * * * ?")
    public void checkSpecialRules() {
        // 获取或创建 ConfigMap
        Map<String, String> configMapData = k8sService.getConfigmapOrCreate(DEFAULT_NAMESPACE, CONFIGMAP_NAME);
        List<Node> nodeList = deployService.listAllNodes();
        for (Node node : nodeList) {
            String ipv4Key = RULES_IPV4_PREFIX + node.getName();
            String ipv6Key = RULES_IPV6_PREFIX + node.getName();
            configMapData.putIfAbsent(ipv4Key, "");
            configMapData.putIfAbsent(ipv6Key, "");
        }
        k8sService.editConfigmap(DEFAULT_NAMESPACE, CONFIGMAP_NAME, configMapData);
    }
}
