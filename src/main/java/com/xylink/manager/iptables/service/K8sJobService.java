package com.xylink.manager.iptables.service;

import com.xylink.config.Constants;
import com.xylink.manager.inspection.common.OpsManagerException;
import com.xylink.manager.iptables.enums.JobAction;
import com.xylink.manager.iptables.util.AesUtil;
import com.xylink.manager.model.deploy.Job;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.service.base.IDeployService;
import com.xylink.manager.service.base.K8sService;
import com.xylink.util.UUIDGenerator;
import io.fabric8.kubernetes.client.KubernetesClientException;
import io.fabric8.kubernetes.client.utils.IOHelpers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nonnull;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Created by niulong on 2022/11/2 6:06 下午
 */
@Service
@Slf4j
public class K8sJobService {
    @Autowired
    private K8sService k8sService;
    @Autowired
    private IDeployService deployService;

    public String createSecurityGroupJob(@Nonnull String jobName, @Nonnull String privateIp,
                                         String privateKey, String iv, @Nonnull JobAction action) {
        if (StringUtils.isBlank(privateKey)) {
            privateKey = UUIDGenerator.generate();
        }
        if (StringUtils.isBlank(iv)) {
            iv = UUIDGenerator.generate().substring(0, 16);
        }
        Map<String, String> installConfig = k8sService.getConfigmapOrCreate("install-job");
        if (CollectionUtils.isEmpty(installConfig)) {
            installConfig = new HashMap<>();
        }
        String securityImage = installConfig.get("SECURITY_IMAGE");
        if (StringUtils.isBlank(securityImage)) {
            securityImage = "hub.xylink.com:5000/private_cloud/xylinkwalld:v2.2";
        }
        try {
            InputStream yaml = new ClassPathResource("SecurityJob.yaml").getInputStream();
            String str = IOHelpers.readFully(yaml);

            String replace = str.replace("{{jobName}}", jobName)
                    .replace("{{ip}}", privateIp)
                    .replace("{{privateKey}}", privateKey)
                    .replace("{{iv}}", iv)
                    .replace("{{image}}", securityImage)
                    .replace("{{type}}", action.getMsg());
            // 使用 Yaml 类进行反序列化
            Job job = deployService.createOrReplaceJobByYaml(replace);
            log.info("Job created successfully, name: {}, ip: {}, actionType: {}, privateKey: {}, iv: {}", jobName, privateIp, action.getMsg(), privateKey, iv);
            // 获取 Pod 名称
            if (action == JobAction.QUERY) {
                return queryRules(job, privateIp, privateKey, iv);
            }
        } catch (IOException e) {
            log.error("io error", e);
            throw new OpsManagerException(HttpStatus.INTERNAL_SERVER_ERROR, "io error");
        } catch (KubernetesClientException e) {
            log.error("Error creating job", e);
            throw new OpsManagerException(HttpStatus.INTERNAL_SERVER_ERROR, "创建job失败");
        } catch (Exception e) {
            log.error("Error creating job", e);
        }
        return null;
    }

    private String queryRules(Job job, String privateIp, String privateKey, String iv) {
        final int MAX_ATTEMPTS = 10; // 设置最大重试次数
        String podName = getPodName(job);
        if (podName == null) {
            log.error("Pod not found or not running after {} attempts", MAX_ATTEMPTS);
            return "";
        }
        String realPolicyBuilder = getRealPolicyFromPod(job, podName);
        if (StringUtils.isBlank(realPolicyBuilder)) {
            return "";
        }
        String realPolicy = StringUtils.substringAfter(realPolicyBuilder, "[DATA] ");
        if (StringUtils.isBlank(realPolicy)) {
            return "";
        }
        try {
            log.info("privateIp:{}, privateKey:{}, iv:{}, realPolicy:'{}'", privateIp, privateKey, iv, realPolicy);
            return AesUtil.decryptCbc(privateKey, iv, realPolicy);
        } catch (Exception e) {
            log.error("queryRules 解密失败", e);
            return "";
        }
    }

    private String getPodName(Job job) {
        int attempts = 0;
        final int MAX_ATTEMPTS = 10;
        while (attempts < MAX_ATTEMPTS) {
            Pod pod = k8sService.getPodsByLabel(job.getNamespace(), "job-name", job.getName())
                    .stream()
                    .findFirst()
                    .orElse(null);
            if (pod != null && "Succeeded".equals(pod.getStatusPhase())) {
                return pod.getPodName();
            }
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                log.error("sleep error", e);
                // 恢复线程的中断状态
                Thread.currentThread().interrupt();
                throw new OpsManagerException(HttpStatus.INTERNAL_SERVER_ERROR, "中断异常");
            }
            attempts++;
        }
        return null;
    }

    private String getRealPolicyFromPod(Job job, String podName) {
        try (PipedOutputStream outputStream = new PipedOutputStream();
             PipedInputStream inputStream = new PipedInputStream()) {
            inputStream.connect(outputStream);
            deployService.watchPodLog(podName, job.getName(),null, outputStream);
            try (BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8), 8192)) {
                return bufferedReader.lines().reduce((first, second) -> second).orElse("");
            }
        } catch (Exception e) {
            if (!e.getMessage().contains("Write end dead")) {
                log.error("查询实际规则失败", e);
            }
            return "";
        }
    }


    private static final long TIMEOUT_DURATION = TimeUnit.MINUTES.toMillis(5);
    private static final long SLEEP_INTERVAL = 5000;
    public void jobIsFinish(String jobName) {
        long startTime = System.currentTimeMillis();
        while (System.currentTimeMillis() - startTime < TIMEOUT_DURATION) {
            Job job = deployService.getJobByName(jobName, Constants.NAMESPACE_DEFAULT);
            List<Job.Condition> conditions = job.getConditions();
            if (CollectionUtils.isEmpty(conditions)) {
                try {
                    Thread.sleep(SLEEP_INTERVAL);
                    continue;
                } catch (InterruptedException e) {
                    log.error("Sleep interrupted", e);
                    Thread.currentThread().interrupt();
                    return;
                }
            }
            boolean isJobCompleteOrFailed = conditions.stream()
                    .filter(Objects::nonNull)
                    .map(Job.Condition::getType)
                    .anyMatch(type -> "Complete".equals(type) || "Failed".equals(type));
            if (isJobCompleteOrFailed) {
                log.info("Job {} is either completed or failed.", jobName);
                return;
            }
            try {
                Thread.sleep(SLEEP_INTERVAL);
            } catch (InterruptedException e) {
                log.error("Sleep interrupted", e);
                Thread.currentThread().interrupt();
                return;
            }
        }
        log.info("Job {} did not complete or fail within the timeout period.", jobName);
    }
}
