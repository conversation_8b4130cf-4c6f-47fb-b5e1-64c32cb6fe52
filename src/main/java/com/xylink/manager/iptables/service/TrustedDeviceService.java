package com.xylink.manager.iptables.service;

import com.xylink.manager.iptables.db.IptablesWhiteIp;
import com.xylink.manager.iptables.dto.CmsClusterInfo;
import com.xylink.manager.model.common.Page;
import com.xylink.manager.model.common.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/4
 */
public interface TrustedDeviceService {

    Page<IptablesWhiteIp> getTrustedDevicePage(String key, Pageable pageable, boolean all);

    List<IptablesWhiteIp> getTrustedDeviceList();

    CmsClusterInfo getCmsClusterInfo();

    void addTrustedDevice(List<IptablesWhiteIp> iptablesWhiteIpList);

    void deleteTrustedDevice(List<String> ip);
}
