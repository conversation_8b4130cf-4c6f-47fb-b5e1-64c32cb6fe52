package com.xylink.manager.iptables.service;

import org.springframework.scheduling.annotation.Scheduled;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/11
 */
public interface SpecialRuleService {
    void addSpecialRule(String nodeName, String rule, int order, boolean isIpv4);

    List<String> getSpecialRules(String nodeName, boolean isIpv4);

    void clearSpecialRules(String nodeName, boolean isIpv4);

    void checkSpecialRules();
}
