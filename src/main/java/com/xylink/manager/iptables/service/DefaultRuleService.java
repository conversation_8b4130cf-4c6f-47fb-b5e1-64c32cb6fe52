package com.xylink.manager.iptables.service;

import com.xylink.manager.iptables.db.IptablesModuleOptions;
import com.xylink.manager.iptables.dto.RuleDTO;
import com.xylink.manager.iptables.dto.TotalRuleDTO;
import com.xylink.manager.iptables.enums.NodeType;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/24
 */
public interface DefaultRuleService {
    TotalRuleDTO getRules(NodeType nodeType);

    void saveRules(String rulesJson, String nodeType, boolean saveWhiteIp, int order);

    void saveRules(List<RuleDTO> ruleDTOs, boolean saveWhiteIp, int order);

    void saveRules(TotalRuleDTO totalRuleDTO, String nodeType, boolean saveWhiteIp, int order);

    void addRules(String rulesJson, String nodeType, boolean saveWhiteIp);

    List<IptablesModuleOptions> getOptionsWithCm(String key);

    void updateOptions(List<IptablesModuleOptions> optionsList);
}
