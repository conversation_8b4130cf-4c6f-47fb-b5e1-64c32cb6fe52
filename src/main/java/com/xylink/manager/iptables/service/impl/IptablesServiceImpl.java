package com.xylink.manager.iptables.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.xylink.config.Constants;
import com.xylink.manager.inspection.common.OpsManagerException;
import com.xylink.manager.inspection.utils.JsonUtils;
import com.xylink.manager.iptables.dto.RuleDTO;
import com.xylink.manager.iptables.dto.TotalRuleDTO;
import com.xylink.manager.iptables.enums.JobAction;
import com.xylink.manager.iptables.enums.NodeType;
import com.xylink.manager.iptables.service.DefaultRuleService;
import com.xylink.manager.iptables.service.IptablesService;
import com.xylink.manager.iptables.service.K8sJobService;
import com.xylink.manager.iptables.service.SpecialRuleService;
import com.xylink.manager.iptables.util.AesUtil;
import com.xylink.manager.iptables.util.IptablesRuleParser;
import com.xylink.manager.iptables.util.Pair3;
import com.xylink.manager.iptables.util.RuleUtil;
import com.xylink.manager.model.deploy.Node;
import com.xylink.manager.service.base.K8sService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/19
 */
@Service
@Slf4j
public class IptablesServiceImpl implements IptablesService {
    public static final String NODE_TYPE_COMMON_MAIN = "common-main";
    public static final String OTHER_NODE_LABEL = "other-node";
    private final K8sJobService jobService;
    private final K8sService k8sService;
    private final DefaultRuleService defaultRuleService;
    private final SpecialRuleService specialRuleService;

    public IptablesServiceImpl(K8sJobService jobService, K8sService k8sService, DefaultRuleService defaultRuleService, SpecialRuleService specialRuleService) {
        this.jobService = jobService;
        this.k8sService = k8sService;
        this.defaultRuleService = defaultRuleService;
        this.specialRuleService = specialRuleService;
    }

    @Override
    @Async
    public synchronized void checkAndUpdateRules(String source, NodeType nodeType) {
        log.info("iptables check rules, source:{}, nodeType:{}", source, nodeType.getType());
        Map<String, String> configmap = k8sService.getConfigmapOrCreate("xylinkwalld-cm");
        if (configmap == null) {
            log.warn("xylinkwalld-cm is null");
            configmap = new HashMap<>();
        }
        if (configmap.containsKey("auto-refresh-switch") && configmap.get("auto-refresh-switch").equals("false")) {
            log.info("auto-refresh-switch is false");
            return;
        }
        List<Node> nodeList = k8sService.listNode();

        TotalRuleDTO totalRuleDTO = defaultRuleService.getRules(nodeType);
        //主机节点规则
        if (nodeType == NodeType.ALL || nodeType == NodeType.COMMON_MAIN) {
            updateMainNodeRules(nodeList, configmap, totalRuleDTO);
        }
        //其它节点规则
        if (nodeType == NodeType.ALL || nodeType == NodeType.OTHER_NODE) {
            updateOtherNodeRules(nodeList, configmap, totalRuleDTO);
        }
    }

    private void updateMainNodeRules(List<Node> nodeList, Map<String, String> configmap, TotalRuleDTO totalRuleDTO) {
        //main节点规则
        Node mainNode = nodeList.stream()
                .filter(node -> node.getIp().equals("**********"))
                .findFirst()
                .orElse(null);
        if (mainNode == null) {
            log.warn("main node is not exist");
            return;
        }
        TotalRuleDTO mainNodeRules = new TotalRuleDTO();
        List<RuleDTO> iptables = totalRuleDTO.getIptables().stream()
                .filter(rule -> StringUtils.equals(rule.getNodeType(), NODE_TYPE_COMMON_MAIN)).collect(Collectors.toList());
        List<RuleDTO> ip6tables = totalRuleDTO.getIp6tables().stream()
                .filter(rule -> StringUtils.equals(rule.getNodeType(), NODE_TYPE_COMMON_MAIN)).collect(Collectors.toList());
        mainNodeRules.setIptables(iptables);
        mainNodeRules.setIp6tables(ip6tables);
        addSpecialRules(mainNode.getName(), iptables, true);
        addSpecialRules(mainNode.getName(), ip6tables, false);
        checkAndUpdateRules(mainNode, configmap, mainNodeRules, mainNode.getName());
    }

    private void updateOtherNodeRules(List<Node> nodeList, Map<String, String> configmap, TotalRuleDTO totalRuleDTO) {
        //其它节点规则
        TotalRuleDTO otherNodeRules = new TotalRuleDTO();
        List<RuleDTO> iptablesOtherNode = totalRuleDTO.getIptables().stream()
                .filter(rule -> !StringUtils.equals(rule.getNodeType(), NODE_TYPE_COMMON_MAIN)).collect(Collectors.toList());
        List<RuleDTO> ip6tablesOtherNode = totalRuleDTO.getIp6tables().stream()
                .filter(rule -> !StringUtils.equals(rule.getNodeType(), NODE_TYPE_COMMON_MAIN)).collect(Collectors.toList());
        otherNodeRules.setIptables(iptablesOtherNode);
        otherNodeRules.setIp6tables(ip6tablesOtherNode);
        if (iptablesOtherNode.isEmpty() && ip6tablesOtherNode.isEmpty()) {
            log.info("other node rules is empty");
            return;
        }
        for (Node node : nodeList) {
            if (StringUtils.equals(node.getType(), NODE_TYPE_COMMON_MAIN)) {
                continue;
            }
            addSpecialRules(node.getName(), iptablesOtherNode, true);
            addSpecialRules(node.getName(), ip6tablesOtherNode, false);
            checkAndUpdateRules(node, configmap, otherNodeRules, OTHER_NODE_LABEL);
        }
    }

    private void checkAndUpdateRules(Node node, Map<String, String> configmap, TotalRuleDTO totalRuleDTO, String nodeType) {
        log.info("start config iptables for node:{}", node.getName());
        RuleUtil.sortRules(totalRuleDTO.getIptables());
        RuleUtil.sortRules(totalRuleDTO.getIp6tables());
        String privateKey;
        String encryptValue;
        String iv;
        String jobNamePrefix = "firewall-" + nodeType + "-";
        String jobName = jobNamePrefix + System.currentTimeMillis();
        String realRules = jobService.createSecurityGroupJob(jobName, node.getName(), "", "",
                JobAction.QUERY);
        String destRules = JsonUtils.objectToJsonNonNull(totalRuleDTO);
        log.debug("nodeName:{},iptables real rules:{}", node.getName(), realRules);
        log.debug("nodeName:{},iptables dest rules:{}", node.getName(), destRules);
        if (StringUtils.equals(destRules, realRules)) {
            log.info("iptables rules not change");
            return;
        }
        if (isSame(node.getName(), realRules, totalRuleDTO)) {
            log.info("iptables rules not change");
            return;
        }
        //写configMap
        try {
            Pair3<String, String, String> encryptSecond = AesUtil.encryptCbc(destRules);
            privateKey = encryptSecond.getFirst();
            iv = encryptSecond.getSecond();
            encryptValue = encryptSecond.getThird();
        } catch (Exception e) {
            log.error("iptables Generator key error", e);
            throw new OpsManagerException(HttpStatus.INTERNAL_SERVER_ERROR, "Aes加密失败");
        }
        configmap.put("rules", encryptValue);
        k8sService.editConfigmap(Constants.NAMESPACE_DEFAULT, "xylinkwalld-cm", configmap);
        jobName = jobNamePrefix + System.currentTimeMillis();
        jobService.createSecurityGroupJob(jobName, node.getName(), privateKey, iv, JobAction.BIND);
        jobService.jobIsFinish(jobName);
    }

    private boolean isSame(String nodeName, String realRulesJson, TotalRuleDTO destRules) {
        addSpecialRules(nodeName, destRules.getIptables(), true);
        addSpecialRules(nodeName, destRules.getIp6tables(), false);
        TotalRuleDTO realRules;
        if (StringUtils.isBlank(realRulesJson)) {
            log.info("realRulesJson is null");
            return false;
        }
        realRules = JsonUtils.jsonToPo(realRulesJson, new TypeReference<TotalRuleDTO>() {
        });
        if (realRules == null) {
            log.info("realRules is null");
            return false;
        }
        if (realRules.getIptables().size() != destRules.getIptables().size()) {
            log.info("realRules.getIptables().size() != destRules.getIptables().size()");
            return false;
        }

        if (realRules.getIp6tables().size() != destRules.getIp6tables().size()) {
            log.info("realRules.getIp6tables().size() != destRules.getIp6tables");
            return false;
        }

        // 对比 iptables 规则
        boolean isSameV4 = RuleUtil.isSame("iptables", destRules.getIptables(), realRules.getIptables());
        // 对比 ip6tables 规则
        boolean isSameV6 = RuleUtil.isSame("ip6tables", destRules.getIp6tables(), realRules.getIp6tables());
        return isSameV4 && isSameV6;
    }


    @Override
    public void addSpecialRules(String nodeName, List<RuleDTO> ruleDTOList, boolean isIpv4) {
        List<RuleDTO> list = new ArrayList<>();
        List<String> specialRules = specialRuleService.getSpecialRules(nodeName, isIpv4);
        if (CollectionUtils.isEmpty(specialRules)) {
            log.info("specialRules is empty");
            return;
        }
        for (String specialRule : specialRules) {
            if (StringUtils.equals(specialRule, "null")) {
                continue;
            }
            //JsonUtils.objectToJsonNonNull(IptablesRuleParser.parseIptablesRule(specialRule));
            //RuleDTO ruleDTO = JsonUtils.jsonToPo(AesUtil.decryptCBC(specialRule), new TypeReference<RuleDTO>() {});
            RuleDTO ruleDTO = IptablesRuleParser.parseIptablesRule(specialRule);
            if (StringUtils.isNotBlank(ruleDTO.getSrcIp()) && !ruleDTO.getSrcIp().contains("/")) {
                ruleDTO.setSrcIp(ruleDTO.getSrcIp() + "/32");
            }
            if (StringUtils.isNotBlank(ruleDTO.getDestIp()) && !ruleDTO.getDestIp().contains("/")) {
                ruleDTO.setDestIp(ruleDTO.getDestIp() + "/32");
            }
            ruleDTO.setRuleOrder(0);
            list.add(ruleDTO);
        }
        if (!list.isEmpty()) {
            ruleDTOList.addAll(list);
        }
    }
}
