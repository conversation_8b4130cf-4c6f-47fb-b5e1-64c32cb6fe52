package com.xylink.manager.iptables.service;

import com.xylink.manager.iptables.enums.NodeType;
import com.xylink.manager.model.deploy.Pod;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.base.SystemModeConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/3/6
 */
@Service
@Slf4j
public class IptablesJob {
    private static final String REFRESH_SWITCH = "auto-refresh-switch";
    private static final String CONFIG_MAP_NAME = "xylinkwalld-cm";
    private static final int CLEAR_JOB_MINUTES = 10;
    private final IptablesService iptablesService;
    private final K8sService k8sService;

    public IptablesJob(IptablesService iptablesService, K8sService k8sService) {
        this.iptablesService = iptablesService;
        this.k8sService = k8sService;
    }

    @Scheduled(cron = "0 0/10 * * * ?")
    public void checkRules() {
        if (!SystemModeConfig.isNewCms()) {
            log.info("not new cms mode, not check rules");
            return;
        }
        try {
            Map<String, String> configmap = k8sService.getConfigmapOrCreate(CONFIG_MAP_NAME);
            if (configmap == null) {
                log.warn("xylinkwalld-cm is null");
                configmap = new HashMap<>();
            }
            if (!configmap.containsKey(REFRESH_SWITCH)) {
                configmap.put(REFRESH_SWITCH, "true");
                k8sService.editConfigmap(CONFIG_MAP_NAME, configmap);
            }
            iptablesService.checkAndUpdateRules("job", NodeType.ALL);
        } catch (Exception e) {
            log.error("check rules error", e);
        }
    }


    @Scheduled(cron = "0 0/5 * * * ?")
    public void clearJobs() {
        if (!SystemModeConfig.isNewCms()) {
            log.info("not new cms mode, not check rules");
            return;
        }
        try {
            List<Pod> pods = k8sService.getPodListWithLabelInApp("xylinkwalld");
            pods.forEach(pod -> {
                String creationTimestamp = pod.getCreationTimestamp();
                if (StringUtils.isNotBlank(creationTimestamp)) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
                    sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
                    try {
                        Date date = sdf.parse(creationTimestamp);
                        long timestamp = date.getTime();
                        if (System.currentTimeMillis() - timestamp > 1000 * 60 * CLEAR_JOB_MINUTES) {
                            k8sService.deletePod(pod.getPodName());
                        }
                    } catch (ParseException e) {
                        log.error("clearJobs error", e);
                    }
                }
            });
        } catch (Exception e) {
            log.error("clearJobs error", e);
        }
    }
}
