package com.xylink.manager.iptables.util;

import com.xylink.manager.inspection.utils.JsonUtils;
import com.xylink.manager.iptables.dto.MatchDTO;
import com.xylink.manager.iptables.dto.RuleDTO;
import com.xylink.manager.iptables.dto.TargetDTO;
import com.xylink.manager.iptables.dto.TotalRuleDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/3/11
 */
@Slf4j
public class IptablesRuleParser {

    public static RuleDTO parseIptablesRule(String iptablesRule) {
        RuleDTO rule = new RuleDTO();
        rule.setMatches(new ArrayList<>());
        rule.setTable("filter");

        String[] parts = iptablesRule.split(" ");
        for (int i = 0; i < parts.length; i++) {
            String part = parts[i];
            if ("-t".equals(part)) {
                handleTable(parts, i, rule);
            } else if ("-I".equals(part)) {
                handleChain(parts, i, rule);
            } else if ("-s".equals(part)) {
                handleSrcIp(parts, i, rule);
            } else if ("-d".equals(part)) {
                handleDestIp(parts, i, rule);
            } else if ("-i".equals(part)) {
                handleInInterface(parts, i, rule);
            } else if ("-o".equals(part)) {
                handleOutInterface(parts, i, rule);
            } else if ("-p".equals(part)) {
                handleProtocol(parts, i, rule);
            } else if ("-m".equals(part)) {
                handleModule(parts, i, rule);
            } else if ("--dport".equals(part)) {
                handleDPort(parts, i, rule);
            } else if ("-j".equals(part)) {
                handleTarget(parts, i, rule);
            }
        }

        return rule;
    }

    private static void handleTable(String[] parts, int index, RuleDTO rule) {
        if (index + 1 < parts.length) {
            rule.setTable(parts[index + 1]);
        }
    }

    private static void handleChain(String[] parts, int index, RuleDTO rule) {
        if (index + 1 < parts.length) {
            rule.setChain(parts[index + 1]);
        }
    }

    private static void handleSrcIp(String[] parts, int index, RuleDTO rule) {
        if (index + 1 < parts.length) {
            rule.setSrcIp(parts[index + 1]);
        }
    }

    private static void handleDestIp(String[] parts, int index, RuleDTO rule) {
        if (index + 1 < parts.length) {
            rule.setDestIp(parts[index + 1]);
        }
    }

    private static void handleInInterface(String[] parts, int index, RuleDTO rule) {
        if (index + 1 < parts.length) {
            rule.setInInterface(parts[index + 1]);
        }
    }

    private static void handleOutInterface(String[] parts, int index, RuleDTO rule) {
        if (index + 1 < parts.length) {
            rule.setOutInterface(parts[index + 1]);
        }
    }

    private static void handleProtocol(String[] parts, int index, RuleDTO rule) {
        if (index + 1 < parts.length) {
            rule.setProtocol(parts[index + 1]);
        }
    }

    private static void handleModule(String[] parts, int index, RuleDTO rule) {
        if (index + 1 < parts.length) {
            String module = parts[index + 1];
            Map<String, String> options = new HashMap<>();
            index += 2; // 跳过模块名
            parseOptions(parts, index, options);
            MatchDTO match = new MatchDTO();
            match.setModule(module);
            match.setOptions(options);
            rule.getMatches().add(match);
        }
    }

    private static void handleDPort(String[] parts, int index, RuleDTO rule) {
        if (index + 1 < parts.length && !parts[index + 1].startsWith("-")) {
            MatchDTO match = new MatchDTO();
            match.setModule(rule.getProtocol());
            Map<String, String> options = new HashMap<>();
            options.put("dport", parts[index + 1]);
            match.setOptions(options);
            rule.getMatches().add(match);
        }
    }

    private static void handleTarget(String[] parts, int index, RuleDTO rule) {
        if (index + 1 < parts.length) {
            TargetDTO target = new TargetDTO();
            target.setModule(parts[index + 1]);
            Map<String, String> targetOptions = new HashMap<>();
            index += 2; // 跳过目标动作
            parseOptions(parts, index, targetOptions);
            target.setOptions(targetOptions);
            rule.setTarget(target);
        }
    }

    private static int parseOptions(String[] parts, int startIndex, Map<String, String> options) {
        int i = startIndex;
        while (i < parts.length && parts[i].startsWith("--")) {
            String optionKey = parts[i].substring(2);
            StringBuilder optionValue = new StringBuilder();
            i++; // 跳过选项键
            if (i < parts.length && !parts[i].startsWith("-")) {
                optionValue.append(parts[i]);
                i++;
                // 处理带有空格的值
                while (i < parts.length && !parts[i].startsWith("-")) {
                    optionValue.append(" ").append(parts[i]);
                    i++;
                }
            }
            if ("comment".equals(optionKey)) {
                optionValue = new StringBuilder(removeQuotes(optionValue.toString()));
            }
            options.put(optionKey, optionValue.toString());
        }
        return i - 1;
    }

    private static String removeQuotes(String value) {
        if (value.startsWith("\"") && value.endsWith("\"")) {
            return value.substring(1, value.length() - 1);
        }
        if (value.startsWith("'") && value.endsWith("'")) {
            return value.substring(1, value.length() - 1);
        }
        return value;
    }

    /**
     * 根据 RuleDTO 生成 iptables/ip6tables 命令字符串
     */
    public static String generateRuleCommand(RuleDTO rule, boolean isIpv6) {
        StringBuilder command = new StringBuilder();

        // 设置 iptables 或 ip6tables
        if (isIpv6) {
            command.append("ip6tables");
        } else {
            command.append("iptables");
        }

        // 添加表名
        if (rule.getTable() != null) {
            command.append(" -t ").append(rule.getTable());
        }

        // 添加链名
        if (rule.getChain() != null) {
            command.append(" -A ").append(rule.getChain());
        }

        // 添加源地址
        if (rule.getSrcIp() != null) {
            command.append(" -s ").append(rule.getSrcIp());
        }

        // 添加目的地址
        if (rule.getDestIp() != null) {
            command.append(" -d ").append(rule.getDestIp());
        }

        // 添加入接口
        if (rule.getInInterface() != null) {
            command.append(" -i ").append(rule.getInInterface());
        }

        // 添加出接口
        if (rule.getOutInterface() != null) {
            command.append(" -o ").append(rule.getOutInterface());
        }

        // 添加协议
        if (rule.getProtocol() != null) {
            command.append(" -p ").append(rule.getProtocol());
        }

        // 添加匹配条件
        for (MatchDTO match : rule.getMatches()) {
            command.append(" -m ").append(match.getModule());

            for (Map.Entry<String, String> entry : match.getOptions().entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();

                if ("dport".equals(key)) {
                    command.append(" --dport ").append(value);
                } else if ("dports".equals(key)) {
                    command.append(" --dports ").append(value);
                } else if ("sport".equals(key)) {
                    command.append(" --sport ").append(value);
                } else if ("sports".equals(key)) {
                    command.append(" --sports ").append(value);
                } else if ("comment".equals(key)) {
                    command.append(" --comment \"").append(value).append("\"");
                } else {
                    command.append(" --").append(key).append(" ").append(value);
                }
            }
        }

        // 添加目标动作
        TargetDTO target = rule.getTarget();
        if (target != null) {
            command.append(" -j ").append(target.getModule());

            for (Map.Entry<String, String> entry : target.getOptions().entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                command.append(" --").append(key).append(" ").append(value);
            }
        }

        return command.toString();
    }

    public static void main(String[] args) {
        String iptablesRuleNat = "-I CMS_OUTPUT -d 2001:db8:85a3::8a2e:370:7334/128 -o bond0 -p tcp -m multiport --dports 6100,6101 -m comment --comment \"cms访问crs ms访问crs\" -j ACCEPT";
        TotalRuleDTO totalRuleDTO = new TotalRuleDTO();
        totalRuleDTO.setIptables(new ArrayList<>());
        RuleDTO ruleDTONat = parseIptablesRule(iptablesRuleNat);
        log.info("Nat Rule: " +  JsonUtils.objectToJsonNonNull(ruleDTONat));
    }
}