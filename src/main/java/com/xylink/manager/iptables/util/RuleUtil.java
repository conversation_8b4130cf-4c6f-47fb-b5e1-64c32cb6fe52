package com.xylink.manager.iptables.util;

import com.xylink.manager.inspection.utils.JsonUtils;
import com.xylink.manager.iptables.dto.RuleDTO;
import com.xylink.manager.iptables.dto.TargetDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/20
 */
@Slf4j
public class RuleUtil {

    // 添加私有构造函数，防止实例化
    private RuleUtil() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
    public static void sortRules(List<RuleDTO> ruleList) {
        ruleList.sort(Comparator.comparingInt(RuleDTO::getRuleOrder));
        ruleList.sort(Comparator.comparingInt(rule -> {
            if (StringUtils.isNotBlank(rule.getSrcIp()) && rule.getSrcIp().contains("!")) {
                return 1;
            }
            if (StringUtils.isNotBlank(rule.getDestIp()) && rule.getDestIp().contains("!")) {
                return 1;
            }
            TargetDTO target = rule.getTarget();
            if (target != null && "DROP".equals(target.getModule())) {
                return 1;
            }
            if (target == null) {
                log.warn("target is null:{}", rule);
            }
            return 0;
        }));
    }

    public static boolean isSame(String ruleType, List<RuleDTO> currentRules, List<RuleDTO> realRules) {
        if (currentRules == null || realRules == null) {
            log.info(ruleType + " rules: One of the lists is null.");
            return false;
        }

        currentRules.forEach(currentRule -> {
            currentRule.setRuleOrder(null);
            currentRule.setId(null);
            currentRule.setWhiteMode(null);
            currentRule.setNodeType(null);
            currentRule.setRuleType(null);
        });

        Set<Object> currentRuleSet = new HashSet<>(currentRules);
        Set<Object> realRuleSet = new HashSet<>(realRules);

        // 找出 currentRuleSet 中独有的规则
        Set<Object> uniqueInCurrent = currentRuleSet.stream()
                .filter(rule -> !realRuleSet.contains(rule))
                .collect(Collectors.toSet());

        // 找出 realRuleSet 中独有的规则
        Set<Object> uniqueInReal = realRuleSet.stream()
                .filter(rule -> !currentRuleSet.contains(rule))
                .collect(Collectors.toSet());

        if (!uniqueInCurrent.isEmpty()) {
            log.warn(ruleType + " rules: Unique in current:" + JsonUtils.objectToJsonNonNull(uniqueInCurrent));
        }

        if (!uniqueInReal.isEmpty()) {
            log.warn(ruleType + " rules: Unique in real:" + JsonUtils.objectToJsonNonNull(uniqueInReal));
        }

        if (uniqueInCurrent.isEmpty() && uniqueInReal.isEmpty()) {
            log.info(ruleType + " rules: No differences found.");
            return true;
        }
        return false;
    }

    public static boolean isSame(RuleDTO rule1, RuleDTO rule2) {
        return JsonUtils.objectToJsonNonNull(rule1).equals(JsonUtils.objectToJsonNonNull(rule2));
    }
}
