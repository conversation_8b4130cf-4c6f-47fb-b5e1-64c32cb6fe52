package com.xylink.manager.iptables.util;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Create at 11:30 上午 2022/10/11
 */
public class Pair2<A, B> implements Serializable {

    private transient A first;
    private transient B second;

    public Pair2() {

    }

    public Pair2(A first, B second) {
        this.first = first;
        this.second = second;
    }

    public A getFirst() {
        return first;
    }

    public B getSecond() {
        return second;
    }

    @Override
    public String toString() {
        return "Pair2{" +
                "first=" + first +
                ", second=" + second +
                '}';
    }
}
