package com.xylink.manager.iptables.util;

import com.xylink.manager.inspection.common.OpsManagerException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.util.Base64Utils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * <AUTHOR>
 * Create at 5:56 下午 2023/3/30
 */
public class AesUtil {
    private static final Logger logger = LoggerFactory.getLogger(AesUtil.class);

    private static final String ALGORITHM = "AES";
    private static final String CIPHER_ALGORITHM = "AES/CBC/PKCS5Padding";

    private static final String KEY = "46e831dfa5964115aaca98a58d705c11";
    private static final String IV = "8bc4333c38ec42d4";

    /**
     * 跨云使用
     */
    private static final String CLOUD_SECRET_KEY = "0KJVfUeHx4hiVV/VW1sSiw==";
    private static final String CLOUD_IV = "0000000000000000";

    /***CBC***/
    public static Pair3<String, String, String> encryptCbc(String plainText) throws Exception {
        String key = UUIDGenerator.generate();
        String iv = UUIDGenerator.generate().substring(0, 16);
        SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(), ALGORITHM);
        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, new IvParameterSpec(iv.getBytes()));
        return new Pair3<>(key, iv, Base64Utils.encodeToString(cipher.doFinal(plainText.getBytes())));

    }

    public static String decryptCbc(String key, String iv, String cipherText) throws Exception {
        SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(), ALGORITHM);
        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, keySpec, new IvParameterSpec(iv.getBytes()));
        //解码
        return new String(cipher.doFinal(Base64Utils.decodeFromString(cipherText)));
    }

    /*** ECB**/
    public static String encrypt(String value) throws Exception {
        String key = UUIDGenerator.generate();
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), ALGORITHM);
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);

        byte[] encryptedValueBytes = cipher.doFinal(value.getBytes());
        return Base64Utils.encodeToString(encryptedValueBytes);
    }

    public static String decrypt(String encryptedValue, String key) throws Exception {
        byte[] data = Base64Utils.decodeFromString(encryptedValue);
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), ALGORITHM);
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, secretKey);

        byte[] decryptedValueBytes = cipher.doFinal(data);
        return new String(decryptedValueBytes);
    }


    /***CBC***/
    public static String encryptCBC(String plainText) {
        try {
            SecretKeySpec keySpec = new SecretKeySpec(KEY.getBytes(), ALGORITHM);
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, new IvParameterSpec(IV.getBytes()));
            return Base64Utils.encodeToString(cipher.doFinal(plainText.getBytes()));
        } catch (Exception e) {
            logger.error("AES encryptCBC error", e);
            throw new OpsManagerException(HttpStatus.BAD_REQUEST, "参数错误");
        }
    }

    public static String decryptCBC(String cipherText) {
        if (StringUtils.isBlank(cipherText)) {
            return "";
        }
        try {
            SecretKeySpec keySpec = new SecretKeySpec(KEY.getBytes(), ALGORITHM);
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, new IvParameterSpec(IV.getBytes()));
            //解码
            return new String(cipher.doFinal(Base64Utils.decodeFromString(cipherText)));
        } catch (Exception e) {
            throw new OpsManagerException(HttpStatus.BAD_REQUEST, "参数错误");
        }
    }


    /***跨云专用***/
    public static String encryptToCloud(String plainText) {
        try {
            SecretKeySpec keySpec = new SecretKeySpec(CLOUD_SECRET_KEY.getBytes(), ALGORITHM);
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, new IvParameterSpec(CLOUD_IV.getBytes()));
            return Base64Utils.encodeToString(cipher.doFinal(plainText.getBytes()));
        } catch (Exception e) {
            logger.error("AES encryptCBC error", e);
            return "";
        }
    }

    public static String decryptToCloud(String cipherText) {
        try {
            SecretKeySpec keySpec = new SecretKeySpec(CLOUD_SECRET_KEY.getBytes(), ALGORITHM);
            Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, new IvParameterSpec(CLOUD_IV.getBytes()));
            //解码
            return new String(cipher.doFinal(Base64Utils.decodeFromString(cipherText)));
        } catch (Exception e) {
            throw new OpsManagerException(HttpStatus.BAD_REQUEST, "参数错误");
        }
    }

    public static void main(String[] args) throws Exception {

    }
}
