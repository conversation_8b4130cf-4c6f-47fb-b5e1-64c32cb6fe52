package com.xylink.manager.iptables.util;

import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.Security;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @2025/01/14 17:33:02
 * @version 1.0
 */
@Service
@Slf4j
public class AesEncryptUtil {

    private static final String INIT_ERROR_LOG_MESSAGE = "Initialization error: ";

    // 算法名称
    private static final String KEY_ALGORITHM = "AES";
    // 加解密算法/模式/填充方式
    private static final String ALGORITHMSTR = "AES/CBC/PKCS7Padding";
    //
    private Key key;
    private Cipher cipher;


    public void init(byte[] keyBytes) {
        int base = 16;
        if (keyBytes.length % base != 0) {
            int groups = keyBytes.length / base + (keyBytes.length % base != 0 ? 1 : 0);
            byte[] temp = new byte[groups * base];
            Arrays.fill(temp, (byte) 0);
            System.arraycopy(keyBytes, 0, temp, 0, keyBytes.length);
            keyBytes = temp;
        }
        Security.addProvider(new BouncyCastleProvider());
        key = new SecretKeySpec(keyBytes, KEY_ALGORITHM);
        try {
            cipher = Cipher.getInstance(ALGORITHMSTR, "BC");
        } catch (NoSuchAlgorithmException e) {
            log.error(INIT_ERROR_LOG_MESSAGE, e);
        } catch (NoSuchPaddingException e) {
            log.error(INIT_ERROR_LOG_MESSAGE, e);
        } catch (NoSuchProviderException e) {
            log.error(INIT_ERROR_LOG_MESSAGE, e);
        }
    }
    /**
     * 加密方法
     *
     * @param content
     *            要加密的字符串
     * @param keyBytes
     *            加密密钥
     * @return
     */
    public byte[] encrypt(byte[] content, byte[] keyBytes,byte[] iv) {
        byte[] encryptedText = null;
        init(keyBytes);
        try {
            cipher.init(Cipher.ENCRYPT_MODE, key, new IvParameterSpec(iv));
            encryptedText = cipher.doFinal(content);
        } catch (Exception e) {
            log.error("encrypt error, ", e);
        }
        return encryptedText;
    }
    /**
     * 解密方法
     *
     * @param encryptedData
     *            要解密的字符串
     * @param keyBytes
     *            解密密钥
     * @return
     */
    public byte[] decrypt(byte[] encryptedData, byte[] keyBytes,byte[] iv) {
        byte[] encryptedText = null;
        init(keyBytes);
        try {
            cipher.init(Cipher.DECRYPT_MODE, key, new IvParameterSpec(iv));
            encryptedText = cipher.doFinal(encryptedData);
        } catch (Exception e) {
            log.error("decrypt error,", e);
        }
        return encryptedText;
    }
}
