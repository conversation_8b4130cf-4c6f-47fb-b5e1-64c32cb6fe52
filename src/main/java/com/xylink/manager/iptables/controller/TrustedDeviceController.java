package com.xylink.manager.iptables.controller;

import com.xylink.config.exception.basic.ClientErrorException;
import com.xylink.config.exception.basic.ErrorStatus;
import com.xylink.manager.controller.BaseController;
import com.xylink.manager.controller.dto.activestandby.ActiveStandbyBaseInfoDto;
import com.xylink.manager.iptables.db.IptablesWhiteIp;
import com.xylink.manager.iptables.enums.NodeType;
import com.xylink.manager.iptables.service.IptablesService;
import com.xylink.manager.iptables.service.TrustedDeviceService;
import com.xylink.manager.model.common.Page;
import com.xylink.manager.service.activestandby.ICmsActiveStandbyConfigService;
import com.xylink.util.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/3/4
 */
@RestController
@RequestMapping("/trustedDevice")
@Slf4j
public class TrustedDeviceController extends BaseController {
    private static final String DEFAULT_TRUSTED_TYPE = "default";

    private final TrustedDeviceService trustedDeviceService;
    private final ICmsActiveStandbyConfigService cmsActiveStandbyConfigService;
    private final IptablesService iptablesService;

    public TrustedDeviceController(TrustedDeviceService trustedDeviceService, ICmsActiveStandbyConfigService cmsActiveStandbyConfigService, IptablesService iptablesService) {
        this.trustedDeviceService = trustedDeviceService;
        this.cmsActiveStandbyConfigService = cmsActiveStandbyConfigService;
        this.iptablesService = iptablesService;
    }

    @PostMapping("/page")
    public Page<IptablesWhiteIp> getTrustedDevicePage(@RequestBody(required = false) Map<String, String> body,
                                                      @RequestParam(value = "all", required = false,
                                                              defaultValue = "false") Boolean all) {
        String key = null;
        if (body != null) {
            key = body.get("key");
        }
        return trustedDeviceService.getTrustedDevicePage(key, pageable(), all);
    }

    @PostMapping("/add")
    public void addTrustedDevice(@RequestBody IptablesWhiteIp iptablesWhiteIp) {
        check(iptablesWhiteIp);
        List<IptablesWhiteIp> list = Collections.singletonList(iptablesWhiteIp);
        trustedDeviceService.addTrustedDevice(list);
        iptablesService.checkAndUpdateRules("addTrustedDevice", NodeType.ALL);
    }

    @PostMapping("/delete")
    public void deleteTrustedDevice(@RequestBody IptablesWhiteIp iptablesWhiteIp) {
        check(iptablesWhiteIp);
        Optional<ActiveStandbyBaseInfoDto> activeStandbyBaseInfoDto = cmsActiveStandbyConfigService.baseInfo();
        String trustedType = getTrustedType(iptablesWhiteIp);
        if (!trustedType.equals(DEFAULT_TRUSTED_TYPE) && activeStandbyBaseInfoDto.isPresent()) {
            log.info("the cms主备关系将解除，ip:{},peerIp:{},arbitrationIp:{}", iptablesWhiteIp.getIp(),
                    activeStandbyBaseInfoDto.get().getPeerIp(), activeStandbyBaseInfoDto.get().getArbitrationIp());
            cmsActiveStandbyConfigService.cancel();
        }
        trustedDeviceService.deleteTrustedDevice(Collections.singletonList(iptablesWhiteIp.getIp()));
        iptablesService.checkAndUpdateRules("deleteTrustedDevice", NodeType.ALL);
    }

    @PostMapping("/trustedType")
    public String getTrustedType(@RequestBody IptablesWhiteIp iptablesWhiteIp) {
        String ip = iptablesWhiteIp.getIp();
        //cms, ams, default
        Optional<ActiveStandbyBaseInfoDto> activeStandbyBaseInfoDto = cmsActiveStandbyConfigService.baseInfo();
        if (!activeStandbyBaseInfoDto.isPresent()) {
            return DEFAULT_TRUSTED_TYPE;
        }

        if (activeStandbyBaseInfoDto.get().getPeerIp().equals(ip)) {
            return "cms";
        }
        if (activeStandbyBaseInfoDto.get().getArbitrationIp().contains(ip)) {
            return "ams";
        }
        return DEFAULT_TRUSTED_TYPE;
    }

    private void check(IptablesWhiteIp iptablesWhiteIp) {
        String ip = iptablesWhiteIp.getIp();
        String mask = null;
        if (iptablesWhiteIp.getIp().contains("/")) {
            String[] ipAndMask = iptablesWhiteIp.getIp().split("/");
            ip = ipAndMask[0];
            mask = ipAndMask[1];
        }
        if (!IpUtils.isIpv4(ip) && !IpUtils.isIpv6(ip)) {
            log.warn("ip is not valid");
            throw new ClientErrorException(ErrorStatus.TRUSTED_IP_NOT_VALID);
        }
        if (StringUtils.isNotBlank(mask) && IpUtils.isIpv4(ip) && !IpUtils.isValidSubnetMaskIpv4(mask)) {
            log.warn("mask is not valid for ipv4");
            throw new ClientErrorException(ErrorStatus.TRUSTED_IP_NOT_VALID);
        }
        if (StringUtils.isNotBlank(mask) && IpUtils.isIpv6(ip) && !IpUtils.isValidSubnetMaskIpv6(mask)) {
            log.warn("mask is not valid for ipv6");
            throw new ClientErrorException(ErrorStatus.TRUSTED_IP_NOT_VALID);
        }
    }

}
