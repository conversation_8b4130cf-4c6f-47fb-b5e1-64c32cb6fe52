package com.xylink.manager.iptables.controller;

import com.xylink.manager.inspection.utils.JsonUtils;
import com.xylink.manager.iptables.dto.RuleDTO;
import com.xylink.manager.iptables.dto.TotalRuleDTO;
import com.xylink.manager.iptables.enums.NodeType;
import com.xylink.manager.iptables.service.DefaultRuleService;
import com.xylink.manager.iptables.service.IptablesService;
import com.xylink.manager.iptables.util.IptablesRuleParser;
import com.xylink.manager.iptables.util.RuleUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

import static com.xylink.config.Constants.NODE_TYPE_COMMON_MAIN;

/**
 * <AUTHOR>
 * @date 2025/6/4
 */
@RestController
@RequestMapping("/iptablesDebug/")
@Slf4j
public class IpTablesDebugController {

    @Autowired
    private DefaultRuleService defaultRuleService;
    @Autowired
    private IptablesService iptablesService;

    @RequestMapping("/print")
    public void getIpTablesDebug() {
        TotalRuleDTO totalRuleDTO = defaultRuleService.getRules(NodeType.COMMON_MAIN);
        List<RuleDTO> iptables = totalRuleDTO.getIptables().stream()
                .filter(rule -> org.apache.commons.lang3.StringUtils.equals(rule.getNodeType(), NODE_TYPE_COMMON_MAIN)).collect(Collectors.toList());
        List<RuleDTO> ip6tables = totalRuleDTO.getIp6tables().stream()
                .filter(rule -> org.apache.commons.lang3.StringUtils.equals(rule.getNodeType(), NODE_TYPE_COMMON_MAIN)).collect(Collectors.toList());
        iptablesService.addSpecialRules("private-docker-main", iptables, true);
        iptablesService.addSpecialRules("private-docker-main", ip6tables, false);
        RuleUtil.sortRules(totalRuleDTO.getIptables());
        RuleUtil.sortRules(totalRuleDTO.getIp6tables());
        log.debug(JsonUtils.objectToPrettyJsonString(totalRuleDTO));

        for(RuleDTO ruleDTO: totalRuleDTO.getIptables()) {
            log.debug(IptablesRuleParser.generateRuleCommand(ruleDTO, false));
        }

        for(RuleDTO ruleDTO: totalRuleDTO.getIp6tables()) {
            log.debug(IptablesRuleParser.generateRuleCommand(ruleDTO, true));
        }
    }
}
