package com.xylink.manager.iptables.db;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xylink.manager.controller.dto.ISearchDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/2/24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IptablesWhiteIp implements ISearchDto {
    @JsonIgnore
    private String id;
    private String ip;

    @Override
    public boolean containSearchContent(String key) {
        return this.ip.contains(key);
    }
}
