package com.xylink.manager.iptables.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xylink.manager.iptables.db.IptablesRule;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.util.List;

// 表示单个规则的类
@Data
@NoArgsConstructor
public class RuleDTO {
    private String table;
    private String chain;
    private String inInterface;
    private String outInterface;
    private String srcIp;
    private String destIp;
    private String protocol;
    private List<MatchDTO> matches;
    private TargetDTO target;
    @JsonIgnore
    private String nodeType;
    @JsonIgnore
    private String whiteMode;
    @JsonIgnore
    private Integer ruleOrder;
    @JsonIgnore
    private String ruleType;
    @JsonIgnore
    private String id;
    @JsonIgnore
    private String relationServiceLabel;
    public RuleDTO(IptablesRule iptablesRule) {
        this.table = iptablesRule.getRuleTable();
        this.chain = iptablesRule.getRuleChain();
        this.inInterface = iptablesRule.getInInterface();
        this.outInterface = iptablesRule.getOutInterface();
        this.srcIp = iptablesRule.getSrcIp();
        this.destIp = iptablesRule.getDestIp();
        this.protocol = iptablesRule.getProtocol();
        this.whiteMode = iptablesRule.getWhiteMode();
        this.ruleOrder = iptablesRule.getRuleOrder();
        this.ruleType = iptablesRule.getRuleType();
        this.id = iptablesRule.getId();
        this.nodeType = iptablesRule.getNodeType();
        this.relationServiceLabel = iptablesRule.getRelationServiceLabel();
    }

    public RuleDTO(RuleDTO ruleDto) {
        BeanUtils.copyProperties(ruleDto, this);
    }
}
