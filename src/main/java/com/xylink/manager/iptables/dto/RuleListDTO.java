package com.xylink.manager.iptables.dto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/24
 */

import com.xylink.manager.iptables.db.IptablesModule;
import com.xylink.manager.iptables.db.IptablesModuleOptions;
import com.xylink.manager.iptables.db.IptablesRule;
import lombok.AllArgsConstructor;
import lombok.Data;

// 表示整个规则列表的类
@Data
@AllArgsConstructor
public class RuleListDTO {
    private List<IptablesRule> ruleList;
    private List<IptablesModule> moduleList;
    private List<IptablesModuleOptions> optionsList;
}

