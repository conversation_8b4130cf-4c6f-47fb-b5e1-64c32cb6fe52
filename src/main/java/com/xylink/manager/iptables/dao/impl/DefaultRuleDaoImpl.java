package com.xylink.manager.iptables.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xylink.manager.inspection.config.DataSourceManager;
import com.xylink.manager.iptables.dao.DefaultRuleDao;
import com.xylink.manager.iptables.db.IptablesModule;
import com.xylink.manager.iptables.db.IptablesModuleOptions;
import com.xylink.manager.iptables.db.IptablesRule;
import com.xylink.manager.iptables.dto.RuleListDTO;
import com.xylink.manager.iptables.enums.NodeType;
import com.xylink.manager.iptables.mapper.IptablesModuleMapper;
import com.xylink.manager.iptables.mapper.IptablesModuleOptionsMapper;
import com.xylink.manager.iptables.mapper.IptablesRuleMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/20
 */
@Service
@Slf4j
public class DefaultRuleDaoImpl implements DefaultRuleDao {

    private final DataSourceManager dataSourceManager;

    public DefaultRuleDaoImpl(DataSourceManager dataSourceManager) {
        this.dataSourceManager = dataSourceManager;
    }

    @Override
    public RuleListDTO getAllRuleDTO(NodeType nodeType) {
        List<IptablesRule> ruleList;
        List<IptablesModule> moduleList;
        List<IptablesModuleOptions> optionsList;

        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            IptablesRuleMapper ruleMapper = session.getMapper(IptablesRuleMapper.class);
            QueryWrapper<IptablesRule> queryWrapper = new QueryWrapper<>();
            if (nodeType != null && nodeType != NodeType.ALL) {
                queryWrapper.eq("node_type", nodeType.getType());
            }
            ruleList = ruleMapper.selectList(queryWrapper);

            IptablesModuleMapper moduleMapper = session.getMapper(IptablesModuleMapper.class);
            moduleList = moduleMapper.selectList(new QueryWrapper<>());

            IptablesModuleOptionsMapper moduleOptionsMapper = session.getMapper(IptablesModuleOptionsMapper.class);
            optionsList = moduleOptionsMapper.selectList(new QueryWrapper<>());
        }
        return new RuleListDTO(ruleList, moduleList, optionsList);
    }

    @Override
    public void saveRules(List<IptablesRule> rulesList, List<IptablesModule> moduleList,
                          List<IptablesModuleOptions> moduleOptionsList) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            IptablesRuleMapper ruleMapper = session.getMapper(IptablesRuleMapper.class);
            ruleMapper.insert(rulesList);

            IptablesModuleMapper moduleMapper = session.getMapper(IptablesModuleMapper.class);
            moduleMapper.insert(moduleList);

            IptablesModuleOptionsMapper moduleOptionsMapper = session.getMapper(IptablesModuleOptionsMapper.class);
            moduleOptionsMapper.insert(moduleOptionsList);

            session.commit();
        } catch (Exception e) {
            log.error("Failed to save rules", e);
            throw new RuntimeException("Failed to save rules", e);
        }
    }

    @Override
    public int getMaxOrder() {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            IptablesRuleMapper ruleMapper = session.getMapper(IptablesRuleMapper.class);
            QueryWrapper<IptablesRule> wrapper = new QueryWrapper<>();
            wrapper.orderByDesc("rule_order");
            wrapper.last("limit 1");
            IptablesRule iptablesRule = ruleMapper.selectOne(wrapper);
            return iptablesRule == null ? 1 : iptablesRule.getRuleOrder() + 1;
        }
    }

    @Override
    public List<IptablesModuleOptions> getOptionsWithCm(String key) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            IptablesModuleOptionsMapper optionsMapper = session.getMapper(IptablesModuleOptionsMapper.class);
            QueryWrapper<IptablesModuleOptions> queryWrapper = new QueryWrapper<>();
            queryWrapper.like("relation_cm_key", key);
            return optionsMapper.selectList(queryWrapper);
        }
    }

    @Override
    public void updateOptions(List<IptablesModuleOptions> optionsList) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            IptablesModuleOptionsMapper optionsMapper = session.getMapper(IptablesModuleOptionsMapper.class);
            optionsMapper.updateById(optionsList);
        }
    }

    @Override
    public void deleteRulesByIds(List<String> ids) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            IptablesRuleMapper ruleMapper = session.getMapper(IptablesRuleMapper.class);
            QueryWrapper<IptablesModule> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("rule_id", ids);
            ruleMapper.deleteByIds(ids);

            IptablesModuleMapper moduleMapper = session.getMapper(IptablesModuleMapper.class);
            List<IptablesModule> modules = moduleMapper.selectList(queryWrapper);
            moduleMapper.deleteByIds(modules.stream().map(IptablesModule::getId).collect(Collectors.toList()));

            IptablesModuleOptionsMapper optionsMapper = session.getMapper(IptablesModuleOptionsMapper.class);
            QueryWrapper<IptablesModuleOptions> optionsQueryWrapper = new QueryWrapper<>();
            queryWrapper.in("module_id", ids);
            List<IptablesModuleOptions> options = optionsMapper.selectList(optionsQueryWrapper);
            optionsMapper.deleteByIds(options.stream().map(IptablesModuleOptions::getId).collect(Collectors.toList()));
            session.commit();
        }
    }
}
