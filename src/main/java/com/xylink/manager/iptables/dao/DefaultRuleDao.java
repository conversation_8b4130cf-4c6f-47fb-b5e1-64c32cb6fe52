package com.xylink.manager.iptables.dao;

import com.xylink.manager.iptables.db.IptablesModule;
import com.xylink.manager.iptables.db.IptablesModuleOptions;
import com.xylink.manager.iptables.db.IptablesRule;
import com.xylink.manager.iptables.dto.RuleListDTO;
import com.xylink.manager.iptables.enums.NodeType;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/20
 */
public interface DefaultRuleDao {
    RuleListDTO getAllRuleDTO(NodeType nodeType);

    void saveRules(List<IptablesRule> rulesList, List<IptablesModule> moduleList,
                   List<IptablesModuleOptions> moduleOptionsList);

    int getMaxOrder();

    List<IptablesModuleOptions> getOptionsWithCm(String key);

    void updateOptions(List<IptablesModuleOptions> optionsList);

    void deleteRulesByIds(List<String> ids);
}
