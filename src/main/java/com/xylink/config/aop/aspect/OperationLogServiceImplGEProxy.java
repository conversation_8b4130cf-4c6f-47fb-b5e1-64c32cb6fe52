package com.xylink.config.aop.aspect;

import com.xylink.config.Constants;
import com.xylink.manager.model.common.Page;
import com.xylink.manager.model.common.Pageable;
import com.xylink.manager.service.base.K8sService;
import com.xylink.manager.service.remote.applicationsecurity.ge.GEApplicationSecurityClient;
import com.xylink.manager.service.remote.applicationsecurity.ge.dto.GEApplicationSecuritySignReq;
import com.xylink.manager.service.remote.applicationsecurity.ge.dto.GEApplicationSecuritySignRes;
import com.xylink.manager.service.remote.applicationsecurity.ge.dto.GEApplicationSecurityVerifySignReq;
import com.xylink.manager.service.remote.applicationsecurity.ge.dto.GEApplicationSecurityVerifySignRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * 代理类
 *
 * <AUTHOR>
 * @since 2024/1/27 9:42 PM
 */
@Slf4j
public class OperationLogServiceImplGEProxy implements IOperationLogService {
    private IOperationLogService target;
    private GEApplicationSecurityClient geApplicationSecurityClient;
    private K8sService k8sService;


    public OperationLogServiceImplGEProxy(IOperationLogService target, GEApplicationSecurityClient geApplicationSecurityClient, K8sService k8sService) {
        this.target = target;
        this.geApplicationSecurityClient = geApplicationSecurityClient;
        this.k8sService = k8sService;
    }

    @Override
    @Async
    public void save(OperationLogVO operationLogVO) {
        addSignInfo(operationLogVO);
        target.save(operationLogVO);
    }

    @Override
    public void save(List<OperationLogVO> list) {
        list.forEach(this::addSignInfo);
        target.save(list);
    }

    @Override
    public Page<OperationLogVO> searchAndPageList(Pageable pageable, OperationLogSearchVO searchVO) {
        Page<OperationLogVO> page = target.searchAndPageList(pageable, searchVO);
        if (page != null && !CollectionUtils.isEmpty(page.getRecords())) {
            page.getRecords().forEach(this::addVerifySignDataInfo);
        }
        return page;
    }

    private void addSignInfo(OperationLogVO operationLogVO) {
        Map<String, String> allIp = k8sService.getConfigmap(Constants.CONFIGMAP_ALLIP);
        String certAlias = allIp.get("GE_CERT_ALIAS");
        GEApplicationSecuritySignReq req = new GEApplicationSecuritySignReq();
        req.setCertAlias(certAlias);
        req.setB64OriginData(operationLogVO.b64OriginData());

        GEApplicationSecuritySignRes response;
        try {
            response = geApplicationSecurityClient.sign(req);
            if (response.isSuccess()) {
                operationLogVO.setSign(response.getB64Cert());
                operationLogVO.setB64SignedData(response.getB64SignedData());
            }
        } catch (Exception e) {
            log.error("Digital signatures request error.", e);
        }
    }

    private void addVerifySignDataInfo(OperationLogVO operationLogVO) {
        String cert = operationLogVO.getSign();
        String b64SignedData = operationLogVO.getB64SignedData();
        if (StringUtils.isBlank(cert) || StringUtils.isBlank(b64SignedData)) {
            log.warn("Data sign or signedData is null.");
            return;
        }
        GEApplicationSecurityVerifySignReq req = new GEApplicationSecurityVerifySignReq();
        req.setB64OriginData(operationLogVO.b64OriginData());
        req.setB64Cert(cert);
        req.setB64SignedData(b64SignedData);
        try {
            GEApplicationSecurityVerifySignRes response = geApplicationSecurityClient.verifySignedData(req);
            operationLogVO.setValid(response.isSuccess());
        } catch (Exception e) {
            log.error("Verify the signature request error.", e);
            operationLogVO.setValid(true);
        }
    }
}
