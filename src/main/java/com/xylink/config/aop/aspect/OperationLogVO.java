package com.xylink.config.aop.aspect;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * <AUTHOR>
 * @create 2024/1/25 14:09
 */
@Data
public class OperationLogVO {
    private String id;
    /**
     * 操作时间
     */
    private Long operateTime;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 操作人ip地址
     */
    private String ipAddress;
    /**
     * 操作类型
     */
    private String operationType;
    /**
     * 描述
     */
    private String description;
    /**
     * 数据是否有效
     */
    private Boolean valid;

    @JsonIgnore
    private String sign;

    @JsonIgnore
    private String b64SignedData;

    public String b64OriginData() {
        String source = this.operateTime + this.operator + this.ipAddress + this.operationType + this.description;
        return Base64.getEncoder().encodeToString(source.getBytes(StandardCharsets.UTF_8));
    }
}
