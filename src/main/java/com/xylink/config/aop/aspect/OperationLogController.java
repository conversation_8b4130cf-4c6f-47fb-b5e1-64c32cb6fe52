package com.xylink.config.aop.aspect;

import com.xylink.manager.controller.BaseController;
import com.xylink.manager.model.common.Page;
import com.xylink.manager.model.common.Pageable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @create 2024/1/26 10:08
 */
@RestController
@RequestMapping("/operationLog")
@Slf4j
public class OperationLogController extends BaseController {
    @Autowired
    private IOperationLogService operationLogService;

    @GetMapping("/pageList")
    Page<OperationLogVO> searchAndPageList(OperationLogSearchVO searchVO){
        Pageable pageable = pageable();
        log.info("operationLog pageList,pageAble:{},SearchKey:{}", pageable, searchVO);
        return operationLogService.searchAndPageList(pageable, searchVO);
    }
}
