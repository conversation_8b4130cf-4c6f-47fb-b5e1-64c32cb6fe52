package com.xylink.config.aop.aspect;

import com.xylink.manager.model.common.Page;
import com.xylink.manager.model.common.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/1/26 09:53
 */
public interface IOperationLogService {

    /**
     * 单个保存
     * @param operationLogVO
     */
    void save(OperationLogVO operationLogVO);

    /**
     * 批量保存
     * @param list
     */
    void save(List<OperationLogVO> list);

    /**
     * 分页搜索查询
     * @param pageable
     * @param searchVO 搜索条件
     * @return
     */
    Page<OperationLogVO> searchAndPageList(Pageable pageable, OperationLogSearchVO searchVO);
}
