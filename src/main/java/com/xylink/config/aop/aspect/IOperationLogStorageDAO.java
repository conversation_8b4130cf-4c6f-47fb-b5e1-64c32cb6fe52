package com.xylink.config.aop.aspect;

import com.xylink.manager.model.common.Page;
import com.xylink.manager.model.common.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/1/25 18:47
 */
public interface IOperationLogStorageDAO {

    /**
     * 保存持久化操作日志
     * @param operationLogVO
     */
    void save(OperationLogVO operationLogVO);

    /**
     * 批量持久化操作日志
     * @param list
     */
    void save(List<OperationLogVO> list);

    /**
     * 查询并分页
     * @param pageable 分页条件
     * @param searchVO 查询条件
     * @return
     */
    Page<OperationLogVO> searchAndPageList(Pageable pageable, OperationLogSearchVO searchVO);
}
