package com.xylink.config.aop.aspect;

import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xylink.config.aop.OperationLog;
import com.xylink.util.ThreadLocalUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2024/1/25 10:23
 */
@Aspect
@Component
@Slf4j
public class OperationLogAspect {

    @Value("${user.operationLog.switch}")
    private boolean userOperationLogSwitch;

    @Autowired
    private HttpServletRequest request;

    private final ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(1, 2, 1, TimeUnit.SECONDS, new LinkedBlockingQueue<>(100), new ThreadFactoryBuilder().setNameFormat("operationLog-pool-%d").build());


    @Around("@annotation(operationLog)")
    public Object logOperation(ProceedingJoinPoint joinPoint, OperationLog operationLog) throws Throwable {
        //抛异常就不计入操作日志
        Object result = joinPoint.proceed();

        if (userOperationLogSwitch) {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            poolExecutor.execute(() -> {
                ThreadLocalUtil.setAuthentication(authentication);
                ThreadLocalUtil.setRequest(request);
                saveOperationLog(LocalDateTime.now(), joinPoint, operationLog);
                ThreadLocalUtil.clearAuthentication();
                ThreadLocalUtil.clearRequest();
            });
        }

        return result;
    }

    private void saveOperationLog(LocalDateTime now,ProceedingJoinPoint joinPoint, OperationLog operationLog) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (ObjectUtils.isEmpty(authentication)) {
            log.info("current request no authentication required!");
            return;
        }

        if (StringUtils.isBlank(operationLog.description()) || ObjectUtils.isEmpty(operationLog.operationType())) {
            log.info("description or operationType is null");
            return;
        }

        OperationLogVO operationLogVO = new OperationLogVO();
        operationLogVO.setOperateTime(now.atZone(ZoneId.systemDefault()).toInstant().getEpochSecond());
        operationLogVO.setOperator(authentication.getName());
        operationLogVO.setDescription(getDescription(joinPoint, operationLog));
        operationLogVO.setOperationType(operationLog.operationType().getDescription());
        operationLogVO.setIpAddress(getRequestIpRoute());

        //todo 设置sign签名
        System.out.println(operationLogVO);
    }

    private String getRequestIpRoute() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        String remoteAddr = request.getRemoteAddr();
        String xFFIp = request.getHeader("X-Forwarded-For");
        return remoteAddr + "," + xFFIp;
    }

    private String getDescription(ProceedingJoinPoint joinPoint, OperationLog operationLog) {
        Class<?> requestClass = operationLog.requestClass();
        String[] operateParam = operationLog.paramNameArr();
        String description = operationLog.description();
        if (ObjectUtils.isEmpty(requestClass) || requestClass == Void.class) {
            return description;
        }

        // 获取请求参数
        Object[] args = joinPoint.getArgs();
        if (args.length > 0) {
            for (Object arg : args) {
                if (isPrimitiveType(requestClass) && ObjectUtils.isEmpty(operateParam)) {
                    description = getReplacedDescription(description, Lists.newArrayList("" + arg));
                }else if (requestClass == MultipartFile.class && ObjectUtils.isEmpty(operateParam)) {
                    MultipartFile file = (MultipartFile) arg;
                    description = getReplacedDescription(description, Lists.newArrayList(file.getOriginalFilename()));
                }else if (requestClass.isInstance(arg) && ObjectUtils.isNotEmpty(operateParam)) {
                    // 处理其他类型的请求参数实体类
                    List<String> fieldValueList = getFieldValue(arg, operateParam);
                    description = getReplacedDescription(description, fieldValueList);
                }
            }
        }
        return description;
    }


    private boolean isPrimitiveType(Class<?> clazz) {
        return clazz.isPrimitive() || clazz == String.class;
    }

    /**
     * 通过反射拿到实体类 各参数对应值
     * @param object
     * @param fieldNameArr
     * @return
     */
    private List<String> getFieldValue(Object object, String[] fieldNameArr){
        if (ObjectUtils.anyNull(object, fieldNameArr)) {
            return Collections.emptyList();
        }
        List<String> res = new ArrayList<>();
        for (String fieldName : fieldNameArr) {
            try {
                Field field = object.getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                res.add(String.valueOf(field.get(object)));
            }catch (Exception e){
                log.error("Class:{} fieldName:{} get param value failed,", object.getClass(), fieldName, e);
                res.add("null");
            }

        }
        return res;
    }

    private static String getReplacedDescription(String description, List<String> placeholderValues) {
        for (int i = 0; i < placeholderValues.size(); i++) {
            String placeholder = "$" + (i + 1);
            String replacement = placeholderValues.get(i);
            description = description.replace(placeholder, replacement);
        }
        return description;
    }

}
