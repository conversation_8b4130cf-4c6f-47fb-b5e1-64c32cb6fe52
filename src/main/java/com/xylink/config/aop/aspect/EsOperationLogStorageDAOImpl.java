package com.xylink.config.aop.aspect;

import com.xylink.manager.model.common.Page;
import com.xylink.manager.model.common.Pageable;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/1/25 19:16
 */
@Slf4j
public class EsOperationLogStorageDAOImpl implements IOperationLogStorageDAO {

    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    /**
     * 检查es索引，没有就创建
     */
    @PostConstruct
    public void check() {
        log.info("check elasticsearch index");
        try {
            if (!elasticsearchRestTemplate.indexOps(ESOperationLogDTO.class).exists()) {
                elasticsearchRestTemplate.indexOps(ESOperationLogDTO.class).createWithMapping();
            }
        } catch (Exception e) {
            log.error("check elasticsearch index failed,", e);
        }
    }

    @Override
    public void save(OperationLogVO operationLogVO) {
        ESOperationLogDTO transform = ESOperationLogDTO.transform(operationLogVO);
        elasticsearchRestTemplate.save(transform);
    }

    @Override
    public void save(List<OperationLogVO> list) {
        List<ESOperationLogDTO> data = list.stream().map(ESOperationLogDTO::transform).collect(Collectors.toList());
        elasticsearchRestTemplate.save(data);
    }

    @Override
    public Page<OperationLogVO> searchAndPageList(Pageable pageable, OperationLogSearchVO searchVO) {

        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        if (ObjectUtils.isNotEmpty(searchVO.getStartTime())) {
            queryBuilder.must(QueryBuilders.rangeQuery("operateTime").gte(searchVO.getStartTime()));
        }

        if (ObjectUtils.isNotEmpty(searchVO.getEndTime())) {
            queryBuilder.must(QueryBuilders.rangeQuery("operateTime").lte(searchVO.getEndTime()));
        }

        if (StringUtils.isNotBlank(searchVO.getOperator())) {
            queryBuilder.filter(QueryBuilders.termQuery("operator", searchVO.getOperator()));
        }
        if (StringUtils.isNotBlank(searchVO.getOperationType())) {
            queryBuilder.filter(QueryBuilders.termQuery("operationType", searchVO.getOperationType()));
        }

        Sort sort = Sort.by(Sort.Direction.DESC, "operateTime");
        PageRequest pageRequest = PageRequest.of((int) pageable.getPageNumber()-1, (int) pageable.getPageSize(), sort);
        NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withQuery(queryBuilder)
                .withPageable(pageRequest)
                .build();

        long count = elasticsearchRestTemplate.count(searchQuery, ESOperationLogDTO.class);
        SearchHits<ESOperationLogDTO> searchHits = elasticsearchRestTemplate.search(searchQuery, ESOperationLogDTO.class);
        List<ESOperationLogDTO> dtoList = searchHits.stream()
                .map(SearchHit::getContent)
                .collect(Collectors.toList());
        List<OperationLogVO> record = dtoList.stream().map(ESOperationLogDTO::to).collect(Collectors.toList());
        Page<OperationLogVO> res = new Page<>();
        res.setPageSize(pageable.getPageSize());
        res.setCurrent(pageable.getPageNumber());
        res.setTotal(count);
        res.setRecords(record);
        return res;
    }
}
