package com.xylink.config.aop.aspect;

import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/1/25 15:11
 */
@Getter
public enum OperationTypeEnum {
    LOGIN(1,"登录"),
    FILE_UPLOAD(2,"上传文件"),
    FILE_DOWNLOAD(3,"下载文件"),
    CLIENT_MANAGE(4,"客户端管理"),
    SERVER_MANAGE(5,"服务管理"),
    SERVER_MONITOR(6,"服务监控"),
    CONFIG_FILE(7,"配置文件"),
    ;

    OperationTypeEnum(Integer typeId, String description) {
        this.typeId = typeId;
        this.description = description;
    }

    private Integer typeId;


    private String description;


    private static Map<Integer, String> typeIdToDescription = new HashMap<>();

    static {
        for (OperationTypeEnum value : values()) {
            typeIdToDescription.put(value.getTypeId(), value.getDescription());
        }
    }

    public static String getTypeDescription(Integer typeId) {
        if (ObjectUtils.isEmpty(typeId) ) {
            return null;
        }

        return typeIdToDescription.get(typeId);
    }
}
