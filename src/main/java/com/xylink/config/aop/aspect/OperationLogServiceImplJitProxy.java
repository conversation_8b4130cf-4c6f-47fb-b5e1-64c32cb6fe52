package com.xylink.config.aop.aspect;

import com.xylink.manager.model.common.Page;
import com.xylink.manager.model.common.Pageable;
import com.xylink.manager.model.miping.MmAppInfo;
import com.xylink.manager.service.remote.applicationsecurity.jit.MmApplicationSecurityClient;
import com.xylink.manager.service.remote.applicationsecurity.jit.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 代理类
 *
 * <AUTHOR>
 * @since 2024/1/27 9:42 PM
 */
@Slf4j
public class OperationLogServiceImplJitProxy implements IOperationLogService {
    private final IOperationLogService target;
    private final MmApplicationSecurityClient mmApplicationSecurityClient;
    private final MmAppInfo mmAppInfo;


    public OperationLogServiceImplJitProxy(IOperationLogService target, MmApplicationSecurityClient mmApplicationSecurityClient, MmAppInfo mmAppInfo) {
        this.target = target;
        this.mmApplicationSecurityClient = mmApplicationSecurityClient;
        this.mmAppInfo = mmAppInfo;
    }

    @Override
    @Async
    public void save(OperationLogVO operationLogVO) {
        addSignInfo(operationLogVO);
        target.save(operationLogVO);
    }

    @Override
    public void save(List<OperationLogVO> list) {
        list.forEach(this::addSignInfo);
        target.save(list);
    }

    @Override
    public Page<OperationLogVO> searchAndPageList(Pageable pageable, OperationLogSearchVO searchVO) {
        Page<OperationLogVO> page = target.searchAndPageList(pageable, searchVO);
        if (page != null && !CollectionUtils.isEmpty(page.getRecords())) {
            page.getRecords().forEach(this::addVerifySignDataInfo);
        }
        return page;
    }

    private void addSignInfo(OperationLogVO operationLogVO) {
        MmApplicationSecuritySignReq req = new MmApplicationSecuritySignReq();
        BeanUtils.copyProperties(mmAppInfo, req);
        req.setOriData(operationLogVO.b64OriginData());
        MmResponse<MmApplicationSecuritySignRes> response;
        try {
            response = mmApplicationSecurityClient.sign(req);
            if (response.isSuccess()) {
                MmApplicationSecuritySignRes data = response.getData();
                operationLogVO.setB64SignedData(data.getSignValue());
            }
        } catch (Exception e) {
            log.error("Digital signatures request error.", e);
        }
    }

    private void addVerifySignDataInfo(OperationLogVO operationLogVO) {
        String b64SignedData = operationLogVO.getB64SignedData();
        if (StringUtils.isBlank(b64SignedData)) {
            log.warn("Data signedData is null.");
            return;
        }
        MmApplicationSecurityVerifySignReq req = new MmApplicationSecurityVerifySignReq();
        BeanUtils.copyProperties(mmAppInfo, req);
        req.setOriData(operationLogVO.b64OriginData());
        req.setSignValue(operationLogVO.getB64SignedData());
        try {
            MmResponse<MmApplicationSecurityVerifySignRes> response = mmApplicationSecurityClient.verifySignedData(req);
            operationLogVO.setValid(response.isSuccess() && response.getData() != null && response.getData().isVerified());
        } catch (Exception e) {
            log.error("Verify the signature request error.", e);
            operationLogVO.setValid(true);
        }
    }
}
