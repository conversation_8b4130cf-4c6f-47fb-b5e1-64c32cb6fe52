package com.xylink.config.aop.aspect;

import com.xylink.manager.inspection.config.DataSourceManager;
import com.xylink.manager.inspection.utils.PageUtil;
import com.xylink.manager.mapper.operationlog.OperationLogMapper;
import com.xylink.manager.model.common.Page;
import com.xylink.manager.model.common.Pageable;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/1/25 19:16
 */
@Primary
@Repository
@Slf4j
public class DbOperationLogStorageDAOImpl implements IOperationLogStorageDAO {

    private DataSourceManager dataSourceManager;

    public DbOperationLogStorageDAOImpl(DataSourceManager dataSourceManager) {
        this.dataSourceManager = dataSourceManager;
    }

    @Override
    public void save(OperationLogVO operationLogVO) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            OperationLogMapper operationLogMapper = session.getMapper(OperationLogMapper.class);
            operationLogMapper.save(operationLogVO);
        }
    }

    @Override
    public void save(List<OperationLogVO> list) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            OperationLogMapper operationLogMapper = session.getMapper(OperationLogMapper.class);
            operationLogMapper.batchSave(list);
        }
    }

    @Override
    public Page<OperationLogVO> searchAndPageList(Pageable pageable, OperationLogSearchVO searchVO) {
        try (SqlSession session = dataSourceManager.getSqlSessionFactory().openSession()) {
            OperationLogMapper operationLogMapper = session.getMapper(OperationLogMapper.class);
            long count = operationLogMapper.count(searchVO);
            List<OperationLogVO> record = Collections.emptyList();
            if (count > 0) {
                int startRow = PageUtil.getStart((int) pageable.getPageNumber()-1, (int) pageable.getPageSize());
                record = operationLogMapper.searchAndPageList((int) pageable.getPageSize(), startRow, searchVO);
            }
            Page<OperationLogVO> res = new Page<>();
            res.setPageSize(pageable.getPageSize());
            res.setCurrent(pageable.getPageNumber());
            res.setTotal(count);
            res.setRecords(record);
            return res;
        }
    }
}
