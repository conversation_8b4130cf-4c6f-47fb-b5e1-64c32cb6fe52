package com.xylink.config.aop.aspect;

import com.xylink.manager.model.common.Page;
import com.xylink.manager.model.common.Pageable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/1/26 09:36
 */
@Service
public class OperationLogServiceImpl implements IOperationLogService{

    @Autowired
    private IOperationLogStorageDAO operationLogStrategy;


    @Override
    @Async
    public void save(OperationLogVO operationLogVO) {
        operationLogStrategy.save(operationLogVO);
    }

    @Override
    public void save(List<OperationLogVO> list) {
        operationLogStrategy.save(list);
    }

    @Override
    public Page<OperationLogVO> searchAndPageList(Pageable pageable, OperationLogSearchVO searchVO) {
        return operationLogStrategy.searchAndPageList(pageable, searchVO);
    }
}
