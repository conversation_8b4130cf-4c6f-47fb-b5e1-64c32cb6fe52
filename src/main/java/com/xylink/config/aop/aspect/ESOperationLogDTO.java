package com.xylink.config.aop.aspect;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * <AUTHOR>
 * @create 2024/1/25 14:09
 */
@Data
@Document(indexName = "private_manager_operation_log")
public class ESOperationLogDTO {

    /**
     * 不用处理，Elasticsearch会自动生成一个唯一的字符串作为文档的ID
     */
    @Id
    private String id;
    @Field(type = FieldType.Long)
    private Long operateTime;
    @Field(type = FieldType.Keyword)
    private String operator;
    @Field(type = FieldType.Keyword)
    private String ipAddress;
    @Field(type = FieldType.Keyword)
    private String operationType;
    @Field(type = FieldType.Keyword)
    private String description;
    @Field(type = FieldType.Keyword)
    private String sign;
    @Field(type = FieldType.Keyword)
    private String b64SignedData;

    public static ESOperationLogDTO transform(OperationLogVO operationLogVO) {
        ESOperationLogDTO res = new ESOperationLogDTO();
        res.setOperateTime(operationLogVO.getOperateTime());
        res.setOperator(operationLogVO.getOperator());
        res.setIpAddress(operationLogVO.getIpAddress());
        res.setOperationType(operationLogVO.getOperationType());
        res.setDescription(operationLogVO.getDescription());
        res.setSign(operationLogVO.getSign());
        res.setB64SignedData(operationLogVO.getB64SignedData());
        return res;
    }

    public OperationLogVO to() {
        OperationLogVO res = new OperationLogVO();
        res.setId(getId());
        res.setOperateTime(getOperateTime());
        res.setOperator(getOperator());
        res.setOperationType(getOperationType());
        res.setIpAddress(getIpAddress());
        res.setDescription(getDescription());
        res.setSign(getSign());
        res.setB64SignedData(getB64SignedData());
        return res;
    }
}
