#!/bin/bash

# 容器启动脚本
set -e

# 打印启动信息
echo "=========================================="
echo "启动 Manager 应用"
echo "时间: $(date)"
echo "Java版本: $(java -version 2>&1 | head -n 1)"
echo "内存信息: $(free -h | grep Mem)"
echo "=========================================="

# 设置默认的JVM参数
DEFAULT_JAVA_OPTS="-Xms512m -Xmx2g -XX:+UseG1GC -XX:G1HeapRegionSize=16m -XX:+UseStringDeduplication -XX:+OptimizeStringConcat -Djava.security.egd=file:/dev/./urandom -Dfile.encoding=UTF-8 -Duser.timezone=Asia/Shanghai"

# 合并环境变量中的JAVA_OPTS
FINAL_JAVA_OPTS="${DEFAULT_JAVA_OPTS} ${JAVA_OPTS:-}"

# 设置Spring配置文件路径
SPRING_CONFIG_LOCATION="${SPRING_CONFIG_LOCATION:-classpath:/application.properties}"

# 设置应用配置
SPRING_OPTS="--spring.config.location=${SPRING_CONFIG_LOCATION}"

# 如果有额外的Spring参数，添加它们
if [ -n "${SPRING_ARGS}" ]; then
    SPRING_OPTS="${SPRING_OPTS} ${SPRING_ARGS}"
fi

# 打印最终的启动参数
echo "JAVA_OPTS: ${FINAL_JAVA_OPTS}"
echo "SPRING_OPTS: ${SPRING_OPTS}"
echo "=========================================="

# 启动应用
exec java ${FINAL_JAVA_OPTS} -jar app.war ${SPRING_OPTS} "$@"
