apiVersion: v1
kind: ConfigMap
metadata:
  name: manager-config
  namespace: default
  labels:
    app: manager
data:
  # 应用配置文件
  application.properties: |
    # 服务器配置
    server.servlet.context-path=/manager
    server.port=18028
    base.dir=${MANAGER_BASE_DIR:/mnt/xylink}
    
    # Spring配置
    spring.banner.location=banner.txt
    spring.main.banner-mode=LOG
    spring.main.allow-circular-references=true
    spring.application.name=manager
    server.error.whitelabel.enabled=false
    server.tomcat.connection-timeout=5000ms
    
    # 日志配置
    logging.level.com.xylink=info
    logging.level.root=${LOG_LEVEL:info}
    
    # 安全配置
    spring.security.ignoringUrlList[0]=/error/**
    spring.security.ignoringUrlList[1]=/static/**
    spring.security.ignoringUrlList[2]=/api/rest/**
    spring.security.ignoringUrlList[3]=/asset-manifest.json
    spring.security.ignoringUrlList[4]=/favicon.ico
    spring.security.ignoringUrlList[5]=/manifest.json
    spring.security.ignoringUrlList[6]=/precache-manifest**
    spring.security.ignoringUrlList[7]=/service-worker.js
    spring.security.ignoringUrlList[8]=/server.svg
    spring.security.ignoringUrlList[9]=/index.html
    spring.security.ignoringUrlList[10]=/
    spring.security.ignoringUrlList[11]=/image/**
    spring.security.ignoringUrlList[12]=/node/notify
    spring.security.ignoringUrlList[13]=/node/types
    spring.security.ignoringUrlList[14]=/open/manager
    spring.security.ignoringUrlList[15]=/version/info
    spring.security.ignoringUrlList[16]=/support/hostname/check
    spring.security.ignoringUrlList[17]=/proper/list/*
    spring.security.ignoringUrlList[18]=/alert/api/*
    spring.security.ignoringUrlList[19]=/query/api/internal/node/info
    spring.security.ignoringUrlList[20]=/query/api/internal/node/service/info
    spring.security.ignoringUrlList[21]=/node/types/anke
    spring.security.ignoringUrlList[22]=/mysql/slave/error/report
    spring.security.ignoringUrlList[23]=/server/config/mysql/*/readonly/*
    spring.security.ignoringUrlList[24]=/#/errPage
    spring.security.ignoringUrlList[25]=/backUp/notify
    spring.security.ignoringUrlList[26]=/mysql/switch/mode
    spring.security.ignoringUrlList[27]=/nightingale/**
    spring.security.ignoringUrlList[28]=/noah/**
    spring.security.ignoringUrlList[29]=/tiangong/**
    spring.security.ignoringUrlList[30]=/OauthIndex.html
    spring.security.ignoringUrlList[31]=/ThirdCallback.html
    spring.security.ignoringUrlList[32]=/applicationSecurity/**
    spring.security.ignoringUrlList[33]=/micro/**
    spring.security.ignoringUrlList[34]=/autoupgrade/api/**
    spring.security.ignoringUrlList[35]=/internal/client/terminal/info
    spring.security.ignoringUrlList[36]=/peer/scouts/node/**
    spring.security.ignoringUrlList[37]=/internal/upload/**
    spring.security.ignoringUrlList[38]=/peer/server/config/**
    spring.security.ignoringUrlList[39]=/server/config/haproxy/*
    spring.security.ignoringUrlList[40]=/openapi/**
    
    # 文件上传配置
    spring.servlet.multipart.enabled=true
    spring.servlet.multipart.file-size-threshold=1MB
    spring.servlet.multipart.location=/tmp
    spring.servlet.multipart.max-file-size=-1
    spring.servlet.multipart.max-request-size=-1
    
    # 升级存储配置
    upgrade.storage.dir=${base.dir}/upgrade
    upgrade.storage.log-folder=${base.dir}/upgrade/logs
    upgrade.storage.log-compare=${base.dir}/upgrade/compare
    upgrade.storage.file-folder=files
    upgrade.storage.tmp-folder=tmp
    upgrade.storage.upgrade-shell=upgrade_shell.sh
    upgrade.storage.security-validation=true
    
    # 数据库管理配置
    db.backupcommand=mysqldump
    db.restorecommand=mysql
    db.backupdir=/usr/libra/mysqlbackup/
    db.host.backupdir=${base.dir}/db/mysqlbackup/
    
    # K8s配置
    k8s.config.path=${base.dir}/kubeconfig
    k8s.protocol.default.http=true
    k8s.prefix.url=${K8S_API_URL:http://127.0.0.1:8080}
    harbor.prefix.url=${HARBOR_URL:http://hub.xylink.com:5000}
    
    # InfluxDB配置
    influxdb.url=${INFLUXDB_URL:http://127.0.0.1:8086}
    
    # 告警配置
    mail.alert.interval=240
    license.alert.interval=1440
    main.log.dir=${base.dir}/logs/
    alert.check.initial.delay=1200000
    alert.check.fixed.rate=600000
    license.check.initial.delay=6000000
    license.check.fixed.rate=600000
    license.alert.begin.days=30
    
    # 验证码配置
    captcha.enableCaptcha=${CAPTCHA_ENABLED:true}
    captcha.urlList[0]=/login
    captcha.conf.kaptcha.border=no
    captcha.conf.kaptcha.textproducer.font.color=73,190,56
    captcha.conf.kaptcha.textproducer.char.string=123456789ABCDEF
    captcha.conf.kaptcha.textproducer.char.length=5
    captcha.conf.kaptcha.background.clear.from=white
    captcha.conf.kaptcha.background.clear.to=white
    captcha.conf.kaptcha.noise.color=245,245,245
    
    # 用户配置
    user.list.admin=${ADMIN_USER:111111}
    user.list.guest=${GUEST_USER:111111}
    user.list.develop=${DEVELOP_USER:develop}
    
    # 访问日志配置
    server.tomcat.accesslog.buffered=false
    server.tomcat.accesslog.enabled=true
    server.tomcat.accesslog.prefix=localhost_access_log
    server.tomcat.accesslog.suffix=.log
    server.tomcat.basedir=${base.dir}/logs
    server.tomcat.accesslog.pattern=%h %l %u %t "%r" %s %b %D %I "%{Referer}i" "%{User-Agent}i" "%{X-Forwarded-For}i"
    server.tomcat.accesslog.request-attributes-enabled=true
    
    # JWT配置
    jwt.secret-key=${JWT_SECRET:472F4EA2D294EA50AEA66261A41115E22E337C35229AA5E73E4D95320AF820E2}
    jwt.timeout=${JWT_TIMEOUT:7200s}
    
    # 文件访问令牌配置
    file.access.token.timeout=60s
    
    # 操作日志开关
    user.operationLog.switch=${OPERATION_LOG_ENABLED:true}
    
    # 第三方容器云兼容开关
    third.k8s.switch=${THIRD_K8S_ENABLED:false}
    
    # Jackson配置
    spring.jackson.serialization.WRITE_DATES_AS_TIMESTAMPS=true
    
    # 定时任务配置
    schedule.cron.test-mail-server=${MAIL_TEST_CRON:0 0 1 * * ?}
    
    # 检测配置
    detect.amq.period=300000
    detect.redis.period=300000
    
    # 日志防护配置
    logback.defender.enable=true
    logback.defender.replaces[0].regex=(adminPwd=)([^,}]*)
    logback.defender.replaces[0].replacement=$1******
    logback.defender.replaces[1].regex=("password":)([^,}]*)
    logback.defender.replaces[1].replacement=$1"******"
    logback.defender.replaces[2].regex=U1O5ZeRyLFd#u9T6TF9h
    logback.defender.replaces[2].replacement=******
    logback.defender.replaces[3].regex=(ZOOKEEPER_PASSWORD=)([^,}]*)
    logback.defender.replaces[3].replacement=$1******
