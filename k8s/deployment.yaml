apiVersion: apps/v1
kind: Deployment
metadata:
  name: manager
  namespace: default
  labels:
    app: manager
    version: v1.0.0
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: manager
  template:
    metadata:
      labels:
        app: manager
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "18028"
        prometheus.io/path: "/manager/actuator/prometheus"
    spec:
      # 镜像拉取密钥
      imagePullSecrets:
        - name: harbor-registry-secret
      
      # 安全上下文
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      
      # 初始化容器（可选，用于等待依赖服务）
      initContainers:
        - name: wait-for-dependencies
          image: busybox:1.35
          command: ['sh', '-c']
          args:
            - |
              echo "等待依赖服务启动..."
              # 这里可以添加等待数据库、Redis等服务的逻辑
              # 例如: until nc -z mysql-service 3306; do echo "等待MySQL..."; sleep 2; done
              echo "依赖服务检查完成"
          securityContext:
            runAsNonRoot: true
            runAsUser: 1000
            runAsGroup: 1000
      
      containers:
        - name: manager
          image: hub.xylink.com:5000/private_cloud/manager:latest
          imagePullPolicy: Always
          
          ports:
            - name: http
              containerPort: 18028
              protocol: TCP
          
          # 环境变量
          env:
            - name: SPRING_PROFILES_ACTIVE
              value: "k8s"
            - name: MANAGER_BASE_DIR
              value: "/mnt/xylink"
            - name: LOG_LEVEL
              value: "info"
            - name: JAVA_OPTS
              value: "-Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UseStringDeduplication"
            - name: SPRING_CONFIG_LOCATION
              value: "file:/app/config/application.properties"
            - name: TZ
              value: "Asia/Shanghai"
            
            # 从Secret获取敏感信息
            - name: DB_PASSWORD_ENCRYPT_KEY
              valueFrom:
                secretKeyRef:
                  name: manager-secret
                  key: db-password-encrypt-key
            - name: DB_PASSWORD_ENCRYPT_IV
              valueFrom:
                secretKeyRef:
                  name: manager-secret
                  key: db-password-encrypt-iv
            - name: NOAH_AUTHORIZATION
              valueFrom:
                secretKeyRef:
                  name: manager-secret
                  key: noah-authorization
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: manager-secret
                  key: jwt-secret-key
          
          # 挂载配置文件
          volumeMounts:
            - name: config-volume
              mountPath: /app/config
              readOnly: true
            - name: data-volume
              mountPath: /mnt/xylink
            - name: logs-volume
              mountPath: /mnt/xylink/logs
            - name: tmp-volume
              mountPath: /tmp
          
          # 资源限制
          resources:
            requests:
              memory: "1Gi"
              cpu: "500m"
            limits:
              memory: "3Gi"
              cpu: "2000m"
          
          # 存活探针
          livenessProbe:
            httpGet:
              path: /manager/version/info
              port: 18028
              scheme: HTTP
            initialDelaySeconds: 120
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
            successThreshold: 1
          
          # 就绪探针
          readinessProbe:
            httpGet:
              path: /manager/version/info
              port: 18028
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
            successThreshold: 1
          
          # 启动探针
          startupProbe:
            httpGet:
              path: /manager/version/info
              port: 18028
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 30
            successThreshold: 1
          
          # 安全上下文
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: false
            capabilities:
              drop:
                - ALL
      
      # 卷定义
      volumes:
        - name: config-volume
          configMap:
            name: manager-config
            items:
              - key: application.properties
                path: application.properties
        - name: data-volume
          persistentVolumeClaim:
            claimName: manager-data-pvc
        - name: logs-volume
          persistentVolumeClaim:
            claimName: manager-logs-pvc
        - name: tmp-volume
          emptyDir:
            sizeLimit: 1Gi
      
      # 节点选择器（可选）
      nodeSelector:
        kubernetes.io/os: linux
      
      # 容忍度（可选）
      tolerations:
        - key: "node-role.kubernetes.io/master"
          operator: "Exists"
          effect: "NoSchedule"
      
      # 亲和性（可选）
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - manager
                topologyKey: kubernetes.io/hostname
      
      # 重启策略
      restartPolicy: Always
      
      # DNS策略
      dnsPolicy: ClusterFirst
      
      # 终止宽限期
      terminationGracePeriodSeconds: 30
