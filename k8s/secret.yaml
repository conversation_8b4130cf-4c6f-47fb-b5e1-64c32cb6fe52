apiVersion: v1
kind: Secret
metadata:
  name: manager-secret
  namespace: default
  labels:
    app: manager
type: Opaque
data:
  # 数据库密码加密密钥 (base64编码)
  db-password-encrypt-key: QTlCOUNEREVGMjAyMQ==  # A9B9C8DEF2021
  db-password-encrypt-iv: OTk4MjAyMQ==  # 9982021
  
  # Noah授权密钥 (base64编码)
  noah-authorization: VmcxU2cwalZjM1R0VFloZ1lwRjFNQlFNUXdoZjU4dks=  # Vg1Sg0jVc3TtTYhgYpF1MBQMQwhf58vK
  
  # API认证客户端密钥 (base64编码)
  api-auth-common-client: NDcyRjRFQTJEMjk0RUE1MEFFQTc2MjYxQTQxMTE1RTIyRTMzN0MzNTIyOUFBNUU3M0U0RDk1MzIwQUY4MjBFMg==  # 472F4EA2D294EA50AEA66261A41115E22E337C35229AA5E73E4D95320AF820E2
  api-auth-bm-server: NWYzZDhhN2MxZTliMmE2ZDRjMGY4ZTdiNWE5ZDNjMmUxZjZhOGI3ZDRjM2UyZjFhMGI5YzhkN2U2ZjVhNGIz  # 5f3d8a7c1e9b2a6d4c0f8e7b5a9d3c2e1f6a8b7d4c3e2f1a0b9c8d7e6f5a4b3
  
  # JWT密钥 (base64编码)
  jwt-secret-key: NDcyRjRFQTJEMjk0RUE1MEFFQTc2MjYxQTQxMTE1RTIyRTMzN0MzNTIyOUFBNUU3M0U0RDk1MzIwQUY4MjBFMg==  # 472F4EA2D294EA50AEA66261A41115E22E337C35229AA5E73E4D95320AF820E2

---
# 镜像拉取密钥（如果需要从私有仓库拉取镜像）
apiVersion: v1
kind: Secret
metadata:
  name: harbor-registry-secret
  namespace: default
type: kubernetes.io/dockerconfigjson
data:
  # 这里需要根据实际的Harbor仓库配置进行base64编码
  # 格式: {"auths":{"hub.xylink.com:5000":{"username":"用户名","password":"密码","auth":"用户名:密码的base64编码"}}}
  .dockerconfigjson: ********************************************************************************************************************************************************
