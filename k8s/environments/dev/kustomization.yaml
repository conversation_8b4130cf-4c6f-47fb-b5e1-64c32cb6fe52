apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

# 基础资源
resources:
  - ../../configmap.yaml
  - ../../secret.yaml
  - ../../pvc.yaml
  - ../../deployment.yaml
  - ../../service.yaml
  - ../../ingress.yaml
  - ../../hpa.yaml

# 命名空间
namespace: manager-dev

# 名称前缀
namePrefix: dev-

# 标签
commonLabels:
  environment: dev
  version: v1.0.0

# 注解
commonAnnotations:
  managed-by: kustomize
  environment: development

# 镜像替换
images:
  - name: hub.xylink.com:5000/private_cloud/manager
    newTag: dev-latest

# 配置补丁
patchesStrategicMerge:
  - deployment-patch.yaml
  - service-patch.yaml

# JSON补丁
patchesJson6902:
  - target:
      group: apps
      version: v1
      kind: Deployment
      name: manager
    path: deployment-json-patch.yaml

# 资源配置
replicas:
  - name: manager
    count: 1

# 配置生成器
configMapGenerator:
  - name: manager-env-config
    literals:
      - LOG_LEVEL=debug
      - ENVIRONMENT=development
      - CAPTCHA_ENABLED=false
      - THIRD_K8S_ENABLED=true

# Secret生成器
secretGenerator:
  - name: manager-env-secret
    literals:
      - DB_PASSWORD=dev_password
      - REDIS_PASSWORD=dev_redis_password
