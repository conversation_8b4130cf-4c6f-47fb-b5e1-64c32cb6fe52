apiVersion: apps/v1
kind: Deployment
metadata:
  name: manager
spec:
  replicas: 1
  template:
    spec:
      containers:
        - name: manager
          # 开发环境资源配置
          resources:
            requests:
              memory: "512Mi"
              cpu: "250m"
            limits:
              memory: "1Gi"
              cpu: "1000m"
          
          # 开发环境变量
          env:
            - name: SPRING_PROFILES_ACTIVE
              value: "dev,k8s"
            - name: LOG_LEVEL
              value: "debug"
            - name: JAVA_OPTS
              value: "-Xms256m -Xmx1g -XX:+UseG1GC -Dspring.profiles.active=dev"
            - name: CAPTCHA_ENABLED
              value: "false"
            - name: THIRD_K8S_ENABLED
              value: "true"
          
          # 开发环境探针配置（更宽松的超时）
          livenessProbe:
            httpGet:
              path: /manager/version/info
              port: 18028
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 15
            failureThreshold: 5
          
          readinessProbe:
            httpGet:
              path: /manager/version/info
              port: 18028
            initialDelaySeconds: 20
            periodSeconds: 10
            timeoutSeconds: 10
            failureThreshold: 5
          
          startupProbe:
            httpGet:
              path: /manager/version/info
              port: 18028
            initialDelaySeconds: 20
            periodSeconds: 10
            timeoutSeconds: 10
            failureThreshold: 60
