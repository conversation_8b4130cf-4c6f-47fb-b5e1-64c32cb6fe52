apiVersion: apps/v1
kind: Deployment
metadata:
  name: manager
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    spec:
      containers:
        - name: manager
          # 生产环境资源配置
          resources:
            requests:
              memory: "2Gi"
              cpu: "1000m"
            limits:
              memory: "4Gi"
              cpu: "2000m"
          
          # 生产环境变量
          env:
            - name: SPRING_PROFILES_ACTIVE
              value: "prod,k8s"
            - name: LOG_LEVEL
              value: "info"
            - name: JAVA_OPTS
              value: "-Xms2g -Xmx3g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UseStringDeduplication -XX:+OptimizeStringConcat"
            - name: CAPTCHA_ENABLED
              value: "true"
            - name: THIRD_K8S_ENABLED
              value: "false"
          
          # 生产环境探针配置（更严格）
          livenessProbe:
            httpGet:
              path: /manager/version/info
              port: 18028
            initialDelaySeconds: 120
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
          
          readinessProbe:
            httpGet:
              path: /manager/version/info
              port: 18028
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          
          startupProbe:
            httpGet:
              path: /manager/version/info
              port: 18028
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 30
      
      # 生产环境亲和性配置
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - manager
              topologyKey: kubernetes.io/hostname
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              preference:
                matchExpressions:
                  - key: node-type
                    operator: In
                    values:
                      - compute
