apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: manager-hpa
spec:
  minReplicas: 3
  maxReplicas: 20
  metrics:
    # CPU使用率指标（生产环境更严格）
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 60
    
    # 内存使用率指标
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 70
  
  # 生产环境扩缩容行为配置
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 600  # 更长的缩容稳定窗口期
      policies:
        - type: Percent
          value: 25  # 每次最多缩容25%的Pod
          periodSeconds: 120
        - type: Pods
          value: 1   # 每次最多缩容1个Pod
          periodSeconds: 120
      selectPolicy: Min
    scaleUp:
      stabilizationWindowSeconds: 120
      policies:
        - type: Percent
          value: 50  # 每次最多扩容50%的Pod
          periodSeconds: 60
        - type: Pods
          value: 2   # 每次最多扩容2个Pod
          periodSeconds: 60
      selectPolicy: Max
