apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

# 基础资源
resources:
  - ../../configmap.yaml
  - ../../secret.yaml
  - ../../pvc.yaml
  - ../../deployment.yaml
  - ../../service.yaml
  - ../../ingress.yaml
  - ../../hpa.yaml

# 命名空间
namespace: manager-prod

# 名称前缀
namePrefix: prod-

# 标签
commonLabels:
  environment: prod
  version: v1.0.0

# 注解
commonAnnotations:
  managed-by: kustomize
  environment: production

# 镜像替换
images:
  - name: hub.xylink.com:5000/private_cloud/manager
    newTag: v1.0.0

# 配置补丁
patchesStrategicMerge:
  - deployment-patch.yaml
  - service-patch.yaml
  - hpa-patch.yaml

# 资源配置
replicas:
  - name: manager
    count: 3

# 配置生成器
configMapGenerator:
  - name: manager-env-config
    literals:
      - LOG_LEVEL=info
      - ENVIRONMENT=production
      - CAPTCHA_ENABLED=true
      - THIRD_K8S_ENABLED=false

# Secret生成器
secretGenerator:
  - name: manager-env-secret
    literals:
      - DB_PASSWORD=prod_secure_password
      - REDIS_PASSWORD=prod_redis_secure_password
