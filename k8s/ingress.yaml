apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: manager-ingress
  namespace: default
  labels:
    app: manager
  annotations:
    # Nginx Ingress Controller注解
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "64k"
    nginx.ingress.kubernetes.io/proxy-buffers-number: "32"
    nginx.ingress.kubernetes.io/client-max-body-size: "100m"
    
    # SSL配置（如果需要HTTPS）
    # cert-manager.io/cluster-issuer: "letsencrypt-prod"
    # nginx.ingress.kubernetes.io/ssl-redirect: "true"
    
    # 会话亲和性
    nginx.ingress.kubernetes.io/affinity: "cookie"
    nginx.ingress.kubernetes.io/session-cookie-name: "manager-session"
    nginx.ingress.kubernetes.io/session-cookie-expires: "86400"
    nginx.ingress.kubernetes.io/session-cookie-max-age: "86400"
    nginx.ingress.kubernetes.io/session-cookie-path: "/manager"
    
    # 限流配置
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    
    # 白名单配置（可选）
    # nginx.ingress.kubernetes.io/whitelist-source-range: "10.0.0.0/8,**********/12,***********/16"
spec:
  # TLS配置（如果需要HTTPS）
  # tls:
  #   - hosts:
  #       - manager.example.com
  #     secretName: manager-tls-secret
  
  rules:
    - host: manager.example.com  # 替换为实际的域名
      http:
        paths:
          - path: /manager(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: manager-service
                port:
                  number: 18028
    
    # 如果需要支持多个域名
    - host: manager-dev.example.com
      http:
        paths:
          - path: /manager(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: manager-service
                port:
                  number: 18028

---
# 如果使用Traefik作为Ingress Controller
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: manager-traefik-ingress
  namespace: default
  labels:
    app: manager
  annotations:
    kubernetes.io/ingress.class: "traefik"
    traefik.ingress.kubernetes.io/router.entrypoints: "web"
    traefik.ingress.kubernetes.io/router.middlewares: "default-manager-stripprefix@kubernetescrd"
    # 如果需要HTTPS
    # traefik.ingress.kubernetes.io/router.entrypoints: "websecure"
    # traefik.ingress.kubernetes.io/router.tls: "true"
spec:
  rules:
    - host: manager-traefik.example.com
      http:
        paths:
          - path: /manager
            pathType: Prefix
            backend:
              service:
                name: manager-service
                port:
                  number: 18028

---
# Traefik中间件配置（用于路径重写）
apiVersion: traefik.containo.us/v1alpha1
kind: Middleware
metadata:
  name: manager-stripprefix
  namespace: default
spec:
  stripPrefix:
    prefixes:
      - /manager
