apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: manager-data-pvc
  namespace: default
  labels:
    app: manager
    volume-type: data
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  # 存储类名称，根据实际K8s集群配置调整
  storageClassName: standard
  # 卷模式
  volumeMode: Filesystem

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: manager-logs-pvc
  namespace: default
  labels:
    app: manager
    volume-type: logs
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  # 存储类名称，根据实际K8s集群配置调整
  storageClassName: standard
  # 卷模式
  volumeMode: Filesystem

---
# 如果需要共享存储，可以使用ReadWriteMany
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: manager-shared-pvc
  namespace: default
  labels:
    app: manager
    volume-type: shared
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 20Gi
  # 共享存储类名称，如NFS、CephFS等
  storageClassName: nfs-storage
  volumeMode: Filesystem
