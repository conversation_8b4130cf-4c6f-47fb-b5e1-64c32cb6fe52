apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: manager-hpa
  namespace: default
  labels:
    app: manager
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: manager
  minReplicas: 2
  maxReplicas: 10
  metrics:
    # CPU使用率指标
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
    
    # 内存使用率指标
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 80
    
    # 自定义指标（如果有Prometheus监控）
    # - type: Pods
    #   pods:
    #     metric:
    #       name: http_requests_per_second
    #     target:
    #       type: AverageValue
    #       averageValue: "100"
  
  # 扩缩容行为配置
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300  # 缩容稳定窗口期
      policies:
        - type: Percent
          value: 50  # 每次最多缩容50%的Pod
          periodSeconds: 60
        - type: Pods
          value: 2   # 每次最多缩容2个Pod
          periodSeconds: 60
      selectPolicy: Min  # 选择最小的缩容策略
    scaleUp:
      stabilizationWindowSeconds: 60   # 扩容稳定窗口期
      policies:
        - type: Percent
          value: 100  # 每次最多扩容100%的Pod
          periodSeconds: 60
        - type: Pods
          value: 4    # 每次最多扩容4个Pod
          periodSeconds: 60
      selectPolicy: Max   # 选择最大的扩容策略

---
# 垂直Pod自动扩缩容（VPA）配置（可选）
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: manager-vpa
  namespace: default
  labels:
    app: manager
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: manager
  updatePolicy:
    updateMode: "Auto"  # 可选值: "Off", "Initial", "Recreation", "Auto"
  resourcePolicy:
    containerPolicies:
      - containerName: manager
        minAllowed:
          cpu: 100m
          memory: 512Mi
        maxAllowed:
          cpu: 4000m
          memory: 8Gi
        controlledResources: ["cpu", "memory"]
        controlledValues: RequestsAndLimits
