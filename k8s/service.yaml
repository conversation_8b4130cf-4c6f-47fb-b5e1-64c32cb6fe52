apiVersion: v1
kind: Service
metadata:
  name: manager-service
  namespace: default
  labels:
    app: manager
  annotations:
    # Prometheus监控注解
    prometheus.io/scrape: "true"
    prometheus.io/port: "18028"
    prometheus.io/path: "/manager/actuator/prometheus"
spec:
  type: ClusterIP
  ports:
    - name: http
      port: 18028
      targetPort: 18028
      protocol: TCP
  selector:
    app: manager
  sessionAffinity: None

---
# NodePort服务（用于外部访问）
apiVersion: v1
kind: Service
metadata:
  name: manager-nodeport
  namespace: default
  labels:
    app: manager
    service-type: nodeport
spec:
  type: NodePort
  ports:
    - name: http
      port: 18028
      targetPort: 18028
      nodePort: 30028  # 可以指定具体端口，或让K8s自动分配
      protocol: TCP
  selector:
    app: manager

---
# Headless服务（用于StatefulSet或服务发现）
apiVersion: v1
kind: Service
metadata:
  name: manager-headless
  namespace: default
  labels:
    app: manager
    service-type: headless
spec:
  type: ClusterIP
  clusterIP: None
  ports:
    - name: http
      port: 18028
      targetPort: 18028
      protocol: TCP
  selector:
    app: manager
