#!/bin/bash

# Kubernetes部署脚本
set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
K8S_DIR="${PROJECT_ROOT}/k8s"

# 默认配置
NAMESPACE=${NAMESPACE:-"default"}
ENVIRONMENT=${ENVIRONMENT:-"dev"}
KUBECTL_CONTEXT=${KUBECTL_CONTEXT:-""}
DRY_RUN=${DRY_RUN:-"false"}

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
Kubernetes部署脚本

用法: $0 [选项] [操作]

操作:
    deploy      部署应用 (默认)
    delete      删除应用
    restart     重启应用
    status      查看应用状态
    logs        查看应用日志
    scale       扩缩容应用

选项:
    -n, --namespace NAMESPACE    设置命名空间 (默认: default)
    -e, --environment ENV        设置环境 (dev/test/prod, 默认: dev)
    -c, --context CONTEXT        设置kubectl上下文
    -d, --dry-run               只显示将要执行的操作，不实际执行
    -f, --force                 强制执行操作
    -w, --wait                  等待部署完成
    -t, --timeout TIMEOUT       设置超时时间 (默认: 300s)
    -r, --replicas REPLICAS      设置副本数 (用于scale操作)
    -h, --help                  显示此帮助信息

示例:
    $0 deploy -n production -e prod     # 部署到生产环境
    $0 delete -n test                   # 删除测试环境的应用
    $0 restart -w                       # 重启应用并等待完成
    $0 scale -r 5                       # 扩容到5个副本
    $0 status                           # 查看应用状态
    $0 logs -f                          # 实时查看日志

环境变量:
    NAMESPACE        命名空间
    ENVIRONMENT      环境
    KUBECTL_CONTEXT  kubectl上下文
    DRY_RUN         是否为试运行
EOF
}

# 检查kubectl命令
check_kubectl() {
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl命令未找到，请先安装kubectl"
        exit 1
    fi
    
    # 检查kubectl连接
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到Kubernetes集群，请检查kubectl配置"
        exit 1
    fi
    
    log_info "Kubernetes集群连接正常"
}

# 设置kubectl上下文
set_context() {
    if [[ -n "${KUBECTL_CONTEXT}" ]]; then
        log_info "切换kubectl上下文到: ${KUBECTL_CONTEXT}"
        kubectl config use-context "${KUBECTL_CONTEXT}"
    fi
}

# 创建命名空间
create_namespace() {
    if ! kubectl get namespace "${NAMESPACE}" &> /dev/null; then
        log_info "创建命名空间: ${NAMESPACE}"
        if [[ "${DRY_RUN}" == "true" ]]; then
            echo "kubectl create namespace ${NAMESPACE}"
        else
            kubectl create namespace "${NAMESPACE}"
        fi
    else
        log_info "命名空间 ${NAMESPACE} 已存在"
    fi
}

# 应用Kubernetes资源
apply_resources() {
    local resource_files=(
        "configmap.yaml"
        "secret.yaml"
        "pvc.yaml"
        "deployment.yaml"
        "service.yaml"
        "ingress.yaml"
        "hpa.yaml"
    )
    
    log_info "开始部署Kubernetes资源..."
    
    for file in "${resource_files[@]}"; do
        local file_path="${K8S_DIR}/${file}"
        if [[ -f "${file_path}" ]]; then
            log_info "应用资源文件: ${file}"
            if [[ "${DRY_RUN}" == "true" ]]; then
                echo "kubectl apply -f ${file_path} -n ${NAMESPACE}"
            else
                kubectl apply -f "${file_path}" -n "${NAMESPACE}"
            fi
        else
            log_warn "资源文件不存在: ${file_path}"
        fi
    done
}

# 删除Kubernetes资源
delete_resources() {
    local resource_files=(
        "hpa.yaml"
        "ingress.yaml"
        "service.yaml"
        "deployment.yaml"
        "pvc.yaml"
        "secret.yaml"
        "configmap.yaml"
    )
    
    log_info "开始删除Kubernetes资源..."
    
    for file in "${resource_files[@]}"; do
        local file_path="${K8S_DIR}/${file}"
        if [[ -f "${file_path}" ]]; then
            log_info "删除资源文件: ${file}"
            if [[ "${DRY_RUN}" == "true" ]]; then
                echo "kubectl delete -f ${file_path} -n ${NAMESPACE} --ignore-not-found=true"
            else
                kubectl delete -f "${file_path}" -n "${NAMESPACE}" --ignore-not-found=true
            fi
        fi
    done
}

# 重启应用
restart_app() {
    log_info "重启应用..."
    if [[ "${DRY_RUN}" == "true" ]]; then
        echo "kubectl rollout restart deployment/manager -n ${NAMESPACE}"
    else
        kubectl rollout restart deployment/manager -n "${NAMESPACE}"
    fi
}

# 等待部署完成
wait_for_deployment() {
    local timeout=${1:-300}
    log_info "等待部署完成 (超时: ${timeout}s)..."
    if [[ "${DRY_RUN}" != "true" ]]; then
        kubectl rollout status deployment/manager -n "${NAMESPACE}" --timeout="${timeout}s"
    fi
}

# 查看应用状态
show_status() {
    log_info "应用状态:"
    echo "=========================================="
    
    # Deployment状态
    echo "Deployment状态:"
    kubectl get deployment manager -n "${NAMESPACE}" -o wide 2>/dev/null || echo "Deployment不存在"
    echo ""
    
    # Pod状态
    echo "Pod状态:"
    kubectl get pods -l app=manager -n "${NAMESPACE}" -o wide 2>/dev/null || echo "Pod不存在"
    echo ""
    
    # Service状态
    echo "Service状态:"
    kubectl get service -l app=manager -n "${NAMESPACE}" -o wide 2>/dev/null || echo "Service不存在"
    echo ""
    
    # HPA状态
    echo "HPA状态:"
    kubectl get hpa manager-hpa -n "${NAMESPACE}" 2>/dev/null || echo "HPA不存在"
    echo ""
    
    # Ingress状态
    echo "Ingress状态:"
    kubectl get ingress manager-ingress -n "${NAMESPACE}" 2>/dev/null || echo "Ingress不存在"
    echo ""
    
    # PVC状态
    echo "PVC状态:"
    kubectl get pvc -l app=manager -n "${NAMESPACE}" 2>/dev/null || echo "PVC不存在"
    
    echo "=========================================="
}

# 查看应用日志
show_logs() {
    local follow=${1:-false}
    local tail_lines=${2:-100}
    
    log_info "查看应用日志..."
    
    local pods=$(kubectl get pods -l app=manager -n "${NAMESPACE}" -o jsonpath='{.items[*].metadata.name}' 2>/dev/null)
    
    if [[ -z "${pods}" ]]; then
        log_error "未找到运行中的Pod"
        return 1
    fi
    
    local pod_array=($pods)
    local first_pod=${pod_array[0]}
    
    log_info "显示Pod ${first_pod} 的日志"
    
    if [[ "${follow}" == "true" ]]; then
        kubectl logs -f "${first_pod}" -n "${NAMESPACE}"
    else
        kubectl logs "${first_pod}" -n "${NAMESPACE}" --tail="${tail_lines}"
    fi
}

# 扩缩容应用
scale_app() {
    local replicas=${1:-2}
    log_info "扩缩容应用到 ${replicas} 个副本..."
    
    if [[ "${DRY_RUN}" == "true" ]]; then
        echo "kubectl scale deployment manager --replicas=${replicas} -n ${NAMESPACE}"
    else
        kubectl scale deployment manager --replicas="${replicas}" -n "${NAMESPACE}"
        wait_for_deployment
    fi
}

# 解析命令行参数
OPERATION="deploy"
FORCE=false
WAIT=false
TIMEOUT=300
REPLICAS=2
FOLLOW_LOGS=false
TAIL_LINES=100

while [[ $# -gt 0 ]]; do
    case $1 in
        deploy|delete|restart|status|logs|scale)
            OPERATION="$1"
            shift
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -c|--context)
            KUBECTL_CONTEXT="$2"
            shift 2
            ;;
        -d|--dry-run)
            DRY_RUN="true"
            shift
            ;;
        -f|--force)
            FORCE="true"
            shift
            ;;
        -w|--wait)
            WAIT="true"
            shift
            ;;
        -t|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        -r|--replicas)
            REPLICAS="$2"
            shift 2
            ;;
        --follow)
            FOLLOW_LOGS="true"
            shift
            ;;
        --tail)
            TAIL_LINES="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 显示配置信息
log_info "=========================================="
log_info "Kubernetes部署配置"
log_info "=========================================="
log_info "操作: ${OPERATION}"
log_info "命名空间: ${NAMESPACE}"
log_info "环境: ${ENVIRONMENT}"
log_info "试运行: ${DRY_RUN}"
if [[ -n "${KUBECTL_CONTEXT}" ]]; then
    log_info "kubectl上下文: ${KUBECTL_CONTEXT}"
fi
log_info "=========================================="

# 检查环境
check_kubectl
set_context

# 执行操作
case "${OPERATION}" in
    deploy)
        create_namespace
        apply_resources
        if [[ "${WAIT}" == "true" ]]; then
            wait_for_deployment "${TIMEOUT}"
        fi
        show_status
        ;;
    delete)
        if [[ "${FORCE}" == "true" ]] || [[ "${DRY_RUN}" == "true" ]]; then
            delete_resources
        else
            read -p "确认删除应用? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                delete_resources
            else
                log_info "取消删除操作"
            fi
        fi
        ;;
    restart)
        restart_app
        if [[ "${WAIT}" == "true" ]]; then
            wait_for_deployment "${TIMEOUT}"
        fi
        show_status
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs "${FOLLOW_LOGS}" "${TAIL_LINES}"
        ;;
    scale)
        scale_app "${REPLICAS}"
        show_status
        ;;
    *)
        log_error "未知操作: ${OPERATION}"
        show_help
        exit 1
        ;;
esac

log_info "操作完成: ${OPERATION}"
