#!/bin/bash

# Docker镜像构建脚本
set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
VERSION=${VERSION:-"latest"}

# 镜像配置
REGISTRY=${REGISTRY:-"hub.xylink.com:5000"}
NAMESPACE=${NAMESPACE:-"private_cloud"}
IMAGE_NAME=${IMAGE_NAME:-"manager"}
FULL_IMAGE_NAME="${REGISTRY}/${NAMESPACE}/${IMAGE_NAME}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
Docker镜像构建脚本

用法: $0 [选项]

选项:
    -v, --version VERSION     设置镜像版本标签 (默认: latest)
    -r, --registry REGISTRY   设置镜像仓库地址 (默认: hub.xylink.com:5000)
    -n, --namespace NAMESPACE 设置镜像命名空间 (默认: private_cloud)
    -i, --image IMAGE_NAME    设置镜像名称 (默认: manager)
    -p, --push               构建完成后推送到仓库
    -c, --clean              构建前清理旧镜像
    --no-cache               不使用Docker缓存构建
    --multi-arch             构建多架构镜像 (amd64, arm64)
    -h, --help               显示此帮助信息

示例:
    $0 -v 1.0.0 -p                    # 构建v1.0.0版本并推送
    $0 --clean --no-cache             # 清理旧镜像并无缓存构建
    $0 --multi-arch -v 1.0.0 -p       # 构建多架构镜像并推送

环境变量:
    REGISTRY     镜像仓库地址
    NAMESPACE    镜像命名空间
    IMAGE_NAME   镜像名称
    VERSION      镜像版本
    DOCKER_BUILDKIT  启用Docker BuildKit (推荐设置为1)
EOF
}

# 解析命令行参数
PUSH=false
CLEAN=false
NO_CACHE=false
MULTI_ARCH=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -i|--image)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -p|--push)
            PUSH=true
            shift
            ;;
        -c|--clean)
            CLEAN=true
            shift
            ;;
        --no-cache)
            NO_CACHE=true
            shift
            ;;
        --multi-arch)
            MULTI_ARCH=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 更新完整镜像名称
FULL_IMAGE_NAME="${REGISTRY}/${NAMESPACE}/${IMAGE_NAME}"

# 显示构建信息
log_info "=========================================="
log_info "Docker镜像构建配置"
log_info "=========================================="
log_info "项目根目录: ${PROJECT_ROOT}"
log_info "镜像名称: ${FULL_IMAGE_NAME}:${VERSION}"
log_info "构建时间: ${BUILD_DATE}"
log_info "Git提交: ${GIT_COMMIT}"
log_info "推送镜像: ${PUSH}"
log_info "清理旧镜像: ${CLEAN}"
log_info "无缓存构建: ${NO_CACHE}"
log_info "多架构构建: ${MULTI_ARCH}"
log_info "=========================================="

# 切换到项目根目录
cd "${PROJECT_ROOT}"

# 检查必要文件
if [[ ! -f "Dockerfile" ]]; then
    log_error "未找到Dockerfile文件"
    exit 1
fi

if [[ ! -f "build.gradle" ]]; then
    log_error "未找到build.gradle文件"
    exit 1
fi

# 清理旧镜像
if [[ "${CLEAN}" == "true" ]]; then
    log_info "清理旧镜像..."
    docker images "${FULL_IMAGE_NAME}" --format "table {{.Repository}}:{{.Tag}}\t{{.ID}}" | grep -v "REPOSITORY" | while read line; do
        image_id=$(echo $line | awk '{print $2}')
        if [[ -n "${image_id}" ]]; then
            log_info "删除镜像: ${image_id}"
            docker rmi "${image_id}" || true
        fi
    done
fi

# 启用Docker BuildKit
export DOCKER_BUILDKIT=1

# 构建参数
BUILD_ARGS=(
    "--build-arg" "BUILD_DATE=${BUILD_DATE}"
    "--build-arg" "GIT_COMMIT=${GIT_COMMIT}"
    "--build-arg" "VERSION=${VERSION}"
    "--label" "org.opencontainers.image.created=${BUILD_DATE}"
    "--label" "org.opencontainers.image.revision=${GIT_COMMIT}"
    "--label" "org.opencontainers.image.version=${VERSION}"
    "--label" "org.opencontainers.image.title=Manager Application"
    "--label" "org.opencontainers.image.description=XYLink Manager Application"
    "--label" "org.opencontainers.image.vendor=XYLink"
)

# 添加无缓存参数
if [[ "${NO_CACHE}" == "true" ]]; then
    BUILD_ARGS+=("--no-cache")
fi

# 构建镜像
if [[ "${MULTI_ARCH}" == "true" ]]; then
    log_info "开始多架构镜像构建..."
    
    # 创建并使用buildx构建器
    docker buildx create --name manager-builder --use || docker buildx use manager-builder
    
    # 多架构构建
    if [[ "${PUSH}" == "true" ]]; then
        docker buildx build \
            "${BUILD_ARGS[@]}" \
            --platform linux/amd64,linux/arm64 \
            --tag "${FULL_IMAGE_NAME}:${VERSION}" \
            --tag "${FULL_IMAGE_NAME}:latest" \
            --push \
            .
    else
        docker buildx build \
            "${BUILD_ARGS[@]}" \
            --platform linux/amd64,linux/arm64 \
            --tag "${FULL_IMAGE_NAME}:${VERSION}" \
            --tag "${FULL_IMAGE_NAME}:latest" \
            .
    fi
else
    log_info "开始单架构镜像构建..."
    
    # 单架构构建
    docker build \
        "${BUILD_ARGS[@]}" \
        --tag "${FULL_IMAGE_NAME}:${VERSION}" \
        --tag "${FULL_IMAGE_NAME}:latest" \
        .
    
    # 推送镜像
    if [[ "${PUSH}" == "true" ]]; then
        log_info "推送镜像到仓库..."
        docker push "${FULL_IMAGE_NAME}:${VERSION}"
        docker push "${FULL_IMAGE_NAME}:latest"
    fi
fi

# 显示镜像信息
log_info "=========================================="
log_info "构建完成！"
log_info "=========================================="
if [[ "${MULTI_ARCH}" != "true" ]]; then
    docker images "${FULL_IMAGE_NAME}" --format "table {{.Repository}}:{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
fi

log_info "镜像标签:"
log_info "  - ${FULL_IMAGE_NAME}:${VERSION}"
log_info "  - ${FULL_IMAGE_NAME}:latest"

if [[ "${PUSH}" == "true" ]]; then
    log_info "镜像已推送到仓库: ${REGISTRY}"
fi

log_info "构建完成时间: $(date)"
log_info "=========================================="
