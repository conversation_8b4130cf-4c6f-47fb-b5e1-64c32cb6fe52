#!/bin/bash
## docker 版本
##备份主库数据，在任何一个宿主机上执行即可，备份文件在当前目录下 
## 5.2账号权限不用回收
## 3.9及以下的版本回收权限 ，revoke super, SHUTDOWN  on *.* from 'private_cloud'@'%';
## 主从复制账号：5.2 使用private_cloud，3.9及以下使用private_cloud 或者 rep 密码是一样的

pwd=`pwd`

#备份主库连接信息
master_mysqlip=$1
master_port=$2
master_user='dbbak'
master_passwd='U1O5ZeRyLFd#u9T6TF9h'

# 主从复制账号
repl_user='repl_canal'
repl_passwd='U8kkWjeX7#HeYOv6TXIG'


#还原从库连接信息
slave_mysqlip=$3
slave_port=$4
user='dbbak'
passwd='U1O5ZeRyLFd#u9T6TF9h'

mysqlImage=$(kubectl get pods | grep private-mysql | awk '{print $1}' | head -1 | xargs kubectl describe pods | grep Image:| grep mysql| awk '{print $2}' | head -1)

echo -e "\033[32;1m--------mysql docker 镜像名:$mysqlImage------------\033[0m"

master_status=`docker run --rm --net=host $mysqlImage mysql -u$user -p$passwd -h $master_mysqlip -P$master_port -e "show master status\G;"`
master_log_file=`echo "$master_status" | awk  'NR==2{print $2}'`
master_log_pos=`echo "$master_status" | awk 'NR==3{print $2}'`

masterinfo="MASTER_HOST='$master_mysqlip',MASTER_PORT=$master_port,MASTER_USER='$repl_user',MASTER_PASSWORD='$repl_passwd';"
change_masterinfo="CHANGE MASTER TO MASTER_LOG_FILE='$master_log_file',MASTER_LOG_POS=$master_log_pos",$masterinfo

echo -e "\033[32;1m--------$(date +"%Y-%m-%d %H:%M:%S") 开始在$slave_mysqlip上创建主从关系！！！命令是：$change_masterinfo------------\033[0m"
docker run --rm --net=host $mysqlImage mysql -u$user -p$passwd -h $slave_mysqlip -P$slave_port -e "stop slave;$change_masterinfo;start slave;"
Slave_IO=`docker run --rm --net=host $mysqlImage mysql -u$user -p$passwd -h $slave_mysqlip -P$slave_port -e "show slave status \G" | egrep "Slave_IO_Running:" | awk -F ': ' '{print $2}'`
Slave_SQL=`docker run --rm --net=host $mysqlImage mysql -u$user -p$passwd -h $slave_mysqlip -P$slave_port -e "show slave status \G" | egrep "Slave_SQL_Running:" | awk -F ': ' '{print $2}'`
Slave_status=`docker run --rm --net=host $mysqlImage mysql -u$user -p$passwd -h $slave_mysqlip -P$slave_port -e "show variables like 'super_read_only' \G" | egrep "Value:" | awk -F ': ' '{print $2}'`
echo -e "\033[32;1m--------$(date +"%Y-%m-%d %H:%M:%S") 主从复制状态信息 Slave_IO_Running:$Slave_IO Slave_SQL_Running:$Slave_SQL------------\033[0m"
if [ "$Slave_status" = "ON" ];then
     echo -e "\033[32;1m--------$(date +"%Y-%m-%d %H:%M:%S") $slave_mysqlip的状态为:只读------------\033[0m"
elif [ "$Slave_status" = "OFF" ];then
     echo -e "\033[32;1m--------$(date +"%Y-%m-%d %H:%M:%S") $slave_mysqlip的状态为:读写------------\033[0m"
else
      echo -e "\033[32;1m--------$(date +"%Y-%m-%d %H:%M:%S") $slave_mysqlip的状态为:$Slave_status------------\033[0m"
fi
if [ "$Slave_IO" = "Yes" ]&&[ "$Slave_SQL" = "Yes" ];then
     echo -e "\033[32;1m--------$(date +"%Y-%m-%d %H:%M:%S") 主从复制正常，请使用！！！------------\033[0m"
else
     echo -e "\033[31;1m--------$(date +"%Y-%m-%d %H:%M:%S") 主从复制异常，请连接管理员！！！------------\033[0m"
fi
