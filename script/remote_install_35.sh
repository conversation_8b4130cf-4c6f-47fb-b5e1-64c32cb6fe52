#! /bin/bash

#   cat machine_2018-05-29.csv | awk -F ',' '{gsub(/"/, "", $5); print $5}'
#   1, need install sshpass first: yum install -y sshpass
#   2, need set ip and ssh port for master
#   3，this script need with node_xylink.tar.gz on the same position

#set -o nounset  #引用未定义的变量(缺省值为“”)
#set -o errexit  #执行失败的命令被忽略
#set -o verbose  #为调试打开verbose模式
#set -o xtrace   #跟踪脚本执行

pwd=$(pwd)
date=$(date +%s)
password=Wow1nemo
masterip=************
masterport=26826

if [ ! $1 ] ;then
   echo "need input file"
   exit 0
fi

list=$(cat $1)
for line in $list
do
  if [ `echo $line | grep ^#` ] ; then
         echo $line" : continue"
         continue
  fi

  if [ -z "$line" ] ; then
        continue
  fi

  ip=$(echo $line | cut -d '@' -f1)
  pubip=$(echo $line | cut -d '@' -f2)
  label=$(echo $line | cut -d '@' -f3)
  nfs=$(echo $line | cut -d '@' -f4)
  nfsip=$(echo $line | cut -d '@' -f5)
  ntp=$(echo $line | cut -d '@' -f6)
  ntpip=$(echo $line | cut -d '@' -f7)

  sshpass -p $password scp -oStrictHostKeyChecking=no ./xylink_node.tar.gz root@$ip:/home
  sshpass -p $password ssh -oStrictHostKeyChecking=no root@$ip 'tar xzf /home/<USER>/home/<USER>/home/<USER>/install_node.sh /home/<USER>/install_node_new.sh'

  input_para="master=$masterip; local_ip=$ip; nodetype=$label;"

  if [[ "$nfs" = nfs ]] ; then
    input_para=$input_para" installnfs=yes; nfs_server_ip=$nfsip;"
  fi

  if [[ "$ntp" = ntp ]] ; then
    input_para=$input_para" usentp=yes; ntp_server_ip=$ntpip;"
  fi

  echo $input_para

  sshpass -p $password ssh -oStrictHostKeyChecking=no root@$ip 'sed -i "s/get_input_parameter #tag/'"$input_para"'/g;" /home/<USER>/install_node_new.sh'

  #sshpass -p $password ssh -oStrictHostKeyChecking=no root@$ip 'sed -i "s/^echo -n.*//g; s/^read master/master='"$masterip"'/g; s/^read local_ip/local_ip='"$ip"'/g;" /home/<USER>/install_node_new.sh'

  sshpass -p $password ssh -oStrictHostKeyChecking=no root@$ip 'cd /home/<USER>/ && sh install_node_new.sh'

  sleep 5
  nodename=$(sshpass -p $password ssh -oStrictHostKeyChecking=no root@$ip 'uname -n')

  if [[ "$nodename" && "$label" ]] ; then
    sshpass -p $password ssh -oStrictHostKeyChecking=no root@$masterip -p $masterport 'kubectl label nodes '"$nodename $label"'=xylink'
    sshpass -p $password ssh -oStrictHostKeyChecking=no root@$masterip -p $masterport 'kubectl label nodes '"$nodename"' type='"$label"''
  fi

  if [[ "$label" = dmcu || "$label" = record ]] ; then
    if [[ "$pubip" ]] ;then
        patchdata='{"data":{"'"$nodename"'-INTERNAL-IP": "'"$ip"'",'"$nodename"'-PUBLIC-IP": "'"$pubip"'"}}'
    else
        patchdata='{"data":{"'"$nodename"'-INTERNAL-IP": "'"$ip"'"}}'
    fi
  #  sshpass -p $password ssh -oStrictHostKeyChecking=no root@$masterip -p $masterport 'kubectl patch configmap all-dmcu -p '"'"''"$patchdata"''"'"''
    sshpass -p $password ssh -oStrictHostKeyChecking=no root@$masterip -p $masterport 'kubectl patch configmap all-'"$label"' -p '\'"$patchdata"\'''
  fi


  echo "install done $ip:$label"

done

