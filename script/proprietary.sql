CREATE DATABASE IF NOT EXISTS proprietary DEFAULT CHARSET utf8 COLLATE utf8_general_ci;
-- ----------------------------
-- Table structure for libra_cloud_cluster
-- ----------------------------
use proprietary;
DROP TABLE IF EXISTS `libra_cloud_cluster`;
CREATE TABLE `libra_cloud_cluster` (
  `cloud_uuid` varchar(64) NOT NULL COMMENT '专有云唯一云团号',
  `fingerprint` varchar(200) NOT NULL COMMENT '指纹码',
  `last_fingerprint` varchar(200) DEFAULT NULL COMMENT '记录上一次指纹码信息',
  `cloud_cluster_id` varchar(10) NOT NULL COMMENT '云团ID',
  `cloud_id` varchar(10) NOT NULL COMMENT '当前分区云区号',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`cloud_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 comment '云团ID和分区云区号表';

-- ----------------------------
-- Table structure for libra_cloud_connection
-- ----------------------------
DROP TABLE IF EXISTS `libra_cloud_connection`;
CREATE TABLE `libra_cloud_connection` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `cloud_uuid` varchar(64) NOT NULL COMMENT '本云ID',
  `cloud_cluster_id` varchar(10) NOT NULL COMMENT '云团ID',
  `cloud_id` varchar(10) NOT NULL COMMENT '分区云区号',
  `cloud_domain` varchar(128) NOT NULL COMMENT 'IP地址/域名',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `del_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除状态，0=未删除，1=已删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 comment '区号管理连接关系表';

-- ----------------------------
-- Table structure for libra_route_config
-- ----------------------------
DROP TABLE IF EXISTS `libra_route_config`;
CREATE TABLE `libra_route_config` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '路由ID',
  `src_cloud_id` varchar(10) NOT NULL COMMENT '源区号',
  `next_cloud_id` varchar(10) NOT NULL COMMENT '路由区号，',
  `dst_cloud_id` varchar(10) DEFAULT NULL COMMENT '目的区号，默认路由规则该值可为空',
  `route_rule` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0=默认路由，1=指定路由',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 comment '区号管理路由配置表';

-- ----------------------------
-- Table structure for libra_call_cloud_authority
-- ----------------------------
DROP TABLE IF EXISTS `libra_call_cloud_authority`;
CREATE TABLE `libra_call_cloud_authority` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '路由ID',
  `cloud_cluster_id` varchar(10) NOT NULL COMMENT '云团ID',
  `call_cloud_id` varchar(10) NOT NULL COMMENT '呼入呼出分区号',
  `call_state` tinyint(4) NOT NULL DEFAULT '0' COMMENT '呼叫状态，0=允许呼入，1=允许呼出',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 comment '区号管理呼叫权限表';