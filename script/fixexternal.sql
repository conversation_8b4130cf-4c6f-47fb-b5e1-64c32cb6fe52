use ainemo;

CREATE TABLE `ainemo`.`libra_device_external_new` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `device_id` bigint(20) DEFAULT NULL,
  `device_sn` varchar(64) DEFAULT NULL,
  `name_code` varchar(256) DEFAULT NULL,
  `call_uri` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_device_external_id` (`device_id`),
  CONSTRAINT `fk_device_external_id_new` FOREIGN KEY (`device_id`) REFERENCES `libra_user_device` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8;

INSERT INTO `ainemo`.`libra_device_external_new` (device_id, device_sn, name_code, call_uri) SELECT device_id, device_sn, name_code, call_uri FROM libra_device_external WHERE device_id is not NULL and call_uri is not NULL;

TRUNCATE TABLE `ainemo`.`libra_device_external`;

INSERT INTO `ainemo`.`libra_device_external` (device_id, device_sn, name_code, call_uri) SELECT device_id, device_sn, name_code, call_uri FROM libra_device_external_new WHERE device_id is not NULL and call_uri is not NULL;

DROP TABLE `ainemo`.`libra_device_external_new`;
