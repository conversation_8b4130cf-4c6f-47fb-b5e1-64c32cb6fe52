#!/bin/bash
echo $*
echo "Begin to upgrade:"
while getopts "a:m:o:w:t:e:d:p:s:c:j:n:r:h:i:b:" opt
do
   case $opt in 
      a ) nemohw="ainemo-vulture-nemohw-release-"$OPTARG
          nemohwvrn=$OPTARG
          echo "   nemohw servion: "$nemohw", vesion:"$nemohwvrn;;
      m ) marlin="marlin-release-"$OPTARG
          marlinvrn=$OPTARG
          echo "   marlin servion: "$marlin", vesion:"$marlinvrn;;
      o ) nemoos="ainemo-vulture-nemoos-release-"$OPTARG
          nemoosvrn=$OPTARG
          echo "   nemoos verson: "$nemoos;;
      w ) nemoawos="ainemo-vulture-nemoawos-release-"$OPTARG
          nemoawosvrn=$OPTARG 
          echo "   nemoawos verson: "$nemoawos;;
      t ) thirdpart="ainemo-vulture-thirdapp-release-"$OPTARG
          thirdpartvrn=$OPTARG 
          echo "   thirdpart verson: "$thirdpart;;
      e ) gillapp_me20="ainemo-vulture-gillapp_me20-release-"$OPTARG
          gillapp_me20vrn=$OPTARG 
          echo "   gillapp_me20 verson: "$gillapp_me20;;
      d ) debos="ainemo-vulture-debos-release-"$OPTARG
          debosvrn=$OPTARG 
          echo "   debos verson: "$debos;;
      p ) gillapp="ainemo-vulture-gillapp-release-"$OPTARG
          gillappvrn=$OPTARG 
          echo "   gillapp verson: "$gillapp;;
      s ) gillos="ainemo-vulture-gillos-release-"$OPTARG
          gillosvrn=$OPTARG 
          echo "   gillos verson: "$gillos;;
      c ) crushos="ainemo-vulture-crushos-release-"$OPTARG
          crushosvrn=$OPTARG
          echo "   crushos verson: "$crushos;;
      j ) jennyos="ainemo-vulture-jennyos-release-"$OPTARG
          jennyosvrn=$OPTARG
          echo "   jennyos verson: "$jennyos;;
      n ) jennyapp="jenny-release"
          jennyappvrn=$OPTARG
          echo "   jennyapp verson: "$jennyappvrn;;
      r ) nemoroom="NemoRoom-"$OPTARG".exe"
          nemoroomvrn=$OPTARG
          echo "   nemoroom verson: "$nemoroom;;
      h ) nemoroom_touch="NemoRoom-touch-"$OPTARG".exe"
          nemoroom_touchvrn=$OPTARG
          echo "   nemoroom_touch verson: "$nemoroom_touch;;
      i ) nigel="vessel-master-"$OPTARG
          nigelvrn=$OPTARG
          echo "   nigel verson: "$nigel;;
      b ) branch=$OPTARG
          echo "   branch: "$branch;;
      ? ) echo "error"
          exit 1;;
   esac
done

cd /var/nginx/www/appdownload

if [ $nemohw ]
then
   echo "install nemohw servion: "$nemohw
   yum install -y $nemohw
fi  

if [ $marlin ]
then
   echo "install marlin servion: "$marlin
   yum install -y $marlin
fi  

if [ $nemoos ]
then
   echo "upgrade nemoos verson: "$nemoos
   yum install -y $nemoos
fi 

if [ $nemoawos ]
then
   echo "install nemoawos servion: "$nemoawos
   yum install -y $nemoawos
fi 

if [ $thirdpart ]
then
   echo "install thirdpart servion: "$thirdpart
   yum install -y $thirdpart
fi 

if [ $gillapp_me20 ]
then
   echo "install gillapp_me20 servion: "$gillapp_me20
   yum install -y $gillapp_me20
fi 

if [ $debos ]
then
   echo "install debos servion: "$debos
   yum install -y $debos
fi 

if [ $gillapp ]
then
   echo "install gillapp servion: "$gillapp
   yum install -y $gillapp
fi 

if [ $gillos ]
then
   echo "install gillos servion: "$gillos
   yum install -y $gillos
fi 

if [ $crushos ]
then
   echo "install crushos servion: "$crushos
   yum install -y $crushos
fi 

if [ $jennyos ]
then
   echo "install jennyos servion: "$jennyos
   yum install -y $jennyos
fi

if [ $jennyapp ]
then
   jennyapprpm=$jennyapp
  if [ $branch ]
  then
    jennyapprpm=${jennyapp}_$branch
  fi
  jennyapprpm=${jennyapprpm}-$jennyappvrn
   echo "install jennyapp servion: "$jennyapprpm
   yum install -y $jennyapprpm
fi

if [ $nigel ]
then
   echo "install nigel servion: "$nigel
   yum install -y $nigel
fi  

if [ $nemohw ]
then
  status=`rpm -qa | grep $nemohw`
  if [ $status ]
  then
    echo "-------------Upgrade "$nemohw" successful"
    mkdir nemohw/$nemohwvrn
    cp app_pkt_info_nemohw.json nemohw/$nemohwvrn
  else
    echo "************* Fail to upgrade "$nemohw
    echo "************* Current version "`rpm -qa | grep ainemo-vulture-nemohw-release`
  fi
fi  

if [ $nemoos ]
then
  status=`rpm -qa | grep $nemoos`
  if [ $status ]
  then
    echo "-------------Upgrade "$nemoos" successful"
    mkdir nemoos/$nemoosvrn
    cp app_pkt_info_nemoos.json nemoos/$nemoosvrn
  else
    echo "************* Fail to upgrade "$nemoos
    echo "************* Current version "`rpm -qa | grep ainemo-vulture-nemoos-release`
  fi
fi

if [ $nemoawos ]
then  
  status=`rpm -qa | grep $nemoawos`
  if [ $status ]
  then
    echo "-------------Upgrade "$nemoawos" successful"
    mkdir nemoawos/$nemoawosvrn
    cp app_pkt_info_nemoawos.json nemoawos/$nemoawosvrn
  else
    echo "************* Fail to upgrade "$nemoawos
    echo "************* Current version "`rpm -qa | grep ainemo-vulture-nemoawos-release`
  fi
fi  

if [ $thirdpart ]
then  
  status=`rpm -qa | grep $thirdpart`
  if [ $status ]
  then
    echo "-------------Upgrade "$thirdpart" successful"
    mkdir thirdapp/$thirdpartvrn
    cp app_pkt_info_thirdapp.json thirdapp/$thirdpartvrn
  else
    echo "************* Fail to upgrade "$thirdpart
    echo "************* Current version "`rpm -qa | grep ainemo-vulture-thirdapp-release`
  fi
fi  

if [ $marlin ]
then  
  status=`rpm -qa | grep $marlin`
  if [ $status ]
  then
    echo "-------------Upgrade "$marlin" successful"
    mkdir marlinhw/$marlinvrn
    cp app_pkt_info_marlinhw.json marlinhw/$marlinvrn
  else
    echo "************* Fail to upgrade "$marlin
    echo "************* Current version "`rpm -qa | grep marlin-release`
  fi
fi  

if [ $gillapp_me20 ]
then  
  status=`rpm -qa | grep $gillapp_me20`
  if [ $status ]
  then
    echo "-------------Upgrade "$gillapp_me20" successful"
    mkdir gillapp_me20/$gillapp_me20vrn
    cp app_pkt_info_gill_me20.json gillapp_me20/$gillapp_me20vrn
  else
    echo "************* Fail to upgrade "$gillapp_me20
    echo "************* Current version "`rpm -qa | grep ainemo-vulture-gillapp_me20-release`
  fi
fi  

if [ $debos ]
then  
  status=`rpm -qa | grep $debos`
  if [ $status ]
  then
    echo "-------------Upgrade "$debos" successful"
    mkdir debos/$debosvrn
    cp app_pkt_info_debos.json debos/$debosvrn
  else
    echo "************* Fail to upgrade "$debos
    echo "************* Current version "`rpm -qa | grep ainemo-vulture-debos-release`
  fi
fi

if [ $gillapp ]
then  
  status=`rpm -qa | grep $gillapp`
  if [ $status ]
  then
    echo "-------------Upgrade "$gillapp" successful"
    mkdir gillapp/$gillappvrn
    cp app_pkt_info_gill_enapp_tvbox.json gillapp/$gillappvrn
  else
    echo "************* Fail to upgrade "$gillapp
    echo "************* Current version "`rpm -qa | grep ainemo-vulture-gillapp-release`
  fi
fi

if [ $gillos ]
then  
  status=`rpm -qa | grep $gillos`
  if [ $status ]
  then
    echo "-------------Upgrade "$gillos" successful"
    mkdir gillos/$gillosvrn
    cp app_pkt_info_gillos.json gillos/$gillosvrn
  else
    echo "************* Fail to upgrade "$gillos
    echo "************* Current version "`rpm -qa | grep ainemo-vulture-gillos-release`
  fi
fi

if [ $crushos ]
then  
  status=`rpm -qa | grep $crushos`
  if [ $status ]
  then
    echo "-------------Upgrade "$crushos" successful"
    mkdir crushos/$crushosvrn
    cp app_pkt_info_crushos.json crushos/$crushosvrn
  else
    echo "************* Fail to upgrade "$crushos
    echo "************* Current version "`rpm -qa | grep ainemo-vulture-crushos-release`
  fi
fi

if [ $jennyos ]
then  
  status=`rpm -qa | grep $jennyos`
  if [ $status ]
  then
    echo "-------------Upgrade "$jennyos" successful"
    mkdir -p jennyos/$jennyosvrn
    cp app_pkt_info_jennyos.json jennyos/$jennyosvrn
  else
    echo "************* Fail to upgrade "$jennyos
    echo "************* Current version "`rpm -qa | grep ainemo-vulture-jennyos-release`
  fi
fi

if [ $jennyapp ]
then  
  status=`rpm -qa | grep $jennyapprpm`
  if [ $status ]
  then
    echo "-------------Upgrade "$jennyapprpm" successful"
    mkdir -p jennyapp/$jennyappvrn
    cp app_pkt_info_jennyapp.json jennyapp/$jennyappvrn
  else
    echo "************* Fail to upgrade "$jennyapprpm
    echo "************* Current version "`rpm -qa | grep jenny-release`
  fi
fi

if [ $nigel ]
then  
  status=`rpm -qa | grep $nigel`
  if [ $status ]
  then
    echo "-------------Upgrade "$nigel" successful"
    mkdir -p nigel/$nigelvrn
    cp app_pkt_info_nigel.json nigel/$nigelvrn
  else
    echo "************* Fail to upgrade "$nigel
    echo "************* Current version "`rpm -qa | grep vessel-master`
  fi
fi

if [ $nemoroom ]
then
  echo "update nemoroom servion: "$nemoroom "(json)"
  status=`ls app_pkt_info_nemoroom.json`
  if [ $status ]
  then
    echo "-------------find app_pkt_info_nemoroom.json"
  else
    touch app_pkt_info_nemoroom.json
  fi
  buildTime=`date +%s`
  echo "{\"version\": \""$nemoroomvrn"\",\"relativePath\": \""$nemoroom"\",\"buildTime\": "$buildTime"000,\"releaseNote\": \"\"}" > app_pkt_info_nemoroom.json
  mkdir -p nemoroom/$nemoroomvrn
  cp app_pkt_info_nemoroom.json nemoroom/$nemoroomvrn
  cat nemoroom/${nemoroomvrn}/app_pkt_info_nemoroom.json
  echo "-------------Upgrade "$nemoroom" successful"
fi

if [ $nemoroom_touch ]
then
  echo "update nemoroom_touch servion: "$nemoroom_touch "(json)"
  status=`ls app_pkt_info_nemoroom_touch.json`
  if [ $status ]
  then
    echo "-------------find app_pkt_info_nemoroom_touch.json"
  else
    touch app_pkt_info_nemoroom_touch.json
  fi
  buildTime=`date +%s`
  echo "{\"version\": \""$nemoroom_touchvrn"\",\"relativePath\": \""$nemoroom_touch"\",\"buildTime\": "$buildTime"000,\"releaseNote\": \"\"}" > app_pkt_info_nemoroom_touch.json
  mkdir -p nemoroom_touch/$nemoroom_touchvrn
  cp app_pkt_info_nemoroom_touch.json nemoroom_touch/$nemoroom_touchvrn
  cat nemoroom_touch/${nemoroom_touchvrn}/app_pkt_info_nemoroom_touch.json
  echo "-------------Upgrade "$nemoroom_touch" successful"
fi