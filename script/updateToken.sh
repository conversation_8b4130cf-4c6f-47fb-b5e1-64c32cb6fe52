#! /bin/bash
#
#  Private cloud enterprise token, need to update when deploy, avoid same token for different enterprise
#

set -o nounset
set -o errexit

if [ ! $1 ] ;then
   echo "need input DB IP"
   exit 0
fi

date=`date +%s`
pwd=`pwd`
#DBIP=*************
DBIP=$1


NEW_TOKEN=$(cat /dev/urandom | tr -dc 'a-zA-Z0-9' | fold -w 64 | head -n 1)

echo $NEW_TOKEN

docker run --rm --net=host hub.xylink.com:5000/private_cloud/mysql mysql -h$DBIP -uprivate_cloud -p'Da?548!YZ' -e "use buffet; select token from t_en_enterprise_token" > /tmp/token.$date

docker run --rm --net=host hub.xylink.com:5000/private_cloud/mysql mysql -h$DBIP -uprivate_cloud -p'Da?548!YZ' -e "use buffet; update t_en_enterprise_token set token='$NEW_TOKEN'" 
