#! /bin/bash

#set -o nounset  #引用未定义的变量(缺省值为“”)
#set -o errexit  #执行失败的命令被忽略
#set -o verbose  #为调试打开verbose模式
#set -o xtrace   #跟踪脚本执行

if [ ! $1 ] ;then
   echo "need input file"
   exit 0
fi

password=XXXX


list=$(cat $1)
for line in $list
do
  if [ `echo $line | grep ^#` ] ; then
         echo $line" : continue"
         continue
  fi

  ip=$(echo $line | cut -d '/' -f1)
  name=$(echo $line | cut -d '/' -f2)

  sshpass -p $password ssh -oStrictHostKeyChecking=no root@$ip 'echo '"$ip $name"' >>/etc/hosts'
  sshpass -p $password ssh -oStrictHostKeyChecking=no root@$ip 'service kubelet restart'
  
  echo "$ip $name"

done

