#! /bin/bash

#start main ds
mainDaemonsets=('redis' 'amq' 'uss' 'contact' 'sharing' 'meeting-recorder')
for ds in ${mainDaemonsets[@]}
do
    echo "start ${ds}"
    kubectl label nodes private-docker-main "${ds}"=xylink
done

#start main deploy
mainDeploys=('private-buffet' 'private-charge' 'private-consumer' 'private-dating' 'private-externalweb' \
'private-iauth' 'private-locator' 'private-logserver' 'private-mcserver' 'private-msgserver' 'private-nettool' \
'private-openresty-main' 'private-page' 'private-pivotor' 'private-sitecode' 'private-vcs')

for deploy in ${mainDeploys[@]}
do
    echo "start ${deploy}"
    kubectl scale --replicas=1 deployment/"${deploy}"
done
