#! /bin/bash

version=5.2

echo "upgrade manager to $version"

pwd=`pwd`
date=`date +%s`

setKillMode() {
  if ! grep -q 'KillMode=process' /etc/systemd/system/private.service;then
    sed -i "/Service/a KillMode=process" /etc/systemd/system/private.service
    systemctl daemon-reload
  fi
}

installDir=`kubectl get cm private-customize-install -o jsonpath='{.data.installdir}'`
if [ -z $installDir ]; then
    installDir="/mnt/xylink"
fi
echo "-------- auto install dir: ${installDir} --------"
privateDir=$installDir/private_manager

echo "backup manager"
cp $privateDir/manager.war $privateDir/manager.war.$date

setKillMode

echo "manager is being upgraded to version: $version, please wait 60 seconds and refresh page!!!"

sleep 5

/usr/sbin/service private stop

echo "replace manager war"
cp $pwd/manager.war $privateDir

sleep 5

/usr/sbin/service private restart

echo "upgrade manager to $version SUCCEED!!!"


