#! /bin/bash

code="\[ro.build.version.code\]"
dev="\[ro.build.version.dev\]"

if [ ! $1 ] ;then
   echo "need input file"
   exit 0
fi

list=$(cat $1)
for line in $list
do
  ip=`echo $line | cut -d '#' -f1`
  if [ `echo $line | grep ^@` ] ; then
         echo $line
         continue
  fi

  pingResult=`ping -W 1000 -c 1 $ip | grep "icmp_seq"`
  if [ ! "$pingResult" ] ; then
        echo "Not online: " $line
        continue
  fi

  #echo $ip
  adb_con=`timeout 5 adb connect $ip:5656`
  if [[ $adb_con =~ "connected" ]] ;then
        sleep 1s
        vcode=`adb shell getprop | grep $code`
        vdev=`adb shell getprop | grep $dev`
        subcode=`echo $vcode | grep -o ': \[.*\]'`
        v1=`echo $subcode | cut -d '[' -f2 | cut -d ']' -f1`
        subdev=`echo $vdev | grep -o ': \[.*\]'`
        v2=`echo $subdev | cut -d '[' -f2 | cut -d ']' -f1`
        echo $v1 : $v2 : $line
        adb disconnect
        sleep 2s
  else
        adb disconnect
        echo "cannot connect:" $line
        sleep 2s
  fi

done