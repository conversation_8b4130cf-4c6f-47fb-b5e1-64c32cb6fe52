#! /bin/bash

#stop main ds
mainDaemonsets=('meetingcontrol' 'redis' 'amq' 'uss' 'contact' 'access' 'signal' 'sharing' 'meeting-recorder' 'dmcu')
for ds in ${mainDaemonsets[@]}
do
    echo ${ds}
    kubectl get nodes -a -l "type=main" -o name | cut -d "/" -f2 | xargs -I {} kubectl label nodes {} "${ds}"-
done

#stop main deploy
mainDeploys=('private-buffet' 'private-charge' 'private-consumer' 'private-dating' 'private-externalweb' \
'private-iauth' 'private-locator' 'private-logserver' 'private-mcserver' 'private-msgserver' 'private-nettool' \
'private-openresty-main' 'private-page' 'private-pivotor' 'private-sitecode' 'private-vcs')

for deploy in ${mainDeploys[@]}
do
    echo ${deploy}
    kubectl scale --replicas=0 deployment/"${deploy}"
done


vodDeploys=('private-live' 'private-nodelive' 'private-openresty-vod' 'private-rmserver' 'private-srs' 'private-vod' \
'private-vodbroker' 'private-vodfilemanager' 'private-vodmanager' 'private-vodshare')

#stop vod deploy

for deploy in ${vodDeploys[@]}
do
    echo ${deploy}
    kubectl scale --replicas=0 deployment/"${deploy}"
done

# stop mysql
kubectl get nodes -a -l "type=mysql" -o name | cut -d "/" -f2 | xargs -I {} kubectl label nodes {} mysql-

# stop all dmcu
kubectl get nodes -a -l "type=dmcu" -o name | cut -d "/" -f2 | xargs -I {} kubectl label nodes {} dmcu-





