#!/bin/bash
<<!
**********************************************************
* Author        : liujian
* Email         : <EMAIL>
* Last modified : 2017-12-02 00:20
* Filename      : node-monitor
* Description   :
* *******************************************************
!

set -euo pipefail

echo -e "Iterating...\n"

nodes=$(kubectl get node --no-headers -o custom-columns=NAME:.metadata.name)

for node in $nodes; do
  echo "Node: $node"
  pods=$(kubectl describe node "$node" | sed '1,/Namespace                  Name/d' | awk '{print $2}'| sed '/resources/, $d')
  echo -e "POD                           NAME             CPU(cores)   MEMORY(bytes)\n" | awk '{printf "%-50s %-30s %-15s %-15s\n",$1,$2,$3,$4}'
  for pod in $pods; do
    if [[ $pod == ---* || $pod == etcd-* || $pod == heapster-* || $pod == monitoring-* || $pod == kube* || $pod == private-sharing* ]]
    then 
      continue
    fi
#    echo "pod: $pod"
    kubectl top pod $pod --containers | sed -n "2, 1p" | awk '{printf "%-50s %-30s %-15s %-15s\n",$1,$2,$3,$4}'
  done
  echo
done
