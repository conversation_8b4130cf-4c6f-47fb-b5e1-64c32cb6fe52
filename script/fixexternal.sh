#! /bin/bash

pwd=`pwd`
echo "----------------------------------------------------------"
echo
mysqlIp=$(kubectl get cm all-ip -o template --template={{.data.DATABASE_IP}})
echo "mysqlIp: "$mysqlIp
if [[ ! $mysqlIp ]] ; then
    echo "can not found mysql,stop delete"
    exit
fi

begdate=`date`

echo "---------begin clean invalid data from $begdate---------"
docker run --rm -v $pwd:/tmp:z --net=host hub.xylink.com:5000/private_cloud/mysql:5.7.22  mysql -h $mysqlIp -u private_cloud -p'Da?548!YZ' -e "source /tmp/fixexternal.sql"

enddate=`date`
echo "---------succeed clean invalid data end at $enddate---------"