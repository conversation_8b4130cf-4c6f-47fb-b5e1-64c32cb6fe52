#!/bin/bash
## docker 版本
##备份主库数据，在任何一个宿主机上执行即可，备份文件在当前目录下 
## 5.2账号权限不用回收
## 3.9及以下的版本回收权限 ，revoke super, SHUTDOWN  on *.* from 'private_cloud'@'%';
## 主从复制账号：5.2 使用private_cloud，3.9及以下使用private_cloud 或者 rep 密码是一样的

pwd=`pwd`

#备份主库连接信息
master_mysqlip=$1
master_port=$2
master_user='dbbak'
master_passwd='U1O5ZeRyLFd#u9T6TF9h'

# 主从复制账号
repl_user='repl_canal'
repl_passwd='U8kkWjeX7#HeYOv6TXIG'


#还原从库连接信息
slave_mysqlip=$3
slave_port=$4
user='dbbak'
passwd='U1O5ZeRyLFd#u9T6TF9h'


##从库设置只读账号
#set_user='root'
#set_passwd='ak87?k'

mysqlImage=$(kubectl get pods | grep private-mysql | awk '{print $1}' | head -1 | xargs kubectl describe pods | grep Image:| grep mysql| awk '{print $2}' | head -1)
#mysqlImage=''


echo -e "\033[32;1m--------mysql docker 镜像名:$mysqlImage------------\033[0m"
echo -e "\033[32;1m--------$(date +"%Y-%m-%d %H:%M:%S") 开始备份主库数据，主库IP：$master_mysqlip------------\033[0m"


dump_masterdb_backup(){
  docker run  --rm -v $pwd:/tmp --net=host $mysqlImage /usr/bin/mysqldump --add-drop-database --set-gtid-purged=OFF -u$master_user -p$master_passwd -h $master_mysqlip -P$master_port --databases ainemo buffet charge contact dating dating_job external inspection task vcs vote proprietary --single-transaction --master-data=2 --flush-logs|tee $pwd/mysql_maindump.sql > /dev/null
}

dump_masterdb_backup

if [ $? -eq 0 ];then
    echo -e "\033[32;1m--------$(date +"%Y-%m-%d %H:%M:%S") 主库全量数据备份成功！！！备份文件是:$pwd/mysql_maindump.sql------------\033[0m"
    echo -e "\033[32;1m--------$(date +"%Y-%m-%d %H:%M:%S") 开始还原从库，从库IP：$slave_mysqlip------------\033[0m"
    docker run --rm -v $pwd:/tmp --net=host $mysqlImage mysql -u$user -p$passwd -h $slave_mysqlip -P$slave_port -e "set names utf8mb4;set global super_read_only=off;set sql_log_bin =off;source /tmp/mysql_maindump.sql;"
    if [ $? -eq 0 ];then
        echo -e "\033[32;1m--------$(date +"%Y-%m-%d %H:%M:%S") 从库($slave_mysqlip)全量数据还原成功！！！------------\033[0m"
        change_masterinfo=`cat $pwd/mysql_maindump.sql | grep '\-- CHANGE MASTER'|awk -F '--' '{print $2}'`
        masterinfo="MASTER_HOST='$master_mysqlip',MASTER_PORT=$master_port,MASTER_USER='$repl_user',MASTER_PASSWORD='$repl_passwd';"
        change_masterinfo=`echo $change_masterinfo | awk -F ';' '{print $1}'`,$masterinfo
        #echo $change_masterinfo
        echo -e "\033[32;1m--------$(date +"%Y-%m-%d %H:%M:%S") 开始在从库($slave_mysqlip) 上创建主从关系！！！命令是：$change_masterinfo------------\033[0m"
        docker run --rm --net=host $mysqlImage mysql -u$user -p$passwd -h $slave_mysqlip -P$slave_port -e "stop slave;$change_masterinfo;start slave;set global super_read_only=on;"
        Slave_IO=`docker run --rm --net=host $mysqlImage mysql -u$user -p$passwd -h $slave_mysqlip -P$slave_port -e "show slave status \G" | egrep "Slave_IO_Running:" | awk -F ': ' '{print $2}'` 
        Slave_SQL=`docker run --rm --net=host $mysqlImage mysql -u$user -p$passwd -h $slave_mysqlip -P$slave_port -e "show slave status \G" | egrep "Slave_SQL_Running:" | awk -F ': ' '{print $2}'`
        Slave_status=`docker run --rm --net=host $mysqlImage mysql -u$user -p$passwd -h $slave_mysqlip -P$slave_port -e "show variables like 'super_read_only' \G" | egrep "Value:" | awk -F ': ' '{print $2}'`
        echo -e "\033[32;1m--------$(date +"%Y-%m-%d %H:%M:%S") 主从复制状态信息 Slave_IO_Running:$Slave_IO Slave_SQL_Running:$Slave_SQL------------\033[0m" 
        if [ "$Slave_status" = "ON" ];then
             echo -e "\033[32;1m--------$(date +"%Y-%m-%d %H:%M:%S") 从库($slave_mysqlip)的状态为:只读------------\033[0m"
        elif [ "$Slave_status" = "OFF" ];then
             echo -e "\033[32;1m--------$(date +"%Y-%m-%d %H:%M:%S") 从库($slave_mysqlip)的状态为:读写------------\033[0m"
        else 
              echo -e "\033[32;1m--------$(date +"%Y-%m-%d %H:%M:%S") 从库($slave_mysqlip)的状态为:$Slave_status------------\033[0m"
        fi
        if [ "$Slave_IO" = "Yes" ]&&[ "$Slave_SQL" = "Yes" ];then 
             echo -e "\033[32;1m--------$(date +"%Y-%m-%d %H:%M:%S") 主从复制正常，请使用！！！------------\033[0m"
        else
             echo -e "\033[31;1m--------$(date +"%Y-%m-%d %H:%M:%S") 主从复制异常，请连接管理员！！！------------\033[0m"
        fi
    else
        echo -e "\033[31;1m--------$(date +"%Y-%m-%d %H:%M:%S") 从库($slave_mysqlip)全量数据还原失败！！！------------\033[0m"
    fi
else
    echo -e  "\033[31;1m--------$(date +"%Y-%m-%d %H:%M:%S") 主库($master_mysqlip)全量数据备份失败！！！------------\033[0m"
fi
