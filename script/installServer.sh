#!/bin/bash

if test "$#" == 0
then
echo "file name required"
exit 1
fi

if [ ! -n "$upgrade_dir" ] || [ ! -n "$upgrade_log_folder" ] || [ ! -n "$upgrade_file_folder" ] || [ ! -n "$upgrade_tmp_folder" ] || [ ! -n "$upgrade_shell" ]; then
    echo "env is required"
    exit 1
elif [ "$upgrade_dir" = "" ] || [ "$upgrade_log_folder" = "" ] || [ "$upgrade_file_folder" = "" ] || [ "$upgrade_tmp_folder" = "" ] || [ "$upgrade_shell" = "" ]; then
    echo "env is empty"
    exit 1
elif [ "$upgrade_dir" = "/" ] || [ "$upgrade_log_folder" = "/" ] || [ "$upgrade_file_folder" = "/" ] || [ "$upgrade_tmp_folder" = "/" ] || [ "$upgrade_shell" = "/" ]; then
    echo "env is /"
    exit 1
elif [ ! -n "$db_upgradecommand" ] || [ ! -n "$db_upgradecommand_arg" ] || [ ! -n "$db_upgradecommand_workdir" ] || [ ! -n "$db_upgradecommand_upgradesql_dir" ]; then
    echo "db env is required"
    exit 1
else
    mkdir -p $upgrade_dir"/"$upgrade_log_folder
    mkdir -p $upgrade_dir"/"$upgrade_file_folder
    mkdir -p $upgrade_dir"/"$upgrade_tmp_folder
    echo "env is ok"
fi

export db_upgradecommand
export db_upgradecommand_arg
export db_upgradecommand_workdir
export db_upgradecommand_upgradesql_dir

cd $upgrade_dir
if [ -n "$upgrade_log_folder" ]; then
    echo "delete log file"$upgrade_dir"/"$upgrade_log_folder"/"${1//.zip/}".log"
    rm -rf $upgrade_dir"/"$upgrade_log_folder"/"${1//.zip/}".log"
fi
exec &>$upgrade_dir"/"$upgrade_log_folder"/"${1//.zip/}".log"

echo "db_upgradecommand is "$db_upgradecommand
echo "db_upgradecommand_arg is "$db_upgradecommand_arg
echo "db_upgradecommand_workdir is "$db_upgradecommand_workdir
echo "db_upgradecommand_upgradesql_dir is "$db_upgradecommand_upgradesql_dir

echo "upgrade_dir is "$upgrade_dir
echo "upgrade_log_folder is "$upgrade_log_folder
echo "upgrade_file_folder is "$upgrade_file_folder
echo "upgrade_tmp_folder is "$upgrade_tmp_folder
echo "upgrade_shell is "$upgrade_shell

chmod 777 $upgrade_file_folder"/"$1
if [[ "${1:(-4)}" != ".zip" ]]
then
    echo $1" not zip file"
    if [ -n "$upgrade_file_folder" ]; then
        rm -f $upgrade_file_folder"/"$1
    fi
    exit 1
else
    if [ -n "$upgrade_tmp_folder" ]; then
        echo "pre empty "$upgrade_dir"/"$upgrade_tmp_folder"/"
        rm -rf $upgrade_dir"/"$upgrade_tmp_folder"/"
    fi
    echo "unzip file "$1
    unzip -qo $upgrade_file_folder"/"$1 -d $upgrade_tmp_folder
    cd $upgrade_dir"/"$upgrade_tmp_folder
    if [ -f "$upgrade_shell" ]; then
        echo "execute "$upgrade_shell
        sh $upgrade_shell
    else
        echo "$upgrade_shell" not exist
    fi
    if [ -n "$upgrade_tmp_folder" ]; then
        echo "post empty "$upgrade_dir"/"$upgrade_tmp_folder"/"
        rm -rf $upgrade_dir"/"$upgrade_tmp_folder"/"
    fi
fi