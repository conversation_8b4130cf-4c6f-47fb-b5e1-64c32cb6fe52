#! /bin/bash

#set -o nounset  #引用未定义的变量(缺省值为“”)
#set -o errexit  #执行失败的命令被忽略
#set -o verbose  #为调试打开verbose模式
#set -o xtrace   #跟踪脚本执行

pwd=$(pwd)
date=$(date +%s)
password=1

if [ ! $1 ] ;then
   echo "need input file"
   exit 0
fi

list=$(cat $1)
for line in $list
do
  if [ `echo $line | grep ^#` ] ; then
         echo $line" : continue"
         continue
  fi

  if [ -z "$line" ] ; then
        continue
  fi

  ip=$(echo $line | cut -d '@' -f1)

  sshpass -p $password ssh -oStrictHostKeyChecking=no root@$ip 'timedatectl set-timezone Asia/Shanghai'
  echo "set $ip time zone done"
  #sshpass -p $password ssh -oStrictHostKeyChecking=no root@$ip 'sed -i -e "/^UUID.*/d" /etc/sysconfig/network-scripts/ifcfg-ens160'
  sshpass -p $password ssh -oStrictHostKeyChecking=no root@$ip 'sed -i -e "/^UUID.*/d" /etc/sysconfig/network-scripts/ifcfg-ens32'
  echo "remove $ip UUID done"
  sshpass -p $password ssh -oStrictHostKeyChecking=no root@$ip 'service network restart'

done